import path from 'path';
import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import Icons from 'unplugin-icons/vite';
import IconsResolver from 'unplugin-icons/resolver';
import { FileSystemIconLoader } from 'unplugin-icons/loaders';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { NaiveUiResolver, ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import vueJsx from '@vitejs/plugin-vue-jsx';
import { svgBuilder } from './src/plugins/svgBuilder';

// https://vitejs.dev/config/
export default ({ mode }) => {
  const developmentEnv = loadEnv('development', process.cwd(), '');
  const currentEnv = mode === 'development' ? developmentEnv : loadEnv(mode, process.cwd(), '');
  const env = mode === 'local_bpms' ? { ...developmentEnv, ...currentEnv } : currentEnv;
  const sanitizeEnvValue = (value?: string) => (value ? value.replace(/^['"]|['"]$/g, '') : '');
  const cleanEnv = Object.entries(env).reduce<Record<string, string>>((acc, [key, value]) => {
    acc[key] = sanitizeEnvValue(value);
    return acc;
  }, {} as Record<string, string>);
  const removeProxyPrefix = (path: string, prefix: string) =>
    prefix && path.startsWith(prefix) ? path.slice(prefix.length) || '/' : path;
  const baseProxy = cleanEnv.VITE_APP_BASE_PROXY || '/bpms-api';
  const backendHost = cleanEnv.VITE_APP_BACKEND_HOST;
  const backendUserInfoHost = cleanEnv.VITE_APP_BACKEND_USERINFO_HOST;
  const biHost = cleanEnv.VITE_APP_BI_HOST;
  const stripProxyFlag = cleanEnv.VITE_APP_PROXY_STRIP_PREFIX;
  const shouldStripProxyPrefix = (() => {
    if (stripProxyFlag) {
      return stripProxyFlag.toLowerCase() === 'true';
    }
    if (!backendHost) {
      return false;
    }
    try {
      const { hostname } = new URL(backendHost);
      return hostname === 'localhost' || hostname === '127.0.0.1';
    } catch (error) {
      return backendHost.includes('localhost') || backendHost.includes('127.0.0.1');
    }
  })();
  const rewriteProxyPath = (prefix: string) => (path: string) =>
    shouldStripProxyPrefix ? removeProxyPrefix(path, prefix) : path;
  return defineConfig({
    base: cleanEnv.VITE_APP_BASE_URL,
    css: {
      modules: {
        localsConvention: 'camelCase',
      },
      preprocessorOptions: {
        scss: {
          additionalData: `
            @use "@guwave/element-plus-theme/src/_theme.scss" as *;
          `,
        },
      },
    },
    plugins: [
      vue({
        script: {
          defineModel: true,
        },
      }),
      vueJsx(),
      AutoImport({
        dts: true,
        imports: ['vue', 'vue-router', 'pinia', '@vueuse/head', '@vueuse/core'],
        eslintrc: {
          enabled: true,
          filepath: './.eslintrc-auto-import.json',
          globalsPropValue: true,
        },
        resolvers: [
          NaiveUiResolver(),
          ElementPlusResolver({ importStyle: false }),
          // 自动导入图标组件
          IconsResolver({
            prefix: 'Icon',
          }),
        ],
      }),
      Components({
        dts: true,
        resolvers: [
          // 自动注册图标组件
          IconsResolver({
            prefix: 'icon',
            customCollections: ['gb', 'gb-cs'],
          }),
          // 自动导入 Element Plus 组件
          NaiveUiResolver(),
        ],
      }),
      Icons({
        customCollections: {
          gb: FileSystemIconLoader(path.resolve('src/assets/icons'), (svg) => {
            let s = svg.replace(/['"](#fff['"])|(#ffffff['"])/gi, '"-"');
            s = s.replace(/fill=["']#\w*?["']/gi, 'fill="currentColor"');
            s = s.replace(/stroke=["']#\w*?["']/gi, 'stroke="currentColor"');
            s = s.replace(/"-"/g, '"#fff"');
            return s;
          }),
          'gb-cs': FileSystemIconLoader(path.resolve('src/assets/icons/colors')),
        },
      }),
      svgBuilder('./src/assets/icon/svg/'),
    ],
    resolve: {
      alias: [{ find: '@', replacement: path.resolve(__dirname, 'src') }],
    },
    server: {
      host: '0.0.0.0', // 添加此行以支持局域网访问
      proxy: {
        [baseProxy]: {
          target: backendHost,
          changeOrigin: true,
          rewrite: rewriteProxyPath(baseProxy),
        },
        [cleanEnv.VITE_APP_API_PREFIX_USER_CENTER]: {
          target: backendUserInfoHost,
          changeOrigin: true,
        },
        [cleanEnv.VITE_APP_API_BI]: {
          target: biHost,
          changeOrigin: true,
        },
        [cleanEnv.VITE_APP_API_PARAME_SYSTEM]: {
          target: backendHost,
          changeOrigin: true,
        },
        '/dev-api': {
          target: 'http://riot82:19194', // 目标服务器地址
          changeOrigin: true, // 必须设置为 true，以避免 host header 问题
          rewrite: (path) => path.replace(/^\/dev-api/, ''), // 重写请求路径（移除 '/api' 前缀）
        },
        '/bpms-api': {
          target: backendHost,
          changeOrigin: true,
          rewrite: rewriteProxyPath('/bpms-api'),
        },
        '/user-center-api': {
          target: backendUserInfoHost,
          changeOrigin: true,
        },
      },
    },
  });
};
