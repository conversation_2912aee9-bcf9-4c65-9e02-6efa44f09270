sourceCompatibility = 1.8
targetCompatibility = 1.8
[compileJava, compileTestJava, javadoc]*.options*.encoding = 'UTF-8'

defaultTasks 'clean', 'build'

buildDir = "target"

repositories {
  mavenLocal()
  maven {
    allowInsecureProtocol true
    url mavenPublicUrl
  }
  mavenCentral()
}

java {
//  withJavadocJar()
  withSourcesJar()
}

compileJava {
  doLast {
    jar {
      manifest.attributes("Implementation-Title": project.description,
              "Automatic-Module-Name": project.group,
              "Created-By": "$gradleVersion",
              "Build-Jdk": "$jdkVersion",
              "Specification-Version": project.version,
              "Implementation-Version": project.version)
    }
  }
}

publishing {
  publications {
    maven(MavenPublication) {
      artifactId project.name
      from components.java

      pom {
        description = project.description
        licenses {
          license {
            name = 'The Apache License, Version 2.0'
            url = 'https://www.apache.org/licenses/LICENSE-2.0'
          }
        }
        developers {
          developer {
            id = 'levi'
            name = '<PERSON>'
            email = '<EMAIL>'
          }
        }
      }
    }
  }

  repositories {
    mavenLocal()
    maven {
      allowInsecureProtocol true
      //指定要上传的maven私服仓库
      url = version.endsWith('SNAPSHOT') ? mavenSnapshotUrl : mavenReleaseUrl
      //认证用户和密码
      credentials {
        username nexusUserName
        password nexusUserPwd
      }
    }
  }
}

javadoc {
  if(JavaVersion.current().isJava9Compatible()) {
    options.addBooleanOption('html5', true)
  }
}
