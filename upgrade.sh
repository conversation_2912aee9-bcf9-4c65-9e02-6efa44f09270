#!/bin/bash
pre_version=$1
version=$2

pwd=`pwd`

# 加载properties文件
sed -i 's/\r$//' ../../../properties/bigdata-common.properties
source ../../../properties/bigdata-common.properties
source upgrade.properties

# 创建topic
echo `date '+%Y-%m-%d %H:%M:%S'`'开始创建topic'
IFS=',' read -ra addTopics <<< "$bpms_topics_add"
for topic in "${addTopics[@]}"; do
  echo `date '+%Y-%m-%d %H:%M:%S'`'创建topic '$topic''
  sshpass -p $kafka_server_password ssh -o StrictHostKeyChecking=no ${kafka_server_user}@${kafka_servers} '/usr/hdp/*******-315/kafka/bin/kafka-topics.sh --zookeeper '$zookeeper_address' --partitions '$kafka_topic_partition' --replication-factor '$kafka_topic_replication' --config "min.insync.replicas='$kafka_min_insync_replicas'" --create --topic '$topic''
done
echo `date '+%Y-%m-%d %H:%M:%S'`'结束创建topic'

# 停止bpms_app
if [[ ${kill_app} -eq '1' ]]; then
  if [ -z "$bpms_app_deploy" ]; then
    echo "无需停止bpms_app_deploy"
  else
    echo `date '+%Y-%m-%d %H:%M:%S'`'开始停止bpms_app_deploy'

    echo `date '+%Y-%m-%d %H:%M:%S'`'开始在'$bpms_app_deploy'上停止旧版本bpms_app'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'ps -ef | grep java | grep bpms-app | grep '$devops_deploy_user' | grep -v "grep" | grep -v "sshpass" | awk '"'"'{print $2}'"'"' | xargs kill'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sudo docker stop $(sudo docker ps -q --filter "name=bpms-app")'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sudo docker rm $(sudo docker ps -q --filter "name=bpms-app" --filter "status=exited")'
    echo `date '+%Y-%m-%d %H:%M:%S'`'结束在'$bpms_app_deploy'上停止旧版本bpms_app'

    echo `date '+%Y-%m-%d %H:%M:%S'`'结束停止bpms_app_deploy'
  fi
fi

# 执行mysql patch脚本
if [[ ${exec_patch} -eq '1' ]]; then
  echo `date '+%Y-%m-%d %H:%M:%S'`'开始修改mysql脚本'
  sed -i 's/`bpms`/`'$mysql_bpms_database'`/g' $pwd/resources/sql/mysql/patch/*.sql
  sed -i 's/`onedata`/`'$mysql_onedata_database'`/g' $pwd/resources/sql/mysql/patch/*.sql
  sed -i 's/`useradmin`/`'$mysql_useradmin_database'`/g' $pwd/resources/sql/mysql/patch/*.sql
  sed -i 's/`compute`/`'$mysql_compute_database'`/g' $pwd/resources/sql/mysql/patch/*.sql
  sed -i 's/`linkx`/`'$mysql_linkx_database'`/g' $pwd/resources/sql/mysql/patch/*.sql
  sed -i 's|\${ACCESS_ADDR}|'$ACCESS_ADDR'|g' $pwd/resources/sql/mysql/patch/*.sql
  sed -i 's|\${wafer_overall_yield}|'$wafer_overall_yield'|g' $pwd/resources/sql/mysql/patch/*.sql
  sed -i 's|\${assembly_overall_yield}|'$assembly_overall_yield'|g' $pwd/resources/sql/mysql/patch/*.sql
  sed -i 's|\${wip_snapshot}|'$wip_snapshot'|g' $pwd/resources/sql/mysql/patch/*.sql
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束修改mysql脚本'

  echo `date '+%Y-%m-%d %H:%M:%S'`'开始分发mysql脚本'
  sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host 'mkdir -p ~/deploy/onedata/bpms/resources/sql/mysql/patch'
  sshpass -p $mysql_host_password scp -o StrictHostKeyChecking=no $pwd/resources/sql/mysql/patch/*.sql $mysql_host:~/deploy/onedata/bpms/resources/sql/mysql/patch
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束分发mysql脚本'

  echo `date '+%Y-%m-%d %H:%M:%S'`'开始执行mysql脚本'
  sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host 'mysql -u'$mysql_root_username' -p'$mysql_root_password' -h127.0.0.1 -P'$mysql_port' < ~/deploy/onedata/bpms/resources/sql/mysql/patch/patch_v'$version'_schema.sql'
  sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host 'mysql -u'$mysql_root_username' -p'$mysql_root_password' -h127.0.0.1 -P'$mysql_port' < ~/deploy/onedata/bpms/resources/sql/mysql/patch/patch_v'$version'_common.sql'
  sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host 'mysql -u'$mysql_root_username' -p'$mysql_root_password' -h127.0.0.1 -P'$mysql_port' < ~/deploy/onedata/bpms/resources/sql/mysql/patch/patch_v'$version'_useradmin.sql'
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束执行mysql脚本'
fi

##  配置文件改动,启动项目
if [ -z "$bpms_app_deploy" ]; then
  echo "无需部署bpms_app"
else
  echo `date '+%Y-%m-%d %H:%M:%S'`'开始部署bpms_app'

  echo `date '+%Y-%m-%d %H:%M:%S'`'开始分发bpms_app到'$bpms_app_deploy
  sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'mkdir -p ~/deploy/onedata/bpms/bpms-app'
  sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'cd ~/deploy/onedata/bpms/bpms-app && rm -rf base.'$version'.properties'
  sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'cd ~/deploy/onedata/bpms/bpms-app && cp base.'$pre_version'.properties base.'$version'.properties'
  sshpass -p $devops_deploy_password scp -o StrictHostKeyChecking=no bpms-app-$version.tar $bpms_app_deploy:~/deploy/onedata/bpms/bpms-app
  sshpass -p $devops_deploy_password scp -o StrictHostKeyChecking=no site-packages.tar.gz $bpms_app_deploy:~/deploy/onedata/bpms/bpms-app
  sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'cd ~/deploy/onedata/bpms/bpms-app && sudo docker load -i bpms-app-'$version'.tar'
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束分发bpms_app到'$bpms_app_deploy

  echo `date '+%Y-%m-%d %H:%M:%S'`'开始在'$bpms_app_deploy'上修改bpms-app的配置文件'
  sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'cd ~/deploy/onedata/bpms/bpms-app && mkdir -p logs'
  sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'cd ~/deploy/onedata/bpms/bpms-app && rm -rf site-packages && tar xf site-packages.tar.gz'

  # 在原有配置前面增加配置项
  IFS='|' read -ra addbeforeConfigs <<< "$bpms_app_add_before"
  for config_pair in "${addbeforeConfigs[@]}"; do
    position_config=$(echo "$config_pair" | grep -o '^[^:]*')
    new_config=$(echo "$config_pair" | sed 's/^[^:]*://')
    key=$(echo "$new_config" | cut -d'=' -f1)
    if [[ -n "${properties_map[$key]+x}" ]]; then
      value=${properties_map[$key]}
      new_config="$key=${!value}"
    fi
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "/^'$position_config'=/ {
                                                                                                  i\
                                                                                                  '$new_config'
                                                                                                  }" ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
  done
  # 在原有配置后面增加配置项
  IFS='|' read -ra addAfterConfigs <<< "$bpms_app_add_after"
  for config_pair in "${addAfterConfigs[@]}"; do
    position_config=$(echo "$config_pair" | grep -o '^[^:]*')
    new_config=$(echo "$config_pair" | sed 's/^[^:]*://')
    key=$(echo "$new_config" | cut -d'=' -f1)
    if [[ -n "${properties_map[$key]+x}" ]]; then
      value=${properties_map[$key]}
      new_config="$key=${!value}"
    fi
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "/^'$position_config'/ {
                                                                                                  a\
                                                                                                  '$new_config'
                                                                                                  }" ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
  done
  # 删除原有配置
  IFS='|' read -ra deleteConfigs <<< "$bpms_app_delete"
  for position_config in "${deleteConfigs[@]}"; do
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "/^'$position_config'=/d" ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
  done
  # 更新原有配置
  IFS='|' read -ra updateConfigs <<< "$bpms_app_update"
  for new_config in "${updateConfigs[@]}"; do
    position_config=$(echo "$new_config" | cut -d'=' -f1)
    if [[ -n "${properties_map[$position_config]+x}" ]]; then
      value=${properties_map[$position_config]}
      new_config="$position_config=${!value}"
    fi
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "\|^'$position_config'|s|.*|'$new_config'|" ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
  done
  sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/\r$//" ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束在'$bpms_app_deploy'上修改bpms-app的配置文件'
fi

if [[ ${start_app} -eq '1' ]]; then
  if [ -z "$bpms_app_deploy" ]; then
    echo "无需部署bpms_app"
  else
    echo `date '+%Y-%m-%d %H:%M:%S'`'开始启动'$bpms_app_deploy'上的bpms_app'
    GROUP_ID=$(sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'echo `id '$devops_deploy_user' -g`')
    USER_ID=$(sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'echo `id '$devops_deploy_user' -u`')
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sudo docker run -d \
                                                                                                   --name bpms-app \
                                                                                                   -e JAVA_OPTS="'$bpms_app_config'" \
                                                                                                   -e GROUP_ID='$GROUP_ID' \
                                                                                                   -e ENVIRON_GROUP='$devops_deploy_group' \
                                                                                                   -e USER_ID='$USER_ID' \
                                                                                                   -e ENVIRON_USER='$devops_deploy_user' \
                                                                                                   --hostname `hostname` \
                                                                                                   --network=host \
                                                                                                   -ti \
                                                                                                   -v /etc/hosts:/etc/hosts:ro \
                                                                                                   -v /home/'$devops_deploy_user'/deploy/onedata/bpms/bpms-app/base.'$version'.properties:/home/<USER>/deploy/datahub/bpms/bpms-app/bpms-app-'$version'/properties/bpms-app.properties \
                                                                                                   -v /home/'$devops_deploy_user'/deploy/onedata/bpms/bpms-app/logs:/home/<USER>/deploy/datahub/bpms/bpms-app/bpms-app-'$version'/logs \
                                                                                                   -v /home/'$devops_deploy_user'/deploy/onedata/bpms/bpms-app/site-packages:/opt/python38/lib/python3.8/site-packages \
                                                                                                   -v /home/'$devops_deploy_user'/deploy/onedata/bpms/bpms-app/lib-dynload:/opt/python38/lib/python3.8/lib-dynload \
                                                                                                   -v /home/'$devops_deploy_user'/deploy/bigbrother/skyeye/properties:/home/<USER>/deploy/bigbrother/skyeye/properties \
                                                                                                   -v /usr/share/fonts:/usr/share/fonts \
                                                                                                   -v /tmp:/tmp \
                                                                                                   bpms/bpms-app:'$version
    echo `date '+%Y-%m-%d %H:%M:%S'`'结束启动'$bpms_app_deploy'上的bpms_app'

    echo `date '+%Y-%m-%d %H:%M:%S'`'删除bpms-app的tar包和zip包'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'cd ~/deploy/onedata/bpms/bpms-app && find . -type f \( -name "*.tar" -o -name "*.zip" \) -path "./bpms*" | xargs rm'

    echo `date '+%Y-%m-%d %H:%M:%S'`'结束部署bpms_app'
  fi
fi