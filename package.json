{"name": "bpms-web", "private": true, "version": "3.5.3", "type": "module", "scripts": {"dev": "vite", "local": "vite --mode local_bpms", "build": "vite build", "commit": "git add . && pnpx git-cz", "preview": "vite preview", "lint-staged": "lint-staged", "prepare": "husky", "test": "echo"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix"], "*.vue": ["eslint --fix"]}, "dependencies": {"@antv/x6": "^2.18.1", "@element-plus/icons-vue": "^2.0.9", "@form-create/element-ui": "^3.1.26", "@guwave/components": "^0.2.44", "@guwave/element-plus-theme": "0.1.9", "@guwave/i18n": "^0.1.1", "@guwave/utils": "0.1.21", "@sentry/vue": "^7.106.0", "@vicons/ionicons5": "^0.13.0", "@vueuse/components": "^10.7.0", "@vueuse/core": "^10.7.0", "@vueuse/integrations": "^10.7.0", "autoprefixer": "^10.4.21", "axios": "^0.27.2", "dayjs": "^1.11.10", "echarts": "^5.4.3", "element-plus": "2.7.4", "lodash": "^4.17.21", "naive-ui": "^2.38.1", "nanoid": "^5.0.4", "pinia": "^2.1.7", "postcss": "^8.5.6", "qs": "^6.11.2", "unplugin-auto-import": "^0.17.1", "unplugin-vue-components": "^0.25.2", "vue": "^3.3.8", "vue-i18n": "^9.7.0", "vue-router": "^4.2.5"}, "devDependencies": {"@babel/core": "^7.18.9", "@sentry/vite-plugin": "^2.14.3", "@types/lodash": "^4.14.202", "@types/node": "^18.11.3", "@types/qs": "^6.9.10", "@typescript-eslint/eslint-plugin": "^6.20.0", "@typescript-eslint/parser": "^6.20.0", "@vitejs/plugin-vue": "^4.5.0", "@vitejs/plugin-vue-jsx": "^3.1.0", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.20.1", "husky": "^9.0.7", "less": "^4.2.0", "lint-staged": "^15.2.0", "sass": "^1.69.5", "tailwindcss": "^3.4.17", "typescript": "^5.3.2", "unplugin-element-plus": "^0.7.2", "unplugin-icons": "^0.18.1", "vite": "^5.0.2", "vue-tsc": "^1.8.22"}, "packageManager": "pnpm@9.13.2+sha1.969cc200a41db98449afee1bfa7578b3ce6ff330"}