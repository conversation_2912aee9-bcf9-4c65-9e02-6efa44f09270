#!/bin/bash
version=$1

pwd=`pwd`

# 加载properties文件
sed -i 's/\r$//' ../../../properties/bigdata-common.properties
source ../../../properties/bigdata-common.properties
source deploy.properties

# 停止bpms-app
if [[ ${kill_app} -eq '1' ]]; then
    if [ -z "$bpms_app_deploy" ]; then
      echo "无需停止bpms-app"
    else
      echo `date '+%Y-%m-%d %H:%M:%S'`'开始停止bpms-app'

      echo `date '+%Y-%m-%d %H:%M:%S'`'开始在'$bpms_app_deploy'上停止旧版本bpms-app'
      sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'ps -ef | grep java | grep bpms-app | grep '$glory_deploy_user' | grep -v "grep" | grep -v "sshpass" | awk '"'"'{print $2}'"'"' | xargs kill'
      sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sudo docker stop $(sudo docker ps -q --filter "name=bpms-app")'
      sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sudo docker rm $(sudo docker ps -q --filter "name=bpms-app" --filter "status=exited")'
      echo `date '+%Y-%m-%d %H:%M:%S'`'结束在'$bpms_app_deploy'上停止旧版本bpms-app'

      echo `date '+%Y-%m-%d %H:%M:%S'`'结束停止bpms-app'
    fi
fi

# 执行mysql full脚本
if [[ ${exec_full} -eq '1' ]]; then
  echo `date '+%Y-%m-%d %H:%M:%S'`'开始修改mysql脚本'
  sed -i 's/USE `bpms`;/USE `'$mysql_bpms_database'`;/g' $pwd/resources/sql/mysql/full/*.sql
  sed -i 's/NOT EXISTS `bpms`/NOT EXISTS `'$mysql_bpms_database'`/g' $pwd/resources/sql/mysql/full/*.sql
  sed -i 's/USE `onedata`;/USE `'$mysql_onedata_database'`;/g' $pwd/resources/sql/mysql/full/*.sql
  sed -i 's/`onedata`;/`'$mysql_onedata_database'`;/g' $pwd/resources/sql/mysql/full/*.sql
  sed -i 's/USE `useradmin`;/USE `'$mysql_useradmin_database'`;/g' $pwd/resources/sql/mysql/full/*.sql
  sed -i 's/`useradmin`;/`'$mysql_useradmin_database'`;/g' $pwd/resources/sql/mysql/full/*.sql
  sed -i 's/NOT EXISTS `onedata`/NOT EXISTS `'$mysql_onedata_database'`/g' $pwd/resources/sql/mysql/full/*.sql
  sed -i 's/USE `linkx`;/USE `'$mysql_linkx_database'`;/g' $pwd/resources/sql/mysql/full/*.sql
  sed -i 's/`linkx`;/`'$mysql_linkx_database'`;/g' $pwd/resources/sql/mysql/full/*.sql
  sed -i 's/NOT EXISTS `linkx`/NOT EXISTS `'$mysql_linkx_database'`/g' $pwd/resources/sql/mysql/full/*.sql
  sed -i 's|\${ACCESS_ADDR}|'$ACCESS_ADDR'|g' $pwd/resources/sql/mysql/full/*.sql
  sed -i 's|\${wafer_overall_yield}|'$wafer_overall_yield'|g' $pwd/resources/sql/mysql/full/init_data.sql
  sed -i 's|\${assembly_overall_yield}|'$assembly_overall_yield'|g' $pwd/resources/sql/mysql/full/init_data.sql
  sed -i 's|\${wip_snapshot}|'$wip_snapshot'|g' $pwd/resources/sql/mysql/full/init_data.sql
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束修改mysql脚本'

  echo `date '+%Y-%m-%d %H:%M:%S'`'开始分发mysql脚本'
  sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host 'mkdir -p ~/deploy/onedata/bpms/resources/sql/mysql/full'
  sshpass -p $mysql_host_password scp -o StrictHostKeyChecking=no $pwd/resources/sql/mysql/full/*.sql $mysql_host:~/deploy/onedata/bpms/resources/sql/mysql/full
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束分发mysql脚本'

  echo `date '+%Y-%m-%d %H:%M:%S'`'开始执行mysql脚本'
  sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host 'mysql -u'$mysql_root_username' -p'$mysql_root_password' -h127.0.0.1 -P'$mysql_port' < ~/deploy/onedata/bpms/resources/sql/mysql/full/schema.sql'
  sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host 'mysql -u'$mysql_root_username' -p'$mysql_root_password' -h127.0.0.1 -P'$mysql_port' < ~/deploy/onedata/bpms/resources/sql/mysql/full/init_data.sql'
  sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host 'mysql -u'$mysql_root_username' -p'$mysql_root_password' -h127.0.0.1 -P'$mysql_port' < ~/deploy/onedata/bpms/resources/sql/mysql/full/init_useradmin.sql'
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束执行mysql脚本'
fi

##  配置文件改动,启动项目
# 部署bpms-app
if [[ ${start_app} -eq '1' ]]; then
  if [ -z "bpms_app_deploy" ]; then
    echo "无需部署bpms-app"
  else
    echo `date '+%Y-%m-%d %H:%M:%S'`'开始部署bpms-app'

    echo `date '+%Y-%m-%d %H:%M:%S'`'开始分发bpms-app到'$bpms_app_deploy
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'mkdir -p ~/deploy/onedata/bpms/bpms-app'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'cd ~/deploy/onedata/bpms/bpms-app && rm -rf base.'$version'.properties'
    sshpass -p $devops_deploy_password scp -o StrictHostKeyChecking=no bpms-app-$version.zip $bpms_app_deploy:~/deploy/onedata/bpms/bpms-app
    sshpass -p $devops_deploy_password scp -o StrictHostKeyChecking=no bpms-app-$version.tar $bpms_app_deploy:~/deploy/onedata/bpms/bpms-app
    sshpass -p $devops_deploy_password scp -o StrictHostKeyChecking=no site-packages.tar.gz $bpms_app_deploy:~/deploy/onedata/bpms/bpms-app
    sshpass -p $devops_deploy_password scp -o StrictHostKeyChecking=no lib-dynload.zip $bpms_app_deploy:~/deploy/onedata/bpms/bpms-app
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'cd ~/deploy/onedata/bpms/bpms-app && sudo docker load -i bpms-app-'$version'.tar'
    echo `date '+%Y-%m-%d %H:%M:%S'`'结束分发bpms-app到'$bpms_app_deploy

    echo `date '+%Y-%m-%d %H:%M:%S'`'开始在'$bpms_app_deploy'上修改bpms-app的配置文件'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'cd ~/deploy/onedata/bpms/bpms-app && unzip -o bpms-app-'$version'.zip'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'cd ~/deploy/onedata/bpms/bpms-app && unzip -o lib-dynload.zip'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'cd ~/deploy/onedata/bpms/bpms-app && tar xf site-packages.tar.gz'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'cd ~/deploy/onedata/bpms/bpms-app && cp bpms-app-'$version'/properties/bpms-app.properties base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^database.address=.*/database.address='$mysql_address'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^database.name.bpms=.*/database.name.bpms='$mysql_bpms_database'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^database.username.bpms=.*/database.username.bpms='$mysql_bpms_username'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^database.password.bpms=.*/database.password.bpms='$mysql_bpms_password'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^database.name.dw=.*/database.name.dw='$mysql_onedata_database'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^database.name.compute=.*/database.name.compute='$mysql_compute_database'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^database.username.dw=.*/database.username.dw='$mysql_onedata_username'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^database.password.dw=.*/database.password.dw='$mysql_onedata_password'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^database.name.linkx=.*/database.name.linkx='$mysql_linkx_database'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^database.username.linkx=.*/database.username.linkx='$mysql_linkx_username'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^database.password.linkx=.*/database.password.linkx='$mysql_linkx_password'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s|^authBaseUrl=.*|authBaseUrl='$auth_base_url'|g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s|^licenseBaseUrl=.*|licenseBaseUrl='$license_base_url'|g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s|^zookeeper.address=.*|zookeeper.address='$zookeeper_address'|g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^environment.group=.*/environment.group='$environment_group'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s|^data.clickhouse.address=.*|data.clickhouse.address='$ck_lb_address'|g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^data.clickhouse.username=.*/data.clickhouse.username='$ck_username'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^data.clickhouse.password=.*/data.clickhouse.password='$ck_password'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^redis.host=.*/redis.host='$redis_host'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^redis.port=.*/redis.port='$redis_port'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^redis.password=.*/redis.password='$redis_password'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^kafka.bootstrapServers=.*/kafka.bootstrapServers='$kafka_bootstrap_servers'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^kafka.loadEndFlagTopic=.*/kafka.loadEndFlagTopic='$kafka_loadendflagtopic'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s|^resourceManagerUrl=.*|resourceManagerUrl='$yarnResourcemanagerUrl'|g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^gdp.file.hdfsMode=.*/gdp.file.hdfsMode='$hdfs_mode'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s|^gdp.file.hdfsUrl=.*|gdp.file.hdfsUrl='$hdfs_url'|g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^gdp.file.hdfsUser=.*/gdp.file.hdfsUser='$hdfs_user'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^notification.email=.*/notification.email='$notification_email'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s|^data.clickhouse.main.address=.*|data.clickhouse.main.address='$ck_main_address'|g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s|^data.clickhouse.replica.address=.*|data.clickhouse.replica.address='$ck_replica_address'|g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^ck.ads.database=.*/ck.ads.database='$ck_ads_db'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^ck.dws.database=.*/ck.dws.database='$ck_dws_db'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^ck.dim.database=.*/ck.dim.database='$ck_dim_db'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^ck.dwd.database=.*/ck.dwd.database='$ck_dwd_db'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^ck.ods.database=.*/ck.ods.database='$ck_ods_db'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^ck.skyeye.database=.*/ck.skyeye.database='$ck_skyeye_db'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s|^data.mysql.address=.*|data.mysql.address='$mysql_jdbc_address'|g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^mysql.onedata.database=.*/mysql.onedata.database='$mysql_onedata_database'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^data.mysql.username=.*/data.mysql.username='$mysql_onedata_username'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^data.mysql.password=.*/data.mysql.password='$mysql_onedata_password'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^mysql.product.username=.*/mysql.product.username='$mysql_product_username'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^mysql.product.password=.*/mysql.product.password='$mysql_product_password'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^mysql.product.database=.*/mysql.product.database='$mysql_product_database'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^mysql.lotmanager.database=.*/mysql.lotmanager.database='$mysql_lotmanager_database'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^kafka.repairTopic=.*/kafka.repairTopic='$kafka_repairTopic'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^kafka.repairFinishTopic=.*/kafka.repairFinishTopic='$kafka_repairFinishTopic'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^kafka.repairRecalculateTopic=.*/kafka.repairRecalculateTopic='$kafka_repairRecalculateTopic'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s|^ambari.server.url=.*|ambari.server.url='$ambari_server_url'|g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^ambari.server.username=.*/ambari.server.username='$ambari_server_username'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/^ambari.server.password=.*/ambari.server.password='$ambari_server_password'/g"  ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sed -i "s/\r$//" ~/deploy/onedata/bpms/bpms-app/base.'$version'.properties'
    echo `date '+%Y-%m-%d %H:%M:%S'`'结束在'$bpms_app_deploy'上修改bpms-app的配置文件'


    echo `date '+%Y-%m-%d %H:%M:%S'`'开始启动'$bpms_app_deploy'上的bpms-app'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'cd ~/deploy/onedata/bpms/bpms-app && mkdir -p logs'$i
    GROUP_ID=$(sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'echo `id '$devops_deploy_user' -g`')
    USER_ID=$(sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'echo `id '$devops_deploy_user' -u`')
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'sudo docker run -d \
                                                                                                   --name bpms-app \
                                                                                                   -e JAVA_OPTS="'$bpms_app_config'" \
                                                                                                   -e GROUP_ID='$GROUP_ID' \
                                                                                                   -e ENVIRON_GROUP='$devops_deploy_group' \
                                                                                                   -e USER_ID='$USER_ID' \
                                                                                                   -e ENVIRON_USER='$devops_deploy_user' \
                                                                                                   --hostname `hostname` \
                                                                                                   --network=host \
                                                                                                   -ti \
                                                                                                   -v /etc/hosts:/etc/hosts:ro \
                                                                                                   -v /home/'$devops_deploy_user'/deploy/onedata/bpms/bpms-app/base.'$version'.properties:/home/<USER>/deploy/datahub/bpms/bpms-app/bpms-app-'$version'/properties/bpms-app.properties \
                                                                                                   -v /home/'$devops_deploy_user'/deploy/onedata/bpms/bpms-app/logs:/home/<USER>/deploy/datahub/bpms/bpms-app/bpms-app-'$version'/logs \
                                                                                                   -v /home/'$devops_deploy_user'/deploy/onedata/bpms/bpms-app/site-packages:/opt/python38/lib/python3.8/site-packages \
                                                                                                   -v /home/'$devops_deploy_user'/deploy/onedata/bpms/bpms-app/lib-dynload:/opt/python38/lib/python3.8/lib-dynload \
                                                                                                   -v /home/'$devops_deploy_user'/deploy/bigbrother/skyeye/properties:/home/<USER>/deploy/bigbrother/skyeye/properties \
                                                                                                   -v /usr/share/fonts:/usr/share/fonts \
                                                                                                   -v /tmp:/tmp \
                                                                                                   bpms/bpms-app:'$version
    echo `date '+%Y-%m-%d %H:%M:%S'`'结束启动'$bpms_app_deploy'上的bpms-app'


    echo `date '+%Y-%m-%d %H:%M:%S'`'删除bpms-app的tar包和zip包'
    sshpass -p $devops_deploy_password ssh -o StrictHostKeyChecking=no $bpms_app_deploy 'cd ~/deploy/onedata/bpms/bpms-app && find . -type f \( -name "*.tar" -o -name "*.zip" \) -path "./bpms*" | xargs rm'

    echo `date '+%Y-%m-%d %H:%M:%S'`'结束部署bpms-app'
  fi
fi