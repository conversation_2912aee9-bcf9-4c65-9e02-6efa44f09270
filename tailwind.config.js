/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  theme: {
    screens: {},
    extend: {
      colors: {
        gColorPrimary: '#5d2dcd',
        gTextColorPlaceholder: '#A6A6A6',
        gTextColorRegular: '#595959',
        gTextColorSecondary: '#7f7f7f',
        gSuccessColorBase: '#67C23A',
        gDangerColorBase: '#F56C6C',
      },
    },
  },

  corePlugins: {
    // 禁用所有响应式相关的插件
    responsive: false,
  },
  plugins: [],
};
