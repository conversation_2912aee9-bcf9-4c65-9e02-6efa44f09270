# Repository Guidelines

## 项目结构与模块划分
- 核心代码位于`src`，`views`用于页面级视图，每个子目录聚焦单一业务域，并通过`router`中的懒加载路由装配，便于按需拆分。
- `components`承载跨页面复用组件，配合`plugins/svgBuilder.ts`自动注册图标；`layout`维护壳层、导航与面包屑结构，更新时同步调整`router`中的嵌套路由。
- 状态管理集中在`store`（Pinia），API调用统一放在`service`并通过`axios`实例处理拦截器；`hooks`、`utils`提供组合式逻辑，`theme.ts`与`style.scss`管理主题和基础样式。
- 静态资源放在`src/assets`与`public`，`setup/sentry.ts`负责Sentry初始化，路径别名`@`指向`src`，确保导入保持相对独立。

## 构建、测试与开发命令
- 使用`pnpm install`安装依赖，保持与`pnpm-lock.yaml`一致；首次克隆后执行`pnpm prepare`以安装husky钩子。
- `pnpm dev`启动Vite开发服务器，读取`.env.local`或`.env.development`中的`VITE_APP_*`变量并激活代理；如需局域网调试可直接访问提示的外网地址。
- `pnpm build`生成生产包，配合`pnpm preview`在本地验证部署结果；CI脚本可直接复用这两条命令。
- 质量检查可通过`pnpm exec vue-tsc --noEmit`执行类型验证，`pnpm exec eslint "src/**/*.{ts,tsx,vue}" --max-warnings=0`确保风格一致；在提交前可运行`pnpm lint-staged`模拟钩子。

## 代码风格与命名规范
- 默认使用TypeScript与Vue 3 `<script setup>`语法，模板、脚本和样式均采用2空格缩进，并优先保持模板块顺序：`template` → `script setup` → `style`。
- 组件与自定义Hooks使用PascalCase命名（如`useConfig.ts`），路由页面采用业务词+Page格式，静态资源使用kebab-case或数字后缀区分版本。
- 样式文件通过Tailwind与SCSS混合策略管理，全局变量集中在`style.scss`，主题重写放在`theme.ts`；避免在组件内硬编码颜色，可改用CSS变量或`@guwave`主题变量。
- 自动导入由`unplugin-auto-import`和`unplugin-vue-components`控制，新增全局工具时在`vite.config.ts`同步配置，避免重复显式`import`触发ESLint告警。

## 测试指引
- 当前`pnpm test`仅输出占位信息，新增测试建议引入Vitest或Cypress；单元测试文件以`*.spec.ts`命名并紧邻被测模块，方便按目录运行。
- 流程配置、图编辑器、国际化等关键能力应覆盖正向场景与异常路径，建议保持≥80%语句覆盖，并在PR描述中贴出`vitest --coverage`结果。
- 端到端测试可放置在`tests/e2e`，通过`pnpm exec playwright test`或`pnpm exec cypress run`等命令执行，需在README记录运行前置条件。

## 提交与合并规范
- 遵循历史习惯，提交信息可使用`[story-数字] 动词+对象`格式，例如`[story-1001802] 调整流程图节点显示`，便于追踪需求。
- 提交前确保`lint-staged`钩子通过，如需手动修复可执行`pnpm exec eslint --fix`和`pnpm exec stylelint`（若引入）；仓库提供`pnpm commit`调用commitizen交互式生成信息。
- 发起PR时需包含变更背景、核心改动列表、受影响的页面截图或GIF，以及关联的需求/缺陷链接；大体积变更应拆分为多PR或分批提交以降低评审压力。
- 若改动涉及接口或环境变量，需在PR内同步附加配置说明和回滚策略。

## 配置与安全提示
- 所有后端地址与代理前缀通过`VITE_APP_*`变量注入，示例见`vite.config.ts`的`server.proxy`；请将实际密钥存放在未提交的`.env.local`中。
- 新增第三方脚本或微前端入口时，务必在`public`目录内隔离静态资源，并在`src/setup`中新建初始化文件以统一注册，防止污染全局作用域。
- Sentry初始化在`src/setup/sentry.ts`进行，需要更新DSN或采样率时，同时检查`hooks/useConfig.ts`确保配置顺序正确，避免在首次渲染前遗漏异常捕获。

## 架构与扩展建议
- 业务模块普遍按领域划分（如`src/store/conf-mgt`与`src/service/conf-mgt`），新增能力时优先沿用相同命名与目录结构，避免跨域耦合。
- 路由与权限解耦：在`src/router`中定义基础路由，再由`store/permission`注入权限菜单，新增页面需同时补齐两处配置。
- 若需要与第三方平台交互，可在`src/plugins`增加独立插件并通过`main.ts`注册，确保初始化逻辑集中可测。

## 协作与知识共享
- 复杂流程的关键设计（状态机、数据契约、接口依赖）建议在`README`或`docs/`目录补充图示与示例代码，便于后续维护。
- 评审意见应在PR内回复或追加commit处理，避免线下沟通遗失结论；涉及多团队时可在描述中@对应负责人。
- 里程碑版本发布后请更新`CHANGELOG`或在团队wiki记录版本差异和回滚指引，保持跨版本迁移透明。

## 依赖与版本策略
- 核心运行时依赖包含 Vue 3、Element Plus、Naive UI、Pinia、axios 以及 @guwave 系列内部组件库，升级前请核对它们的 release note 并在孤立分支验证兼容性。
- Vite 与 TypeScript 版本更新需同步检查 `tsconfig.*`、`vite.config.ts`、`auto-imports.d.ts` 等配置文件，避免生成的类型声明失步。
- 若引入新的第三方库，请优先通过 `pnpm add <package>` 安装并记录用途，体积较大的库应在 PR 中说明分包方案与性能影响。

## 本地调试提示
- 推荐使用 VS Code 配合 Volar 与 ESLint 插件，设置 `editor.codeActionsOnSave` 自动触发 `source.fixAll.eslint`，保证与 lint-staged 行为一致。
- 代理依赖 `.env` 中的 `VITE_APP_BASE_PROXY`、`VITE_APP_BACKEND_HOST` 等变量，调试他人环境时仅需替换这些变量即可共享配置。
- 浏览器调试建议开启 Vue Devtools 与 Pinia Devtools，定位状态更新问题时可直接追踪到对应的 store action。
