# Repository Guidelines

## 项目结构与模块组织
- 根模块由 Gradle 7.2 管理，核心子模块包括 `bpms-app`（Spring Boot 主入口，启动类 `com.guwave.datahub.bpms.app.BpmsAppApplication`）、`bpms-dao`（MyBatis/JPA 数据层）与 `bpms-common`（共享模型与工具）。
- 运行与模板资源位于 `bpms-app/src/main/resources`，SQL 升级脚本按版本归档在 `resources/sql/mysql/v<version>`，Docker 镜像脚本存于 `bpms-app/image`。
- 打包输出统一进入各模块的 `target` 目录，清理时使用 `./gradlew clean`，避免手动删除遗留文件。
- 建议在各模块下创建 `src/test/java` 与 `src/test/resources` 以保持测试结构对齐。

- `JAVA_HOME=/Library/Java/JavaVirtualMachines/zulu8.76.0.17-ca-jdk8.0.402-macosx_x64/Contents/Home GRADLE_USER_HOME=$PWD/.gradle ./gradlew --no-daemon :bpms-app:compileJava`：执行编译检查是否有报错。

## 编码风格与命名约定
- Java 源码统一使用 UTF-8、Java 8 语法与四空格缩进，遵循 Spring Bean 命名惯例，包名保持小写层级（例如 `com.guwave.datahub.bpms.dao`）。
- Lombok 被广泛使用，请在 IDE 启用注解处理；公共常量集中到 `bpms-common`，命名采用全大写蛇形。
- REST 接口路径保持动词-名词组合，如 `/api/tasks/export`，返回对象以 `Dto`/`Vo` 收尾。
- 数据访问 XML 位于 `bpms-app/src/main/resources/mapper`，命名应与对应仓储类一致，以 `Mapper.xml` 结尾，SQL 标签内保持两空格缩进。

## 测试准则
- 依赖 `spring-boot-starter-test`，默认使用 JUnit 5；类名以 `*Test`/`*IT` 结尾，方法命名推荐 Given_When_Then 格式。
- 集成测试可使用 `@SpringBootTest`，数据访问测试选择 `@DataJpaTest` 或嵌入式数据库。
- 新增功能需附带正向与异常路径测试，提交前运行 `./gradlew test` 并确认覆盖核心逻辑，并在 PR 中同步记录关键覆盖率（目标 >70%）。

## 提交与 Pull Request 准则
- Git 历史遵循 `[story-123456] 变更摘要` 前缀格式，保持中文语义清晰，必要时补充影响模块与风险点。
- PR 描述应包含需求链接、主要改动、测试结论；涉及界面或接口返回变化时附截图或示例 JSON。
- 提交前请自查冲突与静态检查输出，指派至少一位模块负责人评审。
- 推送前执行 `git config core.hooksPath .githooks` 启用仓库钩子，确保统一检查。

## 配置与安全提示
- 私有仓库凭据位于 `gradle.properties`，仅供本地构建使用，勿在公共渠道扩散。
- 环境差异配置请放至 `deploy.properties` 或模块内 `properties` 子目录，通过外部化配置注入，避免硬编码敏感信息。
- 部署包会生成 `deploy/<version>` 与 `deploy/<version>_thin` 两套目录，thin 包供增量分发使用，请按运维要求挑选上传。
