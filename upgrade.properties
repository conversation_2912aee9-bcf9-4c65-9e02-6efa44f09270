# 需要部署的步骤
kill_app=1
start_app=1
exec_patch=1

# 配置文件修改,多个配置用|分隔
bpms_app_add_before=
bpms_app_add_after="data.replay.file.maxCount:data.replay.file.batchSize=1000"
bpms_app_delete=
bpms_app_update="data.replay.file.maxCount=10000"

# 需要增加的topic，多个之间用英文逗号分割
bpms_topics_add=

declare -A properties_map
properties_map["database.address"]="mysql_address"
properties_map["database.name.bpms"]="mysql_bpms_database"
properties_map["database.username.bpms"]="mysql_bpms_username"
properties_map["database.password.bpms"]="mysql_bpms_password"
properties_map["database.name.dw"]="mysql_onedata_database"
properties_map["database.name.compute"]="mysql_compute_database"
properties_map["database.username.dw"]="mysql_onedata_username"
properties_map["database.password.dw"]="mysql_onedata_password"
properties_map["database.name.linkx"]="mysql_linkx_database"
properties_map["database.username.linkx"]="mysql_linkx_username"
properties_map["database.password.linkx"]="mysql_linkx_password"
properties_map["authBaseUrl"]="auth_base_url"
properties_map["licenseBaseUrl"]="license_base_url"
properties_map["zookeeper.address"]="zookeeper_address"
properties_map["data.clickhouse.address"]="ck_address"
properties_map["data.clickhouse.main.address"]="ck_main_address"
properties_map["data.clickhouse.replica.address"]="ck_replica_address"
properties_map["data.clickhouse.username"]="ck_username"
properties_map["data.clickhouse.password"]="ck_password"
properties_map["ck.ads.database"]="ck_ads_db"
properties_map["ck.dws.database"]="ck_dws_db"
properties_map["ck.dim.database"]="ck_dim_db"
properties_map["ck.dwd.database"]="ck_dwd_db"
properties_map["ck.ods.database"]="ck_ods_db"
properties_map["ck.skyeye.database"]="ck_skyeye_db"
properties_map["data.mysql.address"]="mysql_jdbc_address"
properties_map["mysql.onedata.database"]="mysql_onedata_database"
properties_map["data.mysql.username"]="mysql_onedata_username"
properties_map["data.mysql.password"]="mysql_onedata_password"
properties_map["mysql.product.username"]="mysql_product_username"
properties_map["mysql.product.password"]="mysql_product_password"
properties_map["mysql.product.database"]="mysql_product_database"
properties_map["mysql.lotmanager.database"]="mysql_lotmanager_database"
properties_map["redis.host"]="redis_host"
properties_map["redis.port"]="redis_port"
properties_map["redis.password"]="redis_password"
properties_map["kafka.bootstrapServers"]="kafka_bootstrap_servers"
properties_map["resourceManagerUrl"]="yarnResourcemanagerUrl"
properties_map["gdp.file.hdfsMode"]="hdfs_mode"
properties_map["gdp.file.hdfsUrl"]="hdfs_url"
properties_map["gdp.file.hdfsUser"]="hdfs_user"
properties_map["adapter.python.install.path"]="adapter_python_install_path"
properties_map["notification.email"]="notification_email"
properties_map["ambari.server.url"]="ambari_server_url"
properties_map["ambari.server.username"]="ambari_server_username"
properties_map["ambari.server.password"]="ambari_server_password"

