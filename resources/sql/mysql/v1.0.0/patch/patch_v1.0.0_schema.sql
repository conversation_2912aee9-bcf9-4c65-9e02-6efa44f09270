USE `bpms`;

alter table bz_data_replay_record add column sblot_id_list varchar(256) null comment 'sblot id，可能有多个' after wafer_no_list;
alter table bz_data_replay_record add column filter_process_status_list varchar(256) null comment 'process_status，可能有多个' after step;
alter table bz_data_replay_record add column filter_exception_message longtext null comment 'exception_message' after filter_process_status_list;
alter table bz_data_replay_record add column filter_file_warehousing_record_id longtext null comment '提交重播任务勾选的dw_file_warehousing_record id' after filter_exception_message;
alter table bz_data_replay_record add column filter_step_list varchar(256) DEFAULT NULL COMMENT '重播的文件的处理步骤，可能有多个' AFTER filter_file_warehousing_record_id;
alter table bz_data_replay_record add column start_create_time varchar(256) null comment '重播的文件create_time开始时间' after filter_step_list;
alter table bz_data_replay_record add column end_create_time varchar(256) null comment '重播的文件create_time结束时间' after start_create_time;
alter table bz_data_replay_record add column start_update_time varchar(256) null comment '重播的文件update_time开始时间' after end_create_time;
alter table bz_data_replay_record add column end_update_time varchar(256) null comment '重播的文件update_time结束时间' after start_update_time;
alter table bz_data_replay_record add column file_warehousing_record_id longtext null comment '重播任务实际处理的dw_file_warehousing_record id' after end_update_time;
alter table bz_data_replay_record add column remote_file_path varchar(1024) DEFAULT NULL COMMENT 'file远程路径' AFTER file_name;
alter table bz_data_replay_record modify lot_type varchar(64) null comment 'Lot类型，Engineering/Production等';

create table if not exists `bz_module_scheduled_control_switch`
(
    `id`          bigint NOT NULL AUTO_INCREMENT,
    `module`      varchar(255) DEFAULT NULL,
    `stop_flag`   int          DEFAULT NULL,
    `create_time` datetime(3)  DEFAULT CURRENT_TIMESTAMP(3),
    `create_user` varchar(255) DEFAULT NULL,
    `update_time` datetime(3)  DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    `update_user` varchar(255) DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
    COMMENT ='定时任务开关表';
