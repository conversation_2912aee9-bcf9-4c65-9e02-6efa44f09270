USE `compute`;
UPDATE compute.bz_moudule_info SET version = '1.0.0', update_time = now() where moudule = 'bpms';

USE `bpms`;

update bz_data_replay_record set factory = concat('["', replace(factory, ',', '","'), '"]') where factory is not null and factory not like '[%]';
update bz_data_replay_record set device_id = concat('["', replace(device_id, ',', '","'), '"]') where device_id is not null and device_id not like '[%]';
update bz_data_replay_record set lot_id_list = concat('["', replace(lot_id_list, ',', '","'), '"]') where lot_id_list is not null and lot_id_list not like '[%]';
update bz_data_replay_record set wafer_no_list = concat('["', replace(wafer_no_list, ',', '","'), '"]') where wafer_no_list is not null and wafer_no_list not like '[%]';
update bz_data_replay_record set test_stage_list = concat('["', replace(test_stage_list, ',', '","'), '"]') where test_stage_list is not null and test_stage_list not like '[%]';


INSERT INTO bz_module_scheduled_control_switch(module, stop_flag, create_time, create_user, update_time, update_user)
VALUES ('bpms-app', 1, now(), 'System', now(), 'System');