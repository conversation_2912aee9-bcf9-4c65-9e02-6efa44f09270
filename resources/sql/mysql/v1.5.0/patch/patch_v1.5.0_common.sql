USE `compute`;
UPDATE compute.bz_moudule_info SET version = '1.5.0', update_time = now() where moudule = 'bpms';



use `bpms`;


INSERT INTO bz_business_fail_record_query_condition (db, `table`, query_condition, create_time, update_time,
                                                     create_user, update_user)
VALUES ('onedata', 'dw_sftp_file_detail', ' test_area = \'WAT\' and convert_flag = 0',
        now(), now(), 'System', 'System');


INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoPlatformOverview', 'CP_WAFER_CNT', '', 'select ifnull(count(distinct lot_id, wafer_no), 0) cpWaferCnt
from dw_lot_wafer_cal_status
where die_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and die_end_time < CURRENT_DATE()
  and die_status in (\'SUCCESS\')
  and test_area in (\'CP\', \'CP(Map)\', \'CP(InklessMap)\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoPlatformOverview', 'FT_LOT_CNT', '', 'select ifnull(count(distinct lot_id), 0) ftLotCnt
from dw_lot_wafer_cal_status
where die_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and die_end_time < CURRENT_DATE()
  and die_status in (\'SUCCESS\')
  and test_area not in (\'CP\', \'CP(Map)\', \'CP(InklessMap)\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoPlatformOverview', 'DIE_CNT', '', 'select ifnull(sum(die_total_cnt), 0) dieCnt
from dw_lot_wafer_cal_status
where die_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and die_end_time < CURRENT_DATE()
  and die_status in (\'SUCCESS\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoPlatformOverview', 'TEST_ITEM_CNT', '', 'select ifnull(sum(test_item_total_cnt), 0) testItemCnt
from dw_lot_wafer_cal_status
where test_item_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and test_item_end_time < CURRENT_DATE()
  and test_item_status in (\'SUCCESS\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoPlatformOverview', 'AUTO_MANUAL_RERUN_TASK', '', 'select ifnull(sum(if(auto_rerun > 0, auto_rerun, 0)), 0)     dieTaskCntAutoRerun,
       ifnull(sum(if(manual_rerun > 0, manual_rerun, 0)), 0) dieTaskCntManualRerun
from (select if(new_cal_cnt = 1, 0,
                if(old_cal_cnt = 0, if(new_manual_cnt = 0, new_cal_cnt - 1, new_cal_cnt - new_manual_cnt),
                   (new_cal_cnt - new_manual_cnt) - (old_cal_cnt - old_manual_cnt))) auto_rerun,
             new_manual_cnt - old_manual_cnt                                         manual_rerun
      from (select a.cal_cnt               new_cal_cnt,
                   a.manual_cnt            new_manual_cnt,
                   ifnull(b.cal_cnt, 0)    old_cal_cnt,
                   ifnull(b.manual_cnt, 0) old_manual_cnt
            from (select customer,
                         factory,
                         test_area,
                         device_id,
                         lot_id,
                         wafer_no,
                         test_stage,
                         lot_type,
                         file_category,
                         cal_cnt,
                         replay_cnt + repair_cnt manual_cnt
                  from dw_lot_wafer_cal_record
                  where id in (select max(id)
                               from dw_lot_wafer_cal_record
                               where create_time >= CURRENT_DATE() - INTERVAL 1 DAY
                                 and create_time < CURRENT_DATE()
                               group by customer,
                                        factory,
                                        test_area,
                                        device_id,
                                        lot_id,
                                        wafer_no,
                                        test_stage,
                                        lot_type,
                                        file_category)) a
                     left join (select customer,
                                       factory,
                                       test_area,
                                       device_id,
                                       lot_id,
                                       wafer_no,
                                       test_stage,
                                       lot_type,
                                       file_category,
                                       cal_cnt,
                                       replay_cnt + repair_cnt manual_cnt
                                from dw_lot_wafer_cal_record
                                where id in (select max(id)
                                             from dw_lot_wafer_cal_record
                                             where create_time < CURRENT_DATE() - INTERVAL 1 DAY
                                             group by customer,
                                                      factory,
                                                      test_area,
                                                      device_id,
                                                      lot_id,
                                                      wafer_no,
                                                      test_stage,
                                                      lot_type,
                                                      file_category)) b
                               on
                                           a.customer = b.customer and
                                           a.factory = b.factory and
                                           a.test_area = b.test_area and
                                           a.device_id = b.device_id and
                                           a.lot_id = b.lot_id and
                                           a.wafer_no = b.wafer_no and
                                           a.test_stage = b.test_stage and
                                           a.lot_type = b.lot_type and
                                           a.file_category = b.file_category) x) y', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'FILE_SIZE', '', 'select count(1)                                                        fileCnt,
       round(ifnull(sum(file_size), 0) / 1024 / 1024 / 1024, 3)        fileSizeCompress,
       round(ifnull(sum(origin_file_size), 0) / 1024 / 1024 / 1024, 3) fileSizeOrigin
from dw_sftp_file_detail
where create_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and create_time < CURRENT_DATE()
  and source_file_names is null', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'CONVERT_FILE_FINISH', '', 'select ifnull(sum(if(process_status = \'FAIL\', 1, 0)), 0)    convertFileCntFail,
       ifnull(sum(if(process_status = \'SUCCESS\', 1, 0)), 0) convertFileCntSuccess
from dw_sftp_file_detail
where update_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and update_time < CURRENT_DATE()
  and convert_flag = 1
  and process_status in (\'FAIL\', \'SUCCESS\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'CONVERT_FILE_NOT_FINISH', '', 'select ifnull(sum(if(process_status = \'CREATE\', 1, 0)), 0)     convertFileCntPending,
       ifnull(sum(if(process_status = \'PROCESSING\', 1, 0)), 0) convertFileCntProcessing
from dw_sftp_file_detail
where convert_flag = 1
  and process_status in (\'CREATE\', \'PROCESSING\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'PRE_PARSE_FILE_FINISH', '', 'select ifnull(sum(if(process_status = \'FAIL\', 1, 0)), 0)    preParsefileCntFail,
       ifnull(sum(if(process_status = \'SUCCESS\', 1, 0)), 0) preParsefileCntSuccess
from dw_sftp_file_detail
where update_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and update_time < CURRENT_DATE()
  and convert_flag = 0
  and process_status in (\'FAIL\', \'SUCCESS\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'PRE_PARSE_FILE_NOT_FINISH', '', 'select ifnull(sum(if(process_status = \'CREATE\', 1, 0)), 0)     preParsefileCntPending,
       ifnull(sum(if(process_status = \'PROCESSING\', 1, 0)), 0) preParsefileCntProcessing
from dw_sftp_file_detail
where convert_flag = 0
  and process_status in (\'CREATE\', \'PROCESSING\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'PARSE_FILE_FINISH', '', 'select ifnull(sum(if(process_status = \'FAIL\', 1, 0)), 0)    parsefileCntFail,
       ifnull(sum(if(process_status = \'SUCCESS\', 1, 0)), 0) parsefileCntSuccess
from dw_lot_meta_data_detail
where update_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and update_time < CURRENT_DATE()
  and process_status in (\'FAIL\', \'SUCCESS\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'PARSE_FILE_NOT_FINISH', '', 'select ifnull(sum(if(process_status = \'CREATE\', 1, 0)), 0)     parsefileCntPending,
       ifnull(sum(if(process_status = \'PROCESSING\', 1, 0)), 0) parsefileCntProcessing
from dw_lot_meta_data_detail
where process_status in (\'CREATE\', \'PROCESSING\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'DIE_TASK_FINISH', '', 'select ifnull(sum(if(process_status = \'FAIL\', 1, 0)), 0)    dieTaskCntFail,
       ifnull(sum(if(process_status = \'SUCCESS\', 1, 0)), 0) dieTaskCntSuccess
from dw_layer_calculate_pool
where update_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and update_time < CURRENT_DATE()
  and dw_layer = \'DWD\'
  and process_status in (\'FAIL\', \'SUCCESS\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'DIE_TASK_NOT_FINISH', '', 'select ifnull(sum(if(process_status = \'CREATE\', 1, 0)), 0)     dieTaskCntPending,
       ifnull(sum(if(process_status = \'PROCESSING\', 1, 0)), 0) dieTaskCntProcessing
from dw_layer_calculate_pool
where dw_layer = \'DWD\'
  and process_status in (\'CREATE\', \'PROCESSING\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'TEST_ITEM_TASK_FINISH', '', 'select ifnull(sum(if(test_item_status = \'FAIL\', 1, 0)), 0)    testItemTaskCntFail,
       ifnull(sum(if(test_item_status = \'SUCCESS\', 1, 0)), 0) testItemTaskCntSuccess
from dw_lot_wafer_cal_record
where update_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and update_time < CURRENT_DATE()
  and test_item_status in (\'FAIL\', \'SUCCESS\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'TEST_ITEM_TASK_NOT_FINISH', '', 'select ifnull(sum(if(test_item_status = \'CREATE\', 1, 0)), 0)     testItemTaskCntPending,
       ifnull(sum(if(test_item_status = \'PROCESSING\', 1, 0)), 0) testItemTaskCntProcessing
from dw_lot_wafer_cal_status
where die_status = \'SUCCESS\'
  and test_item_status in (\'CREATE\', \'PROCESSING\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'SCAN_FILE_CONSUME', '', 'select round(ifnull(avg(timestampdiff(second, create_time, update_time)), 0), 0) scanFileConsumeAvg,
       ifnull(min(timestampdiff(second, create_time, update_time)), 0)           scanFileConsumeMin,
       ifnull(max(timestampdiff(second, create_time, update_time)), 0)           scanFileConsumeMax,
       count(1)                                                                  scanFileConsumeCnt
from dw_sftp_batch_info
where update_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and update_time < CURRENT_DATE()
  and batch_status in (\'SUCCESS\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'DOWNLOAD_FILE_CONSUME', '', 'select round(ifnull(avg(timestampdiff(second, step_start_time, step_end_time)), 0), 0) downloadFileConsumeAvg,
       ifnull(min(timestampdiff(second, step_start_time, step_end_time)), 0)           downloadFileConsumeMin,
       ifnull(max(timestampdiff(second, step_start_time, step_end_time)), 0)           downloadFileConsumeMax,
       count(1)                                                                        downloadFileConsumeCnt
from dw_file_loading_log
where step_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and step_end_time < CURRENT_DATE()
  and step = 1000
  and file_name like \'/%\'
  and process_status in (\'SUCCESS\', \'FAIL\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'CONVERT_FILE_CONSUME', '', 'select round(ifnull(avg(timestampdiff(second, step_start_time, step_end_time)), 0), 0) convertfileConsumeAvg,
       ifnull(min(timestampdiff(second, step_start_time, step_end_time)), 0)           convertfileConsumeMin,
       ifnull(max(timestampdiff(second, step_start_time, step_end_time)), 0)           convertfileConsumeMax,
       count(1)                                                                        convertfileConsumeCnt
from dw_file_loading_log
where step_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and step_end_time < CURRENT_DATE()
  and step = 2200
  and process_status in (\'SUCCESS\', \'FAIL\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'PRE_PARSE_FILE_CONSUME', '', 'select round(ifnull(avg(timestampdiff(second, step_start_time, step_end_time)), 0), 0) perParsefileConsumeAvg,
       ifnull(min(timestampdiff(second, step_start_time, step_end_time)), 0)           perParsefileConsumeMin,
       ifnull(max(timestampdiff(second, step_start_time, step_end_time)), 0)           preParsefileConsumeMax,
       count(1)                                                                        preParsefileConsumeCnt
from dw_file_loading_log
where step_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and step_end_time < CURRENT_DATE()
  and step = 3100
  and process_status in (\'SUCCESS\', \'FAIL\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'PARSE_FILE_CONSUME', '', 'select round(ifnull(avg(timestampdiff(second, step_start_time, step_end_time)), 0), 0) parsefileConsumeAvg,
       ifnull(min(timestampdiff(second, step_start_time, step_end_time)), 0)           parsefileConsumeMin,
       ifnull(max(timestampdiff(second, step_start_time, step_end_time)), 0)           parsefileConsumeMax,
       count(1)                                                                        parsefileConsumeCnt
from dw_file_loading_log
where step_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and step_end_time < CURRENT_DATE()
  and step = 4100
  and process_status in (\'SUCCESS\', \'FAIL\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'compute', 'autoTaskOverview', 'DIE_TASK_CONSUME', '', 'select round(ifnull(avg(execute_time), 0) / 1000, 0) dieTaskConsumeAvg,
       round(ifnull(min(execute_time), 0) / 1000, 0) dieTaskConsumeMin,
       round(ifnull(max(execute_time), 0) / 1000, 0) dieTaskConsumeMax,
       count(1)                                      dieTaskConsumeCnt
from bz_compute_pool
where end_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and end_time < CURRENT_DATE()
  and process_status in (\'SUCCESS\', \'FAIL\')
  and queue in (\'die\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'compute', 'autoTaskOverview', 'TEST_ITEM_TASK_CONSUME', '', 'select round(ifnull(avg(execute_time), 0) / 1000, 0) testItemTaskConsumeAvg,
       round(ifnull(min(execute_time), 0) / 1000, 0) testItemTaskConsumeMin,
       round(ifnull(max(execute_time), 0) / 1000, 0) testItemTaskConsumeMax,
       count(1)                                      testItemTaskConsumeCnt
from bz_compute_pool
where end_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and end_time < CURRENT_DATE()
  and process_status in (\'SUCCESS\', \'FAIL\')
  and queue in (\'testitem\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'DIE_WAIT_TIME', '', 'select round(ifnull(avg(wait_time), 0) / 1000, 0) dieWaitTimeAvg,
       round(ifnull(min(wait_time), 0) / 1000, 0) dieWaitTimeMin,
       round(ifnull(max(wait_time), 0) / 1000, 0) dieWaitTimeMax,
       count(1)                                   dieWaitTimeCnt
from (select timestampdiff(second, substring_index(file_pre_parse_times, \'~\', -1),
                           substring_index(die_cal_time, \'~\', 1)) wait_time
      from dw_lot_wafer_cal_record
      where update_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and update_time < CURRENT_DATE()
        and die_status in (\'SUCCESS\')) a', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'TEST_ITEM_WAIT_TIME', '', 'select round(ifnull(avg(wait_time), 0) / 1000, 0) testItemWaitTimeAvg,
       round(ifnull(min(wait_time), 0) / 1000, 0) testItemWaitTimeMin,
       round(ifnull(max(wait_time), 0) / 1000, 0) testItemWaitTimeMax,
       count(1)                                   testItemWaitTimeCnt
from (select timestampdiff(second, substring_index(die_cal_time, \'~\', -1),
                           substring_index(test_item_cal_time, \'~\', 1)) wait_time
      from dw_lot_wafer_cal_record
      where update_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and update_time < CURRENT_DATE()
        and test_item_status in (\'SUCCESS\')) a', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'manualTaskOverview', 'FILE_SIZE', '', 'select count(1)                                                 fileCnt,
       round(ifnull(sum(file_size), 0) / 1024 / 1024 / 1024, 3) fileSize
from dw_manual_file_info
where create_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and create_time < CURRENT_DATE()
  and origin_file_ids = \'\'', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'manualTaskOverview', 'PARSE_FILE_FINISH', '', 'select ifnull(sum(if(process_status = \'FAIL\', 1, 0)), 0)    parsefileCntFail,
       ifnull(sum(if(process_status = \'SUCCESS\', 1, 0)), 0) parsefileCntSuccess
from dw_manual_calculate_task
where update_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and update_time < CURRENT_DATE()
  and manual_type = \'UPLOAD\'
  and dw_layer = \'ODS\'
  and process_status in (\'FAIL\', \'SUCCESS\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'manualTaskOverview', 'PARSE_FILE_NOT_FINISH', '', 'select ifnull(sum(if(process_status = \'CREATE\', 1, 0)), 0)     parsefileCntPending,
       ifnull(sum(if(process_status = \'PROCESSING\', 1, 0)), 0) parsefileCntProcessing
from dw_manual_calculate_task
where manual_type = \'UPLOAD\'
  and dw_layer = \'ODS\'
  and process_status in (\'CREATE\', \'PROCESSING\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'manualTaskOverview', 'WAREHOUSE_TASK_FINISH', '', 'select ifnull(sum(if(process_status = \'FAIL\', 1, 0)), 0)    wareHouseTaskCntFail,
       ifnull(sum(if(process_status = \'SUCCESS\', 1, 0)), 0) wareHouseTaskCntSuccess
from dw_manual_calculate_task
where update_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and update_time < CURRENT_DATE()
  and dw_layer = \'DWD\'
  and process_status in (\'FAIL\', \'SUCCESS\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'manualTaskOverview', 'WAREHOUSE_TASK_NOT_FINISH', '', 'select ifnull(sum(if(process_status = \'CREATE\', 1, 0)), 0)     wareHouseTaskCntPending,
       ifnull(sum(if(process_status = \'PROCESSING\', 1, 0)), 0) wareHouseTaskCntProcessing
from dw_manual_calculate_task
where dw_layer = \'DWD\'
  and process_status in (\'CREATE\', \'PROCESSING\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'manualTaskOverview', 'PARSE_FILE_CONSUME', '', 'select round(ifnull(avg(timestampdiff(second, create_time, update_time)), 0), 0) parsefileConsumeAvg,
       ifnull(min(timestampdiff(second, create_time, update_time)), 0)           parsefileConsumeMin,
       ifnull(max(timestampdiff(second, create_time, update_time)), 0)           parsefileConsumeMax,
       count(1)                                                                  parsefileConsumeCnt
from dw_manual_calculate_task
where update_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and update_time < CURRENT_DATE()
  and manual_type = \'UPLOAD\'
  and dw_layer = \'ODS\'
  and process_status in (\'SUCCESS\', \'FAIL\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'manualTaskOverview', 'WAREHOUSE_TASK_CONSUME', '', 'select round(ifnull(avg(timestampdiff(second, create_time, update_time)), 0), 0) wareHouseTaskConsumeAvg,
       ifnull(min(timestampdiff(second, create_time, update_time)), 0)           wareHouseTaskConsumeMin,
       ifnull(max(timestampdiff(second, create_time, update_time)), 0)           wareHouseTaskConsumeMax,
       count(1)                                                                  wareHouseTaskConsumeCnt
from dw_manual_calculate_task
where update_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and update_time < CURRENT_DATE()
  and dw_layer = \'DWD\'
  and process_status in (\'SUCCESS\', \'FAIL\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskSlow', 'TOP_10', '', 'select taskName, startTime, endTime, executeTime, detail
from (select regexp_replace(app_name, \'.*\\.impl\\.\', \'\')   taskName,
             date_format(start_time, \'%Y-%m-%d %H:%i:%s\') startTime,
             date_format(end_time, \'%Y-%m-%d %H:%i:%s\')   endTime,
             round(execute_time / 1000, 0)                executeTime,
             if(queue = \'die\', \'Die任务\', \'测项任务\')     detail
      from compute.bz_compute_pool
      where end_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and end_time < CURRENT_DATE()
        and process_status in (\'FAIL\', \'SUCCESS\')
        and queue in (\'die\', \'testitem\')
      union
      select file_name                                             taskName,
             date_format(step_start_time, \'%Y-%m-%d %H:%i:%s\')     startTime,
             date_format(step_end_time, \'%Y-%m-%d %H:%i:%s\')       endTime,
             timestampdiff(second, step_start_time, step_end_time) executeTime,
             case step
                 when 1000 then \'文件拉取\'
                 when 2100 then \'文件转换前读取\'
                 when 2200 then \'文件转换\'
                 when 3100 then \'文件预解析\'
                 when 4100 then \'文件正式解析\'
                 when 7100 then \'文件入库\'
                 else \'\'
                 end
                                                                   detail
      from dw_file_loading_log
      where (step in (1000, 2100, 2200, 3100, 4100) or
             (step = 7100 and file_category not in (\'STDF\', \'RAW_DATA\', \'BIT_MEM\')))
        and process_status in (\'FAIL\', \'SUCCESS\')
        and step_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and step_end_time < CURRENT_DATE()) x
order by executeTime desc
limit 10', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskFail', 'FAIL_DETAIL', '', 'select taskName, startTime, endTime, executeTime, detail, exceptionMessage
from (select regexp_replace(app_name, \'.*\\.impl\\.\', \'\')   taskName,
             date_format(start_time, \'%Y-%m-%d %H:%i:%s\') startTime,
             date_format(end_time, \'%Y-%m-%d %H:%i:%s\')   endTime,
             round(execute_time / 1000, 0)                executeTime,
             if(queue = \'die\', \'Die任务\', \'测项任务\')     detail,
             substr(error_message, 1, 1000)               exceptionMessage
      from compute.bz_compute_pool
      where end_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and end_time < CURRENT_DATE()
        and process_status = \'FAIL\'
        and queue in (\'die\', \'testitem\')
      union
      select file_name                                             taskName,
             date_format(step_start_time, \'%Y-%m-%d %H:%i:%s\')     startTime,
             date_format(step_end_time, \'%Y-%m-%d %H:%i:%s\')       endTime,
             timestampdiff(second, step_start_time, step_end_time) executeTime,
             case step
                 when 1000 then \'文件拉取\'
                 when 2100 then \'文件转换前读取\'
                 when 2200 then \'文件转换\'
                 when 3100 then \'文件预解析\'
                 when 4100 then \'文件正式解析\'
                 when 7100 then \'文件入库\'
                 else \'\'
                 end
                                                                   detail,
             substr(error_message, 1, 1000)                        exceptionMessage
      from dw_file_loading_log
      where (step in (1000, 2100, 2200, 3100, 4100) or
             (step = 7100 and file_category not in (\'STDF\', \'RAW_DATA\', \'BIT_MEM\')))
        and process_status = \'FAIL\'
        and step_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and step_end_time < CURRENT_DATE()) x
order by endTime desc
limit 100', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'manualTaskSlow', 'TOP_10', '', 'select taskName, startTime, endTime, executeTime, detail
from (select regexp_replace(app_name, \'.*\\.impl\\.\', \'\')   taskName,
             date_format(start_time, \'%Y-%m-%d %H:%i:%s\') startTime,
             date_format(end_time, \'%Y-%m-%d %H:%i:%s\')   endTime,
             round(execute_time / 1000, 0)                executeTime,
             \'数仓任务\'                                   detail
      from compute.bz_compute_pool
      where end_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and end_time < CURRENT_DATE()
        and process_status in (\'FAIL\', \'SUCCESS\')
        and queue in (\'manual\')
      union
      select file_name                                       taskName,
             date_format(create_time, \'%Y-%m-%d %H:%i:%s\')   startTime,
             date_format(update_time, \'%Y-%m-%d %H:%i:%s\')   endTime,
             timestampdiff(second, create_time, update_time) executeTime,
             case dw_layer
                 when \'ODS\' then \'文件解析\'
                 when \'DWD\' then \'数仓任务\'
                 else \'\'
                 end
                                                             detail
      from dw_manual_calculate_task
      where (dw_layer = \'ODS\' or (dw_layer = \'DWD\' and run_mode = \'STANDALONE\'))
        and process_status in (\'FAIL\', \'SUCCESS\')
        and update_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and update_time < CURRENT_DATE()) x
order by executeTime desc
limit 10', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'manualTaskFail', 'FAIL_DETAIL', '', 'select taskName, startTime, endTime, executeTime, detail, exceptionMessage
from (select regexp_replace(app_name, \'.*\\.impl\\.\', \'\')   taskName,
             date_format(start_time, \'%Y-%m-%d %H:%i:%s\') startTime,
             date_format(end_time, \'%Y-%m-%d %H:%i:%s\')   endTime,
             round(execute_time / 1000, 0)                executeTime,
             \'数仓任务\'                                   detail,
             substr(error_message, 1, 1000)               exceptionMessage
      from compute.bz_compute_pool
      where end_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and end_time < CURRENT_DATE()
        and process_status = \'FAIL\'
        and queue in (\'manual\')
      union
      select a.file_name                                         taskName,
             date_format(a.create_time, \'%Y-%m-%d %H:%i:%s\')     startTime,
             date_format(a.update_time, \'%Y-%m-%d %H:%i:%s\')     endTime,
             timestampdiff(second, a.create_time, a.update_time) executeTime,
             case a.dw_layer
                 when \'ODS\' then \'文件解析\'
                 when \'DWD\' then \'数仓任务\'
                 else \'\'
                 end
                                                                 detail,
             substr(b.error_message, 1, 1000)                      exceptionMessage
      from dw_manual_calculate_task a
               join dw_manual_message b on a.message_id = b.id
      where (a.dw_layer = \'ODS\' or (a.dw_layer = \'DWD\' and a.run_mode = \'STANDALONE\'))
        and a.process_status = \'FAIL\'
        and a.update_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and a.update_time < CURRENT_DATE()) x
order by endTime desc
limit 100', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');



