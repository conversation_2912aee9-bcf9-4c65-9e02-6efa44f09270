USE `bpms`;

create table if not exists bz_modify_warehousing_rule_record
(
    id             bigint auto_increment
    primary key,
    customer       varchar(128)                  not null comment '客户名',
    rule_table     varchar(128)                  null comment '修改的表名：dw_sftp_file_rename_rule，dw_sftp_sub_customer_rule，dw_source_standard_field_rule，dw_source_standard_device_id_rule，dw_data_clear_rule，dw_sftp_file_convert_script',
    operation_type varchar(128)                  not null comment '操作类型：DELETE、UPDATE、INSERT、OVERWRITE',
    data_snapshot  longtext                      null comment '操作的数据，DELETE和UPDATE时为原来的数据，OVERWRITE和INSERT时为空',
    create_time    datetime(3)                   null comment '创建时间',
    update_time    datetime(3)                   null comment '更新时间',
    create_user    varchar(128) default 'System' null comment '创建用户',
    update_user    varchar(128) default 'System' null comment '更新用户'
    ) comment '入库规则表操作记录' ENGINE = InnoDB;

create table if not exists `bz_daily_summary`
(
    `id`          bigint       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `day`         varchar(12)  NOT NULL DEFAULT '' COMMENT '日期',
    `data`        longtext     NOT NULL COMMENT '具体数据，json形式',
    `create_time` datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `create_user` varchar(100) NOT NULL DEFAULT 'System' COMMENT '创建者',
    `update_time` datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `update_user` varchar(100) NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`),
    KEY `i_day` (`day`)
    ) ENGINE = InnoDB
    comment '平台每日运行情况概览';


create table if not exists `bz_daily_summary_query_config`
(
    `id`           bigint       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `type`         varchar(50)  NOT NULL DEFAULT '' COMMENT 'MYSQL、CLICKHOUSE等',
    `db`           varchar(50)  NOT NULL DEFAULT '' COMMENT '数据库名',
    `code`         varchar(100) NOT NULL DEFAULT '' COMMENT 'json的名称code，同一个code可以有多条配置，多个结果会合并到同一个json里面',
    `query_name`   varchar(100) NOT NULL DEFAULT '' COMMENT '查询语句代号',
    `query_desc`   varchar(500) NOT NULL DEFAULT '' COMMENT '查询语句描述',
    `fields_query` longtext     NOT NULL COMMENT '查询语句，查询出来的结果作为json的内容',
    `create_time`  datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `create_user`  varchar(100) NOT NULL DEFAULT 'System' COMMENT '创建者',
    `update_time`  datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `update_user`  varchar(100) NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`)
    ) ENGINE = InnoDB
    comment '平台每日运行情况概览查询内容配置';
