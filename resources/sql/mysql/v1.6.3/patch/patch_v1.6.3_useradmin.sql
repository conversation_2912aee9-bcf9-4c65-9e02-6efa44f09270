use `useradmin`;

-- 二级菜单：运维中心 三级菜单：日志管理
SELECT @parentId70700 := 70700;
delete from sys_menu where menu_id = 180900;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180900 , '运维中心' , NULL , @parentId70700 , 402470 , '${ACCESS_ADDR}/ops/#/' , 'ops' , 0 , 0 , 'M' , '0' , '0' , '' , '#' , 'root' );

delete from sys_menu where menu_id = 180910;
SELECT @parentId180900 := 180900;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180910 , '日志管理' , NULL , @parentId180900 , 402471 , '${ACCESS_ADDR}/bpms/#/log' , 'log' , 0 , 0 , 'C' , '0' , '0' , '' , '#' , 'root' );

-- 二级菜单：Bin定义
delete from sys_menu where menu_id = 181000;
SELECT @parentId70700 := 70700;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (181000 , 'Bin定义' , NULL , @parentId70700 , 402480 , '${ACCESS_ADDR}/bpms/#/bin-definition' , 'bin-definition' , 0 , 0 , 'C' , '0' , '0' , '' , '#' , 'root' );
