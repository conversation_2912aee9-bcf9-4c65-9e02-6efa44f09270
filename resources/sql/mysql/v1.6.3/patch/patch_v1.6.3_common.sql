-- 升级时清理mysql旧数据
use `bpms`;
DELETE FROM `bpms`.bz_kafka_fail_message_record WHERE update_time < DATE_SUB(NOW(), INTERVAL 2 MONTH) AND delete_flag = 1;
UPDATE `bpms`.bz_schedule_task_definition SET cron_expression = '0 0 1 * * ?' WHERE task_name = 'lot-history' and cron_expression = '0 0 * * * ?';

USE `compute`;
UPDATE `compute`.bz_moudule_info SET version = '1.6.3', update_time = now() where moudule = 'bpms';

use `bpms`;

delete from bpms.bz_daily_summary_query_config where query_name = 'TEST_ITEM_TASK_NOT_FINISH';
INSERT INTO bpms.bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'TEST_ITEM_TASK_NOT_FINISH', '', 'select ifnull(sum(if(test_item_status is null or test_item_status in (\'\', \'CREATE\'), 1, 0)), 0)     testItemTaskCntPending,
                ifnull(sum(if(test_item_status = \'PROCESSING\', 1, 0)), 0) testItemTaskCntProcessing
         from dw_lot_wafer_cal_status
         where die_status = \'SUCCESS\'
           and (test_item_status is null or test_item_status in (\'\', \'CREATE\', \'PROCESSING\'))
           and calculate_dw_test_item = 1', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');

INSERT INTO bz_schedule_task_definition (task_name, task_description, execute_engine, cron_expression, command, is_active, delete_flag, create_time, update_time, create_user, update_user)
VALUES  ('dim-optimize',       'dim表定时optimize任务', 'PYTHON', '0 30 0/6 * * ?',  '/home/<USER>/deploy/datahub/bpms/cron/dim_optimize.py|{ck_address}|{ck_user}|{ck_password}|{dim_database}', true, false, now(),  now(), 'SYSTEM', 'SYSTEM');