CREATE DATABASE IF NOT EXISTS `bpms` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;

USE `bpms`;

create table if not exists `bz_metadata_table_description`
(
    `id`            bigint       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `datasource`    varchar(20)  NOT NULL DEFAULT '' COMMENT '数据源，clickhouse/mysql',
    `database_name` varchar(20)  NOT NULL DEFAULT '' COMMENT '库名',
    `table_name`    varchar(100) NOT NULL DEFAULT '' COMMENT '表名',
    `description`   longtext              DEFAULT NULL COMMENT '表描述',
    `create_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `create_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '创建人',
    `update_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `update_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB;

create table if not exists `bz_metadata_table_link`
(
    `id`            bigint       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `datasource`    varchar(20)  NOT NULL DEFAULT '' COMMENT '数据源，clickhouse/mysql',
    `database_name` varchar(20)  NOT NULL DEFAULT '' COMMENT '库名',
    `table_name`    varchar(100) NOT NULL DEFAULT '' COMMENT '表名',
    `link_name`     varchar(100) NOT NULL DEFAULT '' COMMENT '链接名',
    `link`          varchar(200) NOT NULL DEFAULT '' COMMENT '链接',
    `create_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `create_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '创建人',
    `update_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `update_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB;

create table if not exists `bz_metadata_schema`
(
    `id`            bigint       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `datasource`    varchar(20)  NOT NULL DEFAULT '' COMMENT '数据源，clickhouse/mysql',
    `database_name` varchar(20)  NOT NULL DEFAULT '' COMMENT '库名',
    `table_name`    varchar(100) NOT NULL DEFAULT '' COMMENT '表名',
    `field_name`    varchar(100)  NOT NULL DEFAULT '' COMMENT '字段名',
    `field_type`    varchar(50)  NOT NULL DEFAULT '' COMMENT '字段类型',
    `default_value` varchar(100) NOT NULL DEFAULT '' COMMENT '缺省值',
    `position`      int          NOT NULL DEFAULT 0 COMMENT '字段位置',
    `field_comment` varchar(200) NOT NULL DEFAULT '' COMMENT '字段注释',
    `cp_logic`      longtext              DEFAULT NULL COMMENT 'CP计算逻辑',
    `ft_logic`      longtext              DEFAULT NULL COMMENT 'FT计算逻辑',
    `info`          longtext              DEFAULT NULL COMMENT '备注',
    `create_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `create_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '创建人',
    `update_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `update_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB;

create table if not exists `bz_metadata_lineage`
(
    `id`            bigint       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `from_source`   varchar(20)  NOT NULL DEFAULT '' COMMENT '起点数据源',
    `from_database` varchar(20)  NOT NULL DEFAULT '' COMMENT '起点库名',
    `from_table`    varchar(100) NOT NULL DEFAULT '' COMMENT '起点表名',
    `from_field`    varchar(100) NOT NULL DEFAULT '' COMMENT '起点字段名',
    `to_source`     varchar(20)  NOT NULL DEFAULT '' COMMENT '终点数据源',
    `to_database`   varchar(20)  NOT NULL DEFAULT '' COMMENT '终点库名',
    `to_table`      varchar(100) NOT NULL DEFAULT '' COMMENT '终点表名',
    `to_field`      varchar(100) NOT NULL DEFAULT '' COMMENT '终点字段名',
    `create_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `create_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '创建人',
    `update_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `update_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB;

create table if not exists bz_data_replay_record
(
    `id`               bigint      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `customer`         varchar(128) DEFAULT NULL COMMENT '客户名',
    `test_area`        varchar(64)  DEFAULT NULL COMMENT 'CP/FT/WAT等 表示测试阶段大类型，可能有多个',
    `factory`          varchar(256) DEFAULT NULL COMMENT '测试工厂，可能有多个',
    `device_id`        varchar(256) DEFAULT NULL COMMENT 'device id，可能有多个',
    `file_category`    varchar(64)  DEFAULT NULL COMMENT '文件类型，比如STDF、RAW_DATA等',
    `lot_id_list`      varchar(256) DEFAULT NULL COMMENT 'lot id，可能有多个',
    `wafer_no_list`    varchar(128) DEFAULT NULL COMMENT 'wafer no，可能有多个',
    `lot_type`         varchar(64)  DEFAULT NULL COMMENT 'Lot类型，Engineering/Production等，可能有多个',
    `test_stage_list`  varchar(256) DEFAULT NULL COMMENT 'test_stage值，可能有多个',
    `file_name`        varchar(256) DEFAULT NULL COMMENT '文件名',
    `replay_type`      varchar(64) NOT NULL COMMENT '删除/重跑,DELETE_FILE/DELETE_LOT/REPROCESS_FILE/REPROCESS_LOT',
    `step`             int         NOT NULL COMMENT '删除步骤(1000),重跑步骤(2200->需要转换的文件文件合并；3100->文件预解析；4100->文件解析到ods；5100->dwd层计算)',
    `replay_comments`  varchar(256) DEFAULT NULL COMMENT '重播备注',
    `replay_file_cnt`  int          DEFAULT NULL COMMENT '删除/重跑文件个数',
    `total_task_cnt`   int          DEFAULT NULL COMMENT '删除/重跑任务总数',
    `success_task_cnt` int          DEFAULT NULL COMMENT '删除/重跑任务执行成功数',
    `fail_task_cnt`    int          DEFAULT NULL COMMENT '删除/重跑任务执行失败数',
    `process_status`   varchar(128) DEFAULT NULL COMMENT '处理状态',
    `create_time`      datetime(3)  DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime(3)  DEFAULT NULL COMMENT '更新时间',
    `create_user`      varchar(128) DEFAULT 'System' COMMENT '创建用户',
    `update_user`      varchar(128) DEFAULT 'System' COMMENT '更新用户',
    PRIMARY KEY (`id`),
    KEY `i_file` (`file_name`),
    KEY `i_customer_area_factory_device_category` (`customer`, `test_area`, `factory`, `device_id`, `file_category`)
) ENGINE = InnoDB
    comment '数据重播记录';
