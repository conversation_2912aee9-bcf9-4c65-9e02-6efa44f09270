use `useradmin`;
INSERT IGNORE INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (70700, '入库管理', 'config', 0, 75, 'dbconfig_70700', NULL, 1, 0, 'M', '0', '0', '', '#', 'root');


-- 一级菜单: 70700
SELECT @parentId70700 := 70700;

-- 二级菜单: order_num[402100,402200)
update sys_menu set parent_id = @parentId70700, order_num = '402110', menu_name = 'Conf预览' where menu_id = '180500';

-- 二级菜单: order_num[402400,402500)
update sys_menu set parent_id = @parentId70700, order_num = '402400', menu_name = '数据重播' where menu_id = '180100';
update sys_menu set parent_id = @parentId70700, order_num = '402410', menu_name = '计算任务' where menu_id = '180200';
update sys_menu set parent_id = @parentId70700, order_num = '402420', menu_name = '数据质量' where menu_id = '180300';
update sys_menu set parent_id = @parentId70700, order_num = '402430', menu_name = '元数据管理' where menu_id = '180400';

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180600, '业务计算管理', NULL, @parentId70700, 402440, '${ACCESS_ADDR}/bpms/#/service-management', 'service-management', 0, 0, 'C', '0', '0', '', '#', 'root');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180700, '任务优先级管理', NULL, @parentId70700, 402450, '${ACCESS_ADDR}/bpms/#/priority', 'priority', 0, 0, 'C', '0', '0', '', '#', 'root');
