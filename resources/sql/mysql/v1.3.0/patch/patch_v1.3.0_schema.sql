USE `bpms`;

create table if not exists bpms.bz_kafka_fail_message_record
(
    `id`             bigint                                    NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `project`        varchar(64)  DEFAULT ''                   NOT NULL comment '发送该消息的项目',
    `module`         varchar(64)  DEFAULT ''                   NOT NULL comment '发送该消息的模块',
    `topic`          varchar(256) DEFAULT ''                   NOT NULL comment '该条消息的topic',
    `key`            longtext                                  NULL comment '该条消息的key',
    `value`          longtext                                  NOT NULL comment '消息体',
    `process_status` varchar(32)  DEFAULT 'FAIL'               NULL comment '消息处理状态',
    `fail_cnt`       int          DEFAULT 0                    NULL comment '发送失败次数',
    `delete_flag`    int          DEFAULT 0                    NULL comment '删除标记：0->有效,1->删除',
    `create_time`    datetime(3)  DEFAULT CURRENT_TIMESTAMP(3) NULL,
    `update_time`    datetime(3)  DEFAULT CURRENT_TIMESTAMP(3) NULL,
    `create_user`    varchar(64)  DEFAULT 'System'             NULL,
    `update_user`    varchar(64)  DEFAULT 'System'             NULL,
    PRIMARY KEY (`id`),
    KEY `process_status_delete_flag` (`process_status`, `delete_flag`)
) ENGINE = InnoDB
    comment '发送失败消息记录';