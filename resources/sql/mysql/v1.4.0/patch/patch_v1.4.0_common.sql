USE `compute`;
UPDATE compute.bz_moudule_info SET version = '1.4.0', update_time = now() where moudule = 'bpms';

-- TTL 清理两个月前的数据


use `bpms`;

INSERT INTO bz_business_fail_record_query_condition (db, `table`, query_condition, create_time, update_time,
                                                          create_user, update_user)
VALUES ('linkx', 'bz_app_instance', ' exception_message in (\'连接Mysql异常\',\'连接Clickhouse异常\',\'常驻进程异常\',\'HDFS文件损坏\',\'调用Dubbo接口失败\',\'Zookeeper异常\',\'Clickhouse异常\')',
        now(), now(), 'System', 'System');
INSERT INTO bz_business_fail_record_query_condition (db, `table`, query_condition, create_time, update_time,
                                                          create_user, update_user)
VALUES ('onedata', 'dw_layer_calculate_pool', ' dw_layer = \'DWD\' and exception_message in (\'连接Mysql异常\',\'连接Clickhouse异常\',\'常驻进程异常\',\'HDFS文件损坏\',\'调用Dubbo接口失败\',\'Zookeeper异常\',\'Clickhouse异常\')',
        now(), now(), 'System', 'System');

