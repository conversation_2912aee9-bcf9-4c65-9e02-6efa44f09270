USE `xxl_job`;

-- 注册执行器
SELECT address_list into @app01_address
    FROM xxl_job.xxl_job_group
WHERE app_name = "yms-app";

INSERT INTO xxl_job.xxl_job_group (app_name, title, address_type, address_list, update_time)
VALUES ('bigdata-bpms', 'bpms定时任务', 1, @app01_address, now());

SELECT  id into @bpms_id
    FROM xxl_job.xxl_job_group
WHERE app_name = "bigdata-bpms";

INSERT INTO xxl_job.xxl_job_info (job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time)
VALUES (@bpms_id, '定期清理bpms表的历史数据（bz_kafka_fail_message_record）', now(), now(), 'dataops', '', 'CRON', '0 0 1 ? * 7', 'DO_NOTHING', 'FIRST', '', '', 'SERIAL_EXECUTION', 0, 0, 'GLUE_SHELL', '#!/bin/bash
sshpass -p "devops@guwave" ssh devops@mpp01 "bash /home/<USER>/deploy/onedata/dataware/cron/bpms_delete_commit.sh &> /home/<USER>/deploy/onedata/dataware/cron/bpms_delete_commit.log"
', 'job-init', now(), '', 1, 0, 0);

INSERT INTO xxl_job.xxl_job_info (job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time)
VALUES (@bpms_id, '定期清理compute表的历史数据（bz_compute_fail_message_record）', now(), now(), 'dataops', '', 'CRON', '0 20 1 ? * 7', 'DO_NOTHING', 'FIRST', '', '', 'SERIAL_EXECUTION', 0, 0, 'GLUE_SHELL', '#!/bin/bash
sshpass -p "devops@guwave" ssh devops@mpp01 "bash /home/<USER>/deploy/onedata/dataware/cron/compute_delete_commit.sh &> /home/<USER>/deploy/onedata/dataware/cron/compute_delete_commit.log"
', 'job-init', now(), '', 1, 0, 0);

INSERT INTO xxl_job.xxl_job_info (job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time)
VALUES (@bpms_id, '定期清理linkx表的历史数据（bz_linkx_fail_message_record、bz_task_instance）', now(), now(), 'dataops', '', 'CRON', '0 30 1 ? * 7', 'DO_NOTHING', 'FIRST', '', '', 'SERIAL_EXECUTION', 0, 0, 'GLUE_SHELL', '#!/bin/bash
sshpass -p "devops@guwave" ssh devops@mpp01 "bash /home/<USER>/deploy/onedata/dataware/cron/linkx_delete_commit.sh &> /home/<USER>/deploy/onedata/dataware/cron/linkx_delete_commit.log"
', 'job-init', now(), '', 1, 0, 0);

INSERT INTO xxl_job.xxl_job_info (job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time)
VALUES (@bpms_id, '定期清理onedata表的历史数据（dw_dataware_fail_message_record）', now(), now(), 'dataops', '', 'CRON', '0 40 1 ? * 7', 'DO_NOTHING', 'FIRST', '', '', 'SERIAL_EXECUTION', 0, 0, 'GLUE_SHELL', '#!/bin/bash
sshpass -p "devops@guwave" ssh devops@mpp01 "bash /home/<USER>/deploy/onedata/dataware/cron/onedata_delete_commit.sh &> /home/<USER>/deploy/onedata/dataware/cron/onedata_delete_commit.log"
', 'job-init', now(), '', 1, 0, 0);
