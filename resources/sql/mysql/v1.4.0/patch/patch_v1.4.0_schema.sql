USE `bpms`;


create table if not exists `bz_business_fail_record`
(
    `id`                    bigint                      NOT NULL AUTO_INCREMENT,
    `db`                    varchar(32)  DEFAULT ''     not NULL COMMENT '数据库名',
    `table`                 varchar(64)  DEFAULT ''     not NULL COMMENT '表名',
    `record_id`             bigint                      not NULL COMMENT '表里面的那条记录的id',
    `record_retry_cnt`      int          DEFAULT 0      not null COMMENT '记录的重试次数',
    `record_process_status` varchar(128) default 'FAIL' not null comment '记录的计算状态',
    `create_time`           datetime(3)  DEFAULT NULL COMMENT '创建时间',
    `update_time`           datetime(3)  DEFAULT NULL COMMENT '更新时间',
    `create_user`           varchar(128) DEFAULT 'System' COMMENT '创建用户',
    `update_user`           varchar(128) DEFAULT 'System' COMMENT '更新用户',
    PRIMARY KEY (`id`),
    KEY `i_status_cnt` (record_process_status, record_retry_cnt),
    KEY `i_record_id_table_db` (record_id, `table`, db)
    ) ENGINE = InnoDB
    COMMENT = '业务失败的记录';

create table if not exists `bz_business_fail_record_query_condition`
(
    `id`              bigint                  NOT NULL AUTO_INCREMENT,
    `db`              varchar(32)  DEFAULT '' not NULL COMMENT '数据库名',
    `table`           varchar(64)  DEFAULT '' not NULL COMMENT '表名',
    `query_condition` longtext                NULL COMMENT '查询需要重试的失败记录的查询条件',
    `create_time`     datetime(3)  DEFAULT NULL COMMENT '创建时间',
    `update_time`     datetime(3)  DEFAULT NULL COMMENT '更新时间',
    `create_user`     varchar(128) DEFAULT 'System' COMMENT '创建用户',
    `update_user`     varchar(128) DEFAULT 'System' COMMENT '更新用户',
    PRIMARY KEY (`id`)
    ) ENGINE = InnoDB
    COMMENT = '业务失败记录的查询条件';

create table if not exists `bpms`.`bz_daily_report_record`
(
    `id`                bigint       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `day`               varchar(12)  NOT NULL DEFAULT '' COMMENT '日期',
    `process_status`    varchar(20)  NOT NULL DEFAULT '' COMMENT '运行状态',
    `start_time`        datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '开始时间',
    `end_time`          datetime(3)  NULL default CURRENT_TIMESTAMP(3) COMMENT '结束时间',
    `error_message`     longtext     NULL COMMENT '错误信息',
    `create_time`       datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `create_user`       varchar(100) NOT NULL DEFAULT 'System' COMMENT '创建者',
    `update_time`       datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `update_user`       varchar(100) NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`),
    KEY `i_date` (`day`)
    ) ENGINE = InnoDB;

create table if not exists `bpms`.`bz_daily_report_result`
(
    `id`            bigint       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `report_module` varchar(64)  NOT NULL DEFAULT '' COMMENT '所属模块，如TIMELINESS',
    `data_code`     varchar(64)  NOT NULL DEFAULT '' COMMENT '数据标志码，用于标识该行数据的类型，如FAIL_WAREHOUSING_FILE',
    `data`          longtext     NOT NULL  COMMENT '具体数据，json形式',
    `day`           varchar(12)  NOT NULL DEFAULT '' COMMENT '该条数据的所属日期',
    `latest_flag`   int NOT NULL  default '1' COMMENT '是否是此维度最新一条记录：0->否，1->是',
    `create_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `create_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '创建者',
    `update_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `update_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`),
    KEY `i_rule_id` (`data_code`),
    KEY `i_date` (`day`),
    KEY `i_module` (`report_module`)
    ) ENGINE = InnoDB;

