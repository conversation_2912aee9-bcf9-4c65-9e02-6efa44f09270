use `useradmin`;

-- menu_type: 菜单类型（M目录 C菜单 F按钮）
-- is_frame: 是否为外链（0是 1否）
-- status: 菜单状态（0正常 1停用）
INSERT IGNORE INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (70700, '入库管理', 'config', 0, 75, 'dbconfig_70700', NULL, 1, 0, 'M', '0', '0', '', '#', 'root');

-- 一级菜单: 70700
SELECT @parentId70700 := 70700;
-- 二级菜单: order_num[402100,402200)
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180500, 'Conf预览', NULL, @parentId70700, 402110, '${ACCESS_ADDR}/bpms/#/clear-rule-preview', 'clear-rule-preview', 0, 0, 'C', '0', '0', '', '#', 'root');

-- 二级菜单: order_num[402400,402500)
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
(180100, '数据重播', NULL, @parentId70700, 402400, '${ACCESS_ADDR}/bpms/#/data-replay', 'data-replay', 0, 0, 'C', '0', '0', '', '#', 'root');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180200, '计算任务', NULL, @parentId70700, 402410, '${ACCESS_ADDR}/bpms/#/task', 'task', 0, 0, 'C', '0', '0', '', '#', 'root');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180300, '数据质量', NULL, @parentId70700, 402420, '${ACCESS_ADDR}/bpms/#/data-quality', 'data-quality', 0, 0, 'C', '0', '0', '', '#', 'root');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180400, '元数据管理', NULL, @parentId70700, 402430, '${ACCESS_ADDR}/bpms/#/metadata', 'metadata', 0, 0, 'C', '0', '0', '', '#', 'root');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180600, '业务计算管理', NULL, @parentId70700, 402440, '${ACCESS_ADDR}/bpms/#/service-management', 'service-management', 0, 0, 'C', '0', '0', '', '#', 'root');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180700, '任务优先级管理', NULL, @parentId70700, 402450, '${ACCESS_ADDR}/bpms/#/priority', 'priority', 0, 0, 'C', '0', '0', '', '#', 'root');
