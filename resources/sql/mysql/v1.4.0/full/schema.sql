USE `bpms`;

create table if not exists `bz_metadata_table_description`
(
    `id`            bigint       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `datasource`    varchar(20)  NOT NULL DEFAULT '' COMMENT '数据源，clickhouse/mysql',
    `database_name` varchar(20)  NOT NULL DEFAULT '' COMMENT '库名',
    `table_name`    varchar(100) NOT NULL DEFAULT '' COMMENT '表名',
    `description`   longtext              DEFAULT NULL COMMENT '表描述',
    `create_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `create_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '创建人',
    `update_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `update_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB;

create table if not exists `bz_metadata_table_link`
(
    `id`            bigint       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `datasource`    varchar(20)  NOT NULL DEFAULT '' COMMENT '数据源，clickhouse/mysql',
    `database_name` varchar(20)  NOT NULL DEFAULT '' COMMENT '库名',
    `table_name`    varchar(100) NOT NULL DEFAULT '' COMMENT '表名',
    `link_name`     varchar(100) NOT NULL DEFAULT '' COMMENT '链接名',
    `link`          varchar(200) NOT NULL DEFAULT '' COMMENT '链接',
    `create_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `create_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '创建人',
    `update_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `update_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB;

create table if not exists `bz_metadata_schema`
(
    `id`            bigint       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `datasource`    varchar(20)  NOT NULL DEFAULT '' COMMENT '数据源，clickhouse/mysql',
    `database_name` varchar(20)  NOT NULL DEFAULT '' COMMENT '库名',
    `table_name`    varchar(100) NOT NULL DEFAULT '' COMMENT '表名',
    `field_name`    varchar(100) NOT NULL DEFAULT '' COMMENT '字段名',
    `field_type`    varchar(50)  NOT NULL DEFAULT '' COMMENT '字段类型',
    `default_value` varchar(100) NOT NULL DEFAULT '' COMMENT '缺省值',
    `position`      int          NOT NULL DEFAULT 0 COMMENT '字段位置',
    `field_comment` varchar(200) NOT NULL DEFAULT '' COMMENT '字段注释',
    `cp_logic`      longtext              DEFAULT NULL COMMENT 'CP计算逻辑',
    `ft_logic`      longtext              DEFAULT NULL COMMENT 'FT计算逻辑',
    `info`          longtext              DEFAULT NULL COMMENT '备注',
    `create_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `create_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '创建人',
    `update_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `update_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB;

create table if not exists `bz_metadata_lineage`
(
    `id`            bigint       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `from_source`   varchar(20)  NOT NULL DEFAULT '' COMMENT '起点数据源',
    `from_database` varchar(20)  NOT NULL DEFAULT '' COMMENT '起点库名',
    `from_table`    varchar(100) NOT NULL DEFAULT '' COMMENT '起点表名',
    `from_field`    varchar(100) NOT NULL DEFAULT '' COMMENT '起点字段名',
    `to_source`     varchar(20)  NOT NULL DEFAULT '' COMMENT '终点数据源',
    `to_database`   varchar(20)  NOT NULL DEFAULT '' COMMENT '终点库名',
    `to_table`      varchar(100) NOT NULL DEFAULT '' COMMENT '终点表名',
    `to_field`      varchar(100) NOT NULL DEFAULT '' COMMENT '终点字段名',
    `create_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `create_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '创建人',
    `update_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `update_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB;

create table if not exists bz_data_replay_record
(
    `id`                                bigint       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `customer`                          varchar(128)  DEFAULT NULL COMMENT '客户名',
    `test_area`                         varchar(256)   DEFAULT NULL COMMENT 'CP/FT/WAT等 表示测试阶段大类型，可能有多个',
    `factory`                           varchar(256)  DEFAULT NULL COMMENT '测试工厂，可能有多个',
    `device_id`                         varchar(256)  DEFAULT NULL COMMENT 'device id，可能有多个',
    `file_category`                     varchar(64)   DEFAULT NULL COMMENT '文件类型，比如STDF、RAW_DATA等',
    `lot_id_list`                       varchar(2048)  DEFAULT NULL COMMENT 'lot id，可能有多个',
    `wafer_no_list`                     varchar(1024)  DEFAULT NULL COMMENT 'wafer no，可能有多个',
    `sblot_id_list`                     varchar(2048) null comment 'sblot id，可能有多个',
    `lot_type`                          varchar(64)   DEFAULT NULL COMMENT 'Lot类型，Engineering/Production等',
    `test_stage_list`                   varchar(256)  DEFAULT NULL COMMENT 'test_stage值，可能有多个',
    `file_name`                         varchar(256)  DEFAULT NULL COMMENT '文件名',
    `remote_file_path`                  varchar(1024) DEFAULT NULL COMMENT 'file远程路径',
    `replay_type`                       varchar(64)  NOT NULL COMMENT '删除/重跑,DELETE_FILE/DELETE_LOT/REPROCESS_FILE/REPROCESS_LOT',
    `step`                              int          NOT NULL COMMENT '删除步骤(1000),重跑步骤(2200->需要转换的文件文件合并；3100->文件预解析；4100->文件解析到ods；5100->dwd层计算)',
    `filter_process_status_list`        varchar(256) null comment 'process_status，可能有多个',
    `filter_exception_message`          longtext     null comment 'exception_message',
    `filter_file_warehousing_record_id` longtext     null comment '提交重播任务勾选的dw_file_warehousing_record id',
    `filter_step_list`                  varchar(256)  DEFAULT NULL COMMENT '重播的文件的处理步骤，可能有多个',
    `start_create_time`                 varchar(256) null comment '重播的文件create_time开始时间',
    `end_create_time`                   varchar(256) null comment '重播的文件create_time结束时间',
    `start_update_time`                 varchar(256) null comment '重播的文件update_time开始时间',
    `end_update_time`                   varchar(256) null comment '重播的文件update_time结束时间',
    `file_warehousing_record_id`        longtext     null comment '重播任务实际处理的dw_file_warehousing_record id',
    `replay_comments`                   varchar(256)  DEFAULT NULL COMMENT '重播备注',
    `replay_file_cnt`                   int           DEFAULT NULL COMMENT '删除/重跑文件个数',
    `total_task_cnt`                    int           DEFAULT NULL COMMENT '删除/重跑任务总数',
    `success_task_cnt`                  int           DEFAULT NULL COMMENT '删除/重跑任务执行成功数',
    `fail_task_cnt`                     int           DEFAULT NULL COMMENT '删除/重跑任务执行失败数',
    `process_status`                    varchar(128)  DEFAULT NULL COMMENT '处理状态',
    `create_time`                       datetime(3)   DEFAULT NULL COMMENT '创建时间',
    `update_time`                       datetime(3)   DEFAULT NULL COMMENT '更新时间',
    `create_user`                       varchar(128)  DEFAULT 'System' COMMENT '创建用户',
    `update_user`                       varchar(128)  DEFAULT 'System' COMMENT '更新用户',
    PRIMARY KEY (`id`),
    KEY `i_file` (`file_name`),
    KEY `i_customer_area_factory_device_category` (`customer`, `test_area`, `factory`, `device_id`, `file_category`)
) ENGINE = InnoDB
    comment '数据重播记录';


create table if not exists bz_clear_rule_preview_record
(
    id                            bigint auto_increment
        primary key,
    customer                      varchar(128)  null comment '客户名',
    sub_customer                  varchar(256)  null comment '子客户',
    given_test_area               varchar(64)   null comment '给定的testArea，CP/FT',
    factory                       varchar(128)  null comment '测试工厂',
    factory_site                  varchar(128)  null comment '测试工厂子厂',
    fab                           varchar(128)  null comment '晶圆的生产厂',
    fab_site                      varchar(128)  null comment '晶圆的生产厂子厂',
    file_category                 varchar(64)   null comment '文件类型，STDF、RAW_DATA',
    file_name                     varchar(512)  null comment 'hdfs上zip压缩后的文件名',
    file_size                     bigint        null comment 'hdfs上zip压缩后的文件大小(byte)',
    origin_file_name              varchar(512)  null comment '原始文件解压出来的文件名',
    origin_file_size              bigint        null comment '原始文件解压出来的文件大小(byte)',
    remote_origin_file_name       varchar(512)  null comment '文件在ftp上的原始文件名',
    remote_origin_file_path       varchar(1024) null comment '文件在ftp上的原始目录路径',
    hdfs_path                     varchar(1024) null comment 'hdfs全路径',
    ftp_path                      varchar(1024) null comment '文件在ftp上的原始全路径',
    test_area                     varchar(64)   null comment '实际的testArea，CP/FT/SLT等',
    device_id                     varchar(128)  null comment 'device id',
    lot_id                        varchar(256)  null comment 'lot id',
    wafer_id                      varchar(128)  null comment '标准化后的wafer_id',
    origin_wafer_id               varchar(128)  null comment '原始的wafer_id',
    wafer_no                      varchar(128)  null comment 'wafer no',
    lot_type                      varchar(64)   null comment 'Lot类型，Engineering/Production等',
    offline_retest                int           null comment 'offline_retest值',
    interrupt                     int           null comment 'interrupt值',
    dup_retest                    int           null comment 'dup_retest值',
    batch_num                     int           null comment 'batch_num值',
    sblot_id                      varchar(256)  null comment 'sblot_id值',
    test_cod                      varchar(128)  null comment 'test_cod值',
    test_stage                    varchar(128)  null comment 'test_stage值',
    start_t                       varchar(128)  null comment 'start_t值',
    origin_start_t                varchar(128)  null comment 'start_t值',
    finish_t                      varchar(128)  null comment 'finish_t值',
    origin_finish_t               varchar(128)  null comment 'finish_t值',
    pos_x                         varchar(128)  null comment 'pos_x值',
    pos_y                         varchar(128)  null comment 'pos_y值',
    notch                         varchar(128)  null comment 'notch值',
    floor_id                      varchar(255)  null comment 'floor_id值',
    test_temperature              varchar(255)  null comment 'test_temperature值',
    tester_name                   varchar(128)  null comment 'TESTER名称',
    tester_type                   varchar(128)  null comment 'TESTER类型',
    probecard_loadboard_id        varchar(128)  null comment 'CP: CARD_ID, FT: LOAD_ID',
    test_program                  varchar(256)  null comment '测试程序名称',
    test_program_version          varchar(256)  null comment '测试程序版本',
    pkg_typ                       varchar(128)  null comment 'pkg_typ',
    retest_bin_num                varchar(256)  null comment 'retest hbin, 默认取值MIR的USER_TXT',
    `process`                     varchar(256)  null comment 'process值',
    condition_set                 longtext      null comment '测试条件集合，用于测试条件动态扩展，Map动态类型{Key: Value}',
    real_wafer_id                 varchar(128)  null comment 'real_wafer_id',
    process_status                varchar(128)  null comment '处理状态（PROCESSING->处理中；SUCCESS->处理成功；FAIL->处理失败；）',
    preview_result                longtext      null comment '预览结果json',
    preview_detail_file_hdfs_path varchar(1024) null comment '预览详细信息文件hdfs全路径',
    exception_type                varchar(128)  null comment '异常类型（KEY_FIELD_NULL->关键字段为空；OTHER_EXCEPTION->处理失败；）',
    exception_message             varchar(256)  null comment '异常信息',
    error_message                 longtext      null comment '错误信息',
    failed_fields                 varchar(256)  null comment '导致失败的异常字段,多个用英文逗号分隔',
    delete_flag                   int default 0 not null comment '删除标记：0->有效,1->删除',
    create_time                   datetime(3)   null comment '创建时间',
    update_time                   datetime(3)   null comment '更新时间',
    create_user                   varchar(128)  null comment '创建用户',
    update_user                   varchar(128)  null comment '更新用户'
) ENGINE = InnoDB comment '清洗规则预览记录';

create table if not exists `bz_module_scheduled_control_switch`
(
    `id`          bigint NOT NULL AUTO_INCREMENT,
    `module`      varchar(255) DEFAULT NULL,
    `stop_flag`   int          DEFAULT NULL,
    `create_time` datetime(3)  DEFAULT CURRENT_TIMESTAMP(3),
    `create_user` varchar(255) DEFAULT NULL,
    `update_time` datetime(3)  DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    `update_user` varchar(255) DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
    COMMENT ='定时任务开关表';

create table if not exists bpms.bz_kafka_fail_message_record
(
    `id`             bigint                                    NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `project`        varchar(64)  DEFAULT ''                   NOT NULL comment '发送该消息的项目',
    `module`         varchar(64)  DEFAULT ''                   NOT NULL comment '发送该消息的模块',
    `topic`          varchar(256) DEFAULT ''                   NOT NULL comment '该条消息的topic',
    `key`            longtext                                  NULL comment '该条消息的key',
    `value`          longtext                                  NOT NULL comment '消息体',
    `process_status` varchar(32)  DEFAULT 'FAIL'               NULL comment '消息处理状态',
    `fail_cnt`       int          DEFAULT 0                    NULL comment '发送失败次数',
    `delete_flag`    int          DEFAULT 0                    NULL comment '删除标记：0->有效,1->删除',
    `create_time`    datetime(3)  DEFAULT CURRENT_TIMESTAMP(3) NULL,
    `update_time`    datetime(3)  DEFAULT CURRENT_TIMESTAMP(3) NULL,
    `create_user`    varchar(64)  DEFAULT 'System'             NULL,
    `update_user`    varchar(64)  DEFAULT 'System'             NULL,
    PRIMARY KEY (`id`),
    KEY `process_status_delete_flag` (`process_status`, `delete_flag`)
) ENGINE = InnoDB
    comment '发送失败消息记录';




create table if not exists `bz_business_fail_record`
(
    `id`                    bigint                      NOT NULL AUTO_INCREMENT,
    `db`                    varchar(32)  DEFAULT ''     not NULL COMMENT '数据库名',
    `table`                 varchar(64)  DEFAULT ''     not NULL COMMENT '表名',
    `record_id`             bigint                      not NULL COMMENT '表里面的那条记录的id',
    `record_retry_cnt`      int          DEFAULT 0      not null COMMENT '记录的重试次数',
    `record_process_status` varchar(128) default 'FAIL' not null comment '记录的计算状态',
    `create_time`           datetime(3)  DEFAULT NULL COMMENT '创建时间',
    `update_time`           datetime(3)  DEFAULT NULL COMMENT '更新时间',
    `create_user`           varchar(128) DEFAULT 'System' COMMENT '创建用户',
    `update_user`           varchar(128) DEFAULT 'System' COMMENT '更新用户',
    PRIMARY KEY (`id`),
    KEY `i_status_cnt` (record_process_status, record_retry_cnt),
    KEY `i_record_id_table_db` (record_id, `table`, db)
    ) ENGINE = InnoDB
    COMMENT = '业务失败的记录';

create table if not exists `bz_business_fail_record_query_condition`
(
    `id`              bigint                  NOT NULL AUTO_INCREMENT,
    `db`              varchar(32)  DEFAULT '' not NULL COMMENT '数据库名',
    `table`           varchar(64)  DEFAULT '' not NULL COMMENT '表名',
    `query_condition` longtext                NULL COMMENT '查询需要重试的失败记录的查询条件',
    `create_time`     datetime(3)  DEFAULT NULL COMMENT '创建时间',
    `update_time`     datetime(3)  DEFAULT NULL COMMENT '更新时间',
    `create_user`     varchar(128) DEFAULT 'System' COMMENT '创建用户',
    `update_user`     varchar(128) DEFAULT 'System' COMMENT '更新用户',
    PRIMARY KEY (`id`)
    ) ENGINE = InnoDB
    COMMENT = '业务失败记录的查询条件';

create table if not exists `bpms`.`bz_daily_report_record`
(
    `id`                bigint       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `day`               varchar(12)  NOT NULL DEFAULT '' COMMENT '日期',
    `process_status`    varchar(20)  NOT NULL DEFAULT '' COMMENT '运行状态',
    `start_time`        datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '开始时间',
    `end_time`          datetime(3)  NULL default CURRENT_TIMESTAMP(3) COMMENT '结束时间',
    `error_message`     longtext     NULL COMMENT '错误信息',
    `create_time`       datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `create_user`       varchar(100) NOT NULL DEFAULT 'System' COMMENT '创建者',
    `update_time`       datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `update_user`       varchar(100) NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`),
    KEY `i_date` (`day`)
    ) ENGINE = InnoDB;

create table if not exists `bpms`.`bz_daily_report_result`
(
    `id`            bigint       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `report_module` varchar(64)  NOT NULL DEFAULT '' COMMENT '所属模块，如TIMELINESS',
    `data_code`     varchar(64)  NOT NULL DEFAULT '' COMMENT '数据标志码，用于标识该行数据的类型，如FAIL_WAREHOUSING_FILE',
    `data`          longtext     NOT NULL  COMMENT '具体数据，json形式',
    `day`           varchar(12)  NOT NULL DEFAULT '' COMMENT '该条数据的所属日期',
    `latest_flag`   int NOT NULL  default '1' COMMENT '是否是此维度最新一条记录：0->否，1->是',
    `create_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `create_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '创建者',
    `update_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `update_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`),
    KEY `i_rule_id` (`data_code`),
    KEY `i_date` (`day`),
    KEY `i_module` (`report_module`)
    ) ENGINE = InnoDB;
