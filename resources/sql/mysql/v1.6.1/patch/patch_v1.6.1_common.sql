-- 升级时清理mysql旧数据
use `bpms`;
DELETE FROM `bpms`.bz_kafka_fail_message_record WHERE update_time < DATE_SUB(NOW(), INTERVAL 2 MONTH) AND delete_flag = 1;

USE `compute`;
UPDATE `compute`.bz_moudule_info SET version = '1.6.1', update_time = now() where moudule = 'bpms';

use `bpms`;


delete from bpms.bz_warehousing_rule_flow where update_user = 'SYSTEM';
insert bpms.bz_warehousing_rule_flow(name,customer,sub_customer,factory,test_area,file_category,remarks,delete_flag,create_time,update_time,create_user,update_user)
select concat(customer, '-', sub_customer, '-', factory, '-', test_area, '-', file_category),
       customer,
       sub_customer,
       factory,
       test_area,
       file_category,
       '' as remarks,
       0 as delete_flag,
       now() as create_time,
       now() as update_time,
       'SY<PERSON><PERSON>' as create_user,
       'SYSTEM' as update_user
from(
-- dw_sftp_sub_customer_rule
        select distinct t1.customer as customer,
                        t1.sub_customer as sub_customer,
                        if(t1.factory is not null and t1.factory != '', t1.factory, t2.factory) as factory,
                        if(t1.test_area is not null and t1.test_area != '', t1.test_area, t2.test_area) as test_area,
                        t2.file_category as file_category
        from onedata.dw_sftp_sub_customer_rule t1
                 join onedata.dw_sftp_file_convert_script t2
                      on t1.customer = t2.customer
                          and t1.sub_customer = t2.sub_customer
        union
-- dw_sftp_file_rename_rule
        select t1.customer as customer,
               t1.sub_customer as sub_customer,
               t2.factory as factory ,
               t2.test_area as test_area,
               t2.file_category as file_category
        from onedata.dw_sftp_file_rename_rule t1
                 join onedata.dw_sftp_file_convert_script t2
                      on t1.customer = t2.customer
                          and t1.sub_customer = t2.sub_customer
        union
-- dw_data_clear_rule
        select distinct customer,
                        sub_customer,
                        factory,
                        test_area,
                        'STDF' file_category
        from onedata.dw_data_clear_rule t1
        union
-- dw_source_standard_device_id_rule
        select distinct t1.customer as customer,
                        t1.sub_customer as sub_customer,
                        if(t1.factory is not null and t1.factory != '', t1.factory, t2.factory) as factory,
                        if(t1.test_area is not null and t1.test_area != '', t1.test_area, t2.test_area) as test_area,
                        t2.file_category as file_category
        from onedata.dw_source_standard_device_id_rule t1
                 join onedata.dw_sftp_file_convert_script t2
                      on t1.customer = t2.customer
                          and t1.sub_customer = t2.sub_customer
                          and t1.factory = t2.factory
                          and t1.test_area = t2.test_area
        union
-- dw_source_standard_field_rule
        select distinct t1.customer as customer,
                        t1.sub_customer as sub_customer,
                        if(t1.factory is not null and t1.factory != '', t1.factory, t2.factory) as factory,
                        if(t1.test_area is not null and t1.test_area != '', t1.test_area, t2.test_area) as test_area,
                        t2.file_category as file_category
        from onedata.dw_source_standard_field_rule t1
                 join onedata.dw_sftp_file_convert_script t2
                      on t1.customer = t2.customer
                          and t1.sub_customer = t2.sub_customer
                          and t2.factory = t2.factory
                          and t1.test_area = t2.test_area
    )t
where customer is not null
  and sub_customer is not null
  and factory is not null
  and test_area is not null
  and file_category is not null;

truncate table bpms.bz_daily_summary;
