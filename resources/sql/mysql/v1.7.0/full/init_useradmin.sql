use `useradmin`;

-- menu_type: 菜单类型（M目录 C菜单 F按钮）
-- is_frame: 是否为外链（0是 1否）
-- status: 菜单状态（0正常 1停用）
INSERT IGNORE INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (70700, '入库管理', 'config', 0, 75, 'dbconfig_70700', NULL, 1, 0, 'M', '0', '0', '', '#', 'root');

-- 一级菜单: 70700
SELECT @parentId70700 := 70700;
-- 二级菜单: order_num[402100,402200)
delete from sys_menu where menu_id = 180500;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180500, 'Conf预览', NULL, @parentId70700, 402110, '${ACCESS_ADDR}/bpms/#/clear-rule-preview', 'clear-rule-preview', 0, 0, 'C', '0', '0', '', '#', 'root');

-- 二级菜单: order_num[402400,402500)
delete from sys_menu where menu_id = 180100;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
(180100, '数据重播', NULL, @parentId70700, 402400, '${ACCESS_ADDR}/bpms/#/data-replay', 'data-replay', 0, 0, 'C', '0', '0', '', '#', 'root');
delete from sys_menu where menu_id = 180200;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180200, '计算任务', NULL, @parentId70700, 402410, '${ACCESS_ADDR}/bpms/#/task', 'task', 0, 0, 'C', '0', '0', '', '#', 'root');
delete from sys_menu where menu_id = 180300;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180300, '数据质量', NULL, @parentId70700, 402420, '${ACCESS_ADDR}/bpms/#/data-quality', 'data-quality', 0, 0, 'C', '0', '0', '', '#', 'root');
delete from sys_menu where menu_id = 180400;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180400, '元数据管理', NULL, @parentId70700, 402430, '${ACCESS_ADDR}/bpms/#/metadata', 'metadata', 0, 0, 'C', '0', '0', '', '#', 'root');
delete from sys_menu where menu_id = 180600;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180600, '业务计算管理', NULL, @parentId70700, 402440, '${ACCESS_ADDR}/bpms/#/service-management', 'service-management', 0, 0, 'C', '0', '0', '', '#', 'root');
delete from sys_menu where menu_id = 180700;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180700, '任务优先级管理', NULL, @parentId70700, 402450, '${ACCESS_ADDR}/bpms/#/priority', 'priority', 0, 0, 'C', '0', '0', '', '#', 'root');
delete from sys_menu where menu_id = 180800;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180800, '自动入库规则管理', NULL, @parentId70700, 402460, '${ACCESS_ADDR}/bpms/#/conf-mgt', 'conf-mgt', 0, 0, 'C', '0', '0', '', '#', 'root');

-- 二级菜单：运维中心 三级菜单：日志管理
SELECT @parentId70700 := 70700;
delete from sys_menu where menu_id = 180900;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180900 , '运维中心' , NULL , @parentId70700 , 402470 , '${ACCESS_ADDR}/ops/#/' , 'ops' , 0 , 0 , 'M' , '0' , '0' , '' , '#' , 'root' );

delete from sys_menu where menu_id = 180910;
SELECT @parentId180900 := 180900;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180910 , '日志管理' , NULL , @parentId180900 , 402471 , '${ACCESS_ADDR}/bpms/#/log' , 'log' , 0 , 0 , 'C' , '0' , '0' , '' , '#' , 'root' );

-- 二级菜单：Bin定义
delete from sys_menu where menu_id = 181000;
SELECT @parentId70700 := 70700;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (181000 , 'Bin定义' , NULL , @parentId70700 , 402480 , '${ACCESS_ADDR}/bpms/#/bin-definition' , 'bin-definition' , 0 , 0 , 'C' , '0' , '0' , '' , '#' , 'root' );

SELECT id
into @delete_notification_id
FROM notification_config
WHERE code = 'DATAWARE_DAILY_SUMMARY';
delete from notification_template where config_id = @delete_notification_id;
delete from notification_config where code = 'DATAWARE_DAILY_SUMMARY';

INSERT INTO notification_config (code, name, create_by, create_time, update_by, update_time, delete_flag,
                                 company_code, test_data, remark)
VALUES ('DATAWARE_DAILY_SUMMARY', 'OneData数据平台日报', 'root', '2025-03-25 11:14:23', null, null, 0, null, '', '');

SELECT id
into @notification_id
FROM notification_config
WHERE code = 'DATAWARE_DAILY_SUMMARY';

INSERT INTO notification_template (config_id, title, body, enabled, create_by, create_time, update_by,
                                   update_time, delete_flag, type, company_code, order_num, is_notify)
VALUES (@notification_id, 'OneData数据平台日报_${date}', '[(${content})]', 1, 'root', '2025-03-25 11:14:23', null, null, 0, 'email', null, null, 0);

INSERT INTO notification_template (config_id, title, body, enabled, create_by, create_time, update_by,
                                   update_time, delete_flag, type, company_code, order_num, is_notify)
VALUES (@notification_id, '', '', 0, 'root', '2025-03-25 11:14:23', null, null, 0, 'insite', null, null, 0);
