CREATE DATABASE IF NOT EXISTS `bpms` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;

USE `bpms`;

create table if not exists `bz_metadata_table_description`
(
    `id`            bigint       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `datasource`    varchar(20)  NOT NULL DEFAULT '' COMMENT '数据源，clickhouse/mysql',
    `database_name` varchar(20)  NOT NULL DEFAULT '' COMMENT '库名',
    `table_name`    varchar(100) NOT NULL DEFAULT '' COMMENT '表名',
    `description`   longtext              DEFAULT NULL COMMENT '表描述',
    `create_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `create_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '创建人',
    `update_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `update_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB;

create table if not exists `bz_metadata_table_link`
(
    `id`            bigint       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `datasource`    varchar(20)  NOT NULL DEFAULT '' COMMENT '数据源，clickhouse/mysql',
    `database_name` varchar(20)  NOT NULL DEFAULT '' COMMENT '库名',
    `table_name`    varchar(100) NOT NULL DEFAULT '' COMMENT '表名',
    `link_name`     varchar(100) NOT NULL DEFAULT '' COMMENT '链接名',
    `link`          varchar(200) NOT NULL DEFAULT '' COMMENT '链接',
    `create_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `create_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '创建人',
    `update_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `update_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB;

create table if not exists `bz_metadata_schema`
(
    `id`            bigint       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `datasource`    varchar(20)  NOT NULL DEFAULT '' COMMENT '数据源，clickhouse/mysql',
    `database_name` varchar(20)  NOT NULL DEFAULT '' COMMENT '库名',
    `table_name`    varchar(100) NOT NULL DEFAULT '' COMMENT '表名',
    `field_name`    varchar(100)  NOT NULL DEFAULT '' COMMENT '字段名',
    `field_type`    varchar(50)  NOT NULL DEFAULT '' COMMENT '字段类型',
    `default_value` varchar(100) NOT NULL DEFAULT '' COMMENT '缺省值',
    `position`      int          NOT NULL DEFAULT 0 COMMENT '字段位置',
    `field_comment` varchar(200) NOT NULL DEFAULT '' COMMENT '字段注释',
    `cp_logic`      longtext              DEFAULT NULL COMMENT 'CP计算逻辑',
    `ft_logic`      longtext              DEFAULT NULL COMMENT 'FT计算逻辑',
    `info`          longtext              DEFAULT NULL COMMENT '备注',
    `create_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `create_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '创建人',
    `update_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `update_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB;

create table if not exists `bz_metadata_lineage`
(
    `id`            bigint       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `from_source`   varchar(20)  NOT NULL DEFAULT '' COMMENT '起点数据源',
    `from_database` varchar(20)  NOT NULL DEFAULT '' COMMENT '起点库名',
    `from_table`    varchar(100) NOT NULL DEFAULT '' COMMENT '起点表名',
    `from_field`    varchar(100) NOT NULL DEFAULT '' COMMENT '起点字段名',
    `to_source`     varchar(20)  NOT NULL DEFAULT '' COMMENT '终点数据源',
    `to_database`   varchar(20)  NOT NULL DEFAULT '' COMMENT '终点库名',
    `to_table`      varchar(100) NOT NULL DEFAULT '' COMMENT '终点表名',
    `to_field`      varchar(100) NOT NULL DEFAULT '' COMMENT '终点字段名',
    `create_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `create_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '创建人',
    `update_time`   datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `update_user`   varchar(100) NOT NULL DEFAULT 'System' COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB;

create table if not exists bz_data_replay_record
(
    `id`               bigint      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `customer`         varchar(128) DEFAULT NULL COMMENT '客户名',
    `test_area`        varchar(64)  DEFAULT NULL COMMENT 'CP/FT/WAT等 表示测试阶段大类型，可能有多个',
    `factory`          varchar(256) DEFAULT NULL COMMENT '测试工厂，可能有多个',
    `device_id`        varchar(256) DEFAULT NULL COMMENT 'device id，可能有多个',
    `file_category`    varchar(64)  DEFAULT NULL COMMENT '文件类型，比如STDF、RAW_DATA等',
    `lot_id_list`      varchar(256) DEFAULT NULL COMMENT 'lot id，可能有多个',
    `wafer_no_list`    varchar(128) DEFAULT NULL COMMENT 'wafer no，可能有多个',
    `lot_type`         varchar(64)  DEFAULT NULL COMMENT 'Lot类型，Engineering/Production等，可能有多个',
    `test_stage_list`  varchar(256) DEFAULT NULL COMMENT 'test_stage值，可能有多个',
    `file_name`        varchar(256) DEFAULT NULL COMMENT '文件名',
    `replay_type`      varchar(64) NOT NULL COMMENT '删除/重跑,DELETE_FILE/DELETE_LOT/REPROCESS_FILE/REPROCESS_LOT',
    `step`             int         NOT NULL COMMENT '删除步骤(1000),重跑步骤(2200->需要转换的文件文件合并；3100->文件预解析；4100->文件解析到ods；5100->dwd层计算)',
    `replay_comments`  varchar(256) DEFAULT NULL COMMENT '重播备注',
    `replay_file_cnt`  int          DEFAULT NULL COMMENT '删除/重跑文件个数',
    `total_task_cnt`   int          DEFAULT NULL COMMENT '删除/重跑任务总数',
    `success_task_cnt` int          DEFAULT NULL COMMENT '删除/重跑任务执行成功数',
    `fail_task_cnt`    int          DEFAULT NULL COMMENT '删除/重跑任务执行失败数',
    `process_status`   varchar(128) DEFAULT NULL COMMENT '处理状态',
    `create_time`      datetime(3)  DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime(3)  DEFAULT NULL COMMENT '更新时间',
    `create_user`      varchar(128) DEFAULT 'System' COMMENT '创建用户',
    `update_user`      varchar(128) DEFAULT 'System' COMMENT '更新用户',
    PRIMARY KEY (`id`),
    KEY `i_file` (`file_name`),
    KEY `i_customer_area_factory_device_category` (`customer`, `test_area`, `factory`, `device_id`, `file_category`)
) ENGINE = InnoDB
    comment '数据重播记录';


create table if not exists bz_clear_rule_preview_record
(
    id                            bigint auto_increment
        primary key,
    customer                      varchar(128)  null comment '客户名',
    sub_customer                  varchar(256)  null comment '子客户',
    given_test_area               varchar(64)   null comment '给定的testArea，CP/FT',
    factory                       varchar(128)  null comment '测试工厂',
    factory_site                  varchar(128)  null comment '测试工厂子厂',
    fab                           varchar(128)  null comment '晶圆的生产厂',
    fab_site                      varchar(128)  null comment '晶圆的生产厂子厂',
    file_category                 varchar(64)   null comment '文件类型，STDF、RAW_DATA',
    file_name                     varchar(512)  null comment 'hdfs上zip压缩后的文件名',
    file_size                     bigint        null comment 'hdfs上zip压缩后的文件大小(byte)',
    origin_file_name              varchar(512)  null comment '原始文件解压出来的文件名',
    origin_file_size              bigint        null comment '原始文件解压出来的文件大小(byte)',
    remote_origin_file_name       varchar(512)  null comment '文件在ftp上的原始文件名',
    remote_origin_file_path       varchar(1024) null comment '文件在ftp上的原始目录路径',
    hdfs_path                     varchar(1024) null comment 'hdfs全路径',
    ftp_path                      varchar(1024) null comment '文件在ftp上的原始全路径',
    test_area                     varchar(64)   null comment '实际的testArea，CP/FT/SLT等',
    device_id                     varchar(128)  null comment 'device id',
    lot_id                        varchar(256)  null comment 'lot id',
    wafer_id                      varchar(128)  null comment '标准化后的wafer_id',
    origin_wafer_id               varchar(128)  null comment '原始的wafer_id',
    wafer_no                      varchar(128)  null comment 'wafer no',
    lot_type                      varchar(64)   null comment 'Lot类型，Engineering/Production等',
    offline_retest                int           null comment 'offline_retest值',
    interrupt                     int           null comment 'interrupt值',
    dup_retest                    int           null comment 'dup_retest值',
    batch_num                     int           null comment 'batch_num值',
    sblot_id                      varchar(256)  null comment 'sblot_id值',
    test_cod                      varchar(128)  null comment 'test_cod值',
    test_stage                    varchar(128)  null comment 'test_stage值',
    start_t                       varchar(128)  null comment 'start_t值',
    origin_start_t                varchar(128)  null comment 'start_t值',
    finish_t                      varchar(128)  null comment 'finish_t值',
    origin_finish_t               varchar(128)  null comment 'finish_t值',
    pos_x                         varchar(128)  null comment 'pos_x值',
    pos_y                         varchar(128)  null comment 'pos_y值',
    notch                         varchar(128)  null comment 'notch值',
    floor_id                      varchar(255)  null comment 'floor_id值',
    test_temperature              varchar(255)  null comment 'test_temperature值',
    tester_name                   varchar(128)  null comment 'TESTER名称',
    tester_type                   varchar(128)  null comment 'TESTER类型',
    probecard_loadboard_id        varchar(128)  null comment 'CP: CARD_ID, FT: LOAD_ID',
    test_program                  varchar(256)  null comment '测试程序名称',
    test_program_version          varchar(256)  null comment '测试程序版本',
    pkg_typ                       varchar(128)  null comment 'pkg_typ',
    retest_bin_num                varchar(256)  null comment 'retest hbin, 默认取值MIR的USER_TXT',
    `process`                     varchar(256)  null comment 'process值',
    condition_set                 longtext      null comment '测试条件集合，用于测试条件动态扩展，Map动态类型{Key: Value}',
    real_wafer_id                 varchar(128)  null comment 'real_wafer_id',
    process_status                varchar(128)  null comment '处理状态（PROCESSING->处理中；SUCCESS->处理成功；FAIL->处理失败；）',
    preview_result                longtext      null comment '预览结果json',
    preview_detail_file_hdfs_path varchar(1024) null comment '预览详细信息文件hdfs全路径',
    exception_type                varchar(128)  null comment '异常类型（KEY_FIELD_NULL->关键字段为空；OTHER_EXCEPTION->处理失败；）',
    exception_message             varchar(256)  null comment '异常信息',
    error_message                 longtext      null comment '错误信息',
    failed_fields                 varchar(256)  null comment '导致失败的异常字段,多个用英文逗号分隔',
    delete_flag                   int default 0 not null comment '删除标记：0->有效,1->删除',
    create_time                   datetime(3)   null comment '创建时间',
    update_time                   datetime(3)   null comment '更新时间',
    create_user                   varchar(128)  null comment '创建用户',
    update_user                   varchar(128)  null comment '更新用户'
) ENGINE = InnoDB comment '清洗规则预览记录';



