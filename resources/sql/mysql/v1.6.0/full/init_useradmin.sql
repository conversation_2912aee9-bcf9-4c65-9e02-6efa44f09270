use `useradmin`;

-- menu_type: 菜单类型（M目录 C菜单 F按钮）
-- is_frame: 是否为外链（0是 1否）
-- status: 菜单状态（0正常 1停用）
INSERT IGNORE INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (70700, '入库管理', 'config', 0, 75, 'dbconfig_70700', NULL, 1, 0, 'M', '0', '0', '', '#', 'root');

-- 一级菜单: 70700
SELECT @parentId70700 := 70700;
-- 二级菜单: order_num[402100,402200)
delete from sys_menu where menu_id = 180500;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180500, 'Conf预览', NULL, @parentId70700, 402110, '${ACCESS_ADDR}/bpms/#/clear-rule-preview', 'clear-rule-preview', 0, 0, 'C', '0', '0', '', '#', 'root');

-- 二级菜单: order_num[402400,402500)
delete from sys_menu where menu_id = 180100;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
(180100, '数据重播', NULL, @parentId70700, 402400, '${ACCESS_ADDR}/bpms/#/data-replay', 'data-replay', 0, 0, 'C', '0', '0', '', '#', 'root');
delete from sys_menu where menu_id = 180200;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180200, '计算任务', NULL, @parentId70700, 402410, '${ACCESS_ADDR}/bpms/#/task', 'task', 0, 0, 'C', '0', '0', '', '#', 'root');
delete from sys_menu where menu_id = 180300;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180300, '数据质量', NULL, @parentId70700, 402420, '${ACCESS_ADDR}/bpms/#/data-quality', 'data-quality', 0, 0, 'C', '0', '0', '', '#', 'root');
delete from sys_menu where menu_id = 180400;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180400, '元数据管理', NULL, @parentId70700, 402430, '${ACCESS_ADDR}/bpms/#/metadata', 'metadata', 0, 0, 'C', '0', '0', '', '#', 'root');
delete from sys_menu where menu_id = 180600;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180600, '业务计算管理', NULL, @parentId70700, 402440, '${ACCESS_ADDR}/bpms/#/service-management', 'service-management', 0, 0, 'C', '0', '0', '', '#', 'root');
delete from sys_menu where menu_id = 180700;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180700, '任务优先级管理', NULL, @parentId70700, 402450, '${ACCESS_ADDR}/bpms/#/priority', 'priority', 0, 0, 'C', '0', '0', '', '#', 'root');
delete from sys_menu where menu_id = 180800;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180800, '自动入库规则管理', NULL, @parentId70700, 402460, '${ACCESS_ADDR}/bpms/#/conf-mgt', 'conf-mgt', 0, 0, 'C', '0', '0', '', '#', 'root');

SELECT id
into @delete_notification_id
FROM notification_config
WHERE code = 'DATAWARE_DAILY_SUMMARY';
delete from notification_template where config_id = @delete_notification_id;
delete from notification_config where name = 'DATAWARE_DAILY_SUMMARY';

INSERT INTO notification_config (code, name, create_by, create_time, update_by, update_time, delete_flag,
                                 company_code, test_data, remark)
VALUES ('DATAWARE_DAILY_SUMMARY', 'OneData数据平台日报', 'root', '2025-03-25 11:14:23', null, null, 0, null, '', '');

SELECT id
into @notification_id
FROM notification_config
WHERE code = 'DATAWARE_DAILY_SUMMARY';

INSERT INTO notification_template (config_id, title, body, enabled, create_by, create_time, update_by,
                                   update_time, delete_flag, type, company_code, order_num, is_notify)
VALUES (@notification_id, 'OneData数据平台日报_${date}', '<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>大数据平台运行日报</title>
</head>
<body style="padding: 20px; margin: 0; background-color: #ffffff;">
<div style="width: 100%; margin: 0 auto; padding: 0 15px;">

  <!-- 平台数据概览 -->
  <div style="margin-bottom: 10px;">
    <h3 style="margin-bottom: 5px; color: #333; font-size: 18px;">平台数据概览</h3>
    <div style="overflow-x: auto;">
      <table style="width: 100%; border-collapse: collapse; border-spacing: 0; font-size: 14px; border: 1px solid #dee2e6;">
        <thead>
        <tr>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">日期</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">新增CP Wafer数</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">新增FT Lot数</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">新增Die数</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">新增测项数</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">自动重跑次数</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">手动重跑次数</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">磁盘使用量(G)<br/>(GDP|MPP)</th>
        </tr>
        </thead>
        <tbody>
        <tr th:each="overview : ${autoPlatformOverview}" style="background-color: #ffffff;">
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.date}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.datas[0].cpWaferCnt}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.datas[0].ftLotCnt}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.datas[0].dieCnt}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.datas[0].testItemCnt}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.datas[0].dieTaskCntAutoRerun}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.datas[0].dieTaskCntManualRerun}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${(overview.datas[0].gdpDiskUsed == null ? \'-\' :  overview.datas[0].gdpDiskUsed) + \' | \' + (overview.datas[0].mppDiskUsed == null ? \'-\' :  overview.datas[0].mppDiskUsed)}"></td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>


  <!-- 自动运行任务概览 -->
  <div style="margin-bottom: 10px;">
    <h3 style="margin-bottom: 5px; color: #333; font-size: 18px;">自动运行任务概览</h3>
    <div style="overflow-x: auto;">
      <table style="width: 100%; border-collapse: collapse; border-spacing: 0; font-size: 14px; border: 1px solid #dee2e6;">
        <thead>
        <tr>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">日期</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">新增文件数</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">新增文件量(G)<br/>(压缩|原始)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">扫描耗时(秒)<br/>(平均|最小|最大)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">转换文件<br/>(失败|成功|待处理|处理中)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">转换耗时(秒)<br/>(平均|最小|最大)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">解析文件<br/>(失败|成功|待处理|处理中)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">解析耗时(秒)<br/>(平均|最小|最大)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">Die任务<br/>(失败|成功|待处理|处理中)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">Die耗时(秒)<br/>(平均|最小|最大)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">Die等待(秒)<br/>(平均|最小|最大)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">测项任务<br/>(失败|成功|待处理|处理中)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">测项耗时(秒)<br/>(平均|最小|最大)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">测项等待(秒)<br/>(平均|最小|最大)</th>
        </tr>
        </thead>
        <tbody>
        <tr th:each="overview : ${autoTaskOverview}" style="background-color: #ffffff;">
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.date}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.datas[0].fileCnt}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.datas[0].fileSizeCompress + \' | \' + overview.datas[0].fileSizeOrigin}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;"
              th:text="${overview.datas[0].scanFileConsumeCnt == 0} ? \'-\'
          : ((${overview.datas[0].scanFileConsumeAvg} == 0 ? \'<1\' : ${overview.datas[0].scanFileConsumeAvg}) + \' | \'
          + (${overview.datas[0].scanFileConsumeMin} == 0 ? \'<1\' : ${overview.datas[0].scanFileConsumeMin}) + \' | \'
          + (${overview.datas[0].scanFileConsumeMax} == 0 ? \'<1\' : ${overview.datas[0].scanFileConsumeMax}))">
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;">
            <span th:style="${overview.datas[0].convertFileCntFail} > 0 ? \'color: red; font-weight: bold;\' : \'\'">
              [[${overview.datas[0].convertFileCntFail}]]
            </span>
            |
            [[${overview.datas[0].convertFileCntSuccess}]]
            |
            [[${overview.datas[0].convertFileCntPending}]]
            |
            [[${overview.datas[0].convertFileCntProcessing}]]
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;"
              th:text="${overview.datas[0].convertfileConsumeCnt == 0} ? \'-\'
            : ((${overview.datas[0].convertfileConsumeAvg} == 0 ? \'<1\' : ${overview.datas[0].convertfileConsumeAvg}) + \' | \'
            + (${overview.datas[0].convertfileConsumeMin} == 0 ? \'<1\' : ${overview.datas[0].convertfileConsumeMin}) + \' | \'
            + (${overview.datas[0].convertfileConsumeMax} == 0 ? \'<1\' : ${overview.datas[0].convertfileConsumeMax}))">
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;">
            <span th:style="${overview.datas[0].parsefileCntFail} > 0 ? \'color: red; font-weight: bold;\' : \'\'">
              [[${overview.datas[0].parsefileCntFail}]]
            </span>
            |
            [[${overview.datas[0].parsefileCntSuccess}]]
            |
            [[${overview.datas[0].parsefileCntPending}]]
            |
            [[${overview.datas[0].parsefileCntProcessing}]]
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;"
              th:text="${overview.datas[0].parsefileConsumeCnt == 0} ? \'-\'
            : ((${overview.datas[0].parsefileConsumeAvg} == 0 ? \'<1\' : ${overview.datas[0].parsefileConsumeAvg}) + \' | \'
            + (${overview.datas[0].parsefileConsumeMin} == 0 ? \'<1\' : ${overview.datas[0].parsefileConsumeMin}) + \' | \'
            + (${overview.datas[0].parsefileConsumeMax} == 0 ? \'<1\' : ${overview.datas[0].parsefileConsumeMax}))">
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;">
            <span th:style="${overview.datas[0].dieTaskCntFail} > 0 ? \'color: red; font-weight: bold;\' : \'\'">
              [[${overview.datas[0].dieTaskCntFail}]]
            </span>
            |
            [[${overview.datas[0].dieTaskCntSuccess}]]
            |
            [[${overview.datas[0].dieTaskCntPending}]]
            |
            [[${overview.datas[0].dieTaskCntProcessing}]]
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;"
              th:text="${overview.datas[0].dieTaskConsumeCnt == 0} ? \'-\'
            : ((${overview.datas[0].dieTaskConsumeAvg} == 0 ? \'<1\' : ${overview.datas[0].dieTaskConsumeAvg}) + \' | \'
            + (${overview.datas[0].dieTaskConsumeMin} == 0 ? \'<1\' : ${overview.datas[0].dieTaskConsumeMin}) + \' | \'
            + (${overview.datas[0].dieTaskConsumeMax} == 0 ? \'<1\' : ${overview.datas[0].dieTaskConsumeMax}))">
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;"
              th:text="${overview.datas[0].dieWaitTimeCnt == 0} ? \'-\'
            : ((${overview.datas[0].dieWaitTimeAvg} == 0 ? \'<1\' : ${overview.datas[0].dieWaitTimeAvg}) + \' | \'
            + (${overview.datas[0].dieWaitTimeMin} == 0 ? \'<1\' : ${overview.datas[0].dieWaitTimeMin}) + \' | \'
            + (${overview.datas[0].dieWaitTimeMax} == 0 ? \'<1\' : ${overview.datas[0].dieWaitTimeMax}))">
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;">
            <span th:style="${overview.datas[0].testItemTaskCntFail} > 0 ? \'color: red; font-weight: bold;\' : \'\'">
              [[${overview.datas[0].testItemTaskCntFail}]]
            </span>
            |
            [[${overview.datas[0].testItemTaskCntSuccess}]]
            |
            [[${overview.datas[0].testItemTaskCntPending}]]
            |
            [[${overview.datas[0].testItemTaskCntProcessing}]]
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;"
              th:text="${overview.datas[0].testItemTaskConsumeCnt == 0} ? \'-\'
            : ((${overview.datas[0].testItemTaskConsumeAvg} == 0 ? \'<1\' : ${overview.datas[0].testItemTaskConsumeAvg}) + \' | \'
            + (${overview.datas[0].testItemTaskConsumeMin} == 0 ? \'<1\' : ${overview.datas[0].testItemTaskConsumeMin}) + \' | \'
            + (${overview.datas[0].testItemTaskConsumeMax} == 0 ? \'<1\' : ${overview.datas[0].testItemTaskConsumeMax}))">
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;"
              th:text="${overview.datas[0].testItemWaitTimeCnt == 0} ? \'-\'
            : ((${overview.datas[0].testItemWaitTimeAvg} == 0 ? \'<1\' : ${overview.datas[0].testItemWaitTimeAvg}) + \' | \'
            + (${overview.datas[0].testItemWaitTimeMin} == 0 ? \'<1\' : ${overview.datas[0].testItemWaitTimeMin}) + \' | \'
            + (${overview.datas[0].testItemWaitTimeMax} == 0 ? \'<1\' : ${overview.datas[0].testItemWaitTimeMax}))">
          </td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>



  <!-- 手动运行任务概览 -->
  <div style="margin-bottom: 10px;">
    <h3 style="margin-bottom: 5px; color: #333; font-size: 18px;">手动运行任务概览</h3>
    <div style="overflow-x: auto;">
      <table style="width: 100%; border-collapse: collapse; border-spacing: 0; font-size: 14px; border: 1px solid #dee2e6;">
        <thead>
        <tr>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">日期</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">新增文件数</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">新增文件量(G)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">解析文件<br/>(失败|成功|待处理|处理中)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">解析耗时(秒)<br/>(平均|最小|最大)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">数仓任务<br/>(失败|成功|待处理|处理中)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">数仓耗时(秒)<br/>(平均|最小|最大)</th>
        </tr>
        </thead>
        <tbody>
        <tr th:each="overview : ${manualTaskOverview}" style="background-color: #ffffff;">
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.date}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.datas[0].fileCnt}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.datas[0].fileSize}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;">
            <span th:style="${overview.datas[0].parsefileCntFail} > 0 ? \'color: red; font-weight: bold;\' : \'\'">
              [[${overview.datas[0].parsefileCntFail}]]
            </span>
            |
            [[${overview.datas[0].parsefileCntSuccess}]]
            |
            [[${overview.datas[0].parsefileCntPending}]]
            |
            [[${overview.datas[0].parsefileCntProcessing}]]
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;"
              th:text="${overview.datas[0].parsefileConsumeCnt == 0} ? \'-\'
            : ((${overview.datas[0].parsefileConsumeAvg} == 0 ? \'<1\' : ${overview.datas[0].parsefileConsumeAvg}) + \' | \'
            + (${overview.datas[0].parsefileConsumeMin} == 0 ? \'<1\' : ${overview.datas[0].parsefileConsumeMin}) + \' | \'
            + (${overview.datas[0].parsefileConsumeMax} == 0 ? \'<1\' : ${overview.datas[0].parsefileConsumeMax}))">
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;">
            <span th:style="${overview.datas[0].wareHouseTaskCntFail} > 0 ? \'color: red; font-weight: bold;\' : \'\'">
              [[${overview.datas[0].wareHouseTaskCntFail}]]
            </span>
            |
            [[${overview.datas[0].wareHouseTaskCntSuccess}]]
            |
            [[${overview.datas[0].wareHouseTaskCntPending}]]
            |
            [[${overview.datas[0].wareHouseTaskCntProcessing}]]
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;"
              th:text="${overview.datas[0].wareHouseTaskConsumeCnt == 0} ? \'-\'
            : ((${overview.datas[0].wareHouseTaskConsumeAvg} == 0 ? \'<1\' : ${overview.datas[0].wareHouseTaskConsumeAvg}) + \' | \'
            + (${overview.datas[0].wareHouseTaskConsumeMin} == 0 ? \'<1\' : ${overview.datas[0].wareHouseTaskConsumeMin}) + \' | \'
            + (${overview.datas[0].wareHouseTaskConsumeMax} == 0 ? \'<1\' : ${overview.datas[0].wareHouseTaskConsumeMax}))">
          </td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>




  <!-- 自动任务耗时Top 10 -->
  <div style="margin-bottom: 10px;">
    <h3 style="margin-bottom: 5px; color: #333; font-size: 18px;">自动任务耗时Top 10</h3>
    <div style="overflow-x: auto;">
      <table style="width: 100%; border-collapse: collapse; border-spacing: 0; font-size: 14px; border: 1px solid #dee2e6;">
        <thead>
        <tr>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">任务名</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">开始时间</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">结束时间</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">耗时(秒)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">任务描述</th>
        </tr>
        </thead>
        <tbody>
        <tr th:if="${#lists.isEmpty(autoTaskSlow[0].datas)}">
          <td colspan="5" style="padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">暂无数据</td>
        </tr>
        <tr th:each="task : ${autoTaskSlow[0].datas}" style="background-color: #ffffff;">
          <td style="vertical-align: middle; width: 400px; padding: 3px; border: 1px solid #dee2e6; text-align: left; word-break: break-all;" th:text="${task.taskName}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.startTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.endTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.executeTime == 0} ? \'<1\' : ${task.executeTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.detail}"></td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>






  <!-- 手动任务耗时Top 10 -->
  <div style="margin-bottom: 10px;">
    <h3 style="margin-bottom: 5px; color: #333; font-size: 18px;">手动任务耗时Top 10</h3>
    <div style="overflow-x: auto;">
      <table style="width: 100%; border-collapse: collapse; border-spacing: 0; font-size: 14px; border: 1px solid #dee2e6;">
        <thead>
        <tr>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">任务名</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">开始时间</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">结束时间</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">耗时(秒)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">任务描述</th>
        </tr>
        </thead>
        <tbody>
        <tr th:if="${#lists.isEmpty(manualTaskSlow[0].datas)}">
          <td colspan="5" style="padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">暂无数据</td>
        </tr>
        <tr th:each="task : ${manualTaskSlow[0].datas}" style="background-color: #ffffff;">
          <td style="vertical-align: middle; width: 400px; padding: 3px; border: 1px solid #dee2e6; text-align: left; word-break: break-all;" th:text="${task.taskName}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.startTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.endTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.executeTime == 0} ? \'<1\' : ${task.executeTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.detail}"></td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>





  <!-- 自动任务失败概览 -->
  <div style="margin-bottom: 10px;">
    <h3 style="margin-bottom: 5px; color: #333; font-size: 18px;">自动任务失败概览</h3>
    <div style="overflow-x: auto;">
      <table style="width: 100%; border-collapse: collapse; border-spacing: 0; font-size: 14px; border: 1px solid #dee2e6;">
        <thead>
        <tr>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">任务名</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">开始时间</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">结束时间</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">耗时(秒)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">任务描述</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">异常信息</th>
        </tr>
        </thead>
        <tbody>
        <tr th:if="${#lists.isEmpty(autoTaskFail[0].datas)}">
          <td colspan="6" style="padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">暂无数据</td>
        </tr>
        <tr th:each="task : ${autoTaskFail[0].datas}" style="background-color: #ffffff;">
          <td style="vertical-align: middle; width: 400px; padding: 3px; border: 1px solid #dee2e6; text-align: left; word-break: break-all;" th:text="${task.taskName}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.startTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.endTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.executeTime == 0} ? \'<1\' : ${task.executeTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.detail}"></td>
          <td style="vertical-align: middle; min-width: 500px; padding: 3px; border: 1px solid #dee2e6; text-align: left;" th:text="${task.exceptionMessage}"></td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>







  <!-- 手动任务失败概览 -->
  <div style="margin-bottom: 10px;">
    <h3 style="margin-bottom: 5px; color: #333; font-size: 18px;">手动任务失败概览</h3>
    <div style="overflow-x: auto;">
      <table style="width: 100%; border-collapse: collapse; border-spacing: 0; font-size: 14px; border: 1px solid #dee2e6;">
        <thead>
        <tr>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">任务名</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">开始时间</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">结束时间</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">耗时(秒)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">任务描述</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">异常信息</th>
        </tr>
        </thead>
        <tbody>
        <tr th:if="${#lists.isEmpty(manualTaskFail[0].datas)}">
          <td colspan="6" style="padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">暂无数据</td>
        </tr>
        <tr th:each="task : ${manualTaskFail[0].datas}" style="background-color: #ffffff;">
          <td style="vertical-align: middle; width: 400px; padding: 3px; border: 1px solid #dee2e6; text-align: left; word-break: break-all;" th:text="${task.taskName}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.startTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.endTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.executeTime == 0} ? \'<1\' : ${task.executeTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.detail}"></td>
          <td style="vertical-align: middle; min-width: 500px; padding: 3px; border: 1px solid #dee2e6; text-align: left;" th:text="${task.exceptionMessage}"></td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>

</div>
</body>
</html>
', 1, 'root', '2025-03-25 11:14:23', null, null, 0, 'email', null, null, 0);

INSERT INTO notification_template (config_id, title, body, enabled, create_by, create_time, update_by,
                                   update_time, delete_flag, type, company_code, order_num, is_notify)
VALUES (@notification_id, '', '', 0, 'root', '2025-03-25 11:14:23', null, null, 0, 'insite', null, null, 0);
