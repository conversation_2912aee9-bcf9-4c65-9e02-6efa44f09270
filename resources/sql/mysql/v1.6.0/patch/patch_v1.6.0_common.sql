-- 升级时清理mysql旧数据
use `bpms`;
DELETE FROM `bpms`.bz_kafka_fail_message_record WHERE update_time < DATE_SUB(NOW(), INTERVAL 2 MONTH) AND delete_flag = 1;

USE `compute`;
UPDATE `compute`.bz_moudule_info SET version = '1.6.0', update_time = now() where moudule = 'bpms';

use `bpms`;

update bz_business_fail_record_query_condition set query_condition = ' exception_message in (\'连接Mysql异常\',\'连接Clickhouse异常\',\'常驻进程异常\',\'HDFS文件损坏\',\'调用Dubbo接口失败\',\'Zookeeper异常\',\'Clickhouse异常\',\'Redis异常\')' where `table` = 'bz_app_instance';
update bz_business_fail_record_query_condition set query_condition = ' dw_layer = \'DWD\' and exception_message in (\'连接Mysql异常\',\'连接Clickhouse异常\',\'常驻进程异常\',\'HDFS文件损坏\',\'调用Dubbo接口失败\',\'Zookeeper异常\',\'Clickhouse异常\',\'Redis异常\')' where `table` = 'dw_layer_calculate_pool';

truncate table bz_daily_summary_query_config;

INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoPlatformOverview', 'CP_WAFER_CNT', '', 'select ifnull(count(1), 0) cpWaferCnt
from (select date(substring_index(die_cal_time, \'~\', -1)) die_cal_date
      from dw_lot_wafer_cal_record
      where die_status in (\'SUCCESS\')
        and test_area not in (\'FT\', \'SLT\', \'CAL\', \'ASSY\', \'REL\', \'MT\', \'NA\')
        and create_time >= CURRENT_DATE() - INTERVAL 15 DAY
      group by lot_id, wafer_no, die_cal_date) x
where die_cal_date >= CURRENT_DATE() - INTERVAL 1 DAY
  and die_cal_date < CURRENT_DATE()', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoPlatformOverview', 'FT_LOT_CNT', '', 'select ifnull(count(1), 0) ftLotCnt
from (select date(substring_index(die_cal_time, \'~\', -1)) die_cal_date
      from dw_lot_wafer_cal_record
      where die_status in (\'SUCCESS\')
        and test_area in (\'FT\', \'SLT\', \'CAL\', \'ASSY\', \'REL\', \'MT\', \'NA\')
        and create_time >= CURRENT_DATE() - INTERVAL 15 DAY
      group by lot_id, die_cal_date) x
where die_cal_date >= CURRENT_DATE() - INTERVAL 1 DAY
  and die_cal_date < CURRENT_DATE()', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoPlatformOverview', 'DIE_CNT', '', 'select ifnull(sum(cnt), 0) dieCnt
from (select max(die_total_cnt)                           cnt,
             date(substring_index(die_cal_time, \'~\', -1)) die_cal_date
      from dw_lot_wafer_cal_record
      where die_status in (\'SUCCESS\')
        and create_time >= CURRENT_DATE() - INTERVAL 15 DAY
      group by customer,
               factory,
               test_area,
               device_id,
               lot_id,
               wafer_no,
               test_stage,
               lot_type,
               file_category,
               die_cal_date) x
where die_cal_date >= CURRENT_DATE() - INTERVAL 1 DAY
  and die_cal_date < CURRENT_DATE()', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoPlatformOverview', 'TEST_ITEM_CNT', '', 'select ifnull(sum(cnt), 0) testItemCnt
from (select max(test_item_total_cnt)                           cnt,
             date(substring_index(test_item_cal_time, \'~\', -1)) test_item_cal_date
      from dw_lot_wafer_cal_record
      where test_item_status in (\'SUCCESS\')
        and create_time >= CURRENT_DATE() - INTERVAL 15 DAY
        and calculate_dw_test_item = 1
      group by customer,
               factory,
               test_area,
               device_id,
               lot_id,
               wafer_no,
               test_stage,
               lot_type,
               file_category,
               test_item_cal_date) x
where test_item_cal_date >= CURRENT_DATE() - INTERVAL 1 DAY
  and test_item_cal_date < CURRENT_DATE()', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoPlatformOverview', 'AUTO_MANUAL_RERUN_TASK', '', 'select ifnull(sum(if(auto_rerun > 0, auto_rerun, 0)), 0)     dieTaskCntAutoRerun,
       ifnull(sum(if(manual_rerun > 0, manual_rerun, 0)), 0) dieTaskCntManualRerun
from (select if(auto > 0, auto, 0) auto_rerun,
             if(new_cal_cnt = 1,
                0,
                if(old_cal_cnt = 0,
                   if(new_manual_cnt = 0,
                      0,
                      (new_cal_cnt - 1) - (if(auto > 0, auto, 0))
                       ),
                   (new_cal_cnt - old_cal_cnt) - (if(auto > 0, auto, 0))
                    )
                 )                 manual_rerun
      from (select if(new_cal_cnt = 1,
                      0,
                      if(old_cal_cnt = 0,
                         if(new_manual_cnt = 0,
                            new_cal_cnt - 1,
                            new_cal_cnt - 1 - least(new_cal_cnt - 1, new_manual_cnt)
                             ),
                         (new_cal_cnt - least(new_cal_cnt, new_manual_cnt)) -
                         (old_cal_cnt - least(old_cal_cnt, old_manual_cnt))
                          )
                       ) auto,
                   x.*
            from (select a.cal_cnt               new_cal_cnt,
                         a.manual_cnt            new_manual_cnt,
                         ifnull(b.cal_cnt, 0)    old_cal_cnt,
                         ifnull(b.manual_cnt, 0) old_manual_cnt
                  from (select customer,
                               factory,
                               test_area,
                               device_id,
                               lot_id,
                               wafer_no,
                               test_stage,
                               lot_type,
                               file_category,
                               cal_cnt,
                               replay_cnt + repair_cnt manual_cnt
                        from dw_lot_wafer_cal_record
                        where id in (select max(id)
                                     from dw_lot_wafer_cal_record
                                     where create_time >= CURRENT_DATE() - INTERVAL 1 DAY
                                       and create_time < CURRENT_DATE()
                                     group by customer,
                                              factory,
                                              test_area,
                                              device_id,
                                              lot_id,
                                              wafer_no,
                                              test_stage,
                                              lot_type,
                                              file_category)) a
                           left join (select customer,
                                             factory,
                                             test_area,
                                             device_id,
                                             lot_id,
                                             wafer_no,
                                             test_stage,
                                             lot_type,
                                             file_category,
                                             cal_cnt,
                                             replay_cnt + repair_cnt manual_cnt
                                      from dw_lot_wafer_cal_record
                                      where id in (select max(id)
                                                   from dw_lot_wafer_cal_record
                                                   where create_time < CURRENT_DATE() - INTERVAL 1 DAY
                                                   group by customer,
                                                            factory,
                                                            test_area,
                                                            device_id,
                                                            lot_id,
                                                            wafer_no,
                                                            test_stage,
                                                            lot_type,
                                                            file_category)) b
                                     on
                                                 a.customer = b.customer and
                                                 a.factory = b.factory and
                                                 a.test_area = b.test_area and
                                                 a.device_id = b.device_id and
                                                 a.lot_id = b.lot_id and
                                                 a.wafer_no = b.wafer_no and
                                                 a.test_stage = b.test_stage and
                                                 a.lot_type = b.lot_type and
                                                 a.file_category = b.file_category) x) y) z', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'FILE_SIZE', '', 'select count(1)                                                        fileCnt,
       round(ifnull(sum(file_size), 0) / 1024 / 1024 / 1024, 3)        fileSizeCompress,
       round(ifnull(sum(origin_file_size), 0) / 1024 / 1024 / 1024, 3) fileSizeOrigin
from dw_sftp_file_detail
where create_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and create_time < CURRENT_DATE()
  and source_file_names is null', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'CONVERT_FILE_FINISH', '', 'select ifnull(sum(fail), 0)    convertFileCntFail,
       ifnull(sum(success), 0) convertFileCntSuccess
from (select ifnull(sum(if(process_status = \'FAIL\', 1, 0)), 0)    fail,
             ifnull(sum(if(process_status = \'SUCCESS\', 1, 0)), 0) success
      from dw_file_loading_log
      where step = 2200
        and step_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and step_end_time < CURRENT_DATE()
        and process_status in (\'FAIL\', \'SUCCESS\')
      union all
      select ifnull(sum(if(process_status = \'FAIL\', 1, 0)), 0) fail,
             0                                                 success
      from dw_file_loading_log
      where step = 2100
        and step_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and step_end_time < CURRENT_DATE()
        and process_status in (\'FAIL\')) x', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'CONVERT_FILE_NOT_FINISH', '', 'select ifnull(sum(pending), 0)    convertFileCntPending,
       ifnull(sum(processing), 0) convertFileCntProcessing
from (select ifnull(sum(if(process_status = \'CREATE\', 1, 0)), 0)     pending,
             ifnull(sum(if(process_status = \'PROCESSING\', 1, 0)), 0) processing
      from dw_sftp_file_detail
      where convert_flag = 1
        and process_status in (\'CREATE\', \'PROCESSING\')
      union all
      select ifnull(sum(if(process_status = \'CREATE\', 1, 0)), 0)     pending,
             ifnull(sum(if(process_status = \'PROCESSING\', 1, 0)), 0) processing
      from dw_log_map_detail
      where file_category = \'LOG\'
        and process_status in (\'CREATE\', \'PROCESSING\')) x', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'PRE_PARSE_FILE_FINISH', '', 'select ifnull(sum(if(process_status = \'FAIL\', 1, 0)), 0)    preParsefileCntFail,
       ifnull(sum(if(process_status = \'SUCCESS\', 1, 0)), 0) preParsefileCntSuccess
from dw_file_loading_log
where step = 3100
  and step_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and step_end_time < CURRENT_DATE()
  and process_status in (\'FAIL\', \'SUCCESS\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'PRE_PARSE_FILE_NOT_FINISH', '', 'select ifnull(sum(if(process_status = \'CREATE\', 1, 0)), 0)     preParsefileCntPending,
       ifnull(sum(if(process_status = \'PROCESSING\', 1, 0)), 0) preParsefileCntProcessing
from dw_sftp_file_detail
where convert_flag = 0
  and file_category in (\'STDF\', \'RAW_DATA\', \'BIT_MEM\')
  and process_status in (\'CREATE\', \'PROCESSING\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'PARSE_FILE_FINISH', '', 'select ifnull(sum(fail), 0)    parsefileCntFail,
       ifnull(sum(success), 0) parsefileCntSuccess
from (select ifnull(sum(if(process_status = \'FAIL\', 1, 0)), 0)    fail,
             ifnull(sum(if(process_status = \'SUCCESS\', 1, 0)), 0) success
      from dw_file_loading_log
      where step = 4100
        and step_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and step_end_time < CURRENT_DATE()
        and process_status in (\'FAIL\', \'SUCCESS\')
      union all
      select ifnull(sum(if(process_status = \'FAIL\', 1, 0)), 0) fail,
             0                                                 success
      from dw_file_loading_log
      where step = 3100
        and step_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and step_end_time < CURRENT_DATE()
        and process_status in (\'FAIL\')
      union all
      select ifnull(sum(if(process_status = \'FAIL\', 1, 0)), 0)    fail,
             ifnull(sum(if(process_status = \'SUCCESS\', 1, 0)), 0) success
      from dw_file_loading_log
      where step = 7100
        and file_category not in (\'STDF\', \'RAW_DATA\', \'BIT_MEM\')
        and step_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and step_end_time < CURRENT_DATE()
        and process_status in (\'FAIL\', \'SUCCESS\')) x', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'PARSE_FILE_NOT_FINISH', '', 'select ifnull(sum(pending), 0)    parsefileCntPending,
       ifnull(sum(processing), 0) parsefileCntProcessing
from (select ifnull(sum(if(process_status = \'CREATE\', 1, 0)), 0)     pending,
             ifnull(sum(if(process_status = \'PROCESSING\', 1, 0)), 0) processing
      from dw_lot_meta_data_detail
      where process_status in (\'CREATE\', \'PROCESSING\')
      union all
      select ifnull(sum(if(process_status = \'CREATE\', 1, 0)), 0)     pending,
             ifnull(sum(if(process_status = \'PROCESSING\', 1, 0)), 0) processing
      from dw_sftp_file_detail
      where convert_flag = 0
        and process_status in (\'CREATE\', \'PROCESSING\')) x', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'DIE_TASK_FINISH', '', 'select ifnull(sum(if(die_status = \'FAIL\', 1, 0)), 0)    dieTaskCntFail,
       ifnull(sum(if(die_status = \'SUCCESS\', 1, 0)), 0) dieTaskCntSuccess
from (select die_status, date(substring_index(die_cal_time, \'~\', -1)) die_cal_date
      from dw_lot_wafer_cal_record
      where die_status in (\'FAIL\', \'SUCCESS\')
        and create_time >= CURRENT_DATE() - INTERVAL 15 DAY) x
where die_cal_date >= CURRENT_DATE() - INTERVAL 1 DAY
  and die_cal_date < CURRENT_DATE()', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'DIE_TASK_NOT_FINISH', '', 'select ifnull(sum(if(process_status = \'CREATE\', 1, 0)), 0)     dieTaskCntPending,
       ifnull(sum(if(process_status = \'PROCESSING\', 1, 0)), 0) dieTaskCntProcessing
from dw_layer_calculate_pool
where dw_layer = \'DWD\'
  and process_status in (\'CREATE\', \'PROCESSING\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'TEST_ITEM_TASK_FINISH', '', 'select ifnull(sum(if(test_item_status = \'FAIL\', 1, 0)), 0)    testItemTaskCntFail,
       ifnull(sum(if(test_item_status = \'SUCCESS\', 1, 0)), 0) testItemTaskCntSuccess
from (select test_item_status, date(substring_index(test_item_cal_time, \'~\', -1)) test_item_cal_date
      from dw_lot_wafer_cal_record
      where test_item_status in (\'FAIL\', \'SUCCESS\')
        and create_time >= CURRENT_DATE() - INTERVAL 15 DAY
        and calculate_dw_test_item = 1) x
where test_item_cal_date >= CURRENT_DATE() - INTERVAL 1 DAY
  and test_item_cal_date < CURRENT_DATE()', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'TEST_ITEM_TASK_NOT_FINISH', '', 'select ifnull(sum(if(test_item_status = \'CREATE\', 1, 0)), 0)     testItemTaskCntPending,
       ifnull(sum(if(test_item_status = \'PROCESSING\', 1, 0)), 0) testItemTaskCntProcessing
from dw_lot_wafer_cal_status
where die_status = \'SUCCESS\'
  and test_item_status in (\'CREATE\', \'PROCESSING\')
  and calculate_dw_test_item = 1', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'SCAN_FILE_CONSUME', '', 'select round(ifnull(avg(timestampdiff(second, create_time, update_time)), 0), 0) scanFileConsumeAvg,
       ifnull(min(timestampdiff(second, create_time, update_time)), 0)           scanFileConsumeMin,
       ifnull(max(timestampdiff(second, create_time, update_time)), 0)           scanFileConsumeMax,
       count(1)                                                                  scanFileConsumeCnt
from dw_sftp_batch_info
where update_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and update_time < CURRENT_DATE()
  and batch_status in (\'SUCCESS\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'DOWNLOAD_FILE_CONSUME', '', 'select round(ifnull(avg(timestampdiff(second, step_start_time, step_end_time)), 0), 0) downloadFileConsumeAvg,
       ifnull(min(timestampdiff(second, step_start_time, step_end_time)), 0)           downloadFileConsumeMin,
       ifnull(max(timestampdiff(second, step_start_time, step_end_time)), 0)           downloadFileConsumeMax,
       count(1)                                                                        downloadFileConsumeCnt
from dw_file_loading_log
where step_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and step_end_time < CURRENT_DATE()
  and step = 1000
  and file_name like \'/%\'
  and process_status in (\'SUCCESS\', \'FAIL\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'CONVERT_FILE_CONSUME', '', 'select round(ifnull(avg(sec), 0), 0) convertfileConsumeAvg,
       ifnull(min(sec), 0)           convertfileConsumeMin,
       ifnull(max(sec), 0)           convertfileConsumeMax,
       count(1)                      convertfileConsumeCnt
from (select ifnull(sum(timestampdiff(second, step_start_time, step_end_time)), 0) sec
      from dw_file_loading_log
      where file_name in (select file_name
                          from dw_file_loading_log
                          where step = 2200
                            and step_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
                            and step_end_time < CURRENT_DATE()
                            and process_status in (\'FAIL\', \'SUCCESS\')
                          union
                          select file_name
                          from dw_file_loading_log
                          where step = 2100
                            and step_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
                            and step_end_time < CURRENT_DATE()
                            and process_status in (\'FAIL\'))
        and step in (2200, 2100)
      group by file_name) x', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'PRE_PARSE_FILE_CONSUME', '', 'select round(ifnull(avg(timestampdiff(second, step_start_time, step_end_time)), 0), 0) perParsefileConsumeAvg,
       ifnull(min(timestampdiff(second, step_start_time, step_end_time)), 0)           perParsefileConsumeMin,
       ifnull(max(timestampdiff(second, step_start_time, step_end_time)), 0)           preParsefileConsumeMax,
       count(1)                                                                        preParsefileConsumeCnt
from dw_file_loading_log
where step_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and step_end_time < CURRENT_DATE()
  and step = 3100
  and process_status in (\'SUCCESS\', \'FAIL\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'PARSE_FILE_CONSUME', '', 'select round(ifnull(avg(sec), 0), 0) parsefileConsumeAvg,
       ifnull(min(sec), 0)           parsefileConsumeMin,
       ifnull(max(sec), 0)           parsefileConsumeMax,
       count(1)                      parsefileConsumeCnt
from (select ifnull(sum(timestampdiff(second, step_start_time, step_end_time)), 0) sec
      from dw_file_loading_log
      where file_name in (select file_name
                          from dw_file_loading_log
                          where step = 4100
                            and step_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
                            and step_end_time < CURRENT_DATE()
                            and process_status in (\'FAIL\', \'SUCCESS\')
                          union
                          select file_name
                          from dw_file_loading_log
                          where step = 3100
                            and step_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
                            and step_end_time < CURRENT_DATE()
                            and process_status in (\'FAIL\'))
        and step in (3100, 4100)
      group by file_name
      union all
      select timestampdiff(second, step_start_time, step_end_time) sec
      from dw_file_loading_log
      where step = 7100
        and file_category not in (\'STDF\', \'RAW_DATA\', \'BIT_MEM\')
        and step_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and step_end_time < CURRENT_DATE()
        and process_status in (\'FAIL\', \'SUCCESS\')) x', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'DIE_TASK_CONSUME', '', 'select round(ifnull(avg(sec), 0), 0) dieTaskConsumeAvg,
       round(ifnull(min(sec), 0), 0) dieTaskConsumeMin,
       round(ifnull(max(sec), 0), 0) dieTaskConsumeMax,
       count(1)                      dieTaskConsumeCnt
from (select date(substring_index(die_cal_time, \'~\', -1))                                                         die_cal_date,
             timestampdiff(second, substring_index(die_cal_time, \'~\', 1), substring_index(die_cal_time, \'~\', -1)) sec
      from dw_lot_wafer_cal_record
      where die_status in (\'FAIL\', \'SUCCESS\')
        and create_time >= CURRENT_DATE() - INTERVAL 15 DAY) x
where die_cal_date >= CURRENT_DATE() - INTERVAL 1 DAY
  and die_cal_date < CURRENT_DATE()', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'TEST_ITEM_TASK_CONSUME', '', 'select round(ifnull(avg(sec), 0), 0) testItemTaskConsumeAvg,
       round(ifnull(min(sec), 0), 0) testItemTaskConsumeMin,
       round(ifnull(max(sec), 0), 0) testItemTaskConsumeMax,
       count(1)                      testItemTaskConsumeCnt
from (select date(substring_index(test_item_cal_time, \'~\', -1))          test_item_cal_date,
             timestampdiff(second, substring_index(test_item_cal_time, \'~\', 1),
                           substring_index(test_item_cal_time, \'~\', -1)) sec
      from dw_lot_wafer_cal_record
      where test_item_status in (\'FAIL\', \'SUCCESS\')
        and create_time >= CURRENT_DATE() - INTERVAL 15 DAY
        and calculate_dw_test_item = 1) x
where test_item_cal_date >= CURRENT_DATE() - INTERVAL 1 DAY
  and test_item_cal_date < CURRENT_DATE()', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'DIE_WAIT_TIME', '', 'select round(ifnull(avg(sec), 0), 0) dieWaitTimeAvg,
       round(ifnull(min(sec), 0), 0) dieWaitTimeMin,
       round(ifnull(max(sec), 0), 0) dieWaitTimeMax,
       count(1)                      dieWaitTimeCnt
from (select date(substring_index(die_cal_time, \'~\', -1))         die_cal_date,
             timestampdiff(second, substring_index(file_pre_parse_times, \'~\', -1),
                           substring_index(die_cal_time, \'~\', 1)) sec
      from dw_lot_wafer_cal_record
      where die_status in (\'FAIL\', \'SUCCESS\')
        and create_time >= CURRENT_DATE() - INTERVAL 15 DAY) x
where die_cal_date >= CURRENT_DATE() - INTERVAL 1 DAY
  and die_cal_date < CURRENT_DATE()', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskOverview', 'TEST_ITEM_WAIT_TIME', '', 'select round(ifnull(avg(sec), 0), 0) testItemWaitTimeAvg,
       round(ifnull(min(sec), 0), 0) testItemWaitTimeMin,
       round(ifnull(max(sec), 0), 0) testItemWaitTimeMax,
       count(1)                      testItemWaitTimeCnt
from (select date(substring_index(test_item_cal_time, \'~\', -1))         test_item_cal_date,
             timestampdiff(second, substring_index(die_cal_time, \'~\', -1),
                           substring_index(test_item_cal_time, \'~\', 1)) sec
      from dw_lot_wafer_cal_record
      where test_item_status in (\'FAIL\', \'SUCCESS\')
        and create_time >= CURRENT_DATE() - INTERVAL 15 DAY
        and calculate_dw_test_item = 1) x
where test_item_cal_date >= CURRENT_DATE() - INTERVAL 1 DAY
  and test_item_cal_date < CURRENT_DATE()', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'manualTaskOverview', 'FILE_SIZE', '', 'select count(1)                                                 fileCnt,
       round(ifnull(sum(file_size), 0) / 1024 / 1024 / 1024, 3) fileSize
from dw_manual_file_info
where create_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and create_time < CURRENT_DATE()
  and origin_file_ids = \'\'', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'manualTaskOverview', 'PARSE_FILE_FINISH', '', 'select ifnull(sum(if(process_status = \'FAIL\', 1, 0)), 0)    parsefileCntFail,
       ifnull(sum(if(process_status = \'SUCCESS\', 1, 0)), 0) parsefileCntSuccess
from dw_manual_calculate_task
where update_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and update_time < CURRENT_DATE()
  and manual_type = \'UPLOAD\'
  and dw_layer = \'ODS\'
  and process_status in (\'FAIL\', \'SUCCESS\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'manualTaskOverview', 'PARSE_FILE_NOT_FINISH', '', 'select ifnull(sum(if(process_status = \'CREATE\', 1, 0)), 0)     parsefileCntPending,
       ifnull(sum(if(process_status = \'PROCESSING\', 1, 0)), 0) parsefileCntProcessing
from dw_manual_calculate_task
where manual_type = \'UPLOAD\'
  and dw_layer = \'ODS\'
  and process_status in (\'CREATE\', \'PROCESSING\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'manualTaskOverview', 'WAREHOUSE_TASK_FINISH', '', 'select ifnull(sum(if(process_status = \'FAIL\', 1, 0)), 0)    wareHouseTaskCntFail,
       ifnull(sum(if(process_status = \'SUCCESS\', 1, 0)), 0) wareHouseTaskCntSuccess
from dw_manual_calculate_task
where update_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and update_time < CURRENT_DATE()
  and dw_layer = \'DWD\'
  and process_status in (\'FAIL\', \'SUCCESS\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'manualTaskOverview', 'WAREHOUSE_TASK_NOT_FINISH', '', 'select ifnull(sum(if(process_status = \'CREATE\', 1, 0)), 0)     wareHouseTaskCntPending,
       ifnull(sum(if(process_status = \'PROCESSING\', 1, 0)), 0) wareHouseTaskCntProcessing
from dw_manual_calculate_task
where dw_layer = \'DWD\'
  and process_status in (\'CREATE\', \'PROCESSING\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'manualTaskOverview', 'PARSE_FILE_CONSUME', '', 'select round(ifnull(avg(timestampdiff(second, create_time, update_time)), 0), 0) parsefileConsumeAvg,
       ifnull(min(timestampdiff(second, create_time, update_time)), 0)           parsefileConsumeMin,
       ifnull(max(timestampdiff(second, create_time, update_time)), 0)           parsefileConsumeMax,
       count(1)                                                                  parsefileConsumeCnt
from dw_manual_calculate_task
where update_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and update_time < CURRENT_DATE()
  and manual_type = \'UPLOAD\'
  and dw_layer = \'ODS\'
  and process_status in (\'SUCCESS\', \'FAIL\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'manualTaskOverview', 'WAREHOUSE_TASK_CONSUME', '', 'select round(ifnull(avg(timestampdiff(second, create_time, update_time)), 0), 0) wareHouseTaskConsumeAvg,
       ifnull(min(timestampdiff(second, create_time, update_time)), 0)           wareHouseTaskConsumeMin,
       ifnull(max(timestampdiff(second, create_time, update_time)), 0)           wareHouseTaskConsumeMax,
       count(1)                                                                  wareHouseTaskConsumeCnt
from dw_manual_calculate_task
where update_time >= CURRENT_DATE() - INTERVAL 1 DAY
  and update_time < CURRENT_DATE()
  and dw_layer = \'DWD\'
  and process_status in (\'SUCCESS\', \'FAIL\')', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskSlow', 'TOP_10', '', 'select taskName, startTime, endTime, executeTime, detail
from (select regexp_replace(app_name, \'.*\\.impl\\.\', \'\')   taskName,
             date_format(start_time, \'%Y-%m-%d %H:%i:%s\') startTime,
             date_format(end_time, \'%Y-%m-%d %H:%i:%s\')   endTime,
             round(execute_time / 1000, 0)                executeTime,
             if(queue = \'die\', \'Die任务\', \'测项任务\')     detail
      from compute.bz_compute_pool
      where end_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and end_time < CURRENT_DATE()
        and process_status in (\'FAIL\', \'SUCCESS\')
        and queue in (\'die\', \'testitem\')
      union
      select file_name                                             taskName,
             date_format(step_start_time, \'%Y-%m-%d %H:%i:%s\')     startTime,
             date_format(step_end_time, \'%Y-%m-%d %H:%i:%s\')       endTime,
             timestampdiff(second, step_start_time, step_end_time) executeTime,
             case step
                 when 1000 then \'文件拉取\'
                 when 2100 then \'文件转换前读取\'
                 when 2200 then \'文件转换\'
                 when 3100 then \'文件预解析\'
                 when 4100 then \'文件正式解析\'
                 when 7100 then \'文件入库\'
                 else \'\'
                 end
                                                                   detail
      from dw_file_loading_log
      where (step in (1000, 2100, 2200, 3100, 4100) or
             (step = 7100 and file_category not in (\'STDF\', \'RAW_DATA\', \'BIT_MEM\')))
        and process_status in (\'FAIL\', \'SUCCESS\')
        and step_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and step_end_time < CURRENT_DATE()) x
order by executeTime desc
limit 10', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'autoTaskFail', 'FAIL_DETAIL', '', 'select taskName, startTime, endTime, executeTime, detail, exceptionMessage
from (select regexp_replace(app_name, \'.*\\.impl\\.\', \'\')   taskName,
             date_format(start_time, \'%Y-%m-%d %H:%i:%s\') startTime,
             date_format(end_time, \'%Y-%m-%d %H:%i:%s\')   endTime,
             round(execute_time / 1000, 0)                executeTime,
             if(queue = \'die\', \'Die任务\', \'测项任务\')     detail,
             substr(error_message, 1, 1000)               exceptionMessage
      from compute.bz_compute_pool
      where end_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and end_time < CURRENT_DATE()
        and process_status = \'FAIL\'
        and queue in (\'die\', \'testitem\')
      union
      select file_name                                             taskName,
             date_format(step_start_time, \'%Y-%m-%d %H:%i:%s\')     startTime,
             date_format(step_end_time, \'%Y-%m-%d %H:%i:%s\')       endTime,
             timestampdiff(second, step_start_time, step_end_time) executeTime,
             case step
                 when 1000 then \'文件拉取\'
                 when 2100 then \'文件转换前读取\'
                 when 2200 then \'文件转换\'
                 when 3100 then \'文件预解析\'
                 when 4100 then \'文件正式解析\'
                 when 7100 then \'文件入库\'
                 else \'\'
                 end
                                                                   detail,
             substr(error_message, 1, 1000)                        exceptionMessage
      from dw_file_loading_log
      where (step in (1000, 2100, 2200, 3100, 4100) or
             (step = 7100 and file_category not in (\'STDF\', \'RAW_DATA\', \'BIT_MEM\')))
        and process_status = \'FAIL\'
        and step_end_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and step_end_time < CURRENT_DATE()) x
order by endTime desc
limit 100', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'manualTaskSlow', 'TOP_10', '', 'select taskName, startTime, endTime, executeTime, detail
from (select regexp_replace(app_name, \'.*\\.impl\\.\', \'\')   taskName,
             date_format(start_time, \'%Y-%m-%d %H:%i:%s\') startTime,
             date_format(end_time, \'%Y-%m-%d %H:%i:%s\')   endTime,
             round(execute_time / 1000, 0)                executeTime,
             \'数仓任务\'                                   detail
      from compute.bz_compute_pool
      where end_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and end_time < CURRENT_DATE()
        and process_status in (\'FAIL\', \'SUCCESS\')
        and queue in (\'manual\')
      union
      select file_name                                       taskName,
             date_format(create_time, \'%Y-%m-%d %H:%i:%s\')   startTime,
             date_format(update_time, \'%Y-%m-%d %H:%i:%s\')   endTime,
             timestampdiff(second, create_time, update_time) executeTime,
             case dw_layer
                 when \'ODS\' then \'文件解析\'
                 when \'DWD\' then \'数仓任务\'
                 else \'\'
                 end
                                                             detail
      from dw_manual_calculate_task
      where (dw_layer = \'ODS\' or (dw_layer = \'DWD\' and run_mode = \'STANDALONE\'))
        and process_status in (\'FAIL\', \'SUCCESS\')
        and update_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and update_time < CURRENT_DATE()) x
order by executeTime desc
limit 10', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'onedata', 'manualTaskFail', 'FAIL_DETAIL', '', 'select taskName, startTime, endTime, executeTime, detail, exceptionMessage
from (select regexp_replace(app_name, \'.*\\.impl\\.\', \'\')   taskName,
             date_format(start_time, \'%Y-%m-%d %H:%i:%s\') startTime,
             date_format(end_time, \'%Y-%m-%d %H:%i:%s\')   endTime,
             round(execute_time / 1000, 0)                executeTime,
             \'数仓任务\'                                   detail,
             substr(error_message, 1, 1000)               exceptionMessage
      from compute.bz_compute_pool
      where end_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and end_time < CURRENT_DATE()
        and process_status = \'FAIL\'
        and queue in (\'manual\')
      union
      select a.file_name                                         taskName,
             date_format(a.create_time, \'%Y-%m-%d %H:%i:%s\')     startTime,
             date_format(a.update_time, \'%Y-%m-%d %H:%i:%s\')     endTime,
             timestampdiff(second, a.create_time, a.update_time) executeTime,
             case a.dw_layer
                 when \'ODS\' then \'文件解析\'
                 when \'DWD\' then \'数仓任务\'
                 else \'\'
                 end
                                                                 detail,
             substr(b.error_message, 1, 1000)                      exceptionMessage
      from dw_manual_calculate_task a
               join dw_manual_message b on a.message_id = b.id
      where (a.dw_layer = \'ODS\' or (a.dw_layer = \'DWD\' and a.run_mode = \'STANDALONE\'))
        and a.process_status = \'FAIL\'
        and a.update_time >= CURRENT_DATE() - INTERVAL 1 DAY
        and a.update_time < CURRENT_DATE()) x
order by endTime desc
limit 100', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');
INSERT INTO bz_daily_summary_query_config (type, db, code, query_name, query_desc, fields_query, create_time, create_user, update_time, update_user) VALUES ('MYSQL', 'bpms', 'autoPlatformOverview', 'GDP_MPP_DISK_USED', '', 'select round(ifnull(max(if(indicator_name = \'TOTAL_GDP_DISK_USED_AMT\', indicator_value, 0)), 0), 3) gdpDiskUsed,
       round(ifnull(max(if(indicator_name = \'TOTAL_MPP_DISK_USED_AMT\', indicator_value, 0)), 0), 4) mppDiskUsed
from gubo_system_metrics_view
where delete_flag = 0
  and category = \'SERVER\'
  and indicator_name in (
                         \'TOTAL_GDP_DISK_USED_AMT\',
                         \'TOTAL_MPP_DISK_USED_AMT\'
    )
  and dt >= CURRENT_DATE() - INTERVAL 1 DAY
  and dt < CURRENT_DATE()
;', '2025-03-19 17:38:22', 'System', '2025-03-19 17:38:25', 'System');


-- 注销旧的调度任务
use `xxl_job`;

delete from xxl_job_info
where job_group in (select id
                    FROM xxl_job_group
                    where app_name IN ('bigdata-bpms', 'bigdata-dataware', 'bigdata-rule', 'bigdata-yms'));

-- 向bpms注册删除ck旧数据定时任务:
select db into @ods_db FROM `onedata`.dw_table where dw_layer = 'ODS' limit 1;
select db into @dwd_db FROM `onedata`.dw_table where dw_layer = 'DWD' limit 1;
select db into @dws_db FROM `onedata`.dw_table where dw_layer = 'DWS' limit 1;
select db into @dim_db FROM `onedata`.dw_table where dw_layer = 'DIM' limit 1;
select db into @ads_db FROM `onedata`.dw_table where dw_layer = 'ADS' limit 1;

select concat(@dwd_db,'.','dwd_die_detail_local;',@dwd_db,'.','dwd_test_item_detail_local;',@dwd_db,'.','dwd_shmoo_detail_local;',@dwd_db,'.','dwd_shmoo_vmin_vmax_detail_local;',@dwd_db,'.','dwd_dft_detail_local;',@dwd_db,'.','dwd_mes_bin_detail_local;',@dwd_db,'.','dwd_assy_lot_overall_yield_detail_local;') into @dataware_dwd_table_name;
select concat(@dim_db,'.','dim_lot_relation_local;',@dim_db,'.','dim_lot_wafer_bin_local;',@dim_db,'.','dim_lot_wafer_local;',@dim_db,'.','dim_test_item_local;',@dim_db,'.','dim_test_program_bin_local;',@dim_db,'.','dim_test_program_site_local;',@dim_db,'.','dim_test_program_test_item_local;',@dim_db,'.','dim_bin_failitem_local;',@dim_db,'.','dim_sblot_local;',@dim_db,'.','dim_flowid_local;') into @dataware_dim_table_name;
select concat(@dws_db,'.','dws_bin_failitem_index_local;',@dws_db,'.','dws_bin_index_local;',@dws_db,'.','dws_bin_test_item_index_local;',@dws_db,'.','dws_flowid_bin_index_local;',@dws_db,'.','dws_lot_wafer_data_check_index_local;',@dws_db,'.','dws_lot_wafer_index_local;',@dws_db,'.','dws_pts_index_local;',@dws_db,'.','dws_sblot_index_local;',@dws_db,'.','dws_site_bin_index_local;',@dws_db,'.','dws_test_item_index_local;',@dws_db,'.','dws_wafer_index_local;',@dws_db,'.','dws_wafer_overall_yield_index_local;',@dws_db,'.','dws_sblot_bin_index_local;',@dws_db,'.','dws_sblot_analog_index_local;',@dws_db,'.','dws_sblot_bin_analog_index_local;',@dws_db,'.','dws_assy_lot_overall_yield_index_local;') into @dataware_dws_table_name;
select concat(@ods_db,'.','ods_dc_device_info_local;',@ods_db,'.','ods_dc_product_info_local;',@ods_db,'.','ods_dc_device_wafermap_config_local;',@ods_db,'.','ods_dc_product_wafermap_config_local;',@ods_db,'.','ods_dc_device_wafermap_config_mapping_local;',@ods_db,'.','ods_yms_wafermap_config_mapping_snapshot_local;',@ods_db,'.','ods_yms_wafermap_config_snapshot_local;',@ods_db,'.','ods_yms_device_wafermap_config_lot_wafer_local;') into @yms_ods_table_name;
select concat(@dwd_db,'.','dwd_yms_die_detail_local;') into @yms_dwd_table_name;
select concat(@dim_db,'.','dim_yms_sblot_wafer_local;') into @yms_dim_table_name;
select concat(@dws_db,'.','dws_yms_flowid_local;',@dws_db,'.','dws_yms_sblot_wafer_analog_local;',@dws_db,'.','dws_yms_sblot_wafer_bin_analog_local;',@dws_db,'.','dws_yms_sblot_wafer_bin_local;',@dws_db,'.','dws_yms_sblot_wafer_binswap_local;',@dws_db,'.','dws_yms_sblot_wafer_local;',@dws_db,'.','dws_yms_sblot_wafer_site_bin_analog_local;',@dws_db,'.','dws_yms_sblot_wafer_site_bin_local;',@dws_db,'.','dws_yms_mes_sblot_wafer_bin_local;') into @yms_dws_table_name;
select concat(@ads_db,'.','ads_yms_flowid_test_time_local;',@ads_db,'.','ads_yms_stage_sblot_wafer_analog_local;',@ads_db,'.','ads_yms_stage_sblot_wafer_bin_analog_local;',@ads_db,'.','ads_yms_stage_sblot_wafer_bin_local;',@ads_db,'.','ads_yms_stage_sblot_wafer_binswap_local;',@ads_db,'.','ads_yms_stage_sblot_wafer_local;',@ads_db,'.','ads_yms_stage_sblot_wafer_site_analog_local;',@ads_db,'.','ads_yms_stage_sblot_wafer_site_bin_analog_local;',@ads_db,'.','ads_yms_stage_sblot_wafer_site_bin_local;',@ads_db,'.','ads_yms_stage_sblot_wafer_site_local;',@ads_db,'.','ads_yms_stage_test_item_program_local;',@ads_db,'.','ads_yms_stage_test_item_program_site_local;',@ads_db,'.','ads_yms_stage_test_item_program_bin_local;',@ads_db,'.','ads_yms_stage_test_item_program_site_bin_local;') into @yms_ads_table_name;
select concat(@dws_db,'.','dws_rule_sblot_wafer_local;',@dws_db,'.','dws_rule_sblot_analog_local;',@dws_db,'.','dws_rule_sblot_wafer_bin_local;',@dws_db,'.','dws_rule_sblot_bin_analog_local;',@dws_db,'.','dws_rule_sblot_wafer_site_bin_local;',@dws_db,'.','dws_rule_sblot_site_bin_analog_local;',@dws_db,'.','dws_rule_lot_local;',@dws_db,'.','dws_rule_lot_analog_local;',@dws_db,'.','dws_rule_lot_bin_local;',@dws_db,'.','dws_rule_lot_bin_analog_local;',@dws_db,'.','dws_rule_lot_site_bin_local;',@dws_db,'.','dws_rule_lot_site_bin_analog_local;') into @rule_dws_table_name;
select concat(@ads_db,'.','ads_rule_stage_sblot_wafer_local;',@ads_db,'.','ads_rule_stage_sblot_wafer_analog_local;',@ads_db,'.','ads_rule_stage_sblot_wafer_bin_local;',@ads_db,'.','ads_rule_stage_sblot_wafer_bin_analog_local;',@ads_db,'.','ads_rule_stage_sblot_wafer_site_local;',@ads_db,'.','ads_rule_stage_sblot_wafer_site_analog_local;',@ads_db,'.','ads_rule_stage_sblot_wafer_site_bin_local;',@ads_db,'.','ads_rule_stage_sblot_wafer_site_bin_analog_local;',@ads_db,'.','ads_rule_stage_lot_local;',@ads_db,'.','ads_rule_stage_lot_analog_local;',@ads_db,'.','ads_rule_stage_lot_bin_local;',@ads_db,'.','ads_rule_stage_lot_bin_analog_local;',@ads_db,'.','ads_rule_stage_lot_site_local;',@ads_db,'.','ads_rule_stage_lot_site_analog_local;',@ads_db,'.','ads_rule_stage_lot_site_bin_local;',@ads_db,'.','ads_rule_stage_lot_site_bin_analog_local;') into @rule_ads_table_name;

INSERT INTO bpms.bz_schedule_task_definition (task_name, task_description, execute_engine, cron_expression, command, is_active, delete_flag, create_time, update_time, create_user, update_user)
VALUES ('delete-dataware-dwd', 'dataware-dwd表定时删数据任务', 'CLICKHOUSE', '0 0 4 ? * 7',  @dataware_dwd_table_name, true, false, now(),  now(), 'SYSTEM', 'SYSTEM')
     ,('delete-dataware-dim', 'dataware-dim表定时删数据任务', 'CLICKHOUSE', '0 0 2 ? * 7',  @dataware_dim_table_name, true, false, now(),  now(), 'SYSTEM', 'SYSTEM')
     ,('delete-dataware-dws', 'dataware-dws表定时删数据任务', 'CLICKHOUSE', '0 0 3 ? * 7',  @dataware_dws_table_name, true, false, now(),  now(), 'SYSTEM', 'SYSTEM')
     ,('delete-yms-ods',      'yms-ods表定时删数据任务',      'CLICKHOUSE', '0 10 3 ? * 7', @yms_ods_table_name,      true, false, now(),  now(), 'SYSTEM', 'SYSTEM')
     ,('delete-yms-dwd',      'yms-dwd表定时删数据任务',      'CLICKHOUSE', '0 10 3 ? * 7', @yms_dwd_table_name,      true, false, now(),  now(), 'SYSTEM', 'SYSTEM')
     ,('delete-yms-dim',      'yms-dim表定时删数据任务',      'CLICKHOUSE', '0 10 3 ? * 7', @yms_dim_table_name,      true, false, now(),  now(), 'SYSTEM', 'SYSTEM')
     ,('delete-yms-dws',      'yms-dws表定时删数据任务',      'CLICKHOUSE', '0 20 3 ? * 7', @yms_dws_table_name,      true, false, now(),  now(), 'SYSTEM', 'SYSTEM')
     ,('delete-yms-ads',      'yms-ads表定时删数据任务',      'CLICKHOUSE', '0 30 3 ? * 7', @yms_ads_table_name,      true, false, now(),  now(), 'SYSTEM', 'SYSTEM')
     ,('delete-rule-dws',     'rule-dws表定时删数据任务',     'CLICKHOUSE', '0 20 3 ? * 7', @rule_dws_table_name,     true, false, now(),  now(), 'SYSTEM', 'SYSTEM')
     ,('delete-rule-ads',     'rule-ads表定时删数据任务',     'CLICKHOUSE', '0 30 3 ? * 7', @rule_ads_table_name,     true, false, now(),  now(), 'SYSTEM', 'SYSTEM');

INSERT INTO bpms.bz_schedule_task_definition (task_name, task_description, execute_engine, cron_expression, command, is_active, delete_flag, create_time, update_time, create_user, update_user)
VALUES ('wafermap-config',        'WafermapConfig推算任务', 'SPARK', '0 0 1 * * ?',  'com.guwave.onedata.dataware.dw.spark.task.impl.WafermapConfigBatchTask',       true, false, now(),  now(), 'SYSTEM', 'SYSTEM')
     ,('wafer-overall-yield',    '综合良率计算任务',       'SPARK', '0 0 0 * * ?',  'com.guwave.onedata.dataware.dw.spark.task.impl.WaferOverallYieldTask',         ${wafer_overall_yield}, false, now(),  now(), 'SYSTEM', 'SYSTEM')
     ,('assembly-overall-yield', '封装综合良率计算任务',   'SPARK', '30 0 0 * * ?', 'com.guwave.onedata.dataware.dw.spark.task.impl.AssemblyOverallStageYieldTask', ${assembly_overall_yield}, false, now(),  now(), 'SYSTEM', 'SYSTEM');

-- python的定时任务：
INSERT INTO bpms.bz_schedule_task_definition (task_name, task_description, execute_engine, cron_expression, command, is_active, delete_flag, create_time, update_time, create_user, update_user)
VALUES ('dim-refresh',       'dim表定时刷新任务', 'PYTHON', '0 0 2 * * ?',  '/home/<USER>/deploy/datahub/bpms/cron/dim_refresh.py|{ck_address}|{mysqlAddress}|{mysqlUserName}|{mysqlPassword}|{ck_user}|{ck_password}|{dwd_database}|{dim_database}|{onedataDatabase}|{bpmsUserName}|{bpmsPassword}|{bpmsDatabase}', true, false, now(),  now(), 'SYSTEM', 'SYSTEM')
     ,('ck-disk-rebalance', 'CK磁盘动平衡任务',  'PYTHON', '0 0 22 ? * 6', '/home/<USER>/deploy/datahub/bpms/cron/ck_disk_rebalance.py|{replica_address}|{ck_user}|{ck_password}', true, false, now(),  now(), 'SYSTEM', 'SYSTEM')
     ,('lot-history',      'lot_history计算任务',      'PYTHON', '0 0 * * * ?',  '/home/<USER>/deploy/datahub/bpms/cron/lot_history.py|{ck_address}|{mysqlAddress}|{ck_user}|{ck_password}|{dwd_database}|{mysqlUserName}|{mysqlPassword}|{lotmanagerDatabase}|{mysqlProductUserName}|{mysqlProductPassword}|{productDatabase}', true, false, now(),  now(), 'SYSTEM', 'SYSTEM')
     ,('wip-snapshot',      'WIP计算任务',      'PYTHON', '0 0 2 * * ?',  '/home/<USER>/deploy/datahub/bpms/cron/wip_snapshot.py|{ck_address}|{ck_user}|{ck_password}', ${wip_snapshot}, false, now(),  now(), 'SYSTEM', 'SYSTEM')
     ,('yarn-calculator-update',      '更新yarn calculator',      'PYTHON', '0 0 * * * ?',  '/home/<USER>/deploy/datahub/bpms/cron/yarn_calculator_update.py|{ambari_server}|{ambari_user}|{ambari_pass}', true, false, now(),  now(), 'SYSTEM', 'SYSTEM');
