USE `bpms`;

create table if not exists bz_warehousing_rule_flow
(
    `id`            bigint                                     NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `name`          varchar(256)                              NOT NULL COMMENT '流程名称',
    `customer`      varchar(128)                               NOT NULL COMMENT '客户名',
    `sub_customer`  varchar(128)                               NOT NULL COMMENT '子客户名',
    `factory`       varchar(128)                               NOT NULL COMMENT '测试工厂',
    `test_area`     varchar(64)                                NOT NULL COMMENT '测试大阶段：CP/FT/WAT',
    `file_category` varchar(64)                                NOT NULL COMMENT '文件类型，比如STDF、RAW_DATA等',
    `remarks`       varchar(1024) DEFAULT '' COMMENT '备注',
    `delete_flag`   int           DEFAULT 0                    NULL COMMENT '删除标记：0->有效,1->删除',
    `create_time`   datetime(3)   DEFAULT CURRENT_TIMESTAMP(3) NOT NULL COMMENT '创建时间',
    `update_time`   datetime(3)   DEFAULT CURRENT_TIMESTAMP(3) NULL ON UPDATE CURRENT_TIMESTAMP(3) NULL COMMENT '更新时间',
    `create_user`   varchar(128)  DEFAULT 'System'             NULL COMMENT '创建用户',
    `update_user`   varchar(128)  DEFAULT 'System'             NULL COMMENT '更新用户',
    PRIMARY KEY (`id`),
    UNIQUE KEY uk_customer_name_delete_flag (`customer`, `name`, (IF(`delete_flag` = 0, `delete_flag`, NULL))) USING BTREE,
    KEY `i_customer_delete_flag` (`customer`, `delete_flag`)
) ENGINE = InnoDB
    comment '入库规则流程表';


create table if not exists bz_schedule_task_definition
(
    id                     bigint auto_increment           primary key,
    task_name              varchar(255)                    not null comment '任务名称',
    task_description       varchar(255)                    null comment '任务描述',
    execute_engine         varchar(255)                    null comment '执行引擎: CLICKHOUSE/SPARK/PYTHON',
    cron_expression        varchar(255)                    not null comment '定时任务表达式',
    command                longtext                        null comment '执行的命令(sql为表名列表)',
    is_active              bit          default b'1'       not null comment '是否生效',
    delete_flag            bit          default b'0'       not null comment '是否删除，0：未删除，1：已删除',
    create_time            datetime(3)                     null comment '创建时间',
    update_time            datetime(3)                     null comment '更新时间',
    create_user            varchar(128) default 'System'   null comment '创建用户',
    update_user            varchar(128) default 'System'   null comment '更新用户',
    constraint task_name
        unique (task_name)
)
    comment '定时任务配置表' collate = utf8_bin;

create table if not exists bz_schedule_task_record
(
    id                     bigint auto_increment            primary key,
    task_definition_id     bigint                           not null comment 'bz_schedule_task_definition的id',
    task_name              varchar(255)                     not null comment '任务名称',
    execute_engine         varchar(255)                     null comment '执行引擎: CLICKHOUSE/SPARK/PYTHON',
    cron_expression        varchar(255)                     not null comment '定时任务表达式',
    command                longtext                         null comment '执行的命令',
    unique_id              varchar(255)                     null comment 'SPARK任务唯一标识',
    process_status         varchar(255)                     null comment '执行状态',
    schedule_time          datetime(3)                      null comment '调度任务生成时间',
    start_time             datetime(3)                      null comment '开始时间',
    end_time               datetime(3)                      null comment '结束时间',
    execute_time           bigint                           null comment '执行耗时(ms)',
    exception_type         varchar(255)                     null comment '异常类型',
    error_message          longtext                         null comment '错误信息',
    create_time            datetime(3)                      null comment '创建时间',
    update_time            datetime(3)                      null comment '更新时间',
    create_user            varchar(128) default 'System'    null comment '创建用户',
    update_user            varchar(128) default 'System'    null comment '更新用户',
    INDEX idx_task_definition_id (task_definition_id),
    INDEX idx_unique_id_process_status (process_status,unique_id),
    INDEX idx_process_status_execute_engine_id (process_status, execute_engine)
)
    comment '定时任务执行记录表' collate = utf8_bin;

create table if not exists bz_data_repair_info
(
    id                          bigint auto_increment
    primary key,
    customer                    varchar(128)                              null comment '客户',
    sub_customer                varchar(128)                              null comment '子客户',
    factory                     varchar(128)                              null comment 'factory',
    factory_site                varchar(128)                              null comment 'factory_site',
    repair_batch_id             bigint                                    null comment '处理批次',
    file_id                     bigint                                    null comment 'fileId',
    file_name                   varchar(1024)                             not null comment '文件名称',
    wafer_id                    varchar(1024)                             null comment 'wafer_id',
    test_area                   varchar(128)                              null comment '测试阶段，CP/FT',
    repair_json                 text                                      null comment '修改内容json',
    original_device_id          varchar(128)                              null comment '原始的device_id',
    original_test_stage         varchar(128)                              null comment '原始的test stage',
    original_lot_id             varchar(128)                              null comment '原始的lot_id',
    original_wafer_id           varchar(1024)                             null comment '原始的wafer_id',
    original_wafer_no           varchar(128)                              null comment '原始的wafer_no',
    original_sblot_id           varchar(128)                              null comment '原始的sblot_id',
    original_test_program       varchar(512)                              null comment '原始的test_program',
    original_lot_type           varchar(128)                              null comment '原始的lot_type',
    original_retest_hbin_number varchar(128)                              null comment '原始的retest_hbin_number',
    original_flow_id            varchar(128)                              null comment '原始的flow_id',
    modify_device_id            varchar(128)                              null comment '修改的device_id',
    modify_test_stage           varchar(128)                              null comment '修改的test stage',
    modify_lot_id               varchar(128)                              null comment '修改的lot_id',
    modify_wafer_no             varchar(128)                              null comment '修改的wafer_no',
    modify_sblot_id             varchar(128)                              null comment '修改的sblot_id',
    modify_test_program         varchar(512)                              null comment '修改的test_program',
    modify_lot_type             varchar(128)                              null comment '修改的lot_type',
    modify_retest_hbin_number   varchar(128)                              null comment '修改的retest_hbin_number',
    modify_flow_id              varchar(128)                              null comment '修改的flow_id',
    repair_category             varchar(128)                              null comment '修改类别 Data Accuracy,Data Completion',
    last_repair_type            varchar(128)                              null comment '修改类型 准确性 默认为关键字段调整',
    repair_status               varchar(128)                              null comment '修改状态 Success,Fail,Process',
    repair_error_info           text                                      null comment '修改异常信息',
    repair_times                int          default 0                    not null comment '修改次数',
    start_time                  datetime(3)                               null comment '开始时间',
    end_time                    datetime(3)                               null comment '结束时间',
    remark                      varchar(128)                              null comment 'remark',
    delete_flag                 tinyint(1)   default 0                    not null comment '删除标志,0:未删除，1:已删除',
    create_time                 datetime(3)  default CURRENT_TIMESTAMP(3) null comment '创建时间',
    update_time                 datetime(3)  default CURRENT_TIMESTAMP(3) null on update CURRENT_TIMESTAMP(3) comment '更新时间',
    create_user                 varchar(128) default 'System'             null comment '创建用户',
    update_user                 varchar(128) default 'System'             null comment '更新用户'
    ) ENGINE = InnoDB
    comment '数据修复表';

create table if not exists bz_data_repair_history
(
    id                          bigint auto_increment
    primary key,
    customer                    varchar(128)                              null comment '客户',
    sub_customer                varchar(128)                              null comment '子客户',
    factory                     varchar(128)                              null comment 'factory',
    factory_site                varchar(128)                              null comment 'factory_site',
    repair_batch_id             bigint                                    null comment '处理批次',
    data_repair_id              bigint                                    null comment 'data_repair_id',
    file_id                     bigint                                    null comment 'fileId',
    file_name                   varchar(1024)                             not null comment '文件名称',
    wafer_id                    varchar(1024)                             null comment 'wafer_id',
    test_area                   varchar(128)                              null comment '测试阶段，CP/FT',
    repair_category             varchar(128)                              null comment '修改类别 DataAccuracy,DataCompletion',
    last_repair_type            varchar(128)                              null comment '修改类型 准确性 默认为关键字段调整',
    repair_status               varchar(128)                              null comment '修改状态 Success,Fail,Process',
    repair_error_info           text                                      null comment '修改异常信息',
    original_device_id          varchar(128)                              null comment '原始的device_id',
    original_test_stage         varchar(128)                              null comment '原始的test stage',
    original_lot_id             varchar(128)                              null comment '原始的lot_id',
    original_wafer_no           varchar(128)                              null comment '原始的wafer_no',
    original_wafer_id           varchar(1024)                             null comment '原始的wafer_id',
    original_sblot_id           varchar(128)                              null comment '原始的sblot_id',
    original_test_program       varchar(512)                              null comment '原始的test_program',
    original_lot_type           varchar(128)                              null comment '原始的lot_type',
    original_retest_hbin_number varchar(128)                              null comment '原始的retest_hbin_number',
    original_flow_id            varchar(128)                              null comment '原始的flow_id',
    modify_device_id            varchar(128)                              null comment '修改的device_id',
    modify_test_stage           varchar(128)                              null comment '修改的test stage',
    modify_lot_id               varchar(128)                              null comment '修改的lot_id',
    modify_wafer_no             varchar(128)                              null comment '修改的wafer_no',
    modify_sblot_id             varchar(128)                              null comment '修改的sblot_id',
    modify_test_program         varchar(512)                              null comment '修改的test_program',
    modify_lot_type             varchar(128)                              null comment '修改的lot_type',
    modify_retest_hbin_number   varchar(128)                              null comment '修改的retest_hbin_number',
    modify_flow_id              varchar(128)                              null comment '修改的flow_id',
    repair_json                 text                                      null comment '修改内容json',
    repair_times                int          default 0                    not null comment '当前修改次数',
    delete_flag                 tinyint(1)   default 0                    not null comment '删除标志,0:未删除，1:已删除',
    create_time                 datetime(3)  default CURRENT_TIMESTAMP(3) null comment '创建时间',
    update_time                 datetime(3)  default CURRENT_TIMESTAMP(3) null on update CURRENT_TIMESTAMP(3) comment '更新时间',
    create_user                 varchar(128) default 'System'             null comment '创建用户',
    update_user                 varchar(128) default 'System'             null comment '更新用户'
    ) ENGINE = InnoDB
    comment '数据修复历史表';

create table if not exists bz_data_repair_effect_batch
(
    id                bigint auto_increment
    primary key,
    customer          varchar(128)                              null comment '客户',
    sub_customer      varchar(128)                              null comment '子客户',
    factory           varchar(128)                              null comment 'factory',
    factory_site      varchar(128)                              null comment 'factory_site',
    repair_batch_id   bigint                                    null comment '处理批次',
    repair_status     varchar(128)                              null comment '修改状态 Success,Fail,Process',
    device_id         varchar(128)                              null comment 'device_id',
    lot_id            varchar(128)                              null comment 'lot_id',
    test_stage        varchar(128)                              null comment 'test stage',
    test_program      varchar(512)                              null comment 'test_program',
    test_area         varchar(128)                              null comment '测试阶段，CP/FT',
    sblot_id          varchar(128)                              null comment 'sblot_id',
    wafer_id          varchar(1024)                             null comment 'wafer_id',
    wafer_no          varchar(128)                              null comment 'wafer_no',
    repair_no         int          default 0                    not null comment '批次累积受影响次数',
    file_count_before bigint                                    null comment '批次变更前文件个数',
    file_list_before  text                                      null comment '批次变更前文件名列表',
    file_count_after  bigint                                    null comment '批次变更后文件个数',
    file_list_after   text                                      null comment '批次变更后文件名列表',
    repair_files      text                                      null comment '变更文件名',
    repair_info_ids   varchar(128)                              null comment '文件变更id',
    delete_flag       tinyint(1)   default 0                    not null comment '删除标志,0:未删除，1:已删除',
    create_time       datetime(3)  default CURRENT_TIMESTAMP(3) null comment '创建时间',
    update_time       datetime(3)  default CURRENT_TIMESTAMP(3) null on update CURRENT_TIMESTAMP(3) comment '更新时间',
    create_user       varchar(128) default 'System'             null comment '创建用户',
    update_user       varchar(128) default 'System'             null comment '更新用户'
    ) ENGINE = InnoDB
    comment '数据修复影响批次表';

create table if not exists bz_data_repair_batch_info
(
    id           bigint auto_increment
    primary key,
    customer     varchar(128)                              null comment '客户',
    sub_customer varchar(128)                              null comment '子客户',
    factory      varchar(128)                              null comment 'factory',
    factory_site varchar(128)                              null comment 'factory_site',
    device_id    varchar(128)                              null comment 'device_id',
    lot_id       varchar(128)                              null comment 'lot_id',
    test_stage   varchar(128)                              null comment 'test stage',
    test_area    varchar(128)                              null comment '测试阶段，CP/FT',
    sblot_id     varchar(128)                              null comment 'sblot_id',
    wafer_id     varchar(1024)                             null comment 'wafer_id',
    delete_flag  tinyint(1)   default 0                    not null comment '删除标志,0:未删除，1:已删除',
    create_time  datetime(3)  default CURRENT_TIMESTAMP(3) null comment '创建时间',
    update_time  datetime(3)  default CURRENT_TIMESTAMP(3) null on update CURRENT_TIMESTAMP(3) comment '更新时间',
    create_user  varchar(128) default 'System'             null comment '创建用户',
    update_user  varchar(128) default 'System'             null comment '更新用户'
    ) ENGINE = InnoDB
    comment '数据修复批次表';



create table if not exists bz_data_integrity_delete_log
(
    id           bigint auto_increment
    primary key,
    customer     varchar(128)                              null comment '客户',
    sub_customer varchar(128)                              null comment '子客户',
    file_id      bigint                                    null comment 'fileId',
    file_name    varchar(1024)                             not null comment 'fileName',
    remark       varchar(1024)                             null comment '备注',
    delete_flag  integer   default 0                    not null comment '删除标志,0:未删除，1:已删除',
    create_time  datetime(3)  default CURRENT_TIMESTAMP(3) null comment '创建时间',
    update_time  datetime(3)  default CURRENT_TIMESTAMP(3) null on update CURRENT_TIMESTAMP(3) comment '更新时间',
    create_user  varchar(128) default 'System'             null comment '创建用户',
    update_user  varchar(128) default 'System'             null comment '更新用户'
    ) ENGINE = InnoDB
    COMMENT '数据完整性删除日志表';

USE `bpms`;
create or replace view gubo_system_metrics_view as select * from `useradmin`.gubo_system_metrics;
