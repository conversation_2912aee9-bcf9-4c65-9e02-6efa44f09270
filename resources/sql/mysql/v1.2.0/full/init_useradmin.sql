use `useradmin`;

-- menu_type: 菜单类型（M目录 C菜单 F按钮）
-- is_frame: 是否为外链（0是 1否）
-- status: 菜单状态（0正常 1停用）

-- bpms id范围：180000 - 189999
-- 一级菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
(180000, '大数据平台管理', NULL, 0, 80, 'bpms_180000', NULL, 1, 0, 'M', '0', '0', '', 'xitong', 'root');


SELECT @parentId180000 := 180000;
-- 二级菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
(180100, '数据重播', NULL, @parentId180000, 20, '${ACCESS_ADDR}/bpms/#/data-replay', 'data-replay', 0, 0, 'C', '0', '0', '', '#', 'root');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180200, '计算任务管理', NULL, @parentId180000, 40, '${ACCESS_ADDR}/bpms/#/task', 'task', 0, 0, 'C', '0', '0', '', '#', 'root');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180300, '数据质量', NULL, @parentId180000, 60, '${ACCESS_ADDR}/bpms/#/data-quality', 'data-quality', 0, 0, 'C', '0', '0', '', '#', 'root');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180400, '元数据管理', NULL, @parentId180000, 80, '${ACCESS_ADDR}/bpms/#/metadata', 'metadata', 0, 0, 'C', '1', '0', '', '#', 'root');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `short_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`) VALUES
    (180500, '清洗规则预览', NULL, @parentId180000, 100, '${ACCESS_ADDR}/bpms/#/clear-rule-preview', 'clear-rule-preview', 0, 0, 'C', '0', '0', '', '#', 'root');
