[database]
host=mysql01.qa.guwave.com
port=3306
user=root
password=root@mysql@Guwave

[mail]
smtp_server=smtp.office365.com
smtp_port=587
email_username=<EMAIL>
email_password=l4hyoqxu3ov@SmeA
mail_recipient=<EMAIL>,<EMAIL>

[rule]
2200_max_task=200
3100_max_task=200
4100_max_task=200
5100_max_task=200
7100_max_task=200
task_timeout_seconds=1800

[application]
devops_deploy_user=devops
devops_deploy_password=devops@guwave
glory_deploy_user=glory
glory_deploy_password=glory@guwave
dataware_collectx_deploy_all=devops@app01:2,devops@mpp01:1
dataware_source_agent_cp_deploy_all=devops@app01:1
dataware_source_agent_ft_deploy_all=devops@app01:1
dataware_source_agent_wat_deploy_all=devops@app01:1
dataware_source_agent_manual_deploy_all=devops@app01:1
dataware_repair_engine_deploy_all=devops@app01:1
dataware_quality_deploy=devops@app01
bpms_app_deploy=devops@app01
dataware_scheduler_deploy=glory@gdp01
linkx_scheduler_deploy=glory@gdp01
next_compute_scheduler_deploy=glory@gdp01

[gdp]
gdp_ambari_host=gdp01.guwave.com
gdp_ambari_port=8080
gdp_ambari_user=admin
gdp_ambari_password=admin

[clickhouse]
ck_hosts=mpp01.guwave.com
ck_host_password=devops@guwave
ck_port=9000
ck_username=admin
ck_password=admin@ck@Guwave
ck_replica_port=9200
ck_replica_flag=true
ck_max_mutations=500
ck_max_replication_queue_cnt=20000