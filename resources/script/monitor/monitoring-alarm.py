import configparser
import logging
import os
import smtplib
import subprocess
from datetime import datetime
from email.mime.multipart import MIME<PERSON>ultipart
from email.mime.text import MIMEText
from typing import Dict, Any

import paramiko
import pymysql
import requests
from requests.auth import HTTPBasicAuth

# 全局配置
# 配置日志
logger = logging.getLogger('monitoring-alarm')
logger.setLevel(logging.DEBUG)
file_handler = logging.FileHandler(os.path.join(os.path.dirname(__file__), 'monitoring-alarm.log'))
file_handler.setLevel(logging.DEBUG)
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)
console_handler.setFormatter(formatter)
logger.addHandler(file_handler)
logger.addHandler(console_handler)

# 读取配置文件
config = configparser.ConfigParser()
config.read(os.path.join(os.path.dirname(__file__), 'monitoring-alarm.properties'))

# 数据库连接配置
db_config = {
    'host': config.get('database', 'host'),
    'port': config.getint('database', 'port'),
    'user': config.get('database', 'user'),
    'password': config.get('database', 'password')
}

# 邮件配置
mail_config = {
    'smtp_server': config.get('mail', 'smtp_server'),
    'smtp_port': config.getint('mail', 'smtp_port'),
    'username': config.get('mail', 'email_username'),
    'password': config.get('mail', 'email_password'),
    'recipient': config.get('mail', 'mail_recipient')
}


# MySQL连接函数
def get_db_connection():
    try:
        connection = pymysql.connect(**db_config)
        logger.info("数据库连接成功")
        return connection
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None


# 发送邮件函数
def send_email(subject, body, is_html=False):
    try:
        msg = MIMEMultipart()
        msg['From'] = mail_config['username']
        # 修改: 支持多个邮件接收人
        recipients = [addr.strip() for addr in mail_config['recipient'].split(',')]
        msg['To'] = ', '.join(recipients)
        msg['Subject'] = subject

        if is_html:
            msg.attach(MIMEText(body, 'html', 'utf-8'))
        else:
            msg.attach(MIMEText(body, 'plain', 'utf-8'))

        server = smtplib.SMTP(mail_config['smtp_server'], mail_config['smtp_port'])
        server.starttls()
        server.login(mail_config['username'], mail_config['password'])
        # 修改: 向多个接收人发送邮件
        server.send_message(msg, to_addrs=recipients)
        server.quit()
        logger.info("邮件发送成功")
    except Exception as e:
        logger.error(f"邮件发送失败: {e}")


# 监控数据量类
class DataVolumeMonitor:
    def __init__(self, db_connection):
        self.db_connection = db_connection

    def monitor(self):
        # 这里实现具体的监控逻辑
        logger.info("开始监控数据量")

        try:
            cursor = self.db_connection.cursor()

            # 查询2200阶段create任务数量
            sql_2200 = "select count(1) from onedata.dw_sftp_file_detail where convert_flag = 1 and process_status = 'CREATE' and batch_status = 'SUCCESS'"
            cursor.execute(sql_2200)
            count_2200 = cursor.fetchone()[0]

            # 查询3100阶段create任务数量
            sql_3100 = "select count(1) from onedata.dw_sftp_file_detail where convert_flag = 0 and process_status = 'CREATE' and batch_status = 'SUCCESS'"
            cursor.execute(sql_3100)
            count_3100 = cursor.fetchone()[0]

            # 查询4100阶段create任务数量
            sql_4100 = "select count(1) from onedata.dw_lot_meta_data_detail where process_status = 'CREATE'"
            cursor.execute(sql_4100)
            count_4100 = cursor.fetchone()[0]

            # 查询5100阶段create任务数量
            sql_5100 = "select count(1) from onedata.dw_layer_calculate_pool where process_status = 'CREATE' and dw_layer = 'DWS'"
            cursor.execute(sql_5100)
            count_5100 = cursor.fetchone()[0]

            # 查询7100阶段create任务数量
            sql_7100 = "select count(1) from linkx.bz_app_instance where process_status = 'CREATE' and opt_type = 'NEW_TEST_ITEM_DATA'"
            cursor.execute(sql_7100)
            count_7100 = cursor.fetchone()[0]

            # 新增：查询2200阶段最后一个成功任务的update_time和当天成功任务数
            sql_2200_last_update = "SELECT MAX(update_time) FROM onedata.dw_sftp_file_detail WHERE convert_flag = 1 AND batch_status = 'SUCCESS'"
            cursor.execute(sql_2200_last_update)
            last_update_2200 = cursor.fetchone()[0]

            sql_2200_today_count = "SELECT COUNT(1) FROM onedata.dw_sftp_file_detail WHERE convert_flag = 1 AND batch_status = 'SUCCESS' AND update_time >= CURDATE()"
            cursor.execute(sql_2200_today_count)
            today_count_2200 = cursor.fetchone()[0]

            # 新增：查询3100阶段最后一个成功任务的update_time和当天成功任务数
            sql_3100_last_update = "SELECT MAX(update_time) FROM onedata.dw_sftp_file_detail WHERE convert_flag = 0 AND batch_status = 'SUCCESS'"
            cursor.execute(sql_3100_last_update)
            last_update_3100 = cursor.fetchone()[0]

            sql_3100_today_count = "SELECT COUNT(1) FROM onedata.dw_sftp_file_detail WHERE convert_flag = 0 AND batch_status = 'SUCCESS' AND update_time >= CURDATE()"
            cursor.execute(sql_3100_today_count)
            today_count_3100 = cursor.fetchone()[0]

            # 新增：查询4100阶段最后一个成功任务的update_time和当天成功任务数
            sql_4100_last_update = "SELECT MAX(update_time) FROM onedata.dw_lot_meta_data_detail WHERE process_status = 'SUCCESS'"
            cursor.execute(sql_4100_last_update)
            last_update_4100 = cursor.fetchone()[0]

            sql_4100_today_count = "SELECT COUNT(1) FROM onedata.dw_lot_meta_data_detail WHERE process_status = 'SUCCESS' AND update_time >= CURDATE()"
            cursor.execute(sql_4100_today_count)
            today_count_4100 = cursor.fetchone()[0]

            # 新增：查询5100阶段最后一个成功任务的update_time和当天成功任务数
            sql_5100_last_update = "SELECT MAX(update_time) FROM onedata.dw_layer_calculate_pool WHERE process_status = 'SUCCESS' AND dw_layer = 'DWS'"
            cursor.execute(sql_5100_last_update)
            last_update_5100 = cursor.fetchone()[0]

            sql_5100_today_count = "SELECT COUNT(1) FROM onedata.dw_layer_calculate_pool WHERE process_status = 'SUCCESS' AND dw_layer = 'DWS' AND update_time >= CURDATE()"
            cursor.execute(sql_5100_today_count)
            today_count_5100 = cursor.fetchone()[0]

            # 新增：查询7100阶段最后一个成功任务的update_time和当天成功任务数
            sql_7100_last_update = "SELECT MAX(update_time) FROM linkx.bz_app_instance WHERE opt_type = 'NEW_TEST_ITEM_DATA' AND process_status = 'SUCCESS'"
            cursor.execute(sql_7100_last_update)
            last_update_7100 = cursor.fetchone()[0]

            sql_7100_today_count = "SELECT COUNT(1) FROM linkx.bz_app_instance WHERE opt_type = 'NEW_TEST_ITEM_DATA' AND process_status = 'SUCCESS' AND update_time >= CURDATE()"
            cursor.execute(sql_7100_today_count)
            today_count_7100 = cursor.fetchone()[0]

            cursor.close()

            # 初始化各阶段状态
            status_2200 = "success"
            status_3100 = "success"
            status_4100 = "success"
            status_5100 = "success"
            status_7100 = "success"
            task_timeout_seconds = int(config.getint('rule', 'task_timeout_seconds'))
            # 执行任务数校验和最后更新时间检查，并设置各阶段状态
            max_2200 = config.getint('rule', '2200_max_task')
            if int(count_2200) > max_2200 or (
                    last_update_2200 and (datetime.now() - last_update_2200).total_seconds() > task_timeout_seconds):
                status_2200 = "error"

            max_3100 = config.getint('rule', '3100_max_task')
            if int(count_3100) > max_3100 or (
                    last_update_3100 and (datetime.now() - last_update_3100).total_seconds() > task_timeout_seconds):
                status_3100 = "error"

            max_4100 = config.getint('rule', '4100_max_task')
            if int(count_4100) > max_4100 or (
                    last_update_4100 and (datetime.now() - last_update_4100).total_seconds() > task_timeout_seconds):
                status_4100 = "error"

            max_5100 = config.getint('rule', '5100_max_task')
            if int(count_5100) > max_5100 or (
                    last_update_5100 and (datetime.now() - last_update_5100).total_seconds() > task_timeout_seconds):
                status_5100 = "error"

            max_7100 = config.getint('rule', '7100_max_task')
            if int(count_7100) > max_7100 or (
                    last_update_7100 and (datetime.now() - last_update_7100).total_seconds() > task_timeout_seconds):
                status_7100 = "error"

            # 设置总体状态：如果有任一阶段为error则为error，否则为success
            overall_status = "error" if (status_2200 == "error" or status_3100 == "error" or
                                         status_4100 == "error" or status_5100 == "error" or
                                         status_7100 == "error") else "success"

            # 将结果制成字典
            result = {
                "2200": {
                    "count": str(count_2200),
                    "last_update": last_update_2200.strftime('%Y-%m-%d %H:%M:%S') if last_update_2200 else None,
                    "today_count": str(today_count_2200),
                    "status": status_2200
                },
                "3100": {
                    "count": str(count_3100),
                    "last_update": last_update_3100.strftime('%Y-%m-%d %H:%M:%S') if last_update_3100 else None,
                    "today_count": str(today_count_3100),
                    "status": status_3100
                },
                "4100": {
                    "count": str(count_4100),
                    "last_update": last_update_4100.strftime('%Y-%m-%d %H:%M:%S') if last_update_4100 else None,
                    "today_count": str(today_count_4100),
                    "status": status_4100
                },
                "5100": {
                    "count": str(count_5100),
                    "last_update": last_update_5100.strftime('%Y-%m-%d %H:%M:%S') if last_update_5100 else None,
                    "today_count": str(today_count_5100),
                    "status": status_5100
                },
                "7100": {
                    "count": str(count_7100),
                    "last_update": last_update_7100.strftime('%Y-%m-%d %H:%M:%S') if last_update_7100 else None,
                    "today_count": str(today_count_7100),
                    "status": status_7100
                },
                "status": overall_status
            }

            return {"status": overall_status, "message": "数据量监控完成", "data": result}

        except Exception as e:
            logger.error(f"数据量监控失败: {e}")
            return {"status": "error", "message": f"数据量监控失败: {e}", "data": {}}

# 监控中间件类
class MiddlewareMonitor:
    def __init__(self, db_connection):
        new_cfg = {}
        for sec in ('gdp', 'clickhouse'):
            if config.has_section(sec):
                new_cfg.update(config.items(sec))
        self.config = new_cfg
        self.db_connection = db_connection

    def _execute_ssh_command(self, host: str, password: str, command: str) -> str:
        """执行SSH命令"""
        try:
            ssh_cmd = f"sshpass -p {password} ssh -o StrictHostKeyChecking=no {host} '{command}'"
            result = subprocess.run(ssh_cmd, shell=True, capture_output=True, text=True, timeout=30)
            return result.stdout.strip()
        except subprocess.TimeoutExpired:
            return ""
        except Exception:
            return ""

    def check_gdp_components(self) -> Dict[str, Any]:
        """检查GDP组件"""
        results = {}

        # 构建服务URL
        base_url = f"http://{self.config['gdp_ambari_host']}:{self.config['gdp_ambari_port']}/api/v1/clusters/gdp/services"
        service_urls = {
            'NAMENODE': f"{base_url}/HDFS/components/NAMENODE",
            'DATANODE': f"{base_url}/HDFS/components/DATANODE",
            'RESOURCEMANAGER': f"{base_url}/YARN/components/RESOURCEMANAGER",
            'NODEMANAGER': f"{base_url}/YARN/components/NODEMANAGER",
            'ZOOKEEPER_SERVER': f"{base_url}/ZOOKEEPER/components/ZOOKEEPER_SERVER",
            'KAFKA_BROKER': f"{base_url}/KAFKA/components/KAFKA_BROKER"
        }

        auth = HTTPBasicAuth(self.config['gdp_ambari_user'], self.config['gdp_ambari_password'])

        # 用于汇总每个组件类型的状态
        component_summary = {}

        for component_type, service_url in service_urls.items():
            component_summary[component_type] = {
                'normal_hosts': [],
                'error_hosts': [],
                'has_error': False
            }

            try:
                response = requests.get(service_url, auth=auth, timeout=30)
                if response.status_code == 200:
                    data = response.json()
                    host_components = data.get('host_components', [])

                    for host_component in host_components:
                        href = host_component.get('href', '')
                        if href:
                            try:
                                host_response = requests.get(href, auth=auth, timeout=30)
                                if host_response.status_code == 200:
                                    host_data = host_response.json()
                                    host_roles = host_data.get('HostRoles', {})
                                    component_name = host_roles.get('component_name', '')
                                    component_host = host_roles.get('public_host_name', '')
                                    component_state = host_roles.get('state', '')

                                    if component_state == 'STARTED':
                                        component_summary[component_type]['normal_hosts'].append(component_host)
                                    else:
                                        component_summary[component_type]['error_hosts'].append(component_host)
                                        component_summary[component_type]['has_error'] = True
                            except Exception as e:
                                component_summary[component_type]['has_error'] = True
                                component_summary[component_type]['error_hosts'].append(f"检查主机组件时出错: {str(e)}")
                else:
                    component_summary[component_type]['has_error'] = True
                    component_summary[component_type]['error_hosts'].append(f"无法获取信息, HTTP状态码: {response.status_code}")
            except Exception as e:
                component_summary[component_type]['has_error'] = True
                component_summary[component_type]['error_hosts'].append(f"无法调用API")

        # 根据汇总结果生成最终返回值
        for component_type, summary in component_summary.items():
            if summary['has_error'] or summary['error_hosts']:
                # 有错误的情况，返回出错的主机IP
                error_hosts = [host for host in summary['error_hosts'] if host]
                if error_hosts:
                    error_host_str = ', '.join(error_hosts)
                    results[component_type] = {
                        'status': 'error',
                        'message': f'{error_host_str} {component_type}异常'
                    }
                else:
                    results[component_type] = {
                        'status': 'error',
                        'message': f'{component_type}状态异常，请立即处理'
                    }
            else:
                # 所有主机都正常
                results[component_type] = {
                    'status': 'success',
                    'message': ''
                }

        return results

    def check_clickhouse_mutation(self) -> Dict[str, Any]:
        """检查ClickHouse mutation"""
        results = {}
        mutation_threshold = int(self.config['ck_max_mutations'])  # 设定阈值

        # 检查ClickHouse
        ck_sql = "select count(1) from system.mutations where is_done = 0;"
        ck_hosts = self.config.get('ck_hosts', '').split(',')

        # 用于汇总所有hosts的mutation状态
        mutation_summary = {
            'normal_instances': [],
            'error_instances': [],
            'has_error': False,
            'query_failed_instances': []
        }

        # 检查主端口
        for ck_host in ck_hosts:
            ck_host = ck_host.strip()
            if ck_host:
                ck_command = f"clickhouse-client -h127.0.0.1 --port {self.config['ck_port']} -u{self.config['ck_username']} --password {self.config['ck_password']} -q \"{ck_sql}\""
                ck_result = self._execute_ssh_command(ck_host, self.config['ck_host_password'], ck_command)

                instance_key = f"{ck_host}({self.config['ck_port']})"
                if not ck_result:
                    mutation_summary['query_failed_instances'].append(instance_key)
                    mutation_summary['has_error'] = True
                else:
                    try:
                        mutation_count = int(ck_result)
                        if mutation_count > mutation_threshold:
                            # 修改: 在错误信息中包含具体的端口和mutation数量
                            mutation_summary['error_instances'].append(f"{instance_key}:{mutation_count}")
                            mutation_summary['has_error'] = True
                        else:
                            mutation_summary['normal_instances'].append(instance_key)
                    except ValueError:
                        mutation_summary['query_failed_instances'].append(instance_key)
                        mutation_summary['has_error'] = True

        # 通过ck_replica_flag参数控制是否检查replica端口
        if (self.config.get('ck_replica_flag', 'false').lower() == 'true' and
                'ck_replica_port' in self.config and self.config['ck_replica_port']):
            for ck_host in ck_hosts:
                ck_host = ck_host.strip()
                if ck_host:
                    ck_command = f"clickhouse-client -h127.0.0.1 --port {self.config['ck_replica_port']} -u{self.config['ck_username']} --password {self.config['ck_password']} -q \"{ck_sql}\""
                    ck_result = self._execute_ssh_command(ck_host, self.config['ck_host_password'], ck_command)

                    instance_key = f"{ck_host}({self.config['ck_replica_port']})"
                    if not ck_result:
                        mutation_summary['query_failed_instances'].append(instance_key)
                        mutation_summary['has_error'] = True
                    else:
                        try:
                            mutation_count = int(ck_result)
                            if mutation_count > mutation_threshold:
                                # 修改: 在错误信息中包含具体的端口和mutation数量
                                mutation_summary['error_instances'].append(f"{instance_key}:{mutation_count}")
                                mutation_summary['has_error'] = True
                            else:
                                mutation_summary['normal_instances'].append(instance_key)
                        except ValueError:
                            mutation_summary['query_failed_instances'].append(ck_host)
                            mutation_summary['has_error'] = True

        # 根据汇总结果生成最终返回值
        if mutation_summary['has_error']:
            # 有错误的情况
            error_messages = []
            if mutation_summary['error_instances']:
                error_instances_str = ', '.join(mutation_summary['error_instances'])
                error_messages.append(f'{error_instances_str}')
            if mutation_summary['query_failed_instances']:
                failed_instances_str = ', '.join(mutation_summary['query_failed_instances'])
                error_messages.append(f'{failed_instances_str}查询失败')

            results['ck mutation数量'] = {
                'status': 'error',
                'message': ', '.join(error_messages)
            }
        else:
            # 所有instances都正常
            results['ck mutation数量'] = {
                'status': 'success',
                'message': ''
            }

        return results

    def check_clickhouse_replication_queue(self) -> Dict[str, Any]:
        """检查ClickHouse replication_queue"""
        results = {}
        replication_threshold = int(self.config['ck_max_replication_queue_cnt'])  # 设定阈值

        # 检查replication_queue的SQL
        replication_sql = "select count(1) from system.replication_queue;"
        ck_hosts = self.config.get('ck_hosts', '').split(',')

        # 用于汇总所有hosts和端口的replication_queue状态
        replication_summary = {
            'normal_instances': [],
            'error_instances': [],
            'has_error': False,
            'query_failed_instances': []
        }

        # 检查主端口
        for ck_host in ck_hosts:
            ck_host = ck_host.strip()
            if ck_host:
                ck_command = f"clickhouse-client -h127.0.0.1 --port {self.config['ck_port']} -u{self.config['ck_username']} --password {self.config['ck_password']} -q \"{replication_sql}\""
                ck_result = self._execute_ssh_command(ck_host, self.config['ck_host_password'], ck_command)

                instance_key = f"{ck_host}({self.config['ck_port']})"
                if not ck_result:
                    replication_summary['query_failed_instances'].append(instance_key)
                    replication_summary['has_error'] = True
                else:
                    try:
                        replication_count = int(ck_result)
                        if replication_count > replication_threshold:
                            # 修改: 在错误信息中包含具体的replication_queue数量
                            replication_summary['error_instances'].append(f"{instance_key}:{replication_count}")
                            replication_summary['has_error'] = True
                        else:
                            replication_summary['normal_instances'].append(instance_key)
                    except ValueError:
                        replication_summary['query_failed_instances'].append(instance_key)
                        replication_summary['has_error'] = True

        # 通过ck_replica_flag参数控制是否检查replica端口
        if (self.config.get('ck_replica_flag', 'false').lower() == 'true' and
                'ck_replica_port' in self.config and self.config['ck_replica_port']):
            for ck_host in ck_hosts:
                ck_host = ck_host.strip()
                if ck_host:
                    ck_command = f"clickhouse-client -h127.0.0.1 --port {self.config['ck_replica_port']} -u{self.config['ck_username']} --password {self.config['ck_password']} -q \"{replication_sql}\""
                    ck_result = self._execute_ssh_command(ck_host, self.config['ck_host_password'], ck_command)

                    instance_key = f"{ck_host}({self.config['ck_replica_port']})"
                    if not ck_result:
                        replication_summary['query_failed_instances'].append(instance_key)
                        replication_summary['has_error'] = True
                    else:
                        try:
                            replication_count = int(ck_result)
                            if replication_count > replication_threshold:
                                # 修改: 在错误信息中包含具体的replication_queue数量
                                replication_summary['error_instances'].append(f"{instance_key}:{replication_count}")
                                replication_summary['has_error'] = True
                            else:
                                replication_summary['normal_instances'].append(instance_key)
                        except ValueError:
                            replication_summary['query_failed_instances'].append(instance_key)
                            replication_summary['has_error'] = True

        # 根据汇总结果生成最终返回值
        if replication_summary['has_error']:
            # 有错误的情况
            error_messages = []
            if replication_summary['error_instances']:
                error_instances_str = ', '.join(replication_summary['error_instances'])
                error_messages.append(f'{error_instances_str}')
            if replication_summary['query_failed_instances']:
                failed_instances_str = ', '.join(replication_summary['query_failed_instances'])
                error_messages.append(f'{failed_instances_str}查询失败')

            results['ck replication_queue数量'] = {
                'status': 'error',
                'message': ', '.join(error_messages)
            }
        else:
            # 所有instances都正常
            results['ck replication_queue数量'] = {
                'status': 'success',
                'message': ''
            }

        return results

    def monitor(self):
        """运行所有检查"""
        all_results = {}

        logger.info("开始检查GDP组件...")
        all_results.update(self.check_gdp_components())

        logger.info("检查ClickHouse mutation...")
        all_results.update(self.check_clickhouse_mutation())

        logger.info("检查ClickHouse replication_queue...")
        all_results.update(self.check_clickhouse_replication_queue())

        return all_results

# 监控应用类
class ApplicationMonitor:
    def __init__(self, db_connection):
        self.db_connection = db_connection

    def monitor(self):
        logger.info("开始监控应用")
        app_results = {}  # 改为存储每个应用的详细结果

        # 获取所有需要监控的应用配置
        app_configs = {}
        for key in config.options('application'):
            if key.endswith('_deploy_all') or key.endswith('_deploy'):
                app_name = key.replace('_deploy_all', '')
                app_name = app_name.replace('_deploy', '')
                app_configs[app_name] = config.get('application', key)

        # 监控每个应用
        for app_name, deploy_info in app_configs.items():
            try:
                app_name = app_name.replace('_', '-')
                app_alerts, app_count, machine_details = self._monitor_app(app_name, deploy_info)
                # 将每个应用的结果单独存储
                app_results[app_name] = {
                    "alerts": app_alerts,
                    "count": app_count,
                    "machine_details": machine_details,  # 机器详细信息
                    "status": "error" if app_alerts else "success"
                }
            except Exception as e:
                logger.error(f"监控应用 {app_name} 时出错: {e}")
                app_results[app_name] = {
                    "alerts": [f"监控应用 {app_name} 时发生错误: {e}"],
                    "count": 0,
                    "machine_details": {},
                    "status": "error"
                }

        # 确定整体状态
        overall_status = "error" if any(app["status"] == "error" for app in app_results.values()) else "success"

        # 返回包含status和各应用详细信息的结果
        return {
            "status": overall_status,
            "message": "应用监控完成",
            "data": app_results  # 返回各应用的详细信息
        }

    def _monitor_app(self, app_name, deploy_info):
        """
        监控单个应用
        :param app_name: 应用名称
        :param deploy_info: 部署信息
        :return: 告警列表和应用总数量
        """
        alerts = []
        total_count = 0  # 用于统计该应用的总容器数
        machine_details = {}  # 存储每台机器的详细信息

        # 解析部署信息
        deployments = deploy_info.split(',')
        for deployment in deployments:
            # 解析 user@host:count 格式或者 user@host 格式
            if ':' in deployment:
                user_host, expected_count = deployment.split(':')
                expected_count = int(expected_count)
            else:
                user_host = deployment
                expected_count = 1  # 默认为1个实例

            user, host = user_host.split('@')

            # 获取用户对应的密码
            password = config.get('application', f'{user}_deploy_password')

            # 检查容器数量
            actual_count = self._check_docker_containers(host, user, password, app_name)
            total_count += actual_count  # 累加到总数量

            hanging_count = expected_count - actual_count if expected_count > actual_count else 0

            # 检查日志中的内存错误
            deadlock_count = 0
            if actual_count > 0:
                log_errors = self._check_app_logs(host, user, password, app_name, actual_count)
                if log_errors:
                    deadlock_count = len(log_errors)

            # 存储机器详细信息
            machine_details[host] = {
                "hanging_count": hanging_count,
                "deadlock_count": deadlock_count,
                "expected_count": expected_count,
                "actual_count": actual_count
            }

            # 为每台机器构建告警信息
            if hanging_count > 0 or deadlock_count > 0:
                alerts.append(f"{host}：挂起({hanging_count}) | 假死({deadlock_count})")

        return alerts, total_count, machine_details

    def _check_docker_containers(self, host, user, password, app_name):
        """
        检查远程主机上指定应用的Docker容器数量
        :param host: 主机地址
        :param user: 用户名
        :param password: 密码
        :param app_name: 应用名称
        :return: 容器数量
        """
        try:
            # 创建SSH客户端
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            # 连接远程主机
            ssh.connect(hostname=host, username=user, password=password)

            # 执行docker ps命令
            container_name_pattern = app_name.replace('_', '-')
            command = f"sudo docker ps | grep {container_name_pattern} | wc -l"
            stdin, stdout, stderr = ssh.exec_command(command)

            # 获取结果
            count = int(stdout.read().decode().strip())

            ssh.close()
            return count
        except Exception as e:
            logger.error(f"检查 {host} 上 {app_name} 容器时出错: {e}")
            return 0

    def _check_app_logs(self, host, user, password, app_name, container_count):
        """
        检查应用日志中的内存错误
        :param host: 主机地址
        :param user: 用户名
        :param password: 密码
        :param app_name: 应用名称
        :param container_count: 容器数量
        :return: 错误列表
        """
        alerts = []
        today = datetime.now().strftime('%Y-%m-%d')

        # 内存错误模式
        error_patterns = [
            "java.lang.OutOfMemoryError: Java heap space",
            "java.lang.OutOfMemoryError: GC overhead limit exceeded",
            "Caused by: java.lang.RuntimeException: java.lang.OutOfMemoryError: Java heap space"
        ]
        app_names = ['next-compute-scheduler', 'linkx-scheduler', 'dataware-scheduler', 'dataware-quality', 'bpms-app']

        # 特殊处理gdp_server应用
        if app_name in app_names:
            # gdp机器需要检查3个特定容器的日志
            try:
                # 构建日志路径
                if user == 'glory' and app_name == 'next-compute-scheduler':
                    module = 'next-compute'
                elif user == 'glory' and app_name == 'linkx-scheduler':
                    module = 'linkx'
                elif app_name == 'bpms-app':
                    module = 'bpms'
                else:
                    module = 'dataware'
                log_path = f"/home/<USER>/deploy/onedata/{module}/{app_name}/logs/info/{app_name}_{today}.log"

                # 读取日志文件内容
                errors = self._check_log_file_for_errors(host, user, password, log_path, error_patterns)
                if errors:
                    alerts.append(f"{host}机器{app_name}应用异常，可能存在假死")
            except Exception as e:
                logger.error(f"检查 {host} 上 {app_name} 容器的日志时出错: {e}")
        else:
            # 检查每个容器的日志
            for i in range(1, container_count + 1):
                try:
                    # 构建日志路径
                    log_path = f"/home/<USER>/deploy/onedata/dataware/{app_name}/logs{i}/info/{app_name}_{today}.log"
                    # 读取日志文件内容（这里简化处理，实际可能需要通过SSH读取远程文件）
                    errors = self._check_log_file_for_errors(host, user, password, log_path, error_patterns)
                    if errors:
                        alerts.append(f"{host}机器{app_name}应用异常，可能存在假死")
                        break  # 只要有一个容器出错就告警，避免重复
                except Exception as e:
                    logger.error(f"检查 {host} 上 {app_name} 容器 {i} 的日志时出错: {e}")

        return alerts

    def _check_log_file_for_errors(self, host, user, password, log_path, error_patterns):
        """
        检查日志文件中是否包含指定的错误模式
        :param host: 主机地址
        :param user: 用户名
        :param password: 密码
        :param log_path: 日志文件路径
        :param error_patterns: 错误模式列表
        :return: 是否包含错误
        """
        try:
            # 创建SSH客户端
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            # 连接远程主机
            ssh.connect(hostname=host, username=user, password=password)

            # 检查文件是否存在
            stdin, stdout, stderr = ssh.exec_command(f"test -f {log_path} && echo 'exists'")
            result = stdout.read().decode().strip()

            if result != 'exists':
                ssh.close()
                return False

            # 查找错误模式
            for pattern in error_patterns:
                command = f"grep '{pattern}' {log_path}"
                stdin, stdout, stderr = ssh.exec_command(command)
                errors = stdout.read().decode().strip()
                if errors:
                    ssh.close()
                    return True

            ssh.close()
            return False
        except Exception as e:
            logger.error(f"检查日志文件 {log_path} 时出错: {e}")
            return False


# 主函数
def main():
    logger.info("监控报警系统启动")

    # 获取数据库连接
    db_conn = get_db_connection()
    if not db_conn:
        send_email("监控系统错误", "无法连接到数据库")
        return

    # 创建监控实例
    data_monitor = DataVolumeMonitor(db_conn)
    middleware_monitor = MiddlewareMonitor(db_conn)
    app_monitor = ApplicationMonitor(db_conn)

    # 执行监控

    data_result = {}
    middleware_result = {}
    app_result = {}

    data_result = data_monitor.monitor()
    middleware_result = middleware_monitor.monitor()
    app_result = app_monitor.monitor()

    # 收集报警信息
    alerts = []

    # 收集数据监控告警
    if "data" in data_result and "alerts" in data_result["data"]:
        alerts.extend(data_result["data"]["alerts"])

    # 收集应用监控告警
    if "data" in app_result:
        for app_name, app_info in app_result["data"].items():
            if "alerts" in app_info:
                alerts.extend(app_info["alerts"])

    # 收集中间件监控告警
    for key, value in middleware_result.items():
        if value["status"] == "error":
            alerts.append(value["message"])

    # 构建HTML邮件内容
    html_content = """
    <html>
    <head>
        <style>
            table {
                border-collapse: collapse;
                width: 100%;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }
            th {
                background-color: #f2f2f2;
            }
            .error { color: red; }
            .success { color: green; }
        </style>
    </head>
    <body>
    """

    # 第一块：入库状态总结
    html_content += "<h2>入库状态总结</h2>"

    # 创建三行三列的总结表格
    html_content += """
    <table>
        <tr>
            <th>模块</th>
            <th>状态</th>
            <th>处理建议</th>
        </tr>
    """

    # 入库状态行
    data_status = data_result.get("status", "success")
    data_status_icon = "&#10007;" if data_status == "error" else "&#10003;"
    data_status_class = "error" if data_status == "error" else "success"
    data_suggestion = "异常，请及时处理" if data_status == "error" else "正常"
    html_content += f"""
        <tr>
            <td>入库状态</td>
            <td class="{data_status_class}">{data_status_icon}</td>
            <td>{data_suggestion}</td>
        </tr>
    """

    # 中间件状态行
    middleware_has_error = any(info["status"] == "error" for info in middleware_result.values())
    middleware_status_icon = "&#10007;" if middleware_has_error else "&#10003;"
    middleware_status_class = "error" if middleware_has_error else "success"
    middleware_suggestion = "异常，请及时关注并处理" if middleware_has_error else "正常"
    html_content += f"""
        <tr>
            <td>中间件状态</td>
            <td class="{middleware_status_class}">{middleware_status_icon}</td>
            <td>{middleware_suggestion}</td>
        </tr>
    """

    # 应用状态行
    app_status = app_result.get("status", "success")
    app_status_icon = "&#10007;" if app_status == "error" else "&#10003;"
    app_status_class = "error" if app_status == "error" else "success"
    app_suggestion = "异常，请及时处理" if app_status == "error" else "正常"
    html_content += f"""
        <tr>
            <td>应用状态</td>
            <td class="{app_status_class}">{app_status_icon}</td>
            <td>{app_suggestion}</td>
        </tr>
    """

    html_content += "</table>"

    # 第二块：入库状态详情
    html_content += "<h2>入库状态详情</h2>"
    if "data" in data_result:
        html_content += """
        <table>
            <tr>
                <th>阶段</th>
                <th>状态</th>
                <th>积压数</th>
                <th>当天成功数</th>
                <th>最后成功时间</th>
            </tr>
        """

        # 阶段名称映射
        phase_names = {
            "2200": "转换任务",
            "3100": "预解析任务",
            "4100": "正式解析任务",
            "5100": "die表计算任务",
            "7100": "测项表计算任务"
        }

        for phase in ["2200", "3100", "4100", "5100", "7100"]:
            if phase in data_result["data"]:
                phase_data = data_result["data"][phase]
                status_icon = "&#10007;" if phase_data.get('status') == 'error' else "&#10003;"
                status_class = "error" if phase_data.get('status') == 'error' else "success"
                html_content += f"""
                <tr>
                    <td>{phase_names.get(phase, phase)}</td>
                    <td class="{status_class}">{status_icon}</td>
                    <td>{phase_data.get('count', 'N/A')}</td>
                    <td>{phase_data.get('today_count', 'N/A')}</td>
                    <td>{phase_data.get('last_update', 'N/A')}</td>
                </tr>
                """
        html_content += "</table>"

    # 第三块：中间件信息
    html_content += "<h2>中间件状态详情</h2>"
    if middleware_result:
        html_content += """
        <table>
            <tr>
                <th>组件</th>
                <th>状态</th>
                <th>信息</th>
            </tr>
        """
        for component, info in middleware_result.items():
            status_icon = "&#10003;" if info["status"] == "success" else "&#10007;"
            status_class = "success" if info["status"] == "success" else "error"
            html_content += f"""
            <tr>
                <td>{component}</td>
                <td class="{status_class}">{status_icon}</td>
                <td>{info["message"]}</td>
            </tr>
            """
        html_content += "</table>"

    # 第四块：应用状态详情
    html_content += "<h2>应用状态详情</h2>"
    if "data" in app_result:
        html_content += """
        <table>
            <tr>
                <th>应用名称</th>
                <th>状态</th>
                <th>实例数</th>
                <th>信息</th>
            </tr>
        """
        for app_name, app_info in app_result["data"].items():
            status_icon = "&#10003;" if app_info["status"] == "success" else "&#10007;"
            status_class = "success" if app_info["status"] == "success" else "error"
            alerts_info = "; ".join(app_info.get("alerts", [])) if app_info.get("alerts") else ""
            html_content += f"""
            <tr>
                <td>{app_name}</td>
                <td class="{status_class}">{status_icon}</td>
                <td>{app_info.get("count", 0)}</td>
                <td>{alerts_info}</td>
            </tr>
            """
        html_content += "</table>"

    html_content += """
    </body>
    </html>
    """

    # 如果有报警信息则发送邮件
    if alerts or True:  # 始终发送邮件报告
        # 修改邮件主题，添加日期时间戳
        current_time = datetime.now().strftime("%Y-%m-%d_%H")
        subject = f"数据平台时报_{current_time}"
        send_email(subject, html_content, is_html=True)
        logger.info("发现监控异常，已发送报警邮件") if alerts else logger.info("监控报告已发送")
    else:
        logger.info("所有监控项正常")

    # 关闭数据库连接
    db_conn.close()


if __name__ == "__main__":
    main()
