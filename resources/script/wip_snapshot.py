# -*- coding: utf-8 -*-
import argparse
import datetime
import logging
import re
import sys
from collections import deque, defaultdict

import pandas as pd
from clickhouse_driver import Client


class ClickHouseHelper:
    """处理ClickHouse数据库操作的类"""

    def __init__(self, host, port, user, password, database):
        """初始化ClickHouse连接"""
        self.connection_config = {
            'host': host,
            'port': int(port),
            'user': user,
            'password': password,
            'database': database
        }
        self.client = Client(**self.connection_config)
        self.logger = logging.getLogger("ClickHouseHelper")

    def read_ck(self, sql):
        """执行查询并返回DataFrame"""
        try:
            data, columns = self.client.execute(
                sql, columnar=True, with_column_types=True)
            return pd.DataFrame({re.sub(r'\W', '_', col[0]): d for d, col in zip(data, columns)})
        except Exception as e:
            self.logger.error(f"执行查询失败: {str(e)}")
            self.logger.error(f"SQL: {sql}")
            raise

    def to_ck(self, dataframe, table_name):
        """将DataFrame写入ClickHouse表"""
        try:
            sql = f"select name, type from system.columns where table='{table_name}';"
            column_type_df = self.read_ck(sql)
            column_type_df = column_type_df.set_index('name')
            type_dict = column_type_df.to_dict('dict')['type']
            columns = [i for i in list(type_dict.keys()) if i not in [
                'PARTITION_KEY', 'VERSION']]
            cols = ','.join(columns)
            data = dataframe.to_dict('records')
            sql = f"INSERT INTO {table_name} ({cols}) VALUES"
            self.client.execute(sql, data, types_check=True)
            self.logger.info(f"成功插入{len(data)}条数据到{table_name}")
        except Exception as e:
            self.logger.error(f"写入数据失败: {str(e)}")
            self.logger.error(f"表名: {table_name}")
            raise

    def execute(self, sql):
        """执行SQL语句"""
        try:
            return self.client.execute(sql)
        except Exception as e:
            self.logger.error(f"执行SQL失败: {str(e)}")
            self.logger.error(f"SQL: {sql}")
            raise


class WipSnapshotProcessor:
    """WIP快照处理器"""

    def __init__(self, ck_helper):
        """初始化处理器"""
        self.ck_helper = ck_helper
        self.logger = logging.getLogger("WipSnapshotProcessor")

    @staticmethod
    def topological_sort(edges):
        """
        执行拓扑排序，并返回排序结果的字典，其中键为节点，值为该节点在排序中的位置。

        :param edges: 表示图中边的列表，每个边由两个节点构成的元组表示
        :return: 排序结果的字典
        """
        # 使用字典存储每个节点的出度（指向其它节点的边的数量）
        edges_dict = defaultdict(list)
        for i in edges:
            edges_dict[i[0]].append(i[1])  # 为节点的出边建模

        path = deque()  # 用于存储拓扑排序的结果
        in_degree = {u: 0 for u in edges_dict}  # 初始化所有节点的入度为0

        # 计算每个节点的入度
        for u in edges_dict:
            for v in edges_dict[u]:
                if v not in in_degree:
                    in_degree[v] = 1
                else:
                    in_degree[v] += 1

        # 初始化队列，存放入度为0的节点（没有节点指向它们）
        queue = deque([u for u in edges_dict if in_degree[u] == 0])

        # 核心算法：使用Kahn算法进行拓扑排序
        while queue:
            u = queue.popleft()  # 从队列中取出一个入度为0的节点
            path.append(u)  # 将该节点加入拓扑排序的结果中
            for v in edges_dict[u]:  # 更新该节点指向的所有节点的入度
                in_degree[v] -= 1
                if in_degree[v] == 0:  # 如果某个节点的入度变为0，将其加入队列
                    queue.append(v)

        # 返回排序结果, 键为节点，值为该节点在排序中的位置
        return {value: index for index, value in enumerate(path)}

    @staticmethod
    def find_key(data, j, k):
        """查找字典中的值"""
        if j in data and k in data[j]:
            return data[j][k]
        else:
            return 0

    def process(self):
        """处理WIP快照"""
        self.logger.info('开始计算WIP快照表')

        # 查询需要标记成MoveOut的数据，这些数据记录了当前的STAGE和前一个STAGE
        move_out_df = self.ck_helper.read_ck("""
        select *
        from (select *, any(STAGE) OVER w AS STAGE_PRE
              from dwd_wip_detail_cluster  where STAGE_START_TIME IS NOT NULL and STAGE_DESC not LIKE '%REJECT%'
              window w as (
                      PARTITION BY (CUSTOMER, DATA_SOURCE, FACTORY, DEVICE_ID, LOT_ID, SBLOT_ID) 
                      ORDER BY STAGE_START_TIME
                      ROWS BETWEEN 1 PRECEDING AND 1 PRECEDING ))
        where STAGE != STAGE_PRE
        """)

        if len(move_out_df) == 0:
            self.logger.info('没有数据需要计算站点顺序，任务结束')
            return False

        # 计算DEVICE的站点顺序
        device_stage_df = move_out_df[['DEVICE_ID', 'STAGE_PRE', 'STAGE']].drop_duplicates()
        device_stage_df['EDGE'] = device_stage_df.apply(lambda x: [x.STAGE_PRE, x.STAGE], axis=1)
        device_edge_df = device_stage_df[['DEVICE_ID', 'EDGE']].groupby("DEVICE_ID").agg(self.topological_sort).reset_index()
        device_to_stage_order = {i['DEVICE_ID']: i['EDGE'] for i in device_edge_df.to_dict(orient='records')}

        self.logger.info(f'device_to_stage_order: {device_to_stage_order}')

        # 计算SBLOT的站点顺序
        move_out_df['SBLOT_ID_KEY'] = move_out_df['DEVICE_ID'] + '_' + move_out_df['LOT_ID'] + '_' + move_out_df['SBLOT_ID']
        lot_stage_df = move_out_df[['SBLOT_ID_KEY', 'STAGE_PRE', 'STAGE']].drop_duplicates()
        lot_stage_df['EDGE'] = lot_stage_df.apply(lambda x: [x.STAGE_PRE, x.STAGE], axis=1)
        lot_edge_df = lot_stage_df[['SBLOT_ID_KEY', 'EDGE']].groupby("SBLOT_ID_KEY").agg(self.topological_sort).reset_index()
        lot_to_stage_order = {i['SBLOT_ID_KEY']: i['EDGE'] for i in lot_edge_df.to_dict(orient='records')}

        self.logger.info(f'lot_to_stage_order: {lot_to_stage_order}')

        # 修改状态为MoveOut
        move_out_df.STAGE = move_out_df.STAGE_PRE
        move_out_df.LOT_STATUS = 'MoveOut'
        move_out_df = move_out_df.drop("STAGE_PRE", axis=1)
        move_out_df = move_out_df[move_out_df['STAGE'] != '']

        # 拉取dwd_wip_detail_cluster
        wip_detail = self.ck_helper.read_ck("""
        SELECT * FROM dwd_wip_detail_cluster FINAL WHERE STAGE_START_TIME IS NOT NULL
        """)

        self.logger.info(f'wip_detail数据条数: {len(wip_detail)}')

        # 追加MoveOut数据, 计算STAGE_ORDER和ACTUAL_STAGE_ORDER
        snapshot = pd.concat([wip_detail, move_out_df])
        snapshot['STAGE_ORDER'] = snapshot.apply(lambda x: self.find_key(device_to_stage_order, x.DEVICE_ID, x.STAGE), axis=1)
        snapshot['ACTUAL_STAGE_ORDER'] = snapshot.apply(
            lambda x: self.find_key(lot_to_stage_order, x['DEVICE_ID'] + '_' + x['LOT_ID'] + '_' + x['SBLOT_ID'], x.STAGE), axis=1)
        current_time = datetime.datetime.now()
        snapshot['CREATE_TIME'] = current_time
        snapshot['CREATE_HOUR_KEY'] = ''.join([i for i in str(current_time) if i.isdigit()])[0: 10]
        snapshot['CREATE_DAY_KEY'] = ''.join([i for i in str(current_time) if i.isdigit()])[0: 8]

        self.logger.info(f'快照表数据条数: {len(snapshot)}')

        # 清除快照表
        self.logger.info('开始清除快照表')
        self.ck_helper.execute("TRUNCATE TABLE dwd_wip_snapshot_local ON CLUSTER cluster_3shards_1replicas")

        # 最终结果写入快照表
        self.logger.info('开始写入快照表')
        self.ck_helper.to_ck(snapshot, "dwd_wip_snapshot_cluster")

        self.logger.info('SYSTEM CODE: 1 任务结束')
        return True


def setup_logging(log_file):
    """设置日志配置"""
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)

    # 文件处理器
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.DEBUG)

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.DEBUG)

    # 格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger


def main():
    parser = argparse.ArgumentParser(description='WIP快照处理')
    parser.add_argument('jdbc_url', help='ClickHouse JDBC URL (e.g., ***************************)')
    parser.add_argument('user', help='ClickHouse用户名')
    parser.add_argument('password', help='ClickHouse密码')
    parser.add_argument('--database', default='dwd', help='ClickHouse数据库名称')
    parser.add_argument('--log-file', default='/home/<USER>/deploy/datahub/bpms/cron/wip_snapshot.log',
                        help='日志文件路径')

    args = parser.parse_args()

    # 解析JDBC URL
    match = re.match(r'jdbc:clickhouse://([^:]+):(\d+)', args.jdbc_url)
    if not match:
        print(f"错误: 无效的JDBC URL格式: {args.jdbc_url}", file=sys.stderr)
        sys.exit(3)  # Use a different exit code for invalid URL format
    
    host = match.group(1)
    port = match.group(2)

    logger = setup_logging(args.log_file)
    logger.info(f'host: {host}, port: {port}, user: {args.user}, database: {args.database}')

    try:
        ck_helper = ClickHouseHelper(
            host=host,
            port=port,
            user=args.user,
            password=args.password,
            database=args.database
        )

        processor = WipSnapshotProcessor(ck_helper)

        result = processor.process()
        sys.exit(0 if result else 1)

    except Exception as e:
        logger.error(f"处理WIP快照时发生错误: {str(e)}", exc_info=True)
        sys.exit(2)


if __name__ == "__main__":
    main()
