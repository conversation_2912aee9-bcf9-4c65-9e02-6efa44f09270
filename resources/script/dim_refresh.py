# coding=utf-8
import logging
import time
import os
import json
import sys
import pymysql
from math import log
from clickhouse_driver import Client
import argparse
import re

get_last_success_time = '''
SELECT start_time
FROM {bpms_database}.bz_schedule_task_record
WHERE task_name = 'dim-refresh'
  and process_status = 'SUCCESS'
order by start_time desc
limit 1;
'''

get_device_need_replay = '''
SELECT  DISTINCT l.customer AS customer
       ,l.factory           AS factory
       ,l.test_area         AS test_area
       ,l.test_stage        AS test_stage
       ,l.device_id         AS device_id
FROM
(
    SELECT  DISTINCT customer
           ,test_area
           ,test_stage
           ,factory
           ,device_id
    FROM {onedata_database}.dw_cleanup_task
    WHERE cast(step AS UNSIGNED) <= 3100
    AND process_status = 'SUCCESS'
    AND update_time >= '{last_run_time}'
    AND update_time < '{refresh_start_time}'
    UNION DISTINCT
    SELECT  DISTINCT customer
           ,test_area
           ,test_stage
           ,factory
           ,device_id
    FROM
    (
        SELECT  customer
               ,test_area
               ,coalesce(CASE WHEN JSON_UNQUOTE(JSON_EXTRACT(repair_field,CONCAT('$[',seq.seq - 1,'].repairField'))) = 'TEST_STAGE' THEN JSON_UNQUOTE(JSON_EXTRACT(repair_field,CONCAT('$[',seq.seq - 1,'].originValue'))) ELSE NULL END,test_stage) AS test_stage
               ,coalesce(CASE WHEN JSON_UNQUOTE(JSON_EXTRACT(repair_field,CONCAT('$[',seq.seq - 1,'].repairField'))) = 'FACTORY' THEN JSON_UNQUOTE(JSON_EXTRACT(repair_field,CONCAT('$[',seq.seq - 1,'].originValue'))) ELSE NULL END,factory) AS factory
               ,coalesce(CASE WHEN JSON_UNQUOTE(JSON_EXTRACT(repair_field,CONCAT('$[',seq.seq - 1,'].repairField'))) = 'DEVICE_ID' THEN JSON_UNQUOTE(JSON_EXTRACT(repair_field,CONCAT('$[',seq.seq - 1,'].originValue'))) ELSE NULL END,device_id) AS device_id
        FROM {onedata_database}.dw_repair_record, JSON_TABLE(repair_field, '$[*]' COLUMNS ( seq FOR ORDINALITY )) AS seq
        WHERE JSON_UNQUOTE(JSON_EXTRACT(repair_field, CONCAT('$[', seq.seq - 1, '].repairField'))) IN ('TEST_STAGE', 'FACTORY', 'DEVICE_ID')
        AND process_status = 'SUCCESS' 
        AND update_time >= '{last_run_time}'
        AND update_time < '{refresh_start_time}'
    )base
)l
LEFT JOIN
(
    SELECT  customer
           ,test_area
           ,test_stage
           ,factory
           ,device_id
    FROM {onedata_database}.dw_lot_meta_data_detail
    GROUP BY  customer
             ,test_area
             ,test_stage
             ,factory
             ,device_id
)r
ON l.customer = r.customer
AND l.test_area = r.test_area
AND l.test_stage = r.test_stage
AND l.factory = r.factory
AND l.device_id = r.device_id
WHERE r.customer IS NULL
AND r.test_area IS NULL
AND r.test_stage IS NULL
AND r.factory IS NULL
AND r.device_id IS NULL
AND l.customer IS NOT NULL
AND l.test_area IS NOT NULL
AND l.test_stage IS NOT NULL
AND l.factory IS NOT NULL
AND l.device_id IS NOT NULL;
'''

insert_bin = '''
INSERT INTO {dim_database}.dim_test_program_bin_local(CUSTOMER ,SUB_CUSTOMER ,UPLOAD_TYPE ,FILE_ID ,FACTORY ,FACTORY_SITE ,FAB ,FAB_SITE ,TEST_AREA ,TEST_STAGE ,DEVICE_ID ,TEST_PROGRAM ,TEST_PROGRAM_VERSION ,TEST_TEMPERATURE ,HBIN_NUM ,HBIN_NUM_KEY ,SBIN_NUM ,SBIN_NUM_KEY ,SBIN_PF ,SBIN_NAM ,HBIN_PF ,HBIN_NAM ,HBIN ,SBIN ,CREATE_HOUR_KEY ,CREATE_DAY_KEY ,CREATE_TIME ,CREATE_USER ,UPLOAD_TIME ,VERSION ,IS_DELETE)
SELECT  CUSTOMER                                                                  AS CUSTOMER
       ,SUB_CUSTOMER                                                              AS SUB_CUSTOMER
       ,UPLOAD_TYPE                                                               AS UPLOAD_TYPE
       ,0
       ,FACTORY                                                                   AS FACTORY
       ,FACTORY_SITE                                                              AS FACTORY_SITE
       ,argMin(FAB,ifNull(START_TIME,toDate(0)))                                  AS FAB
       ,argMin(FAB_SITE,ifNull(START_TIME,toDate(0)))                             AS FAB_SITE
       ,TEST_AREA                                                                 AS TEST_AREA
       ,TEST_STAGE                                                                AS TEST_STAGE
       ,DEVICE_ID                                                                 AS DEVICE_ID
       ,TEST_PROGRAM                                                              AS TEST_PROGRAM
       ,TEST_PROGRAM_VERSION                                                      AS TEST_PROGRAM_VERSION
       ,argMin(TEST_TEMPERATURE,ifNull(START_TIME,toDate(0)))                     AS TEST_TEMPERATURE
       ,IF(TEST_AREA = 'WAT',999999999,HBIN_NUM)                                  AS HBIN_NUM_FIX
       ,ifNull(toString(HBIN_NUM_FIX),'')                                         AS HBIN_NUM_KEY
       ,IF(TEST_AREA = 'WAT',999999999,SBIN_NUM)                                  AS SBIN_NUM_FIX
       ,ifNull(toString(SBIN_NUM_FIX),'')                                         AS SBIN_NUM_KEY
       ,SBIN_PF                                                                   AS SBIN_PF
       ,SBIN_NAM                                                                  AS SBIN_NAM
       ,HBIN_PF                                                                   AS HBIN_PF
       ,HBIN_NAM                                                                  AS HBIN_NAM
       ,argMin(HBIN,ifNull(START_TIME,toDate(0)))                                 AS HBIN
       ,argMin(SBIN,ifNull(START_TIME,toDate(0)))                                 AS SBIN
       ,formatDateTime(toDateTime('{CURRENT_TIME}'),'%Y%m%d%H')                   AS CREATE_HOUR_KEY
       ,formatDateTime(toDateTime('{CURRENT_TIME}'),'%Y%m%d')                     AS CREATE_DAY_KEY
       ,'{CURRENT_TIME}'                                                          AS CREATE_TIME
       ,'System'                                                                  AS CREATE_USER
       ,argMin(UPLOAD_TIME,ifNull(START_TIME,toDate(0)))                          AS UPLOAD_TIME
       ,3999999999 - ifNull(MIN(toUnixTimestamp(ifNull(START_TIME,toDate(0)))),0) AS VERSION
       ,0
FROM {dwd_database}.dwd_die_detail_cluster
WHERE IS_DELETE = 0
AND UPLOAD_TYPE = 'AUTO'
AND CUSTOMER = '{CUSTOMER}'
AND FACTORY = '{FACTORY}'
AND TEST_AREA = '{TEST_AREA}'
AND TEST_STAGE = '{TEST_STAGE}'
AND DEVICE_ID = '{DEVICE_ID}'
GROUP BY  CUSTOMER
         ,SUB_CUSTOMER
         ,UPLOAD_TYPE
         ,FACTORY
         ,FACTORY_SITE
         ,TEST_AREA
         ,TEST_STAGE
         ,DEVICE_ID
         ,TEST_PROGRAM
         ,TEST_PROGRAM_VERSION
         ,HBIN_NUM_FIX
         ,SBIN_NUM_FIX
         ,SBIN_PF
         ,SBIN_NAM
         ,HBIN_PF
         ,HBIN_NAM;
'''

insert_site = '''
INSERT INTO {dim_database}.dim_test_program_site_local(CUSTOMER ,SUB_CUSTOMER ,UPLOAD_TYPE ,FILE_ID ,FACTORY ,FACTORY_SITE ,FAB ,FAB_SITE ,TEST_AREA ,TEST_STAGE ,DEVICE_ID ,TEST_PROGRAM ,TEST_PROGRAM_VERSION ,TEST_TEMPERATURE ,SITE ,SITE_KEY ,CREATE_HOUR_KEY ,CREATE_DAY_KEY ,CREATE_TIME ,CREATE_USER ,UPLOAD_TIME ,VERSION ,IS_DELETE)
SELECT  CUSTOMER                                                                  AS CUSTOMER
       ,SUB_CUSTOMER                                                              AS SUB_CUSTOMER
       ,UPLOAD_TYPE                                                               AS UPLOAD_TYPE
       ,0
       ,FACTORY                                                                   AS FACTORY
       ,FACTORY_SITE                                                              AS FACTORY_SITE
       ,argMin(FAB,ifNull(START_TIME,toDate(0)))                                  AS FAB
       ,argMin(FAB_SITE,ifNull(START_TIME,toDate(0)))                             AS FAB_SITE
       ,TEST_AREA                                                                 AS TEST_AREA
       ,TEST_STAGE                                                                AS TEST_STAGE
       ,DEVICE_ID                                                                 AS DEVICE_ID
       ,TEST_PROGRAM                                                              AS TEST_PROGRAM
       ,TEST_PROGRAM_VERSION                                                      AS TEST_PROGRAM_VERSION
       ,argMin(TEST_TEMPERATURE,ifNull(START_TIME,toDate(0)))                     AS TEST_TEMPERATURE
       ,IF(TEST_AREA = 'WAT',999999999,SITE)                                      AS SITE_FIX
       ,ifNull(toString(SITE_FIX),'')                                             AS SITE_KEY
       ,formatDateTime(toDateTime('{CURRENT_TIME}'),'%Y%m%d%H')                   AS CREATE_HOUR_KEY
       ,formatDateTime(toDateTime('{CURRENT_TIME}'),'%Y%m%d')                     AS CREATE_DAY_KEY
       ,'{CURRENT_TIME}'                                                          AS CREATE_TIME
       ,'System'                                                                  AS CREATE_USER
       ,argMin(UPLOAD_TIME,ifNull(START_TIME,toDate(0)))                          AS UPLOAD_TIME
       ,3999999999 - ifNull(MIN(toUnixTimestamp(ifNull(START_TIME,toDate(0)))),0) AS VERSION
       ,0
FROM {dwd_database}.dwd_die_detail_cluster
WHERE IS_DELETE = 0
AND UPLOAD_TYPE = 'AUTO'
AND CUSTOMER = '{CUSTOMER}'
AND FACTORY = '{FACTORY}'
AND TEST_AREA = '{TEST_AREA}'
AND TEST_STAGE = '{TEST_STAGE}'
AND DEVICE_ID = '{DEVICE_ID}'
GROUP BY  CUSTOMER
         ,SUB_CUSTOMER
         ,UPLOAD_TYPE
         ,FACTORY
         ,FACTORY_SITE
         ,TEST_AREA
         ,TEST_STAGE
         ,DEVICE_ID
         ,TEST_PROGRAM
         ,TEST_PROGRAM_VERSION
         ,SITE_FIX;
'''

insert_program = '''
INSERT INTO {dim_database}.dim_test_program_test_item_local(CUSTOMER ,SUB_CUSTOMER ,UPLOAD_TYPE ,FILE_ID ,FACTORY ,FACTORY_SITE ,FAB ,FAB_SITE ,TEST_AREA ,TEST_STAGE ,DEVICE_ID ,TEST_PROGRAM ,TEST_PROGRAM_VERSION ,TEST_TEMPERATURE ,TEST_NUM ,TEST_NUM_KEY ,TEST_TXT ,TEST_ITEM ,TESTITEM_TYPE ,ORIGIN_HI_LIMIT ,ORIGIN_HI_LIMIT_KEY ,ORIGIN_LO_LIMIT ,ORIGIN_LO_LIMIT_KEY ,ORIGIN_UNITS ,LO_LIMIT ,LO_LIMIT_KEY ,HI_LIMIT ,HI_LIMIT_KEY ,TARGET ,UNITS ,CONDITION_SET ,CONDITION_SET_STR ,CREATE_HOUR_KEY ,CREATE_DAY_KEY ,CREATE_TIME ,CREATE_USER ,UPLOAD_TIME ,VERSION ,IS_DELETE)
SELECT  CUSTOMER                                                AS CUSTOMER
       ,SUB_CUSTOMER                                            AS SUB_CUSTOMER
       ,UPLOAD_TYPE                                             AS UPLOAD_TYPE
       ,0                                                       AS FILE_ID
       ,FACTORY                                                 AS FACTORY
       ,FACTORY_SITE                                            AS FACTORY_SITE
       ,argMax(FAB,VERSION)                                     AS FAB
       ,argMax(FAB_SITE,VERSION)                                AS FAB_SITE
       ,TEST_AREA                                               AS TEST_AREA
       ,TEST_STAGE                                              AS TEST_STAGE
       ,DEVICE_ID                                               AS DEVICE_ID
       ,TEST_PROGRAM                                            AS TEST_PROGRAM
       ,TEST_PROGRAM_VERSION                                    AS TEST_PROGRAM_VERSION
       ,TEST_TEMPERATURE                                        AS TEST_TEMPERATURE
       ,argMax(TEST_NUM_FIX,VERSION)                            AS TEST_NUM
       ,TEST_NUM_KEY                                            AS TEST_NUM_KEY
       ,TEST_TXT                                                AS TEST_TXT
       ,TEST_ITEM                                               AS TEST_ITEM
       ,TESTITEM_TYPE                                           AS TESTITEM_TYPE
       ,argMax(ORIGIN_HI_LIMIT,VERSION)                         AS ORIGIN_HI_LIMIT
       ,ORIGIN_HI_LIMIT_KEY                                     AS ORIGIN_HI_LIMIT_KEY
       ,argMax(ORIGIN_LO_LIMIT,VERSION)                         AS ORIGIN_LO_LIMIT
       ,ORIGIN_LO_LIMIT_KEY                                     AS ORIGIN_LO_LIMIT_KEY
       ,ORIGIN_UNITS                                            AS ORIGIN_UNITS
       ,argMax(LO_LIMIT,VERSION)                                AS LO_LIMIT
       ,LO_LIMIT_KEY                                            AS LO_LIMIT_KEY
       ,argMax(HI_LIMIT,VERSION)                                AS HI_LIMIT
       ,HI_LIMIT_KEY                                            AS HI_LIMIT_KEY
       ,argMax(TARGET,VERSION)
       ,UNITS                                                   AS UNITS
       ,argMax(CONDITION_SET,VERSION)                           AS CONDITION_SET
       ,CONDITION_SET_STR                                       AS CONDITION_SET_STR
       ,formatDateTime(toDateTime('{CURRENT_TIME}'),'%Y%m%d%H') AS CREATE_HOUR_KEY
       ,formatDateTime(toDateTime('{CURRENT_TIME}'),'%Y%m%d')   AS CREATE_DAY_KEY
       ,'{CURRENT_TIME}'                                        AS CREATE_TIME
       ,argMax(CREATE_USER,VERSION)                             AS CREATE_USER
       ,argMax(UPLOAD_TIME,VERSION)                             AS UPLOAD_TIME
       ,3999999999 - ifNull(MIN(toUnixTimestamp(ifNull(START_TIME,toDate(0)))),0)
       ,0                                                       AS IS_DELETE
FROM
(
    SELECT  CUSTOMER
           ,SUB_CUSTOMER
           ,UPLOAD_TYPE
           ,FACTORY
           ,FACTORY_SITE
           ,FAB
           ,FAB_SITE
           ,TEST_AREA
           ,TEST_STAGE
           ,DEVICE_ID
           ,TEST_PROGRAM
           ,TEST_PROGRAM_VERSION
           ,TEST_TEMPERATURE
           ,TEST_NUM
           ,TEST_TXT
           ,TEST_ITEM
           ,TESTITEM_TYPE
           ,ORIGIN_HI_LIMIT
           ,ORIGIN_LO_LIMIT
           ,ORIGIN_UNITS
           ,LO_LIMIT
           ,HI_LIMIT
           ,TARGET
           ,UNITS
           ,CONDITION_SET
           ,CREATE_USER
           ,UPLOAD_TIME
           ,START_TIME
           ,VERSION
           ,if(ORIGIN_HI_LIMIT is null , '' , if(position(toString(ORIGIN_HI_LIMIT),'.') > 0 , toString(ORIGIN_HI_LIMIT) , concat(toString(ORIGIN_HI_LIMIT) , '.0'))) AS ORIGIN_HI_LIMIT_KEY
           ,if(ORIGIN_LO_LIMIT is null , '' , if(position(toString(ORIGIN_LO_LIMIT),'.') > 0 , toString(ORIGIN_LO_LIMIT) , concat(toString(ORIGIN_LO_LIMIT) , '.0'))) AS ORIGIN_LO_LIMIT_KEY
           ,if(LO_LIMIT is null , '' , if(position(toString(LO_LIMIT),'.') > 0 , toString(LO_LIMIT) , concat(toString(LO_LIMIT) , '.0'))) AS LO_LIMIT_KEY
           ,if(HI_LIMIT is null , '' , if(position(toString(HI_LIMIT),'.') > 0 , toString(HI_LIMIT) , concat(toString(HI_LIMIT) , '.0'))) AS HI_LIMIT_KEY
           ,IF(TEST_AREA = 'WAT',ifNull(TEST_NUM,999999999),TEST_NUM) AS TEST_NUM_FIX
           ,ifNull(toString(TEST_NUM_FIX),'')                         AS TEST_NUM_KEY
           ,arrayStringConcat(arrayMap(key -> concat('(',key, ',', CONDITION_SET[key], ')'), arraySort(mapKeys(CONDITION_SET))), '') AS CONDITION_SET_STR
    FROM {dim_database}.dim_test_item_cluster
    WHERE IS_DELETE = 0
    AND UPLOAD_TYPE = 'AUTO'
    AND CUSTOMER = '{CUSTOMER}'
    AND FACTORY = '{FACTORY}'
    AND TEST_AREA = '{TEST_AREA}'
    AND TEST_STAGE = '{TEST_STAGE}'
    AND DEVICE_ID = '{DEVICE_ID}'
)
GROUP BY  CUSTOMER
         ,SUB_CUSTOMER
         ,UPLOAD_TYPE
         ,FACTORY
         ,FACTORY_SITE
         ,TEST_AREA
         ,TEST_STAGE
         ,DEVICE_ID
         ,TEST_PROGRAM
         ,TEST_PROGRAM_VERSION
         ,TEST_TEMPERATURE
         ,TEST_NUM_KEY
         ,TEST_TXT
         ,TEST_ITEM
         ,TESTITEM_TYPE
         ,ORIGIN_HI_LIMIT_KEY
         ,ORIGIN_LO_LIMIT_KEY
         ,ORIGIN_UNITS
         ,LO_LIMIT_KEY
         ,HI_LIMIT_KEY
         ,UNITS
         ,CONDITION_SET_STR;
'''

delete_bin = '''
ALTER TABLE dim_test_program_bin_local
ON cluster cluster_3shards_1replicas UPDATE IS_DELETE = 1 IN PARTITION ('{CUSTOMER}', 'AUTO', '{TEST_AREA}', '{FACTORY}')
WHERE CREATE_TIME < '{CURRENT_TIME}'
AND IS_DELETE = 0
AND UPLOAD_TYPE = 'AUTO'
AND CUSTOMER = '{CUSTOMER}'
AND FACTORY = '{FACTORY}'
AND TEST_AREA = '{TEST_AREA}'
AND TEST_STAGE = '{TEST_STAGE}'
AND DEVICE_ID = '{DEVICE_ID}';
'''

delete_site = '''
ALTER TABLE dim_test_program_site_local
ON cluster cluster_3shards_1replicas UPDATE IS_DELETE = 1 IN PARTITION ('{CUSTOMER}', 'AUTO', '{TEST_AREA}', '{FACTORY}')
WHERE CREATE_TIME < '{CURRENT_TIME}'
AND IS_DELETE = 0
AND UPLOAD_TYPE = 'AUTO'
AND CUSTOMER = '{CUSTOMER}'
AND FACTORY = '{FACTORY}'
AND TEST_AREA = '{TEST_AREA}'
AND TEST_STAGE = '{TEST_STAGE}'
AND DEVICE_ID = '{DEVICE_ID}';
'''

delete_program = '''
ALTER TABLE dim_test_program_test_item_local
ON cluster cluster_3shards_1replicas UPDATE IS_DELETE = 1 IN PARTITION ('{CUSTOMER}', 'AUTO', '{TEST_AREA}', '{FACTORY}')
WHERE CREATE_TIME < '{CURRENT_TIME}'
AND IS_DELETE = 0
AND UPLOAD_TYPE = 'AUTO'
AND CUSTOMER = '{CUSTOMER}'
AND FACTORY = '{FACTORY}'
AND TEST_AREA = '{TEST_AREA}'
AND TEST_STAGE = '{TEST_STAGE}'
AND DEVICE_ID = '{DEVICE_ID}';
'''

table_name = 'dim_refresh'

def format_sql(template_sql, dwd_database, dim_database, current_time, CUSTOMER, FACTORY, TEST_AREA, TEST_STAGE, DEVICE_ID):
    return template_sql.replace('{dwd_database}', dwd_database) \
        .replace('{dim_database}', dim_database) \
        .replace('{CURRENT_TIME}', str(current_time)) \
        .replace('{CUSTOMER}', CUSTOMER) \
        .replace('{FACTORY}', FACTORY) \
        .replace('{TEST_AREA}', TEST_AREA) \
        .replace('{TEST_STAGE}', TEST_STAGE) \
        .replace('{DEVICE_ID}', DEVICE_ID)

def setup_logging(log_file):
    """配置日志记录"""
    logger = logging.getLogger('dim_refresh')

    # 清除已有处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    logger.setLevel(logging.DEBUG)  # 设置日志级别
    logger.propagate = False  # 禁用日志传播
    # 创建文件处理器
    file_handler = logging.FileHandler(log_file, mode='w')
    file_handler.setLevel(logging.DEBUG)

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)

    # 定义日志格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器到日志记录器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

class MySQLHelper:
    def __init__(self, host, port, mysql_user, mysql_password, database, charset='utf8mb4'):
        self.host = host
        self.port = int(port)
        self.user = mysql_user
        self.password = mysql_password
        self.database = database
        self.charset = charset
        self.connection = None
        self.cursor = None

    def connect(self):
        try:
            self.connection = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                charset=self.charset
            )
            self.cursor = self.connection.cursor()
            logging.info("MySQL 连接成功")
        except Exception as e:
            logging.error(f"MySQL 连接失败: {e}")
            raise

    def disconnect(self):
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logging.info("MySQL 连接已断开")

    def execute(self, sql):
        try:
            self.cursor.execute(sql)
            self.connection.commit()
            logging.info(f"MySQL SQL 执行成功: {sql}")
        except Exception as e:
            logging.error(f"MySQL SQL 执行失败: {sql}，错误信息: {e}")
            raise

    def fetchall(self):
        try:
            result = self.cursor.fetchall()
            return result
        except Exception as e:
            logging.error(f"MySQL fetchall 执行失败: {e}")
            raise

def init(mysql_helper, bpms_helper, ck_user, ck_password, dwd_database, dim_database, logger, args):
    refresh_start_time = time.time()
    onedata_database = args.mysql_database
    bpms_database=args.bpms_database
    if not logger.handlers:
        logger.setLevel(logging.DEBUG)
        file_handler = logging.FileHandler(args.log_file)
        file_handler.setLevel(logging.DEBUG)
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        dwd_database = args.clickhouse_database
        dim_database = args.clickhouse_database

    partition_client = None
    try:
        get_last_success_time_sql = get_last_success_time.replace('{bpms_database}', bpms_database)
        logger.info(f'开始获取上次成功时间:{get_last_success_time_sql}')
        bpms_helper.execute(get_last_success_time_sql)
        result = bpms_helper.fetchall()
        logger.info(f'result: {result}')
        if result and result[0]:
            last_run_time = result[0][0].strftime('%Y-%m-%d %H:%M:%S')
        else:
            logger.info('获取上次执行时间失败，使用七天前时间')
            last_run_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time() - 7 * 24 * 60 * 60))
        logger.info(f'上次脚本执行时间: {last_run_time}, 本次脚本开始执行时间: {time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(refresh_start_time))}')
        get_device_need_replay_sql = get_device_need_replay.replace('{last_run_time}', last_run_time) \
            .replace('{refresh_start_time}', str(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(refresh_start_time)))) \
            .replace('{onedata_database}', onedata_database)
        logger.info(f'get_device_need_replay_sql: {get_device_need_replay_sql}')
        mysql_helper.execute(get_device_need_replay_sql)
        device_list = mysql_helper.fetchall()
        total_devices = len(device_list)
        logger.info(f'检测到要重跑的DEVICE数量为: {total_devices}')
        current_device_idx = 0

        # 从partition_address中获取特定ck地址:
        ck_address = args.jdbc_url.split('//')[1]
        addresses = [addr.strip() for addr in ck_address.split(',') if addr.strip()]
        
        partition_address = addresses[0]
        partition_address_config = {
            'host': partition_address.split(':')[0],
            'port': int(partition_address.split(':')[1]),
            'user': ck_user,
            'password': ck_password,
            'database': dim_database
        }
        partition_client = Client(**partition_address_config)
        

        for device in device_list:
            current_device_idx += 1
            try:
                current_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(int(time.time())))
                customer = device[0]
                factory = device[1]
                test_area = device[2]
                test_stage = device[3]
                device_id = device[4]
                partition_str = f"('{customer}','AUTO','{test_area}','{factory}')"
                logger.info(f"device: {device}")
                data_store_index = abs(hash(partition_str)) % len(addresses)
                logger.info(f"data_store_index: {data_store_index}")

                logger.info(f"开始处理第 {current_device_idx}/{total_devices} 个DEVICE: {customer}/{factory}/{test_area}/{test_stage}/{device_id}")

                insert_bin_sql = format_sql(insert_bin, dwd_database, dim_database, current_time, customer, factory, test_area, test_stage, device_id)
                insert_site_sql = format_sql(insert_site, dwd_database, dim_database, current_time, customer, factory, test_area, test_stage, device_id)
                insert_program_sql = format_sql(insert_program, dwd_database, dim_database, current_time, customer, factory, test_area, test_stage, device_id)
                delete_bin_sql = format_sql(delete_bin, dwd_database, dim_database, current_time, customer, factory, test_area, test_stage, device_id)
                delete_site_sql = format_sql(delete_site, dwd_database, dim_database, current_time, customer, factory, test_area, test_stage, device_id)
                delete_program_sql = format_sql(delete_program, dwd_database, dim_database, current_time, customer, factory, test_area, test_stage, device_id)

                try:
                    start_time = time.time()
                    partition_client.execute(insert_bin_sql, types_check=True)
                    insert_time = time.time()
                    partition_client.execute(delete_bin_sql, types_check=True)
                    end_time = time.time()
                    insert_duration = insert_time - start_time
                    delete_duration = end_time - insert_time
                except Exception as e:
                    logger.error(f'[{current_device_idx}/{total_devices}] dim_test_program_bin表 CUSTOMER: \'{customer}\', FACTORY: \'{factory}\', TEST_AREA: \'{test_area}\', TEST_STAGE: \'{test_stage}\', DEVICE_ID: \'{device_id}\' 更新失败！ \n{e}')

                try:
                    start_time = time.time()
                    partition_client.execute(insert_site_sql, types_check=True)
                    insert_time = time.time()
                    partition_client.execute(delete_site_sql, types_check=True)
                    end_time = time.time()
                    insert_duration = insert_time - start_time
                    delete_duration = end_time - insert_time
                except Exception as e:
                    logger.error(f'[{current_device_idx}/{total_devices}] dim_test_program_site表 CUSTOMER: \'{customer}\', FACTORY: \'{factory}\', TEST_AREA: \'{test_area}\', TEST_STAGE: \'{test_stage}\', DEVICE_ID: \'{device_id}\' 更新失败！ \n{e}')

                try:
                    start_time = time.time()
                    partition_client.execute(insert_program_sql, types_check=True)
                    insert_time = time.time()
                    partition_client.execute(delete_program_sql, types_check=True)
                    end_time = time.time()
                    insert_duration = insert_time - start_time
                    delete_duration = end_time - insert_time
                except Exception as e:
                    logger.error(f'[{current_device_idx}/{total_devices}] dim_test_program_test_item表 CUSTOMER: \'{customer}\', FACTORY: \'{factory}\', TEST_AREA: \'{test_area}\', TEST_STAGE: \'{test_stage}\', DEVICE_ID: \'{device_id}\' 更新失败！ \n{e}')

                logger.info(f"完成第 {current_device_idx}/{total_devices} 个DEVICE处理")

            except Exception as e:
                logger.error(f'处理DEVICE {customer}/{factory}/{test_area}/{test_stage}/{device_id} 失败！ \n{e}')

        logger.info(f"所有DEVICE处理完成，共处理 {total_devices} 个DEVICE")

    except Exception as e:
        logger.error(f'device_list获取失败！：{e}')

    finally:
        if partition_client is not None:
            partition_client.disconnect()

    refresh_end_time = time.time()
    refresh_duration = refresh_end_time - refresh_start_time
    logger.info(f'SYSTEM CODE: 1 dim表更新完成，耗时：{refresh_duration} 秒')

def main():
    parser = argparse.ArgumentParser(description='dim表每日定时更新')
    parser.add_argument('jdbc_url', help='ClickHouse JDBC URL (e.g., ***************************)')
    parser.add_argument('mysql_jdbc_url', help='MySQL JDBC URL (e.g., **********************)')
    parser.add_argument('mysql_user', help='MySQL 用户名')
    parser.add_argument('mysql_password', help='MySQL 密码')
    parser.add_argument('ck_user', help='ClickHouse 用户名')
    parser.add_argument('ck_password', help='ClickHouse 密码')
    parser.add_argument('dwd_database', help='ClickHouse DWD 数据库名称')
    parser.add_argument('dim_database', help='ClickHouse DIM 数据库名称')
    parser.add_argument('mysql_database', help='MySQL数据库名称')
    parser.add_argument('bpms_user', help='BPMS数据库用户名')
    parser.add_argument('bpms_password', help='BPMS数据库密码')
    parser.add_argument('bpms_database', help='BPMS数据库名称')
    parser.add_argument('--log-file', default=os.path.join(os.path.dirname(__file__), 'dim_refresh.log'), help='日志文件路径')

    args = parser.parse_args()

    # 解析ClickHouse JDBC URL
    ck_match = re.match(r'jdbc:clickhouse://([^:]+):(\d+)', args.jdbc_url)
    if not ck_match:
        print(f"错误: 无效的ClickHouse JDBC URL格式: {args.jdbc_url}", file=sys.stderr)
        sys.exit(3)

    ck_host = ck_match.group(1)
    ck_port = ck_match.group(2)

    # 解析MySQL JDBC URL
    mysql_match = re.match(r'jdbc:mysql://([^:]+):(\d+)', args.mysql_jdbc_url)
    if not mysql_match:
        print(f"错误: 无效的MySQL JDBC URL格式: {args.mysql_jdbc_url}", file=sys.stderr)
        sys.exit(4)

    mysql_host = mysql_match.group(1)
    mysql_port = mysql_match.group(2)

    logger = setup_logging(args.log_file)
    logger.info(f'ClickHouse host: {ck_host}, port: {ck_port}, MySQL host: {mysql_host}, port: {mysql_port}, mysql_user: {args.mysql_user}, ClickHouse dwd database: {args.dwd_database}, ClickHouse dim database: {args.dim_database}, MySQL database: {args.mysql_database}, bpms_user: {args.bpms_user}, bpms_password: {args.bpms_password}')

    try:
        # MySQL连接配置
        mysql_helper = MySQLHelper(
            host=mysql_host,
            port=mysql_port,
            mysql_user=args.mysql_user,
            mysql_password=args.mysql_password,
            database=args.mysql_database
        )
        mysql_helper.connect()
        # bpms连接配置
        bpms_helper = MySQLHelper(
            host=mysql_host,
            port=mysql_port,
            mysql_user=args.bpms_user,
            mysql_password=args.bpms_password,
            database=args.bpms_database
        )
        bpms_helper.connect()

        init(mysql_helper, bpms_helper, args.ck_user, args.ck_password, args.dwd_database, args.dim_database, logger, args)

    except Exception as e:
        logger.error(f"dim表每日定时更新时发生错误: {str(e)}", exc_info=True)
        sys.exit(2)
    finally:
        if mysql_helper:
            mysql_helper.disconnect()

if __name__ == '__main__':
    main()