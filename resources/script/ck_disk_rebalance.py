import argparse
import configparser
import logging
import os
import re
import sys
import traceback
from datetime import datetime

from clickhouse_driver import Client

def setup_logging(log_file):
    """设置日志配置"""
    logger = logging.getLogger('ck_disk_rebalance')

    # 清除已有处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    logger.setLevel(logging.DEBUG)  # 设置日志级别
    logger.propagate = False  # 禁用日志传播

    # 文件处理器
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.DEBUG)

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.DEBUG)

    # 格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

def get_all_server(clickhouse_config):
    clickhouse_connection_config = {
        'host': clickhouse_config['host'],
        'port': int(clickhouse_config['port']),
        'user': clickhouse_config['user'],
        'password': clickhouse_config['password']
    }
    clickhouse_client = Client(**clickhouse_connection_config)

    try:
        sql = f"""
            select host_name, port
            from system.clusters
            where cluster = 'cluster_3shards_1replicas'
            order by host_name, port
                """
        logger.info(f'执行sql：{sql}')
        res = clickhouse_client.execute(sql)
        return res
    finally:
        clickhouse_client.disconnect()

def get_all_table(clickhouse_client: Client, args_config):
    min_table_size_gb = float(args_config['min_table_size_gb'])
    sql = f"""
        select database, table, count(distinct disk_name) disk_cnt, round(sum(bytes_on_disk) / 1024 / 1024 / 1204, 8) size
        from system.parts
        where active
          and (database like 'dwd%' or database like 'dim%' or database like 'dws%' or database like 'ads%')
          and (table like 'dwd_%_local' or table like 'dim_%_local' or table like 'dws_%_local' or
               table like 'ads_%_local')
        group by database, table
        having size >= {min_table_size_gb}
        order by size desc
            """
    logger.info(f'执行sql：{sql}')
    res = clickhouse_client.execute(sql)
    return res

def get_disk_info(clickhouse_client: Client, database, table, print_info=False):
    sql = f"""
        select disk_name,
               round(sum(bytes_on_disk / 1024 / 1024 / 1024), 8) size,
               count(1)                                          part_cnt,
               count(distinct partition)                         partition_cnt
        from system.parts
        where active
          and database = '{database}'
          and table = '{table}'
        group by disk_name
        order by disk_name
            """
    logger.info(f'执行sql：{sql}')
    res = clickhouse_client.execute(sql)
    if print_info:
        logger.info('disk_name\tsize\tpart_cnt\tpartition_cnt')
        for disk_name, size, part_cnt, partition_cnt in res:
            logger.info(f'{disk_name}\t{size}\t{part_cnt}\t{partition_cnt}')
    return sorted([(disk_name, size) for disk_name, size, part_cnt, partition_cnt in res], key=lambda x: x[1],
                  reverse=True)

def get_part_move_info(clickhouse_client: Client, database, table, from_disk, move_size):
    sql = f"""
        select name, round(bytes_on_disk / 1024 / 1024 / 1024, 8) size
        from system.parts
        where active
          and database = '{database}'
          and table = '{table}'
          and disk_name = '{from_disk}'
          and size <= round({move_size} * 1.1, 8) and size > 0
        order by size desc
        limit 1000
            """
    logger.info(f'执行sql：{sql}')
    res = clickhouse_client.execute(sql)
    sum_size = 0.0
    move_parts = []
    if len(res) == 0:
        logger.info(f'没有符合条件的part {move_size}')
        return move_parts, sum_size
    for part_name, part_size in res:
        if round(sum_size + part_size, 8) <= round(move_size * 1.1, 8):
            sum_size = round(sum_size + part_size, 8)
            move_parts.append((part_name, part_size))
    return move_parts, sum_size

def execute_move_part(clickhouse_client: Client, database, table, from_disk, part, to_disk):
    sql = f"""
        alter table {database}.{table} move part '{part}' to disk '{to_disk}'
            """
    logger.info(f'执行sql：{sql}')
    try:
        clickhouse_client.execute(sql)
    except Exception as e:
        logger.error(f'执行出错：{traceback.format_exc()}')

def main():
    parser = argparse.ArgumentParser(description='ClickHouse磁盘均衡处理')
    parser.add_argument('jdbc_url', help='ClickHouse JDBC URL (e.g., ***************************)')
    parser.add_argument('user', help='ClickHouse用户名')
    parser.add_argument('password', help='ClickHouse密码')
    parser.add_argument('--database', default='default', help='ClickHouse数据库名称')
    parser.add_argument('--min-table-size-gb', type=float, default=10.0, help='最小表大小（GB）')
    parser.add_argument('--min-disk-size-gap-gb', type=float, default=5.0, help='最小磁盘大小差距（GB）')
    parser.add_argument('--log-file', default=os.path.join(os.path.dirname(__file__), 'ck_disk_rebalance.log'), help='日志文件路径')

    args = parser.parse_args()

    # 解析JDBC URL
    match = re.match(r'jdbc:clickhouse://([^:]+):(\d+)', args.jdbc_url)
    if not match:
        print(f"错误: 无效的JDBC URL格式: {args.jdbc_url}", file=sys.stderr)
        sys.exit(3)  # Use a different exit code for invalid URL format

    host = match.group(1)
    port = match.group(2)

    logger = setup_logging(args.log_file)
    logger.info(f'host: {host}, port: {port}, user: {args.user}, database: {args.database}')

    try:
        clickhouse_config = {
            'host': host,
            'port': port,
            'user': args.user,
            'password': args.password,
            'database': args.database
        }

        args_config = {
            'min_table_size_gb': args.min_table_size_gb,
            'min_disk_size_gap_gb': args.min_disk_size_gap_gb
        }

        for host_name, port in get_all_server(clickhouse_config):
            logger.info(f'开始处理 {host_name}:{port}')
            clickhouse_connection_config = {
                'host': host_name,
                'port': port,
                'user': clickhouse_config['user'],
                'password': clickhouse_config['password']
            }
            clickhouse_client = Client(**clickhouse_connection_config)
            min_disk_size_gap_gb = float(args_config['min_disk_size_gap_gb'])
            try:
                for database, table, disk_cnt, size in get_all_table(clickhouse_client, args_config):
                    logger.info(f'开始处理 {database} {table} {disk_cnt} {size}')
                    if (disk_cnt < 2):
                        logger.info(f'磁盘数小于2，不处理')
                        continue
                    for i in range(disk_cnt):
                        logger.info(f'开始第{i + 1}次平衡')
                        disk_info = get_disk_info(clickhouse_client, database, table, i == 0)
                        disk_sizes = ([disk[1] for disk in disk_info])
                        disk_size_avg = round(sum(disk_sizes) / len(disk_sizes), 8)
                        logger.info(f'disk平均值 {disk_size_avg}')
                        max_size_disk = disk_sizes[0]
                        min_size_disk = disk_sizes[len(disk_sizes) - 1]
                        disk_size_gap = round(max_size_disk - min_size_disk, 8)
                        if disk_size_gap < min_disk_size_gap_gb:
                            logger.info(f'最大与最小磁盘之间的差值 {disk_size_gap} 小于 {min_disk_size_gap_gb} 不处理')
                            break
                        move_out_size = round(max_size_disk - disk_size_avg, 8)
                        move_in_size = round(disk_size_avg - min_size_disk, 8)
                        move_size = min(move_out_size, move_in_size)
                        from_disk = disk_info[0][0]
                        to_disk = disk_info[len(disk_info) - 1][0]
                        logger.info(f'需要从 {from_disk} 移动 {move_size} 到 {to_disk}')
                        move_parts, sum_size = get_part_move_info(clickhouse_client, database, table, from_disk, move_size)
                        for part_name, part_size in move_parts:
                            logger.info(f'从 {from_disk} 移动 {part_name} ({part_size}) 到 {to_disk}')
                            execute_move_part(clickhouse_client, database, table, from_disk, part_name, to_disk)
                        logger.info(f'结束第{i + 1}次平衡')
                        get_disk_info(clickhouse_client, database, table, True)
            finally:
                clickhouse_client.disconnect()

    except Exception as e:
        logger.error(f"脚本执行出错: {traceback.format_exc()}")
        logger.error(f"脚本异常结束，时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        logger = setup_logging(os.path.join(os.path.dirname(__file__), 'ck_disk_rebalance.log'))
        logger.info(f"脚本开始执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        main()
        logger.info(f"SYSTEM CODE: 1 脚本执行成功，结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    except Exception as e:
        logger.error(f"脚本执行出错: {traceback.format_exc()}")
        logger.error(f"脚本异常结束，时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        sys.exit(1)
