# -*- coding: utf-8 -*-
import logging
import sys
import re
import configparser
import os
import argparse
import pandas as pd
import pymysql
import pymysql.cursors
from clickhouse_driver import Client


# 查询站点定义
stage_config_sql = """
        select config.factory as FACTORY,
               config.area as AREA,
               detail.step as STEP,
               config.order as AREA_ORDER,
               detail.step_order as STEP_ORDER,
               config.step_type as NEW_STEP_TYPE,
               config.step_type_order as STEP_TYPE_ORDER,
               detail.stage as NEW_STAGE,
               config.update_time as STAGE_PUBLISH_TIME
        from lotmanager.t_stage_config config
        join lotmanager.t_stage_config_detail detail on config.id = detail.config_id;
        """

# 查询工艺路线
product_process_sql = """
        SELECT pm.material_name as PART,
               ppd.device_name  as NEW_DEVICE_ID,
               ppdo.bin         as NEW_BIN_NUM,
               pps.step_name    as NEW_PROCESS_SITE,
               pp.publish_time  as PROCESS_PUBLISH_TIME
        FROM product.product_material AS pm
        LEFT JOIN product.product_process_device_output AS ppdo on pm.id = ppdo.product_material_id
        LEFT JOIN product.product_process_device_channel AS ppdc on ppdo.channel_id = ppdc.id
        LEFT JOIN product.product_process_device AS ppd ON ppdc.device_id = ppd.id
        LEFT JOIN product.product_process_step AS pps ON ppd.step_id = pps.id
        left join product.product_process pp on pp.id = pps.process_id
        WHERE pm.publish_status = 'published'
          and pp.publish_status = 'published';
        """

# 中间结果表
stage_cache_table = "lot_history_stage_config_cache"
process_cache_table = "lot_history_product_process_cache"
lot_history_staging_table = "dwd_lot_history_staging"

create_stage_cache_table = f"""
        CREATE TABLE IF NOT EXISTS {stage_cache_table} ON CLUSTER cluster_3shards_1replicas
        (
            FACTORY       String,
            AREA          String,
            STEP          String,
            AREA_ORDER Nullable(UInt32),
            STEP_ORDER Nullable(UInt32),
            NEW_STEP_TYPE String,
            STEP_TYPE_ORDER Nullable(UInt32),
            NEW_STAGE     String,
            STAGE_PUBLISH_TIME Nullable(DateTime)
        )
        ENGINE = Memory();
"""

create_process_cache_table = f"""
        CREATE TABLE IF NOT EXISTS {process_cache_table} ON CLUSTER cluster_3shards_1replicas
        (
            PART             String,
            NEW_DEVICE_ID    String,
            NEW_BIN_NUM      String,
            NEW_PROCESS_SITE String,
            PROCESS_PUBLISH_TIME Nullable(DateTime)
        )
        ENGINE = Memory();
"""

create_lot_history_staging_local = f"""
        CREATE TABLE IF NOT EXISTS {lot_history_staging_table}_local ON CLUSTER cluster_3shards_1replicas AS dwd_lot_history_local
            ENGINE = MergeTree PARTITION BY (CUSTOMER, DATA_SOURCE) ORDER BY (DEVICE_ID, FACTORY, AREA, LOT_TYPE, LOT_ID, WIP_REFRESH_TIME_KEY);
"""

create_lot_history_staging_cluster = f"""
        CREATE TABLE IF NOT EXISTS {lot_history_staging_table}_cluster ON CLUSTER cluster_3shards_1replicas AS dwd_lot_history_local
            ENGINE = Distributed(cluster_3shards_1replicas, currentDatabase(), {lot_history_staging_table}_local, rand());
"""

clickhouse_etl_sql = f"""
            INSERT INTO {lot_history_staging_table}_cluster
            SELECT
                wip.CUSTOMER,
                wip.SUB_CUSTOMER,
                wip.FILE_ID,
                wip.FILE_NAME,
                wip.WIP_TYPE,
                wip.DATA_SOURCE,
                wip.FACTORY,
                wip.FACTORY_SITE,
                wip.FAB,
                wip.FAB_SITE,
                wip.PURCHASE_ORDER,
                wip.WORKING_ORDER,
                wip.PRODUCT_ID,
                wip.LOCATION,
                coalesce(
                        CASE
                            WHEN wip.WIP_REFRESH_TIME > p.PROCESS_PUBLISH_TIME
                                THEN p.NEW_DEVICE_ID
                            ELSE NULL
                            END,
                        wip.DEVICE_ID
                ) AS DEVICE_ID,
                wip.DEVICE_VERSION,
                wip.PART,
                wip.LOT_TYPE,
                wip.AREA,
                coalesce(
                        CASE
                            WHEN wip.WIP_REFRESH_TIME > p.PROCESS_PUBLISH_TIME
                                THEN p.NEW_PROCESS_SITE
                            ELSE NULL
                            END,
                        wip.PROCESS_SITE
                ) AS PROCESS_SITE,
                coalesce(
                        CASE
                            WHEN wip.WIP_REFRESH_TIME > s.STAGE_PUBLISH_TIME
                                THEN s.NEW_STAGE
                            ELSE NULL
                            END,
                        wip.STAGE
                ) AS STAGE,
                wip.STEP,
                coalesce(
                        CASE
                            WHEN wip.WIP_REFRESH_TIME > s.STAGE_PUBLISH_TIME
                                THEN s.NEW_STEP_TYPE
                            ELSE NULL
                            END,
                        wip.STEP_TYPE
                ) AS STEP_TYPE,
                if(wip.WIP_REFRESH_TIME > s.STAGE_PUBLISH_TIME, s.STEP_TYPE_ORDER, NULL) STEP_TYPE_ORDER,
                wip.STEP_DESC,
                if(wip.WIP_REFRESH_TIME > a.STAGE_PUBLISH_TIME, a.AREA_ORDER, NULL) AREA_ORDER,
                if(wip.WIP_REFRESH_TIME > s.STAGE_PUBLISH_TIME, s.STEP_ORDER, NULL) STEP_ORDER,
                wip.STAGE_DESC,
                wip.TEST_PROGRAM,
                wip.TESTER_NAME,
                wip.LAST_STAGE,
                wip.LOT_ID,
                wip.LOT_STATUS,
                '' NCMR_NUM,
                '' NCMR_DISPOSAL,
                wip.QTY,
                wip.UNIT,
                coalesce(
                        CASE
                            WHEN wip.WIP_REFRESH_TIME > p.PROCESS_PUBLISH_TIME
                                THEN p.NEW_BIN_NUM
                            ELSE NULL
                            END,
                        wip.BIN_NUM
                ) AS BIN_NUM,
                wip.SBLOT_WAFER_LIST,
                wip.SBLOT_ID,
                wip.WAFER_LOT_ID,
                wip.VENDOR_STEP_NAME,
                wip.EVENT_COUNT,
                wip.EVENT_NAME,
                wip.HOLD_CODE,
                wip.HOLD_COMMENT,
                wip.HOLD_TIME,
                wip.HOLD_TYPE,
                wip.HOLD_REASON,
                wip.PACKAGE_TYPE,
                wip.PO_ESD,
                wip.PO_RECEIVE_TIME,
                wip.EVENT_TIME,
                wip.START_TIME,
                wip.END_TIME,
                wip.CYCLE_TIME_RECEIVE,
                wip.CYCLE_TIME_SHIP,
                wip.STAGE_START_TIME,
                wip.WIP_REFRESH_TIME,
                wip.WIP_REFRESH_TIME_KEY,
                wip.AGE,
                wip.END_LOT_FLAG,
                wip.LONG_ATTRIBUTE_SET,
                wip.STRING_ATTRIBUTE_SET,
                wip.FLOAT_ATTRIBUTE_SET,
                now() AS CREATE_TIME,
                CREATE_USER,
                UPLOAD_TIME,
                IS_DELETE
            FROM dwd_wip_detail_cluster AS wip FINAL
                     GLOBAL
                     LEFT JOIN
                 (select FACTORY, AREA, AREA_ORDER, min(STAGE_PUBLISH_TIME) STAGE_PUBLISH_TIME from {stage_cache_table} group by FACTORY, AREA, AREA_ORDER) AS a
                 ON wip.FACTORY = a.FACTORY
                     AND wip.AREA = a.AREA
                     GLOBAL
                     LEFT JOIN
                 {stage_cache_table} AS s
                 ON wip.FACTORY = s.FACTORY
                     AND wip.AREA = s.AREA
                     AND wip.STEP = s.STEP
                     GLOBAL
                     LEFT JOIN
                 {process_cache_table} AS p
                 ON wip.PART = p.PART
            where wip.IS_DELETE = 0
            """


# 配置日志
def setup_logging(log_file):
    """
    设置日志配置。

    :param log_file: 日志文件路径
    """
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)

    # 文件处理器
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.DEBUG)

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.DEBUG)

    # 格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger


class ClickHouseHelper:
    """处理ClickHouse数据库操作的类，持有持久连接。"""

    def __init__(self, host, port, user, password, database):
        """初始化ClickHouse连接"""
        self.host = host
        self.port = int(port)
        self.user = user
        self.password = password
        self.database = database
        self.client: Client
        self.logger = logging.getLogger(self.__class__.__name__)
        self._connect()

    def _connect(self):
        """创建ClickHouse连接。"""
        try:
            self.logger.info(f"尝试连接ClickHouse: {self.host}:{self.port}/{self.database}")
            # clickhouse_driver.Client 连接参数
            self.client = Client(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database
            )
            # 检查连接是否成功
            self.client.execute("SELECT 1")
            self.logger.info("ClickHouse连接成功")
        except Exception as e:
            self.logger.error(f"ClickHouse连接失败: {str(e)}")
            # 在连接失败时直接抛出异常，让调用者处理
            raise

    def insert_df(self, dataframe, table_name):
        """
        将DataFrame写入ClickHouse表。

        :param dataframe: 要写入的DataFrame
        :param table_name: 目标表名
        """
        if dataframe.empty:
            self.logger.info(f"DataFrame为空, 跳过写入 {table_name}")
            return

        if not self.client or not self.client.connection.connected:
            self.logger.warning("ClickHouse连接已断开或未连接，尝试重新连接...")
            self._connect()  # 如果连接断开，尝试重新连接

        try:
            self.logger.info(f"尝试写入 {len(dataframe)} 条数据到 {table_name}")
            # 构建INSERT语句
            columns = dataframe.columns.tolist()
            sql = f"INSERT INTO {table_name} ({','.join(columns)}) VALUES"

            # 将DataFrame转换为列表的字典，clickhouse_driver支持这种格式批量插入
            # to_dict(orient='records') 得到 [{col1: val1, col2: val2}, ...] 格式
            data = dataframe.to_dict(orient='records')

            # 执行批量插入
            self.client.execute(sql, data, types_check=True)  # types_check=True帮助类型检查

            self.logger.info(f"成功插入 {len(data)} 条数据到 {table_name}")
        except Exception as e:
            self.logger.error(f"写入ClickHouse数据失败到 {table_name}: {str(e)}")
            # 检查是否是连接相关的错误
            if "Connection refused" in str(e) or "Socket error" in str(e):
                self.logger.warning("ClickHouse连接可能已断开，标记需要重新连接...")
                self.client = None  # 标记连接已失效
            raise

    def execute_sql(self, sql):
        """
        执行非查询SQL语句 (如TRUNCATE, INSERT ... SELECT)。

        :param sql: SQL语句
        """
        if not self.client or not self.client:
            self.logger.warning("ClickHouse连接已断开或未连接，尝试重新连接...")
            self._connect()  # 如果连接断开，尝试重新连接

        try:
            self.logger.debug(f"执行ClickHouse SQL: {sql[:200]}...")
            self.client.execute(sql)
            self.logger.debug("ClickHouse SQL执行成功.")
        except Exception as e:
            self.logger.error(f"执行ClickHouse SQL失败: {str(e)}")
            self.logger.error(f"SQL: {sql}")
            # 检查是否是连接相关的错误
            if "Connection refused" in str(e) or "Socket error" in str(e):
                self.logger.warning("ClickHouse连接可能已断开，标记需要重新连接...")
                self.client = None
            raise

    def read(self, sql):
        try:
            data, columns = self.client.execute(
                sql, columnar=True, with_column_types=True)
            return pd.DataFrame({re.sub(r'\W', '_', col[0]): d for d, col in zip(data, columns)})
        except Exception as e:
            self.logger.error(f"执行Clickhouse read SQL失败: {str(e)}")
            self.logger.error(f"SQL: {sql}")
            raise

    def close(self):
        """关闭ClickHouse连接。"""
        if self.client and self.client.connection.connected:
            self.logger.info("关闭ClickHouse连接...")
            self.client.disconnect()
            self.logger.info("ClickHouse连接已关闭。")
        elif self.client:
            self.logger.info("ClickHouse客户端已初始化但未连接，无需关闭。")
        else:
            self.logger.info("ClickHouse客户端未初始化，无需关闭。")


class MySQLHelper:
    """处理MySQL数据库操作的类，持有持久连接。使用 pymysql。"""

    def __init__(self, host, port, user, password, database):
        """初始化MySQL连接"""
        self.host = host
        self.port = int(port)
        self.user = user
        self.password = password
        self.database = database
        self.connection: pymysql.connections.Connection
        self.logger = logging.getLogger(self.__class__.__name__)
        self._connect()

    def _connect(self):
        """创建MySQL连接。"""
        try:
            self.logger.info(f"尝试连接MySQL: {self.host}:{self.port}/{self.database}")
            self.connection = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor,
                autocommit=True
            )
            # 检查连接是否成功
            with self.connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            self.logger.info("MySQL连接成功")
        except pymysql.MySQLError as e:
            self.logger.error(f"MySQL连接失败: {str(e)}")
            raise

    def read_sql_to_df(self, sql):
        """
        执行查询并返回DataFrame。

        :param sql: 查询SQL语句
        :return: pandas DataFrame
        """
        try:
            self.connection.ping(reconnect=True)
            self.logger.debug(f"执行MySQL查询: {sql[:200]}...")
            with self.connection.cursor() as cursor:
                cursor.execute(sql)
                result = cursor.fetchall()
            df = pd.DataFrame(result)
            self.logger.info(f"MySQL查询结束, 返回 {len(df)} 条数据.")
            return df
        except pymysql.MySQLError as e:
            self.logger.error(f"执行MySQL查询失败: {str(e)}")
            self.logger.error(f"SQL: {sql}")
            raise
        except Exception as e:
            self.logger.error(f"执行MySQL查询发生未知错误: {str(e)}")
            self.logger.error(f"SQL: {sql}")
            raise

    def close(self):
        """关闭MySQL连接。"""
        if self.connection and self.connection.open:
            self.logger.info("关闭MySQL连接...")
            self.connection.close()
            self.logger.info("MySQL连接已关闭。")
        elif self.connection:
            self.logger.info("MySQL连接已初始化但未打开，无需关闭。")
        else:
            self.logger.info("MySQL连接未初始化，无需关闭。")


class LotHistoryProcessor:
    """处理数据以创建lot_history表的类，核心计算在ClickHouse中完成。"""

    def __init__(self, ck_helper: ClickHouseHelper, mysql_lotmanager_helper: MySQLHelper, mysql_product_helper: MySQLHelper):
        """
        初始化处理器。

        :param ck_helper: ClickHouseHelper实例
        :param mysql_lotmanager_helper: MySQLHelper实例用于lotmanager库
        :param mysql_product_helper: MySQLHelper实例用于product库
        """
        self.ck_helper = ck_helper
        self.mysql_lotmanager_helper = mysql_lotmanager_helper
        self.mysql_product_helper = mysql_product_helper
        self.logger = logging.getLogger(self.__class__.__name__)

    def create_clickhouse_cache(self):
        self.ck_helper.execute_sql(create_stage_cache_table)
        self.ck_helper.execute_sql(create_process_cache_table)
        self.ck_helper.execute_sql(create_lot_history_staging_local)
        self.ck_helper.execute_sql(create_lot_history_staging_cluster)
        self.logger.info("ClickHouse 中间表创建完成")

    def load_mysql_to_clickhouse_cache(self):
        """从 MySQL 加载数据到 ClickHouse 临时表。"""
        self.logger.info("开始从 MySQL 加载数据到 ClickHouse 中间表...")

        # 清空中间表
        self.logger.info("清空 ClickHouse 中间表...")
        try:
            self.ck_helper.execute_sql(f"TRUNCATE TABLE {stage_cache_table} ON CLUSTER cluster_3shards_1replicas")
            self.ck_helper.execute_sql(f"TRUNCATE TABLE {process_cache_table} ON CLUSTER cluster_3shards_1replicas")
            self.logger.info("中间表清空成功。")
        except Exception as e:
            self.logger.error(f"清空 ClickHouse 中间表失败: {str(e)}")
            raise

        self.logger.info("从 lotmanager 库查询站点定义并导入到 stage_cache_table...")
        stage_config_df = self.mysql_lotmanager_helper.read_sql_to_df(stage_config_sql)
        if not stage_config_df.empty:
            stage_config_df['STAGE_PUBLISH_TIME'] = pd.to_datetime(stage_config_df['STAGE_PUBLISH_TIME'], errors='coerce')
            stage_config_df = stage_config_df.dropna(subset=['STAGE_PUBLISH_TIME'])
            # 相同'FACTORY', 'AREA', 'STEP'取STAGE_PUBLISH_TIME最大的那条
            stage_config_df_idx = stage_config_df.groupby(['FACTORY', 'AREA', 'STEP'])['STAGE_PUBLISH_TIME'].idxmax()
            stage_config_result_df = stage_config_df.loc[stage_config_df_idx]
            self.ck_helper.insert_df(stage_config_result_df, stage_cache_table)
        else:
            self.logger.warning("从 lotmanager 未获取到站点定义数据，stage_cache_table 将为空。")

        self.logger.info("从 product 库查询工艺路线并导入到 process_cache_table...")
        product_process_df = self.mysql_product_helper.read_sql_to_df(product_process_sql)
        if not product_process_df.empty:
            product_process_df['PROCESS_PUBLISH_TIME'] = pd.to_datetime(product_process_df['PROCESS_PUBLISH_TIME'], errors='coerce')
            product_process_df = product_process_df.dropna(subset=['PROCESS_PUBLISH_TIME'])
            # 相同'PART'取PROCESS_PUBLISH_TIME最大的那条
            product_process_df_idx = product_process_df.groupby(['PART'])['PROCESS_PUBLISH_TIME'].idxmax()
            product_process_result_df = product_process_df.loc[product_process_df_idx]
            self.ck_helper.insert_df(product_process_result_df, process_cache_table)
        else:
            self.logger.warning("从 product 未获取到工艺路线数据，process_cache_table 将为空。")

        self.logger.info("MySQL 数据加载到 ClickHouse 中间表完成。")

    def execute_clickhouse_etl(self):
        """在 ClickHouse 中执行 ETL 计算并将结果写入 lot_history。"""
        self.logger.info("开始在 ClickHouse 中执行 ETL 计算...")

        try:
            self.logger.info("开始执行 ClickHouse ETL 计算并将结果写入 staging 表...")
            self.ck_helper.execute_sql(clickhouse_etl_sql)
            self.logger.info("ClickHouse ETL 计算及数据写入 staging 表成功。")
        except Exception as e:
            self.logger.error(f"执行 ClickHouse ETL 计算失败: {str(e)}")
            raise

        return True

    def execute_replace(self):

        # 将 staging 表中数据替换到目标表
        try:
            self.logger.info("开始执行 replace partition ...")
            partition_df = self.ck_helper.read(f"select distinct partition from system.parts where table = '{lot_history_staging_table}_local';")
            if len(partition_df) > 0:
                self.logger.info(partition_df)
                # 拼出REPLACE PARTITION语句
                replace_sqls = [f"ALTER TABLE dwd_lot_history_local ON CLUSTER cluster_3shards_1replicas REPLACE PARTITION {i} FROM {lot_history_staging_table}_local;" for i in
                                partition_df['partition']]
                for replace_sql in replace_sqls:
                    try:
                        self.ck_helper.execute_sql(replace_sql)
                        self.logger.info(f"执行 replace partition: {replace_sql} 成功")
                    except Exception as e:
                        self.logger.error(f"执行 replace partition: {replace_sql} 失败: {str(e)}")
                        raise
                self.logger.info("执行 replace partition 成功")
            else:
                self.logger.info("没有数据需要执行 replace partition")
        except Exception as e:
            self.logger.error(f"执行 replace partition 失败: {str(e)}")
            raise

        # 清空 staging 表
        self.logger.info("清空 staging 表...")
        try:
            self.ck_helper.execute_sql(f"TRUNCATE TABLE {lot_history_staging_table}_local ON CLUSTER cluster_3shards_1replicas")
            self.logger.info("staging表清空成功。")
        except Exception as e:
            self.logger.error(f"清空 staging 表失败: {str(e)}")
            raise
        return True

    def process(self):
        """主处理逻辑，调用子步骤。"""
        self.logger.info("开始处理 lot_history 表 (ClickHouse ETL 模式)...")

        wip_detail_cnt = self.ck_helper.read("select count(*) cnt from dwd_wip_detail_cluster")['cnt'][0]
        self.logger.info(f"wip_detail cnt: {wip_detail_cnt}")
        if wip_detail_cnt == 0:
            self.logger.warning(f"wip_detail 表没有数据，不计算 lot_history")
            return True
        try:
            # 0. 创建 Clickhouse 中间表
            self.create_clickhouse_cache()

            # 1. 从 MySQL 加载数据到 ClickHouse 中间表
            self.load_mysql_to_clickhouse_cache()

            # 2. 在 ClickHouse 中执行 ETL 计算并将结果写入 staging 表
            self.execute_clickhouse_etl()

            # 3. 在 Clickhouse 中执行 replace partition
            self.execute_replace()

            return True

        except Exception as e:
            self.logger.error(f"处理 lot_history 时发生错误: {str(e)}", exc_info=True)
            return False


def main():
    parser = argparse.ArgumentParser(description='Lot History ETL Processing')
    parser.add_argument('jdbc_url', help='ClickHouse JDBC URL (e.g., ***************************)')
    parser.add_argument('mysql_jdbc_url', help='MySQL JDBC URL (e.g., **********************)')
    parser.add_argument('ck_user', help='ClickHouse username')
    parser.add_argument('ck_password', help='ClickHouse password')
    parser.add_argument('dwd_database', help='ClickHouse database')
    parser.add_argument('mysql_user', help='MySQL lotmanager username')
    parser.add_argument('mysql_password', help='MySQL lotmanager password')
    parser.add_argument('lotmanager_database', help='MySQL lotmanager database')
    parser.add_argument('product_user', help='MySQL product username')
    parser.add_argument('product_password', help='MySQL product password')
    parser.add_argument('product_database', help='MySQL product database')
    parser.add_argument('--log-file', default='/home/<USER>/deploy/datahub/bpms/cron/lot_history.log', help='Log file path')

    args = parser.parse_args()

    # 解析ClickHouse JDBC URL
    ck_match = re.match(r'jdbc:clickhouse://([^:]+):(\d+)', args.jdbc_url)
    if not ck_match:
        print(f"错误: 无效的ClickHouse JDBC URL格式: {args.jdbc_url}", file=sys.stderr)
        sys.exit(3)

    ck_host = ck_match.group(1)
    ck_port = ck_match.group(2)

    # 解析MySQL JDBC URL
    mysql_match = re.match(r'jdbc:mysql://([^:]+):(\d+)', args.mysql_jdbc_url)
    if not mysql_match:
        print(f"错误: 无效的MySQL JDBC URL格式: {args.mysql_jdbc_url}", file=sys.stderr)
        sys.exit(4)

    mysql_host = mysql_match.group(1)
    mysql_port = mysql_match.group(2)

    # 配置日志
    logger = setup_logging(args.log_file)

    ck_helper = None
    mysql_lotmanager_helper = None
    mysql_product_helper = None

    try:
        # 初始化数据库连接 Helper
        ck_helper = ClickHouseHelper(
            host=ck_host,
            port=ck_port,
            user=args.ck_user,
            password=args.ck_password,
            database=args.dwd_database
        )

        mysql_lotmanager_helper = MySQLHelper(
            host=mysql_host,
            port=mysql_port,
            user=args.mysql_user,
            password=args.mysql_password,
            database=args.lotmanager_database
        )

        mysql_product_helper = MySQLHelper(
            host=mysql_host,
            port=mysql_port,
            user=args.product_user,
            password=args.product_password,
            database=args.product_database
        )

        # 初始化处理器并执行处理逻辑
        processor = LotHistoryProcessor(ck_helper, mysql_lotmanager_helper, mysql_product_helper)
        result = processor.process()
        logger.info(f'SYSTEM CODE: 1 lot_history表更新完成')
        sys.exit(0 if result else 1)

    except Exception as e:
        logger.error(f"主程序捕获到未处理的错误: {str(e)}", exc_info=True)
        sys.exit(2)
    finally:
        if ck_helper:
            ck_helper.close()
        if mysql_lotmanager_helper:
            mysql_lotmanager_helper.close()
        if mysql_product_helper:
            mysql_product_helper.close()


if __name__ == "__main__":
    main()


