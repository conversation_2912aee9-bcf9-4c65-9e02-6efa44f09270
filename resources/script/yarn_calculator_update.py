# -*- coding: utf-8 -*-
import argparse
from datetime import datetime
import os
import time
import traceback

import requests
import json
import logging
import sys
from requests.auth import HTTPBasicAuth

target_calculator = "org.apache.hadoop.yarn.util.resource.DominantResourceCalculator"

# 请求头
headers = {
    "X-Requested-By": "ambari",
    "Content-Type": "application/json"
}


def setup_logging(log_file):
    """设置日志配置"""
    logger = logging.getLogger('yarn_calculator_update')

    # 清除已有处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    logger.setLevel(logging.DEBUG)  # 设置日志级别
    logger.propagate = False  # 禁用日志传播

    # 文件处理器
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.DEBUG)

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.DEBUG)

    # 格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger


def get_cluster_name(ambari_server, ambari_user, ambari_pass):
    """获取集群名称"""
    url = f"{ambari_server}/api/v1/clusters"
    try:
        response = requests.get(url, auth=HTTPBasicAuth(ambari_user, ambari_pass), headers=headers)
        response.raise_for_status()
        clusters = response.json()
        if "items" in clusters and clusters["items"]:
            return clusters["items"][0]["Clusters"]["cluster_name"]
        raise Exception("未找到任何集群配置")
    except Exception as e:
        logger.info(f"获取集群名称失败: {str(e)}")
        return None


def get_current_calculator(ambari_server, ambari_user, ambari_pass, cluster_name):
    """获取当前Calculator配置"""
    try:
        url = f"{ambari_server}/api/v1/clusters/{cluster_name}?fields=Clusters/desired_configs"
        response = requests.get(url, auth=HTTPBasicAuth(ambari_user, ambari_pass))
        response.raise_for_status()
        desired_configs = response.json()["Clusters"]["desired_configs"]
        tag = desired_configs["capacity-scheduler"]["tag"]

        url = f"{ambari_server}/api/v1/clusters/{cluster_name}/configurations?type=capacity-scheduler&tag={tag}"
        response = requests.get(url, auth=HTTPBasicAuth(ambari_user, ambari_pass))
        response.raise_for_status()
        props = response.json()["items"][0]["properties"]
        current_value = props.get("yarn.scheduler.capacity.resource-calculator", "")
        return current_value
    except Exception as e:
        logger.info(f"获取当前Calculator配置失败: {str(e)}")
        return None


def get_full_capacity_scheduler_config(ambari_server, ambari_user, ambari_pass, cluster_name):
    """
    获取当前capacity-scheduler的全部配置（包括队列、ACL等所有属性）
    :param cluster_name: 集群名称
    :return: 完整配置字典（格式同Ambari API返回的结构），失败返回None
    """
    try:
        # 1. 获取当前生效的配置标签（tag）
        desired_config_url = f"{ambari_server}/api/v1/clusters/{cluster_name}?fields=Clusters/desired_configs"
        response = requests.get(
            desired_config_url,
            auth=HTTPBasicAuth(ambari_user, ambari_pass),
            headers=headers
        )
        response.raise_for_status()
        desired_configs = response.json()["Clusters"]["desired_configs"]
        current_tag = desired_configs["capacity-scheduler"]["tag"]

        # 2. 通过tag获取完整的capacity-scheduler配置
        config_url = f"{ambari_server}/api/v1/clusters/{cluster_name}/configurations?type=capacity-scheduler&tag={current_tag}"
        response = requests.get(
            config_url,
            auth=HTTPBasicAuth(ambari_user, ambari_pass),
            headers=headers
        )
        response.raise_for_status()

        # 3. 返回Ambari API的原始配置结构（包含properties、tag等元数据）
        return response.json()["items"][0]

    except Exception as e:
        logger.info(f"获取完整capacity-scheduler配置失败: {str(e)}")
        return None


def update_calculator_config(ambari_server, ambari_user, ambari_pass, cluster_name):
    """更新Calculator配置为Dominant Resource Calculator"""
    url = f"{ambari_server}/api/v1/clusters/{cluster_name}/configurations"
    try:
        # 1. 获取当前全部配置
        current_config = get_full_capacity_scheduler_config(ambari_server, ambari_user, ambari_pass, cluster_name)
        if not current_config:
            return False

        # 2. 仅更新Calculator字段，保留其他配置
        current_config["properties"]["yarn.scheduler.capacity.resource-calculator"] = target_calculator

        # 3. 提交合并后的配置
        new_config = {
            "type": "capacity-scheduler",
            "tag": f"version{int(time.time()) * 1000}",
            "properties": current_config["properties"]  # 使用完整配置
        }

        # 提交新配置
        response = requests.post(
            url,
            auth=HTTPBasicAuth(ambari_user, ambari_pass),
            headers=headers,
            data=json.dumps(new_config)
        )
        response.raise_for_status()

        # 应用新配置
        apply_url = f"{ambari_server}/api/v1/clusters/{cluster_name}"
        apply_data = {
            "Clusters": {
                "desired_config": {
                    "type": "capacity-scheduler",
                    "tag": new_config["tag"]
                }
            }
        }
        apply_response = requests.put(
            apply_url,
            auth=HTTPBasicAuth(ambari_user, ambari_pass),
            headers=headers,
            json=apply_data
        )
        apply_response.raise_for_status()
        logger.info("配置已成功更新并应用！")
        return True
    except Exception as e:
        logger.info(f"更新Calculator配置失败: {str(e)}")
        return False


def main():
    parser = argparse.ArgumentParser(description='调整Yarn Calculator配置为Dominant')
    parser.add_argument('ambari_server', help='Ambari服务url,如http://gdp01:8080')
    parser.add_argument('ambari_user', help='Ambari管理员用户名')
    parser.add_argument('ambari_pass', help='Ambari管理员密码')
    parser.add_argument('--log-file', default=os.path.join(os.path.dirname(__file__), 'yarn_calculator_update.log'),
                        help='日志文件路径')

    args = parser.parse_args()

    logger = setup_logging(args.log_file)
    logger.info(f'ambari_server: {args.ambari_server}, ambari_user: {args.ambari_user}')

    # 1. 获取集群名称
    cluster_name = get_cluster_name(args.ambari_server, args.ambari_user, args.ambari_pass)
    if not cluster_name:
        return

    logger.info(f"当前集群名称: {cluster_name}")

    # 2. 获取当前Calculator配置
    current_calculator = get_current_calculator(args.ambari_server, args.ambari_user, args.ambari_pass, cluster_name)
    if not current_calculator:
        return

    logger.info(f"当前Calculator配置: {current_calculator}")

    # 3. 检查并更新配置
    if current_calculator != target_calculator:
        logger.info("当前配置不是Dominant Resource Calculator，准备更新...")
        if update_calculator_config(args.ambari_server, args.ambari_user, args.ambari_pass, cluster_name):
            logger.info("配置更新成功！")
        else:
            logger.info("配置更新失败！")
    else:
        logger.info("当前已经是Dominant Resource Calculator配置，无需更新")


if __name__ == "__main__":
    try:
        logger = setup_logging(os.path.join(os.path.dirname(__file__), 'yarn_calculator_update.log'))
        logger.info(f"脚本开始执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        main()
        logger.info(f"SYSTEM CODE: 1 脚本执行成功，结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    except Exception as e:
        logger.error(f"脚本执行出错: {traceback.format_exc()}")
        logger.error(f"脚本异常结束，时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        sys.exit(1)
