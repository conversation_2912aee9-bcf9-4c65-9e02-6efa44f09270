# coding=utf-8
import logging
import time
import os
import sys
from clickhouse_driver import Client
import argparse
import re

table_name = 'dim_optimize'

def setup_logging(log_file):
    """配置日志记录"""
    logger = logging.getLogger('dim_optimize')

    # 清除已有处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    logger.setLevel(logging.DEBUG)  # 设置日志级别
    logger.propagate = False  # 禁用日志传播
    # 创建文件处理器
    file_handler = logging.FileHandler(log_file, mode='w')
    file_handler.setLevel(logging.DEBUG)

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)

    # 定义日志格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器到日志记录器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

def init(ck_user, ck_password, dim_database, logger, args):
    optimize_start_time = time.time()
    
    if not logger.handlers:
        logger.setLevel(logging.DEBUG)
        file_handler = logging.FileHandler(args.log_file)
        file_handler.setLevel(logging.DEBUG)
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

    client = None
    try:
        # 从jdbc_url中获取CK地址:
        ck_address = args.jdbc_url.split('//')[1]
        addresses = [addr.strip() for addr in ck_address.split(',') if addr.strip()]
        
        # 使用第一个地址连接
        address = addresses[0]
        address_config = {
            'host': address.split(':')[0],
            'port': int(address.split(':')[1]),
            'user': ck_user,
            'password': ck_password,
            'database': dim_database
        }
        client = Client(**address_config)
        
        logger.info(f"开始对三个dim表进行OPTIMIZE操作")
        
        # 定义三个需要OPTIMIZE的表
        tables_to_optimize = [
            'dim_test_program_bin_local',
            'dim_test_program_site_local',
            'dim_test_program_test_item_local'
        ]
        
        # 对每个表执行OPTIMIZE操作
        for table in tables_to_optimize:
            try:
                start_time = time.time()
                optimize_sql = f"OPTIMIZE TABLE {dim_database}.{table} ON cluster cluster_3shards_1replicas"
                logger.info(f"开始执行: {optimize_sql}")
                client.execute(optimize_sql)
                end_time = time.time()
                duration = end_time - start_time
                logger.info(f"表 {table} OPTIMIZE完成，耗时: {duration} 秒")
            except Exception as e:
                logger.error(f"表 {table} OPTIMIZE失败: {e}")
                
        logger.info(f"所有dim表OPTIMIZE操作完成")

    except Exception as e:
        logger.error(f'执行OPTIMIZE操作时发生错误: {e}')

    finally:
        if client is not None:
            client.disconnect()

    optimize_end_time = time.time()
    optimize_duration = optimize_end_time - optimize_start_time
    logger.info(f'SYSTEM CODE: 1 dim表OPTIMIZE完成，耗时：{optimize_duration} 秒')

def main():
    parser = argparse.ArgumentParser(description='dim表定时OPTIMIZE')
    parser.add_argument('jdbc_url', help='ClickHouse JDBC URL (e.g., ***************************)')
    parser.add_argument('ck_user', help='ClickHouse 用户名')
    parser.add_argument('ck_password', help='ClickHouse 密码')
    parser.add_argument('dim_database', help='ClickHouse DIM 数据库名称')
    parser.add_argument('--log-file', default=os.path.join(os.path.dirname(__file__), 'dim_optimize.log'), help='日志文件路径')

    args = parser.parse_args()

    # 解析ClickHouse JDBC URL
    ck_match = re.match(r'jdbc:clickhouse://([^:]+):(\d+)', args.jdbc_url)
    if not ck_match:
        print(f"错误: 无效的ClickHouse JDBC URL格式: {args.jdbc_url}", file=sys.stderr)
        sys.exit(3)

    ck_host = ck_match.group(1)
    ck_port = ck_match.group(2)

    logger = setup_logging(args.log_file)
    logger.info(f'ClickHouse host: {ck_host}, port: {ck_port}, ClickHouse dim database: {args.dim_database}')

    try:
        init(args.ck_user, args.ck_password, args.dim_database, logger, args)

    except Exception as e:
        logger.error(f"dim表定时OPTIMIZE时发生错误: {str(e)}", exc_info=True)
        sys.exit(2)

if __name__ == '__main__':
    main()