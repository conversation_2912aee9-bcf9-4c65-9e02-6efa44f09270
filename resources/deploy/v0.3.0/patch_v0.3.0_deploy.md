## 下载并传输部署包

```shell
wget http://riot11.guwave.com/deploy/onedata/bpms/v0.3.0/bpms-app-0.3.0.tar
wget http://riot11.guwave.com/deploy/onedata/dataware/v2.6.0/site-packages.tar.gz
wget http://riot11.guwave.com/deploy/onedata/dataware/v2.6.0/lib-dynload.zip

# 把下载下来的bpms-app-0.3.0.tar部署包传输到待部署的服务器
# 登录待部署的服务器
mkdir -p ~/deploy/onedata/bpms/bpms-app/logs
cd ~/deploy/onedata/bpms/bpms-app
# 把下载下来的site-packages.tar.gz和lib-dynload.zip传到此处
unzip lib-dynload.zip
tar xf site-packages.tar.gz
# 删除前上个版本的部署包
rm bpms-app-0.2.0.tar
# 把下载下来的bpms-app-0.3.0.tar传到此处
sudo docker load -i bpms-app-0.3.0.tar
```

## 执行patch脚本
- resources/sql/mysql/v0.3.0/patch/patch_v0.3.0_schema.sql
- 
下列mysql脚本需要root权限，请联系运维执行

**修改对应库名及内容**
${MYSQL_DB_USER_ADMIN} 修改为客户对应的user_admin库
${ACCESS_ADDR} 修改为前端页面访问地址，如http://riot82.guwave.com:28888

- resources/sql/mysql/v0.3.0/patch/patch_v0.3.0_init_useradmin.sql

## Kill任务

```shell
sudo docker stop $(sudo docker ps -q --filter "name=bpms-app")
sudo docker rm $(sudo docker ps -q --filter "name=bpms-app" --filter "status=exited")
```

## 配置文件改动：

```shell
cd  ~/deploy/onedata/bpms/bpms-app
cp base.0.2.0.properties base.0.3.0.properties
vi base.0.3.0.properties
```

### 文件末尾新增如下配置：

##### ***注意修改 以下 gdp.file 的几个配置***

```

# hdfs
gdp.file.hdfsMode=HA
gdp.file.hdfsUrl=hdfs://riot11.guwave.com:8020,hdfs://riot12.guwave.com:8020
gdp.file.hdfsUser=glory

# scheduling
task.scheduling.pool.size=1
scheduler.polling.milliseconds=5000

adapter.python.install.path=/opt/python38/bin/python3

# siteNum校验,filter(siteNum != 255)
settings.siteNumFilterFlag=false

# keyFields
handler.cp.keyFields=deviceId,testArea,lotType,testStage,lotId,waferId,waferNo,startT,finishT
handler.ft.keyFields=deviceId,testArea,lotType,testStage,lotId,sblotId,startT,finishT

# clearRulePreview parse file threadCnt
clearRulePreview.threadCnt=5
```



## 启动项目

- 先检查好bpms-app-0.2.0的服务都已经kill了

```shell
cd ~/deploy/onedata/bpms/bpms-app && mkdir -p logs
sudo docker run -d \
  --name bpms-app \
  -e JAVA_OPTS="-Xmx4096m -XX:MaxPermSize=1024m -Duser.home=/home/<USER>" \
  -e GROUP_ID=$(id `whoami` -g) \
  -e ENVIRON_GROUP=$(getent group $(id `whoami` -g) | cut -d: -f1) \
  -e USER_ID=$(id `whoami` -u) \
  -e ENVIRON_USER=`whoami` \
  --hostname `hostname` \
  --network=host \
  -ti \
  -v /etc/hosts:/etc/hosts:ro \
  -v ~/deploy/onedata/bpms/bpms-app/base.0.3.0.properties:/home/<USER>/deploy/datahub/bpms/bpms-app/bpms-app-0.3.0/properties/bpms-app.properties \
  -v ~/deploy/onedata/bpms/bpms-app/logs:/home/<USER>/deploy/datahub/bpms/bpms-app/bpms-app-0.3.0/logs \
  -v ~/deploy/onedata/bpms/bpms-app/site-packages:/opt/python38/lib/python3.8/site-packages \
  -v ~/deploy/onedata/bpms/bpms-app/lib-dynload:/opt/python38/lib/python3.8/lib-dynload \
  bpms/bpms-app:0.3.0
```