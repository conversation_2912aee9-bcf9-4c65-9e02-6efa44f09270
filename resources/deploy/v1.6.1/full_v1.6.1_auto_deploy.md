**大数据模块部署&升级顺序：1.next-compute 2.linkx 3.dataware 4.yms 5.rule 6.bpms 7.dataset-etl**

### 文件上传到登录待部署的服务器
```shell
# 把下载下来的bpms-app-1.6.1.tar部署包传输到待部署的服务器
# 登录待部署的服务器
mkdir -p ~/deploy/onedata/bpms/bpms-app/logs
mkdir -p ~/deploy/onedata/bpms/deploy/
cd ~/deploy/onedata/bpms/deploy/
wget http://*************:80/deploy/onedata/bpms/v1.6.1/1.6.1_bpms.zip
unzip 1.6.1_bpms.zip
cd 1.6.1
wget http://riot11.guwave.com/deploy/onedata/dataware/v2.6.0/site-packages.tar.gz
wget http://riot11.guwave.com/deploy/onedata/dataware/v2.6.0/lib-dynload.zip
```

### 开始部署
- 以下命令需要提前安装sshpass命令
```shell
cd ~/deploy/onedata/bpms/deploy/1.6.1/
bash deploy.sh 1.6.1 2>&1 | tee deploy_1.6.1.log
```