## 文件上传到登录待部署的服务器
```shell
# 把下载下来的bpms-app-1.6.3.tar部署包传输到待部署的服务器
# 登录待部署的服务器
mkdir -p ~/deploy/onedata/bpms/bpms-app/logs
mkdir -p ~/deploy/onedata/bpms/deploy/
cd ~/deploy/onedata/bpms/deploy/
wget http://minio.dev.guwave.com:19000/bucketdevops/app_repo/bigdata/bpms/1.6.3_bpms.zip
unzip 1.6.3_bpms.zip
cd 1.6.3
wget http://minio.dev.guwave.com:19000/bucketdevops/app_repo/bigdata/lib/site-packages.tar.gz
wget http://minio.dev.guwave.com:19000/bucketdevops/app_repo/bigdata/lib/lib-dynload.zip
```

### 开始部署
- 以下命令需要提前安装sshpass命令
```shell
cd ~/deploy/onedata/bpms/deploy/1.6.3/
bash deploy.sh 1.6.3 2>&1 | tee deploy_1.6.3.log
```
