### Mysql中停止bpms库的重播任务
```mysql
UPDATE bpms.bz_module_scheduled_control_switch SET stop_flag = 1, update_time = now();
```

## 部署

### 文件上传到登录待部署的服务器
```shell
# 登录待部署的服务器
mkdir -p ~/deploy/onedata/bpms/bpms-app/logs
mkdir -p ~/deploy/onedata/bpms/upgrade
cd ~/deploy/onedata/bpms/upgrade
wget http://minio.dev.guwave.com:19000/bucketdevops/app_repo/bigdata/bpms/1.6.3_bpms.zip
unzip 1.6.3_bpms.zip
cd 1.6.3
```

### 修改配置
无
```shell
# cd ~/deploy/onedata/properties/
# vim bigdata-common.properties
```

### 开始部署
#### 确认任务都已结束
```mysql
# 查询结果必须都为0，才执行下面的操作
select id,create_time,update_time from bpms.bz_data_replay_record where process_status = 'PROCESSING' and replay_type in ('DELETE_FILE','DELETE_LOT') order by update_time;
```

- 以下命令需要提前安装sshpass命令
```shell
cd ~/deploy/onedata/bpms/upgrade/1.6.3/
bash upgrade.sh 1.6.2 1.6.3 2>&1 | tee upgrade_1.6.3.log
```

### 检查应用状态
```shell
sudo docker ps -a | grep bpms
```

## 结束部署
### Mysql中启动任务

- 启动bpms-app库的入库任务（未升级完成时可以不打开开关）

```mysql
UPDATE bpms.bz_module_scheduled_control_switch SET stop_flag = 0, update_time = now();
```
