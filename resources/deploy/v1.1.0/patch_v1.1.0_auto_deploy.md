## 准备工作
- ### Mysql中停止bpms库的重播任务
```mysql
UPDATE bpms.bz_module_scheduled_control_switch SET stop_flag = 1, update_time = now();
```
## 下载并传输部署包

```shell
# 登录待部署的服务器
mkdir -p ~/deploy/onedata/bpms/bpms-app/logs
mkdir -p ~/deploy/onedata/bpms/upgrade
cd ~/deploy/onedata/bpms/upgrade
wget http://riot11.guwave.com/deploy/onedata/bpms/v1.1.0/1.1.0.zip
unzip 1.1.0.zip
cd 1.1.0
```

## 修改配置内容
```shell
cd ~/deploy/onedata/bpms/upgrade/1.1.0
vim upgrade.properties
```
- 根据实际情况修改配置

### 开始部署
#### 确认任务已结束
## 确认bpms库的删除任务都已结束
```mysql
# 查询结果必须都为0，才执行下面的操作
select id,create_time,update_time from bpms.bz_data_replay_record where process_status = 'PROCESSING' and replay_type in ('DELETE_FILE','DELETE_LOT') order by update_time;
```

- 以下命令需要提前安装sshpass命令
```shell
bash upgrade.sh 1.0.0 1.1.0 2>&1 | tee upgrade_1.1.0.log
```

### 检查应用状态
```shell
sudo docker ps -a | grep bpms
```

## 结束部署
### Mysql中启动任务

- 启动bpms-app库的入库任务（未升级完成时可以不打开开关）

```mysql
UPDATE bpms.bz_module_scheduled_control_switch SET stop_flag = 0, update_time = now();
```
