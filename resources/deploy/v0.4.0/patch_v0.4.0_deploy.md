## 下载并传输部署包

```shell
wget http://riot11.guwave.com/deploy/onedata/bpms/v0.4.0/bpms-app-0.4.0.tar

# 把下载下来的bpms-app-0.4.0.tar部署包传输到待部署的服务器
# 登录待部署的服务器
mkdir -p ~/deploy/onedata/bpms/bpms-app/logs
cd ~/deploy/onedata/bpms/bpms-app
# 删除前上个版本的部署包
rm bpms-app-0.3.0.tar
# 把下载下来的bpms-app-0.4.0.tar传到此处
sudo docker load -i bpms-app-0.4.0.tar
```

## Kill任务

```shell
sudo docker stop $(sudo docker ps -q --filter "name=bpms-app")
sudo docker rm $(sudo docker ps -q --filter "name=bpms-app" --filter "status=exited")
```

## 配置文件改动：

```shell
cd  ~/deploy/onedata/bpms/bpms-app
cp base.0.3.0.properties base.0.4.0.properties
```

## 启动项目

- 先检查好bpms-app-0.3.0的服务都已经kill了

```shell
cd ~/deploy/onedata/bpms/bpms-app && mkdir -p logs
sudo docker run -d \
  --name bpms-app \
  -e JAVA_OPTS="-Xmx4096m -XX:MaxPermSize=1024m -Duser.home=/home/<USER>" \
  -e GROUP_ID=$(id `whoami` -g) \
  -e ENVIRON_GROUP=$(getent group $(id `whoami` -g) | cut -d: -f1) \
  -e USER_ID=$(id `whoami` -u) \
  -e ENVIRON_USER=`whoami` \
  --hostname `hostname` \
  --network=host \
  -ti \
  -v /etc/hosts:/etc/hosts:ro \
  -v ~/deploy/onedata/bpms/bpms-app/base.0.4.0.properties:/home/<USER>/deploy/datahub/bpms/bpms-app/bpms-app-0.4.0/properties/bpms-app.properties \
  -v ~/deploy/onedata/bpms/bpms-app/logs:/home/<USER>/deploy/datahub/bpms/bpms-app/bpms-app-0.4.0/logs \
  -v ~/deploy/onedata/bpms/bpms-app/site-packages:/opt/python38/lib/python3.8/site-packages \
  -v ~/deploy/onedata/bpms/bpms-app/lib-dynload:/opt/python38/lib/python3.8/lib-dynload \
  bpms/bpms-app:0.4.0
```