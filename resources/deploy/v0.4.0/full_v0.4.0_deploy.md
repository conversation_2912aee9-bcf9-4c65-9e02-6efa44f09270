## 准备工作

### nginx添加代理（运维操作）
proxy_pass需要替换为bpms-app的部署机器
```shell
location /bpms-api/ {
    client_max_body_size 8092M;
    proxy_connect_timeout 1200;
    proxy_send_timeout 1200;
    proxy_read_timeout 1200;
    proxy_pass http://192.168.2.82:16666/;
}
```

## 执行mysql full脚本
需要运维创建用户
用户可查询库为：bpms

```properties
user:bpms
password:bpms@guwave
```

执行顺序如下
* resources/sql/mysql/v0.4.0/full/schema.sql
* resources/sql/mysql/v0.4.0/full/init_data.sql

下列mysql脚本需要root权限，请联系运维执行

**修改对应库名及内容**
${MYSQL_DB_USER_ADMIN} 修改为客户对应的user_admin库
${ACCESS_ADDR}  修改为前端页面访问地址，如http://riot82.guwave.com:28888

* resources/sql/mysql/v0.4.0/full/init_useradmin.sql

## 文件上传

```shell
wget http://riot11.guwave.com/deploy/onedata/bpms/v0.4.0/bpms-app-0.4.0.tar
wget http://riot11.guwave.com/deploy/onedata/dataware/v2.6.0/site-packages.tar.gz
wget http://riot11.guwave.com/deploy/onedata/dataware/v2.6.0/lib-dynload.zip

# 把下载下来的bpms-app-0.4.0.tar部署包传输到待部署的服务器
# 登录待部署的服务器
mkdir -p ~/deploy/onedata/bpms/bpms-app/logs
cd ~/deploy/onedata/bpms/bpms-app
# 把下载下来的site-packages.tar.gz和lib-dynload.zip传到此处
unzip lib-dynload.zip
tar xf site-packages.tar.gz
# 把下载下来的bpms-app-0.4.0.tar传到此处
sudo docker load -i bpms-app-0.4.0.tar
```

## 配置文件改动

### bpms-app
```shell
cd ~/deploy/onedata/bpms/bpms-app
vim base.0.4.0.properties
# 按照不同客户的实际情况来修改
```
```properties
# server
serverAddress=0.0.0.0
serverPort=16666

# spring doc config
api-docs.enabled=false

# database config
database.address=riot41.guwave.com:3307
database.name.bpms=bpms
database.username.bpms=bpms
database.password.bpms=bpms@guwave
database.name.dw=onedata_dev
database.username.dw=bi
database.password.dw=bi@guwave
database.name.linkx=linkx
database.username.linkx=scheduler
database.password.linkx=scheduler@guwave

# Auth config
authBaseUrl=http://riot82.guwave.com:8098

# License config
licenseBaseUrl=http://riot82.guwave.com:8091

# dubbo config
zookeeper.address=riot12.guwave.com:2181,riot13.guwave.com:2181,riot14.guwave.com:2181
# qa deploy fill with   qa    ,otherwise fill with    prod
environment.group=prod
# rpc timeout (ms)
rpc.timeout=60000

# ck config
data.clickhouse.address=*****************************************
data.clickhouse.username=admin
data.clickhouse.password=admin@ck@Guwave

# kafka config
kafka.bootstrapServers=riot12.guwave.com:6667,riot13.guwave.com:6667,riot14.guwave.com:6667
kafka.consumer.consumeGroup=OnedataBpms
kafka.consumer.autoOffsetReset=earliest
kafka.consumer.autoCommitInterval=1000
kafka.consumer.maxPollRecords=10
kafka.listener.concurrency=1
kafka.producer.batchSize=104857600
kafka.producer.lingerMs=0
kafka.producer.bufferMemory=104857600
kafka.loadEndFlagTopic=t_dw_saas_load_end_flag

data.replay.file.maxCount=1000

# yarn
resourceManagerUrl=http://riot11.guwave.com:8088/

# hdfs
gdp.file.hdfsMode=HA
gdp.file.hdfsUrl=hdfs://riot11.guwave.com:8020,hdfs://riot12.guwave.com:8020
gdp.file.hdfsUser=glory

# scheduling
task.scheduling.pool.size=1
scheduler.polling.milliseconds=5000

adapter.python.install.path=/opt/python38/bin/python3

# siteNum校验,filter(siteNum != 255)
settings.siteNumFilterFlag=false

# keyFields
handler.cp.keyFields=deviceId,testArea,lotType,testStage,lotId,waferId,waferNo,startT,finishT
handler.ft.keyFields=deviceId,testArea,lotType,testStage,lotId,sblotId,startT,finishT

# clearRulePreview parse file threadCnt
clearRulePreview.threadCnt=5
```

## 启动项目
```shell
cd ~/deploy/onedata/bpms/bpms-app && mkdir -p logs
sudo docker run -d \
  --name bpms-app \
  -e JAVA_OPTS="-Xmx4096m -XX:MaxPermSize=1024m -Duser.home=/home/<USER>" \
  -e GROUP_ID=$(id `whoami` -g) \
  -e ENVIRON_GROUP=$(getent group $(id `whoami` -g) | cut -d: -f1) \
  -e USER_ID=$(id `whoami` -u) \
  -e ENVIRON_USER=`whoami` \
  --hostname `hostname` \
  --network=host \
  -ti \
  -v /etc/hosts:/etc/hosts:ro \
  -v ~/deploy/onedata/bpms/bpms-app/base.0.4.0.properties:/home/<USER>/deploy/datahub/bpms/bpms-app/bpms-app-0.4.0/properties/bpms-app.properties \
  -v ~/deploy/onedata/bpms/bpms-app/logs:/home/<USER>/deploy/datahub/bpms/bpms-app/bpms-app-0.4.0/logs \
  -v ~/deploy/onedata/bpms/bpms-app/site-packages:/opt/python38/lib/python3.8/site-packages \
  -v ~/deploy/onedata/bpms/bpms-app/lib-dynload:/opt/python38/lib/python3.8/lib-dynload \
  bpms/bpms-app:0.4.0
```