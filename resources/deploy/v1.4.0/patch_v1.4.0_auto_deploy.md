## 准备工作

**大数据模块部署&升级顺序：1.next-compute 2.linkx 3.dataware 4.yms 5.rule 6.bpms 7.dataset-etl**
**dataware升级执行到spark任务后，可以开始部署4.yms 5.rule 6.bpms 7.dataset-etl；yms升级执行到补数任务后，可以开始部署5.rule 6.bpms 7.dataset-etl**

- ### Mysql中停止bpms库的重播任务
```mysql
UPDATE bpms.bz_module_scheduled_control_switch SET stop_flag = 1, update_time = now();
```
## 下载并传输部署包

```shell
# 登录待部署的服务器
mkdir -p ~/deploy/onedata/bpms/bpms-app/logs
mkdir -p ~/deploy/onedata/bpms/upgrade
cd ~/deploy/onedata/bpms/upgrade
wget http://*************:80/deploy/onedata/bpms/v1.4.0/1.4.0.zip
unzip 1.4.0.zip
cd 1.4.0
```

### 修改配置
```shell
cd ~/deploy/onedata/bpms/properties
vim common.properties
```
在 database_password_linkx 下增加一行配置：
```properties
# 应用部署的jvm配置
bpms_app_config="-Xmx2048m -XX:MaxPermSize=512m -Duser.home=/home/<USER>"
```

### 开始部署
#### 确认任务已结束
## 确认bpms库的删除任务都已结束
```mysql
# 查询结果必须都为0，才执行下面的操作
select id,create_time,update_time from bpms.bz_data_replay_record where process_status = 'PROCESSING' and replay_type in ('DELETE_FILE','DELETE_LOT') order by update_time;
```

- 以下命令需要提前安装sshpass命令
```shell
cd ~/deploy/onedata/bpms/upgrade/1.4.0/
bash upgrade.sh 1.3.0 1.4.0 2>&1 | tee upgrade_1.4.0.log
```

### 检查应用状态
```shell
sudo docker ps -a | grep bpms
```

### 配置xxl-job任务
- 注释旧任务
  在mysql节点注释掉以下任务(记录注释掉每个任务的机器):
    ```shell
    crontab -e    
    # 0 1 * * 6  bash /home/<USER>/deploy/onedata/dataware/cron/bpms_delete_commit.sh &> /home/<USER>/deploy/onedata/dataware/cron/bpms_delete_commit.log
    # 20 1 * * 6  bash /home/<USER>/deploy/onedata/dataware/cron/compute_delete_commit.sh &> /home/<USER>/deploy/onedata/dataware/cron/compute_delete_commit.log
    # 30 1 * * 6  bash /home/<USER>/deploy/onedata/dataware/cron/linkx_delete_commit.sh &> /home/<USER>/deploy/onedata/dataware/cron/linkx_delete_commit.log
    # 40 1 * * 6  bash /home/<USER>/deploy/onedata/dataware/cron/onedata_delete_commit.sh &> /home/<USER>/deploy/onedata/dataware/cron/onedata_delete_commit.log
    ```

- 在mysql节点修改xxl-job配置sql
  ```shell
  cd ~/deploy/onedata/bpms/upgrade/1.4.0/resources/sql/mysql/patch/
  ```
- 修改patch_v1.4.0_xxl_job.sql内容,做以下替换:  
  'mpp01' 替换为客户部署devops任务的机器(上一步注释任务的机器)  
  'devops@guwave' 替换为客户环境devops用户密码

- 在mysql中使用root执行替换后的sql

## 结束部署
### Mysql中启动任务

- 启动bpms-app库的入库任务（未升级完成时可以不打开开关）

```mysql
UPDATE bpms.bz_module_scheduled_control_switch SET stop_flag = 0, update_time = now();
```
