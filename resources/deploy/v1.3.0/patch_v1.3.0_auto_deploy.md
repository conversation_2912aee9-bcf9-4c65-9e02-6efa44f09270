## 准备工作

**大数据模块部署&升级顺序：1.next-compute 2.linkx 3.dataware 4.yms 5.rule 6.bpms 7.dataset-etl**
**dataware升级执行到spark任务后，可以开始部署4.yms 5.rule 6.bpms 7.dataset-etl；yms升级执行到补数任务后，可以开始部署5.rule 6.bpms 7.dataset-etl**

- ### Mysql中停止bpms库的重播任务
```mysql
UPDATE bpms.bz_module_scheduled_control_switch SET stop_flag = 1, update_time = now();
```
## 下载并传输部署包

```shell
# 登录待部署的服务器
mkdir -p ~/deploy/onedata/bpms/bpms-app/logs
mkdir -p ~/deploy/onedata/bpms/upgrade
cd ~/deploy/onedata/bpms/upgrade
wget http://*************:80/deploy/onedata/bpms/v1.3.0/1.3.0.zip
unzip 1.3.0.zip
cd 1.3.0
```

## 添加定时任务

### devops(mysql节点)
按顺序执行以下脚本
- resources/shell/bpms_delete_commit.sh (需要配置mysql用户名密码)
- resources/shell/compute_delete_commit.sh (需要配置mysql用户名密码)
- resources/shell/linkx_delete_commit.sh (需要配置mysql用户名密码)
- resources/shell/onedata_delete_commit.sh (需要配置mysql用户名密码)
- resources/shell/cron_mysql.sh (crontab -e 添加文件中的内容)

## 执行mysql path脚本
下列mysql脚本需要root权限，请联系运维执行

**修改对应库名及内容**
${MYSQL_DB_USER_ADMIN} 修改为客户对应的user_admin库
${ACCESS_ADDR}  修改为前端页面访问地址，如http://riot82.guwave.com:28888

- resources/sql/mysql/v1.3.0/patch/patch_v1.3.0_useradmin.sql

### 开始部署
#### 确认任务已结束
## 确认bpms库的删除任务都已结束
```mysql
# 查询结果必须都为0，才执行下面的操作
select id,create_time,update_time from bpms.bz_data_replay_record where process_status = 'PROCESSING' and replay_type in ('DELETE_FILE','DELETE_LOT') order by update_time;
```

- 以下命令需要提前安装sshpass命令
```shell
cd ~/deploy/onedata/bpms/upgrade/1.3.0/
bash upgrade.sh 1.2.0 1.3.0 2>&1 | tee upgrade_1.3.0.log
```

### 检查应用状态
```shell
sudo docker ps -a | grep bpms
```

## 结束部署
### Mysql中启动任务

- 启动bpms-app库的入库任务（未升级完成时可以不打开开关）

```mysql
UPDATE bpms.bz_module_scheduled_control_switch SET stop_flag = 0, update_time = now();
```
