## 下载并传输部署包

```shell
wget http://riot11.guwave.com/deploy/onedata/bpms/v0.2.0/bpms-app-0.2.0.tar

# 把下载下来的bpms-app-0.2.0.tar部署包传输到待部署的服务器
# 登录待部署的服务器
mkdir -p ~/deploy/onedata/bpms/bpms-app/logs
cd ~/deploy/onedata/bpms/bpms-app
# 把下载下来的bpms-app-0.2.0.tar传到此处
sudo docker load -i bpms-app-0.2.0.tar
```

## 执行patch脚本
- resources/sql/mysql/v0.2.0/patch/patch_v0.2.0_schema.sql
- 
下列mysql脚本需要root权限，请联系运维执行

**修改对应库名及内容**
${MYSQL_DB_USER_ADMIN}修改为客户对应的user_admin库
- resources/sql/mysql/v0.2.0/patch/patch_v0.2.0_init_useradmin.sql

## Kill任务

```shell
sudo docker stop $(sudo docker ps -q --filter "name=bpms-app")
sudo docker rm $(sudo docker ps -q --filter "name=bpms-app" --filter "status=exited")
```

## 配置文件改动：

```shell
cd  ~/deploy/onedata/bpms/bpms-app
cp base.0.1.0.properties base.0.2.0.properties
```

## 启动项目

- 先检查好bpms-app-0.1.0的服务都已经kill了

```shell
cd ~/deploy/onedata/bpms/bpms-app && mkdir -p logs
sudo docker run -d \
  --name bpms-app \
  -e GROUP_ID=$(id `whoami` -g) \
  -e ENVIRON_GROUP=`id -Gn` \
  -e USER_ID=$(id `whoami` -u) \
  -e ENVIRON_USER=`whoami` \
  --hostname `hostname` \
  --network=host \
  -ti \
  -v /etc/hosts:/etc/hosts:ro \
  -v ~/deploy/onedata/bpms/bpms-app/base.0.2.0.properties:/home/<USER>/deploy/datahub/bpms/bpms-app/bpms-app-0.2.0/properties/bpms-app.properties \
  -v ~/deploy/onedata/bpms/bpms-app/logs:/home/<USER>/deploy/datahub/bpms/bpms-app/bpms-app-0.2.0/logs \
  bpms/bpms-app:0.2.0
```