**大数据模块部署&升级顺序：1.next-compute 2.linkx 3.dataware 4.yms 5.rule 6.bpms 7.dataset-etl**
**dataware升级执行到spark任务后，可以开始部署4.yms 5.rule 6.bpms 7.dataset-etl；yms升级执行到补数任务后，可以开始部署5.rule 6.bpms 7.dataset-etl**

- ### Mysql中停止bpms库的重播任务
```mysql
UPDATE bpms.bz_module_scheduled_control_switch SET stop_flag = 1, update_time = now();
```

## 部署

### 文件上传到登录待部署的服务器
```shell
# 登录待部署的服务器
mkdir -p ~/deploy/onedata/bpms/bpms-app/logs
mkdir -p ~/deploy/onedata/bpms/upgrade
cd ~/deploy/onedata/bpms/upgrade
wget http://*************:80/deploy/onedata/bpms/v1.6.2/1.6.2_bpms.zip
unzip 1.6.2_bpms.zip
cd 1.6.2
```

### 修改配置
无
```shell
# cd ~/deploy/onedata/properties/
# vim bigdata-common.properties
```

### 开始部署
#### 确认任务都已结束
```mysql
# 查询结果必须都为0，才执行下面的操作
select id,create_time,update_time from bpms.bz_data_replay_record where process_status = 'PROCESSING' and replay_type in ('DELETE_FILE','DELETE_LOT') order by update_time;
```

- 以下命令需要提前安装sshpass命令
```shell
cd ~/deploy/onedata/bpms/upgrade/1.6.2/
bash upgrade.sh 1.6.1 1.6.2 2>&1 | tee upgrade_1.6.2.log
```

### 检查应用状态
```shell
sudo docker ps -a | grep bpms
```
### 检查定时任务
#### glory环境检查下列任务是否已注释，未注释的要注释:

```shell
crontab -e    
# 30 0 * * *  bash /home/<USER>/deploy/onedata/dataware/cron/dws_wafer_overall_yield.sh
# 0 1 * * *  bash /home/<USER>/deploy/onedata/dataware/cron/dws_wafermap_config_batch.sh &> /home/<USER>/deploy/onedata/dataware/cron/dws_wafermap_config_batch.log
# 0 0 * * *  bash /home/<USER>/deploy/onedata/dataware/cron/assembly_overall_stage_yield_batch.sh &> /home/<USER>/deploy/onedata/dataware/cron/assembly_overall_stage_yield_batch.log
```
#### devops环境检查下列任务是否已注释，未注释的要注释:
```shell
crontab -e    
# app机器上:
# 0 2 * * *  /usr/local/python3/bin/python3 /home/<USER>/deploy/onedata/dataware/cron/dim_refresh.py
# 30 0 * * *  python3 /home/<USER>/deploy/onedata/dataware/cron/wip_snapshot.py
# 0 * * * *  python3 /home/<USER>/deploy/onedata/dataware/cron/lot_history.py
# mpp机器上:
# 0 2 * * 6  bash /home/<USER>/deploy/onedata/dataware/cron/dim_delete_commit.sh &> /home/<USER>/deploy/onedata/dataware/cron/dim_delete_commit.log
# 0 3 * * 6  bash /home/<USER>/deploy/onedata/dataware/cron/dws_delete_commit.sh &> /home/<USER>/deploy/onedata/dataware/cron/dws_delete_commit.log
# 0 4 * * 6  bash /home/<USER>/deploy/onedata/dataware/cron/dwd_delete_commit.sh &> /home/<USER>/deploy/onedata/dataware/cron/dwd_delete_commit.log
# 0 22 * * 5  bash /home/<USER>/deploy/onedata/dataware/cron/ck_disk_rebalance.sh &>> /home/<USER>/deploy/onedata/dataware/cron/ck_disk_rebalance.log
```

## 结束部署
### Mysql中启动任务

- 启动bpms-app库的入库任务（未升级完成时可以不打开开关）

```mysql
UPDATE bpms.bz_module_scheduled_control_switch SET stop_flag = 0, update_time = now();
```
