## 下载并传输部署包

```shell
# 登录待部署的服务器
mkdir -p ~/deploy/onedata/bpms/bpms-app/logs
mkdir -p ~/deploy/onedata/bpms/upgrade
cd ~/deploy/onedata/bpms/upgrade
wget http://riot11.guwave.com/deploy/onedata/bpms/v0.6.0/0.6.0.zip
unzip 0.6.0.zip
cd 0.6.0
```

## 修改配置内容
```shell
cd ~/deploy/onedata/bpms/upgrade/0.6.0
vim upgrade.properties
```
- 根据实际情况修改配置

### 开始部署
- 以下命令需要提前安装sshpass命令
```shell
bash upgrade.sh 0.5.0 0.6.0 2>&1 | tee upgrade_0.6.0.log
```