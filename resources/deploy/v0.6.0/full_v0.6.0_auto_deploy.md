## 准备工作

### nginx添加代理（运维操作）
proxy_pass需要替换为bpms-app的部署机器
```shell
    location /bpms-api/ {
        client_max_body_size 8092M;
        proxy_connect_timeout 1200;
        proxy_send_timeout 1200;
        proxy_read_timeout 1200;
        proxy_pass http://192.168.2.82:16666/;
    }

    # 在合适的位置加入前端配置
    location /bpms {
        client_max_body_size 8092M
        alias /home/<USER>/deploy/front/onedata/bpms-web/;
        if ($request_filename ~* .*\.(?:htm|html)$)
        {
            add_header Cache-Control no-store;
        }
        index  index.html index.htm;
        try_files $uri $uri/ /index.html last;
    }
```

## 执行mysql full脚本
需要运维创建用户
用户可查询库为：bpms

```properties
user:bpms
password:bpms@guwave
```

下列mysql脚本需要root权限，请联系运维执行

**修改对应库名及内容**
${MYSQL_DB_USER_ADMIN} 修改为客户对应的user_admin库
${ACCESS_ADDR}  修改为前端页面访问地址，如http://riot82.guwave.com:28888

* resources/sql/mysql/v0.6.0/full/init_useradmin.sql

## 文件上传

```shell
# 把下载下来的bpms-app-0.6.0.tar部署包传输到待部署的服务器
# 登录待部署的服务器
mkdir -p ~/deploy/onedata/bpms/bpms-app/logs
mkdir -p ~/deploy/onedata/bpms/deploy/
cd ~/deploy/onedata/bpms/deploy/
wget http://riot11.guwave.com/deploy/onedata/bpms/v0.6.0/0.6.0.zip
unzip 0.6.0.zip
cd 0.6.0
wget http://riot11.guwave.com/deploy/onedata/dataware/v2.6.0/site-packages.tar.gz
wget http://riot11.guwave.com/deploy/onedata/dataware/v2.6.0/lib-dynload.zip
```

## 修改配置内容
```shell
cd ~/deploy/onedata/bpms/deploy/0.6.0
vim deploy.properties
```
- 根据实际情况修改配置

### 开始部署
- 以下命令需要提前安装sshpass命令
```shell
bash deploy.sh 0.6.0 2>&1 | tee deploy_0.6.0.log
```