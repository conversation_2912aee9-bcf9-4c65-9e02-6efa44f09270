## 准备工作

## 执行mysql bpms用户赋权
bpms用户增加可查询、可修改compute库的权限
```sql
grant all privileges on compute.* to 'bpms'@'%' with grant option;
```

## 下载并传输部署包

```shell
# 登录待部署的服务器
mkdir -p ~/deploy/onedata/bpms/bpms-app/logs
mkdir -p ~/deploy/onedata/bpms/upgrade
cd ~/deploy/onedata/bpms/upgrade
wget http://riot11.guwave.com/deploy/onedata/bpms/v1.0.0/1.0.0.zip
unzip 1.0.0.zip
cd 1.0.0
```

## 修改配置内容
```shell
cd ~/deploy/onedata/bpms/upgrade/1.0.0
vim upgrade.properties
```
- 根据实际情况修改配置

### 开始部署
- 以下命令需要提前安装sshpass命令
```shell
bash upgrade.sh 0.6.1 1.0.0 2>&1 | tee upgrade_1.0.0.log
```

## 结束部署
### Mysql中启动任务

- 启动bpms-app库的入库任务（未升级完成时可以不打开开关）

```mysql
UPDATE bpms.bz_module_scheduled_control_switch SET stop_flag = 0, update_time = now();
```
