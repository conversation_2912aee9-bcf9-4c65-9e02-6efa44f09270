# 在mysql节点上执行
linkxUsername=scheduler
linkxPassword=
linkxDatabase=linkx
mkdir -p /home/<USER>/deploy/onedata/dataware/cron/
touch /home/<USER>/deploy/onedata/dataware/cron/linkx_delete_commit.sh
cat << EOF > /home/<USER>/deploy/onedata/dataware/cron/linkx_delete_commit.sh
mysql -u$linkxUsername -p$linkxPassword -e "DELETE FROM $linkxDatabase.bz_linkx_fail_message_record WHERE update_time < DATE_SUB(NOW(), INTERVAL 2 MONTH) AND delete_flag = 1;"
mysql -u$linkxUsername -p$linkxPassword -e "DELETE FROM $linkxDatabase.bz_task_instance WHERE update_time < DATE_SUB(NOW(), INTERVAL 2 MONTH);"
EOF