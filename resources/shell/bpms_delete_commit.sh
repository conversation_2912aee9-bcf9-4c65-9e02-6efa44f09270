# 在mysql节点上执行
bpmsUsername=bpms
bpmsPassword=
bpmsDatabase=bpms
mkdir -p /home/<USER>/deploy/onedata/dataware/cron/
touch /home/<USER>/deploy/onedata/dataware/cron/bpms_delete_commit.sh
cat << EOF > /home/<USER>/deploy/onedata/dataware/cron/bpms_delete_commit.sh
mysql -u$bpmsUsername -p$bpmsPassword -e "DELETE FROM $bpmsDatabase.bz_kafka_fail_message_record WHERE update_time < DATE_SUB(NOW(), INTERVAL 2 MONTH) AND delete_flag = 1;"
EOF