# 在mysql节点上执行
computeUsername=bi
computePassword=
computeDatabase=compute
mkdir -p /home/<USER>/deploy/onedata/dataware/cron/
touch /home/<USER>/deploy/onedata/dataware/cron/compute_delete_commit.sh
cat << EOF > /home/<USER>/deploy/onedata/dataware/cron/compute_delete_commit.sh
mysql -u$computeUsername -p$computePassword -e "DELETE FROM $computeDatabase.bz_compute_fail_message_record WHERE update_time < DATE_SUB(NOW(), INTERVAL 2 MONTH) AND delete_flag = 1;"
EOF