# 在mysql节点上执行
onedataUsername=bi
onedataPassword=
onedataDatabase=onedata
mkdir -p /home/<USER>/deploy/onedata/dataware/cron/
touch /home/<USER>/deploy/onedata/dataware/cron/onedata_delete_commit.sh
cat << EOF > /home/<USER>/deploy/onedata/dataware/cron/onedata_delete_commit.sh
mysql -u$onedataUsername -p$onedataPassword -e "DELETE FROM $onedataDatabase.dw_dataware_fail_message_record WHERE update_time < DATE_SUB(NOW(), INTERVAL 2 MONTH) AND delete_flag = 1;"
EOF