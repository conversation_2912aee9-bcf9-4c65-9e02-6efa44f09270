.rule-item-box {
  display: grid;               /* 使用网格布局 */
  grid-template-columns: repeat(4, 1fr);  /* 将宽度分成四等份 */
  grid-auto-flow: row;            /* 子元素按行顺序排列 */
  gap: 16px;    

  .rule-item{
    display: flex;
    flex-direction: column;
  
    > span {
      margin: 12px 0 6px 0;
    }
  
    .el-input,
    .el-select,
    .el-input-number {
      width: 100%;
    }
    .percent-icon{
      margin-left: 8px;
    }
  }
}
.el-table-v2__empty{
  height: 100%;
}


