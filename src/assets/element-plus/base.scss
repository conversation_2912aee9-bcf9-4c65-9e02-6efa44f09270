
html {
  scroll-behavior: smooth;
}

//  算法公共标签
.ruleItemBox {
  min-width: 35%;
  display: grid;
  grid-gap: 8px;
  margin: 12px 0 8px;
  grid-template-columns: repeat(2, 1fr);
}
.ruleItem {
  flex: 1;
  display: flex;
  flex-direction: column;

  > span {
    margin: auto 0 4px;
  }

  > .el-input,
  .el-select,
  .el-input-number {
    width: 100%;
  }
}
.row {
  flex-direction: row;
}
.ruleTitle {
  font-size: 14px;
  font-weight: bold;
  margin: 0 0 0;
}
.bold {
  font-weight: bold;
}
.popBox {
  padding: 4px 0;
  > p {
    padding: 4px 0;
    text-align: center;

    &:hover {
      cursor: pointer;
      background: rgb(217, 215, 222);
    }
  }
}
//  黑框样式
:focus {
  outline: none;
}

:root {
  .el-popper.is-light {
    // margin-top: -8px;
    min-width: 90px;

    .el-popper__arrow::before {
      content: '';
      display: block;
    }
  }

  .el-popconfirm {
    padding: 8px !important;
  }

  .el-table-v2 {
    border: none;
  }
}
// 复写确认框样式开始
.el-message-box {
  .el-message-box__title {
    color: #161616;
    font-size: 14px;
    font-weight: bold;
  }
  .el-message-box__container {
    color: #161616;
    font-weight: 400;
  }
  .el-message-box__btns {
    display: flex;
    justify-content: center;
    flex-direction: row-reverse;
    margin-top: 10px;
  }
  .el-message-box__btns button:nth-child(2) {
    margin-left: 0;
    margin-right: 10px;
  }
  .el-button--small {
    height: 24px;
    font-size: 12px;
    padding: 0 16px;
    border-radius: 4px;
  }
  .redconfirm {
    background: #da1e27;
    border-color: #da1e27;
    &:hover {
      background: #f8535a;
      border-color: #f8535a;
    }
    &:focus,
    &:active {
      background: #a50f16;
      border-color: #a50f16;
    }
  }
  .redcancel {
    background: #fff;
    color: #da1e27;
    border-color: #da1e27;
    &:hover {
      background: #f8e4e4;
      color: #da1e27;
      border-color: #da1e27;
    }
    &:focus,
    &:active {
      background: #ded7d7;
      color: #da1e27;
      border-color: #da1e27;
    }
  }
}
// 复写确认框样式结束

// 自定义dialog common
.custom-dialog {
  margin-bottom: 0 !important;
  .el-dialog__header {
    // border-bottom: 1px solid #d7d8dc;
    padding: 0 16px;
    margin-right: 0;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .el-dialog__title {
      font-size: 14px;
      font-weight: bold;
    }
    .el-dialog__headerbtn {
      .el-dialog__close {
        vertical-align: middle;
      }
      position: static;
      width: 20px;
      height: 20px;
    }
  }

  .el-dialog__body {
    height: calc(100% - 102px);
    overflow: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .el-dialog__footer {
    border-top: 1px solid #d7d8dc;
    padding: 0;
    text-align: center;
    height: 52px;
    line-height: 52px;
  }
  .el-form-item__label {
    color: #161616;
  }
}
.el-dialog {
  padding: 16px 0 0;
  .el-dialog__title {
    margin-left: 8px;
  }
  .el-dialog__headerbtn {
    margin-top: 4px;
  }
}
.custom-body-nopad {
  .el-dialog__body {
    padding: 0;
  }
}
.custom-rule-drawer {
  background: #f3f3f3 !important;
  box-shadow: -4px 0px 6px 0px rgba(178, 178, 178, 0.6);
  color: #161616;
  .el-drawer__header {
    background: #f3f3f3 !important;
  }
  .rule-wrap {
    border-radius: 2px;
    border: 1px solid #d7d8dc;
    padding: 8px;
  }
}
.no-select {
  -webkit-user-select: none; /* Chrome, Safari, Android */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE10+/Edge */
  user-select: none; /* 标准语法 */
}
.select-small .el-input__inner {
  font-size: 12px;
}
.select-popper-px-12 {
  // 普通select
  .el-select-dropdown__item {
    font-size: 12px;
    padding-left: 10px;
    height: 24px;
    line-height: 24px;

    &.hover {
      background: #e7e1f3;
      color: #161616;
    }

    &.selected {
      background: #ebebed;
      color: #161616;
    }
  }
}
.border-e0 {
  border: 1px solid #e0e0e0;
}
.flex-1-h0 {
  flex: 1;
  height: 0;
}
.el-popup-parent--hidden {
  width: 100% !important;
}
