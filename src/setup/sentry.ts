import { getSystemConfigByCode } from '@/service/common';
import { isDev } from '@/utils/common';
import * as Sentry from '@sentry/vue';

/**
 * 初始化sentry配置，开启或关闭等从配置接口获取
 */
export const SENTRY_CONFIG_KEY = 'system.config.sentry';
export const setupSentry = async () => {
  // sentry 只在生产环境部署时启用
  if (isDev) return;

  const res = await getSystemConfigByCode(SENTRY_CONFIG_KEY);
  if (res?.data?.code === 200) {
    try {
      const config = JSON.parse(res.data.data);
      Sentry.init({
        enabled: true,
        dsn: config.dsn,
        environment: config.environment,
        integrations: [
          Sentry.browserTracingIntegration(),
          Sentry.replayIntegration({
            maskAllText: false,
            blockAllMedia: false,
          }),
          // Sentry.feedbackIntegration({
          //   colorScheme: 'system',
          // }),
        ],
        // Performance Monitoring
        tracesSampleRate: 1.0, //  Capture 100% of the transactions
        // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
        tracePropagationTargets: ['localhost', /^https:\/\/yourserver\.io\/api/],
        // Session Replay
        replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
        replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
      });
    } catch (e) {
      console.error('Sentry 配置解析失败', e);
    }
  }
};
