import { createApp } from 'vue';
import { createPinia } from 'pinia';
import ElementPlus from 'element-plus';
import { setupRouter } from './router';
import App from './App.vue';
import '@/utils/permission';
import '@guwave/components/dist/style.css';
import './assets/element-plus/base.scss';
import './assets/element-plus/ink-base.scss';

import './tailwind.css';
import './style.scss';
import '@guwave/element-plus-theme/index.scss';

import SvgIcon from './components/SvgIcon.vue';

// import { setupSentry } from '@/setup/sentry';

function setupPinia(app: any) {
  const pinia = createPinia();
  app.use(pinia);
}

async function bootstrap() {
  const app = createApp(App);

  setupPinia(app);

  // setupSentry();

  setupRouter(app);

  app.use(ElementPlus).component('SvgIcon', SvgIcon).mount('#app');
}

bootstrap();
