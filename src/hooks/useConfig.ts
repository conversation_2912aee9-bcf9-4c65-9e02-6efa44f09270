import { getSysConfigInfo } from '@/service/common';
import useDashUrlInfoStore from '@/store/system/dashboardUrl';

export const useConfig = () => {
  const dashBoardInfo = useDashUrlInfoStore();
  const { factoryMode } = storeToRefs(dashBoardInfo);

  // 控制subCustomer是否显示
  const getFactoryMode = async () => {
    const res = await getSysConfigInfo('factoryMode');
    const { data } = res.data || {};
    // factoryMode.value = data !== 'true';
    factoryMode.value = data === 'true';
  };

  onMounted(() => {
    getFactoryMode();
  });
};
