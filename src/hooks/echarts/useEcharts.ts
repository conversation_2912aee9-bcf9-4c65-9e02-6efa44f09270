import { EchartsInstance, oneDataEchartInit } from '@/utils/echarts/config/init';

const useEcharts = (containerRef: Ref<HTMLElement>) => {
  const echartsRef = shallowRef<EchartsInstance>();
  const { width, height } = useElementSize(containerRef);

  const isMounted = useMounted();

  watch([containerRef, isMounted], ([el, _isMounted]) => {
    echartsRef.value?.dispose();
    if (!el || !_isMounted) return;
    echartsRef.value = oneDataEchartInit(el);
  });

  watchDebounced(
    [width, height, echartsRef, isMounted],
    ([width, height, echarts, _isMounted]) => {
      if (!echarts || !height || !_isMounted) return;
      const prevWidth = echarts.getWidth();
      const prevHeight = echarts.getHeight();
      if (prevWidth && Math.abs(prevWidth - width) < 2 && Math.abs(prevHeight - height) < 2) return;

      echarts.resize({ width, height });
    },
    { debounce: 100 },
  );

  onBeforeUnmount(() => {
    echartsRef.value?.dispose();
  });

  return { echartsRef, width, height };
};

export default useEcharts;
