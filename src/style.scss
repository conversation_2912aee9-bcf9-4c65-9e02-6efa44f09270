
#app {
  /*max-width: 1280px;*/
  margin: 0;
  height: 100vh;
  max-height: 100vh;
  box-sizing: border-box;
  width: auto;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  background-color: #f7f7f7;
  overflow: hidden;
  // color: #565656;
  font-size: 16px;

}


.n-config-provider {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.n-popover {
  max-width: 500px;
}

//  弹性布局全局class
.h {
  display: flex;
}
.v {
  display: flex;
  flex-direction: column;
}
