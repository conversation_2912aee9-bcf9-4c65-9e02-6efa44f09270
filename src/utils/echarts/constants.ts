import { chartsAxisStringLabelStyle } from './echarts';

// 颜色配置数组
export const ECHART_COLORS = [
  '#3C8AFA',
  '#7A0998',
  '#F2C80F',
  '#2E3BBB',
  '#D254A5',
  '#6F50BE',
  '#E26435',
  '#18BB42',
  '#71BAFF',
  '#CF3333',
];

// echarts 通用配置
export const ECHARTS_COMMON_OPTIONS = {
  // toolbox
  toolbox: {
    showTitle: false,
    itemSize: 16,
    itemGap: 12,
    width: 100,
    right: 8,
    tooltip: {
      show: true,
      padding: 4,
      textStyle: {
        fontSize: 12,
      },
      formatter: function (param: Record<string, any>) {
        return param.title;
      },
    },
  },
  // legend
  legend: {
    type: 'scroll',
    orient: 'vertical',
    width: '122px',
    top: '30px',
    right: '8px',
    icon: 'circle',
    itemGap: 4,
    itemHeight: 10,
    itemWidth: 10,
    itemStyle: {
      borderWidth: 0,
    },
    textStyle: {
      lineHeight: 16,
    },
    formatter: function (name: any) {
      return chartsAxisStringLabelStyle(name);
    },
    tooltip: {
      show: true,
    },
    pageIconSize: [10, 10],
    pageTextStyle: {
      fontSize: 12,
    },
  },
  //tooltip
  tooltip: {
    trigger: 'item',
    borderWidth: 0.00001,
    borderColor: '#D7D8DC',
    textStyle: {
      fontSize: 12,
    },
    extraCssText:
      'box-shadow: 0 0 4px 1px rgba(0, 0, 0, 0.2);border-radius: 2px 2px 2px 2px ;white-space: normal; word-break: break-all',
    confine: true,
  },
};
