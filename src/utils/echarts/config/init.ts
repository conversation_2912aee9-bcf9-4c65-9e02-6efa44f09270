import * as echarts from 'echarts/core';

// 可以根据需要选用只用到的渲染器
import { SVGRenderer, CanvasRenderer } from 'echarts/renderers';
import {
  TitleComponent,
  TitleComponentOption,
  ToolboxComponent,
  ToolboxComponentOption,
  TooltipComponent,
  TooltipComponentOption,
  GridComponent,
  GridComponentOption,
  LegendComponent,
  LegendComponentOption,
  DatasetComponent,
  DatasetComponentOption,
  MarkLineComponent,
  MarkLineComponentOption,
  MarkAreaComponent,
  Mark<PERSON>reaComponentOption,
  MarkPointComponent,
  MarkPointComponentOption,
  DataZoomComponent,
  DataZoomComponentOption,
  DataZoomInsideComponent,
  DataZoomSliderComponent,
  BrushComponent,
  BrushComponentOption,
  VisualMapComponent,
  VisualMapComponentOption,
  VisualMapContinuousComponent,
  VisualMapPiecewiseComponent,
} from 'echarts/components';
import {
  LineChart,
  LineSeriesOption,
  BarChart,
  BarSeriesOption,
  BoxplotChart,
  BoxplotSeriesO<PERSON>,
  TreemapChart,
  TreemapSeriesOption,
  ScatterChart,
  ScatterSeriesOption,
  PieChart,
  PieSeriesOption,
  HeatmapChart,
  HeatmapSeriesOption,
  CustomChart,
  CustomSeriesOption,
} from 'echarts/charts';
import { LabelLayout } from 'echarts/features';

// 主题设置引入
import oneData from './oneData.json';

echarts.use([
  SVGRenderer,
  CanvasRenderer,
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DatasetComponent,
  MarkLineComponent,
  LabelLayout,
  MarkAreaComponent,
  MarkPointComponent,
  DataZoomComponent,
  DataZoomInsideComponent,
  DataZoomSliderComponent,
  BrushComponent,
  VisualMapComponent,
  VisualMapContinuousComponent,
  VisualMapPiecewiseComponent,
  LineChart,
  BarChart,
  BoxplotChart,
  ScatterChart,
  TreemapChart,
  PieChart,
  HeatmapChart,
  CustomChart,
]);

echarts.registerTheme('oneData', oneData);

export type EchartsInstance = echarts.ECharts;

export type EChartsOption = echarts.ComposeOption<
  | TitleComponentOption
  | ToolboxComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | LegendComponentOption
  | DatasetComponentOption
  | MarkLineComponentOption
  | MarkAreaComponentOption
  | MarkPointComponentOption
  | DataZoomComponentOption
  | BrushComponentOption
  | VisualMapComponentOption
  | LineSeriesOption
  | BarSeriesOption
  | BoxplotSeriesOption
  | TreemapSeriesOption
  | ScatterSeriesOption
  | PieSeriesOption
  | HeatmapSeriesOption
  | CustomSeriesOption
>;

export const oneDataEchartInit = (currentDom: HTMLElement, type?: 'canvas' | 'svg'): EchartsInstance => {
  return echarts.init(currentDom, 'oneData', {
    renderer: type === 'canvas' ? 'canvas' : 'svg',
    devicePixelRatio: window.devicePixelRatio || 1,
  });
};
