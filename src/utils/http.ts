import axios, { AxiosRequestConfig } from 'axios';
import { TOKEN_KEY, getSessionStorage } from './storage';
import { reLogin, isDev, mockLogin } from '@/utils/common';
import { AxiosLoading } from './loading';
import { BaseURL } from './apiUrl';
import { createDiscreteApi } from 'naive-ui';

const { notification } = createDiscreteApi(['notification']);

// TODO 代码优化
const axiosLoaing = new AxiosLoading();
export interface CustomAxiosRequestConfig extends AxiosRequestConfig {
  showLoading?: boolean;
}
export const RESPONSE_SUCCESS_CODE = '00000001';
export const isSuccessResponse = (code?: string) => code === RESPONSE_SUCCESS_CODE;

const http = axios.create({
  baseURL: BaseURL,
});

// 请求拦截
http.interceptors.request.use((options: CustomAxiosRequestConfig) => {
  const token = getSessionStorage(TOKEN_KEY);
  const headers = {
    ...options.headers,
  };
  if (token) {
    Object.assign(headers, {
      Authorization: `Bearer ${token}`,
    });
  }
  if (options?.showLoading) {
    axiosLoaing.addLoading();
  }
  return { ...options, headers };
});

// 返回拦截
http.interceptors.response.use(
  (response) => {
    axiosLoaing.closeLoading();

    const { code } = response.data || {};

    if (response.status === 200) {
      if (code) {
        if (typeof code === 'string') {
          if (code && code !== SUCCESS_CODE) {
            // 鉴权失败重新登录
            if (code === '00000004' || code === '00000401' || code === '00000402' || code === '10002') {
              if (isDev) {
                mockLogin();
              } else {
                reLogin();
              }
            } else {
              notification.error({
                content: response.data.msg,
                duration: 5000,
              });
            }
          }
        }
      }
    }

    return response;
  },
  (error) => {
    axiosLoaing.closeLoading();
    const { code } = error.data || error?.response?.data || {};

    if (code === 401) {
      if (isDev) {
        mockLogin();
      } else {
        reLogin();
      }
    }
    // 如果不是主动取消的请求则提示相关信息
    if (!axios.isCancel(error)) {
      // ElNotification({
      //   title: 'System Warning',
      //   message: error?.message || 'http request error.',
      //   type: 'error',
      // });
    }
    return Promise.reject(error);
  },
);

export const SUCCESS_CODE = '00000001';

export default http;
