import { isNil, isNumber, isString } from 'lodash';

export const toPercent = (num: number, decimal = 2) => {
  if (isNil(num)) return '-';
  const powNum = Math.pow(10, decimal);
  return `${(Math.round(num * 10000 * powNum) / (100 * powNum)).toFixed(decimal)}%`;
};
export const thousandthFormatter = (num) => {
  if (!isString(num) && !isNumber(num)) return '';

  const str = `${num}`;

  return str.replace(/\d+/, function (n) {
    return n.replace(/(\d)(?=(\d{3})+$)/g, function ($1) {
      return $1 + ',';
    });
  });
};
