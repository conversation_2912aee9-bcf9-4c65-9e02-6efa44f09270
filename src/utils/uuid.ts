import { customAlphabet } from "nanoid";

declare global {
  interface Window {
    msCrypto: any;
  }
}

if (!window.crypto && window.msCrypto) {
  window.crypto = window.msCrypto;

  const getRandomValuesDef = window.crypto.getRandomValues;
  // @ts-ignore
  window.crypto.getRandomValues = function (array: any) {
    const values: any = getRandomValuesDef.call(window.crypto, array);
    const result = [];

    for (let i = 0; i < array.length; i++) {
      result[i] = values[i];
    }

    return result;
  };
}

const uuid = customAlphabet("1234567890abcdefghijklmnopqrstuvwxyz-", 16);

export default uuid;