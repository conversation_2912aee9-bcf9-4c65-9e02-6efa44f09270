import { storeToRefs } from 'pinia';

import router from '@/router';
import usePermissionStore from '@/store/permission';
import { TOKEN_KEY, getSessionStorage, clearSessionStorage } from '@/utils/storage';
import { isDev, mockLogin, reLogin } from '@/utils/common';

const whiteList = ['/404', '/nopermission'];

router.beforeEach(async (to, from, next) => {
  if (whiteList.indexOf(to.path) > -1) {
    next();
  } else {
    next();
    const token = getSessionStorage(TOKEN_KEY);
    if (token) {
      const permission = usePermissionStore();
      const { permissions } = storeToRefs(permission);

      // next();
      if (!(permissions.value && permissions.value.length > 0)) await permission.getPermission();
      if (
        permissions.value &&
        permissions.value.length > 0
        // &&
        // (permissions.value[0] === '*:*:*' || permissions.value.indexOf(to.meta.permission as string) > -1)
      ) {
        next();
      } else {
        next('/404');
      }
    } else {
      clearSessionStorage();
      if (isDev) {
        mockLogin();
      } else {
        reLogin();
      }
    }
  }
});
