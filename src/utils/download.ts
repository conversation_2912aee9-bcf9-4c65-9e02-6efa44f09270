import http from '@/utils/http';
import { parseTime } from './time';

export const downloadFile = (data: Blob, name = 'download', type = '') => {
  if (!(data instanceof Blob)) {
    try {
      data = new Blob([data], { type });
    } catch (e) {
      console.warn('[Download] Unsupported file type');
    }
  }
  // 兼容IE
  const navigator = window.navigator as any;
  if (navigator?.msSaveBlob) {
    navigator.msSaveBlob?.(data, name);
    return;
  }
  // 使用 a 标签下载
  let a: any = document.createElement('a');
  a.download = decodeURIComponent(name);
  a.style.display = 'none';
  a.href = window.URL.createObjectURL(data);
  a.click();
  window.URL.revokeObjectURL(a.href);
  a = null;
};

export const exportServiceUtil = (path: string, options: any) => {
  return new Promise((resolve, reject) => {
    http(path, {
      responseType: 'blob',
      ...options,
    })
      .then((res) => {
        try {
          const contentType = res.headers['content-type'] as string;
          if (contentType !== 'application/json') {
            let fileName = (res.headers['content-disposition'] as string) || '';
            if (fileName?.indexOf(';filename=') > -1) {
              fileName = fileName.split(';filename=')[1];
            }
            downloadFile(res.data, fileName);
            resolve(res);
          } else {
            resolve(res);
          }
        } catch (e) {
          reject(e);
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
};

// 导出csv文件的响应函数
export const downloadCSV = (columns, dataSource) => {
  const table =
    columns.map((col) => col.title).join() +
    '\n' +
    dataSource
      .map((item) => {
        return columns.map((col) => item[col.dataIndex]).join();
      })
      .join('\n');
  // encodeURIComponent 解决中文乱码
  const uri = 'data:text/csv;charset=utf-8,\ufeff' + encodeURIComponent(table);
  //通过创建a标签实现
  let link = document.createElement('a');
  link.href = uri;
  //对下载的文件命名
  link.download = `${parseTime(new Date())}.csv`;
  link.click();
  link = null;
};
