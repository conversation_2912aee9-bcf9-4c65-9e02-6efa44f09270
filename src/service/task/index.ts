import { AddNull, ListRes, OptionsItem, ResponseType, SearchParam } from '@/types/common';
import {
  DetailInfo,
  DmtTaskDetailVo,
  DW_ListItem,
  DW_ListParam,
  DW_RerunParam,
  YMS_DetailInfo,
  YMS_ListItem,
  YMS_ListParam,
  YMS_RerunParam,
} from '@/types/task';
import ApiUrl from '@/utils/apiUrl';
import http from '@/utils/http';

/**
 * 详情信息
 * @id
 * @DetailInfo
 * @get
 */
export const getDetail = (id: number) => {
  return http.get<ResponseType<DetailInfo>>(ApiUrl.Task.getDetail, { params: { id: id } });
};

//  ----------------------------- DW -------------------------------
/**
 * DW下拉框列表
 * @SearchParam
 * @OptionsItem
 * @post
 */
export const getDwDropdown = (param: SearchParam) => {
  return http.post<ResponseType<OptionsItem[]>>(ApiUrl.Task.getDwDropdown, param);
};
/**
 * DW列表接口
 * @DW_ListParam
 * @DW_ListItem
 * @post
 */
export const getDwList = (param: AddNull<DW_ListParam>) => {
  return http.post<ResponseType<ListRes<DW_ListItem>>>(ApiUrl.Task.getDwList, param);
};
/**
 * DW重跑
 * @DW_RerunParam
 * @undefined
 * @post
 */
export const rerunDw = (param: AddNull<DW_RerunParam>) => {
  return http.post<ResponseType<{}>>(ApiUrl.Task.rerunDw, param);
};

//  ----------------------------- YMS -------------------------------
/**
 * YMS下拉框列表
 * @SearchParam
 * @OptionsItem
 * @post
 */
export const getYmsDropdown = (param: SearchParam) => {
  return http.post<ResponseType<OptionsItem[]>>(ApiUrl.Task.getYmsDropdown, param);
};
/**
 * YMS列表接口
 * @DW_ListParam
 * @YMS_ListItem
 * @post
 */
export const getYmsList = (param: AddNull<YMS_ListParam>) => {
  return http.post<ResponseType<ListRes<YMS_ListItem>>>(ApiUrl.Task.getYmsList, param);
};
/**
 * YMS重跑详情接口
 * @DW_ListParam
 * @YMS_ListItem
 * @post
 */
export const getYmsDetail = (id: number) => {
  return http.get<ResponseType<YMS_DetailInfo>>(ApiUrl.Task.getYmsDetail, { params: { id } });
};
/**
 * YMS重跑
 * @YMS_RerunParam
 * @undefined
 * @post
 */
export const rerunYms = (param: AddNull<YMS_RerunParam>) => {
  return http.post<ResponseType<{}>>(ApiUrl.Task.rerunYms, param);
};
/**
 * YMS重跑
 * @YMS_RerunParam
 * @undefined
 * @post
 */
export const rerunDmt = (param: AddNull<{ id: number; dmtTaskDetailVos: DmtTaskDetailVo[] }>) => {
  return http.post<ResponseType<{}>>(ApiUrl.Task.rerunDmt, param);
};
/**
 * 恢复测项数据
 * @YMS_RerunParam
 * @undefined
 * @post
 */
export const freezeDmt = (id: number) => {
  return http.get<ResponseType<{}>>(ApiUrl.Task.freezeDmt, { params: { id } });
};
