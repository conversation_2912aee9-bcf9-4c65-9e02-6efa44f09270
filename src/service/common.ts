import http from '@/utils/http';
import { ResponseType } from '@/types/http';

const base = import.meta.env.VITE_APP_API_PARAME_SYSTEM || '';

/**
 * 获取指定配置字段
 */
export function getSystemConfigByCode(code: string) {
  return http.get(`${base}/system/config/configKey/${code}`);
}

export const getSysConfigInfo = (code) => {
  return http.get<ResponseType<string>>('/user-center-api/system/config/configKey' + `/${code}`, { baseURL: '' });
};

/**
 * 获取 sub customer 字段
 */
export const getSubCustomer = () => {
  return http.get<ResponseType<string>>(`/user-center-api/system/subCustomer/optionselect?moduleCode=DATA`, {
    baseURL: '',
  });
};
