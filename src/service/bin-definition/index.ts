import http from '@/utils/http';
import { PaginationRes, ResponseType } from '@/types/common';
import ApiUrl from '@/utils/apiUrl';
import { useAxios } from '@vueuse/integrations/useAxios';

// 搜索测试程序下拉选项
export function searchTestPrograms(data: any) {
  return http.post<ResponseType<any>>(ApiUrl.BinDefinition.searchTestPrograms, data);
}

// 获取已配置的测试程序列表
export function getConfiguredTestPrograms(data: any) {
  return useAxios<ResponseType<PaginationRes<any>>>(
    `${ApiUrl.BinDefinition.configured}`,
    {
      method: 'POST',
      data,
    },
    http,
  );
}

// 获取未配置的测试程序列表
export function getUnconfiguredTestPrograms(data: any) {
  return useAxios<ResponseType<PaginationRes<any>>>(
    `${ApiUrl.BinDefinition.unconfigured}`,
    {
      method: 'POST',
      data,
    },
    http,
  );
}

// 获取测试程序的bin定义详情
export function getBinDefinitions(testProgram: string) {
  return useAxios<ResponseType<any>>(
    `${ApiUrl.BinDefinition.details}/${testProgram}`,
    {
      method: 'GET',
    },
    http,
  );
}

// 保存测试程序的bin定义
export function saveBinDefinitions(data: any) {
  return useAxios<ResponseType<any>>(
    `${ApiUrl.BinDefinition.save}`,
    {
      method: 'POST',
      data,
    },
    http,
  );
}

// 删除测试程序的bin定义
export function deleteTestProgramBinDefinitions(data: any) {
  return useAxios<ResponseType<any>>(
    `${ApiUrl.BinDefinition.delete}`,
    {
      method: 'POST',
      data,
    },
    http,
  );
}

// 导出bin定义到CSV
export function exportBinDefinitions(data: any) {
  return useAxios<ResponseType<any>>(
    `${ApiUrl.BinDefinition.export}`,
    {
      method: 'POST',
      data,
      responseType: 'blob',
    },
    http,
  );
}

// 从文件导入bin定义
export function importBinDefinitions(file: File, importMode: string) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('importMode', importMode);
  
  return useAxios<ResponseType<any>>(
    `${ApiUrl.BinDefinition.import}`,
    {
      method: 'POST',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
    http,
  );
}

// 导出模板
export function exportTemplate() {
  return useAxios<ResponseType<any>>(
    `${ApiUrl.BinDefinition.exportTemplate}`,
    {
      method: 'GET',
      responseType: 'blob',
    },
    http,
  );
}