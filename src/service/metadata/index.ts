import { ListRes, ResponseType } from '@/types/common';
import {
  AddLinkParam,
  DeleteFieldParam,
  DescriptionItem,
  DatabaseParam,
  FieldItem,
  FieldListParam,
  LinkItem,
  TableLineageItem,
  UpdateDescriptionParam,
  UpdateFieldParam,
  FieldLineage,
} from '@/types/metadata';
import ApiUrl from '@/utils/apiUrl';
import http from '@/utils/http';

//  ----------------------------------------- 链接 -------------------------------------
/**
 * 链接列表
 * @DatabaseParam
 * @LinkItem
 * @post
 */
export const getLinks = (param: DatabaseParam) => {
  return http.post<ResponseType<LinkItem[]>>(ApiUrl.MetaData.getLinks, param);
};
/**
 * 添加链接
 * @AddLinkParam
 * @LinkItem
 * @post
 */
export const addLink = (param: AddLinkParam) => {
  return http.post<ResponseType<LinkItem[]>>(ApiUrl.MetaData.addLink, param);
};
/**
 * 删除链接
 * @id
 * @LinkItem
 * @delete
 */
export const deleteLink = (id: number) => {
  return http.delete<ResponseType<LinkItem[]>>(ApiUrl.MetaData.deleteLink, { params: { id: id } });
};

//  ----------------------------------------- 字段 -------------------------------------
/**
 * 分页查询字段
 * @FieldListParam
 * @FieldItem
 * @post
 */
export const getSchema = (param: FieldListParam) => {
  return http.post<ResponseType<ListRes<FieldItem>>>(ApiUrl.MetaData.getSchema, param);
};
/**
 * 修改字段
 * @UpdateFieldParam
 * @undefined
 * @post
 */
export const updateField = (param: UpdateFieldParam) => {
  return http.post<ResponseType<undefined>>(ApiUrl.MetaData.updateField, param);
};
/**
 * 从表重新解析字段
 * @FieldListParam
 * @FieldItem
 * @post
 */
export const reloadSchema = (param: FieldListParam) => {
  return http.post<ResponseType<ListRes<FieldItem>>>(ApiUrl.MetaData.reloadSchema, param);
};
/**
 * 删除字段
 * @DeleteFieldParam
 * @FieldItem
 * @delete
 */
export const deleteField = (params: DeleteFieldParam) => {
  return http.delete<ResponseType<ListRes<FieldItem>>>(ApiUrl.MetaData.deleteField, { data: params });
};

//  ----------------------------------------- 描述 -------------------------------------
/**
 * 查询描述
 * @DatabaseParam
 * @DescriptionItem
 * @post
 */
export const getDescription = (param: DatabaseParam) => {
  return http.post<ResponseType<DescriptionItem>>(ApiUrl.MetaData.getDescription, param);
};
/**
 * 修改描述
 * @UpdateDescriptionParam
 * @undefined
 * @post
 */
export const updateDescription = (param: UpdateDescriptionParam) => {
  return http.post<ResponseType<undefined>>(ApiUrl.MetaData.updateDescription, param);
};

//  ----------------------------------------- 血缘 -------------------------------------
/**
 * 获取表级血缘
 * @DatabaseParam
 * @TableLineageItem
 * @post
 */
export const getTableLineage = (param: DatabaseParam) => {
  return http.post<ResponseType<TableLineageItem>>(ApiUrl.MetaData.getTableLineage, param);
};
/**
 * 获取字段血缘
 * @SearchParam
 * @TableLineageItem
 * @get
 */
export const getFieldLineage = (id: number) => {
  return http.get<ResponseType<FieldLineage[]>>(ApiUrl.MetaData.getFieldLineage, { params: { id: id } });
};
/**
 * 血缘字段信息
 * @SearchParam
 * @FieldItem
 * @get
 */
export const getFieldInfo = (id: number) => {
  return http.get<ResponseType<FieldItem>>(ApiUrl.MetaData.getFieldInfo, { params: { id: id } });
};

//  ----------------------------------------- 源、库、表 -------------------------------------
/**
 * 根据数据源查库
 * @source
 * @string
 * @get
 */
export const getDatabases = (datasource: string) => {
  return http.get<ResponseType<string[]>>(ApiUrl.MetaData.getDatabases, { params: { datasource: datasource } });
};
/**
 * 根据数据源和库查表
 * @SearchParam
 * @OptionsItem
 * @get
 */
export const getTableNames = (param: { datasource: string; databaseName: string }) => {
  return http.get<ResponseType<string[]>>(ApiUrl.MetaData.getTableNames, { params: param });
};
