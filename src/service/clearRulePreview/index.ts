import { ListItem } from '@/types/clearRulePreview';
import { ListRes, OptionsItem, ResponseType, SearchParam } from '@/types/common';
import { exportServiceUtil } from '@/utils/download';
import ApiUrl from '@/utils/apiUrl';
import http from '@/utils/http';

/**
 * 下拉框列表
 * @SearchParam
 * @OptionsItem
 * @post
 */
export const filterDropdown = (param: SearchParam) => {
  return http.post<ResponseType<OptionsItem[]>>(ApiUrl.ClearRulePreview.getDropdown, param);
};
/**
 * 获取列表
 * @ListParam
 * @ListRes
 * @post
 */
export const getList = (param: SearchParam) => {
  return http.post<ResponseType<ListRes<ListItem>>>(ApiUrl.ClearRulePreview.getList, param);
};
/**
 * 上传
 * @FormData
 * @undefined
 * @post
 */
export const fileUpload = (param: FormData) => {
  return http.post<ResponseType<undefined>>(ApiUrl.ClearRulePreview.upload, param);
};
/**
 * 重试
 * @ids
 * @undefined
 * @get
 */
export const retry = (ids: number[]) => {
  return http.post<ResponseType<undefined>>(ApiUrl.ClearRulePreview.retry, ids);
};
/**
 * 下载
 * @id
 * @undefined
 * @get
 */
export const downloadFile = (id: number) => {
  const apiUrl = ApiUrl.ClearRulePreview.downloadDetail + `?id=${id}`;
  return exportServiceUtil(apiUrl, {
    method: 'get',
    headers: {
      'Content-Type': 'application/json',
    },
  });
};
