import http from '@/utils/http';
import { PaginationRes, ResponseType } from '@/types/http';
import ApiUrl from '@/utils/apiUrl';
import { useAxios } from '@vueuse/integrations/useAxios';
import { DataQualityBaseDataItem } from '@/views/data-center-quality/typing';
import { MatchDetailRes } from '@/views/data-center-quality/DataExact/typing';
import { DataQualityTimelinessData } from '@/views/data-center-quality/DataTimeliness/typing';
import { StepOneListType, DialogTableType, StepTwoListType } from '@/views/data-center-quality/DataRecovery/typing';

export const getConstantMap = () => {
  return http.post<ResponseType<Record<string, any>>>(ApiUrl.DataCenterQuality.getConstantMap);
};

export const getQualityOverviewOptions = (param) => {
  return http.post<ResponseType<{ name: string; showName: string; selectable: boolean }[]>>(
    ApiUrl.DataCenterQuality.getQualityOverviewOptions,
    param,
  );
};

export const getTimelinessOptions = (param) => {
  return http.post<ResponseType<{ name: string; showName: string; selectable: boolean }[]>>(
    ApiUrl.DataCenterQuality.getTimelinessOptions,
    param,
  );
};

export const getExactOptions = (param) => {
  return http.post<ResponseType<{ name: string; showName: string; selectable: boolean }[]>>(
    ApiUrl.DataCenterQuality.getExactOptions,
    param,
  );
};

export const getIntegrityOptions = (param) => {
  return http.post<ResponseType<{ name: string; showName: string; selectable: boolean }[]>>(
    ApiUrl.DataCenterQuality.getIntegrityOptions,
    param,
  );
};

// 数据质量概览接口
export function getQualityOverviewData() {
  return useAxios<ResponseType<DataQualityBaseDataItem[]>>(
    ApiUrl.DataCenterQuality.getQualityOverviewData,
    {
      method: 'POST',
    },
    http,
    { immediate: false },
  );
}

//数据准确性校验 批次匹配详情 接口
export function getExactMatchDetail() {
  return useAxios<ResponseType<MatchDetailRes>>(
    ApiUrl.DataCenterQuality.getExactMatchDetail,
    {
      method: 'POST',
    },
    http,
    { immediate: false },
  );
}

//数据准确性校验 批次良率详情 接口
export function getExactYieldDetail() {
  return useAxios<ResponseType<PaginationRes<Record<string, any>[]>>>(
    ApiUrl.DataCenterQuality.getExactYieldDetail,
    {
      method: 'POST',
    },
    http,
    { immediate: false },
  );
}

// 好品复测下载
export const downloadGoodRetest = (param: Record<string, any>) => {
  return http.post<ResponseType<any>>(ApiUrl.DataCenterQuality.downloadGoodRetest, param, {
    responseType: 'blob',
  });
};

export const downloadFileRetest = (param: Record<string, any>) => {
  return http.post<ResponseType<any>>(ApiUrl.DataCenterQuality.downloadFileRetest, param, {
    responseType: 'blob',
  });
};

// 准确性表格导出
export function getExactExport(param) {
  return http.post<ResponseType<any>>(ApiUrl.DataCenterQuality.getExactExport, param, {
    responseType: 'blob',
  });
}
// 完整性表格导出
export function getIntegrityExport(param) {
  return http.post<ResponseType<any>>(ApiUrl.DataCenterQuality.getIntegrityExport, param, {
    responseType: 'blob',
  });
}

export function downLoadDataExactStdfFile(param) {
  return http.post<ResponseType<any>>(ApiUrl.DataCenterQuality.downLoadDataExactStdfFile, param, {
    responseType: 'blob',
  });
}

// 数据准确性的详情弹窗list接口
export function getExactDetailList() {
  return useAxios<ResponseType<{ detailList: Record<string, any>[]; fileList: Record<string, any>[] }>>(
    ApiUrl.DataCenterQuality.getExactDetailList,
    {
      method: 'POST',
    },
    http,
    { immediate: false },
  );
}

// 数据入库时效性接口
export function getTimeliness() {
  return useAxios<ResponseType<DataQualityTimelinessData>>(
    ApiUrl.DataCenterQuality.getTimeliness,
    {
      method: 'POST',
    },
    http,
    { immediate: false },
  );
}

// 数据完整性基础信息接口
export function getDataQualityIntegrityBase() {
  return useAxios<ResponseType<DataQualityBaseDataItem>>(
    ApiUrl.DataCenterQuality.getDataQualityIntegrityBase,
    {
      method: 'POST',
    },
    http,
    { immediate: false },
  );
}

// 数据完整性列表接口
export function getDataQualityIntegrityList() {
  return useAxios<ResponseType<PaginationRes<Record<string, any>[]>>>(
    ApiUrl.DataCenterQuality.getDataQualityIntegrityList,
    {
      method: 'POST',
    },
    http,
    { immediate: false },
  );
}

//
export function getAllDataQualityIntegrityList(data) {
  return useAxios<ResponseType<PaginationRes<Record<string, any>[]>>>(
    ApiUrl.DataCenterQuality.getDataQualityIntegrityList,
    {
      method: 'POST',
      data,
    },
    http,
  );
}

// 数据完整性列表删除
export function deleteDataQualityIntegrityList(data) {
  return useAxios<ResponseType<PaginationRes<Record<string, any>[]>>>(
    ApiUrl.DataCenterQuality.deleteDataQualityIntegrityList,
    {
      method: 'POST',
      data,
    },
    http,
  );
}
// 数据完整性列表重跑
export function rerunDataQualityIntegrityList(data) {
  return useAxios<ResponseType<PaginationRes<Record<string, any>[]>>>(
    ApiUrl.DataCenterQuality.rerunDataQualityIntegrityList,
    {
      method: 'POST',
      data,
    },
    http,
  );
}
// 数据完整性列表备注
export function remarkDataQualityIntegrityList(data) {
  return useAxios<ResponseType<PaginationRes<Record<string, any>[]>>>(
    ApiUrl.DataCenterQuality.remarkDataQualityIntegrityList,
    {
      method: 'POST',
      data,
    },
    http,
  );
}
// 数据完整性列表修改file label
export function changeFileLabelDataQualityIntegrityList(data) {
  return useAxios<ResponseType<PaginationRes<Record<string, any>[]>>>(
    ApiUrl.DataCenterQuality.changeFileLabelDataQualityIntegrityList,
    {
      method: 'POST',
      data,
    },
    http,
  );
}
// 数据完整性列表修复
export function repairDataQualityIntegrityList(data) {
  return useAxios<ResponseType<PaginationRes<Record<string, any>[]>>>(
    ApiUrl.DataCenterQuality.repairDataQualityIntegrityList,
    {
      method: 'POST',
      data,
    },
    http,
  );
}

export const getBatchChangeOptions = (params: any) => {
  return http.post<ResponseType<{ label: string; value: string }[]>>(
    ApiUrl.DataCenterQuality.getBatchChangeOptions,
    params,
  );
};

export const getFileRecoveryOptions = (params: any) => {
  return http.post<ResponseType<{ label: string; value: string }[]>>(
    ApiUrl.DataCenterQuality.getFileRecoveryOptions,
    params,
  );
};

export const getFileDeleteOptions = (param) => {
  return http.post<ResponseType<{ name: string; showName: string; selectable: boolean }[]>>(
    ApiUrl.DataCenterQuality.getFileDeleteOptions,
    param,
  );
};

export const getBatchList = (param: Record<any, any>) => {
  return http.post<ResponseType<PaginationRes<Record<string, any>[]>>>(ApiUrl.DataCenterQuality.getBatchList, param);
};

export const getRecoveryList = (param: Record<any, any>) => {
  return http.post<ResponseType<PaginationRes<Record<string, any>[]>>>(ApiUrl.DataCenterQuality.getRecoveryList, param);
};

export const getDeleteList = (param) => {
  return http.post<ResponseType<PaginationRes<Record<string, any>[]>>>(ApiUrl.DataCenterQuality.getDeleteList, param);
};

export const getFileDialogList = (id: string) => {
  return http.get<ResponseType<Record<string, any>[]>>(ApiUrl.DataCenterQuality.getFileDialogList, {
    params: { id: id },
  });
};

//  弹窗表格接口
export const getDialogTableData = (fileId: number) => {
  return http.get<ResponseType<DialogTableType[]>>(ApiUrl.DataCenterQuality.getDialogTableData + '?fileId=' + fileId);
};

//  第一步列表接口
export const getStepOneData = (param: { fileIds: number[] } & Record<string, any>) => {
  return http.post<ResponseType<StepOneListType[]>>(ApiUrl.DataCenterQuality.getStepOneData, param);
};
//  第二步列表接口
export const getStepTwoData = (param: { fileIds: number[] } & Record<string, any>) => {
  return http.post<ResponseType<StepTwoListType[]>>(ApiUrl.DataCenterQuality.getStepTwoData, param);
};
//  第三步列表接口
export const getStepThreeData = (param: { repairFileInfos } & Record<string, any>) => {
  return http.post<ResponseType<StepOneListType[]>>(ApiUrl.DataCenterQuality.getStepThreeData, param);
};
//  第四步列表接口
export const getStepFourData = (param: { repairFileInfos } & Record<string, any>) => {
  return http.post<ResponseType<StepOneListType[]>>(ApiUrl.DataCenterQuality.getStepFourData, param);
};
//  保存接口
export const stepSave = (param: { repairFileInfos } & Record<string, any>) => {
  return http.post<ResponseType<StepOneListType[]>>(ApiUrl.DataCenterQuality.stepSave, param);
};
