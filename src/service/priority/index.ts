import { ResponseType, TableRes } from '@/types/common';
import ApiUrl from '@/utils/apiUrl';
import http from '@/utils/http';
import {
  AutoListItem,
  ConfigListItem,
  ConfigListParam,
  ManualListItem,
  ProorityParam,
  SearchParam,
  SparkListItem,
} from '@/views/priority/typing';

/**
 * 获取自动入库列表
 * @SearchParam
 * @ListItem
 * @post
 */
export const getAutoList = (param: SearchParam) => {
  return http.post<ResponseType<TableRes<AutoListItem[]>>>(ApiUrl.Priority.getAutoList, param);
};
/**
 * 获取手动入库列表
 * @SearchParam
 * @ListItem
 * @post
 */
export const getManualList = (param: SearchParam) => {
  return http.post<ResponseType<TableRes<ManualListItem[]>>>(ApiUrl.Priority.getManualList, param);
};
/**
 * 获取spark列表
 * @SearchParam
 * @ListItem
 * @post
 */
export const getSparkList = (param: SearchParam) => {
  return http.post<ResponseType<TableRes<SparkListItem[]>>>(ApiUrl.Priority.getSparkList, param);
};
/**
 * 更新优先级接口
 * @ProorityParam
 * @undefined
 * @post
 */
export const updatePriority = (param: ProorityParam) => {
  return http.post<ResponseType<undefined>>(ApiUrl.Priority.updatePriority, param);
};
/**
 * 获取配置列表
 * @ConfigListParam
 * @ConfigListItem
 * @post
 */
export const getConfigList = (param: ConfigListParam) => {
  return http.post<ResponseType<TableRes<ConfigListItem[]>>>(ApiUrl.Priority.getConfigList, param);
};
/**
 * 添加配置
 * @ConfigListParam
 * @undefined
 * @post
 */
export const addConfig = (param: ConfigListParam) => {
  return http.post<ResponseType<undefined>>(ApiUrl.Priority.addConfig, param);
};
/**
 * 更新配置
 * @UpdateConfigParam
 * @undefined
 * @post
 */
export const updateConfig = (param: ConfigListParam) => {
  return http.post<ResponseType<undefined>>(ApiUrl.Priority.updateConfig, param);
};
/**
 * 删除配置
 * @id
 * @undefined
 * @post
 */
export const deleteConfig = (id: number) => {
  return http.delete<ResponseType<undefined>>(ApiUrl.Priority.deleteConfig, { params: { id } });
};
