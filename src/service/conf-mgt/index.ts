import http from '@/utils/http';

import { PaginationRes, ResponseType } from '@/types/common';
import ApiUrl from '@/utils/apiUrl';
import { useAxios } from '@vueuse/integrations/useAxios';
import { AutoImportRuleItem, ConstantOptionMap } from '@/views/conf-mgt/typing';

export const getFilterDropDown = (data) => {
  return http.post<ResponseType<any>>(ApiUrl.ConfMgt.getFilterDropDown, data);
};

export function getAutoImportRules(data) {
  return useAxios<ResponseType<PaginationRes<AutoImportRuleItem[]>>>(
    ApiUrl.ConfMgt.getAutoImportRules,
    {
      method: 'POST',
      data,
    },
    http,
  );
}

export function getConstantMap() {
  return useAxios<ResponseType<ConstantOptionMap>>(
    ApiUrl.ConfMgt.getConstantMap,
    {
      method: 'POST',
    },
    http,
  );
}

export function deleteAutoImportRule(id: number) {
  return useAxios<ResponseType<any>>(
    `${ApiUrl.ConfMgt.deleteWarehousingRuleFlow}?id=${id}`,
    {
      method: 'DELETE',
    },
    http,
  );
}

export function copyAutoImportRules(id: number) {
  return useAxios<ResponseType<any>>(
    `${ApiUrl.ConfMgt.duplicateWarehousingRuleFlow}?id=${id}`,
    {
      method: 'POST',
    },
    http,
  );
}

export function checkAutoImportRuleExist() {
  return useAxios<ResponseType<any>>(
    `${ApiUrl.ConfMgt.checkAutoImportRuleExist}`,
    {
      method: 'GET',
    },
    http,
  );
}

export function validateRuleFlow(data) {
  return useAxios<ResponseType<any>>(
    ApiUrl.ConfMgt.validateRuleFlow,
    {
      method: 'POST',
      data,
    },
    http,
  );
}

export function getSubCustomerRule(data) {
  return useAxios<ResponseType<any>>(
    ApiUrl.ConfMgt.getSubCustomerRule,
    {
      method: 'POST',
      data,
    },
    http,
  );
}
export function getFileRenameRule(data) {
  return useAxios<ResponseType<any>>(
    ApiUrl.ConfMgt.getFileRenameRule,
    {
      method: 'POST',
      data,
    },
    http,
  );
}
export function getSpecialLogic(data) {
  return useAxios<ResponseType<any>>(
    ApiUrl.ConfMgt.getSpecialLogic,
    {
      method: 'POST',
      data,
    },
    http,
  );
}
export function getConvertScript(data) {
  return useAxios<ResponseType<any>>(
    ApiUrl.ConfMgt.getConvertScript,
    {
      method: 'POST',
      data,
    },
    http,
  );
}
export function getCriticalField(data) {
  return useAxios<ResponseType<any>>(
    ApiUrl.ConfMgt.getCriticalField,
    {
      method: 'POST',
      data,
    },
    http,
  );
}
export function getCriticalFieldDeviceId(data) {
  return useAxios<ResponseType<any>>(
    ApiUrl.ConfMgt.getCriticalFieldDeviceId,
    {
      method: 'POST',
      data,
    },
    http,
  );
}

export function getConvertScriptFileOptions(data) {
  return useAxios<ResponseType<any>>(
    ApiUrl.ConfMgt.getConvertScriptFileOptions,
    {
      method: 'POST',
      data,
      headers: {
        'Content-Type': 'mutipart/form-data',
      },
    },
    http,
  );
}

export function downloadScriptFile(id: number) {
  return useAxios<ResponseType<any>>(
    `${ApiUrl.ConfMgt.downloadScriptFile}?id=${id}`,
    {
      method: 'GET',
      responseType: 'blob',
    },
    http,
  );
}

export function saveRuleFlow(data) {
  return useAxios<ResponseType<any>>(
    ApiUrl.ConfMgt.saveRuleFlow,
    {
      method: 'POST',
      data,
    },
    http,
  );
}
export function saveSubCustomerRule(data) {
  return useAxios<ResponseType<any>>(
    ApiUrl.ConfMgt.saveSubCustomerRule,
    {
      method: 'POST',
      data,
    },
    http,
  );
}
export function saveFileRenameRule(data) {
  return useAxios<ResponseType<any>>(
    ApiUrl.ConfMgt.saveFileRenameRule,
    {
      method: 'POST',
      data,
    },
    http,
  );
}
export function saveSpecialLogic(data) {
  return useAxios<ResponseType<any>>(
    ApiUrl.ConfMgt.saveSpecialLogic,
    {
      method: 'POST',
      data,
    },
    http,
  );
}
export function saveConvertScript(data) {
  return useAxios<ResponseType<any>>(
    ApiUrl.ConfMgt.saveConvertScript,
    {
      method: 'POST',
      data,
      headers: {
        'Content-Type': 'mutipart/form-data',
      },
    },
    http,
  );
}
export function saveCriticalField(data) {
  return useAxios<ResponseType<any>>(
    ApiUrl.ConfMgt.saveCriticalField,
    {
      method: 'POST',
      data,
    },
    http,
  );
}
export function saveCriticalFieldDeviceId(data) {
  return useAxios<ResponseType<any>>(
    ApiUrl.ConfMgt.saveCriticalFieldDeviceId,
    {
      method: 'POST',
      data,
    },
    http,
  );
}

export function importRule(data) {
  return useAxios<ResponseType<any>>(
    `${ApiUrl.ConfMgt.importRule}`,
    {
      method: 'POST',
      data,
      // cancelToken,
      headers: {
        'Content-Type': 'mutipart/form-data',
      },
    },
    http,
  );
}

export function getExportRuleMessage() {
  return useAxios<ResponseType<any>>(
    ApiUrl.ConfMgt.getExportRuleMessage,
    {
      method: 'POST',
    },
    http,
  );
}

export function exportRule() {
  return useAxios<ResponseType<any>>(
    ApiUrl.ConfMgt.exportRule,
    {
      method: 'POST',
      responseType: 'blob',
    },
    http,
  );
}
