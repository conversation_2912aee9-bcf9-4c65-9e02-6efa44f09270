import { AddNull, ListRes, OptionsItem, ResponseType, SearchParam } from '@/types/common';
import {
  BatchReplayParam,
  DE_ListItem,
  DE_ListParam,
  DR_ListItem,
  DR_ListParam,
  SingleReplayParam,
  TaskListParam,
  TaskListRes,
  FileProcessDetail,
} from '@/types/dataReplay';
import ApiUrl from '@/utils/apiUrl';
import http from '@/utils/http';

/**
 * WaferLot下拉框列表
 * @SearchParam
 * @OptionsItem
 * @post
 */
export const getWaferLotDropdown = (param: SearchParam) => {
  return http.post<ResponseType<OptionsItem[]>>(ApiUrl.DataReplay.getWaferLotDropdown, param);
};
/**
 * File下拉列表
 * @SearchParam
 * @OptionsItem
 * @post
 */
export const getFileDropdown = (param: SearchParam) => {
  return http.post<ResponseType<OptionsItem[]>>(ApiUrl.DataReplay.getFileDropdown, param);
};
/**
 * 重播下拉框列表
 * @SearchParam
 * @OptionsItem
 * @post
 */
export const getDrDropdown = (param: SearchParam) => {
  return http.post<ResponseType<OptionsItem[]>>(ApiUrl.DataReplay.getDrDropdown, param);
};
/**
 * waferLot列表
 * @DE_ListParam
 * @ListRes
 * @post
 */
export const getWaferLotList = (param: AddNull<DE_ListParam>) => {
  return http.post<ResponseType<ListRes<DE_ListItem>>>(ApiUrl.DataReplay.getWaferLotList, param);
};
/**
 * file列表
 * @DE_ListParam
 * @ListRes
 * @post
 */
export const getFileList = (param: AddNull<DE_ListParam>) => {
  return http.post<ResponseType<ListRes<DE_ListItem>>>(ApiUrl.DataReplay.getFileList, param);
};
/**
 * 数据重播列表
 * @SearchParam
 * @OptionsItem
 * @post
 */
export const getDrList = (param: AddNull<DR_ListParam>) => {
  return http.post<ResponseType<ListRes<DR_ListItem>>>(ApiUrl.DataReplay.getDrList, param);
};

/**
 * 数据入库列表
 * @DE_ListParam
 * @ListRes
 * @post
 */
export const getDataEntryList = (param: AddNull<DE_ListParam>) => {
  return http.post<ResponseType<ListRes<DE_ListItem>>>(ApiUrl.DataReplay.getDataEntryList, param);
};
/**
 * 数据重播列表
 * @SearchParam
 * @OptionsItem
 * @post
 */
export const getDataReplayList = (param: AddNull<DR_ListParam>) => {
  return http.post<ResponseType<ListRes<DR_ListItem>>>(ApiUrl.DataReplay.getDataReplayList, param);
};

/**
 * 单行重播
 * @SearchParam
 * @OptionsItem
 * @post
 */
export const singleReplay = (param: AddNull<SingleReplayParam>) => {
  return http.post<ResponseType<undefined>>(ApiUrl.DataReplay.replay, param);
};
/**
 * lotWafer重播
 * @SearchParam
 * @OptionsItem
 * @post
 */
export const lotWaferReplay = (param: AddNull<BatchReplayParam>) => {
  return http.post<ResponseType<undefined>>(ApiUrl.DataReplay.lotWaferReplay, param);
};
/**
 * file重播
 * @SearchParam
 * @OptionsItem
 * @post
 */
export const fileReplay = (param: AddNull<BatchReplayParam>) => {
  return http.post<ResponseType<undefined>>(ApiUrl.DataReplay.fileReplay, param);
};
/**
 * 批量重播
 * @SearchParam
 * @OptionsItem
 * @post
 */
export const batchReplay = (param: AddNull<BatchReplayParam>) => {
  return http.post<ResponseType<undefined>>(ApiUrl.DataReplay.replay, param);
};
/**
 * 重试
 * @id
 * @undefined
 * @get
 */
export const retryReplay = (id: number) => {
  return http.get<ResponseType<undefined>>(ApiUrl.DataReplay.retryReplay, { params: { id: id } });
};
/**
 * 取消
 * @id
 * @undefined
 * @get
 */
export const cancelReplay = (id: number) => {
  return http.get<ResponseType<undefined>>(ApiUrl.DataReplay.cancelReplay, { params: { id: id } });
};
/**
 * 置顶
 * @id
 * @undefined
 * @get
 */
export const topReplay = (id: number) => {
  return http.get<ResponseType<undefined>>(ApiUrl.DataReplay.topReplay, { params: { id: id } });
};
/**
 * 重播任务下拉框
 * @TaskDropdownParam
 * @OptionsItem
 * @post
 */
export const getTaskDropdown = (param: SearchParam) => {
  return http.post<ResponseType<OptionsItem[]>>(ApiUrl.DataReplay.getTaskDropdown, param);
};
/**
 * 重播任务列表
 * @TaskListParam
 * @TaskListRes
 * @post
 */
export const getTaskList = (param: TaskListParam) => {
  return http.post<ResponseType<ListRes<TaskListRes>>>(ApiUrl.DataReplay.getTaskList, param);
};

/**
 * 获取文件流程详情
 * @fileWarehousingRecordId
 * @FileProcessDetail
 * @get
 */
export const getFileProcessDetail = (id: number) => {
  return http.get<ResponseType<FileProcessDetail>>(`${ApiUrl.DataReplay.getFileProcessDetail}/${id}`);
};
