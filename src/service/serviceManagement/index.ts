import { ResponseType } from '@/types/common';
import ApiUrl from '@/utils/apiUrl';
import http from '@/utils/http';
import { ListItem, ListParam, ListRes } from '@/views/service-management/typing';

/**
 * 列表接口
 * @ListParam
 * @ListRes
 * @post
 */
export const getPageList = (param: ListParam) => {
  return http.post<ResponseType<ListRes>>(ApiUrl.ServiceManagement.getList, param);
};
/**
 * 添加
 * @ListItem
 * @undefined
 * @post
 */
export const addConfig = (param: ListItem) => {
  return http.post<ResponseType<undefined>>(ApiUrl.ServiceManagement.addConfig, param);
};
/**
 * 更新
 * @ListItem
 * @undefined
 * @post
 */
export const updateConfig = (param: ListItem) => {
  return http.post<ResponseType<undefined>>(ApiUrl.ServiceManagement.updateConfig, param);
};
/**
 *删除
 * @id
 * @undefined
 * @post
 */
export const deleteConfig = (id: number) => {
  return http.delete<ResponseType<undefined>>(ApiUrl.ServiceManagement.deleteConfig, { params: { id } });
};
