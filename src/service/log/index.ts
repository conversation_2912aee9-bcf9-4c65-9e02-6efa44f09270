import { ResponseType } from '@/types/common';
import ApiUrl from '@/utils/apiUrl';
import http from '@/utils/http';
import qs from 'qs';

/**
 * 获取应用列表
 * @undefined
 * @string[]
 * @get
 */
export const getApps = () => {
  return http.get<ResponseType<string[]>>(ApiUrl.log.getApps);
};

/**
 * 根据应用获取主机列表
 * @app
 * @string[]
 * @get
 */
export const getHostsByApp = (app: string) => {
  return http.get<ResponseType<string[]>>(ApiUrl.log.getHosts, { params: { app } });
};

/**
 * 根据应用和主机获取实例列表
 * @app
 * @host
 * @string[]
 * @get
 */
export const getInstancesByAppAndHost = (app: string, host: string) => {
  return http.get<ResponseType<string[]>>(ApiUrl.log.getInstances, { params: { app, host } });
};

/**
 * 查询日志
 * @param params 查询参数
 * @returns 日志数据
 */
export const queryLogs = (params: any) => {
  return http.get<ResponseType<any>>(ApiUrl.log.queryLogs, { 
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  });
};

/**
 * 下载日志
 * @param params 查询参数
 * @returns 日志下载链接
 */
export const downloadLogs = (params: any) => {
  return http.get<any>(ApiUrl.log.downloadLogs, { 
    params, 
    responseType: 'blob',
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  });
};
