import { ListRes, ResponseType } from '@/types/common';
import {
  DataQualityRuleResult,
  ListItem,
  ListParam,
  QueryRuleParam,
  SaveParam,
  SwitchParam,
} from '@/types/dataQuality';

import ApiUrl from '@/utils/apiUrl';
import { exportServiceUtil } from '@/utils/download';
import http from '@/utils/http';

/**
 * 列表接口
 * @ListParam
 * @ListItem
 * @post
 */
export const getList = (param: ListParam) => {
  return http.post<ResponseType<ListRes<ListItem>>>(ApiUrl.DataQuality.getList, param);
};
/**
 * 导入
 * @file
 * @undefined
 * @post
 */
export const importRule = (param: FormData) => {
  return http.post<ResponseType<{}>>(ApiUrl.DataQuality.importRule, param);
};
/**
 * 导出
 * @undefined
 * @file
 * @post
 */
export const exportRule = () => {
  return exportServiceUtil(ApiUrl.DataQuality.exportRule, {
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
  });
};
/**
 * 保存
 * @SaveParam
 * @undefined
 * @post
 */
export const saveRule = (param: SaveParam) => {
  return http.post<ResponseType<undefined>>(ApiUrl.DataQuality.saveRule, param);
};
/**
 * 试运行
 * @SaveParam
 * @DataQualityRuleResult
 * @post
 */
export const tryRun = (param: SaveParam) => {
  return http.post<ResponseType<DataQualityRuleResult>>(ApiUrl.DataQuality.tryRun, param);
};
/**
 * 切换状态
 * @SwitchParams
 * @file
 * @get
 */
export const switchRuleStatus = (param: SwitchParam) => {
  return http.get<ResponseType<{}>>(ApiUrl.DataQuality.switchRuleStatus, { params: param });
};
/**
 * 规则结果
 * @QueryRuleParam
 * @DataQualityRuleResult
 * @get
 */
export const queryRuleResult = (param: QueryRuleParam) => {
  return http.get<ResponseType<DataQualityRuleResult>>(ApiUrl.DataQuality.queryRuleResult, { params: param });
};
/**
 * 规则详情
 * @id
 * @ListItem
 * @get
 */
export const queryDetail = (id: number) => {
  return http.get<ResponseType<ListItem>>(ApiUrl.DataQuality.queryDetail, { params: { id: id } });
};
/**
 * 规则删除
 * @id
 * @und
 * @undefined
 */
export const deleteRule = (id: number) => {
  return http.get<ResponseType<undefined>>(ApiUrl.DataQuality.deleteRule, { params: { id: id } });
};
