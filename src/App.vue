<template>
  <n-config-provider :locale="zhCN" :date-locale="dateZhCN" :theme-overrides="THEME">
    <n-notification-provider>
      <n-dialog-provider>
        <n-message-provider>
          <el-config-provider :locale="zhCn">
            <router-view />
          </el-config-provider>
        </n-message-provider>
      </n-dialog-provider>
    </n-notification-provider>
  </n-config-provider>
</template>

<script setup lang="ts">
import { zhCN, dateZhCN } from 'naive-ui';
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';

import THEME from './theme';
import { useConfig } from '@/hooks/useConfig';

useConfig(); // 请求系统参数
</script>
<style>
.n-config-provider {
  flex: 1;
  height: 0;
}
</style>
