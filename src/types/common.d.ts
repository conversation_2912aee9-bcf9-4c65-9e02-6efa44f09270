import { DE_ListParam, DR_ListParam, TaskDropdownParam } from './dataReplay/index.d';
import { DW_DropdownParam, YMS_DropdownParam } from './task/index.d';
import { ListParam } from './clearRulePreview/index';
import { getDropdown } from '@/service/dataReplay/index';

//  补充null工具函数
export type AddNull<T> = {
  [P in keyof T]: T[P] | null;
};

//  接口返回对象
export interface ResponseType<T> {
  code: string;
  msg: string;
  data: T;
}

export type AddTagParams = {
  title?: string; //tag显示名称
  url?: string; //跳转页面路由
  params?: string; //需要更新tag时传这个，不需要的话params直接拼在url里传过来
  isRefresh?: boolean;
  noCache?: boolean;
  other?: string; //打开其他系统时的publicPath，例spec、onedata-dc
  indexDB?: boolean; // 是否将参数存入 indexDB
};

export interface TableRes<T> {
  total: number;
  pageIndex: number;
  pageSize: number;
  data: T;
}

//  公共搜索
type SearchType = 'select' | 'input' | 'datePicker' | 'customDatePicker';
interface TotalSearchParam
  extends DE_ListParam,
    DR_ListParam,
    DW_DropdownParam,
    YMS_DropdownParam,
    TaskDropdownParam,
    ListParam {
  time?: [string, string];
}
export type SearchParam = AddNull<TotalSearchParam>;
export interface SearchItem {
  label: string;
  type: SearchType;
  key: keyof SearchParam; //传给后端枚举
  multiple?: boolean; //是否多选,默认单选
  maxLength?: number; //最大长度
  dateKey?: string; //时间控件key
  options?: DropdownItem[]; //下拉列表（前端）
  api?: typeof getDropdown; //下拉框列表接口
  required?: boolean; //是否必填
}
export interface ListRes<T> {
  pageIndex: number;
  pageSize: number;
  total: number;
  data: T[];
}

//  通用表头
export interface TableHeader {
  name: string;
  order: number;
  showName: string;
}

//  通用下拉框option
export type DropdownItem = {
  label: string;
  value: string | number;
  disabled?: boolean;
};

//  通用接口返回option
export type OptionsItem = {
  showName: string;
  name: string;
};

//  通用键值对
export type KeyValue<T> = {
  key: string;
  value: T;
};

//  搜索对应值
export type SelectDropdownEnum = Partial<Record<keyof SearchParam, string>>;
