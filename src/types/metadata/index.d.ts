export interface DatabaseParam {
  datasource: string;
  databaseName: string;
  tableName: string;
}

//  ----------------------------------------- 链接 -------------------------------------
export interface AddLinkParam extends DatabaseParam {
  name: string;
  link: string;
}
export interface LinkItem {
  id: number;
  source: string;
  database: string;
  table: string;
  linkName: string;
  link: string;
}

//  ----------------------------------------- 字段 -------------------------------------
export interface FieldListParam extends DatabaseParam {
  pageIndex: number;
  pageSize: number;
}
export interface UpdateFieldParam {
  id: number;
  cpLogic: string;
  ftLogic: string;
  info: string;
}
export interface DeleteFieldParam extends DatabaseParam {
  ids: number[];
}
export interface FieldItem {
  id: number;
  source: string;
  database: string;
  table: string;
  fieldName: string;
  type: string;
  defaultValue: string;
  position: number;
  comment: string;
  cpLogic: string;
  ftLogic: string;
  info: string;
  isEdit?: boolean;
  editLoading?: boolean;
  delLoading?: boolean;
}

//  ----------------------------------------- 描述 -------------------------------------
export interface UpdateDescriptionParam extends DatabaseParam {
  description: string;
}
export interface DescriptionItem extends DatabaseParam {
  id: number;
  description: string;
}

//  ----------------------------------------- 血缘 -------------------------------------
export interface TableLineageItem {
  fromTables?: DatabaseParam[];
  toTables?: DatabaseParam[];
  fields: {
    [key: string]: number;
  };
}
export interface FieldLineage {
  table: DatabaseParam;
  fields: {
    [key: string]: number;
  };
}
