export interface ListParam {
  testArea?: string;
  deviceId?: string;
  lotId?: string;
  waferNo?: string;
  lotType?: string;
  testStage?: string;
  factory?: string;
  factorySite?: string;
  sblotId?: string;
  fileCategory?: string;
  processStatus?: string;
  remoteOriginFilePath?: string;
  remoteOriginFileName?: string;
  fileName?: string;
  exceptionType?: string;
  startTime?: string;
  endTime?: string;
  jpaPageIndex?: number;
}

export interface ListItem {
  id: number;
  fileName: string;
  remoteOriginFilePath: string;
  remoteOriginFileName: string;
  fileCategory: string;
  factory: string;
  factoySite: string;
  givenTestArea: string;
  testArea: string;
  deviceId: string;
  lotId: string;
  waferNo: string;
  lotType: string;
  testStage: string;
  sblotId: string;
  createTime: string;
  previewResult: string;
  previewDetailFileHdfsPath: string;
  updateTime: string;
  processStatus: string;
  exceptionType: string;
  exceptionMessage: string;
  errorMessage: string;
  loading?: boolean;
  downloadLoading?: boolean;
}

//  文件上传提交类型
export interface UploadParam {
  factory?: string;
  factorySite?: string;
  givenTestArea?: string;
  fileCategory?: string;
  remoteOriginFilePath?: string;
  file?: string;
}

//  confResults
export interface ConfResult {
  order: number;
  field: string;
  tip: null | string;
  ruleExpression: null | string;
  value: string | number | null | {};
}
