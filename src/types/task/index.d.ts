export interface DialogInfo {
  isSingle: boolean;
  replayFileMaxCount?: number;
  replayType?: ReRunEnum;
  ids?: number[];
  row?: YMS_ListItem;
  searchParam?: SearchParam;
}

export interface DetailInfo {
  id?: number;
  testArea?: string;
  factory?: string;
  deviceId?: string;
  testStage?: string;
  lotId?: string;
  waferNo?: string;
  lotType?: string;
  dwLayer?: string;
  processStatus?: string;
  numExecutors?: string;
  executorCores?: string;
  executorMemory?: string;
  driverMemory?: string;
  parallelism?: string;
  extraConf?: string;
  errorMessage?: string;
}

//  ---------------------------- DW -------------------------------
export interface DW_DropdownParam {
  filterField?: string;
  lotIdList?: string[];
  testAreaList?: string[];
  factoryList?: string[];
  deviceIdList?: string[];
  waferNoList?: string[];
  lotTypeList?: string[];
  testStageList?: string[];
  processStatusList?: string[];
  dwLayerList?: string[];
  fileCategoryList?: string[];
}
export interface DW_ListParam extends DW_DropdownParam {
  pageIndex?: number;
  pageSize?: number;
}
export interface DW_ListItem {
  id: number;
  testArea: string;
  factory: string;
  deviceId: string;
  testStage: string;
  lotId: string;
  waferNo: string;
  lotType: string;
  dwLayer: string;
  taskUrl: string;
  processStatus: string;
  exceptionType: string;
  createTime: string;
  updateTime: string;
  errorMessage: string;
}
export interface DW_RerunParam {
  ids?: number[];
  flag?: string;
  numExecutors?: string;
  executorCores?: string;
  executorMemory?: string;
  driverMemory?: string;
  parallelism?: string;
  extraConf?: string;
}

//  ---------------------------- YMS -------------------------------
export interface YMS_DropdownParam {
  filterField?: string;
  testAreaList?: string[];
  factoryList?: string[];
  deviceIdList?: string[];
  testStageList?: string[];
  lotIdList?: string[];
  waferNoList?: string[];
  lotTypeList?: string[];
  processStatusList?: string[];
  optTypeList?: string[];
  executeEngineList?: string[];
}
export interface YMS_ListParam extends YMS_DropdownParam {
  pageIndex?: number;
  pageSize?: number;
}
export interface YMS_ListItem {
  id: number;
  name: string;
  optType: string;
  executeEngine: string;
  testArea: string;
  factory: string;
  deviceId: string;
  testStage: string;
  lotId: string;
  waferNo: string;
  lotType: string;
  executeTime: number;
  processStatus: string;
  exceptionType: string;
  errorMessage: string;
  createTime: string;
  updateTime: string;
}
export interface YMS_RerunParam {
  ids: number[];
}
export interface YMS_DetailInfo {
  id?: number;
  testArea?: string;
  factory?: string;
  deviceId?: string;
  testStage?: string;
  lotId?: string;
  waferNo?: string;
  lotType?: string;
  processStatus?: string;
  optType?: string;
  executeEngine?: string;
  createTime?: string;
  updateTime?: string;
  errorMessage?: string;
  dmtTaskDetailVos?: DmtTaskDetailVo[];
}
export interface DmtTaskDetailVo {
  id?: number;
  preTaskInstanceId?: number;
  name?: string;
  executeEngine?: string;
  numExecutors?: string;
  executorCores?: string;
  executorMemory?: string;
  driverMemory?: string;
  parallelism?: string;
  extraConf?: string;
  processStatus?: string;
  createTime?: string;
  updateTime?: string;
  modifyParamFlag?: boolean;
}
