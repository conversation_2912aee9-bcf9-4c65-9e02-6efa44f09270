export interface IUserInfo {
  admin: boolean;
  avatar: string;
  createBy: string;
  createTime: string;
  delFlag: string;
  email: string;
  loginDate: null | string;
  loginIp: string;
  nickName: string;
  phonenumber: string;
  companyCode?: string;
  sex: '1' | '0';
  status: string;
  updateBy: null | string;
  updateTime: null | string;
  userId: number;
  userName: string;
  roles?: any[];
}

export interface IPermissionInfo {
  permissions: string[];
  roles: string[];
  user: IUserInfo;
}

interface IResPermissionInfo extends IPermissionInfo {
  code: number | string;
  msg: string;
}
