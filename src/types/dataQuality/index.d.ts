import { KeyValue } from '../common';

interface TableResult {
  titles: KeyValue<string>[];
  rows: KeyValue<string>[][];
}
interface LineResult {
  xaxis: {
    title: string;
    values: string[];
  };
  yaxisList: {
    title: string;
    values: string[];
  }[];
}
interface DataQualityRuleResult {
  chartType: string;
  tableResult?: TableResult;
  lineResult?: LineResult;
}

export interface ListParam {
  pageIndex: number;
  pageSize: number;
}
export interface ListItem {
  id: number;
  sqlType: number;
  executeTimeType: number;
  qualityName: string;
  comment: string;
  mysqlQuerySql: string;
  ckTempTableSql: string;
  ckQuerySql: string;
  status: number;
  dayKeys: string[];
  dataQualityRuleResult: DataQualityRuleResult;
}

export interface SaveParam {
  id?: number;
  sqlType: number;
  executeTimeType: number;
  qualityName: string;
  comment: string;
  mysqlQuerySql: string;
  ckTempTableSql: string;
  ckQuerySql: string;
  status: number;
  dayKeys?: undefined;
  dataQualityRuleResult?: undefined;
}

export interface SwitchParam {
  id: number;
  status: 0 | 1;
}
export interface QueryRuleParam {
  id: number;
  dayKey: string;
}
