const en = {
  notCertified: {
    message: 'Not certified, please re - certify'
  },
  global: {
    ok: 'Determine',
    cancel: 'cancel',
    pleaseInput: 'Please enter',
    search: 'Search',
    reset: 'Reset',
    delete: 'Delete',
    copy: 'Copy',
    show: 'To view',
    operation: 'Operation',
    addSuccess: 'Add success!',
    delSuccess: 'Delete success!',
    systemError: 'System Error!',
    retry: 'Retry',
    emptyData: 'No data'
  },
  produceRule: {
    produceRuleList: 'Product Rule List',
    configuredDeviceNum: 'The number of devices has been configured',
    notConfiguredDeviceNum: 'The number of devices is not configured',
    addTestStage: 'Add the Test Stage',
    pleaseSelectDevice: 'Please select a Device',
    pleaseInputTestStage: 'Please enter Test Stage',
    pleaseSelectTestStage: 'Select Test Stage',
    copyAndOverwrite: 'Copy and overwrite'
  },
  ruleSet: {
    routeTitle: 'Rule settings',
    runTest: {
      modal: {
        closedConfirm: 'Task will be interrupted after exiting, do you want to confirm exit?'
      }
    }
  },
  batchRuleManagement: {
    list: {
      ruleResult: 'Results of rules',
      batchRuleResult: 'Results of batch rules',
      dashboard: 'Dashboard'
    },
    result: {
      ruleTabsRes: {
        error: 'Failed to get batch rule result'
      }
    }
  },
  interMediateResult: {
    title: 'Test Run'
  }
};

export default en;
