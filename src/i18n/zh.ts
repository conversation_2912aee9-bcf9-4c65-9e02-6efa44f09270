const zh = {
  notCertified: {
    message: '未认证，请重新认证'
  },
  global: {
    ok: '确定',
    cancel: '取消',
    next: '下一步',
    pleaseInput: '请输入',
    search: '搜索',
    reset: '重置',
    delete: '删除',
    copy: '复制',
    show: '查看',
    operation: '操作',
    addSuccess: '添加成功!',
    delSuccess: '删除成功!',
    systemError: '系统异常!',
    retry: '重试',
    emptyData: '暂无数据'
  },
  produceRule: {
    produceRuleList: '产品规则列表',
    configuredDeviceNum: '已配置 Device 数',
    notConfiguredDeviceNum: '未配置 Device 数',
    addTestStage: '添加Test Stage',
    pleaseSelectDevice: '请选择Device',
    pleaseInputTestStage: '请输入Test Stage',
    pleaseSelectTestStage: '请选择Test Stage',
    copyAndOverwrite: '复制并覆盖',
    createRule: '新建规则'
  },
  ruleSet: {
    routeTitle: '规则设置',
    runTest: {
      modal: {
        closedConfirm: '退出后任务将会中断，是否确认退出？'
      }
    }
  },
  batchRuleManagement: {
    list: {
      ruleResult: '规则结果',
      batchRuleResult: '批次规则结果',
      dashboard: '图表分析'
    },
    result: {
      ruleTabsRes: {
        error: '批次规则结果获取失败'
      }
    }
  },
  interMediateResult: {
    result: '试运行'
  }
};

export default zh;
