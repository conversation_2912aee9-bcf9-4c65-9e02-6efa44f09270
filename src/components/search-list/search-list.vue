<template>
  <div ref="pageSearchRef" :class="['pageSearch', 'h', { 'has-factory-highlight': showFactoryHighlight }]">
    <div v-for="item in props.searchList" :key="item.key" class="h">
      <span>{{ item.label }}<span v-if="item.required" class="required">*</span></span>

      <n-tooltip
        v-if="item.type === 'select'"
        :disabled="!isFieldDisabled(item)"
        trigger="hover"
        @show="() => handleTooltipShow(item)"
      >
        <template #trigger>
          <div
            :class="[
              'select-wrapper',
              { 'factory-highlight': item.key === 'factoryList' && showFactoryHighlight }
            ]"
            :data-factory="item.key === 'factoryList' ? 'true' : 'false'"
          >
            <n-select
              v-model:value="searchParam[item.key]"
              :multiple="item?.multiple"
              :filterable="true"
              :options="options"
              :max-tag-count="3"
              clearable
              :disabled="isFieldDisabled(item)"
              :render-option="renderOption"
              @update:show="(flag: boolean) => openDropdown(flag, item)"
              @click="() => handleSelectClick(item)"
              @focus="() => handleSelectFocus(item)"
            >
            </n-select>
          </div>
        </template>
        请先选择Factory
      </n-tooltip>

      <n-input v-if="item.type === 'input'" v-model:value="searchParam[item.key]" :maxlength="item.maxLength"></n-input>

      <n-date-picker
        v-if="item.type === 'datePicker'"
        v-model:value="searchParam[item.key]"
        :on-confirm="dateCommit"
        :on-clear="clearDate"
        type="daterange"
        clearable
      />
      <n-date-picker
        ref="customDateRef"
        v-if="item.type === 'customDatePicker'"
        v-model:value="dateKeyObj[item.dateKey ?? '']"
        :on-confirm="(val: [number, number]) => customDateCommit(val,item.key)"
        :on-clear="() => clearCustomDate(item.key)"
        :format="'yyyy-MM-dd HH:mm:ss'"
        type="datetimerange"
        clearable
      />
    </div>

    <div class="h">
      <n-button @click="submitSearch" type="primary">查询</n-button>
      <n-button @click="reset"> 重置 </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getDrDropdown } from '@/service/dataReplay';
import { SearchItem, SearchParam, DropdownItem, SelectDropdownEnum, AddNull } from '@/types/common';
import { SUCCESS_CODE } from '@/utils/http';
import dayjs from 'dayjs';
import { NTooltip, SelectOption, createDiscreteApi } from 'naive-ui';
import { nextTick } from 'vue';

const { message } = createDiscreteApi(['message']);

const ENUM: SelectDropdownEnum = {
  testArea: 'TEST_AREA',
  testAreaList: 'TEST_AREA',
  factory: 'FACTORY',
  factoryList: 'FACTORY',
  sblotIdList: 'SBLOT_ID',
  factorySite: 'FACTORY_SITE',
  deviceId: 'DEVICE_ID',
  deviceIdList: 'DEVICE_ID',
  lotId: 'LOT_ID',
  lotIdList: 'LOT_ID',
  sblotId: 'SBLOT_ID',
  waferNo: 'WAFER_NO',
  waferNoList: 'WAFER_NO',
  lotType: 'LOT_TYPE',
  lotTypeList: 'LOT_TYPE',
  testStage: 'TEST_STAGE',
  testStageList: 'TEST_STAGE',
  testProgramList: 'TEST_PROGRAM',
  replayTypeList: 'REPLAY_TYPE',
  stepList: 'STEP',
  exceptionType: 'EXCEPTION_TYPE',
  statusList: 'STATUS',
  processStatus: 'PROCESS_STATUS',
  filterStatusList: 'FILTER_PROCESS_STATUS',
  exceptionMessage: 'EXCEPTION_MESSAGE',
  exceptionMessageList: 'EXCEPTION_MESSAGE',
};

const props = defineProps<{ searchList: SearchItem[]; otherParams?: SearchParam; requireFactoryFirst?: boolean }>();
const emit = defineEmits(['search']);

const pageSearchRef = ref();
const searchParam = reactive<AddNull<{ [key: string]: any }>>({});
const options = ref<DropdownItem[]>([]);
const dateKeyObj = ref<{ [key: string]: any }>({});

// Factory高亮提醒状态
const showFactoryHighlight = ref(false);
let highlightTimer: NodeJS.Timeout | null = null;

const { height } = useElementSize(pageSearchRef);

//  ?自定义下拉框渲染函数
const renderOption = ({ node, option }: { node: VNode; option: SelectOption }) =>
  h(NTooltip, null, {
    trigger: () => node,
    default: () => `${option.label}`,
  });

//  调用下拉框列表接口
const getDropdownApi = async (apiFn: typeof getDrDropdown, key: keyof SearchParam) => {
  const param = { ...searchParam, ...props?.otherParams };
  param[key] = undefined;

  const res = await apiFn(param);
  if (res.data.code === SUCCESS_CODE) {
    options.value = res.data.data?.map((item) => ({ label: item.showName, value: item.name }));
  }
};
//  打开下拉框
const openDropdown = (flag: boolean, item: SearchItem) => {
  if (!flag) return;

  // 检查Factory是否必选且未选择
  if (checkFactoryRequired(item)) {
    return;
  }

  options.value = [];
  searchParam.filterField = ENUM[item.key];

  if (item?.options) {
    options.value = item.options;
  } else if (item?.api) {
    getDropdownApi(item.api, item.key);
  }
};
//  日期确定接口
const dateCommit = (val: [number, number]) => {
  searchParam.startTime = dayjs(val[0]).format('YYYY-MM-DD 00:00:00');
  searchParam.endTime = dayjs(val[1]).format('YYYY-MM-DD 23:59:59');
};
const clearDate = () => {
  searchParam.startTime = undefined;
  searchParam.endTime = undefined;
  searchParam.time = undefined;
};
//  ?自定义时间日期确定函数 （避免与老逻辑冲突）
const customDateCommit = (val: [number, number], key: string) => {
  searchParam[key] = [dayjs(val[0]).format('YYYY-MM-DD HH:mm:ss'), dayjs(val[1]).format('YYYY-MM-DD HH:mm:ss')];
};
//  ?自定义时间日期取消函数
const clearCustomDate = (key: string) => {
  searchParam[key] = null;
  // 同时清除dateKeyObj中的值，确保可以重新选择
  const dateKey = props.searchList.find(item => item.key === key)?.dateKey ?? '';
  if (dateKey) {
    dateKeyObj.value[dateKey] = null;
  }
};

const formatterParam = () => {
  const params: any = {};
  props.searchList?.map((item) => {
    if (searchParam[item.key]) params[item.key] = searchParam[item.key];
    if (searchParam['startTime']) params['startTime'] = searchParam['startTime'];
    if (searchParam['endTime']) params['endTime'] = searchParam['endTime'];
    if (searchParam['time']) params['time'] = undefined;
  });
  return params;
};
//  搜索
const submitSearch = () => {
  // 只检查Factory字段是否必选且未选择（仅在启用Factory优先限制时）
  if (props.requireFactoryFirst) {
    const factoryItem = props.searchList.find(item => item.key === 'factoryList');
    if (factoryItem?.required && (!searchParam.factoryList || searchParam.factoryList.length === 0)) {
      message.warning('请先选择Factory');
      triggerFactoryHighlight();
      return;
    }
  }

  emit('search', formatterParam());
};

// 判断字段是否应该被禁用
const isFieldDisabled = (item: SearchItem) => {
  // 如果没有启用Factory优先限制，则不禁用任何字段
  if (!props.requireFactoryFirst) {
    return false;
  }

  // Factory字段本身不禁用
  if (item.key === 'factoryList') {
    return false;
  }

  // 所有其他字段都需要检查Factory是否已选择
  const factoryItem = props.searchList.find(searchItem => searchItem.key === 'factoryList');
  if (factoryItem?.required && (!searchParam.factoryList || searchParam.factoryList.length === 0)) {
    return true;
  }

  return false;
};

// 检查Factory的通用函数
const checkFactoryRequired = (item: SearchItem) => {
  if (props.requireFactoryFirst && item.key !== 'factoryList') {
    const factoryItem = props.searchList.find(searchItem => searchItem.key === 'factoryList');
    if (factoryItem?.required && (!searchParam.factoryList || searchParam.factoryList.length === 0)) {
      message.warning('请先选择Factory');
      triggerFactoryHighlight();
      return true;
    }
  }
  return false;
};

// 处理下拉框点击事件
const handleSelectClick = (item: SearchItem) => {
  checkFactoryRequired(item);
};

// 处理下拉框获得焦点事件
const handleSelectFocus = (item: SearchItem) => {
  checkFactoryRequired(item);
};

// 处理tooltip显示事件
const handleTooltipShow = (item: SearchItem) => {
  // 当tooltip显示时，说明字段被禁用了，触发红框
  if (item.key !== 'factoryList') {
    triggerFactoryHighlight();
  }
};

// 触发Factory高亮提醒
const triggerFactoryHighlight = () => {
  // 清除之前的定时器
  if (highlightTimer) {
    clearTimeout(highlightTimer);
    highlightTimer = null;
  }

  // 先关闭高亮，确保重新触发动画
  showFactoryHighlight.value = false;

  // 使用nextTick确保DOM更新后再开启高亮
  nextTick(() => {
    showFactoryHighlight.value = true;

    // 3秒后停止高亮
    highlightTimer = setTimeout(() => {
      showFactoryHighlight.value = false;
      highlightTimer = null;
    }, 3000);
  });
};

//  重置
const reset = () => {
  clearDate();
  Object.keys(dateKeyObj.value)?.map((key: string) => {
    dateKeyObj.value[key] = null;
  });

  props.searchList?.map((item: SearchItem) => {
    searchParam[item.key] = null;
  });
};

defineExpose({
  reset,
  height,
});
</script>

<style scoped lang="scss">
.pageSearch {
  flex-wrap: wrap;
  max-height: 180px;
  overflow: auto;

  &.has-factory-highlight {
    overflow: visible;
  }

  > div {
    margin: 0 16px 16px 0;

    > span {
      margin: auto 8px auto 0;

      > .required {
        color: red;
      }
    }

    > .n-select,
    .n-input,
    .select-wrapper {
      width: 180px;
      margin: auto 0;
    }

    > .n-button {
      margin: auto 8px auto 0;
    }

    > .n-date-picker {
      margin: auto;
    }
  }
}

// Factory高亮提醒样式
.select-wrapper {
  position: relative;

  &.factory-highlight {
    position: relative;
    z-index: 2147483647;
    &::before {
      content: '';
      position: absolute;
      top: -4px;
      left: -4px;
      right: -4px;
      bottom: -4px;
      border: 3px solid #ff4d4f;
      border-radius: 8px;
      pointer-events: none;
      z-index: 2147483647;
      animation: factoryBorderBlink 0.6s ease-in-out infinite alternate;
      box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.9);
    }

    &::after {
      content: '';
      position: absolute;
      top: -4px;
      left: -4px;
      right: -4px;
      bottom: -4px;
      border-radius: 8px;
      pointer-events: none;
      z-index: 2147483646;
      animation: factoryGlow 0.6s ease-in-out infinite alternate;
    }
  }
}

@keyframes factoryBorderBlink {
  0% {
    border-color: #ff4d4f;
    opacity: 1;
  }
  100% {
    border-color: #ff7875;
    opacity: 0.7;
  }
}

@keyframes factoryGlow {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.4);
  }
  100% {
    box-shadow: 0 0 0 6px rgba(255, 77, 79, 0.1);
  }
}
</style>
