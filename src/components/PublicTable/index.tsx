// 表头、数据、是否可选

import { CheckboxValueType, ElCheckbox, TableV2 } from 'element-plus';
import { FunctionalComponent } from 'vue';
import { cloneDeep } from 'lodash';
import { Column, SortBy } from 'element-plus/lib/components/table-v2/index.js';

import SvgIcon from '@/components/SvgIcon.vue';

export type SelectionCellProps = {
  value: boolean;
  intermediate?: boolean;
  onChange: (value: CheckboxValueType) => void;
};

export interface TableV2Type {
  data: any;
  width?: number;
  height?: number;
  columns: Column[];
  columnSort?: (sortBy: SortBy) => void;
  sortBy?: SortBy;
  rowEventHandlers?: any;
  isSelect?: boolean;
  onDataChange?: (row: any) => void;
}

export const PublicTable: FunctionalComponent<TableV2Type> = ({
  columns,
  data,
  width,
  height,
  isSelect = false,
  columnSort,
  sortBy,
  rowEventHandlers,
  onDataChange,
}) => {
  const tableHead = cloneDeep(columns);

  const SelectionCell: FunctionalComponent<SelectionCellProps> = ({ value, intermediate = false, onChange }) => {
    return (
      <ElCheckbox
        onChange={onChange}
        modelValue={value}
        indeterminate={intermediate}
        onClick={(e) => e.stopPropagation()}
      />
    );
  };

  if (isSelect) {
    tableHead.unshift({
      key: 'selection',
      width: 32,
      fixed: true,
      cellRenderer: ({ rowData, rowIndex }) => {
        const onChange = (value: CheckboxValueType) => {
          const newRow = { ...rowData, checked: value };
          const newData = [...toRaw(data)];
          newData[rowIndex] = newRow;
          onDataChange && onDataChange(newData);
        };
        return <SelectionCell value={rowData.checked} onChange={onChange} />;
      },

      headerCellRenderer: () => {
        const _data = unref(data);
        const onChange = (value: CheckboxValueType) => {
          const newData = _data.map((row) => {
            if (!row?.disabled) {
              row.checked = value;
            }
            return row;
          });
          onDataChange && onDataChange(newData);
        };
        const allSelected = _data.every((row) => row.checked);
        const containsChecked = _data.some((row) => row.checked);

        return <SelectionCell value={allSelected} intermediate={containsChecked && !allSelected} onChange={onChange} />;
      },
    });
  }

  return (
    <TableV2
      fixed
      data={data}
      width={width}
      rowHeight={28}
      height={height}
      headerHeight={28}
      columns={tableHead}
      onColumnSort={columnSort}
      // style={{ overflow: 'auto' }}
      sort-by={sortBy}
      rowEventHandlers={rowEventHandlers}
      scrollbar-always-on={true}
    >
      {{
        empty: () => (
          <div
            style={{
              width: width + 'px',
              height: height - 28 + 'px',
              display: 'flex',
              flexDirection: 'column',
              position: 'absolute',
            }}
          >
            <SvgIcon class="el-icon" name={'empty_nodata'} style={{ margin: 'auto auto 0 auto', fontSize: '60px' }} />
            <p style={{ margin: '0 auto auto auto', color: '#d6d6d6' }}>暂无数据</p>
          </div>
        ),
      }}
    </TableV2>
  );
};
