<template>
  <div class="chart-warp" ref="chartDom" v-show="(props.options?.series as any[])?.length > 0"></div>
  <div class="chart-warp v" v-if="(props.options?.series as any[])?.length < 1"><span>暂无数据</span></div>
</template>

<script setup lang="ts">
import { EChartsOption } from 'echarts';
import { oneDataEchartInit } from '@/utils/echarts/config/init';

const props = defineProps<{
  options: EChartsOption;
}>();
const chartDom = ref<HTMLDivElement>();
const { width, height } = useElementSize(chartDom);

let myChart: any = null;

watchDebounced(
  [width, props],
  () => {
    props.options && myChart.setOption(props.options, { notMerge: true });
    myChart.resize({
      width: width.value,
      height: height.value,
    });
  },
  { debounce: 200, maxWait: 1000 },
);

onMounted(() => {
  if (chartDom.value) {
    myChart = oneDataEchartInit(chartDom.value, 'canvas');
    props.options && myChart.setOption(props.options);
  }
});
</script>

<style lang="scss" scoped>
.chart-warp {
  height: 100%;

  > span {
    margin: auto;
  }
}
</style>
