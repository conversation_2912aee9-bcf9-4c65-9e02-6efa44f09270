import { App } from 'vue';
import { createRouter, createWebHashHistory, RouteRecordRaw, RouterOptions } from 'vue-router';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: '/data-replay',
  },
  {
    path: '/data-replay',
    name: 'dataReplay',
    component: () => import('@/views/data-replay/data-replay.vue'),
  },
  {
    path: '/task',
    name: 'task',
    component: () => import('@/views/task/task.vue'),
  },
  {
    path: '/data-quality',
    name: 'dataQuality',
    component: () => import('@/views/data-quality/data-quality.vue'),
  },
  {
    path: '/data-quality-detail',
    name: 'dataQualityDetail',
    component: () => import('@/views/data-quality/data-quality-detail.vue'),
  },
  {
    path: '/metadata',
    name: 'metadata',
    component: () => import('@/views/metadata/metadata.vue'),
  },
  {
    path: '/metadata-detail',
    name: 'metadataDetail',
    component: () => import('@/views/metadata/metadata-detail.vue'),
  },
  {
    path: '/clear-rule-preview',
    name: 'clearRulePreview',
    component: () => import('@/views/clear-rule-preview/clear-rule-preview.vue'),
  },
  {
    path: '/service-management',
    name: 'serviceManagement',
    component: () => import('@/views/service-management/index.vue'),
  },
  {
    path: '/priority',
    name: 'priority',
    component: () => import('@/views/priority/index.vue'),
  },
  {
    path: '/bin-definition',
    name: 'binDefinition',
    meta: {
      title: '测试程序Bin定义管理',
    },
    component: () => import('@/views/bin-definition/index.vue'),
  },
  //
  {
    path: '/data-quality',
    name: 'DataQuality',
    children: [
      {
        path: 'data-quality-overview',
        name: 'DataQualityOverview',
        meta: {
          title: '数据质量概览',
          // permission: 'dc:data-quality:data-quality-overview',
        },
        component: () => import('@/views/data-center-quality/DataQualityOverview/index.vue'),
      },
      {
        path: 'data-exact',
        name: 'DataExact',
        meta: {
          title: '数据准确性校验',
          permission: 'dc:data-quality:data-exact',
        },
        component: () => import('@/views//data-center-quality/DataExact/index.vue'),
      },
      {
        path: 'data-integrity',
        name: 'DataIntegrity',
        meta: {
          title: '数据完整性校验',
          permission: 'dc:data-quality:data-integrity',
        },
        component: () => import('@/views//data-center-quality/DataIntegrity/index.vue'),
      },
      {
        path: 'data-timeliness',
        name: 'DataTimeliness',
        meta: {
          title: '数据入库时效性分析',
          permission: 'dc:data-quality:data-timeliness',
        },
        component: () => import('@/views//data-center-quality/DataTimeliness/index.vue'),
      },
    ],
  },
  {
    path: '/dataRecoveryManagement',
    name: 'dataRecoveryManagement',
    meta: {
      permission: 'dc:dataRecovery:management',
    },
    component: () => import('@/views/data-center-quality/DataRecovery/index.vue'),
  },
  {
    path: '/dataRecovery',
    name: 'dataRecovery',
    meta: {
      permission: 'dc:dataRecovery:create',
    },
    component: () => import('@/views/data-center-quality/DataRecoveryPage/DataRecoveryPage.vue'),
  },
  {
    path: '/repairResult',
    name: 'repairResult',
    meta: {
      permission: 'dc:dataRecovery:result',
    },
    component: () => import('@/views/data-center-quality/RepairResult/RepairResult.vue'),
  },
  {
    path: '/conf-mgt',
    name: 'confManagement',
    meta: {
      title: '自动入库规则管理',
    },
    component: () => import('@/views/conf-mgt/index.vue'),
  },
  {
    path: '/log',
    name: 'log',
    component: () => import('@/views/log/index.vue'),
  },
] as Array<RouteRecordRaw>;

const router = createRouter({
  history: createWebHashHistory(import.meta.env.VITE_APP_BASE_URL),
  routes,
} as RouterOptions);

export const setupRouter = (app: App<Element>) => {
  app.use(router);
};

export default router;
