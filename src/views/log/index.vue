
<template>
  <PageLayout>
    <template #header>
      <h2>日志查询</h2>
    </template>
    <template #content>
      <div class="v">
          <div class="query-form">
            <div class="form-item h app-item">
              <span>App <span class="required">*</span></span>
              <n-select
                v-model:value="formState.app"
                placeholder="请选择应用"
                :options="appOptions"
                clearable
                filterable
                class="form-select"
                @update:value="handleAppChange"
              />
            </div>

            <div class="form-item h">
              <span>Host</span>
              <n-select
                v-model:value="formState.host"
                placeholder="请选择主机"
                :options="hostOptions"
                clearable
                class="form-select"
                @update:value="handleHostChange"
              />
            </div>

            <div class="form-item h instance-item">
              <span>Instance</span>
              <n-select
                v-model:value="formState.instance"
                placeholder="请选择实例"
                :options="instanceOptions"
                clearable
                class="form-select"
              />
            </div>

            <div class="form-item h log-level-item">
              <span>Log Level</span>
              <n-select
                v-model:value="formState.logLevel"
                placeholder="请选择日志级别"
                :options="logLevelOptions"
                clearable
                multiple
                class="form-select"
              />
            </div>

            <div class="form-item h">
              <span>Time Range <span class="required">*</span></span>
              <n-date-picker
                v-model:value="formState.timeRange"
                type="datetimerange"
                clearable
                class="form-date-picker"
              />
            </div>

            <div class="form-item h keywords-item">
              <span>Keyword</span>
              <div class="keywords-wrapper">
                <div class="keywords-input">
                  <n-input
                    v-model:value="formState.keyword"
                    placeholder="关键词(多个以&&分割)或正则表达式(以regexp=开头)"
                    clearable
                    class="form-input"
                  />
                </div>
              </div>
              <div class="keywords-buttons">
                <n-button type="primary" @click="handleQuery">查询</n-button>
                <n-button @click="resetForm" style="margin-left: 12px;">重置</n-button>
              </div>
            </div>
          </div>

        <!-- 日志显示区域 -->
        <n-card class="log-result-card" size="small">
          <template #header>
            <div class="card-header">
              <div class="log-info" v-if="logCount > 0">
                <span v-if="logCount <= logMaxCount">日志行数: {{ logCount }}</span>
                <span v-else>日志行数: {{ logCount }}，当前仅显示{{ logMaxCount }}条</span>
              </div>
              <div class="card-header-actions">
                <n-tooltip trigger="hover">
                  <template #trigger>
                    <n-button text @click="copyToClipboard">
                      <n-icon size="18">
                        <copy-icon />
                      </n-icon>
                    </n-button>
                  </template>
                  复制到剪贴板
                </n-tooltip>
                <n-tooltip trigger="hover">
                  <template #trigger>
                    <n-button text @click="handleDownloadLogs">
                      <n-icon size="18">
                        <download-icon />
                      </n-icon>
                    </n-button>
                  </template>
                  下载日志
                </n-tooltip>
              </div>
            </div>
          </template>
          <div class="log-content-wrapper" style="overflow: auto">
            <n-spin :show="logLoading">
              <n-empty v-if="logData.length === 0" description="暂无日志数据" class="empty-container">
              </n-empty>
              <template v-else>
                <n-scrollbar class="log-scrollbar" trigger="hover" style="max-height: 100%; height: 100%;">
                  <div class="log-content">{{ logText }}</div>
                </n-scrollbar>
              </template>
            </n-spin>
          </div>
        </n-card>
      </div>
    </template>
  </PageLayout>
</template>

<script setup lang="ts">
import PageLayout from '../common/page-layout.vue';
import { ref, reactive, h, computed, onMounted, watch } from 'vue';
import {
  NSelect,
  NButton,
  NCard,
  NInput,
  NIcon,
  NEmpty,
  NDatePicker,
  NSpin,
  NScrollbar
} from 'naive-ui';
import { CopyOutline as CopyIcon, DownloadOutline as DownloadIcon } from '@vicons/ionicons5';
import { useMessage } from 'naive-ui';
import { getApps, getHostsByApp, getInstancesByAppAndHost, queryLogs, downloadLogs } from '@/service/log';

// 创建图标组件
const copyIcon = () => h(CopyIcon);
const downloadIcon = () => h(DownloadIcon);

// 消息提示
const message = useMessage();

// 日志文本框引用

// 表单数据
const formState = reactive({
  app: null,
  host: null,
  instance: null,
  logLevel: [], // 保持数组类型以支持多选
  timeRange: [Date.now() - 3600000, Date.now()], // 默认近1小时
  keyword: null
});


// 日志数据示例
const logData = ref([]);

// 日志区域loading状态
const logLoading = ref(false);

// 日志总条数
const logCount = ref(0);

// 日志最大显示条数
const logMaxCount = ref(10000);

// 日志最大下载条数
const downloadMaxCount = ref(100000);

// 是否已执行查询的标志
const hasQueried = ref(false);

// 计算日志文本
const logText = computed(() => {
  return logData.value.join('\n');
});

// 日志级别选项
const logLevelOptions = ref([
  { label: 'INFO', value: 'INFO' },
  { label: 'WARN', value: 'WARN' },
  { label: 'ERROR', value: 'ERROR' },
  { label: 'DEBUG', value: 'DEBUG' }
]);

// 应用选项
const appOptions = ref([]);

// 主机选项
const hostOptions = ref([]);

// 实例选项
const instanceOptions = ref([]);

// 获取应用列表
const fetchApps = async () => {
  try {
    const res = await getApps();
    if (res.data && res.data.data) {
      appOptions.value = res.data.data.map((item: string) => ({
        label: item,
        value: item
      }));
    } else {
      message.error('获取应用列表失败');
    }
  } catch (error) {
    message.error('获取应用列表失败');
  }
};

// 根据应用获取主机列表
const fetchHostsByApp = async (app: string) => {
  // 清除已选择的主机和实例
  formState.host = null;
  formState.instance = null;
  
  // 清空主机和实例选项列表
  hostOptions.value = [];
  instanceOptions.value = [];
  
  // 调用实际的API获取主机列表
  try {
    const res = await getHostsByApp(app);
    if (res.data && res.data.data) {
      hostOptions.value = res.data.data.map((item: string) => ({
        label: item,
        value: item
      }));
    }
  } catch (error) {
    message.error('获取主机列表失败');
  }
};

// 根据应用和主机获取实例列表
const fetchInstanceByAppAndHost = async (app: string, host: string) => {
  // 清除已选择的实例
  formState.instance = null;
  
  // 清空实例选项列表
  instanceOptions.value = [];
  
  // 调用实际的API获取实例列表
  try {
    const res = await getInstancesByAppAndHost(app, host);
    if (res.data && res.data.data) {
      instanceOptions.value = res.data.data.map((item: string) => {
        // 只取#以及#之后的内容作为label
        const label = item.includes('#') ? item.split('#')[1] || item : item;
        return {
          label: label,
          value: item
        };
      });
    }
  } catch (error) {
    message.error('获取实例列表失败');
  }
};

// 处理主机选择变化
const handleHostChange = (value: string | null) => {
  if (value && formState.app) {
    fetchInstanceByAppAndHost(formState.app, value);
  } else {
    // 如果清空了主机选择，则清空实例的选择及选项
    formState.instance = null;
    instanceOptions.value = [];
  }
};

// 处理应用选择变化
const handleAppChange = (value: string | null) => {
  if (value) {
    fetchHostsByApp(value);
  } else {
    // 如果清空了应用选择，则清空主机和实例的选择及选项
    formState.host = null;
    formState.instance = null;
    hostOptions.value = [];
    instanceOptions.value = [];
  }
};

// 处理查询
const handleQuery = async () => {
  // 校验app字段不能为空
  if (!formState.app) {
    message.warning('请选择应用');
    return;
  }
  
  // 时间格式化函数
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };
  
  // 组装查询参数
  const queryParams = {
    app: formState.app,
    host: formState.host,
    instance: formState.instance,
    logLevel: formState.logLevel,
    startTime: formState.timeRange?.[0] ? formatTime(formState.timeRange[0]) : null,
    endTime: formState.timeRange?.[1] ? formatTime(formState.timeRange[1]) : null,
    queryType: formState.keyword?.startsWith('regexp=') ? 'regexp' : 'keyword',
    queryValue: formState.keyword?.startsWith('regexp=') ? formState.keyword.substring(7) : formState.keyword
  };
  
  // 开启loading
  logLoading.value = true;
  
  // 调用查询接口
  try {
    const res = await queryLogs(queryParams);
    if (res.data && res.data.data) {
      logData.value = res.data.data.logs || [];
      logCount.value = res.data.data.logCount || 0;
    } else {
      logData.value = [];
      logCount.value = 0;
    }
    // 设置查询标志为true
    hasQueried.value = true;
  } catch (error) {
    message.error('查询日志失败');
    logData.value = [];
    logCount.value = 0;
  } finally {
    // 关闭loading
    logLoading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  formState.app = null;
  formState.host = null;
  formState.instance = null;
  formState.keyword = null;
  formState.timeRange = [Date.now() - 3600000, Date.now()]; // 重置为近1小时
  formState.logLevel = []; // 重置为数组
};

// 组件挂载时执行
onMounted(() => {
  // 获取应用列表
  fetchApps();
});

// 监听表单变化，重置查询标志
watch(formState, () => {
  hasQueried.value = false;
}, { deep: true });

// 复制日志到剪贴板
const copyToClipboard = () => {
  if (!logText.value) {
    message.warning('没有日志内容可复制');
    return;
  }
  
  // 检查浏览器是否支持 Clipboard API
  if (navigator.clipboard && window.isSecureContext) {
    // 使用 Clipboard API
    navigator.clipboard.writeText(logText.value).then(() => {
      message.success('日志已复制到剪贴板');
    }).catch(() => {
      message.error('复制失败');
    });
  } else {
    // 降级处理：使用传统的 execCommand 方法
    try {
      const textarea = document.createElement('textarea');
      textarea.value = logText.value;
      textarea.setAttribute('readonly', '');
      textarea.style.position = 'absolute';
      textarea.style.left = '-9999px';
      document.body.appendChild(textarea);
      textarea.select();
      
      const success = document.execCommand('copy');
      document.body.removeChild(textarea);
      
      if (success) {
        message.success('日志已复制到剪贴板');
      } else {
        message.error('复制失败');
      }
    } catch (error) {
      message.error('复制失败');
    }
  }
};

// 下载日志
const handleDownloadLogs = async () => {
  // 检查是否已执行查询
  if (!hasQueried.value) {
    message.warning('请先查询日志');
    return;
  }
  
  if (!formState.app) {
    message.warning('请选择应用');
    return;
  }
  
  // 检查日志数量是否超出下载限制
  if (logCount.value > downloadMaxCount.value) {
    message.warning(`日志行数超出限制（最大${downloadMaxCount.value}行），无法下载`);
    return;
  }
  
  // 时间格式化函数
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };
  
  // 组装查询参数
  const queryParams = {
    app: formState.app,
    host: formState.host,
    instance: formState.instance,
    logLevel: formState.logLevel,
    startTime: formState.timeRange?.[0] ? formatTime(formState.timeRange[0]) : null,
    endTime: formState.timeRange?.[1] ? formatTime(formState.timeRange[1]) : null,
    queryType: formState.keyword?.startsWith('regexp=') ? 'regexp' : 'keyword',
    queryValue: formState.keyword?.startsWith('regexp=') ? formState.keyword.substring(7) : formState.keyword
  };
  
  try {
    const res = await downloadLogs(queryParams);
    if (res) {
      // 从响应头中获取文件名
      let filename = `logs_${new Date().getTime()}.log`; // 默认文件名
      
      // 尝试从Content-Disposition头获取文件名
      const contentDisposition = res.headers['content-disposition'];
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
        if (filenameMatch && filenameMatch[1]) {
          filename = decodeURIComponent(filenameMatch[1]);
        }
      }
      
      const blob = new Blob([res.data], { type: 'text/plain;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      message.success('日志下载成功');
    } else {
      message.error('日志下载失败');
    }
  } catch (error) {
    message.error('日志下载失败');
  }
};

</script>

<style scoped lang="scss">
.v {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 16px;
  box-sizing: border-box;
  background-color: var(--n-color-embedded);
}

.query-card {
  flex-shrink: 0;
  margin-bottom: 16px;

  :deep(.n-card-header) {
    padding: 12px 16px;
    border-bottom: 1px solid var(--n-border-color);
  }

  :deep(.n-card__content) {
    padding: 16px;
  }
}

.log-result-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;

  :deep(.n-card-header) {
    flex-shrink: 0;
    padding: 12px 16px;
    border-bottom: 1px solid var(--n-border-color);
  }

  :deep(.n-card__content) {
    flex: 1;
    padding: 0;
    overflow: hidden;
    min-height: 0; /* 添加min-height: 0确保内容区域正确收缩 */
  }
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-header > .log-info,
.card-header > .card-header-actions {
  flex: 0 0 auto;
}

.card-header > .log-info {
  margin-left: 0; /* 移除原来的margin-left: auto，使其显示在最左边 */
}

.card-header-actions {
  display: flex;
  gap: 8px;
  margin-left: auto; /* 将操作按钮移到右边 */
}

.query-form-container {
  background: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.query-form-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-bottom: 1px solid var(--n-border-color);
}

.required {
  color: red;
}

.log-result-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0; /* 添加min-height: 0确保flex子元素正确收缩 */

  :deep(.n-card-header) {
    flex-shrink: 0;
    padding: 12px 16px;
    border-bottom: 1px solid var(--n-border-color);
  }

  :deep(.n-card__content) {
    flex: 1;
    padding: 0;
    overflow: hidden;
    min-height: 0; /* 添加min-height: 0确保内容区域正确收缩 */
  }
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-header > .log-info,
.card-header > .card-header-actions {
  flex: 0 0 auto;
}

.card-header > .log-info {
  margin-left: 0; /* 移除原来的margin-left: auto，使其显示在最左边 */
}

.card-header-actions {
  display: flex;
  gap: 8px;
  margin-left: auto; /* 将操作按钮移到右边 */
}

.query-form-container {
  margin-bottom: 16px;
}

.query-form-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-bottom: 1px solid var(--n-border-color);
}

.query-form {
  display: flex;
  flex-wrap: wrap;
  padding: 20px 0 0 20px;
  
  .form-item {
    margin: 0 16px 16px 0;
    
    > span {
      margin: auto 8px auto 0;
      flex-shrink: 0;
      width: 120px;
      text-align: right;
    }
    
    .form-select,
    .n-select {
      width: 180px;
      margin: auto 0;
    }
    
    .form-input,
    .n-input {
      width: 450px; /* 从400px增大到450px */
      margin: auto 0;
    }
    
    .form-date-picker,
    .n-date-picker {
      width: 400px;
      margin: auto 0;
    }
    
    .n-button {
      margin: auto 8px auto 0;
    }
  }
  
  // 调整App选择框宽度
  .app-item {
    .form-select,
    .n-select {
      width: 280px; // 继续增大App选择框长度
    }
  }
  
  // 调整Instance选择框宽度
  .instance-item {
    .form-select,
    .n-select {
      width: 120px; // 减小Instance选择框长度
    }
  }
  
  // 调整App、Host、Instance选择框宽度
  .form-item:not(.keywords-item):not(.buttons-item):not(.log-level-item):not(.app-item):not(.instance-item) {
    .form-select,
    .n-select {
      width: 240px; // 从220px增加到240px
    }
  }
  
  .keywords-item {
    margin-right: 0;
    /* 移除width: 100%，使其不独占整行 */
    flex: 0 0 auto; /* 不伸缩，保持内容宽度 */
    
    > span {
      width: 120px;
      text-align: right;
      align-self: flex-start;
      margin-right: 8px; /* 保持与其他表单元素一致的右边距 */
    }
    
    .keywords-wrapper {
      display: flex;
      flex-direction: column;
      /* 设置与输入框一致的宽度 */
      width: 400px;
      max-width: calc(100% - 128px - 16px - 100px); /* 为按钮留出空间 */
      
      .form-input,
      .n-input {
      }
      
      .keywords-tip {
        margin-top: 4px;
        font-size: 12px;
        color: #999;
      }
    }
    
    /* 添加按钮容器样式，使按钮与输入框在同一行 */
    .keywords-buttons {
      display: flex;
      flex-direction: row;
      white-space: nowrap;
      margin-left: 16px; /* 与其他表单元素间距保持一致 */
      align-self: flex-start;
      
      .n-button {
        margin: 0;
      }
      
      .n-button:last-child {
        margin-left: 12px;
      }
    }
  }
}

/* 修改容器为flex布局，使输入框和按钮在同一行 */
.keywords-item {
  display: flex;
  align-items: flex-start;
}


.keywords-wrapper {
  flex: 0 0 auto; /* 不伸缩，保持固定宽度 */
}

/* 添加新的按钮容器样式 */
.form-buttons {
  display: flex;
  padding: 0 0 20px 20px;
  margin-top: -16px;
  
  > div:last-child {
    .n-button {
      margin: 0;
    }
    
    .n-button:last-child {
      margin-left: 12px;
    }
  }
}

/* 添加按钮容器宽度，确保与关键字输入框对齐 */
.form-buttons > div:last-child {
  width: calc(400px * 3 + 16px * 2); /* 与关键字输入框宽度保持一致 */
  max-width: calc(100% - 128px);
  display: flex;
  flex-direction: row;
  white-space: nowrap;
  overflow: hidden;
}

// 响应式布局优化
@media (max-width: 768px) {
  .query-form {
    .form-item {
      width: 100%;
      margin-right: 0;
      
      > span {
        width: 100%;
        text-align: left;
        margin-bottom: 8px;
      }
    }
    
    .keywords-item .keywords-wrapper .keywords-input {
      margin-bottom: 8px;
    }
  }
}

.log-content-wrapper {
  height: 100%;
  overflow: hidden;
  position: relative;
  min-height: 0;

  .empty-container {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .log-info {
    padding: 8px 12px;
    border-bottom: 1px solid var(--n-border-color);
    font-size: 12px;
    color: #666;
    background-color: var(--n-color);
  }

  .log-scrollbar {
    height: 100%;
    overflow: hidden;
    
    .log-content {
      font-family: monospace;
      font-size: 14px;
      padding: 12px;
      white-space: pre-wrap;
      // 确保内容可以撑开滚动区域
      min-height: 100%;
      // 添加这个属性确保内容可以正确滚动
      overflow-y: auto;
    }
  }
}
</style>