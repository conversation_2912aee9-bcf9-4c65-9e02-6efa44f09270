// 测试程序Bin定义相关类型定义

// 已配置的测试程序
export interface ConfiguredTestProgramVo {
  testProgram: string;
  hbinCount: number;
  sbinCount: number;
  updateTime: string;
}

// 未配置的测试程序
export interface UnconfiguredTestProgramVo {
  testProgram: string;
  createTime: string;
}

// Bin定义详情
export interface BinDefinitionDetail {
  id?: number;
  testProgram: string;
  binType: 'HBIN' | 'SBIN';
  binNum: number;
  binName: string;
  binPf: 'P' | 'F';
}

// 搜索参数
export interface BinDefinitionQueryVo {
  testProgram?: string;
  pageIndex: number;
  pageSize: number;
  sortField?: string;
  sortDirection?: 'ASC' | 'DESC';
}

// 保存参数
export interface BinDefinitionSaveVo {
  testProgram: string;
  binDefinitions: BinDefinitionDetail[];
}

// 删除参数
export interface BinDefinitionDeleteVo {
  testProgram: string;
}

// 导入模式
export enum ImportMode {
  ADD_OR_OVERWRITE = 'ADD_OR_OVERWRITE',
  CLEAR_AND_IMPORT = 'CLEAR_AND_IMPORT'
}

// 导入结果
export interface BinDefinitionImportResultVo {
  success: boolean;
  message: string;
  successCount: number;
  failCount: number;
}
