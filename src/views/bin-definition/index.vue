<template>
  <PageTemplate>
    <template #header>
      <div>测试程序Bin定义管理</div>
    </template>
    <template #filterNormal>
      <SearchForm ref="searchListRef" :searchList="SEARCH_LIST()" @search="onSearch" />
    </template>
    <template #table>
      <div class="table-container">
        <!-- Tab页切换和操作按钮在同一行 -->
        <div class="flex justify-between items-center mb-4">
          <el-tabs v-model="activeTab" @tab-change="handleTabChange">
            <el-tab-pane label="已配置" name="configured" />
            <el-tab-pane label="未配置" name="unconfigured" />
          </el-tabs>
          
          <div class="flex gap-2">
            <el-button @click="onExport"> 导出 </el-button>
            <el-button @click="onImportClick"> 导入 </el-button>
            <el-button @click="onExportTemplate"> 导出模版 </el-button>
          </div>
        </div>

        <!-- 已配置表格 -->
        <CommonTable
          v-if="activeTab === 'configured'"
          title="已配置测试程序列表"
          ref="configuredTableRef"
          :row-key="'testProgram'"
          :data="configuredTableData"
          @sort-change="handleSort"
          v-model:pagination="pageInfo"
          border
          isColumnConfig
          :columns="CONFIGURED_COL_SETTING_LIST"
          :columnUpdate="configuredColumnUpdate"
        >
          <template #columnConfigBtn>
            <el-button class="w-[30px] ml-[8px]">
              <el-tooltip placement="bottom" content="配置表头">
                <div class="w-[30px] h-[30px] flex items-center justify-center">
                  <SvgIcon name="btn_setting" class="text-[16px]" />
                </div>
              </el-tooltip>
            </el-button>
          </template>
          <el-table-column
            v-for="item in configuredTableHeader"
            :key="item.key"
            :prop="item.key"
            :label="item.label"
            :min-width="item?.width ?? 200"
            :fixed="item.fixed"
            :sortable="item.sortable"
            show-overflow-tooltip
          />
          <el-table-column width="150" :align="'left'" fixed="right" label="操作">
            <template v-slot="scope">
              <el-button link @click="onView(scope.row)"> 查看 </el-button>
              <el-button link @click="onDelete(scope.row)"> 删除 </el-button>
            </template>
          </el-table-column>
        </CommonTable>

        <!-- 未配置表格 -->
        <CommonTable
          v-if="activeTab === 'unconfigured'"
          title="未配置测试程序列表"
          ref="unconfiguredTableRef"
          :row-key="'testProgram'"
          :data="unconfiguredTableData"
          @sort-change="handleSort"
          v-model:pagination="pageInfo"
          border
          isColumnConfig
          :columns="UNCONFIGURED_COL_SETTING_LIST"
          :columnUpdate="unconfiguredColumnUpdate"
        >
          <template #columnConfigBtn>
            <el-button class="w-[30px] ml-[8px]">
              <el-tooltip placement="bottom" content="配置表头">
                <div class="w-[30px] h-[30px] flex items-center justify-center">
                  <SvgIcon name="btn_setting" class="text-[16px]" />
                </div>
              </el-tooltip>
            </el-button>
          </template>
          <el-table-column
            v-for="item in unconfiguredTableHeader"
            :key="item.key"
            :prop="item.key"
            :label="item.label"
            :min-width="item?.width ?? 200"
            :fixed="item.fixed"
            :sortable="item.sortable"
            show-overflow-tooltip
          />
        </CommonTable>
      </div>
    </template>
  </PageTemplate>

  <!-- 编辑/查看对话框 -->
  <BinDefinitionDialog
    v-if="binDefinitionDialogVisible"
    v-model:visible="binDefinitionDialogVisible"
    :testProgram="selectedTestProgram"
    :readonly="isReadonly"
    @saveFinish="onSaveFinish"
  />

  <!-- 导入对话框 -->
  <ImportDialog
    v-if="importVisible"
    v-model:visible="importVisible"
    @importFinish="onImportFinish"
  />
</template>

<script setup lang="ts">
import {
  PageTemplate,
  SearchForm,
  CommonTable,
  PaginationType,
  SearchFormParam,
} from '@guwave/components';
import {
  SEARCH_LIST,
  CONFIGURED_COLUMN_LIST,
  UNCONFIGURED_COLUMN_LIST,
  CONFIGURED_COL_SETTING_LIST,
  UNCONFIGURED_COL_SETTING_LIST,
  BPMS_SUCCESSS_CODE,
} from './constants';
import BinDefinitionDialog from './components/BinDefinitionDialog.vue';
import ImportDialog from './components/ImportDialog.vue';
import {
  getConfiguredTestPrograms,
  getUnconfiguredTestPrograms,
  deleteTestProgramBinDefinitions,
  exportBinDefinitions,
  exportTemplate,
} from '@/service/bin-definition';
import {
  ConfiguredTestProgramVo,
  UnconfiguredTestProgramVo,
  BinDefinitionQueryVo,
} from './typing';
import { find, isNil } from 'lodash';
import usePermissionStore from '@/store/permission';
import { parseTime } from '@/utils/time';

const permissionStore = usePermissionStore();
const { userInfo } = storeToRefs(permissionStore);

// 当前激活的tab
const activeTab = ref<'configured' | 'unconfigured'>('configured');

// 表格数据
const configuredTableData = ref<ConfiguredTestProgramVo[]>([]);
const unconfiguredTableData = ref<UnconfiguredTestProgramVo[]>([]);

// 分页信息
const pageInfo = reactive<PaginationType>({
  pageIndex: 1,
  pageSize: 50,
  total: 0,
  currentChange: (current: number) => {
    pageInfo.pageIndex = current;
    getData();
  },
  sizeChange: (size: number) => {
    pageInfo.pageSize = size;
    getData();
  },
});

// 排序参数
const sortParams = reactive<{ sortField?: string; sortDirection?: 'ASC' | 'DESC' }>({});

// 搜索参数
const searchParams = ref<SearchFormParam>( {});

// 表格列配置
const configuredTableHeader = ref<any[]>([...CONFIGURED_COLUMN_LIST]);
const unconfiguredTableHeader = ref<any[]>([...UNCONFIGURED_COLUMN_LIST]);

// 对话框状态
const binDefinitionDialogVisible = ref<boolean>(false);
const importVisible = ref<boolean>(false);
const selectedTestProgram = ref<string>('');
const isReadonly = ref<boolean>(false);

// 处理tab切换
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName as 'configured' | 'unconfigured';
  pageInfo.pageIndex = 1;
  sortParams.sortField = undefined;
  sortParams.sortDirection = undefined;
  getData();
};

// 处理排序
const handleSort = ({ prop, order }) => {
  const currentColumns = activeTab.value === 'configured' ? CONFIGURED_COLUMN_LIST : UNCONFIGURED_COLUMN_LIST;
  const sortKey = find(currentColumns, { key: prop })?.sortKey ?? prop;
  if (isNil(order)) {
    sortParams.sortField = undefined;
    sortParams.sortDirection = undefined;
  } else {
    sortParams.sortField = sortKey;
    sortParams.sortDirection = order === 'ascending' ? 'ASC' : 'DESC';
  }
  getData();
};

// 列配置更新
const configuredColumnUpdate = (list: any[]) => {
  configuredTableHeader.value = CONFIGURED_COLUMN_LIST.filter((item) =>
    list.some((ite) => ite.name === item.key)
  );
};

const unconfiguredColumnUpdate = (list: any[]) => {
  unconfiguredTableHeader.value = UNCONFIGURED_COLUMN_LIST.filter((item) =>
    list.some((ite) => ite.name === item.key)
  );
};

// 搜索
const onSearch = (params: SearchFormParam) => {
  searchParams.value = params;
  pageInfo.pageIndex = 1;
  getData();
};

// 获取数据
const getData = async () => {
  const params: BinDefinitionQueryVo = {
    ...searchParams.value,
    ...sortParams,
    pageIndex: pageInfo.pageIndex,
    pageSize: pageInfo.pageSize,
  };

  try {
    if (activeTab.value === 'configured') {
      const res = await getConfiguredTestPrograms(params);
      const resData = res.data.value;
      if (resData.code === BPMS_SUCCESSS_CODE) {
        const { data, total, pageIndex, pageSize } = resData.data;
        configuredTableData.value = data;
        pageInfo.pageIndex = pageIndex;
        pageInfo.pageSize = pageSize;
        pageInfo.total = total;
      }
    } else {
      const res = await getUnconfiguredTestPrograms(params);
      const resData = res.data.value;
      if (resData.code === BPMS_SUCCESSS_CODE) {
        const { data, total, pageIndex, pageSize } = resData.data;
        unconfiguredTableData.value = data;
        pageInfo.pageIndex = pageIndex;
        pageInfo.pageSize = pageSize;
        pageInfo.total = total;
      }
    }
  } catch (e) {
    console.error(e);
  }
};

// 查看
const onView = (row: ConfiguredTestProgramVo) => {
  selectedTestProgram.value = row.testProgram;
  isReadonly.value = true;
  binDefinitionDialogVisible.value = true;
};

// 编辑
const onEdit = (row: ConfiguredTestProgramVo | UnconfiguredTestProgramVo) => {
  selectedTestProgram.value = row.testProgram;
  isReadonly.value = false;
  binDefinitionDialogVisible.value = true;
};

// 删除
const onDelete = (row: ConfiguredTestProgramVo) => {
  ElMessageBox.confirm('确认删除该测试程序的Bin定义？', '删除确认', {
    confirmButtonText: '确认',
  }).then(async () => {
    try {
      const res = await deleteTestProgramBinDefinitions({ testProgram: row.testProgram });
      const resData = res.data.value;
      if (resData.code === BPMS_SUCCESSS_CODE) {
        getData();
        ElMessage.success('删除成功');
      } else {
        ElMessage.error('删除失败');
      }
    } catch (e) {
      console.error(e);
      ElMessage.error('删除失败');
    }
  });
};

// 导出
const onExport = async () => {
  try {
    const params = {
      ...searchParams.value,
      ...sortParams,
      exportConfiguredFlag: activeTab.value === 'configured' ? 1 : 0,
    };

    const res = await exportBinDefinitions(params);

    // 检查响应类型，如果是JSON说明有错误
    if (res.data.value instanceof Blob) {
      const contentType = res.data.value.type;

      if (contentType.includes('application/json')) {
        // 如果返回的是JSON，说明有错误
        const text = await res.data.value.text();
        const errorData = JSON.parse(text);
        ElMessage.error(errorData.msg || '导出失败');
        return;
      }

      // 正常的文件下载
      const url = window.URL.createObjectURL(res.data.value);
      const el = document.createElement('a');
      el.href = url;
      el.download = `${userInfo.value.companyCode}_测试程序Bin定义_${activeTab.value}_${parseTime(new Date())}.csv`;
      document.body.appendChild(el);
      el.click();
      document.body.removeChild(el);
      window.URL.revokeObjectURL(url);
      ElMessage.success('导出文件成功');
    } else {
      ElMessage.error('导出文件失败，返回数据格式错误');
    }
  } catch (e) {
    console.error(e);
    ElMessage.error('导出文件失败，请重新导出');
  }
};

// 导入
const onImportClick = () => {
  importVisible.value = true;
};

// 导出模板
const onExportTemplate = async () => {
  try {
    const res = await exportTemplate();
    
    // 检查响应类型，如果是JSON说明有错误
    if (res.data.value instanceof Blob) {
      const contentType = res.data.value.type;
      
      if (contentType.includes('application/json')) {
        // 如果返回的是JSON，说明有错误
        const text = await res.data.value.text();
        const errorData = JSON.parse(text);
        ElMessage.error(errorData.msg || '导出模板失败');
        return;
      }
      
      // 正常的文件下载
      const url = window.URL.createObjectURL(res.data.value);
      const el = document.createElement('a');
      el.href = url;
      el.download = `测试程序Bin定义导入模板.csv`;
      document.body.appendChild(el);
      el.click();
      document.body.removeChild(el);
      window.URL.revokeObjectURL(url);
      ElMessage.success('导出模板成功');
    } else {
      ElMessage.error('导出模板失败，返回数据格式错误');
    }
  } catch (e) {
    console.error(e);
    ElMessage.error('导出模板失败');
  }
};

// 保存完成回调
const onSaveFinish = () => {
  getData();
};

// 导入完成回调
const onImportFinish = () => {
  getData();
};

onMounted(async () => {
  getData();
});
</script>

<style scoped lang="scss"></style>
