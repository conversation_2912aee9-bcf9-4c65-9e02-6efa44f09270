import { searchTestPrograms } from '@/service/bin-definition';
import { SearchFormTypeEnum, SearchFormItem, TableHeader as ColSettingItem } from '@guwave/components';

export const BPMS_SUCCESSS_CODE = '00000001';

export const SEARCH_LIST = (): SearchFormItem[] => {
  const list = [
    {
      type: SearchFormTypeEnum.SELECT,
      label: 'Test Program',
      key: 'testPrograms',
      api: searchTestPrograms,
      dropdownKey: 'testProgram',
      nativeAttr: {
        multiple: true,
      },
    },
  ];

  const res = list.map((item) => {
    return {
      ...item,
      api: (param) => {
        const newParams = {
          ...param,
          filterField: param.field,
          field: undefined,
        };
        return item.api(newParams);
      },
    };
  });
  return res;
};

// 已配置测试程序表格列
export const CONFIGURED_COLUMN_LIST = [
  {
    label: 'Test Program',
    key: 'testProgram',
    fixed: 'left',
    sortable: 'custom',
    width: 200,
  },
  {
    label: 'HBIN 数量',
    key: 'hbinCount',
    sortable: false,
    width: 120,
  },
  {
    label: 'SBIN 数量',
    key: 'sbinCount',
    sortable: false,
    width: 120,
  },
  {
    label: '更新时间',
    key: 'updateTime',
    sortable: 'custom',
    width: 180,
  },
];

// 未配置测试程序表格列
export const UNCONFIGURED_COLUMN_LIST = [
  {
    label: 'Test Program',
    key: 'testProgram',
    fixed: 'left',
    sortable: 'custom',
    width: 200,
  },
  {
    label: '创建时间',
    key: 'createTime',
    sortable: 'custom',
    width: 180,
  },
];

export const CONFIGURED_COL_SETTING_LIST: ColSettingItem[] = CONFIGURED_COLUMN_LIST.map((item) => ({
  name: item.key,
  showName: item.label,
}));

export const UNCONFIGURED_COL_SETTING_LIST: ColSettingItem[] = UNCONFIGURED_COLUMN_LIST.map((item) => ({
  name: item.key,
  showName: item.label,
}));

// Bin类型选项
export const BIN_TYPE_OPTIONS = [
  { label: 'HBIN', value: 'HBIN' },
  { label: 'SBIN', value: 'SBIN' },
];

// Bin PF选项
export const BIN_PF_OPTIONS = [
  { label: 'P', value: 'P' },
  { label: 'F', value: 'F' },
];

// 导入模式选项
export const IMPORT_MODE_OPTIONS = [
  { label: '新增或覆盖', value: 'ADD_OR_OVERWRITE' },
  { label: '全部清除并重新导入', value: 'CLEAR_AND_IMPORT' },
];

// Tab页选项
export const TAB_OPTIONS = [
  { label: '已配置', value: 'configured' },
  { label: '未配置', value: 'unconfigured' },
];
