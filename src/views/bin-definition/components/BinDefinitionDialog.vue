<template>
  <el-dialog
    v-model="dialogVisible"
    :title="readonly ? '查看Bin定义' : '编辑Bin定义'"
    width="40%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="bin-definition-dialog">
      <div class="test-program-info mb-4">
        <span class="font-bold">Test Program: </span>
        <span>{{ testProgram }}</span>
      </div>

      <!-- Tab页切换 -->
      <el-tabs v-model="activeTab" class="mb-4">
        <el-tab-pane label="HBIN" name="HBIN" />
        <el-tab-pane label="SBIN" name="SBIN" />
      </el-tabs>

      <!-- 编辑表格 -->
      <div class="table-container">
        <div class="table-header mb-2" v-if="!readonly">
          <el-button type="primary" @click="addRow" :disabled="readonly">
            新增
          </el-button>
        </div>

        <el-table
          :data="currentTableData"
          border
          style="width: 100%"
          max-height="400"
        >
          <el-table-column prop="binNum" label="Bin Num" width="160">
            <template #default="scope">
              <el-input-number
                v-if="!readonly"
                v-model="scope.row.binNum"
                :min="0"
                :precision="0"
                style="width: 140px"
                size="default"
                @change="validateRow(scope.row)"
              />
              <span v-else>{{ scope.row.binNum }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="binName" label="Bin Name" min-width="200">
            <template #default="scope">
              <el-input
                v-if="!readonly"
                v-model="scope.row.binName"
                @input="validateRow(scope.row)"
              />
              <span v-else>{{ scope.row.binName }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="binPf" label="Bin PF" width="120">
            <template #default="scope">
              <el-select
                v-if="!readonly"
                v-model="scope.row.binPf"
                style="width: 100%"
                @change="validateRow(scope.row)"
              >
                <el-option
                  v-for="option in BIN_PF_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
              <span v-else>{{ scope.row.binPf }}</span>
            </template>
          </el-table-column>

          <el-table-column
            v-if="!readonly"
            label="操作"
            width="80"
            fixed="right"
          >
            <template #default="scope">
              <el-button
                type="danger"
                link
                @click="removeRow(scope.$index)"
                :disabled="readonly"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-if="!readonly"
          type="primary"
          @click="handleSave"
          :loading="saving"
        >
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { BIN_PF_OPTIONS, BPMS_SUCCESSS_CODE } from '../constants';
import { BinDefinitionDetail, BinDefinitionSaveVo } from '../typing';
import { getBinDefinitions, saveBinDefinitions } from '@/service/bin-definition';

interface Props {
  visible: boolean;
  testProgram: string;
  readonly?: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'saveFinish'): void;
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
});

const emit = defineEmits<Emits>();

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

// 当前激活的tab
const activeTab = ref<'HBIN' | 'SBIN'>('HBIN');

// 原始数据
const originalData = ref<BinDefinitionDetail[]>([]);

// HBIN和SBIN数据
const hbinData = ref<BinDefinitionDetail[]>([]);
const sbinData = ref<BinDefinitionDetail[]>([]);

// 当前表格数据
const currentTableData = computed(() => {
  return activeTab.value === 'HBIN' ? hbinData.value : sbinData.value;
});

// 保存状态
const saving = ref(false);

// 获取数据
const loadData = async () => {
  if (!props.testProgram) return;

  try {
    const res = await getBinDefinitions(props.testProgram);
    const resData = res.data.value;
    
    if (resData && resData.code === BPMS_SUCCESSS_CODE) {
      originalData.value = resData.data || [];
      
      // 分离HBIN和SBIN数据
      hbinData.value = originalData.value
        .filter(item => item.binType === 'HBIN')
        .map(item => ({ ...item }));
      
      sbinData.value = originalData.value
        .filter(item => item.binType === 'SBIN')
        .map(item => ({ ...item }));
    }
  } catch (e) {
    console.error(e);
    ElMessage.error('获取Bin定义数据失败');
  }
};

// 新增行
const addRow = () => {
  const newRow: BinDefinitionDetail = {
    testProgram: props.testProgram,
    binType: activeTab.value,
    binNum: 0,
    binName: '',
    binPf: 'P',
  };

  if (activeTab.value === 'HBIN') {
    hbinData.value.push(newRow);
  } else {
    sbinData.value.push(newRow);
  }
};

// 删除行
const removeRow = (index: number) => {
  if (activeTab.value === 'HBIN') {
    hbinData.value.splice(index, 1);
  } else {
    sbinData.value.splice(index, 1);
  }
};

// 验证行数据
const validateRow = (_row: BinDefinitionDetail) => {
  // 这里可以添加实时验证逻辑
};

// 验证所有数据
const validateAllData = (): boolean => {
  const allData = [...hbinData.value, ...sbinData.value];
  
  for (const item of allData) {
    if (!item.binName || item.binName.trim() === '') {
      ElMessage.error('Bin Name不能为空');
      return false;
    }
    
    if (item.binNum === undefined || item.binNum === null) {
      ElMessage.error('Bin Num不能为空');
      return false;
    }
    
    if (!item.binPf) {
      ElMessage.error('Bin PF不能为空');
      return false;
    }
  }

  // 检查同一类型下Bin Num是否重复
  const hbinNums = hbinData.value.map(item => item.binNum);
  const sbinNums = sbinData.value.map(item => item.binNum);
  
  if (new Set(hbinNums).size !== hbinNums.length) {
    ElMessage.error('HBIN中存在重复的Bin Num');
    return false;
  }
  
  if (new Set(sbinNums).size !== sbinNums.length) {
    ElMessage.error('SBIN中存在重复的Bin Num');
    return false;
  }

  return true;
};

// 保存
const handleSave = async () => {
  if (!validateAllData()) {
    return;
  }

  saving.value = true;
  
  try {
    const saveData: BinDefinitionSaveVo = {
      testProgram: props.testProgram,
      binDefinitions: [...hbinData.value, ...sbinData.value],
    };

    const res = await saveBinDefinitions(saveData);
    const resData = res.data.value;
    
    if (resData && resData.code === BPMS_SUCCESSS_CODE) {
      ElMessage.success('保存成功');
      emit('saveFinish');
      handleClose();
    } else {
      ElMessage.error(resData?.msg || '保存失败');
    }
  } catch (e) {
    console.error(e);
    ElMessage.error('保存失败');
  } finally {
    saving.value = false;
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置数据
  activeTab.value = 'HBIN';
  hbinData.value = [];
  sbinData.value = [];
  originalData.value = [];
};

// 监听对话框显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      loadData();
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.bin-definition-dialog {
  .test-program-info {
    font-size: 16px;
    padding: 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .table-container {
    .table-header {
      display: flex;
      justify-content: flex-start;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
