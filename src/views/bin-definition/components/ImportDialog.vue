<template>
  <el-dialog
    v-model="dialogVisible"
    title="导入Bin定义"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="import-dialog">
      <!-- 导入模式选择 -->
      <div class="import-mode mb-4">
        <div class="mb-2">
          <span class="font-bold">导入模式：</span>
        </div>
        <el-radio-group v-model="importMode">
          <el-radio
            v-for="option in IMPORT_MODE_OPTIONS"
            :key="option.value"
            :label="option.value"
          >
            {{ option.label }}
          </el-radio>
        </el-radio-group>
      </div>

      <!-- 文件选择 -->
      <div class="file-upload mb-4">
        <div class="mb-2">
          <span class="font-bold">选择文件：</span>
        </div>
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :show-file-list="true"
          :limit="1"
          accept=".csv"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :before-upload="beforeUpload"
        >
          <el-button type="primary">选择CSV文件</el-button>
          <template #tip>
            <div class="el-upload__tip">
              只能上传CSV文件，且不超过10MB
            </div>
          </template>
        </el-upload>
      </div>

      <!-- 上传进度 -->
      <div v-if="uploading" class="upload-progress mb-4">
        <el-progress :percentage="uploadProgress" :status="uploadStatus" />
        <div class="mt-2 text-sm text-gray-600">
          {{ uploadMessage }}
        </div>
      </div>

      <!-- 导入结果 -->
      <div v-if="importResult" class="import-result">
        <el-alert
          :type="importResult.success ? 'success' : 'error'"
          :description="importResult.message"
          show-icon
          :closable="false"
        />
        <div v-if="importResult.success" class="mt-2 text-sm">
          <div>成功导入：{{ importResult.successCount }} 条</div>
          <div v-if="importResult.failCount > 0">
            失败：{{ importResult.failCount }} 条
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleImport"
          :loading="uploading"
          :disabled="!selectedFile"
        >
          导入
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { IMPORT_MODE_OPTIONS, BPMS_SUCCESSS_CODE } from '../constants';
import { ImportMode, BinDefinitionImportResultVo } from '../typing';
import { importBinDefinitions } from '@/service/bin-definition';
import type { UploadInstance, UploadRawFile } from 'element-plus';

interface Props {
  visible: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'importFinish'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

// 导入模式
const importMode = ref<ImportMode>(ImportMode.ADD_OR_OVERWRITE);

// 文件相关
const uploadRef = ref<UploadInstance>();
const selectedFile = ref<File | null>(null);

// 上传状态
const uploading = ref(false);
const uploadProgress = ref(0);
const uploadStatus = ref<'success' | 'exception' | undefined>(undefined);
const uploadMessage = ref('');

// 导入结果
const importResult = ref<BinDefinitionImportResultVo | null>(null);

// 文件变化处理
const handleFileChange = (file: any) => {
  selectedFile.value = file.raw;
  importResult.value = null;
};

// 文件移除处理
const handleFileRemove = () => {
  selectedFile.value = null;
  importResult.value = null;
};

// 上传前验证
const beforeUpload = (file: UploadRawFile) => {
  const isCSV = file.type === 'text/csv' || file.name.endsWith('.csv');
  if (!isCSV) {
    ElMessage.error('只能上传CSV格式的文件!');
    return false;
  }

  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!');
    return false;
  }

  return true;
};

// 执行导入
const handleImport = async () => {
  if (!selectedFile.value) {
    ElMessage.error('请选择要导入的文件');
    return;
  }

  uploading.value = true;
  uploadProgress.value = 0;
  uploadStatus.value = undefined;
  uploadMessage.value = '正在上传文件...';
  importResult.value = null;

  // 模拟进度条
  const progressTimer = setInterval(() => {
    if (uploadProgress.value < 90) {
      uploadProgress.value += 10;
    }
  }, 200);

  try {
    const res = await importBinDefinitions(selectedFile.value, importMode.value);
    const resData = res.data.value;

    clearInterval(progressTimer);
    uploadProgress.value = 100;

    if (resData && resData.code === BPMS_SUCCESSS_CODE) {
      uploadStatus.value = 'success';
      uploadMessage.value = '导入完成';
      importResult.value = {
        success: true,
        message: resData.data.message || '导入成功',
        successCount: resData.data.successCount || 0,
        failCount: resData.data.failedCount || 0,
      };
      
      // 延迟一下再触发完成事件，让用户看到结果
      setTimeout(() => {
        emit('importFinish');
      }, 1500);
    } else {
      uploadStatus.value = 'exception';
      uploadMessage.value = '导入失败';
      importResult.value = {
        success: false,
        message: resData?.msg || '导入失败',
        successCount: 0,
        failCount: 0,
      };
    }
  } catch (error) {
    clearInterval(progressTimer);
    uploadProgress.value = 100;
    uploadStatus.value = 'exception';
    uploadMessage.value = '导入失败';
    
    importResult.value = {
      success: false,
      message: '网络错误或服务器异常',
      successCount: 0,
      failCount: 0,
    };
    
    console.error('导入失败:', error);
  } finally {
    uploading.value = false;
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  
  // 重置状态
  importMode.value = ImportMode.ADD_OR_OVERWRITE;
  selectedFile.value = null;
  uploading.value = false;
  uploadProgress.value = 0;
  uploadStatus.value = undefined;
  uploadMessage.value = '';
  importResult.value = null;
  
  // 清空文件列表
  uploadRef.value?.clearFiles();
};
</script>

<style scoped lang="scss">
.import-dialog {
  .import-mode {
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .file-upload {
    :deep(.el-upload__tip) {
      margin-top: 8px;
      color: #909399;
      font-size: 12px;
    }
  }

  .upload-progress {
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .import-result {
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
