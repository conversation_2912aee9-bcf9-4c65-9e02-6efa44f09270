<template>
  <PageTemplate>
    <template #header>
      <div>自动入库规则管理</div>
    </template>
    <template #filterNormal>
      <SearchForm ref="searchListRef" :searchList="SEARCH_LIST()" @search="onSearch" />
    </template>
    <template #table>
      <CommonTable
        title="接收文件列表"
        ref="tableRef"
        :row-key="'id'"
        :data="tableData"
        @sort-change="handleSort"
        v-model:pagination="pageInfo"
        border
        isColumnConfig
        :columns="COL_SETTING_LIST"
        :columnUpdate="columnUpdate"
      >
        <template #columnConfigBtn>
          <el-button class="w-[30px] ml-[8px]">
            <el-tooltip placement="bottom" content="配置表头">
              <div class="w-[30px] h-[30px] flex items-center justify-center">
                <SvgIcon name="btn_setting" class="text-[16px]" />
              </div>
            </el-tooltip>
          </el-button>
        </template>
        <template #headerRightStart>
          <el-button @click="onDownload"> 批量导出规则 </el-button>
          <el-button @click="onImportClick"> 批量导入规则 </el-button>
          <el-button type="primary" @click="onCreate"> 新建规则流程 </el-button>
        </template>
        <el-table-column
          v-for="item in tableHeader"
          :key="item.key"
          :prop="item.key"
          :label="item.label"
          :min-width="item?.width ?? 200"
          :fixed="item.fixed"
          :sortable="item.sortable"
          show-overflow-tooltip
        >
          <!-- <template #default="scope"> </template> -->
        </el-table-column>
        <el-table-column width="150" :align="'left'" fixed="right" label="操作">
          <template v-slot="scope">
            <el-button link @click="onRuleEdit(scope.row)"> 编辑 </el-button>
            <el-button link @click="onRuleCopy(scope.row)"> 复制 </el-button>
            <el-button link @click="onRuleDelete(scope.row)"> 删除 </el-button>
          </template>
        </el-table-column>
      </CommonTable>
    </template>
  </PageTemplate>
  <RuleProcessDialog
    v-if="ruleProcessDialogVisible"
    v-model:visible="ruleProcessDialogVisible"
    :ruleData="ruleData"
    @saveFinish="onSaveFinish"
  />
  <FileUploadDialog
    v-if="importVisible"
    :openDialog="importVisible"
    drag
    :handleParseFileRulesFn="onFileSelect"
    :fileUploadFn="onUpload"
    :accept="['.xlsx', '.zip', 'py']"
    :isShowTemplateDownload="false"
    @confirm="onUploadDialogOk"
    @close="onUploadDialogClose"
    :isUploadCanCancel="false"
  />
</template>

<script setup lang="ts">
import {
  PageTemplate,
  SearchForm,
  CommonTable,
  PaginationType,
  SearchFormParam,
  FileUploadDialog,
} from '@guwave/components';
import { SEARCH_LIST, COLUMN_LIST, COL_SETTING_LIST, BPMS_SUCCESSS_CODE } from './constants';
import RuleProcessDialog from './components/RuleProcessDialog.vue';
import {
  checkAutoImportRuleExist,
  copyAutoImportRules,
  deleteAutoImportRule,
  exportRule,
  getAutoImportRules,
  getConstantMap,
  getExportRuleMessage,
  importRule,
} from '@/service/conf-mgt';
import { AutoImportRuleItem } from './typing';
import { useConfStore } from '@/store/conf-mgt';
import { dayjs } from 'element-plus';
import { find, isNil } from 'lodash';

import usePermissionStore from '@/store/permission';
import { parseTime } from '@/utils/time';

const confStore = useConfStore();
const { constantOptionMap } = storeToRefs(confStore);
const permissionStore = usePermissionStore();
const { userInfo } = storeToRefs(permissionStore);

const tableData = ref<AutoImportRuleItem[]>([]);

const pageInfo = reactive<PaginationType>({
  pageIndex: 1,
  pageSize: 50,
  total: 0,
  // 页码改变回调
  currentChange: (current: number) => {
    pageInfo.pageIndex = current;
    getData();
  },
  // 每页条数改变回调
  sizeChange: (size: number) => {
    pageInfo.pageSize = size;
    getData();
  },
});
const sortParams = reactive<{ sortField?: string; sortDirection?: 'ASC' | 'DESC' }>({});
// 点击表格排序的响应
const handleSort = ({ prop, order }) => {
  const sortKey = find(COLUMN_LIST, { key: prop })?.sortKey ?? prop;
  if (isNil(order)) {
    sortParams.sortField = undefined;
    sortParams.sortDirection = undefined;
  } else {
    sortParams.sortField = sortKey;
    sortParams.sortDirection = order === 'ascending' ? 'ASC' : 'DESC';
  }
  getData();
};

//  列配置更新
const tableHeader = ref<any[]>([...COLUMN_LIST]);
const columnUpdate = (list: any[]) => {
  tableHeader.value = COLUMN_LIST.filter((item) => list.some((ite) => ite.name === item.key));
};

const searchParams = ref<SearchFormParam>({});
const onSearch = (params: SearchFormParam) => {
  searchParams.value = {
    ...params,
    createTimeStart: params?.createTime ? dayjs(params.createTime?.[0]).format('YYYY-MM-DD 00:00:00') : undefined,
    createTimeEnd: params?.createTime ? dayjs(params.createTime?.[1]).format('YYYY-MM-DD 23:59:59') : undefined,
    updateTimeStart: params?.updateTime ? dayjs(params.updateTime?.[0]).format('YYYY-MM-DD 00:00:00') : undefined,
    updateTimeEnd: params?.updateTime ? dayjs(params.updateTime?.[1]).format('YYYY-MM-DD 23:59:59') : undefined,
    createTime: undefined,
    updateTime: undefined,
  };

  getData();
};

const getData = async () => {
  const params = {
    ...searchParams.value,
    ...sortParams,
    pageIndex: pageInfo.pageIndex,
    pageSize: pageInfo.pageSize,
  };
  try {
    const res = await getAutoImportRules(params);
    const resData = res.data.value;

    if (resData.code === BPMS_SUCCESSS_CODE) {
      const { data, total, pageIndex, pageSize } = resData.data;
      // const { data, total, pageIndex, pageSize } = fackData();
      tableData.value = data;
      pageInfo.pageIndex = pageIndex;
      pageInfo.pageSize = pageSize;
      pageInfo.total = total;
    }
  } catch (e) {
    console.error(e);
  }
};

// 保存完成之后刷新表格
const onSaveFinish = () => {
  getData();
};

// 导入
const importVisible = ref<boolean>(false);
let isRuleExist = false; // 是否有现存的规则
let isFileUpload = false; // 判断要不要关闭弹窗的时候重新请求数据
const onImportClick = async () => {
  importVisible.value = true;
  isRuleExist = false;
  isFileUpload = false;

  // 检查是否有现有规则
  try {
    const res = await checkAutoImportRuleExist();
    const resData = res.data.value;
    if (resData.code === BPMS_SUCCESSS_CODE) {
      // 有规则，需要二次确认
      if (resData.data) {
        isRuleExist = true;
      } else {
        isRuleExist = false;
      }
    } else {
      ElMessage.error(`检查系统中是否已有规则失败,不支持继续上传，请重新操作！`);
      importVisible.value = false;
    }
  } catch (e) {
    ElMessage.error(`检查系统中是否已有规则失败,不支持继续上传，请重新操作！`);
    importVisible.value = false;
  }
};

// 文件的前端校验
const onFileSelect = async (file) => {
  const bits = 1 * 1024 * 1024 * 1024;
  const { size } = file;
  if (size > bits) {
    ElMessage.warning(`上传文件不允许超过1GB`);
    return;
  }
  // 校验
  if (isRuleExist) {
    let flag = false;
    await ElMessageBox.confirm('当前系统中已存在自动入库规则配置，是否确认覆盖当前所有配置？', '上传覆盖确认', {
      confirmButtonText: '确认',
    }).then(() => {
      flag = true;
    });
    return { res: flag };
  } else {
    return { res: true }; // 没有重复的 直接解析成功
  }
};

// 校验成功后的上传
const onUpload = async (file, uploadStatus) => {
  // uploadStatus 会更新进度条
  uploadStatus.value[file.uid] = {
    progress: 0,
    result: undefined,
    message: undefined,
  };
  // 假进度条
  const timer = setInterval(() => {
    if ((uploadStatus.value[file.uid]?.progress ?? 100) < 90) {
      uploadStatus.value[file.uid].progress += 9;
    }
  }, 300);
  // 创建 CancelToken
  // const CancelToken = axios.CancelToken;
  // const cancelTokenSource = CancelToken.source();
  // cancelTokenSources.value[file.uid] = cancelTokenSource;

  try {
    const formData = new FormData();
    formData.append('file', file.raw);

    const res = await importRule(formData);
    const resData = res.data.value;

    if (resData.code === BPMS_SUCCESSS_CODE) {
      uploadStatus.value[file.uid].result = true;
    } else {
      uploadStatus.value[file.uid].result = false;
    }
    uploadStatus.value[file.uid].progress = 100;
    uploadStatus.value[file.uid].message = resData?.msg ?? '';
    isFileUpload = true;
  } catch (error) {
    // if (axios.isCancel(error)) {
    //   console.error('上传被取消:', error?.message);
    // }
    console.error('上传失败:', error);
    uploadStatus.value[file.uid].result = false;
    uploadStatus.value[file.uid].progress = 100;
    uploadStatus.value[file.uid].message = error;
  } finally {
    clearInterval(timer); // 删除定时器
  }
  clearInterval(timer); // 删除定时器
};

const onUploadDialogClose = () => {
  importVisible.value = false;
  if (isFileUpload) {
    getData();
  }
};
const onUploadDialogOk = () => {
  importVisible.value = false;
  if (isFileUpload) {
    getData();
  }
};

// 导出
const onDownload = async () => {
  try {
    const res = await getExportRuleMessage();
    const resData = res.data.value;
    if (resData.code === BPMS_SUCCESSS_CODE) {
      if (resData.data !== '') {
        ElMessage.error(resData.data);
      }
    } else {
      ElMessage.error('检查HDFS上Python脚本文件的接口请求失败');
    }
  } catch (e) {
    ElMessage.error('检查HDFS上Python脚本文件的接口请求失败');
  }

  try {
    const exportRes = await exportRule();
    const exportResData = exportRes.data.value;

    const url = window.URL.createObjectURL(exportResData);
    const el = document.createElement('a');
    el.href = url;
    el.download = `${userInfo.value.companyCode}_自动入库规则配置_${parseTime(new Date())}.zip`;
    document.body.appendChild(el);
    el.click();
    document.body.removeChild(el);
    window.URL.revokeObjectURL(url);
    ElMessage.success('导出文件成功');
  } catch (e) {
    ElMessage.error('导出文件失败，请重新导出');
  }
};

const ruleProcessDialogVisible = ref<boolean>(false);
const ruleData = ref<AutoImportRuleItem>();
// 新建
const onCreate = () => {
  ruleProcessDialogVisible.value = true;
  ruleData.value = undefined;
};

// 编辑
const onRuleEdit = (row: AutoImportRuleItem) => {
  ruleProcessDialogVisible.value = true;
  ruleData.value = row;
};

// 复制
const onRuleCopy = async (row: AutoImportRuleItem) => {
  try {
    const res = await copyAutoImportRules(row.id);
    const resData = res.data.value;
    if (resData.code === BPMS_SUCCESSS_CODE) {
      getData(); //刷新表格
      ElMessage.success('复制成功');
    } else {
      ElMessage.error('复制失败');
    }
  } catch (e) {
    console.error(e);
    ElMessage.error('复制失败');
  }
};

// 删除
const onRuleDelete = (row: AutoImportRuleItem) => {
  ElMessageBox.confirm('仅删除当前流程，不影响后台规则，请确认！', '删除确认', {
    confirmButtonText: '确认',
  }).then(async () => {
    try {
      const res = await deleteAutoImportRule(row.id);
      const resData = res.data.value;
      if (resData.code === BPMS_SUCCESSS_CODE) {
        getData();
        ElMessage.success('删除成功');
      } else {
        ElMessage.error('删除失败');
      }
    } catch (e) {
      console.error(e);
      ElMessage.error('删除失败');
    }
  });
};

// 获取下拉框常量
const getSelectOptionConstantMap = async () => {
  try {
    const res = await getConstantMap();
    const resData = res.data.value;
    if (resData.code === BPMS_SUCCESSS_CODE) {
      constantOptionMap.value = resData.data;
    }
  } catch (e) {
    console.error(e);
  }
};

onMounted(async () => {
  getData();
  getSelectOptionConstantMap();
});
</script>
<style scoped lang="scss"></style>
