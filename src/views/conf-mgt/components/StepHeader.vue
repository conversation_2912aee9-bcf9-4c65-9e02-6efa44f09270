<template>
  <div class="step-header-container">
    <div class="flex items-center gap-[8px]">
      <SvgIcon v-if="step === 1" name="step1_icon" class="text-[24px]" />
      <SvgIcon v-else name="step_passed_icon" class="text-[24px]" />
      填写基础信息
    </div>
    <div class="line"></div>
    <div class="flex items-center gap-[8px]">
      <SvgIcon class="text-[24px]" name="step2_icon" :class="{ active: step === 2, 'not-active': step !== 2 }" />
      配置规则逻辑
    </div>
  </div>
</template>

<script setup lang="ts">
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const props = defineProps<{
  step: number;
}>();
</script>
<style scoped lang="scss">
.step-header-container {
  display: flex;
  justify-content: center;
  align-items: center;
  color: $gTextColorRegular;

  .line {
    height: 1px;
    width: 76px;
    background-color: $gBorderColorBase;
    margin: 0 12px;
  }
  .active {
    --bgColor: #866be5; // 不能用主题变量名
    --numberColor: white;
  }
  .not-active {
    --bgColor: #f8f8f8;
    --numberColor: #a6a6a6;
  }
}
</style>
