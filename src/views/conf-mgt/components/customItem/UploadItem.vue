<template>
  <div class="upload-item-container">
    <el-upload
      v-if="editId === rowData.id"
      ref="uploadRef"
      action=""
      accept=".zip"
      :auto-upload="false"
      :show-file-list="false"
      :limit="1"
      :on-change="onFileSelect"
    >
      <el-button size="small" class="mr-[4px]"> 上传文件 </el-button>
    </el-upload>
    <!-- 没有文件就不展示 -->
    <div v-if="rowData?.fileName" class="file-box group">
      <div class="file-name">
        <div style="width: 12px; height: 12px">
          <SvgIcon class="!text-[12px] text-gTextColorPlaceholder" name="document_norm" />
        </div>

        <div class="truncate text-gTextColorRegular">
          {{ rowData[colItem.key] }}
        </div>
      </div>
      <div class="text-[12px] text-gTextColorRegular">
        <div class="items-center hidden group-hover:flex">
          <!-- <SvgIcon name="delete_icon" class="cursor-pointer" @click="onDelete" /> -->
          <SvgIcon name="download_norm" class="cursor-pointer" @click="onDownload" />
        </div>
        <SvgIcon
          v-if="uploadStatus"
          class="text-gSuccessColorBase group-hover:hidden text-[12px]"
          name="ok_circle_norm"
        />
        <SvgIcon v-else class="text-gDangerColorBase group-hover:hidden text-[12px]" name="warning_circle_norm" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getConvertScriptFileOptions, downloadScriptFile } from '@/service/conf-mgt';
import { BPMS_SUCCESSS_CODE } from '../../constants';

const props = defineProps<{
  colItem: any;
  rowData: any;
  editId: string | undefined;
}>();

const emits = defineEmits(['change']);

const uploadStatus = ref<boolean>(true); // 上传有没有问题
const uploadRef = ref(null);
// 选择了文件
const onFileSelect = async (file) => {
  const bits = 100 * 1024 * 1024;
  const { name, size } = file;
  if (size > bits) {
    ElMessage.warning(`上传文件不允许超过100M`);
    uploadRef.value.clearFiles(); // 清空否则再次上传不触发函数
    return;
  }
  // 校验
  const params = new FormData();
  params.append(`file`, file?.raw ?? null);
  uploadStatus.value = true;
  let options = [];
  try {
    const res = await getConvertScriptFileOptions(params);
    const resData = res.data.value;
    if (resData.code === BPMS_SUCCESSS_CODE) {
      // options = resData.data.map((item) => ({ value: item, label: item }));
      options = resData.data;
    } else {
      ElMessage.error(`文件解析失败,请重新选择文件上传`);
      uploadStatus.value = false;
    }
  } catch (error) {
    ElMessage.error(`文件解析失败,请重新选择文件上传`);
    uploadStatus.value = false;
  }
  // 传给EditTable 更新rowData
  emits('change', props.rowData.id, [
    { colId: props.colItem.key, value: name }, // 文件名
    { colId: 'file', value: file },
    { colId: 'executeFileName', value: undefined }, // 当前选中项清空
    { colId: 'scriptList', value: options }, // 下拉框更新
  ]);
  uploadRef.value.clearFiles(); // 清空否则再次上传不触发函数
};

const onDownload = async () => {
  try {
    let data;
    // 新建或者之前的 都有id的
    if (props.rowData?.file) {
      data = props.rowData.file.raw;
    } else {
      const exportRes = await downloadScriptFile(props.rowData.id);
      data = exportRes.data.value;
    }
    const url = window.URL.createObjectURL(data);
    const el = document.createElement('a');
    el.href = url;
    el.download = `${props.rowData.fileName}`;
    document.body.appendChild(el);
    el.click();
    document.body.removeChild(el);
    window.URL.revokeObjectURL(url);
    ElMessage.success('下载成功');
  } catch (e) {
    ElMessage.error('下载失败，请重新下载');
  }
};
</script>
<style scoped lang="scss">
.upload-item-container {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .file-box {
    flex: 1;
    justify-content: space-between;
    display: flex;
    align-items: center;
    border-radius: 2px;

    // &:hover {
    //   background-color: $gColorPrimaryLight2;
    // }
    .file-name {
      width: 0px;
      flex: 1;

      display: flex;
      align-items: center;
      gap: 4px;
    }
    .icon-box {
      .icon-group {
        align-items: center;
        gap: 4px;
      }
    }
  }
}
</style>
