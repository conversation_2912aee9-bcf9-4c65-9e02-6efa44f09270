<template>
  <template v-if="editId !== rowData.id">
    <div>{{ rowData[colItem.key] }}</div>
  </template>
  <template v-else>
    <el-input ref="inputRef" :value="rowData[colItem.key]" placeholder="请输入" @focus="onFocus"> </el-input>
  </template>
</template>

<script setup lang="ts">
import { useConfStore } from '@/store/conf-mgt';

const props = defineProps<{
  colItem: any;
  rowData: any;
  editId: string | undefined;
}>();

const inputRef = ref(null);

const confStore = useConfStore();
const { editRuleInfo } = storeToRefs(confStore);

// 打开弹窗，填入初始内容
const onFocus = () => {
  inputRef.value.blur();
  editRuleInfo.value = {
    visible: true,
    content: props.rowData[props.colItem.key],
    tableType: props.colItem.tableType,
    rowData: { ...props.rowData },
    colItem: { ...props.colItem },
  };
};
</script>
<style scoped lang="scss"></style>
