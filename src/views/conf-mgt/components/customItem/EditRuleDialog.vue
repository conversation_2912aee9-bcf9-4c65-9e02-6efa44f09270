<template>
  <div class="edit-rule-dialog-container">
    <!-- 编辑rule弹窗 -->
    <el-dialog v-model="editRuleInfo.visible" title="编辑Rule" width="580" :before-close="handleClose">
      <el-input ref="inputRef" v-model="textarea" :rows="17" type="textarea" placeholder="请输入" />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleOk"> 确定 </el-button>
          <el-button @click="handleClose">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useConfStore } from '@/store/conf-mgt';

const emits = defineEmits(['confirm']);

const confStore = useConfStore();
const { editRuleInfo } = storeToRefs(confStore);

const inputRef = ref(null);
const textarea = ref<string>('');

const handleOk = () => {
  // 更新表格数据
  const contentVal = textarea.value?.trim();
  editRuleInfo.value = { ...editRuleInfo.value, visible: false, content: contentVal };
  emits('confirm');
};
const handleClose = () => {
  editRuleInfo.value = { ...editRuleInfo.value, visible: false };
};

watch(
  () => editRuleInfo,
  () => {
    if (editRuleInfo.value?.visible) {
      nextTick(() => {
        inputRef.value.focus();
      });
    }
  },
  { deep: true, immediate: true },
);

// 弹窗打开的时候赋初值
onMounted(() => {
  textarea.value = editRuleInfo.value.content;
});
</script>
<style scoped lang="scss">
.edit-rule-dialog-container {
}
</style>
