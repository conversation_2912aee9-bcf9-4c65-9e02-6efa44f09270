<template>
  <div class="edit-table-container">
    <CommonTable ref="tableRef" :row-key="'id'" :data="tableData" border style="padding-bottom: 0; height: 150px">
      <el-table-column
        v-for="item in columnList"
        :key="item.key"
        :prop="item.key"
        :label="item.label"
        show-overflow-tooltip
      >
        <template #header>
          <div v-if="item.require" class="flex items-center gap-[2px]">
            <SvgIcon
              v-if="item.type !== TableItemType.Text"
              class="text-[12px] text-gTextColorSecondary"
              name="editable_icon"
            />
            {{ item.label }}
            <el-tooltip v-if="item.desc" effect="light" placement="top" :content="item.desc">
              <SvgIcon class="text-[16px]" name="explain_icon" />
            </el-tooltip>
            <SvgIcon class="text-[12px]" name="require_icon" />
          </div>
        </template>
        <template #default="scope">
          <!-- 自定义列 -->
          <template v-if="item.type === TableItemType.Customer">
            <component
              :is="item.customComponent"
              :colItem="item"
              :rowData="scope.row"
              :editId="editId"
              @change="onItemChange"
            />
          </template>
          <!-- text类型 -->
          <template v-else-if="item.type === TableItemType.Text">
            <div>{{ scope.row?.[item.key] }}</div>
          </template>
          <!-- 其他类型列 -->
          <template v-else>
            <!-- 展示态 -->
            <template v-if="editId !== scope.row.id">
              <div>{{ scope.row?.[item.key] }}</div>
            </template>
            <!-- 编辑态 -->
            <template v-else>
              <el-input v-if="item.type === TableItemType.Input" v-model="scope.row[item.key]" placeholder="请输入" />
              <el-select
                v-else-if="item.type === TableItemType.Select"
                v-model="scope.row[item.key]"
                placeholder="请选择"
              >
                <template v-if="item?.options">
                  <el-option v-for="ite in item.options" :key="ite.value" :label="ite.label" :value="ite.value" />
                </template>
                <template v-else-if="item?.optionsKey">
                  <el-option v-for="ite in scope.row[item.optionsKey]" :key="ite" :label="ite" :value="ite" />
                </template>
              </el-select>
            </template>
          </template>
        </template>
      </el-table-column>
      <el-table-column width="170" :align="'left'" fixed="right" label="操作">
        <template v-slot="scope">
          <el-button link v-if="editId !== scope.row.id" @click="onEdit(scope.row.id)"> 编辑 </el-button>
          <el-button link v-else @click="onItemSave(scope.row.id)"> 保存 </el-button>
          <el-button link @click="onItemReset(scope.row.id)"> 重置 </el-button>
          <el-button link @click="onItemDelete(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column>
      <template #empty>
        <div class="h-[100%] p-[12px] flex flex-col items-center justify-center gap-[8px]">
          <SvgIcon class="!w-[64px] !h-[40px]" name="table_empty_icon" />
          <div class="leading-[16px] text-gTextColorPlaceholder">当前暂无数据，点击按钮新增数据</div>
          <div v-if="!isHideEmptyAddBtn" class="flex cursor-pointer" @click="onAddItem">
            <SvgIcon class="text-[14px] items-center text-gColorPrimary gap-[4px]" name="add_circle_icon" />
            <el-button link>新增数据</el-button>
          </div>
        </div>
      </template>
    </CommonTable>
    <div class="add-btn-row" v-if="tableData && tableData.length > 0 && !isHideAddBtn">
      <div class="btn-box" @click="onAddItem">
        <SvgIcon class="text-[12px] cursor-pointer" name="add_circle_icon" />
        <el-button link>新增</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import uuid from '@/utils/uuid';
import { CommonTable } from '@guwave/components';
import { find, isNil, isString } from 'lodash';
import { TableItemType } from './constants';

const props = defineProps<{
  initData: any[];
  columnList: any[];
  maxRowCount?: number;
  isHideEmptyAddBtn?: boolean;
  isHideAddBtn?: boolean;
  saveItemValidFn?: (rowData: any[]) => Promise<boolean>;
}>();

const tableRef = ref(null);

const tableData = defineModel<any[]>('data');
// const isEditing = defineModel<boolean>('isEditing');

const emits = defineEmits(['resetItem', 'deleteItem']);

const editId = ref<string | undefined>(undefined);

const isEditing = computed(() => {
  return !isNil(editId.value);
});

const requireColumnKeys = computed(() => {
  return props.columnList.filter((item) => item.require).map((item) => item.key);
});

// 校验必填项
const vaildRowNotEmpty = (rowData) => {
  const vaild = requireColumnKeys.value.every((key) => {
    const value = rowData[key];
    return !isNil(value) && value !== ''; // 之前已经去除首尾多余空格了
  });
  return vaild;
};

const onEdit = (id: string) => {
  // 有正在编辑的行
  if (!isNil(editId.value)) {
    const rowData = find(tableData.value, { id: editId.value });

    const vaild = vaildRowNotEmpty(rowData);
    if (vaild) {
      editId.value = id; // 切换编辑行
    } else {
      ElMessage.warning('当前编辑行有必填字段未填写');
    }
  } else {
    editId.value = id;
  }
};

const onItemSave = async (id: string) => {
  if (editId.value === id) {
    const rowData = find(tableData.value, { id: id });
    // 去除Input框里的首尾空格
    props.columnList.forEach((item) => {
      if (item.type === TableItemType.Input) {
        const value = isString(rowData[item.key]) ? rowData[item.key].trim() : rowData[item.key];
        rowData[item.key] = value === '' ? undefined : value;
      }
    });

    // 必填字段非空检验
    const vaild = vaildRowNotEmpty(rowData);

    if (vaild) {
      // 自定义保存校验
      if (props?.saveItemValidFn) {
        const flag = await props.saveItemValidFn(rowData); // 错误提示由外部处理
        if (flag) {
          editId.value = undefined;
        }
      } else {
        editId.value = undefined;
      }
    } else {
      ElMessage.warning('当前编辑行有必填字段未填写');
    }
  }
};

// 重置
const onItemReset = (id: string) => {
  const row = find(tableData.value, { id: id });
  if (row) {
    const initRow = find(props.initData, { id: id });
    // 展示的列恢复成初始值
    props.columnList.forEach((col) => {
      if (col.type === TableItemType.Text) return; // Text 类型不重置为空，因为本来就不支持编辑
      row[col.key] = initRow?.[col.key] ?? undefined;
    });
    emits('resetItem', row, initRow);
  }
};

const onItemDelete = (id: string) => {
  const deletedItem = tableData.value.find((item) => item.id === id);
  if (deletedItem) {
    tableData.value = tableData.value.filter((item) => item.id !== id);
    if (id === editId.value) {
      editId.value = undefined;
    }
    emits('deleteItem', deletedItem);
  }
};

const onAddItem = () => {
  // 当前有编辑行，就不可以新加行
  if (!isNil(editId.value)) {
    ElMessage.warning(`当前编辑行未保存`);
    return;
  }
  if (tableData.value.length === props.maxRowCount) {
    ElMessage.warning(`当前表格最多支持 ${props.maxRowCount} 条数据`);
    return;
  }
  const newId = uuid();
  const newRow = props.columnList
    .map((item) => item.key)
    .reduce(
      (acc, key) => {
        acc[key] = undefined;
        return acc;
      },
      { id: newId },
    );
  tableData.value.push(newRow);
  editId.value = newId;
  // 表格滚轮到最后一行
  nextTick(() => {
    if (tableRef.value) {
      // 获取表格的滚动容器
      const scrollBody = tableRef.value.$el.querySelector('.el-table__body');
      if (scrollBody) {
        tableRef.value.tableRef.setScrollTop(scrollBody.scrollHeight); // 使用 setScrollTop 方法滚动到底部
      }
    }
  });
};

// 自定组件 更新值
const onItemChange = (rowId, newVal: { colId: string; value: any }[]) => {
  const row = find(tableData.value, { id: rowId });
  newVal.forEach((item) => {
    row[item.colId] = item.value;
  });
};

onMounted(() => {});

defineExpose({
  // tableData,
  isEditing,
});
</script>
<style scoped lang="scss">
.edit-table-container {
  .add-btn-row {
    width: 100%;
    height: 32px;
    border: 1px solid $gBorderColorBase;
    border-top: none;
    padding: 0 8px;

    .btn-box {
      height: 100%;
      width: 50px;
      color: $gColorPrimary;
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
  :deep(.tableHeader) {
    margin: 0;
  }
  :deep(.el-select__wrapper) {
    min-height: 24px;
  }
}
</style>
