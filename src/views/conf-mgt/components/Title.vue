<template>
  <div class="flex items-center">
    <div class="w-[4px] h-[16px] bg-gColorPrimary mr-[8px]"></div>
    <span class="font-bold text-[#272834] mr-[4px]">{{ title }}</span>
    <el-tooltip v-if="tooltipContent" effect="light" placement="top" :content="tooltipContent">
      <SvgIcon class="text-[16px] cursor-pointer text-gTextColorSecondary" name="explain_icon" />
    </el-tooltip>
    <div v-if="saveRequestStatusMap[tableType] === false" class="text-[red] ml-[4px]">当前区域数据保存失败</div>
  </div>
</template>

<script setup lang="ts">
import { useConfStore } from '@/store/conf-mgt';
import { TableType } from './constants';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const props = defineProps<{
  tableType: TableType | 'baseInfo';
  title: string;
  tooltipContent?: string;
}>();

const confStore = useConfStore();
const { saveRequestStatusMap } = storeToRefs(confStore);
</script>
<style scoped lang="scss"></style>
