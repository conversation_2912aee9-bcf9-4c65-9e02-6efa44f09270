<template>
  <div>
    <Title
      title="基本信息"
      tableType="baseInfo"
      tooltipContent="填写基础信息后，须先预览已有关联逻辑，再进行新增选项条件和对应的逻辑"
    />
    <FormCreate :rule="rule" v-model:api="fApi" :option="options" class="form-create"></FormCreate>
  </div>
</template>

<script setup lang="ts">
import Title from './Title.vue';

import formCreate, { Api } from '@form-create/element-ui';
import usePermissionStore from '@/store/permission';
import { AutoImportRuleItem } from '../typing';
import { useConfStore } from '@/store/conf-mgt';
import { StepOneForm } from './typing';
import { isString } from 'lodash';

const props = defineProps<{
  initInfo?: StepOneForm | AutoImportRuleItem;
}>();

const permissionStore = usePermissionStore();
const { userInfo } = storeToRefs(permissionStore);

const confStore = useConfStore();
const { constantOptionMap } = storeToRefs(confStore);

const FormCreate = formCreate.$form();
const fApi = ref<Api | undefined>(null);
const rule = ref([
  {
    type: 'input',
    field: 'name',
    title: '流程名称',
    value: props.initInfo?.name ?? undefined,
    $required: true,
    props: {
      placeholder: '请输入',
    },
    validate: [
      {
        validator: (rule, value) => {
          if (isString(value) && value.trim() === '') {
            return false;
          } else {
            return true;
          }
        },
        message: '流程名称不能为空',
      },
    ],
  },
  {
    type: 'input',
    field: 'remarks',
    title: '备注',
    value: props.initInfo?.remarks ?? undefined,
    props: {
      placeholder: '请输入',
    },
  },
  {
    type: 'input',
    field: 'customer',
    title: 'Customer',
    $required: true,
    value: props.initInfo?.customer ?? userInfo.value.companyCode,
    props: {
      disabled: true,
    },
  },
  {
    type: 'input',
    field: 'subCustomer',
    title: 'Sub Customer',
    value: props.initInfo?.subCustomer ?? userInfo.value.companyCode,
    $required: true,
    props: {
      placeholder: '请输入',
    },
    validate: [
      {
        validator: (rule, value) => {
          if (isString(value) && value.trim() === '') {
            return false;
          } else {
            return true;
          }
        },
        message: 'Sub Customer不能为空',
      },
    ],
  },
  {
    type: 'input',
    field: 'factory',
    title: 'Factory',
    value: props.initInfo?.factory ?? undefined,
    $required: true,
    props: {
      placeholder: '请输入',
    },
    validate: [
      {
        validator: (rule, value) => {
          if (isString(value) && value.trim() === '') {
            return false;
          } else {
            return true;
          }
        },
        message: 'Factory不能为空',
      },
    ],
  },
  {
    type: 'select',
    field: 'testArea',
    title: 'Test Area',
    value: props.initInfo?.testArea ?? undefined,
    options: Object.keys(constantOptionMap.value.testArea).map((item) => ({ value: item, label: item })),
    $required: true,
    props: {
      placeholder: '请输入',
    },
  },
  {
    type: 'select',
    field: 'fileCategory',
    title: 'File Category',
    options: constantOptionMap.value.fileCategory.map((item) => ({ value: item, label: item })),
    value: props.initInfo?.fileCategory ?? undefined,
    $required: true,
    props: {
      placeholder: '请输入',
    },
  },
]);
const options = ref({
  submitBtn: false,
  resetBtn: false,
  col: {
    span: 12,
  },
});

// 暴露表单验证方法给父组件
defineExpose({
  formApi: fApi,
});
</script>
<style scoped lang="scss">
.form-create {
  :deep(.el-row) {
    > div {
      margin-bottom: 16px;
    }
    .el-form-item__label,
    .el-input__inner,
    .el-select__selected-item {
      // color: $gTextColorPrimary;
    }
  }
}
</style>
