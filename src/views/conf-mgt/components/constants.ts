import RuleItem from './customItem/RuleItem.vue';
import UploadItem from './customItem/UploadItem.vue';

export enum TableItemType {
  Text = 'text',
  Input = 'input',
  Select = 'select',
  Customer = 'customer',
}

export enum TableType {
  SubCustomer = 'subCustomer',
  FileRename = 'fileRename',
  SpecialLogic = 'specialLogic',
  ConvertScript = 'convertScript',
  CriticalField = 'critialField',
}

export const BASE_INFO = 'baseInfo';

export const SUB_CUSTOMER_COLUMN_LIST = [
  {
    type: TableItemType.Input,
    label: 'FTP IP',
    key: 'ftpIp',
    require: true,
  },
  {
    type: TableItemType.Input,
    label: 'Directory',
    key: 'directory',
    require: true,
  },
];

export const FILE_RENAME_COLUMN_LIST = [
  {
    type: TableItemType.Input,
    label: 'FTP IP',
    key: 'ftpIp',
    require: true,
  },
  {
    type: TableItemType.Input,
    label: 'Use Timestamp',
    key: 'useTimestamp',
  },
  {
    type: TableItemType.Input,
    label: 'Directory Num',
    key: 'directoryNum',
  },
  {
    type: TableItemType.Customer,
    label: 'Rule Expression',
    key: 'ruleExpression',
    customComponent: RuleItem,
    tableType: TableType.FileRename, // custom 列需要
    require: true,
  },
];

export const SPECIAL_LOGIC_COLUMN_LIST = (constantOptionMap) => {
  return [
    {
      type: TableItemType.Input,
      label: 'Device',
      key: 'deviceId',
    },
    {
      type: TableItemType.Select,
      label: 'Field Type',
      key: 'fieldType',
      options: constantOptionMap.stdfFieldType.map((item) => ({ value: item, label: item })),
      require: true,
    },
    {
      type: TableItemType.Select,
      label: 'Rule Type',
      key: 'ruleType',
      options: constantOptionMap.dataClearRuleType.map((item) => ({ value: item, label: item })),
    },
    {
      type: TableItemType.Customer,
      label: 'Rule',
      key: 'rule',
      customComponent: RuleItem,
      tableType: TableType.SpecialLogic, // custom 列需要
    },
  ];
};

export const CONVERT_SCRIPT_COLUMN_LIST = () => {
  return [
    {
      type: TableItemType.Customer,
      label: 'Reg Rule',
      key: 'regRule',
      customComponent: RuleItem,
      tableType: TableType.ConvertScript, // custom 列需要
      require: true,
    },
    {
      type: TableItemType.Customer,
      label: '文件名称',
      key: 'fileName',
      customComponent: UploadItem,
      tableType: TableType.ConvertScript, // custom 列需要
      require: true,
    },
    {
      type: TableItemType.Select,
      label: '解析规则',
      key: 'executeFileName',
      require: true,
      optionsKey: 'scriptList',
    },
  ];
};

export const CRITICAL_FIELD_COLUMN_LIST = (constantOptionMap) => {
  return [
    {
      type: TableItemType.Text,
      label: '关键字段',
      key: 'field',
      require: true,
    },
    {
      type: TableItemType.Customer,
      label: 'Rule Expression',
      key: 'ruleExpression',
      customComponent: RuleItem,
      tableType: TableType.CriticalField, // custom 列需要
      require: true,
      desc: `可使用元数据：${constantOptionMap.metadata.join(',')}`,
    },
  ];
};
