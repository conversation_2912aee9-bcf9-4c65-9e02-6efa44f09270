<template>
  <div class="rule-process-dialog-container">
    <el-dialog
      v-model="visible"
      :title="ruleData ? '编辑规则流程' : '新建规则流程'"
      width="920"
      :before-close="handleClose"
      class="rule-process-dialog"
    >
      <div class="content">
        <StepHeader :step="step" />
        <template v-if="step === 1">
          <Step1 ref="formRef" :initInfo="stepOneInitInfo" />
        </template>
        <template v-else>
          <Step2 ref="step2Ref" :stepOneInfo="stepOneInfo" @allRequestFinish="onAllRequestFinish" />
        </template>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <template v-if="step === 1">
            <el-tooltip content="点击下一步预览当前已存在的规则逻辑，可编辑或新增" placement="top" effect="light">
              <el-button @click="onNext" :loading="loading" type="primary">下一步</el-button>
            </el-tooltip>
            <el-button @click="visible = false">取消</el-button>
          </template>
          <template v-else>
            <el-button type="primary" @click="onSave">保存</el-button>
            <el-button v-show="step === 2" @click="onPrevious">上一步</el-button>
            <el-button @click="visible = false">取消</el-button>
          </template>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import StepHeader from './StepHeader.vue';
import Step1 from './Step1.vue';
import Step2 from './Step2.vue';

import { Api } from '@form-create/element-ui';
import { validateRuleFlow } from '@/service/conf-mgt';
import { AutoImportRuleItem } from '../typing';
import { isNil, omit } from 'lodash';
import { BPMS_SUCCESSS_CODE } from '../constants';
import { StepOneForm } from './typing';
import { useConfStore } from '@/store/conf-mgt';

const props = defineProps<{
  ruleData?: AutoImportRuleItem;
}>();
const emits = defineEmits(['saveFinish']);

const visible = defineModel<boolean>('visible');

const confStore = useConfStore();
const { saveRequestStatusMap } = storeToRefs(confStore);

const step = ref<number>(1);

const formRef = ref<{
  formApi: Api | undefined;
}>();

const step2Ref = ref(null);

const stepOneInitInfo = ref<StepOneForm | AutoImportRuleItem>();
const stepOneInfo = ref<StepOneForm>();
const loading = ref<boolean>(false);
const onNext = async () => {
  if (!formRef.value?.formApi) return;
  try {
    const isValid = await formRef.value.formApi.validate().catch((formError) => {
      throw { type: 'FORM_ERROR', error: formError };
    }); // 表单校验
    if (isValid) {
      const formData = formRef.value.formApi.formData();
      const params = {
        id: props?.ruleData?.id ?? undefined,
        ...omit(formData, 'customer'),
      };
      loading.value = true;
      const res = await validateRuleFlow(params); // 后端校验
      const resData = res.data.value;
      if (resData.code === BPMS_SUCCESSS_CODE) {
        // 校验通过
        if (resData.data) {
          stepOneInfo.value = { ...params } as StepOneForm; // step2 需要的
          stepOneInitInfo.value = { ...formData } as StepOneForm; // step1 回显需要的
          step.value++;
        } else {
          ElMessage.error('当前流程名称已存在，请重新命名！'); // 后端只校验了名称是否重复
        }
      } else {
        ElMessage.error('流程名称验证失败');
      }
    }
  } catch (error) {
    // 区分错误类型
    if (error?.type === 'FORM_ERROR') {
      ElMessage.warning('请输入必要的信息后再预览规则！');
    } else {
      ElMessage.error('流程名称验证失败');
    }
  } finally {
    loading.value = false;
  }
};

const onPrevious = () => {
  ElMessageBox.confirm('返回上一步，当前页面修改内容将会清除，请确认。', '确认', {
    confirmButtonText: '确认',
  }).then(() => {
    step.value--;
    // 恢复表格内容
    stepOneInitInfo.value = stepOneInfo.value;
  });
};

const onSave = async () => {
  step2Ref.value.onSaveFn();
};
// 请求全部完成，并且没有错误才能关闭弹窗
const onAllRequestFinish = () => {
  const resStatus = Object.values(saveRequestStatusMap.value).filter((item) => !isNil(item));
  // 全部都是成功的时候才关闭
  if (resStatus.every((item) => item === true)) {
    visible.value = false;
    emits('saveFinish');
  }
};

const handleClose = () => {
  ElMessageBox.confirm('当前页面修改内容将会清除，请确认。', '确认', {
    confirmButtonText: '确认',
  }).then(() => {
    visible.value = false;
  });
};

onMounted(() => {
  stepOneInitInfo.value = props.ruleData;
});
</script>
<style scoped lang="scss">
.rule-process-dialog-container {
  .content {
    // height: 1064px;
    min-height: 568px;
    max-height: calc(100vh - 100px - 56px - 56px - 100px);
    overflow: auto;
    display: flex;
    flex-direction: column;
    gap: 20px;
    color: $gTextColorPrimary;
  }
  :deep(.rule-process-dialog) {
    margin-top: 20px;
  }
}
</style>
