<template>
  <div class="step-two-container" v-loading="loading">
    <!-- 子客户目录配置 -->
    <div class="table-box">
      <Title
        :tableType="TableType.SubCustomer"
        :title="tableTitleMap[TableType.SubCustomer]"
        tooltipContent="可根据不同子客户配置不同FTP路径"
      />
      <EditTable
        ref="subCustomerTableRef"
        :columnList="columnListMap[TableType.SubCustomer]"
        v-model:data="subCustomerTable"
        :initData="initTableMap[TableType.SubCustomer]"
      />
    </div>
    <!-- 文件重命名配置 -->
    <div class="table-box">
      <Title
        :tableType="TableType.FileRename"
        :title="tableTitleMap[TableType.FileRename]"
        tooltipContent="可对拉取到的文件进行重命名"
      />
      <EditTable
        ref="fileRenameTableRef"
        :columnList="columnListMap[TableType.FileRename]"
        v-model:data="fileRenameTable"
        :initData="initTableMap[TableType.FileRename]"
        :saveItemValidFn="fileRenameSaveValid"
      />
    </div>
    <!-- 特殊逻辑配置 -->
    <div class="table-box">
      <Title
        :tableType="TableType.SpecialLogic"
        :title="tableTitleMap[TableType.SpecialLogic]"
        tooltipContent="File Category为STDF时支持J750机台和从TSR中解析测试项的特殊逻辑"
      />
      <template v-if="stepOneInfo.fileCategory === 'STDF'">
        <EditTable
          ref="specialLogicTableRef"
          :columnList="columnListMap[TableType.SpecialLogic]"
          v-model:data="specialLogicTable"
          :initData="initTableMap[TableType.SpecialLogic]"
        />
      </template>
      <div v-else class="text-gTextColorPlaceholder">File Category不为STDF，无需特殊逻辑配置。</div>
    </div>
    <!-- 解析脚本配置 -->
    <div class="table-box">
      <Title
        :tableType="TableType.ConvertScript"
        :title="tableTitleMap[TableType.ConvertScript]"
        tooltipContent="File Category不为STDF时支持通过正则表达式识别需要解析的文件，并上传Python转换脚本进行解析"
      />
      <template v-if="stepOneInfo.fileCategory !== 'STDF'">
        <EditTable
          ref="convertScriptTableRef"
          :columnList="columnListMap[TableType.ConvertScript]"
          v-model:data="convertScriptTable"
          :initData="initTableMap[TableType.ConvertScript]"
          @resetItem="onConvertScriptItemReset"
        />
      </template>
      <div v-else class="text-gTextColorPlaceholder">File Category为STDF，无需解析脚本配置。</div>
    </div>
    <!-- 关键字段配置 -->
    <div class="table-box">
      <Title
        :tableType="TableType.CriticalField"
        :title="tableTitleMap[TableType.CriticalField]"
        tooltipContent="File Category为STDF或RAW_DATA时支持通过JavaScript表达式配合元数据（metaData，STDF原始数据或RAW_DATA预处理成标准CSV中的关键字段）进行入库字段的自定义配置"
      />
      <template v-if="['STDF', 'RAW_DATA', 'BIT_MEM'].includes(stepOneInfo.fileCategory)">
        <div class="flex items-center gap-[8px]">
          <div class="w-[120px] flex items-center justify-end">
            <SvgIcon class="text-[12px]" name="require_icon" />
            选择关键字段
          </div>
          <el-select
            v-model="criticalField"
            multiple
            placeholder="请选择"
            style="width: 790px"
            @change="onCriticalFieldChange"
            clearable
          >
            <el-option v-for="item in criticalFieldOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <EditTable
          ref="criticalFieldTableRef"
          :columnList="columnListMap[TableType.CriticalField]"
          v-model:data="criticalFieldTable"
          :initData="initTableMap[TableType.CriticalField]"
          @deleteItem="onCriticalFieldItemDelete"
          :isHideEmptyAddBtn="true"
          :isHideAddBtn="true"
        />
      </template>
      <div v-else class="text-gTextColorPlaceholder">File Category不为STDF、RAW_DATA或BIT_MEM，无需关键字段配置。</div>
    </div>
    <!-- 编辑rule弹窗：多个表格共用-->
    <EditRuleDialog v-if="editRuleInfo.visible" @confirm="onEditRuleOk" />
  </div>
</template>

<script setup lang="ts">
import { cloneDeep, find, isArray, isNil, isNumber, isString, pick, uniq } from 'lodash';
import EditTable from './EditTable.vue';
import Title from './Title.vue';
import EditRuleDialog from './customItem/EditRuleDialog.vue';

import {
  getConvertScript,
  getCriticalField,
  getCriticalFieldDeviceId,
  getFileRenameRule,
  getSpecialLogic,
  getSubCustomerRule,
  saveConvertScript,
  saveCriticalField,
  saveCriticalFieldDeviceId,
  saveFileRenameRule,
  saveRuleFlow,
  saveSpecialLogic,
  saveSubCustomerRule,
} from '@/service/conf-mgt';
import {
  SUB_CUSTOMER_COLUMN_LIST,
  FILE_RENAME_COLUMN_LIST,
  SPECIAL_LOGIC_COLUMN_LIST,
  CONVERT_SCRIPT_COLUMN_LIST,
  CRITICAL_FIELD_COLUMN_LIST,
  TableType,
  TableItemType,
  BASE_INFO,
} from './constants';
import { StepOneForm } from './typing';
import { useConfStore } from '@/store/conf-mgt';
import uuid from '@/utils/uuid';
import { BPMS_SUCCESSS_CODE } from '../constants';

const props = defineProps<{
  stepOneInfo: StepOneForm;
}>();
const emits = defineEmits(['allRequestFinish']);

const confStore = useConfStore();
const { editRuleInfo, constantOptionMap, saveRequestStatusMap } = storeToRefs(confStore);
const { resetSaveRequestStatusMap } = confStore;

const testAreaType = constantOptionMap.value.testArea[props.stepOneInfo.testArea];

// data
const subCustomerTable = ref<Record<string, any>[]>([]);
const fileRenameTable = ref<Record<string, any>[]>([]);
const specialLogicTable = ref<Record<string, any>[]>([]);
const convertScriptTable = ref<Record<string, any>[]>([]);
const criticalFieldTable = ref<Record<string, any>[]>([]);

// ref
const subCustomerTableRef = ref(null);
const fileRenameTableRef = ref(null);
const specialLogicTableRef = ref(null);
const convertScriptTableRef = ref(null);
const criticalFieldTableRef = ref(null);

const tableTitleMap = {
  [TableType.SubCustomer]: '子客户目录配置',
  [TableType.FileRename]: '文件重命名配置',
  [TableType.SpecialLogic]: '特殊逻辑配置',
  [TableType.ConvertScript]: '解析脚本配置',
  [TableType.CriticalField]: '关键字段配置',
};

const tableMap = {
  [TableType.SubCustomer]: subCustomerTable,
  [TableType.FileRename]: fileRenameTable,
  [TableType.SpecialLogic]: specialLogicTable,
  [TableType.ConvertScript]: convertScriptTable,
  [TableType.CriticalField]: criticalFieldTable,
};

const tableRefMap = {
  [TableType.SubCustomer]: subCustomerTableRef,
  [TableType.FileRename]: fileRenameTableRef,
  [TableType.SpecialLogic]: specialLogicTableRef,
  [TableType.ConvertScript]: convertScriptTableRef,
  [TableType.CriticalField]: criticalFieldTableRef,
};

const requestMap = {
  [TableType.SubCustomer]: getSubCustomerRule,
  [TableType.FileRename]: getFileRenameRule,
  [TableType.SpecialLogic]: getSpecialLogic,
  [TableType.ConvertScript]: getConvertScript,
  [TableType.CriticalField]: getCriticalField,
};

const saveRequestMap = {
  [TableType.SubCustomer]: saveSubCustomerRule,
  [TableType.FileRename]: saveFileRenameRule,
  [TableType.SpecialLogic]: saveSpecialLogic,
  [TableType.ConvertScript]: saveConvertScript,
  [TableType.CriticalField]: saveCriticalField,
};

const columnListMap = {
  [TableType.SubCustomer]: SUB_CUSTOMER_COLUMN_LIST,
  [TableType.FileRename]: FILE_RENAME_COLUMN_LIST,
  [TableType.SpecialLogic]: SPECIAL_LOGIC_COLUMN_LIST(constantOptionMap.value),
  [TableType.ConvertScript]: CONVERT_SCRIPT_COLUMN_LIST(),
  [TableType.CriticalField]: CRITICAL_FIELD_COLUMN_LIST(constantOptionMap.value),
};

// 每个表格的初始值
// 记录每个表格的init数据
const initTableMap = ref({
  [TableType.SubCustomer]: [],
  [TableType.FileRename]: [],
  [TableType.SpecialLogic]: [],
  [TableType.ConvertScript]: [],
  [TableType.CriticalField]: [],
});
const loading = ref<boolean>(false);
let deviceIdAllData = []; // 显示只显示第一行，但是返回需要全量数据
const getInitDetail = async () => {
  const { subCustomer, factory, testArea, fileCategory } = props.stepOneInfo;
  const params = {
    subCustomerList: subCustomer ? [subCustomer] : undefined,
    factoryList: factory ? [factory] : undefined,
    testAreaList: testArea ? [testArea] : undefined,
    fileCategoryList: fileCategory ? [fileCategory] : undefined,
    pageIndex: 1,
    pageSize: 9999,
  };
  loading.value = true;
  try {
    const requestTabelNameList = [TableType.SubCustomer, TableType.FileRename];
    if (fileCategory === 'STDF') {
      requestTabelNameList.push(TableType.SpecialLogic);
    } else {
      requestTabelNameList.push(TableType.ConvertScript);
    }
    if (['STDF', 'RAW_DATA', 'BIT_MEM'].includes(fileCategory)) {
      requestTabelNameList.push(TableType.CriticalField);
    }

    const requestList = requestTabelNameList.map((type: TableType) => {
      return requestMap[type]({ ...params });
    });

    try {
      const resList = await Promise.allSettled(requestList);
      // 请求失败提示
      const failRequest = [];
      resList.forEach((result, index) => {
        if (result.status === 'rejected') {
          ElMessage.error(`${tableTitleMap[requestTabelNameList[index]]}数据请求失败！`);
          failRequest.push(requestTabelNameList[index]);
        } else {
          const resData = result.value.data.value;
          if (resData.code !== BPMS_SUCCESSS_CODE) {
            ElMessage.error(`${tableTitleMap[requestTabelNameList[index]]}数据请求失败！`);
            failRequest.push(requestTabelNameList[index]);
          }
        }
      });

      // 处理结果
      requestTabelNameList.forEach((type: TableType, index: number) => {
        // 请求失败
        if (failRequest.includes(type)) {
          tableMap[type].value = [];
        } else {
          // 请求成功
          const res = resList[index].value?.data?.value?.data;
          const data = res?.data; // 数据表格
          if (isArray(data) && data.length > 0) {
            tableMap[type].value = data;
          } else {
            tableMap[type].value = [];
          }
        }
        // 记录初始值
        initTableMap.value[type] = cloneDeep(tableMap[type].value);
      });

      // 关键字段的deviceid 的单独请求
      if (['STDF', 'RAW_DATA', 'BIT_MEM'].includes(fileCategory)) {
        criticalField.value = criticalFieldTable.value.map((item) => item.field); // 处理关键字段下拉框已选项
        // deviceid 请求
        const res = await getCriticalFieldDeviceId({ ...params, pageSize: 9999 });
        const resData = res.data.value;
        if (resData.code === BPMS_SUCCESSS_CODE) {
          // deviceid 有数据
          if (isArray(resData?.data?.data) && resData?.data?.data?.length > 0) {
            // 如果有多个，显示第一个，但是保存的时候要全部传过去
            deviceIdAllData = cloneDeep(resData?.data?.data ?? []); // 保存deviceid全量数据

            // 插入deviced id 行
            const firstData = resData.data.data[0];
            const newRow = {
              id: firstData.id,
              field: 'deviceId',
              ruleExpression: firstData?.ruleExpression,
            };
            criticalFieldTable.value.unshift(newRow);
            initTableMap.value[TableType.CriticalField].unshift(newRow); // 更新 initTable

            criticalField.value.unshift('deviceId');
          }
        }
      }
    } catch (e) {
      console.error(e);
    }

    // console.log('subCustomerTable', subCustomerTable.value);
    // console.log('fileRenameTable', fileRenameTable.value);
    // console.log('specialLogicTable', specialLogicTable.value);
    // console.log('convertScriptTable', convertScriptTable.value);
    // console.log('criticalFieldTable', criticalFieldTable.value);
  } catch (error) {
    console.error('验证出错:', error);
  } finally {
    loading.value = false;
  }
};

// 弹窗确认---修改字段值
const onEditRuleOk = () => {
  const { tableType, rowData, colItem, content } = editRuleInfo.value;
  const row = find(tableMap[tableType].value, { id: rowData?.id });
  if (row) {
    row[colItem.key] = content;
  }
};

// 校验重命名配置保存的校验
const fileRenameSaveValid = (rowData) => {
  const { directoryNum, useTimestamp } = rowData;
  // edittable 内部已经去除了首尾空
  // useTimestamp==1的时候newDirectoryNum不能为空
  // 不能两个全为空
  if (useTimestamp == 1 && !isNil(directoryNum) && directoryNum !== '') {
    ElMessage.warning(`Use Timestamp为1的时候，不能设置Directory Num`);
    return false;
  }
  //  else if ((isNil(directoryNum) || directoryNum == '') && (isNil(useTimestamp) || useTimestamp == '')) {
  //   ElMessage.warning(`Use Timestamp和Directory Num 不能全部为空`);
  //   return false;
  // }
  return true;
};

// 解析规则--把file都重置了（edittable内部只自动处置了fileName），rowData是已经处置了fileName后的
const onConvertScriptItemReset = (rowData: Record<string, any>, initRow: Record<string, any>) => {
  convertScriptTable.value = convertScriptTable.value.map((item: Record<string, any>) => {
    if (rowData.id === item.id) {
      // scriptList 重置
      return { ...item, file: undefined, scriptList: initRow.scriptList };
    } else {
      return { ...item };
    }
  });
};

const criticalField = ref<string[]>();
const onCriticalFieldChange = () => {
  criticalFieldTable.value = criticalField.value.map((item) => {
    const row = find(criticalFieldTable.value, { field: item });
    if (row) {
      return { ...row };
    } else {
      return { id: uuid(), field: item, ruleExpression: undefined };
    }
  });
};

// 修改关键字段选中项
const onCriticalFieldItemDelete = (rowData) => {
  criticalField.value = criticalField.value.filter((item) => item !== rowData.field);
};

// 保存
const saveRequestList = ref([]); // 记录请求了哪些接口，用来判断最后请求是否全部结束
const onSave = () => {
  // 校验是否有编辑态
  const isAnyTableEditing = Object.values(tableRefMap).some((tableRef) => tableRef.value?.isEditing);
  // 处理table
  if (isAnyTableEditing) {
    ElMessage.warning('当前存在编辑状态的内容，请先保存后再提交');
    return;
  }
  // 检查是否有重复项
  const newTableDataObj = {};

  const { flag: subCustomerFlag, tableData: subCustomerTableData } = checkTable(TableType.SubCustomer);
  if (subCustomerFlag) {
    return;
  }
  newTableDataObj[TableType.SubCustomer] = subCustomerTableData;

  const { flag: fileRenameFlag, tableData: fileRenameTableData } = checkTable(TableType.FileRename);
  if (fileRenameFlag) {
    return;
  }
  newTableDataObj[TableType.FileRename] = fileRenameTableData;

  if (props.stepOneInfo.fileCategory === 'STDF') {
    const { flag, tableData } = checkTable(TableType.SpecialLogic);
    if (flag) {
      return;
    }
    newTableDataObj[TableType.SpecialLogic] = tableData;
  } else {
    const { flag, tableData } = checkTable(TableType.ConvertScript);
    if (flag) {
      return;
    }
    newTableDataObj[TableType.ConvertScript] = tableData;
  }
  if (['STDF', 'RAW_DATA', 'BIT_MEM'].includes(props.stepOneInfo.fileCategory)) {
    const { flag, tableData } = checkTable(TableType.CriticalField);
    if (flag) {
      return;
    }
    newTableDataObj[TableType.CriticalField] = tableData;
  }

  resetSaveRequestStatusMap();
  saveRequestList.value = [BASE_INFO]; // BASE_INFO=baseInfo
  loading.value = true;

  // 开始请求
  saveStepOne();
  Object.keys(newTableDataObj).forEach((key: TableType) => {
    handleRequset(key, newTableDataObj[key]);
  });
};

// 判断请求是否已经全部结束
watch(
  [saveRequestList, saveRequestStatusMap],
  () => {
    if (saveRequestList.value.length == 0) return;
    const resStatus = Object.values(saveRequestStatusMap.value).filter((item) => !isNil(item));

    if (resStatus.length === saveRequestList.value.length) {
      // console.log('全部接口请求完成', saveRequestStatusMap.value);
      loading.value = false;
      emits('allRequestFinish');
    }
  },
  { deep: true },
);

const saveStepOne = async () => {
  try {
    const res = await saveRuleFlow({ ...props.stepOneInfo });
    const resData = res.data.value;
    if (resData.code !== BPMS_SUCCESSS_CODE) {
      ElMessage.warning(`基础信息保存失败：${resData.msg}`);
      saveRequestStatusMap.value[BASE_INFO] = false;
    } else {
      saveRequestStatusMap.value[BASE_INFO] = true;
    }
  } catch (e) {
    saveRequestStatusMap.value[BASE_INFO] = false;
  }
};

const handleRequset = async (tableType: TableType, tableData: any[]) => {
  saveRequestList.value.push(tableType);

  // 关键字段还要再请求deviceId
  let criticalFieldRequestStatus = undefined;
  if (tableType === TableType.CriticalField) {
    const deviceIdRow = find(tableData, { field: 'deviceId' });
    // 现在没有 deviceid ，原本也没有，所以不用请求
    if (isNil(deviceIdRow) && deviceIdAllData.length === 0) {
      criticalFieldRequestStatus = true; // deviceid 不用请求，相当于请求成功了
    } else if (deviceIdRow || (isNil(deviceIdRow) && deviceIdAllData.length > 0)) {
      // 需要请求deviceid
      const params = {
        warehouseRuleFlowRequestVo: {
          ...pick(props.stepOneInfo, ['name', 'subCustomer', 'factory', 'testArea', 'fileCategory']),
        },
        ruleRequestVos: [
          ...deviceIdAllData.slice(1), // 把其他的传过去
        ],
      };

      // 如果现在有deviceid
      if (deviceIdRow) {
        params.ruleRequestVos.unshift({
          id: isNumber(deviceIdRow.id) ? deviceIdRow.id : undefined, // 新加的行 id是string
          ...pick(props.stepOneInfo, ['subCustomer', 'factory', 'testArea']),
          factorySite: props.stepOneInfo.factory,
          ruleExpression: deviceIdRow.ruleExpression,
        });
      }

      try {
        const res = await saveCriticalFieldDeviceId(params);
        const resData = res.data.value;
        if (resData.code !== BPMS_SUCCESSS_CODE) {
          ElMessage.warning(`${tableTitleMap[tableType]}的deviceId保存失败：${resData.msg}`);
          criticalFieldRequestStatus = false;
        } else {
          criticalFieldRequestStatus = true;
        }
      } catch (e) {
        console.error(e);
        ElMessage.warning(`${tableTitleMap[tableType]}保存失败`);
        criticalFieldRequestStatus = false;
      }
    }
  }

  // 组成参数
  const params = formatParams(tableType, tableData);

  // 请求
  try {
    const res = await saveRequestMap[tableType](params);
    const resData = res.data.value;
    if (resData.code !== BPMS_SUCCESSS_CODE) {
      ElMessage.warning(`${tableTitleMap[tableType]}保存失败：${resData.msg}`);
      saveRequestStatusMap.value[tableType] = false;
    } else {
      // 这个接口成功了
      if (tableType === TableType.CriticalField && !isNil(criticalFieldRequestStatus)) {
        // 关键字段还要考虑deviceId接口的结果
        saveRequestStatusMap.value[tableType] = criticalFieldRequestStatus;
      } else {
        saveRequestStatusMap.value[tableType] = true;
      }
    }
  } catch (e) {
    console.error(e);
    ElMessage.warning(`${tableTitleMap[tableType]}保存失败`);
    saveRequestStatusMap.value[tableType] = false;
  }
};

// 过滤表格重复项
// 检查指定列是否有重复项
const checkTable = (tableType: TableType) => {
  let flag = false;
  // 去除空项、空值--重置新建的会导致这个问题
  const columnListKey = columnListMap[tableType].map((item) => item.key);
  const tableData = tableMap[tableType].value.filter((row) => {
    // 全不为空
    return !columnListKey.every((key) => {
      const value = isString(row[key]) ? row[key].trim() : row[key];
      return isNil(value) || value === '';
    });
  });

  // 解析脚本表只需要校验正则字段（不考虑其他字段）
  if (tableType === TableType.ConvertScript) {
    const regRuleList = uniq(tableData.map((item) => item.regRule));
    flag = regRuleList.length !== tableData.length;
    if (flag) {
      ElMessage.warning(`${tableTitleMap[tableType]} 中 Reg Rule 存在重复数据，请更新后保存`);
    }
    return { flag, tableData }; // 不需要下面的： 校验每张表格非自定义列是否重复
  }

  // 如果是关键字段表，要额外校验rule有没有空。因为table里没有进入保存逻辑
  if (tableType === TableType.CriticalField) {
    flag = tableData.some((item) => isNil(item.ruleExpression)); // 只用检查初始加载的undefined情况
    if (flag) {
      ElMessage.warning(`${tableTitleMap[tableType]} 中存在信息未填写`);
      return { flag, tableData };
    }
  }

  // 校验每张表格非自定义列是否重复
  const validColumnListKey = columnListMap[tableType]
    .filter((item) => ![TableItemType.Customer].includes(item.type))
    .map((item) => item.key);

  if (validColumnListKey.length > 0) {
    const seen = new Set<string>();
    for (const obj of tableData) {
      // 生成当前对象的key组合字符串
      const keyCombination = validColumnListKey.map((k) => obj[k]).join('[&]-[#]');
      if (seen.has(keyCombination)) {
        flag = true;
      }
      seen.add(keyCombination);
    }
    if (flag) {
      ElMessage.warning(`“${tableTitleMap[tableType]}” 中存在重复内容，请更新后保存`);
      return { flag, tableData };
    }
  }

  return { flag, tableData };
};

const formatParams = (tableType, tableData) => {
  const columnListKey = columnListMap[tableType].map((item) => item.key);
  let params = {
    warehouseRuleFlowRequestVo: {
      ...pick(props.stepOneInfo, ['name', 'subCustomer', 'factory', 'testArea', 'fileCategory']),
    },
    ruleRequestVos: [],
  };

  if (![TableType.ConvertScript, TableType.CriticalField].includes(tableType)) {
    params.ruleRequestVos = tableData.map((row) => {
      const id = isNumber(row.id) ? row.id : undefined; // 新加的行 id是string
      const columnObj = {};
      columnListKey.forEach((key) => {
        columnObj[key] = row[key];
      });
      if (tableType === TableType.SubCustomer) {
        return {
          id,
          ...pick(props.stepOneInfo, ['subCustomer', 'factory', 'testArea']),
          factorySite: props.stepOneInfo.factory,
          ...columnObj,
        };
      } else if (tableType === TableType.FileRename) {
        return {
          id,
          ...pick(props.stepOneInfo, ['subCustomer']),
          ...columnObj,
        };
      } else if (tableType === TableType.SpecialLogic) {
        return {
          id,
          ...pick(props.stepOneInfo, ['subCustomer', 'factory', 'testArea']),
          factorySite: props.stepOneInfo.factory,
          ...columnObj,
        };
      }
    });
  } else if (tableType === TableType.ConvertScript) {
    // 解析文件接口这里不需要传第一部的信息
    params = new FormData();
    const wrapperList = [];
    const fileList = [];

    // 先传有文件的
    const hasFileTable = tableData.filter((row) => row?.file?.raw);
    const notHasFileTable = tableData.filter((row) => {
      return !row?.file?.raw;
    });

    [...hasFileTable, ...notHasFileTable].forEach((row) => {
      const id = isNumber(row.id) ? row.id : undefined; // 新加的行 id是string
      const columnObj = {};
      columnListKey.forEach((key) => {
        columnObj[key] = row[key];
      });
      wrapperList.push({
        id,
        ...pick(props.stepOneInfo, ['subCustomer', 'factory', 'testArea', 'fileCategory']),
        factorySite: props.stepOneInfo.factory,
        scriptList: row?.scriptList,
        ...columnObj,
      });
      fileList.push(row?.file?.raw ?? null);
    });

    // 如果全部删除了
    if (tableData.length === 0 && wrapperList.length === 0) {
      wrapperList.push({
        ...pick(props.stepOneInfo, ['subCustomer', 'factory', 'testArea', 'fileCategory']),
      });
    }
    // console.log('wrapperList', wrapperList);
    // console.log('fileList', fileList);
    // 将 wrappers 转换为 JSON 字符串
    const wrappersJson = JSON.stringify(wrapperList);
    params.append('wrappers', new Blob([wrappersJson], { type: 'application/json' }));

    // 添加文件字段
    for (let i = 0; i < fileList.length; i++) {
      params.append(`files`, fileList[i]);
    }
  } else if (tableType === TableType.CriticalField) {
    const filterTable = tableData.filter((item) => {
      return item.field !== 'deviceId';
    });
    params.ruleRequestVos = filterTable.map((row) => {
      const id = isNumber(row.id) ? row.id : undefined; // 新加的行 id是string
      const columnObj = {};
      columnListKey.forEach((key) => {
        columnObj[key] = row[key];
      });
      return {
        id,
        ...pick(props.stepOneInfo, ['subCustomer', 'factory', 'testArea']),
        factorySite: props.stepOneInfo.factory,
        ...columnObj,
        deviceId: undefined,
      };
    });
  }
  return params;
};

// 关键字段的下拉选框
const criticalFieldOptions = ['deviceId', ...constantOptionMap.value.parseableField]
  .filter((item) => {
    if (testAreaType === 'CP' && item === 'sblotId') {
      return false;
    } else if (testAreaType === 'FT' && ['originWaferId', 'waferNo'].includes(item)) {
      return false;
    }
    return true;
  })
  .map((item) => {
    if (
      (testAreaType === 'CP' && item === 'waferNo') ||
      (testAreaType === 'FT' && ['sblotId', 'offlineRetest'].includes(item))
    ) {
      return {
        value: item,
        label: `*${item}`,
      };
    } else {
      return {
        value: item,
        label: item,
      };
    }
  });

onMounted(() => {
  getInitDetail(); // 确定各个表格的初始值
  resetSaveRequestStatusMap();
});

defineExpose({
  onSaveFn: onSave,
});
</script>

<style scoped lang="scss">
.step-two-container {
  display: flex;
  flex-direction: column;
  gap: 20px;

  .table-box {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  :deep(.el-radio) {
    margin-right: 8px;
  }
}
</style>
