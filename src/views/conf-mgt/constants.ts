import { getFilterDropDown } from '@/service/conf-mgt';
import { SearchFormTypeEnum, SearchFormItem, TableHeader as ColSettingItem, DATE_SHORTCUTS } from '@guwave/components';
import { dayjs } from 'element-plus';

export const BPMS_SUCCESSS_CODE = '00000001';

export const SEARCH_LIST = (): SearchFormItem[] => {
  const list = [
    {
      type: SearchFormTypeEnum.SELECT,
      label: '流程名称',
      key: 'nameList',
      api: getFilterDropDown,
      dropdownKey: 'NAME',
      nativeAttr: {
        multiple: true,
      },
    },
    {
      type: SearchFormTypeEnum.SELECT_V2,
      label: 'Sub Customer',
      key: 'subCustomerList',
      api: getFilterDropDown,
      dropdownKey: 'SUB_CUSTOMER',
      nativeAttr: {
        multiple: true,
      },
    },
    {
      type: SearchFormTypeEnum.SELECT_V2,
      label: 'Factory',
      key: 'factoryList',
      api: getFilterDropDown,
      dropdownKey: 'FACTORY',
      nativeAttr: {
        multiple: true,
      },
    },
    {
      type: SearchFormTypeEnum.SELECT_V2,
      label: 'Test Area',
      key: 'testAreaList',
      api: getFilterDropDown,
      dropdownKey: 'TEST_AREA',
      nativeAttr: {
        multiple: true,
      },
    },
    {
      type: SearchFormTypeEnum.SELECT_V2,
      label: 'File Category',
      key: 'fileCategoryList',
      api: getFilterDropDown,
      dropdownKey: 'FILE_CATEGORY',
      nativeAttr: {
        multiple: true,
      },
    },
    {
      type: SearchFormTypeEnum.SELECT_V2,
      label: '创建用户',
      key: 'createUserList',
      api: getFilterDropDown,
      dropdownKey: 'CREATE_USER',
      nativeAttr: {
        multiple: true,
      },
    },
    {
      type: SearchFormTypeEnum.DATE,
      label: '创建时间',
      key: 'createTime',
      nativeAttr: {
        type: 'daterange',
        unlinkPanels: true,
        rangeSeparator: '-',
        startPlaceholder: '开始时间',
        endPlaceholder: '结束时间',
        shortcuts: DATE_SHORTCUTS,
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      type: SearchFormTypeEnum.SELECT_V2,
      label: '更新用户',
      key: 'updateUserList',
      api: getFilterDropDown,
      dropdownKey: 'UPDATE_USER',
      nativeAttr: {
        multiple: true,
      },
    },
    {
      type: SearchFormTypeEnum.DATE,
      label: '更新时间',
      key: 'updateTime',
      nativeAttr: {
        type: 'daterange',
        unlinkPanels: true,
        rangeSeparator: '-',
        startPlaceholder: '开始时间',
        endPlaceholder: '结束时间',
        shortcuts: DATE_SHORTCUTS,
        valueFormat: 'YYYY-MM-DD',
      },
    },
  ];
  // return list;
  const res = list.map((item) => {
    // 删除filterKey 重写api的请求参数
    return {
      ...item,
      api: (param) => {
        const newParams = {
          ...param,
          filterField: param.field,
          createTimeStart: param?.createTime ? dayjs(param.createTime?.[0]).format('YYYY-MM-DD 00:00:00') : undefined,
          createTimeEnd: param?.createTime ? dayjs(param.createTime?.[1]).format('YYYY-MM-DD 23:59:59') : undefined,
          updateTimeStart: param?.updateTime ? dayjs(param.updateTime?.[0]).format('YYYY-MM-DD 00:00:00') : undefined,
          updateTimeEnd: param?.updateTime ? dayjs(param.updateTime?.[1]).format('YYYY-MM-DD 23:59:59') : undefined,
          field: undefined,
          createTime: undefined,
          updateTime: undefined,
        };
        return item.api(newParams);
      },
    };
  });
  return res;
};

// 表格列
export const COLUMN_LIST = [
  {
    label: '流程名称',
    key: 'name',
    fixed: 'left',
    sortable: 'custom',
  },
  {
    label: 'Sub Customer',
    key: 'subCustomer',
    sortable: 'custom',
    sortKey: 'sub_customer',
  },
  {
    label: 'Factory',
    key: 'factory',
    sortable: 'custom',
  },
  {
    label: 'Test Area',
    key: 'testArea',
    sortable: 'custom',
    sortKey: 'test_area',
  },
  {
    label: 'File Category',
    key: 'fileCategory',
    sortable: 'custom',
    sortKey: 'file_category',
  },
  {
    label: '备注',
    key: 'remarks',
    sortable: 'custom',
  },
  {
    label: '创建用户',
    key: 'createUser',
    sortable: 'custom',
    sortKey: 'create_user',
  },
  {
    label: '创建时间',
    key: 'createTime',
    sortable: 'custom',
    sortKey: 'create_time',
  },
  {
    label: '更新用户',
    key: 'updateUser',
    sortable: 'custom',
    sortKey: 'update_user',
  },
  {
    label: '更新时间',
    key: 'updateTime',
    sortable: 'custom',
    sortKey: 'update_time',
  },
];

export const COL_SETTING_LIST: ColSettingItem[] = COLUMN_LIST.map((item) => ({
  name: item.key,
  showName: item.label,
}));

export const fackData = () => {
  const size = 30;
  // const currentDate = new Date();
  return {
    pageIndex: 1,
    pageSize: size,
    total: size,
    pages: size,
    data: Array.from({ length: size }, (_, index) => ({
      id: index,
      name: '111',
      customer: '111',
      subCustomer: '111',
      factory: '111',
      testArea: '111',
      fileCategory: '111',
      remarks: '111',
      createTime: '111',
      updateTime: '111',
      createUser: Math.floor(Math.random() * 300),
      updateUser: Math.floor(Math.random() * 500),
    })),
  };
};
