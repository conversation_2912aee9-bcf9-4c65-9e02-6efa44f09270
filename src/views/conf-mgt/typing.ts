export interface AutoImportRuleItem {
  id: number;
  name: string;
  customer: string;
  subCustomer: string;
  factory: string;
  testArea: string;
  fileCategory: string;
  remarks: string;
  createTime: string;
  updateTime: string;
  createUser: string;
  updateUser: string;
}

export interface ConstantOptionMap {
  dataClearRuleType: string[];
  fileCategory: string[];
  parseableField: string[];
  metadata: string[];
  stdfFieldType: string[];
  testArea: string[];
}

// export enum DialogTypeEnum {
//   Create = 'create',
//   Edit = 'edit',
// }
