import { PaginationType } from '@guwave/components';
import { ListItem } from '../typing';
import { getPageList } from '@/service/serviceManagement';
import { RESPONSE_SUCCESS_CODE } from '@/utils/http';

export const useTable = () => {
  const loading = ref(false);
  const tableData = ref<ListItem[]>([]);
  const pagination = reactive<PaginationType>({
    total: 0,
    pageSize: 20,
    pageIndex: 1,
    currentChange: (current: number) => {
      pagination.pageIndex = current;
      getList();
    },
    sizeChange: (size: number) => {
      pagination.pageIndex = 1;
      pagination.pageSize = size;
      getList();
    },
  });

  const getList = async () => {
    const param = {
      pageSize: pagination.pageSize,
      pageIndex: pagination.pageIndex,
    };

    try {
      loading.value = true;
      const res = await getPageList(param);
      if (res.data.code === RESPONSE_SUCCESS_CODE) {
        const rData = res.data.data;
        pagination.total = rData.total;
        pagination.pageIndex = rData.pageIndex;
        pagination.pageSize = rData.pageSize;
        tableData.value = rData.data;
      }
    } catch (err) {
      console.error(err);
    } finally {
      loading.value = false;
    }
  };

  onBeforeMount(() => {
    getList();
  });

  return {
    getList,
    loading,
    tableData,
    pagination,
  };
};
