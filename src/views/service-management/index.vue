<template>
  <div class="servicePage">
    <CommonTable border :data="tableData" v-model:pagination="pagination" title="业务计算管理" v-loading="loading">
      <template #headerRightStart>
        <el-button type="primary" @click="addRow">新建</el-button>
      </template>

      <template v-for="item in TABLE_HEADER" :key="item.prop">
        <!-- *特殊处理状态列 -->
        <el-table-column v-if="item.prop === 'status'" label="状态" show-overflow-tooltip>
          <template #default="scope">
            <el-switch :activeValue="scope.row.status === 0" @change="() => changeStatus(scope.row)" />
          </template>
        </el-table-column>
        <!-- *特殊处理dw列 -->
        <el-table-column v-else-if="item.prop === 'calculateDwTestItem'" label="Dw Test Item" show-overflow-tooltip>
          <template #default="scope">
            <el-switch :activeValue="scope.row.calculateDwTestItem === 0" disabled />
          </template>
        </el-table-column>
        <!-- *特殊处理yms列 -->
        <el-table-column v-else-if="item.prop === 'calculateYmsTestItem'" label="Yms Test Item" show-overflow-tooltip>
          <template #default="scope">
            <el-switch :activeValue="scope.row.calculateYmsTestItem === 0" disabled />
          </template>
        </el-table-column>
        <el-table-column v-bind="item" v-else show-overflow-tooltip />
      </template>

      <el-table-column label="操作" fixed="right" width="120">
        <template #default="scope">
          <el-button text @click="editRow(scope.row)">编辑</el-button>
          <el-button type="danger" text @click="delRow(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </CommonTable>

    <FormDialog v-model:visible="visible" :getList="getList" :formData="currentRow" />
  </div>
</template>

<script setup lang="ts">
import { TABLE_HEADER } from './data';
import { CommonTable } from '@guwave/components';
import { useTable } from './hooks/useTable';
import FormDialog from './FormDialog.vue';
import { ListItem } from './typing';
import { deleteConfig, updateConfig } from '@/service/serviceManagement';
import { RESPONSE_SUCCESS_CODE } from '@/utils/http';
import { ElMessageBox } from 'element-plus';

const visible = ref<boolean>(false);
const currentRow = ref<ListItem | undefined>(undefined);
const { getList, tableData, pagination, loading } = useTable();

//  修改状态
const changeStatus = async (row: ListItem) => {
  const param = { ...row };
  param.status = param.status === 1 ? 0 : 1;

  try {
    const res = await updateConfig(param);

    if (res.data.code === RESPONSE_SUCCESS_CODE) {
      getList();
    }
  } catch (err) {
    console.error(err);
  }
};
//  新增行
const addRow = () => {
  currentRow.value = undefined;
  visible.value = true;
};
//  编辑行
const editRow = (row: ListItem) => {
  currentRow.value = row;
  visible.value = true;
};
//  删除行
const delRow = (row: ListItem) => {
  ElMessageBox.confirm('确认是否删除!', '警告', {
    type: 'warning',
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  }).then(async () => {
    try {
      const res = await deleteConfig(row.id);

      if (res.data.code === RESPONSE_SUCCESS_CODE) {
        getList();
      }
    } catch (err) {
      console.error(err);
    }
  });
};
</script>

<style lang="scss" scoped>
.servicePage {
  height: 99.5vh;
  padding: 8px;
  display: flex;
  flex-direction: column;

  :deep(.el-table .cell) {
    white-space: nowrap;
  }
}
</style>
