export interface ListParam {
  pageIndex: number;
  pageSize: number;
}
export interface ListRes {
  pageIndex: number;
  pageSize: number;
  total: number;
  data: ListItem[];
}
export interface ListItem {
  id: number;
  testArea: string;
  lotType: string;
  fileCategory: string;
  factory: string;
  factorySite: string;
  deviceId: string | null;
  testStage: string | null;
  calculateDwTestItem: number;
  calculateYmsTestItem: number;
  status: number;
  createTime: string;
  updateTime: string;
  createUser: string;
  updateUser: string;
}
