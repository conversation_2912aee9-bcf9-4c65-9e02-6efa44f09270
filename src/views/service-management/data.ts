import { Api, Rule } from '@form-create/element-ui';

export const TABLE_HEADER = [
  {
    prop: 'testArea',
    label: 'Test Area',
  },
  {
    prop: 'fileCategory',
    label: 'File Category',
  },
  {
    prop: 'lotType',
    label: 'Lot Type',
  },
  {
    prop: 'factory',
    label: 'Factory',
  },
  {
    prop: 'factorySite',
    label: 'Factory Site',
  },
  {
    prop: 'deviceId',
    label: 'Device Id',
  },
  {
    prop: 'testStage',
    label: 'Test Stage',
  },
  {
    prop: 'status',
    label: '状态',
  },
  {
    prop: 'calculateDwTestItem',
    label: 'Dw Test Item',
  },
  {
    prop: 'calculateYmsTestItem',
    label: 'Yms Test Item',
  },
  {
    prop: 'createTime',
    label: '创建时间',
  },
  {
    prop: 'updateTime',
    label: '更新时间',
  },
  {
    prop: 'createUser',
    label: '创建用户',
  },
  {
    prop: 'updateUser',
    label: '更新用户',
  },
];

export const RULE = [
  //  Test Area
  {
    type: 'select',
    title: 'Test Area',
    field: 'testArea',
    col: {
      span: 12,
    },
    props: {
      placeholder: '请选择',
    },
    validate: [{ required: true, message: '请选择' }],
    options: [
      { label: 'CP', value: 'CP' },
      { label: 'FT', value: 'FT' },
      { label: 'CP(Map)', value: 'CP(Map)' },
      { label: 'CP(InklessMap)', value: 'CP(InklessMap)' },
      { label: 'BUMP', value: 'BUMP' },
      { label: 'BUMP(Map)', value: 'BUMP(Map)' },
      { label: 'BURNIN', value: 'BURNIN' },
      { label: 'BURNIN(Map)', value: 'BURNIN(Map)' },
      { label: 'WLT', value: 'WLT' },
      { label: 'WLT(Map)', value: 'WLT(Map)' },
      { label: 'REL(Wafer)', value: 'REL(Wafer)' },
      { label: 'REL(Map)', value: 'REL(Map)' },
      { label: 'ASSY(Wafer)', value: 'ASSY(Wafer)' },
      { label: 'ASSY(Map)', value: 'ASSY(Map)' },
      { label: 'ASSY', value: 'ASSY' },
      { label: 'REL', value: 'REL' },
      { label: 'SLT', value: 'SLT' },
      { label: 'MT', value: 'MT' },
      { label: 'NA', value: 'NA' },
      { label: 'CAL', value: 'CAL' },
    ],
  },
  //  File Category
  {
    type: 'select',
    title: 'File Category',
    field: 'fileCategory',
    col: {
      span: 12,
    },
    props: {
      placeholder: '请选择',
    },
    validate: [{ required: true, message: '请选择' }],
    options: [
      { label: 'STDF', value: 'STDF' },
      { label: 'RAW_DATA', value: 'RAW_DATA' },
    ],
  },
  //  Factory
  {
    type: 'input',
    title: 'Factory',
    field: 'factory',
    col: {
      span: 12,
    },
    props: {
      placeholder: '请输入',
    },
    validate: [{ required: true, message: '请输入' }],
  },
  //  FactorySite
  {
    type: 'input',
    title: 'Factory Site',
    field: 'factorySite',
    col: {
      span: 12,
    },
    props: {
      placeholder: '请输入',
    },
    validate: [{ required: true, message: '请输入' }],
  },
  //  Lot Type
  {
    type: 'select',
    title: 'Lot Type',
    field: 'lotType',
    col: {
      span: 12,
    },
    props: {
      placeholder: '请选择',
    },
    validate: [{ required: true, message: '请选择' }],
    options: [
      { label: 'PRODUCTION', value: 'PRODUCTION' }, // 生产批次
      { label: 'ENGINEERING', value: 'ENGINEERING' }, // 工程批次
      { label: 'QUALIFICATION', value: 'QUALIFICATION' }, // 认证批次
      { label: 'EMPTY', value: 'EMPTY' }, // 空批次
    ],
  },
  //  Device Id
  {
    type: 'input',
    title: 'Device Id',
    field: 'deviceId',
    col: {
      span: 12,
    },
    props: {
      placeholder: '请输入',
    },
    validate: [{ required: false, message: '请输入' }],
  },
  //  Test Stage
  {
    type: 'input',
    title: 'Test Stage',
    field: 'testStage',
    col: {
      span: 12,
    },
    props: {
      placeholder: '请输入',
      clearable: true,
    },
  },
  {
    type: 'input',
    field: '_spacer',
    col: {
      span: 12,
    },
    className: 'form-spacer',
    props: {
      disabled: true,
    },
  },
  //  Dw Test Item
  {
    type: 'switch',
    title: 'Dw Test Item',
    field: 'calculateDwTestItem',
    col: {
      span: 12,
    },
    className: 'switch-divider',
    value: 1,
    props: {
      activeValue: 1,
      inactiveValue: 0,
    },
    update: (val: number, rule: Rule, api: Api) => {
      if (val === null) console.log(rule);

      if (val === 0) {
        api.setValue('calculateYmsTestItem', 0);
      }
    },
  },
  //  Yms Test Item
  {
    type: 'switch',
    title: 'Yms Test Item',
    field: 'calculateYmsTestItem',
    col: {
      span: 12,
    },
    className: 'switch-divider',
    value: 1,
    props: {
      activeValue: 1,
      inactiveValue: 0,
    },
    update: (val: number, rule: Rule, api: Api) => {
      if (val === null) console.log(rule);

      if (val === 1) {
        api.setValue('calculateDwTestItem', 1);
      }
    },
  },
];

export const OPTION = {
  submitBtn: false,
  col: {
    labelWidth: '130px',
  },
};
