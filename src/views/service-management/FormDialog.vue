<template>
  <el-dialog v-model="visible" title="业务计算管理配置" width="920px" @open="openFn">
    <FormCreate v-model:api="fApi" :rule="form.rule" :option="form.option" />

    <template #footer>
      <div class="btnbox">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="submit" :loading="loading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { useModel } from 'vue';
import { useForm } from './hooks/useForm';
import { ElMessage } from 'element-plus';
import { ListItem } from './typing';
import { RESPONSE_SUCCESS_CODE } from '@/utils/http';
import { addConfig, updateConfig } from '@/service/serviceManagement';
import { debounce } from 'lodash';

interface Props {
  visible: boolean;
  formData?: ListItem;
  getList: () => void;
}
const props = defineProps<Props>();
const visible = useModel(props, 'visible');

const loading = ref(false);
const { fApi, form, FormCreate } = useForm();

// 表单提交
const submit = debounce(
  async () => {
    fApi.value?.submit(async () => {
      const params: ListItem = fApi.value?.formData() as any;
      params.status = 1;
      const normalizedDevice = params.deviceId?.trim();
      params.deviceId = normalizedDevice ? normalizedDevice : null;
      const normalizedStage = params.testStage?.trim();
      params.testStage = normalizedStage ? normalizedStage : null;

      try {
        loading.value = true;
        const res = props.formData ? await updateConfig(params) : await addConfig(params);

        if (res.data.code === RESPONSE_SUCCESS_CODE) {
          ElMessage.success('执行成功!');
          visible.value = false;
          props.getList();
        } else {
          loading.value = true;
        }
      } catch (err) {
        loading.value = true;
        console.error(err);
      }
    });
  },
  500,
  { leading: true },
);

const openFn = () => {
  loading.value = false;

  if (props.formData) {
    const data = {
      ...props.formData,
    };

    fApi.value?.coverValue && fApi.value?.coverValue(data);
    // 编辑态禁用多个字段
    const disableFields = ['testArea', 'fileCategory', 'factory', 'factorySite', 'lotType', 'deviceId', 'testStage'];
    disableFields.forEach((field) => {
      fApi.value?.updateRule(field, {
        props: {
          disabled: true,
          placeholder: '请输入',
        },
      });
    });
  } else {
    // 新增态启用多个字段
    const disableFields = ['testArea', 'fileCategory', 'factory', 'factorySite', 'lotType', 'deviceId', 'testStage'];
    disableFields.forEach((field) => {
      fApi.value?.updateRule(field, {
        props: {
          disabled: false,
          placeholder: '请输入',
        },
      });
    });

    fApi.value?.resetFields && fApi.value?.resetFields();
    setTimeout(() => {
      fApi.value?.clearValidateState && fApi.value?.clearValidateState();
    });
  }
};
</script>

<style scoped lang="scss">
:deep(.el-row) {
  .el-col {
    margin-bottom: 16px;

    .el-select,
    .el-date-editor {
      width: 100%;
    }
  }
}
:deep(.el-input.is-disabled) {
  .el-input__wrapper {
    background: #f5f5f5 !important;
  }
}
:deep(.el-select .is-disabled) {
  background: #f5f5f5 !important;
}

:deep(.switch-divider) {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

:deep(.form-spacer .el-form-item__label),
:deep(.form-spacer .el-form-item__content) {
  display: none;
}

.btnbox {
  display: flex;

  .el-button {
    &:first-child {
      margin-left: auto;
    }
    &:last-child {
      margin-right: 16px;
    }
  }
}
</style>
