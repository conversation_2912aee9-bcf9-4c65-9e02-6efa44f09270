import { getWaferLotDropdown, getFileDropdown, getDrDropdown, getTaskDropdown } from '@/service/dataReplay';
import { SearchItem } from '@/types/common';
import { DE_ListItem, DR_ListItem, TaskListRes } from '@/types/dataReplay';
import { DataTableColumns, NButton } from 'naive-ui';

export enum ReRunEnum {
  REPROCESS_FILE = 'REPROCESS_FILE',
  REPROCESS_LOT = 'REPROCESS_LOT',
  DELETE_LOT = 'DELETE_LOT',
  DELETE_FILE = 'DELETE_FILE',
}
export enum StatusEnum {
  SUCCESS = '成功',
  FAIL = '失败',
  PROCESSING = '处理中',
  CREATE = '创建',
  CANCEL = '取消',
}
export enum FilterTypeEnum {
  FILE = 'FILE',
  LOTWAFER = 'LOTWAFER',
}

// --------------------------------------数据入库----------------------------------------
export const FILTER_TYPE_OPTIONS = [
  {
    label: 'File',
    value: FilterTypeEnum.FILE,
  },
  {
    label: 'Lot/Wafer',
    value: FilterTypeEnum.LOTWAFER,
  },
];
//  搜搜列表
export const DE_SEARCH_LIST_FILE: SearchItem[] = [
  {
    label: 'Factory',
    type: 'select',
    key: 'factoryList',
    multiple: true,
    required: true,
    api: getFileDropdown,
  },
  {
    label: 'File Name',
    type: 'input',
    key: 'fileName',
  },
  {
    label: 'Remote File Path',
    type: 'input',
    key: 'remoteFilePath',
  },
  {
    label: 'Test Area',
    type: 'select',
    key: 'testArea',
    required: true,
    options: [
      { label: 'CP', value: 'CP' },
      { label: 'FT', value: 'FT' },
      { label: 'CP(Map)', value: 'CP(Map)' },
      { label: 'CP(InklessMap)', value: 'CP(InklessMap)' },
      { label: 'BUMP', value: 'BUMP' },
      { label: 'BUMP(Map)', value: 'BUMP(Map)' },
      { label: 'BURNIN', value: 'BURNIN' },
      { label: 'BURNIN(Map)', value: 'BURNIN(Map)' },
      { label: 'WLT', value: 'WLT' },
      { label: 'WLT(Map)', value: 'WLT(Map)' },
      { label: 'REL(Wafer)', value: 'REL(Wafer)' },
      { label: 'REL(Map)', value: 'REL(Map)' },
      { label: 'ASSY(Wafer)', value: 'ASSY(Wafer)' },
      { label: 'ASSY(Map)', value: 'ASSY(Map)' },
      { label: 'ASSY', value: 'ASSY' },
      { label: 'REL', value: 'REL' },
      { label: 'SLT', value: 'SLT' },
      { label: 'MT', value: 'MT' },
      { label: 'NA', value: 'NA' },
      { label: 'CAL', value: 'CAL' },
    ],
  },
  {
    label: 'File Category',
    type: 'select',
    required: true,
    key: 'fileCategory',
    options: [
      {
        label: 'STDF',
        value: 'STDF',
      },
      {
        label: 'RAW_DATA',
        value: 'RAW_DATA',
      },
      {
        label: 'BIT_MEM',
        value: 'BIT_MEM',
      },
      {
        label: 'WAT',
        value: 'WAT',
      },
      {
        label: 'SUMMARY',
        value: 'SUMMARY',
      },
    ],
  },
  {
    label: 'Step',
    type: 'select',
    key: 'stepList',
    multiple: true,
    api: getWaferLotDropdown,
  },
  {
    label: 'Process Status',
    type: 'select',
    key: 'processStatusList',
    multiple: true,
    options: [
      {
        label: 'FAIL',
        value: 'FAIL',
      },
      {
        label: 'SUCCESS',
        value: 'SUCCESS',
      },
      {
        label: 'CREATE',
        value: 'CREATE',
      },
      {
        label: 'PROCESSING',
        value: 'PROCESSING',
      },
      {
        label: 'CANCEL',
        value: 'CANCEL',
      },
    ],
  },
  {
    label: 'Exception Message',
    type: 'select',
    key: 'exceptionMessage',
    api: getWaferLotDropdown,
  },
  {
    dateKey: 'createTimeDateKey',
    key: 'createTime',
    label: 'Create Time',
    type: 'customDatePicker',
  },
  {
    dateKey: 'updateTimeDateKey',
    key: 'updateTime',
    label: 'Update Time',
    type: 'customDatePicker',
  },
];
export const DE_SEARCH_LIST_LOTWAFER: SearchItem[] = [
  {
    label: 'Factory',
    type: 'select',
    key: 'factoryList',
    multiple: true,
    required: true,
    api: getWaferLotDropdown,
  },
  {
    label: 'Test Area',
    type: 'select',
    key: 'testArea',
    required: true,
    options: [
      { label: 'CP', value: 'CP' },
      { label: 'FT', value: 'FT' },
      { label: 'CP(Map)', value: 'CP(Map)' },
      { label: 'CP(InklessMap)', value: 'CP(InklessMap)' },
      { label: 'BUMP', value: 'BUMP' },
      { label: 'BUMP(Map)', value: 'BUMP(Map)' },
      { label: 'BURNIN', value: 'BURNIN' },
      { label: 'BURNIN(Map)', value: 'BURNIN(Map)' },
      { label: 'WLT', value: 'WLT' },
      { label: 'WLT(Map)', value: 'WLT(Map)' },
      { label: 'REL(Wafer)', value: 'REL(Wafer)' },
      { label: 'REL(Map)', value: 'REL(Map)' },
      { label: 'ASSY(Wafer)', value: 'ASSY(Wafer)' },
      { label: 'ASSY(Map)', value: 'ASSY(Map)' },
      { label: 'ASSY', value: 'ASSY' },
      { label: 'REL', value: 'REL' },
      { label: 'SLT', value: 'SLT' },
      { label: 'MT', value: 'MT' },
      { label: 'NA', value: 'NA' },
      { label: 'CAL', value: 'CAL' },
    ],
  },
  {
    label: 'File Category',
    type: 'select',
    key: 'fileCategory',
    required: true,
    options: [
      {
        label: 'STDF',
        value: 'STDF',
      },
      {
        label: 'RAW_DATA',
        value: 'RAW_DATA',
      },
      {
        label: 'BIT_MEM',
        value: 'BIT_MEM',
      },
      {
        label: 'WAT',
        value: 'WAT',
      },
      {
        label: 'SUMMARY',
        value: 'SUMMARY',
      },
    ],
  },
  {
    label: 'Device ID',
    type: 'select',
    key: 'deviceIdList',
    multiple: true,
    api: getWaferLotDropdown,
  },
  {
    label: 'Lot Type',
    type: 'select',
    key: 'lotType',
    options: [
      { label: 'PRODUCTION', value: 'PRODUCTION' },
      { label: 'ENGINEERING', value: 'ENGINEERING' },
      { label: 'QUALIFICATION', value: 'QUALIFICATION' },
      { label: 'EMPTY', value: 'EMPTY' },
    ],
  },
  {
    label: 'Test Stage',
    type: 'select',
    key: 'testStageList',
    multiple: true,
    api: getWaferLotDropdown,
  },
  {
    label: 'Test Program',
    type: 'select',
    key: 'testProgramList',
    multiple: true,
    api: getWaferLotDropdown,
  },
  {
    label: 'Lot Id',
    type: 'select',
    key: 'lotIdList',
    multiple: true,
    api: getWaferLotDropdown,
  },
  {
    label: 'Wafer No',
    type: 'select',
    key: 'waferNoList',
    multiple: true,
    api: getWaferLotDropdown,
  },
  {
    label: 'Sublot ID',
    type: 'select',
    key: 'sblotIdList',
    multiple: true,
    api: getWaferLotDropdown,
  },
  {
    label: 'Step',
    type: 'select',
    key: 'stepList',
    multiple: true,
    api: getWaferLotDropdown,
  },
  {
    label: 'Process Status',
    type: 'select',
    key: 'processStatusList',
    multiple: true,
    options: [
      {
        label: 'FAIL',
        value: 'FAIL',
      },
      {
        label: 'SUCCESS',
        value: 'SUCCESS',
      },
      {
        label: 'CREATE',
        value: 'CREATE',
      },
      {
        label: 'PROCESSING',
        value: 'PROCESSING',
      },
      {
        label: 'CANCEL',
        value: 'CANCEL',
      },
    ],
  },
  {
    label: 'Exception Message',
    type: 'select',
    key: 'exceptionMessage',
    api: getWaferLotDropdown,
  },
  {
    dateKey: 'createTimeDateKey',
    key: 'createTime',
    label: 'Create Time',
    type: 'customDatePicker',
  },
  {
    dateKey: 'updateTimeDateKey',
    key: 'updateTime',
    label: 'Update Time',
    type: 'customDatePicker',
  },
];
//  表头
export const DE_COLUMNS: (
  reRun: (row: DE_ListItem, key: ReRunEnum) => void,
  deSelectedIds: Ref<number[]>,
  openProcessDetail: (row: DE_ListItem) => void,
) => DataTableColumns<DE_ListItem> = (reRun, deSelectedIds, openProcessDetail) => {
  return [
    {
      type: 'selection',
      fixed: 'left',
      width: 60,
      options: [
        {
          label: '全选',
          key: 'all',
          onSelect: (pageData) => {
            deSelectedIds.value = pageData?.map((item) => item.id);
          },
        },
        {
          label: '反选',
          key: 'rever',
          onSelect: (pageData) => {
            const ids: number[] = [];
            pageData.map((item) => {
              if (!deSelectedIds.value?.some((id) => id === item.id)) {
                ids.push(item.id);
              }
            });

            deSelectedIds.value = ids;
          },
        },
        {
          label: '全不选',
          key: 'none',
          onSelect: () => {
            deSelectedIds.value = [];
          },
        },
      ],
    },
    {
      title: 'Test Area',
      key: 'testArea',
      resizable: true,
      width: 160,
    },
    {
      title: 'Factory',
      key: 'factory',
      resizable: true,
      width: 160,
    },
    {
      title: 'File Category',
      key: 'fileCategory',
      resizable: true,
      width: 160,
    },
    {
      title: 'Device ID',
      key: 'deviceId',
      resizable: true,
      width: 160,
    },
    {
      title: 'Lot Type',
      key: 'lotType',
      resizable: true,
      width: 160,
    },
    {
      title: 'Test Stage',
      key: 'testStage',
      resizable: true,
      width: 160,
    },
    {
      title: 'Test Program',
      key: 'testProgram',
      resizable: true,
      width: 160,
    },
    {
      title: 'Lot ID',
      key: 'lotId',
      resizable: true,
      width: 160,
    },
    {
      title: 'Wafer No',
      key: 'waferNo',
      resizable: true,
      width: 160,
    },
    {
      title: 'SbLot ID',
      key: 'sblotId',
      resizable: true,
      width: 160,
    },
    {
      title: 'File Name',
      key: 'fileName',
      resizable: true,
      width: 160,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: 'FTP Path',
      key: 'ftpPath',
      resizable: true,
      width: 180,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: 'Exception Message',
      key: 'exceptionMessage',
      resizable: true,
      width: 180,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: 'Step',
      key: 'step',
      resizable: true,
      width: 160,
    },
    {
      title: 'Process Status',
      key: 'status',
      resizable: true,
      width: 160,
      render: (rowData) => {
        if (rowData?.status === StatusEnum.SUCCESS) {
          return <span style={{ color: 'rgb(25,160,88)' }}>SUCCESS</span>;
        } else if (rowData?.status === StatusEnum.PROCESSING) {
          return <span style={{ color: 'rgb(240,160,32)' }}>PROCESSING</span>;
        } else if (rowData?.status === StatusEnum.FAIL) {
          return <span style={{ color: 'rgb(208,48,80)' }}>FAIL</span>;
        } else {
          return <span>CREATE</span>;
        }
      },
    },
    {
      title: 'Create Time',
      key: 'createTime',
      resizable: true,
      width: 160,
    },
    {
      title: 'Update Time',
      key: 'updateTime',
      resizable: true,
      width: 160,
    },
    {
      title: '操作',
      key: 'tool',
      width: 240,
      fixed: 'right',
      render: (rowData) => {
        return (
          <>
            <NButton
              type="info"
              text
              style={{ marginRight: '8px' }}
              onClick={() => reRun(rowData, ReRunEnum.REPROCESS_FILE)}
              disabled={rowData.status !== StatusEnum.SUCCESS && rowData.status !== StatusEnum.FAIL}
            >
              重试
            </NButton>
            <NButton
              type="error"
              text
              style={{ marginRight: '8px' }}
              onClick={() => reRun(rowData, ReRunEnum.DELETE_FILE)}
              disabled={rowData.status !== StatusEnum.SUCCESS && rowData.status !== StatusEnum.FAIL}
            >
              删除
            </NButton>
            <NButton
              type="info"
              text
              style={{ marginRight: '8px', color: '#2080f0' }}
              onClick={() => openProcessDetail(rowData)}
            >
              详情
            </NButton>
          </>
        );
      },
    },
  ];
};
//  批量操作列表
export const DE_DROPDOWN_OPTIONS = [
  {
    label: '批量删除',
    key: ReRunEnum.DELETE_LOT,
  },
  {
    label: '批量重试',
    key: ReRunEnum.REPROCESS_LOT,
  },
];

// --------------------------------------数据重播----------------------------------------
//  搜索列表
export const DR_SEARCH_LIST: SearchItem[] = [
  {
    label: 'Factory',
    type: 'select',
    key: 'factoryList',
    multiple: true,
    required: true,
    api: getDrDropdown,
  },
  {
    label: 'Test Area',
    type: 'select',
    key: 'testArea',
    required: true,
    options: [
      { label: 'CP', value: 'CP' },
      { label: 'FT', value: 'FT' },
      { label: 'CP(Map)', value: 'CP(Map)' },
      { label: 'CP(InklessMap)', value: 'CP(InklessMap)' },
      { label: 'BUMP', value: 'BUMP' },
      { label: 'BUMP(Map)', value: 'BUMP(Map)' },
      { label: 'BURNIN', value: 'BURNIN' },
      { label: 'BURNIN(Map)', value: 'BURNIN(Map)' },
      { label: 'WLT', value: 'WLT' },
      { label: 'WLT(Map)', value: 'WLT(Map)' },
      { label: 'REL(Wafer)', value: 'REL(Wafer)' },
      { label: 'REL(Map)', value: 'REL(Map)' },
      { label: 'ASSY(Wafer)', value: 'ASSY(Wafer)' },
      { label: 'ASSY(Map)', value: 'ASSY(Map)' },
      { label: 'ASSY', value: 'ASSY' },
      { label: 'REL', value: 'REL' },
      { label: 'SLT', value: 'SLT' },
      { label: 'MT', value: 'MT' },
      { label: 'NA', value: 'NA' },
      { label: 'CAL', value: 'CAL' },
    ],
  },
  {
    label: 'File Category',
    type: 'select',
    key: 'fileCategory',
    options: [
      {
        label: 'STDF',
        value: 'STDF',
      },
      {
        label: 'RAW_DATA',
        value: 'RAW_DATA',
      },
      {
        label: 'BIT_MEM',
        value: 'BIT_MEM',
      },
      {
        label: 'WAT',
        value: 'WAT',
      },
      {
        label: 'SUMMARY',
        value: 'SUMMARY',
      },
    ],
  },
  {
    label: 'Device ID',
    type: 'select',
    key: 'deviceIdList',
    multiple: true,
    api: getDrDropdown,
  },
  {
    label: 'Lot Type',
    type: 'select',
    key: 'lotType',
    options: [
      { label: 'PRODUCTION', value: 'PRODUCTION' },
      { label: 'ENGINEERING', value: 'ENGINEERING' },
      { label: 'QUALIFICATION', value: 'QUALIFICATION' },
      { label: 'EMPTY', value: 'EMPTY' },
    ],
  },
  {
    label: 'Test Stage',
    type: 'select',
    key: 'testStageList',
    multiple: true,
    api: getDrDropdown,
  },
  {
    label: 'Test Program',
    type: 'select',
    key: 'testProgramList',
    multiple: true,
    api: getDrDropdown,
  },
  {
    label: 'Lot Id',
    type: 'select',
    key: 'lotIdList',
    multiple: true,
    api: getDrDropdown,
  },
  {
    label: 'Wafer No',
    type: 'select',
    key: 'waferNoList',
    multiple: true,
    api: getDrDropdown,
  },
  {
    label: 'Sublot ID',
    type: 'select',
    key: 'sblotIdList',
    multiple: true,
    api: getDrDropdown,
  },
  {
    label: 'Step',
    type: 'select',
    key: 'stepList',
    multiple: true,
    api: getDrDropdown,
  },
  {
    label: 'Filter Process Status',
    type: 'select',
    key: 'filterStatusList',
    multiple: true,
    api: getDrDropdown,
  },
  {
    label: 'Process Status',
    type: 'select',
    key: 'processStatusList',
    multiple: true,
    options: [
      {
        label: 'FAIL',
        value: 'FAIL',
      },
      {
        label: 'SUCCESS',
        value: 'SUCCESS',
      },
      {
        label: 'CREATE',
        value: 'CREATE',
      },
      {
        label: 'PROCESSING',
        value: 'PROCESSING',
      },
      {
        label: 'CANCEL',
        value: 'CANCEL',
      },
    ],
  },
  {
    label: 'Replay Type',
    type: 'select',
    key: 'replayTypeList',
    multiple: true,
    api: getDrDropdown,
  },
  {
    label: 'Exception Message',
    type: 'select',
    key: 'exceptionMessageList',
    multiple: true,
    api: getDrDropdown,
  },
  {
    label: 'File Name',
    type: 'input',
    key: 'fileName',
  },
  {
    dateKey: 'filtrCreateTimeDateKey',
    key: 'filterCreateTime',
    label: 'Filter Create Time',
    type: 'customDatePicker',
  },
  {
    dateKey: 'filterUpdateTimeDateKey',
    key: 'filterUpdateTime',
    label: 'Filter Update Time',
    type: 'customDatePicker',
  },
  {
    dateKey: 'createTimeDateKey',
    key: 'createTime',
    label: 'Create Time',
    type: 'customDatePicker',
  },
  {
    dateKey: 'updateTimeDateKey',
    key: 'updateTime',
    label: 'Update Time',
    type: 'customDatePicker',
  },
];
//  表头
export const DR_COLUMNS: (
  openTask: (row: DR_ListItem) => void,
  retry: (id: number) => void,
  cancel: (id: number) => void,
  top: (id: number) => void,
) => DataTableColumns<DR_ListItem> = (openTask, retry, cancel, top) => [
  {
    title: 'Test Area',
    key: 'testArea',
    resizable: true,
    width: 160,
  },
  {
    title: 'Factory',
    key: 'factory',
    resizable: true,
    width: 160,
  },
  {
    title: 'File Category',
    key: 'fileCategory',
    resizable: true,
    width: 160,
  },
  {
    title: 'Device ID',
    key: 'deviceId',
    resizable: true,
    width: 160,
  },
  {
    title: 'Lot Type',
    key: 'lotType',
    resizable: true,
    width: 160,
  },
  {
    title: 'Test Stage',
    key: 'testStage',
    resizable: true,
    width: 160,
  },
  {
    title: 'Test Program',
    key: 'testProgram',
    resizable: true,
    width: 160,
  },
  {
    title: 'Lot Id',
    key: 'lotId',
    resizable: true,
    width: 160,
  },
  {
    title: 'Wafer No',
    key: 'waferNo',
    resizable: true,
    width: 160,
  },
  {
    title: 'SbLot ID',
    key: 'sblotId',
    resizable: true,
    width: 160,
  },
  {
    title: 'File Name',
    key: 'fileName',
    resizable: true,
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: 'FTP Path',
    key: 'ftpPath',
    resizable: true,
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: 'Exception Message',
    key: 'exceptionMessage',
    resizable: true,
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: 'Replay Type',
    key: 'replayType',
    resizable: true,
    width: 160,
  },
  {
    title: 'Filter Process Status',
    key: 'filterProcessStatus',
    resizable: true,
    width: 180,
  },
  {
    title: 'Filter Create Time',
    key: 'filterCreateTime',
    resizable: true,
    width: 160,
  },
  {
    title: 'Filter Update Time',
    key: 'filterUpdateTime',
    resizable: true,
    width: 160,
  },
  {
    title: 'Step',
    key: 'step',
    resizable: true,
    width: 160,
  },
  {
    title: 'Process Status',
    key: 'status',
    resizable: true,
    width: 160,
    render: (rowData) => {
      if (rowData?.status === StatusEnum.SUCCESS) {
        return <span style={{ color: 'rgb(25,160,88)' }}>SUCCESS</span>;
      } else if (rowData?.status === StatusEnum.PROCESSING) {
        return <span style={{ color: 'rgb(240,160,32)' }}>PROCESSING</span>;
      } else if (rowData?.status === StatusEnum.FAIL) {
        return <span style={{ color: 'rgb(208,48,80)' }}>FAIL</span>;
      } else if (rowData?.status === StatusEnum.CANCEL) {
        return <span style={{ color: '#ccc' }}>CANCEL</span>;
      } else {
        return <span>CREATE</span>;
      }
    },
  },
  {
    title: 'Create Time',
    key: 'createTime',
    resizable: true,
    width: 160,
  },
  {
    title: 'Update Time',
    key: 'updateTime',
    resizable: true,
    width: 160,
  },
  {
    title: 'Replay Comments',
    key: 'replayComments',
    resizable: true,
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: 'Fail/Success/Processing',
    key: 'successTotal',
    resizable: true,
    width: 220,
    fixed: 'right',
    render: (rowData) => {
      const total = rowData?.totalTaskCnt ?? 0;
      const success = rowData?.successTaskCnt ?? 0;
      const fail = rowData?.failTaskCnt ?? 0;
      const other = total - success - fail;
      const status = rowData.status;

      const width = () => {
        if (success > 0 && fail > 0 && other > 0) {
          return '33.33333%';
        } else if (!success && !fail && !other) {
          return 0;
        } else if (
          (success > 0 && !fail && !other) ||
          (fail > 0 && !success && !other) ||
          (other > 0 && !success && !fail)
        ) {
          return '100%';
        } else {
          return '50%';
        }
      };

      const processingDom = (
        <div class="paginationBox h">
          <div class="h" style={{ backgroundColor: 'rgb(208,48,80)', width: fail > 0 ? width() : 0 }}>
            <span>{fail}</span>
          </div>

          <div class="h" style={{ backgroundColor: 'rgb(25,160,88)', width: success > 0 ? width() : 0 }}>
            <span>{success}</span>
          </div>

          <div class="h" style={{ backgroundColor: 'rgb(240,160,32)', width: other > 0 ? width() : 0 }}>
            <span>{other}</span>
          </div>
        </div>
      );
      const cancelDom = (
        <div class="paginationBox h">
          <div class="h" style={{ backgroundColor: '#ccc', width: '100%' }}>
            <span></span>
          </div>
        </div>
      );
      const createDom = (
        <div class="paginationBox h">
          <div class="h" style={{ backgroundColor: '#fff', width: '100%' }}>
            <span></span>
          </div>
        </div>
      );

      const domControl = () => {
        if (status === StatusEnum.PROCESSING || status === StatusEnum.FAIL || status === StatusEnum.SUCCESS) {
          return processingDom;
        } else if (status === StatusEnum.CANCEL) {
          return cancelDom;
        } else {
          return createDom;
        }
      };

      return domControl();
    },
  },
  {
    title: '操作',
    key: 'tool',
    width: 240,
    fixed: 'right',
    render: (rowData) => {
      return (
        <>
          <NButton type="info" text onClick={() => openTask(rowData)}>
            查看
          </NButton>
          <NButton
            type="info"
            text
            style={{ margin: '0 16px' }}
            disabled={rowData?.failTaskCnt < 1}
            onClick={() => retry(rowData.id)}
            loading={rowData.loading}
          >
            重试
          </NButton>
          <NButton type="info" text disabled={rowData.status !== StatusEnum.CREATE} onClick={() => cancel(rowData.id)}>
            取消
          </NButton>
          <NButton
            type="info"
            text
            onClick={() => top(rowData.id)}
            disabled={rowData.status !== StatusEnum.CREATE}
            style={{ marginLeft: '8px' }}
          >
            置顶
          </NButton>
        </>
      );
    },
  },
];

// --------------------------------------任务记录弹窗----------------------------------------
export const TASK_SEARCH_LIST: SearchItem[] = [
  {
    label: 'Lot Id',
    type: 'select',
    key: 'lotIdList',
    multiple: true,
    api: getTaskDropdown,
  },
  {
    label: 'Wafer No',
    type: 'select',
    key: 'waferNoList',
    multiple: true,
    api: getTaskDropdown,
  },
  {
    label: 'Test Stage',
    type: 'select',
    key: 'testStageList',
    multiple: true,
    api: getTaskDropdown,
  },
  {
    label: 'Status',
    type: 'select',
    key: 'statusList',
    multiple: true,
    options: [
      {
        label: 'SUCCESS',
        value: 'SUCCESS',
      },
      {
        label: 'FAIL',
        value: 'FAIL',
      },
      {
        label: 'PROCESSING',
        value: 'PROCESSING',
      },
      {
        label: 'CREATE',
        value: 'CREATE',
      },
    ],
  },
  {
    label: 'Exception Message',
    type: 'select',
    key: 'exceptionMessageList',
    api: getTaskDropdown,
    multiple: true,
  },
];
//  表头
export const TASK_COLUMNS: () => DataTableColumns<TaskListRes> = () => [
  {
    title: 'Test Area',
    key: 'testArea',
    resizable: true,
    width: 160,
  },
  {
    title: 'Factory',
    key: 'factory',
    resizable: true,
    width: 160,
  },
  {
    title: 'Device ID',
    key: 'deviceId',
    resizable: true,
    width: 160,
  },
  {
    title: 'File Category',
    key: 'fileCategory',
    resizable: true,
    width: 160,
  },
  {
    title: 'Lot Id',
    key: 'lotId',
    resizable: true,
    width: 160,
  },
  {
    title: 'Wafer No',
    key: 'waferNo',
    resizable: true,
    width: 160,
  },
  {
    title: 'Lot Type',
    key: 'lotType',
    resizable: true,
    width: 160,
  },
  {
    title: 'Test Stage',
    key: 'testStage',
    resizable: true,
    width: 160,
  },
  {
    title: 'Replay Type',
    key: 'replayType',
    resizable: true,
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: 'Step',
    key: 'step',
    resizable: true,
    width: 160,
  },
  {
    title: 'File List',
    key: 'fileList',
    resizable: true,
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: 'Exception Message',
    key: 'exception',
    resizable: true,
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: 'Error Message',
    key: 'errorMessage',
    resizable: true,
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: 'Status',
    key: 'status',
    resizable: true,
    width: 160,
    render: (rowData) => {
      if (rowData?.status === StatusEnum.SUCCESS) {
        return <span style={{ color: 'rgb(25,160,88)' }}>SUCCESS</span>;
      } else if (rowData?.status === StatusEnum.PROCESSING) {
        return <span style={{ color: 'rgb(240,160,32)' }}>PROCESSING</span>;
      } else if (rowData?.status === StatusEnum.FAIL) {
        return <span style={{ color: 'rgb(208,48,80)' }}>FAIL</span>;
      } else {
        return <span>CREATE</span>;
      }
    },
  },
  {
    title: 'Create Time',
    key: 'createTime',
    resizable: true,
    width: 160,
  },
  {
    title: 'Update Time',
    key: 'updateTime',
    resizable: true,
    width: 160,
  },
];
