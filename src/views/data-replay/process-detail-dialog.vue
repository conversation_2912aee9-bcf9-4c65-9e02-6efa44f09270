<template>
  <n-modal
    v-model:show="visible"
    style="width: 900px"
    :mask-closable="false"
    preset="card"
    title="文件入库流程"
    @close="handleClose"
  >
    <n-spin :show="loading">
      <div v-if="fileInfo" class="process-detail">
        <!-- 文件名信息 -->
        <div class="file-name-section">
          <span class="file-name-text">
            文件名: {{ fileInfo.fileName }}
          </span>
        </div>
        
        <!-- 文件基本信息 -->
        <div class="file-info-section">
          <div class="infoBox h">
            <div class="item h">
              <span>Test Area</span>
              <n-input v-model:value="fileInfo.testArea" disabled placeholder="" />
            </div>
            <div class="item h">
              <span>Factory</span>
              <n-input v-model:value="fileInfo.factory" disabled placeholder="" />
            </div>
            <div class="item h">
              <span>File Category</span>
              <n-input v-model:value="fileInfo.fileCategory" disabled placeholder="" />
            </div>
            <div class="item h">
              <span>Device ID</span>
              <n-input v-model:value="fileInfo.deviceId" disabled placeholder="" />
            </div>
            <div class="item h">
              <span>Test Stage</span>
              <n-input v-model:value="fileInfo.testStage" disabled placeholder="" />
            </div>
            <div class="item h">
              <span>Lot ID</span>
              <n-input v-model:value="fileInfo.lotId" disabled placeholder="" />
            </div>
            <div class="item h">
              <span>Wafer No</span>
              <n-input v-model:value="fileInfo.waferNo" disabled placeholder="" />
            </div>
            <div class="item h">
              <span>Lot Type</span>
              <n-input v-model:value="fileInfo.lotType" disabled placeholder="" />
            </div>
            <div class="item h">
              <span>Process Status</span>
              <n-input 
                v-model:value="fileInfo.processStatus" 
                disabled 
                placeholder=""
              />
            </div>
          </div>
        </div>

        <!-- 流程步骤时间轴 -->
        <div class="timeline-section">
          <div class="section-title">入库流程步骤</div>
          <n-timeline>
            <n-timeline-item
              v-for="step in processSteps"
              :key="step.step"
              :type="getTimelineType(step.stepProcessStatus)"
              :title="step.stepName"
            >
              <template #icon>
                <div v-if="step.stepProcessStatus === 'SUCCESS'" 
                     style="width: 16px; height: 16px; border-radius: 50%; background: #18a058; color: white; display: flex; align-items: center; justify-content: center; font-size: 12px;">
                  ✓
                </div>
                <div v-else-if="step.stepProcessStatus === 'FAIL'" 
                     style="width: 16px; height: 16px; border-radius: 50%; background: #d03050; color: white; display: flex; align-items: center; justify-content: center; font-size: 12px;">
                  ✕
                </div>
                <div v-else-if="step.stepProcessStatus === 'PROCESSING'" 
                     style="width: 16px; height: 16px; border-radius: 50%; background: #2080f0; color: white; display: flex; align-items: center; justify-content: center; font-size: 12px;">
                  ⏳
                </div>
                <div v-else 
                     style="width: 16px; height: 16px; border-radius: 50%; background: #c2c2c2; color: white; display: flex; align-items: center; justify-content: center; font-size: 12px;">
                  ○
                </div>
              </template>
              
              <div class="step-content">
                <div class="step-header">
                  <span class="step-code">步骤 {{ step.step }}</span>
                  <n-tag :type="getTagType(step.stepProcessStatus)" size="small">
                    {{ getStatusText(step.stepProcessStatus) }}
                  </n-tag>
                </div>
                
                <div class="step-details" v-if="step.startTime || step.endTime || (step.stepProcessStatus === 'CREATE' && step.updateTime)">
                  <div class="time-info">
                    <span v-if="step.startTime">开始: {{ formatTime(step.startTime) }}</span>
                    <span v-if="step.endTime">结束: {{ formatTime(step.endTime) }}</span>
                    <span v-if="step.stepProcessStatus === 'CREATE' && step.updateTime">创建: {{ formatTime(step.updateTime) }}</span>
                    <span v-if="step.duration" class="duration">
                      耗时: {{ formatDuration(step.duration) }}
                    </span>
                  </div>
                </div>
                
                <div v-if="step.errorMessage" class="error-message">
                  <n-alert type="error" :show-icon="false">
                    {{ step.errorMessage }}
                  </n-alert>
                </div>
                
                <div v-if="step.details" class="step-extra-details">
                  <n-collapse>
                    <n-collapse-item title="详细信息" name="details">
                      <pre class="details-content">{{ step.details }}</pre>
                    </n-collapse-item>
                  </n-collapse>
                </div>
              </div>
            </n-timeline-item>
          </n-timeline>
        </div>
      </div>
    </n-spin>
    
    <template #action>
      <n-space justify="end">
        <n-button @click="handleRefresh" :loading="loading">刷新</n-button>
        <n-button type="primary" @click="handleClose">关闭</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
// 使用简单的文本符号作为图标

// 定义流程步骤数据类型（适配新的后端接口）
interface ProcessStep {
  step: number;  // 原stepCode
  stepName: string;
  stepProcessStatus: string;  // 原stepStatus
  startTime?: string;
  endTime?: string;
  updateTime?: string;
  duration?: number;
  errorMessage?: string;
  details?: string;
}

interface FileProcessInfo {
  fileName: string;
  testArea: string;
  factory: string;
  deviceId: string;
  fileCategory: string;
  lotId: string;
  waferNo: string;
  lotType: string;
  testStage: string;
  processStatus: string;
}

const props = defineProps<{
  visible: boolean;
  fileId?: number;
  fileData?: DE_ListItem;
}>();

const emits = defineEmits(['update:visible']);
const visible = useVModel(props, 'visible', emits);

const loading = ref(false);
const fileInfo = ref<FileProcessInfo | null>(null);
const processSteps = ref<ProcessStep[]>([]);

// 从传递的数据初始化文件信息（作为默认值）
watchEffect(() => {
  if (props.fileData && !fileInfo.value) {
    fileInfo.value = {
      fileName: props.fileData.fileName || '',
      testArea: props.fileData.testArea || '',
      factory: props.fileData.factory || '',
      deviceId: props.fileData.deviceId || '',
      fileCategory: props.fileData.fileCategory || '',
      lotId: props.fileData.lotId || '',
      waferNo: props.fileData.waferNo || '',
      lotType: props.fileData.lotType || '',
      testStage: props.fileData.testStage || '',
      processStatus: props.fileData.status || ''
    };
  }
});

// 步骤名称映射（后端返回stepCode，前端维护显示话术）
const stepNameMap: Record<number, string> = {
  1000: '文件从ftp下载',
  2200: '文件转换',
  3100: '文件预解析', 
  4100: '文件解析到ods',
  5100: 'dwd层计算',
  6100: 'dws层计算',
  7100: 'ads层计算'
};

// 预定的步骤顺序
const predefinedSteps = [1000, 2200, 3100, 4100, 5100, 6100, 7100];

// 状态显示话术映射
const statusDisplayMap: Record<string, string> = {
  'SUCCESS': '成功',
  'FAIL': '失败', 
  'PROCESSING': '处理中',
  'CREATE': '已创建',
  'CANCEL': '已取消'
};

// 获取时间轴类型
const getTimelineType = (stepStatus: string) => {
  switch (stepStatus) {
    case 'SUCCESS': return 'success';
    case 'FAIL': return 'error';
    case 'PROCESSING': return 'info';
    default: return 'default';
  }
};

// 获取标签类型
const getTagType = (stepStatus: string) => {
  switch (stepStatus) {
    case 'SUCCESS': return 'success';
    case 'FAIL': return 'error';
    case 'PROCESSING': return 'info';
    default: return 'default';
  }
};

// 获取状态文本（使用映射表）
const getStatusText = (stepStatus: string) => {
  return statusDisplayMap[stepStatus] || '待处理';
};



// 格式化时间
const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString('zh-CN');
};

// 格式化耗时
const formatDuration = (seconds: number) => {
  if (seconds < 60) {
    return `${seconds}秒`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}分${remainingSeconds}秒`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}小时${minutes}分钟`;
  }
};

// 获取流程步骤数据
const fetchProcessSteps = async () => {
  if (!props.fileId) return;
  
  loading.value = true;
  try {
    // 调用API接口获取流程步骤
    const { getFileProcessDetail } = await import('@/service/dataReplay');
    const res = await getFileProcessDetail(props.fileId);
    
    if (res.data.code === "00000001") {
      const data = res.data.data;
      // 过滤并按照预定顺序展示步骤
      processSteps.value = predefinedSteps
        .map(stepCode => {
          const step = data.fileProcessStepList.find(s => s.step === stepCode);
          if (step) {
            return {
              ...step,
              stepName: stepNameMap[step.step] || `步骤 ${step.step}`
            };
          }
          return null;
        })
        .filter(Boolean) as ProcessStep[];
      
      // 更新文件信息
      if (fileInfo.value) {
        fileInfo.value.fileName = data.fileName || '';
        fileInfo.value.testArea = data.testArea || '';
        fileInfo.value.factory = data.factory || '';
        fileInfo.value.deviceId = data.deviceId || '';
        fileInfo.value.fileCategory = data.fileCategory || '';
        fileInfo.value.lotId = data.lotId || '';
        fileInfo.value.waferNo = data.waferNo || '';
        fileInfo.value.lotType = data.lotType || '';
        fileInfo.value.testStage = data.testStage || '';
        fileInfo.value.processStatus = data.processStatus || '';
      }
    }
  } catch (error) {
    console.error('获取流程步骤失败:', error);
    // 移除模拟数据，避免影响数据正确性判断
    processSteps.value = [];
  } finally {
    loading.value = false;
  }
};

// 刷新数据
const handleRefresh = () => {
  fetchProcessSteps();
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
  fileInfo.value = null;
  processSteps.value = [];
};

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal && props.fileId) {
    fetchProcessSteps();
  }
});
</script>

<style scoped lang="scss">
.process-detail {
  
  .file-name-section {
    margin-bottom: 16px;
    
    .file-name-text {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }
  
  .file-info-section {
    margin-bottom: 24px;
    
    .infoBox {
      width: 100%;
      flex-wrap: wrap;
      
      .item {
        margin: 8px 8px 8px 0;
        
        > span {
          width: 113px;
          flex-shrink: 0;
          text-align: right;
          margin: auto 8px auto 0;
        }
        
        > .n-input {
          width: 230px;
        }
      }
    }
  }
  
  .timeline-section {
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
    }
    
    .step-content {
      .step-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        
        .step-code {
          font-weight: 500;
          color: #333;
        }
      }
      
      .step-details {
        margin-bottom: 8px;
        
        .time-info {
          display: flex;
          flex-direction: column;
          gap: 4px;
          font-size: 12px;
          color: #666;
          
          .duration {
            font-weight: 500;
            color: #2080f0;
          }
        }
      }
      
      .error-message {
        margin-bottom: 8px;
      }
      
      .step-extra-details {
        .details-content {
          font-size: 12px;
          line-height: 1.4;
          margin: 0;
          white-space: pre-wrap;
          word-break: break-word;
        }
      }
    }
  }
}
</style>