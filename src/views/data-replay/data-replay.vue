<template>
  <n-spin :show="pageLoading">
    <PageLayout v-loading="pageLoading">
      <template #header>
        <n-tabs
          :default-value="TAB_ENUM.DE"
          type="line"
          animated
          :pane-wrapper-class="'cardBox'"
          :on-update:value="tabChange"
        >
          <n-tab-pane :name="TAB_ENUM.DE" tab="数据入库记录"></n-tab-pane>
          <n-tab-pane :name="TAB_ENUM.DP" tab="数据重播记录"></n-tab-pane>
        </n-tabs>
      </template>
      <template #content>
        <div class="v">
          <div class="filterBox h" v-if="activeTab === TAB_ENUM.DE">
            <span>Filter Type</span>
            <n-select v-model:value="filterType" :options="FILTER_TYPE_OPTIONS" @update:value="filterChange" />
          </div>

          <SearchList
            ref="deLotSearchRef"
            @search="getList"
            :search-list="renderObj.searchList"
            :require-factory-first="true"
            v-show="activeTab === TAB_ENUM.DE && filterType === FilterTypeEnum.LOTWAFER"
          />
          <SearchList
            ref="deFileSearchRef"
            @search="getList"
            :search-list="renderObj.searchList"
            :require-factory-first="true"
            v-show="activeTab === TAB_ENUM.DE && filterType === FilterTypeEnum.FILE"
          />
          <SearchList
            ref="drSearchRef"
            @search="getList"
            :search-list="renderObj.searchList"
            v-show="activeTab === TAB_ENUM.DP"
          />
        </div>

        <div class="tabBox v">
          <div class="tableBox v" v-if="activeTab === TAB_ENUM.DE">
            <div class="tableHead h">
              <span v-if="deSelectedIds.length > 0">已选中 {{ deSelectedIds?.length }} 条</span>

              <n-dropdown
                trigger="hover"
                :options="renderObj.dropdownOptions"
                @select="dropdownSelected"
                :disabled="dropdownDis"
              >
                <n-button type="primary" class="fixed_btn" :disabled="dropdownDis || deTable.data?.length < 1">
                  批量操作
                </n-button>
              </n-dropdown>
            </div>

            <n-data-table
              :columns="renderObj.columns"
              :data="deTable.data"
              bottom-bordered
              :bordered="false"
              :single-line="false"
              :table-layout="'fixed'"
              :flex-height="true"
              :row-key="(row:DR_ListItem) => row.id"
              :scroll-x="4200"
              :loading="deTable.loading"
              :style="{ height: `calc(100vh - ${tableHeight}px)` }"
              :max-height="`calc(100vh - ${tableHeight}px)`"
              v-model:checked-row-keys="deSelectedIds"
              @update:checked-row-keys="(ids:number[])=>deSelectedIds = ids"
            />
            <n-pagination
              :showSizePicker="true"
              :pageSizes="[20, 50, 100, 200, 500, 1000]"
              v-model:page="deTable.pagination.pageIndex"
              v-model:pageSize="deTable.pagination.pageSize"
              :item-count="deTable.pagination.total"
              :prefix="deTable.pagination.prefix"
              @update:page="deTable.pagination.onChange"
              @update:pageSize="deTable.pagination.onUpdatePageSize"
            />
          </div>

          <div class="tableBox v" v-else>
            <n-data-table
              :columns="renderObj.columns"
              :data="drTable.data"
              :bordered="false"
              :single-line="false"
              :table-layout="'fixed'"
              :flex-height="true"
              :scroll-x="3890"
              :loading="drTable.loading"
              :row-key="(row:DR_ListItem) => row.id"
              :style="{ height: `calc(100vh - ${tableHeight}px)` }"
              :max-height="`calc(100vh - ${tableHeight}px)`"
            />
            <n-pagination
              :showSizePicker="true"
              :pageSizes="[20, 50, 100, 200, 500, 1000]"
              v-model:page="drTable.pagination.pageIndex"
              v-model:pageSize="drTable.pagination.pageSize"
              :item-count="drTable.pagination.total"
              :prefix="drTable.pagination.prefix"
              @update:page="drTable.pagination.onChange"
              @update:pageSize="drTable.pagination.onUpdatePageSize"
            />
          </div>
        </div>
      </template>
    </PageLayout>
  </n-spin>

  <DataDialog v-model:visible="visible" :dialogInfo="dialogInfo" @submit="reRunSubmit" :deSelectedIds="deSelectedIds" />
  <TaskRecordDialog v-model:visible="taskVisible" :id="currentId" />
  <ProcessDetailDialog v-model:visible="processDetailVisible" :fileId="fileWarehousingRecordId" :fileData="currentFileData" />
</template>

<script setup lang="ts">
import PageLayout from '../common/page-layout.vue';
import DataDialog from './data-dialog.vue';
import TaskRecordDialog from './task-record-dialog.vue';
import ProcessDetailDialog from './process-detail-dialog.vue';
import SearchList from '@/components/search-list/search-list.vue';
import { FILTER_TYPE_OPTIONS, FilterTypeEnum } from './data';
import { useTable } from './useTable';
import { DR_ListItem } from '@/types/dataReplay';

const {
  TAB_ENUM,
  drTable,
  deTable,
  visible,
  getList,
  currentId,
  fileWarehousingRecordId,
  renderObj,
  activeTab,
  tabChange,
  dialogInfo,
  filterType,
  tableHeight,
  dropdownDis,
  taskVisible,
  reRunSubmit,
  pageLoading,
  searchParam,
  drSearchRef,
  filterChange,
  deSelectedIds,
  deFileSearchRef,
  deLotSearchRef,
  dropdownSelected,
  currentFileData,
  processDetailVisible,
} = useTable();

const { resume } = useTimeoutPoll(() => {
  if (activeTab.value === TAB_ENUM.DP) {
    getList(searchParam.value);
  }
}, 15000);

onMounted(() => {
  resume();
});
</script>

<style scoped lang="scss">
.filterBox {
  margin: 0 0 8px;

  > span {
    margin: auto 8px auto 0;
    flex-shrink: 0;
  }

  > .n-select {
    width: 180px;
  }
}

.tabBox {
  flex: 1;
  background: #fff;
  border-radius: 4px;
  overflow: hidden;
  padding: 16px;

  .tableBox {
    height: 100%;

    > .tableHead {
      > .n-button {
        margin-left: auto;
        margin-bottom: 10px;
      }
    }

    > .n-data-table {
      :deep(.n-base-icon) {
        right: 2px;
      }
      :deep(.paginationBox) {
        width: 100%;
        height: 16px;
        overflow: hidden;
        border-radius: 50px;
        border: 1px solid #ccc;

        > div {
          width: 33.3%;
          height: 100%;
          &:hover {
            cursor: pointer;
            & > span {
              font-weight: bold;
            }
          }

          > span {
            color: #fff;
            font-size: 12px;
            margin: auto;
          }
        }
      }
    }

    > .n-pagination {
      margin: 16px 0 0 auto;
    }
  }
}
</style>
