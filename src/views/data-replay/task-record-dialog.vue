<template>
  <n-modal
    style="width: 1200px"
    :show="visible"
    :mask-closable="false"
    preset="dialog"
    title="任务记录"
    display-directive="'if'"
    @close="close"
  >
    <div class="taskRecord v">
      <SearchList
        @search="search"
        :search-list="TASK_SEARCH_LIST"
        :other-params="{ id: props.id, dropDownPageType: 'REPLAY_TASK_PAGE' }"
      />

      <n-data-table
        :columns="TASK_COLUMNS()"
        :data="table.data"
        :bordered="false"
        :single-line="false"
        :table-layout="'auto'"
        :scroll-x="2580"
        :style="{ height: '400px' }"
        :max-height="'300px'"
      />
      <n-pagination
        :showSizePicker="true"
        :pageSizes="[10, 20, 30, 40, 50]"
        v-model:page="table.pagination.pageIndex"
        v-model:pageSize="table.pagination.pageSize"
        :item-count="table.pagination.total"
        :prefix="table.pagination.prefix"
        @update:page="table.pagination.onChange"
        @update:pageSize="table.pagination.onUpdatePageSize"
      />

      <div class="loading" v-if="loading">
        <n-spin size="medium" />
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import SearchList from '@/components/search-list/search-list.vue';
import { TASK_SEARCH_LIST, TASK_COLUMNS } from './data';
import { getTaskList } from '@/service/dataReplay';
import { SUCCESS_CODE } from '@/utils/http';
import { TaskListParam, TaskListRes } from '@/types/dataReplay';
import { PaginationInfo } from 'naive-ui';

const props = defineProps<{ visible: boolean; id: number }>();
const emits = defineEmits(['update:visible']);
const visible = useVModel(props, 'visible', emits);

const loading = ref(false);
const searchParam = ref<TaskListParam>({});

const table = reactive<{ data: TaskListRes[]; pagination: any }>({
  data: [],
  pagination: {
    total: 0,
    pageSize: 20,
    pageIndex: 1,
    prefix: (info: PaginationInfo) => {
      return `共${info.itemCount}条`;
    },
    onChange: (page: number) => {
      table.pagination.pageIndex = page;
      search({ ...searchParam.value, pageIndex: page });
    },
    onUpdatePageSize: (pageSize: number) => {
      table.pagination.pageIndex = 1;
      table.pagination.pageSize = pageSize;
      search({ ...searchParam.value, pageSize: pageSize, pageIndex: 1 });
    },
  },
});

const search = async (param: TaskListParam) => {
  loading.value = true;
  searchParam.value = param;
  const res = await getTaskList({ ...param, id: props.id });
  loading.value = false;
  if (res.data.code === SUCCESS_CODE) {
    const rData = res.data.data;
    table.data = rData.data;
    table.pagination.total = rData.total;
    table.pagination.pageIndex = rData.pageIndex;
    table.pagination.pageSize = rData.pageSize;
  }
};

const close = () => {
  visible.value = false;
  table.data = [];
};

watch(visible, () => {
  if (visible.value) {
    search({});
  }
});
</script>

<style scoped lang="scss">
.taskRecord {
  width: 100%;
  height: 540px;
}
.n-pagination {
  margin: 16px 0 0 auto;
}

.loading {
  width: 100%;
  height: 100%;
  display: flex;
  position: absolute;
  background: #00000011;

  > div {
    margin: auto;
  }
}
</style>
