<template>
  <n-modal
    style="width: 500px"
    :show="visible"
    :mask-closable="false"
    preset="dialog"
    title="确认重播条件并选择重播步骤"
    positive-text="确认"
    negative-text="取消"
    display-directive="'if'"
    @close="clear"
    @negative-click="clear"
    @positive-click="subParam"
  >
    <div class="formBox v">
      <div class="box h" v-for="item in domRender" :key="item.label">
        <span>{{ item.label }}: </span>

        <n-tooltip trigger="hover">
          <template #trigger>
            <span>{{ (item.value + '')?.length < 20 ? item.value : (item.value + '')?.slice(0, 20) + '...' }}</span>
          </template>
          {{ item.value }}
        </n-tooltip>
      </div>

      <div class="box h">
        <span>重播类型: </span>
        <span>{{
          props.dialogInfo.replayType === 'DELETE_LOT' || props.dialogInfo.replayType === 'DELETE_FILE'
            ? '删除'
            : '重试'
        }}</span>
      </div>
      <div class="box h">
        <span>重播步骤: </span>
        <n-select v-model:value="form.replayStep" size="small" :options="options" />
      </div>
      <div class="box h">
        <span>Comments: </span>
        <n-input v-model:value="form.replayComments" size="small" />
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { DropdownItem } from '@/types/common';
import { BatchReplayParam, DE_DropdownParam, DialogInfo, FileCategory, SingleReplayParam } from '@/types/dataReplay';
import { DE_SEARCH_LIST_LOTWAFER, ReRunEnum } from './data';
import { useMessage } from 'naive-ui';
import { isArray, isEmpty } from 'lodash';

const RENDER_KEY_LIST = [
  'testArea',
  'factory',
  'deviceId',
  'fileCategory',
  'lotIdList',
  'waferNoList',
  'lotType',
  'testStageList',
];

const props = defineProps<{ visible: boolean; dialogInfo: DialogInfo; deSelectedIds: number[] }>();
const emits = defineEmits(['update:visible', 'submit']);
const visible = useVModel(props, 'visible', emits);
const message = useMessage();

const domRender = computed(() => {
  const list: { label: string; value: string | number | string[] | [string, string] | undefined | null }[] = [];

  if (props.dialogInfo.isSingle) {
    if (!props.dialogInfo?.row) return list;
    list.push({ label: 'File Name', value: props.dialogInfo.row?.fileName });
  } else {
    if (!props.dialogInfo?.searchParam) return list;

    const getLabel = (key: string) => {
      return DE_SEARCH_LIST_LOTWAFER?.find((item) => item.key === key)?.label ?? key;
    };
    const getVal = (val: string | number | string[] | [string, string] | undefined | null) => {
      if (isArray(val)) {
        return val?.join();
      }

      return val;
    };

    const search = props.dialogInfo.searchParam;
    const keys: any[] = Object.keys(search);
    keys?.map((key: keyof DE_DropdownParam) => {
      if (RENDER_KEY_LIST?.some((item) => item === key) && !isEmpty(search?.[key])) {
        list.push({ label: getLabel(key), value: getVal(search[key]) });
      }
    });

    const len =
      props.deSelectedIds?.length < 1 ? props.dialogInfo?.replayFileMaxCount ?? 0 : props.deSelectedIds.length;

    const batchSize = props.dialogInfo?.replayFileBatchSize ?? 0;
    const fileCountText = len > batchSize ? `${len}（将分批处理，每批最多${batchSize}个文件）` : len;

    list.push({ label: '受影响的文件数', value: fileCountText });
  }

  return list;
});

const getOptions = (fileCategory: FileCategory) => {
  let list: DropdownItem[] = [];
  if (fileCategory === 'RAW_DATA' || fileCategory === 'BIT_MEM') {
    list = [
      {
        label: '2200',
        value: 2200,
        disabled: isDis(2200),
      },
      {
        label: '3100',
        value: 3100,
        disabled: isDis(3100),
      },
      {
        label: '4100',
        value: 4100,
        disabled: isDis(4100),
      },
      {
        label: '5100',
        value: 5100,
        disabled: isDis(5100),
      },
    ];
  } else if (fileCategory === 'STDF') {
    list = [
      {
        label: '3100',
        value: 3100,
        disabled: isDis(3100),
      },
      {
        label: '4100',
        value: 4100,
        disabled: isDis(4100),
      },
      {
        label: '5100',
        value: 5100,
        disabled: isDis(5100),
      },
    ];
  } else {
    list = [
      {
        label: '2200',
        value: 2200,
        disabled: isDis(2200),
      },
    ];
  }

  return list;
};

const isDis = (num: number) => {
  if (!props.dialogInfo?.row?.step) return false;
  return props.dialogInfo.row.step >= num ? false : true;
};
const options = computed(() => {
  let list: DropdownItem[] = [];

  if (props.dialogInfo.replayType === ReRunEnum.DELETE_FILE || props.dialogInfo.replayType === ReRunEnum.DELETE_LOT) {
    list = [{ label: '1000', value: 1000, disabled: isDis(1000) }];
  } else {
    if (props.dialogInfo.isSingle) {
      if (!props.dialogInfo.row) return [];
      list = getOptions(props.dialogInfo.row.fileCategory);
    } else {
      if (!props.dialogInfo.searchParam?.fileCategory) return [];
      list = getOptions(props.dialogInfo.searchParam.fileCategory);
    }
  }

  return list;
});

const form = reactive<SingleReplayParam & BatchReplayParam>({
  replayStep: undefined,
  replayComments: undefined,
  fileCategory: undefined,
  fileWarehousingRecordIdList: [],
});

//  表单提交
const subParam = () => {
  if (!form.replayStep) {
    message.warning('重播步骤未选择!');
    return;
  }
  if (!form.replayComments) {
    message.warning('Comments未填写!');
    return;
  }

  if (props.dialogInfo.isSingle) {
    if (props.dialogInfo.row) {
      form.fileName = props.dialogInfo.row?.fileName;
      form.fileCategory = props.dialogInfo.row?.fileCategory;
      form.replayType = props.dialogInfo.replayType;
      form.fileWarehousingRecordIdList = [props.dialogInfo.row.id];
      emits('submit', form);
    }
  } else {
    form.replayType = props.dialogInfo.replayType;
    form.fileWarehousingRecordIdList = props.deSelectedIds;
    emits('submit', form);
  }
  clear();
};
const clear = () => {
  form.fileName = undefined;
  form.replayStep = undefined;
  form.replayComments = undefined;
  form.fileWarehousingRecordIdList = [];

  visible.value = false;
};
</script>

<style scoped lang="scss">
.formBox {
  padding: 16px;

  > .box {
    margin: 5px 0;

    > span {
      &:first-child {
        margin: auto 10px auto 0;
        text-align: right;
        width: 110px;
      }
      &:last-child {
        overflow: hidden;
      }
    }

    > .n-select,
    .n-input {
      width: 180px;
    }
  }
}
</style>
