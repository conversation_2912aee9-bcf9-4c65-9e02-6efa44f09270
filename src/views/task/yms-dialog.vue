<template>
  <n-modal
    :style="{ width: props.dialogInfo.isSingle ? '1200px' : '450px' }"
    :show="visible"
    :mask-closable="false"
    preset="dialog"
    title="DMT任务重试"
    positive-text="确认"
    negative-text="取消"
    display-directive="'if'"
    @close="clear"
    @negative-click="clear"
    @positive-click="subParam"
  >
    <div class="dwSingleBox v" v-if="props.dialogInfo.isSingle">
      <div class="infoBox h">
        <div class="item h" v-for="item in YMS_SINGLE_INFO_LIST" :key="item.value">
          <span>{{ item.label }}</span>
          <n-input v-model:value="singleInfoObj[item.value]" disabled />
        </div>
      </div>

      <div class="errorBox h">
        <span>error_message</span>
        <n-input
          v-model:value="singleInfoObj['errorMessage']"
          disabled
          type="textarea"
          placeholder=""
          :autosize="{
            minRows: 8,
            maxRows: 8,
          }"
        />
      </div>

      <n-data-table
        :columns="YMS_INFO_COLUMNS"
        :data="singleInfoObj.dmtTaskDetailVos"
        style="max-height: 200"
        :scroll-x="1800"
        :max-height="'260px'"
      />
    </div>

    <div class="dwBox" v-else>
      <div class="infoBox v">
        <div class="item h" v-for="item in YMS_INFO_LIST" :key="item.value">
          <span>{{ item.label }}</span>
          <n-select v-model:value="infoObj[item.value]" disabled multiple placeholder="" />
        </div>

        <div class="item h">
          <span>重试的记录数: </span>
          <div>{{ props.selectedLength }}</div>
        </div>
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { useMessage } from 'naive-ui';
import { SearchParam } from '@/types/common';
import { DialogInfo, YMS_DetailInfo } from '@/types/task';
import { YMS_INFO_LIST, YMS_SINGLE_INFO_LIST, YMS_INFO_COLUMNS } from './data';
import { getYmsDetail } from '@/service/task';
import { SUCCESS_CODE } from '@/utils/http';

const props = defineProps<{ visible: boolean; dialogInfo: DialogInfo; selectedLength: number }>();
const emits = defineEmits(['update:visible', 'submit']);
const visible = useVModel(props, 'visible', emits);

const message = useMessage();
const infoObj = ref<SearchParam>({});
const singleInfoObj = ref<YMS_DetailInfo>({});

//  表单提交
const subParam = () => {
  const param = {
    id: props.dialogInfo.row?.id,
    dmtTaskDetailVos: singleInfoObj.value.dmtTaskDetailVos,
  };
  if (props.dialogInfo.isSingle) {
    if (
      singleInfoObj.value.dmtTaskDetailVos?.some(
        (item) =>
          item.modifyParamFlag &&
          (!item.numExecutors ||
            !item.executorCores ||
            !item.executorMemory ||
            !item.driverMemory ||
            !item.parallelism ||
            !item.extraConf),
      )
    ) {
      message.warning('可编辑参数不能为空!');
      return;
    }
    emits('submit', param);
  } else {
    emits('submit', { ids: props.dialogInfo.ids });
  }
};

const clear = () => {
  visible.value = false;
};

const setSearch = () => {
  infoObj.value = { ...props.dialogInfo.searchParam };
};
const setRow = async () => {
  if (props.dialogInfo.row?.id) {
    const res = await getYmsDetail(props.dialogInfo.row?.id as number);
    if (res.data.code === SUCCESS_CODE) {
      singleInfoObj.value = { ...res.data.data };
    }
  }
};

watchDebounced(visible, () => {
  if (visible.value && props.dialogInfo.isSingle) {
    setRow();
  }

  if (visible.value && !props.dialogInfo.isSingle) {
    setSearch();
  }
});
</script>

<style scoped lang="scss">
.dwSingleBox {
  // height: 580px;
  padding: 0 16px;
  // max-height: 580px;
  // overflow: auto;

  > .infoBox {
    width: 100%;
    flex-wrap: wrap;
    margin-bottom: 10px;

    > .item {
      margin: 8px;
      > span {
        width: 113px;
        flex-shrink: 0;
        text-align: right;
        margin: auto 8px auto 0;
      }
      > .n-input,
      .n-select {
        width: 230px;
      }
    }
  }

  > .errorBox {
    height: 200px;
    border-top: 1px solid #ccc;
    padding-top: 20px;

    > span {
      width: 120px;
      flex-shrink: 0;
      text-align: right;
      margin: 0 8px auto 0;
    }
    > .n-input {
      width: 966px;
    }
  }

  > .n-data-table {
    margin-top: 20px;
  }
}

.dwBox {
  // height: 480px;
  padding: 0 16px;

  > .infoBox {
    width: 100%;
    flex-wrap: wrap;
    margin-bottom: 10px;

    > .item {
      margin: 8px;
      > span {
        width: 103px;
        flex-shrink: 0;
        text-align: right;
        margin: auto 8px auto 0;
      }
      > .n-input,
      .n-select {
        width: 230px;
      }
    }
  }
}
</style>
