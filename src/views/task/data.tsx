import { freezeDmt, getDwDropdown, getYmsDropdown } from '@/service/task';
import { SearchItem, SearchParam } from '@/types/common';
import { DetailInfo, DmtTaskDetailVo, DW_ListItem, DW_RerunParam, YMS_ListItem } from '@/types/task';
import { SUCCESS_CODE } from '@/utils/http';
import { DataTableColumns, NButton, NInput } from 'naive-ui';
import { MessageApiInjection } from 'naive-ui/es/message/src/MessageProvider';

export enum ReRunFlagEnum {
  SINGLE_RERUN = 'SINGLE_RERUN',
  BATCH_RERUN = 'BATCH_RERUN',
}
export enum StatusEnum {
  SUCCESS = 'SUCCESS',
  FAIL = 'FAIL',
  PROCESSING = 'PROCESSING',
  CREATE = 'CREATE',
  FREEZE = 'FREEZE',
  RELOADING = 'RELOADING',
  RELOAD_FAIL = 'RELOAD_FAIL',
  RELOAD_SUCCESS = 'RELOAD_SUCCESS',
}

// --------------------------------------DW----------------------------------------
//  搜搜列表
export const DW_SEARCH_LIST: SearchItem[] = [
  {
    label: 'Test Area',
    type: 'select',
    key: 'testAreaList',
    multiple: true,
    api: getDwDropdown,
  },
  {
    label: 'Factory',
    type: 'select',
    key: 'factoryList',
    multiple: true,
    api: getDwDropdown,
  },
  {
    label: 'Device ID',
    type: 'select',
    key: 'deviceIdList',
    multiple: true,
    api: getDwDropdown,
  },
  {
    label: 'Test Stage',
    type: 'select',
    key: 'testStageList',
    multiple: true,
    api: getDwDropdown,
  },
  {
    label: 'Lot Id',
    type: 'select',
    key: 'lotIdList',
    multiple: true,
    api: getDwDropdown,
  },
  {
    label: 'Wafer No',
    type: 'select',
    key: 'waferNoList',
    multiple: true,
    api: getDwDropdown,
  },
  {
    label: 'Lot Type',
    type: 'select',
    key: 'lotTypeList',
    multiple: true,
    api: getDwDropdown,
  },

  {
    label: 'DW Layer',
    type: 'select',
    key: 'dwLayerList',
    multiple: true,
    options: [
      {
        label: 'DWD',
        value: 'DWD',
      },
      {
        label: 'DWS',
        value: 'DWS',
      },
    ],
  },
  {
    label: 'File Category',
    type: 'select',
    key: 'fileCategoryList',
    multiple: true,
    options: [
      {
        label: 'STDF',
        value: 'STDF',
      },
      {
        label: 'RAW_DATA',
        value: 'RAW_DATA',
      },
      {
        label: 'BIT_MEM',
        value: 'BIT_MEM',
      },
    ],
  },
  {
    label: 'Process Status',
    type: 'select',
    key: 'processStatusList',
    multiple: true,
    options: [
      {
        label: 'FAIL',
        value: 'FAIL',
      },
      {
        label: 'SUCCESS',
        value: 'SUCCESS',
      },
      {
        label: 'CREATE',
        value: 'CREATE',
      },
      {
        label: 'PROCESSING',
        value: 'PROCESSING',
      },
      {
        label: 'FREEZE',
        value: 'FREEZE',
      },
      {
        label: 'RELOADING',
        value: 'RELOADING',
      },
      {
        label: 'RELOAD_FAIL',
        value: 'RELOAD_FAIL',
      },
      {
        label: 'RELOAD_SUCCESS',
        value: 'RELOAD_SUCCESS',
      },
    ],
  },
];

//  表头
export const DW_COLUMNS = (reRun: (row: DW_ListItem) => void, getList: () => void, message: MessageApiInjection) => {
  const freezeDmtFn = async (id: number) => {
    try {
      const res = await freezeDmt(id);
      if (res.data.code === SUCCESS_CODE) {
        message?.success('恢复测项数据中');
        getList();
      }
    } catch (error) {
      message?.error('恢复测项数据失败');
      console.error(error);
    }
  };

  return [
    {
      type: 'selection',
      fixed: 'left',
    },
    {
      title: 'Test Area',
      key: 'testArea',
      resizable: true,
      width: 160,
    },
    {
      title: 'Factory',
      key: 'factory',
      resizable: true,
      width: 160,
    },
    {
      title: 'Device ID',
      key: 'deviceId',
      resizable: true,
      width: 160,
    },
    {
      title: 'Test Stage',
      key: 'testStage',
      resizable: true,
      width: 160,
    },
    {
      title: 'Lot Id',
      key: 'lotId',
      resizable: true,
      width: 160,
    },
    {
      title: 'Wafer No',
      key: 'waferNo',
      resizable: true,
      width: 160,
    },
    {
      title: 'Lot Type',
      key: 'lotType',
      resizable: true,
      width: 160,
    },
    {
      title: 'DW Layer',
      key: 'dwLayer',
      resizable: true,
      width: 160,
    },
    {
      title: 'File Category',
      key: 'fileCategory',
      resizable: true,
      width: 160,
    },
    {
      title: 'Process Status',
      key: 'processStatus',
      resizable: true,
      width: 160,
      render: (rowData: any) => {
        if (rowData?.processStatus === StatusEnum.SUCCESS || rowData?.processStatus === StatusEnum.RELOAD_SUCCESS) {
          return <span style={{ color: 'rgb(25,160,88)' }}>{rowData?.processStatus}</span>;
        } else if (
          rowData?.processStatus === StatusEnum.PROCESSING ||
          rowData?.processStatus === StatusEnum.RELOADING
        ) {
          return <span style={{ color: 'rgb(240,160,32)' }}>{rowData?.processStatus}</span>;
        } else if (rowData?.processStatus === StatusEnum.FAIL || rowData?.processStatus === StatusEnum.RELOAD_FAIL) {
          return <span style={{ color: 'rgb(208,48,80)' }}>{rowData?.processStatus}</span>;
        } else {
          return <span>{rowData?.processStatus}</span>;
        }
      },
    },
    {
      title: 'Exception',
      key: 'exceptionType',
      resizable: true,
      width: 160,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: 'Error Message',
      key: 'errorMessage',
      resizable: true,
      width: 160,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: 'Create Time',
      key: 'createTime',
      resizable: true,
      width: 160,
    },
    {
      title: 'Update Time',
      key: 'updateTime',
      resizable: true,
      width: 160,
    },
    {
      title: '操作',
      key: 'tool',
      width: 260,
      fixed: 'right',
      render: (rowData: any) => {
        return (
          <>
            <NButton
              type="info"
              text
              onClick={() => window.open(rowData?.taskUrl)}
              style={{ marginRight: '16px' }}
              disabled={
                rowData.processStatus === StatusEnum.CREATE ||
                rowData.processStatus === StatusEnum.PROCESSING ||
                !rowData?.taskUrl
              }
            >
              任务日志
            </NButton>
            <NButton
              type="error"
              text
              onClick={() => reRun(rowData)}
              disabled={rowData.processStatus !== StatusEnum.FAIL}
            >
              任务重试
            </NButton>
            <NButton
              type="info"
              text
              onClick={() => freezeDmtFn(rowData.id)}
              style={{ marginLeft: '16px' }}
              disabled={rowData.processStatus !== StatusEnum.FREEZE}
            >
              恢复测项数据
            </NButton>
          </>
        );
      },
    },
  ];
};

//  批量操作列表
export const DW_DROPDOWN_OPTIONS = [
  {
    label: '批量重试',
    key: 'reRun',
  },
];

// --------------------------------------YMS----------------------------------------
//  搜索列表
export const YMS_SEARCH_LIST: SearchItem[] = [
  {
    label: 'Test Area',
    type: 'select',
    key: 'testAreaList',
    multiple: true,
    api: getYmsDropdown,
  },
  {
    label: 'Factory',
    type: 'select',
    key: 'factoryList',
    multiple: true,
    api: getYmsDropdown,
  },
  {
    label: 'Device ID',
    type: 'select',
    key: 'deviceIdList',
    multiple: true,
    api: getYmsDropdown,
  },
  {
    label: 'Test Stage',
    type: 'select',
    key: 'testStageList',
    multiple: true,
    api: getYmsDropdown,
  },
  {
    label: 'Lot Id',
    type: 'select',
    key: 'lotIdList',
    multiple: true,
    api: getYmsDropdown,
  },
  {
    label: 'Wafer No',
    type: 'select',
    key: 'waferNoList',
    multiple: true,
    api: getYmsDropdown,
  },
  {
    label: 'Lot Type',
    type: 'select',
    key: 'lotTypeList',
    multiple: true,
    api: getYmsDropdown,
  },
  {
    label: 'Process Status',
    type: 'select',
    key: 'processStatusList',
    multiple: true,
    options: [
      {
        label: 'FAIL',
        value: 'FAIL',
      },
      {
        label: 'SUCCESS',
        value: 'SUCCESS',
      },
      {
        label: 'CREATE',
        value: 'CREATE',
      },
      {
        label: 'PROCESSING',
        value: 'PROCESSING',
      },
    ],
  },
  {
    label: 'Opt Type',
    type: 'select',
    key: 'optTypeList',
    multiple: true,
    options: [
      {
        label: 'NEW_WAT_DATA',
        value: 'NEW_WAT_DATA',
      },
      {
        label: 'NEW_MES_DATA',
        value: 'NEW_MES_DATA',
      },
      {
        label: 'NEW_TEST_ITEM_DATA',
        value: 'NEW_TEST_ITEM_DATA',
      },
      {
        label: 'NEW_BITMEM_TEST_ITEM_DATA',
        value: 'NEW_BITMEM_TEST_ITEM_DATA',
      },
      {
        label: 'UPDATE_PRODUCT',
        value: 'UPDATE_PRODUCT',
      },
      {
        label: 'UPDATE_WAFERMAP_CONFIG',
        value: 'UPDATE_WAFERMAP_CONFIG',
      },
      {
        label: 'UPDATE_MES_BATCH_INFO',
        value: 'UPDATE_MES_BATCH_INFO',
      },
      // {NEW_MES_DATA,  UPDATE_MES_BATCH_INFO
      //   label: 'MODIFY_WAFERMAP_CONFIG',
      //   value: 'MODIFY_WAFERMAP_CONFIG',
      // },
    ],
  },
  {
    label: 'Execute Engine',
    type: 'select',
    key: 'executeEngineList',
    multiple: true,
    options: [
      {
        label: 'CLICKHOUSE',
        value: 'CLICKHOUSE',
      },
      {
        label: 'SPARK',
        value: 'SPARK',
      },
    ],
  },
];

//  表头
export const YMS_COLUMNS: (reRun: (row: YMS_ListItem) => void) => DataTableColumns<YMS_ListItem> = (reRun) => [
  {
    type: 'selection',
    fixed: 'left',
  },
  {
    title: 'Name',
    key: 'name',
    resizable: true,
    width: 160,
  },
  {
    title: 'Opt Type',
    key: 'optType',
    resizable: true,
    width: 160,
  },
  {
    title: 'Execute Engine',
    key: 'executeEngine',
    resizable: true,
    width: 160,
  },
  {
    title: 'Test Area',
    key: 'testArea',
    resizable: true,
    width: 160,
  },
  {
    title: 'Factory',
    key: 'factory',
    resizable: true,
    width: 160,
  },
  {
    title: 'Device ID',
    key: 'deviceId',
    resizable: true,
    width: 160,
  },
  {
    title: 'Test Stage',
    key: 'testStage',
    resizable: true,
    width: 160,
  },
  {
    title: 'Lot Id',
    key: 'lotId',
    resizable: true,
    width: 160,
  },
  {
    title: 'Wafer No',
    key: 'waferNo',
    resizable: true,
    width: 160,
  },
  {
    title: 'Lot Type',
    key: 'lotType',
    resizable: true,
    width: 160,
  },
  {
    title: 'Start Step',
    key: 'startStep',
    resizable: true,
    width: 160,
  },
  {
    title: 'Process Status',
    key: 'processStatus',
    resizable: true,
    width: 160,
    render: (rowData) => {
      if (rowData?.processStatus === StatusEnum.SUCCESS) {
        return <span style={{ color: 'rgb(25,160,88)' }}>SUCCESS</span>;
      } else if (rowData?.processStatus === StatusEnum.PROCESSING) {
        return <span style={{ color: 'rgb(240,160,32)' }}>PROCESSING</span>;
      } else if (rowData?.processStatus === StatusEnum.FAIL) {
        return <span style={{ color: 'rgb(208,48,80)' }}>FAIL</span>;
      } else {
        return <span>CREATE</span>;
      }
    },
  },
  {
    title: 'Execute Time',
    key: 'executeTime',
    resizable: true,
    width: 160,
  },
  {
    title: 'Exception Type',
    key: 'exceptionType',
    resizable: true,
    width: 160,
  },
  {
    title: 'Error Message',
    key: 'errorMessage',
    resizable: true,
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: 'Create Time',
    key: 'createTime',
    resizable: true,
    width: 160,
  },
  {
    title: 'Update Time',
    key: 'updateTime',
    resizable: true,
    width: 160,
  },
  {
    title: '操作',
    key: 'tool',
    width: 160,
    fixed: 'right',
    render: (rowData) => {
      return (
        <>
          <NButton
            type="info"
            text
            style={{ marginRight: '16px' }}
            onClick={() => reRun(rowData)}
            disabled={rowData.processStatus !== StatusEnum.FAIL}
          >
            任务重试
          </NButton>
        </>
      );
    },
  },
];

//  批量操作列表
export const YMS_DROPDOWN_OPTIONS = [
  {
    label: '批量重试',
    key: 'reRun',
  },
];

// --------------------------------------Dialog----------------------------------------
export const DW_SINGLE_INFO_LIST: {
  label: string;
  value: keyof DetailInfo;
}[] = [
  { label: 'Test Area', value: 'testArea' },
  { label: 'Factory', value: 'factory' },
  { label: 'Device ID', value: 'deviceId' },
  { label: 'Test Stage', value: 'testStage' },
  { label: 'Lot ID', value: 'lotId' },
  { label: 'Wafer No', value: 'waferNo' },
  { label: 'Lot Type', value: 'lotType' },
  { label: 'DW Layer', value: 'dwLayer' },
  { label: 'Process Status', value: 'processStatus' },
];
export const DW_INFO_LIST: {
  label: string;
  value: keyof SearchParam;
}[] = [
  { label: 'Test Area', value: 'testAreaList' },
  { label: 'Factory', value: 'factoryList' },
  { label: 'Device ID', value: 'deviceIdList' },
  { label: 'Test Stage', value: 'testStageList' },
  { label: 'Lot ID', value: 'lotIdList' },
  { label: 'Wafer No', value: 'waferNoList' },
  { label: 'Lot Type', value: 'lotTypeList' },
  { label: 'DW Layer', value: 'dwLayerList' },
  { label: 'Process Status', value: 'processStatusList' },
];
export const YMS_SINGLE_INFO_LIST: {
  label: string;
  value: keyof YMS_ListItem;
}[] = [
  { label: 'Test Area', value: 'testArea' },
  { label: 'Factory', value: 'factory' },
  { label: 'Device ID', value: 'deviceId' },
  { label: 'Test Stage', value: 'testStage' },
  { label: 'Lot ID', value: 'lotId' },
  { label: 'Wafer No', value: 'waferNo' },
  { label: 'Lot Type', value: 'lotType' },
  { label: 'Process Status', value: 'processStatus' },
  { label: 'Opt Type', value: 'optType' },
  { label: 'Execute Engine', value: 'executeEngine' },
];
export const YMS_INFO_LIST: {
  label: string;
  value: keyof SearchParam;
}[] = [
  { label: 'Test Area', value: 'testAreaList' },
  { label: 'Factory', value: 'factoryList' },
  { label: 'Device ID', value: 'deviceIdList' },
  { label: 'Test Stage', value: 'testStageList' },
  { label: 'Lot ID', value: 'lotIdList' },
  { label: 'Wafer No', value: 'waferNoList' },
  { label: 'Lot Type', value: 'lotTypeList' },
  { label: 'Process Status', value: 'processStatusList' },
  { label: 'Opt Type', value: 'optTypeList' },
  { label: 'Execute Engine', value: 'executeEngineList' },
];
export const INFO_COLUMNS: DataTableColumns<DW_RerunParam> = [
  {
    title: 'num_executors',
    key: 'numExecutors',
    width: 160,
    render: (rowData) => <NInput v-model:value={rowData.numExecutors} disabled />,
  },
  {
    title: 'executor_cores',
    key: 'executorCores',
    width: 160,
    render: (rowData) => <NInput v-model:value={rowData.executorCores} disabled />,
  },
  {
    title: 'executor_memory',
    key: 'executorMemory',
    width: 160,
    render: (rowData) => <NInput v-model:value={rowData.executorMemory} disabled />,
  },
  {
    title: 'driver_memory',
    key: 'driverMemory',
    width: 160,
    render: (rowData) => <NInput v-model:value={rowData.driverMemory} disabled />,
  },
  {
    title: 'parallelism',
    key: 'parallelism',
    width: 160,
    render: (rowData) => <NInput v-model:value={rowData.parallelism} disabled />,
  },
  {
    title: 'extra_conf',
    key: 'extraConf',
    width: 160,
    render: (rowData) => <NInput v-model:value={rowData.extraConf} disabled />,
  },
];
export const YMS_INFO_COLUMNS: DataTableColumns<DmtTaskDetailVo> = [
  {
    title: 'Id',
    key: 'id',
    width: 160,
  },
  {
    title: 'Pre Task Instance id',
    key: 'preTaskInstanceId',
    width: 160,
    render: (rowData) => <NInput v-model:value={rowData.preTaskInstanceId} disabled={true} />,
  },
  {
    title: 'Execute Engine',
    key: 'executeEngine',
    width: 160,
    render: (rowData) => <NInput v-model:value={rowData.executeEngine} disabled={true} />,
  },
  {
    title: 'Process Status',
    key: 'processStatus',
    width: 160,
    render: (rowData) => <NInput v-model:value={rowData.processStatus} disabled={true} />,
  },

  {
    title: 'Num Executors',
    key: 'numExecutors',
    width: 160,
    render: (rowData) => <NInput v-model:value={rowData.numExecutors} disabled={!rowData.modifyParamFlag} />,
  },
  {
    title: 'Executor Cores',
    key: 'executorCores',
    width: 160,
    render: (rowData) => <NInput v-model:value={rowData.executorCores} disabled={!rowData.modifyParamFlag} />,
  },
  {
    title: 'Executor Memory',
    key: 'executorMemory',
    width: 160,
    render: (rowData) => <NInput v-model:value={rowData.executorMemory} disabled={!rowData.modifyParamFlag} />,
  },
  {
    title: 'Driver Memory',
    key: 'driverMemory',
    width: 160,
    render: (rowData) => <NInput v-model:value={rowData.driverMemory} disabled={!rowData.modifyParamFlag} />,
  },
  {
    title: 'Parallelism',
    key: 'parallelism',
    width: 160,
    render: (rowData) => <NInput v-model:value={rowData.parallelism} disabled={!rowData.modifyParamFlag} />,
  },
  {
    title: 'Extra Conf',
    key: 'extraConf',
    width: 160,
    render: (rowData) => <NInput v-model:value={rowData.extraConf} disabled={!rowData.modifyParamFlag} />,
  },
];
