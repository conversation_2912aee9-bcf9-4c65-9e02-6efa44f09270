<template>
  <n-spin :show="pageLoading">
    <PageLayout>
      <template #header>
        <n-tabs default-value="dw" type="line" animated :pane-wrapper-class="'cardBox'" :on-update:value="tabChange">
          <n-tab-pane name="dw" tab="DW"></n-tab-pane>
          <n-tab-pane name="yms" tab="DMT"></n-tab-pane>
        </n-tabs>
      </template>
      <template #content>
        <SearchList ref="searchRef" :search-list="renderObj.searchList" @search="getList" v-show="activeTab === 'dw'" />
        <SearchList
          ref="searchRef"
          :search-list="renderObj.searchList"
          @search="getList"
          v-show="activeTab === 'yms'"
        />

        <div class="tabBox v" ref="tabRef">
          <div class="tableBox v" v-if="activeTab === 'dw'">
            <div class="tableHead h">
              <n-dropdown
                trigger="hover"
                :options="renderObj.dropdownOptions"
                @select="dropdownSelected"
                :disabled="disabled"
              >
                <n-button type="primary" class="fixed_btn" :disabled="disabled">批量操作</n-button>
              </n-dropdown>
            </div>

            <n-data-table
              :columns="renderObj.columns"
              :data="dwTable.data"
              bottom-bordered
              :row-key="(row:DW_ListItem) => row.id"
              :bordered="true"
              :single-line="false"
              :table-layout="'fixed'"
              :scroll-x="2300"
              :style="{ height: `calc(100vh - 340px)` }"
              :max-height="'calc(100vh - 390px)'"
              :checked-row-keys="dwSelectedIds"
              @update:checked-row-keys="(ids:number[])=>dwSelectedIds = ids"
            />
            <n-pagination
              :showSizePicker="true"
              :pageSizes="[20, 50, 100, 200, 500, 1000]"
              v-model:page="dwTable.pagination.pageIndex"
              v-model:pageSize="dwTable.pagination.pageSize"
              :item-count="dwTable.pagination.total"
              :prefix="dwTable.pagination.prefix"
              @update:page="dwTable.pagination.onChange"
              @update:pageSize="dwTable.pagination.onUpdatePageSize"
            />
          </div>

          <div class="tableBox v" v-else>
            <div class="tableHead h">
              <n-dropdown
                trigger="hover"
                :options="renderObj.dropdownOptions"
                @select="dropdownSelected"
                :disabled="disabled"
              >
                <n-button type="primary" class="fixed_btn" :disabled="disabled">批量操作</n-button>
              </n-dropdown>
            </div>

            <n-data-table
              :columns="renderObj.columns"
              :data="ymsTable.data"
              bottom-bordered
              :row-key="(row:YMS_ListItem) => row.id"
              :bordered="true"
              :single-line="false"
              :table-layout="'fixed'"
              :scroll-x="2780"
              :style="{ height: `calc(100vh - 340px)` }"
              :max-height="'calc(100vh - 390px)'"
              :checked-row-keys="ymsSelectedIds"
              @update:checked-row-keys="(ids:number[])=>ymsSelectedIds = ids"
            />
            <n-pagination
              :showSizePicker="true"
              :pageSizes="[20, 50, 100, 200, 500, 1000]"
              v-model:page="ymsTable.pagination.pageIndex"
              v-model:pageSize="ymsTable.pagination.pageSize"
              :item-count="ymsTable.pagination.total"
              :prefix="ymsTable.pagination.prefix"
              @update:page="ymsTable.pagination.onChange"
              @update:pageSize="ymsTable.pagination.onUpdatePageSize"
            />
          </div>
        </div>
      </template>
    </PageLayout>

    <DwDialog
      v-model:visible="dwVisible"
      :dialog-info="dwDialogInfo"
      :selectedLength="dwSelectedIds?.length"
      @submit="dwReRunSubmit"
    />
    <YmsDialog
      v-model:visible="ymsVisible"
      :dialog-info="ymsDialogInfo"
      :selectedLength="ymsSelectedIds?.length"
      @submit="ymsReRunSubmit"
    />
  </n-spin>
</template>

<script setup lang="ts">
import PageLayout from '../common/page-layout.vue';
import DwDialog from './dw-dialog.vue';
import YmsDialog from './yms-dialog.vue';
import SearchList from '@/components/search-list/search-list.vue';
import {
  StatusEnum,
  DW_SEARCH_LIST,
  DW_COLUMNS,
  DW_DROPDOWN_OPTIONS,
  YMS_SEARCH_LIST,
  YMS_COLUMNS,
  YMS_DROPDOWN_OPTIONS,
} from './data';
import { useMessage, PaginationInfo } from 'naive-ui';
import { SearchParam } from '@/types/common';
import { getDwList, getYmsList, rerunDmt, rerunDw, rerunYms } from '@/service/task';
import { DialogInfo, DmtTaskDetailVo, DW_ListItem, DW_RerunParam, YMS_ListItem } from '@/types/task';
import { SUCCESS_CODE } from '@/utils/http';
import { cloneDeep } from 'lodash';

const tabRef = ref();
const searchRef = ref();
const activeTab = ref('dw');
const dwVisible = ref(false);
const ymsVisible = ref(false);
const pageLoading = ref(false);
const message = useMessage();

//  搜索信息
const searchParam = ref<SearchParam>({
  pageSize: 20,
  pageIndex: 1,
});
//  选中行
const dwSelectedIds = ref<number[]>([]);
const ymsSelectedIds = ref<number[]>([]);

//  分配搜索、表头、批量列表
const renderObj = computed(() => {
  const searchList = activeTab.value === 'dw' ? DW_SEARCH_LIST : YMS_SEARCH_LIST;
  const columns = activeTab.value === 'dw' ? DW_COLUMNS(reRun, getList, message) : YMS_COLUMNS(reRun);
  const dropdownOptions = activeTab.value === 'dw' ? DW_DROPDOWN_OPTIONS : YMS_DROPDOWN_OPTIONS;

  return {
    columns: columns,
    searchList: searchList,
    dropdownOptions: dropdownOptions,
  };
});

//  批量置灰
const disabled = computed(() => {
  if (activeTab.value === 'dw') {
    return dwSelectedIds.value?.some(
      (id) => dwTable.data?.find((item) => item.id === id)?.processStatus !== StatusEnum.FAIL,
    );
  } else {
    return ymsSelectedIds.value?.some(
      (id) => ymsTable.data?.find((item) => item.id === id)?.processStatus !== StatusEnum.FAIL,
    );
  }
});

//  弹窗信息
const dwDialogInfo = reactive<DialogInfo>({
  isSingle: true,
});
const ymsDialogInfo = reactive<DialogInfo>({
  isSingle: true,
});
//  DW
const dwTable = reactive<{ data: DW_ListItem[]; pagination: any }>({
  data: [],
  pagination: {
    total: 0,
    pageSize: 20,
    pageIndex: 1,
    prefix: (info: PaginationInfo) => {
      return `共${info.itemCount}条`;
    },
    onChange: (page: number) => {
      dwTable.pagination.pageIndex = page;
      getList({ ...searchParam.value, pageIndex: page });
    },
    onUpdatePageSize: (pageSize: number) => {
      dwTable.pagination.pageIndex = 1;
      dwTable.pagination.pageSize = pageSize;
      getList({ ...searchParam.value, pageSize: pageSize, pageIndex: 1 });
    },
  },
});
//  YMS
const ymsTable = reactive<{ data: YMS_ListItem[]; pagination: any }>({
  data: [],
  pagination: {
    total: 0,
    pageSize: 20,
    pageIndex: 1,
    prefix: (info: PaginationInfo) => {
      return `共${info.itemCount}条`;
    },
    onChange: (page: number) => {
      ymsTable.pagination.pageIndex = page;
      getList({ ...searchParam.value, pageIndex: page });
    },
    onUpdatePageSize: (pageSize: number) => {
      ymsTable.pagination.pageIndex = 1;
      ymsTable.pagination.pageSize = pageSize;
      getList({ ...searchParam.value, pageSize: pageSize, pageIndex: 1 });
    },
  },
});

//  tab切换
const tabChange = (tab: string) => {
  activeTab.value = tab;
  //  *切换时不清空搜索项
  // searchRef.value.reset();
};
// 单个重跑
const reRun = (row: YMS_ListItem | DW_ListItem) => {
  if (activeTab.value === 'dw') {
    dwDialogInfo.isSingle = true;
    dwDialogInfo.ids = [row.id];
    dwVisible.value = true;
  } else {
    ymsDialogInfo.isSingle = true;
    ymsDialogInfo.row = row as YMS_ListItem;
    ymsDialogInfo.ids = [row.id];
    ymsVisible.value = true;
  }
};
// 批量操作
const dropdownSelected = () => {
  if (activeTab.value === 'dw') {
    if (dwSelectedIds.value?.length < 1) {
      message.warning('请勾选!');
      return;
    }
    if (
      dwSelectedIds.value?.some((id) => {
        return dwTable.data?.find((item) => item.id === id)?.processStatus !== StatusEnum.FAIL;
      })
    ) {
      message.warning('存在未失败的任务!');
      return;
    }

    dwDialogInfo.isSingle = false;
    dwDialogInfo.ids = dwSelectedIds.value;
    dwDialogInfo.searchParam = searchParam.value;
    dwVisible.value = true;
  } else {
    if (ymsSelectedIds.value?.length < 1) {
      message.warning('请勾选!');
      return;
    }
    if (
      ymsSelectedIds.value?.some((id) => {
        return ymsTable.data?.find((item) => item.id === id)?.processStatus !== StatusEnum.FAIL;
      })
    ) {
      message.warning('存在未失败的任务!');
      return;
    }

    ymsDialogInfo.isSingle = false;
    ymsDialogInfo.ids = ymsSelectedIds.value;
    ymsDialogInfo.searchParam = searchParam.value;

    ymsVisible.value = true;
  }
};
//  重跑提交
const dwReRunSubmit = async (params: DW_RerunParam) => {
  pageLoading.value = true;
  const res = await rerunDw(params);
  pageLoading.value = false;
  if (res.data.code === SUCCESS_CODE) {
    message.success('重试成功!');
    dwVisible.value = false;
  }
};
const ymsReRunSubmit = async (params: { id: number; dmtTaskDetailVos: DmtTaskDetailVo[] } | { ids: number[] }) => {
  pageLoading.value = true;
  let res;
  if ('id' in params) {
    res = await rerunDmt(params);
  } else {
    res = await rerunYms(params);
  }
  pageLoading.value = false;

  if (res.data.code === SUCCESS_CODE) {
    message.success('重试成功!');
    ymsVisible.value = false;
  }
};

//  获取列表
const getList = async (param?: SearchParam) => {
  let sParam;
  if (param) {
    const paramObj = cloneDeep(param);
    sParam = { ...paramObj, pageIndex: paramObj.pageIndex ?? 1, pageSize: paramObj.pageSize ?? 20 };
    searchParam.value = cloneDeep(sParam);
  } else {
    sParam = {
      ...searchParam.value,
    };
  }

  if (activeTab.value === 'dw') {
    const res = await getDwList(sParam);
    if (res.data.code === SUCCESS_CODE) {
      const rData = res.data.data;
      dwTable.data = rData.data;

      dwTable.pagination.total = rData.total;
      dwTable.pagination.pageIndex = rData.pageIndex;
      dwTable.pagination.pageSize = rData.pageSize;

      dwSelectedIds.value = [];
    }
  } else {
    const res = await getYmsList(sParam);
    if (res.data.code === SUCCESS_CODE) {
      const rData = res.data.data;
      ymsTable.data = rData.data;

      ymsTable.pagination.total = rData.total;
      ymsTable.pagination.pageIndex = rData.pageIndex;
      ymsTable.pagination.pageSize = rData.pageSize;

      ymsSelectedIds.value = [];
    }
  }
};

onMounted(() => {
  getList(searchParam.value);
});
</script>

<style scoped lang="scss">
.tabBox {
  flex: 1;
  background: #fff;
  border-radius: 4px;
  overflow: hidden;
  padding: 16px;

  .tableBox {
    height: 100%;

    > .tableHead {
      > .n-button {
        margin-left: auto;
        margin-bottom: 10px;
      }
    }

    > .n-data-table {
      height: calc(100vh - 410px);
      display: flex;
      flex-direction: column;
    }

    > .n-pagination {
      margin: 16px 0 0 auto;
    }
  }
}
</style>
