<template>
  <n-modal
    :style="{ width: props.dialogInfo.isSingle ? '1200px' : '450px' }"
    :show="visible"
    :mask-closable="false"
    preset="dialog"
    title="DW任务重试"
    positive-text="确认"
    negative-text="取消"
    display-directive="'if'"
    @close="clear"
    @negative-click="clear"
    @positive-click="subParam"
  >
    <div class="dwSingleBox v" v-if="props.dialogInfo.isSingle">
      <div class="infoBox h">
        <div class="item h" v-for="item in DW_SINGLE_INFO_LIST" :key="item.value">
          <span>{{ item.label }}</span>
          <n-input v-model:value="singleInfoObj[item.value]" disabled />
        </div>
      </div>

      <div class="errorBox h">
        <span>error_message</span>
        <n-input
          v-model:value="singleInfoObj['errorMessage']"
          disabled
          type="textarea"
          placeholder=""
          :autosize="{
            minRows: 8,
            maxRows: 8,
          }"
        />
      </div>

      <n-data-table :columns="INFO_COLUMNS" :data="data" />
    </div>

    <div class="dwBox" v-else>
      <div class="infoBox v">
        <div class="item h" v-for="item in DW_INFO_LIST" :key="item.value">
          <span>{{ item.label }}</span>
          <n-select v-model:value="infoObj[item.value]" disabled multiple placeholder="" />
        </div>

        <div class="item h">
          <span>重试的记录数: </span>
          <div>{{ props.selectedLength }}</div>
        </div>
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { DetailInfo, DialogInfo } from '@/types/task';
import { DW_SINGLE_INFO_LIST, DW_INFO_LIST, INFO_COLUMNS, ReRunFlagEnum } from './data';
import { getDetail } from '@/service/task';
import { SUCCESS_CODE } from '@/utils/http';
import { SearchParam } from '@/types/common';

const props = defineProps<{ visible: boolean; dialogInfo: DialogInfo; selectedLength: number }>();
const emits = defineEmits(['update:visible', 'submit']);
const visible = useVModel(props, 'visible', emits);

const data = ref([{}]);
const infoObj = ref<SearchParam>({});
const singleInfoObj = ref<DetailInfo>({});

//  表单提交
const subParam = () => {
  const param = {
    ...data.value[0],
    ids: props.dialogInfo.ids,
    flag: props.dialogInfo.isSingle ? ReRunFlagEnum.SINGLE_RERUN : ReRunFlagEnum.BATCH_RERUN,
  };
  emits('submit', param);
};

const clear = () => {
  data.value = [{}];
  visible.value = false;
};
const getInfo = async (id: number) => {
  const res = await getDetail(id);

  if (res.data.code === SUCCESS_CODE) {
    singleInfoObj.value = { ...res.data.data };
    const { numExecutors, executorCores, executorMemory, driverMemory, parallelism, extraConf } = res.data.data || '';

    data.value = [
      {
        numExecutors: numExecutors,
        executorCores: executorCores,
        executorMemory: executorMemory,
        driverMemory: driverMemory,
        parallelism: parallelism,
        extraConf: extraConf,
      },
    ];
  }
};

const setSearch = () => {
  infoObj.value = { ...props.dialogInfo.searchParam };
};

watchDebounced(visible, () => {
  if (visible.value && props.dialogInfo?.ids && props.dialogInfo.isSingle) {
    getInfo(props.dialogInfo?.ids[0]);
  }

  if (visible.value && !props.dialogInfo.isSingle) {
    setSearch();
  }
});
</script>

<style scoped lang="scss">
.dwSingleBox {
  // height: 580px;
  padding: 0 16px;
  // max-height: 580px;
  // overflow: auto;

  > .infoBox {
    width: 100%;
    flex-wrap: wrap;
    margin-bottom: 10px;

    > .item {
      margin: 8px;
      > span {
        width: 113px;
        flex-shrink: 0;
        text-align: right;
        margin: auto 8px auto 0;
      }
      > .n-input,
      .n-select {
        width: 230px;
      }
    }
  }

  > .errorBox {
    height: 200px;
    border-top: 1px solid #ccc;
    padding-top: 20px;

    > span {
      width: 120px;
      flex-shrink: 0;
      text-align: right;
      margin: 0 8px auto 0;
    }
    > .n-input {
      width: 966px;
    }
  }

  > .n-data-table {
    margin-top: 20px;
  }
}

.dwBox {
  // height: 480px;
  padding: 0 16px;

  > .infoBox {
    width: 100%;
    flex-wrap: wrap;
    margin-bottom: 10px;

    > .item {
      margin: 8px;
      > span {
        width: 103px;
        flex-shrink: 0;
        text-align: right;
        margin: auto 8px auto 0;
      }
      > .n-input,
      .n-select {
        width: 230px;
      }
    }
  }
}
</style>
