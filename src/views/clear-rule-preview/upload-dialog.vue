<template>
  <n-modal
    :type="uploadError ? 'error' : 'info'"
    :style="{ width: uploadError ? '320px' : '1300px' }"
    :show="visible"
    :mask-closable="false"
    preset="dialog"
    :title="uploadError ? '上传失败' : '上传文件'"
    display-directive="'if'"
    @close="close"
  >
    <n-form ref="formRef" inline :model="form" :rules="rules" :show-feedback="true" v-if="!uploadError">
      <div class="upload v">
        <div class="row h">
          <div class="item h">
            <span>Factory:</span>
            <n-form-item path="factory">
              <n-input v-model:value="form.factory" :maxlength="50" />
            </n-form-item>
          </div>
          <div class="item h">
            <span>Factory Site:</span>
            <n-form-item path="factorySite">
              <n-input v-model:value="form.factorySite" :maxlength="50" />
            </n-form-item>
          </div>
          <div class="item h">
            <span>Given Test Area:</span>
            <n-form-item path="givenTestArea">
              <n-select v-model:value="form.givenTestArea" :options="GivenOptions" />
            </n-form-item>
          </div>
          <div class="item h">
            <span>File Category:</span>
            <n-form-item path="fileCategory">
              <n-select v-model:value="form.fileCategory" :options="FileCategoryOptions" />
            </n-form-item>
          </div>
        </div>

        <div class="row h">
          <div class="item h">
            <span>Remote Origin File Path:</span>
            <n-form-item path="remoteOriginFilePath">
              <n-input v-model:value="form.remoteOriginFilePath" class="path" :maxlength="600" />
            </n-form-item>
          </div>
        </div>

        <div class="row h">
          <n-upload :on-update:file-list="uploadFile" multiple>
            <n-button>选择文件</n-button>
          </n-upload>
        </div>

        <n-button class="submit" @click="submit">上传</n-button>
      </div>
    </n-form>

    <div class="loading" v-if="loading">
      <n-spin size="large" />
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { UploadParam } from '@/types/clearRulePreview';
import { GivenOptions, FileCategoryOptions } from './data';
import { UploadFileInfo, useMessage } from 'naive-ui';
import { fileUpload } from '@/service/clearRulePreview';
import { SUCCESS_CODE } from '@/utils/http';

const formRef = ref();
const message = useMessage();

const props = defineProps<{ visible: boolean; reloadTable: () => void }>();
const emits = defineEmits(['update:visible']);
const visible = useVModel(props, 'visible', emits);

const loading = ref(false);
const uploadError = ref(false);
const uploadIndex = ref<number>(0);
const fileList = shallowRef<UploadFileInfo[]>([]);

const form = ref<UploadParam>({
  factory: '',
  factorySite: '',
  givenTestArea: '',
  fileCategory: '',
  remoteOriginFilePath: '',
});
const rules = ref({
  factory: {
    required: true,
    message: '请输入Factory',
    trigger: ['input', 'blur'],
  },
  factorySite: {
    required: true,
    message: '请输入Factory Site',
    trigger: ['input', 'blur'],
  },
  givenTestArea: {
    required: true,
    message: '请选择Given Test Area',
    trigger: ['input', 'blur'],
  },
  fileCategory: {
    required: true,
    message: '请选择File Category',
    trigger: ['input', 'blur'],
  },
  remoteOriginFilePath: {
    required: true,
    message: '请输入Remote Origin File Path',
    trigger: ['input', 'blur'],
  },
});

const uploadFile = (list: UploadFileInfo[]) => {
  fileList.value = list;
};
const submitApi = async (param: UploadParam) => {
  if (uploadIndex.value < fileList.value?.length) {
    loading.value = true;
    const formData = new FormData();
    for (const [key, value] of Object.entries(param)) {
      formData.append(key, value);
    }
    formData.append('file', fileList.value[uploadIndex.value]?.file as File);

    const res = await fileUpload(formData);
    if (res.data.code === SUCCESS_CODE) {
      uploadIndex.value++;
      submitApi(param);
    } else {
      uploadError.value = true;
    }
  } else {
    form.value = {
      factory: '',
      factorySite: '',
      givenTestArea: '',
      fileCategory: '',
      remoteOriginFilePath: '',
    };
    uploadIndex.value = 0;
    fileList.value = [];
    loading.value = false;
    visible.value = false;
    message.success('文件上传成功!');
    props.reloadTable();
  }
};

const submit = () => {
  formRef.value?.validate((errors: boolean) => {
    if (!errors) {
      if (fileList.value?.length < 1) {
        message.warning('请上传文件!');
      } else if (
        fileList.value?.some((ite) => {
          if (ite?.file?.size && ite?.file?.size > 10000000000) {
            return true;
          }

          return false;
        })
      ) {
        message.warning('上传单文件大小不得超过10G!');
      } else {
        const param = { ...form.value };

        submitApi(param);
      }
    }
  });
};

const close = () => {
  form.value = {
    factory: '',
    factorySite: '',
    givenTestArea: '',
    fileCategory: '',
    remoteOriginFilePath: '',
  };
  loading.value = false;
  fileList.value = [];
  uploadIndex.value = 0;
  uploadError.value = false;
  visible.value = false;
};
</script>

<style scoped lang="scss">
.upload {
  width: 100%;
  height: 500px;
  padding: 0 16px;

  .row {
    margin: 8px 0 0;

    .item {
      margin-right: 16px;

      > span {
        margin: 14px 8px auto 0;
        flex-shrink: 0;
      }

      .n-form-item {
        height: 62px;
        display: flex;
        flex-direction: column;

        .n-select,
        .n-input {
          margin: 12px 0 0;
          width: 160px;
        }
        .path {
          width: calc(100vw - 600px);
          max-width: 800px;
        }
      }
    }

    .n-upload {
      :deep(.n-upload-file-list) {
        max-height: 240px;
        overflow: auto;
      }
    }
  }

  > .submit {
    width: 120px;
    margin: auto auto 0;
  }
}

.loading {
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  background: #00000011;
  position: absolute;

  > div {
    margin: auto;
  }
}

.error {
  top: 0;
  width: 100%;
  height: 30px;
  display: flex;
  background: #fff;

  > span {
    margin: auto;
    font-size: 18px;
  }
}
</style>
