<!-- 展示错误信息的模态框 -->
<template>
  <n-modal
    style="width: 1300px"
    :show="visible"
    :mask-closable="false"
    preset="dialog"
    title="错误信息"
    display-directive="'if'"
    @close="close"
  >
    <div class="errorMsg">
      <div class="row">
        <span>errorMessage:</span>
        <n-input v-bind:value="data.errorMessage" type="textarea" disabled />
      </div>
      <div class="row">
        <span>exceptionMessage:</span>
        <n-input v-bind:value="data.exceptionMessage" type="textarea" disabled />
      </div>
      <div class="row">
        <span>exceptionType:</span>
        <n-input v-bind:value="data.exceptionType" type="textarea" disabled />
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
const props = defineProps<{
  visible: boolean;
  data: {
    errorMessage: string;
    exceptionMessage: string;
    exceptionType: string;
  };
}>();

const visible = useVModel(props, 'visible');

const close = () => {
  visible.value = false;
};
</script>

<style scoped class="scss">
.errorMsg {
  overflow: auto;

  .row {
    margin: 16px 0;
    display: flex;

    > span {
      margin: auto 0 auto 16px;
      width: 160px;
    }
  }
}
</style>
