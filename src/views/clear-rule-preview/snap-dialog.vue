<template>
  <n-modal
    style="width: 1300px"
    :show="visible"
    :mask-closable="false"
    preset="dialog"
    title=""
    display-directive="'if'"
    @close="close"
  >
    <div class="meta">
      <div class="row">
        <span>Setting Snapshot:</span>
        <n-input v-model:value="snapStr" type="textarea" disabled />
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
const props = defineProps<{
  visible: boolean;
  snapJson: string;
}>();
const emits = defineEmits(['update:visible']);
const visible = useVModel(props, 'visible', emits);

const snapStr = computed(() => props.snapJson ?? '');

const close = () => {
  visible.value = false;
};
</script>

<style scoped lang="scss">
.meta {
  max-height: 520px;
  overflow: auto;

  .row {
    margin: 16px 0;
    display: flex;

    > span {
      margin: auto 0 auto 16px;
      width: 160px;
    }
    // > .n-input {
    //   height: 120px;
    // }
  }
}
</style>
