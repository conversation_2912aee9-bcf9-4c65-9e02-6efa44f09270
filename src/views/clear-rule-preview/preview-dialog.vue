<template>
  <n-modal
    style="width: 1300px"
    :show="visible"
    :mask-closable="false"
    preset="dialog"
    title="预览结果对比"
    display-directive="'if'"
    @close="close"
  >
    <n-data-table
      :columns="table.columns"
      :data="table.data"
      bottom-bordered
      :bordered="false"
      :single-line="false"
      :table-layout="'fixed'"
      :scroll-x="(table.columns?.length - 1) * 180 + 230"
      :style="{ height: `320px` }"
      :max-height="'240px'"
    />

    <MetaDataDialog v-model:visible="metaDataVisible" :metaInfo="metaInfo" />
    <SnapDialog v-model:visible="snapDataVisible" :snapJson="snapJson" />
  </n-modal>
</template>

<script setup lang="ts">
import { ConfResult, ListItem } from '@/types/clearRulePreview';
import { DataTableColumns } from 'naive-ui';
import { previewTable } from './data';
import { cloneDeep } from 'lodash';
import MetaDataDialog from './meta-data-dialog.vue';
import SnapDialog from './snap-dialog.vue';

const props = defineProps<{ visible: boolean; selectedIds: number[]; tableData: ListItem[] }>();
const emits = defineEmits(['update:visible']);
const visible = useVModel(props, 'visible', emits);
const metaDataVisible = ref(false);
const snapDataVisible = ref(false);
const snapJson = ref('');
const metaInfo = reactive({
  ruleExpression: '' as string | null,
  metaData: '',
  finalResult: '',
});

const table = shallowReactive({
  data: [] as { [key: string]: any }[],
  columns: [] as DataTableColumns<ConfResult>,
});

const openMetaDialog = (item: ConfResult, metaData: string, finalResult: string) => {
  metaInfo.metaData = metaData;
  metaInfo.finalResult = finalResult;
  metaInfo.ruleExpression = item?.ruleExpression;
  metaDataVisible.value = true;
};
const openSnapDialog = (json: string) => {
  snapJson.value = json;
  snapDataVisible.value = true;
};

const resetTable = () => {
  const { columns, data } = previewTable(
    cloneDeep(props.selectedIds),
    cloneDeep(props.tableData),
    openMetaDialog,
    openSnapDialog,
  );
  table.columns = columns;
  table.data = cloneDeep(data);
};

const close = () => {
  table.data = [{ fileName: 456 }];
  visible.value = false;
};

watch(visible, () => {
  if (visible.value) {
    resetTable();
  }
});
</script>

<style scoped lang="scss">
.upload {
  width: 100%;
  height: 500px;
  padding: 0 16px;

  .row {
    margin: 8px 0 0;

    .item {
      margin-right: 16px;

      > span {
        margin: 14px 8px auto 0;
        flex-shrink: 0;
      }

      .n-form-item {
        height: 62px;
        display: flex;
        flex-direction: column;

        .n-select,
        .n-input {
          margin: 12px 0 0;
          width: 160px;
        }
        .path {
          width: 800px;
        }
      }
    }

    .n-upload {
      :deep(.n-upload-file-list) {
        max-height: 240px;
        overflow: auto;
      }
    }
  }

  > .submit {
    width: 120px;
    margin: auto auto 0;
  }
}
</style>
