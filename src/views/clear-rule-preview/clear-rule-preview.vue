<template>
  <div class="clearRulePreview">
    <SearchList :searchList="SEARCH_LIST" @search="getCurrentList" />
    <div class="tabBox v" ref="tabRef">
      <div class="tableBox v">
        <div class="tableHead h">
          <n-button class="fixed_btn" :disabled="selectedIds?.length < 1" @click="previewVisible = true">
            预览结果对比
          </n-button>
          <n-button class="fixed_btn" @click="uploadVisible = true">上传文件</n-button>
          <n-dropdown
            trigger="hover"
            :options="renderObj.dropdownOptions"
            @select="dropdownSelected"
            :disabled="disabled"
          >
            <n-button type="primary" class="fixed_btn" :disabled="disabled">批量操作</n-button>
          </n-dropdown>
        </div>
        <n-data-table
          :columns="renderObj.columns"
          :data="currentTable.data"
          bottom-bordered
          :row-key="(row: ListItem) => row.id"
          :bordered="true"
          :single-line="false"
          :table-layout="'fixed'"
          :scroll-x="3040"
          :style="{ height: `calc(100vh - 340px)` }"
          :max-height="'calc(100vh - 420px)'"
          :checked-row-keys="selectedIds"
          @update:checked-row-keys="(ids: number[]) => selectedIds = ids"
        />
        <n-pagination
          :showSizePicker="true"
          :pageSizes="[50, 100, 200, 500, 1000]"
          v-model:page="currentTable.pagination.pageIndex"
          v-model:pageSize="currentTable.pagination.pageSize"
          :item-count="currentTable.pagination.total"
          :prefix="currentTable.pagination.prefix"
          @update:page="currentTable.pagination.onChange"
          @update:pageSize="currentTable.pagination.onUpdatePageSize"
        />
      </div>
    </div>
  </div>

  <UploadDialog v-model:visible="uploadVisible" :reloadTable="() => getCurrentList(searchParam)" />
  <PreviewDialog v-model:visible="previewVisible" :selectedIds="selectedIds" :tableData="currentTable.data" />
  <ErrorMessageDialog :data="errorData" v-model:visible="errorMessageVisible" />
</template>

<script setup lang="ts">
import { getList, retry } from '@/service/clearRulePreview';
import { SEARCH_LIST, StatusEnum, COLUMNS, DROPDOWN_OPTIONS } from './data';
import { SearchParam } from '@/types/common';
import { ListItem } from '@/types/clearRulePreview';
import { useMessage, PaginationInfo } from 'naive-ui';
import { SUCCESS_CODE } from '@/utils/http';
import { downloadFile } from '@/service/clearRulePreview';
import { cloneDeep } from 'lodash';
import UploadDialog from './upload-dialog.vue';
import PreviewDialog from './preview-dialog.vue';
import ErrorMessageDialog from './error-message-dialog.vue';

const message = useMessage();

const uploadVisible = ref(false);
const previewVisible = ref(false);

// 错误信息
const errorMessageVisible = ref(false);
const errorData = {
  errorMessage: '',
  exceptionMessage: '',
  exceptionType: '',
};

//  搜索信息
const searchParam = ref<SearchParam>({});

//  选中行
const selectedIds = ref<number[]>([]);

//  分配搜索、表头、批量列表
const renderObj = computed(() => {
  return {
    columns: COLUMNS(retryBtn, downloadBtn, showErrorMessage),
    searchList: SEARCH_LIST,
    dropdownOptions: DROPDOWN_OPTIONS,
  };
});

//  批量操作按钮置灰
const disabled = computed(() => {
  return selectedIds.value?.some((id) => {
    const item = currentTable.data?.find((item) => item.id === id);
    return !(item?.processStatus === StatusEnum.FAIL || item?.processStatus === StatusEnum.SUCCESS);
  });
});

// 展示错误信息
const showErrorMessage = (errorMessage: string, exceptionMessage: string, exceptionType: string) => {
  errorData.errorMessage = errorMessage;
  errorData.exceptionMessage = exceptionMessage;
  errorData.exceptionType = exceptionType;
  errorMessageVisible.value = true;
};

// 批量操作
const dropdownSelected = async () => {
  if (selectedIds.value?.length < 1) {
    message.warning('请勾选!');
    return;
  }
  if (
    selectedIds.value?.some((id) => {
      const item = currentTable.data?.find((item) => item.id === id);
      return !(item?.processStatus === StatusEnum.FAIL || item?.processStatus === StatusEnum.SUCCESS);
    })
  ) {
    message.warning('存在未完成的任务!');
    return;
  }

  selectedIds.value.forEach((id) => {
    const item = currentTable.data?.find((item) => item.id === id);
    if (item) {
      console.log(id + ' is loading');
      item.loading = true;
    }
  });

  const res = await retry(selectedIds.value);

  if (res.data.code === SUCCESS_CODE) {
    message.success('执行成功!');
    getCurrentList(searchParam.value);
  }
};

// 重试
const retryBtn = async (id: number) => {
  currentTable.data?.map((item) => {
    if (item.id === id) item.loading = true;
  });
  const res = await retry([id]);
  if (res.data.code === SUCCESS_CODE) {
    message.success('执行成功!');
    getCurrentList(searchParam.value);
  }
};

// 下载明细信息
const downloadBtn = async (id: number) => {
  currentTable.data?.map((item) => {
    if (item.id === id) {
      item.downloadLoading = true;
    }
  });
  await downloadFile(id);
  currentTable.data?.map((item) => {
    if (item.id === id) {
      item.downloadLoading = false;
    }
  });
};

const currentTable = reactive<{ data: ListItem[]; pagination: any }>({
  data: [],
  pagination: {
    total: 0,
    pageSize: 50,
    pageIndex: 1,
    prefix: (info: PaginationInfo) => {
      return `共${info.itemCount}条`;
    },
    onChange: (page: number) => {
      currentTable.pagination.pageIndex = page;
      getCurrentList({ ...searchParam.value, pageIndex: page });
    },
    onUpdatePageSize: (pageSize: number) => {
      currentTable.pagination.pageIndex = 1;
      currentTable.pagination.pageSize = pageSize;
      getCurrentList({ ...searchParam.value, pageIndex: 1 });
    },
  },
});

//  获取列表
const getCurrentList = async (param: SearchParam) => {
  searchParam.value = cloneDeep(param);
  const pageSize = currentTable.pagination.pageSize;
  const res = await getList({ ...param, pageSize });

  if (res.data.code === SUCCESS_CODE) {
    const rData = res.data.data;
    currentTable.data = rData.data;

    currentTable.pagination.total = rData.total;
    currentTable.pagination.pageIndex = rData.pageIndex;
    currentTable.pagination.pageSize = rData.pageSize;

    selectedIds.value = [];
  }
};

onMounted(() => {
  getCurrentList(searchParam.value);
});
</script>

<style scoped class="scss">
.clearRulePreview {
  height: 100%;
  padding: 12px 16px 0 16px;

  .tabBox {
    flex: 1;
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    padding: 16px;

    .tableBox {
      height: 100%;

      > .tableHead {
        display: block;
        text-align: right;

        > .n-button {
          margin: 0 0 10px 10px;
        }
      }

      > .n-data-table {
        height: calc(100vh - 410px);
        display: flex;
        flex-direction: column;
      }

      > .n-pagination {
        margin: 16px 0 0 auto;
      }
    }
  }
}
</style>
