<template>
  <n-modal
    style="width: 1300px"
    :show="visible"
    :mask-closable="false"
    preset="dialog"
    title=""
    display-directive="'if'"
    @close="close"
  >
    <div class="meta">
      <div class="row">
        <span>Meta Data:</span>
        <n-input v-model:value="metaData" type="textarea" disabled placeholder="''" />
      </div>
      <div class="row">
        <span>Rule Expression:</span>
        <n-input v-model:value="ruleExpression" type="textarea" disabled placeholder="" />
      </div>
      <div class="row">
        <span>Final Result:</span>
        <n-input v-model:value="finalResult" disabled placeholder="" />
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
const props = defineProps<{
  visible: boolean;
  metaInfo: { metaData: string; ruleExpression: string | null; finalResult: string };
}>();
const emits = defineEmits(['update:visible']);
const visible = useVModel(props, 'visible', emits);

const metaData = computed(() => JSON.stringify(props.metaInfo?.metaData ?? '', null, 2));
const ruleExpression = computed(() => props.metaInfo?.ruleExpression);
const finalResult = computed(() => props.metaInfo?.finalResult ?? '');

const close = () => {
  visible.value = false;
};
</script>

<style scoped lang="scss">
.meta {
  max-height: 520px;
  overflow: auto;

  .row {
    margin: 16px 0;
    display: flex;

    > span {
      margin: auto 0 auto 16px;
      width: 160px;
    }
    // > .n-input {
    //   height: 120px;
    // }
  }
}
</style>
