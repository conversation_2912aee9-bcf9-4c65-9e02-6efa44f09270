import { filterDropdown } from '@/service/clearRulePreview';
import { SearchItem } from '@/types/common';
import { ConfResult, ListItem } from '@/types/clearRulePreview';
import { DataTableColumns, NButton, NTooltip } from 'naive-ui';
import { isNull, isNumber, isString } from 'lodash';

export enum StatusEnum {
  SUCCESS = 'SUCCESS',
  FAIL = 'FAIL',
  PROCESSING = 'PROCESSING',
  CREATE = 'CREATE',
}

//  搜索列表
export const SEARCH_LIST: SearchItem[] = [
  {
    label: 'Test Area',
    type: 'select',
    key: 'testArea',
    api: filterDropdown,
  },
  {
    label: 'Device ID',
    type: 'select',
    key: 'deviceId',
    api: filterDropdown,
  },
  {
    label: 'Lot ID',
    type: 'select',
    key: 'lotId',
    api: filterDropdown,
  },
  {
    label: 'Wafer No',
    type: 'select',
    key: 'waferNo',
    api: filterDropdown,
  },
  {
    label: 'Lot Type',
    type: 'select',
    key: 'lotType',
    api: filterDropdown,
  },
  {
    label: 'Test Stage',
    type: 'select',
    key: 'testStage',
    api: filterDropdown,
  },
  {
    label: 'Factory',
    type: 'select',
    key: 'factory',
    api: filterDropdown,
  },
  {
    label: 'Factory Site',
    type: 'select',
    key: 'factorySite',
    api: filterDropdown,
  },
  {
    label: 'Sblot Id',
    type: 'select',
    key: 'sblotId',
    api: filterDropdown,
  },
  {
    label: 'File Category',
    type: 'select',
    key: 'fileCategory',
    options: [
      {
        label: 'STDF',
        value: 'STDF',
      },
      {
        label: 'RAW_DATA',
        value: 'RAW_DATA',
      },
    ],
  },
  {
    label: 'Process Status',
    type: 'select',
    key: 'processStatus',
    multiple: false,
    options: [
      {
        label: 'SUCCESS',
        value: 'SUCCESS',
      },
      {
        label: 'FAIL',
        value: 'FAIL',
      },
      {
        label: 'PROCESSING',
        value: 'PROCESSING',
      },
      {
        label: 'CREATE',
        value: 'CREATE',
      },
    ],
  },
  {
    label: 'Remote Origin File Path',
    type: 'input',
    key: 'remoteOriginFilePath',
    maxLength: 600,
  },
  {
    label: 'Remote Origin File Name',
    type: 'input',
    key: 'remoteOriginFileName',
    maxLength: 256,
  },
  {
    label: 'File Name',
    type: 'input',
    key: 'fileName',
    maxLength: 256,
  },
  {
    label: 'Exception Type',
    type: 'select',
    key: 'exceptionType',
    api: filterDropdown,
  },
  {
    label: 'Update Time',
    type: 'datePicker',
    key: 'time',
  },
];

//  表头
export const COLUMNS: (
  retryBtn: (id: number) => void,
  downloadBtn: (id: number) => void,
  showErrorMessage: (errorMsg: string, exceptionMessage: string, exceptionType: string) => void,
) => DataTableColumns<ListItem> = (retryBtn, downloadBtn, showErrorMessage) => [
  {
    type: 'selection',
    fixed: 'left',
  },
  {
    title: 'File Name',
    key: 'fileName',
    resizable: true,
    width: 160,
  },
  {
    title: 'Remote Origin File Path',
    key: 'remoteOriginFilePath',
    resizable: true,
    width: 200,
  },
  {
    title: 'Remote Origin File Name',
    key: 'remoteOriginFileName',
    resizable: true,
    width: 200,
  },
  {
    title: 'File Category',
    key: 'fileCategory',
    resizable: true,
    width: 160,
  },
  {
    title: 'Factory',
    key: 'factory',
    resizable: true,
    width: 160,
  },
  {
    title: 'Factory Site',
    key: 'factorySite',
    resizable: true,
    width: 160,
  },
  {
    title: 'Given Test Area',
    key: 'givenTestArea',
    resizable: true,
    width: 160,
  },
  {
    title: 'Test Area',
    key: 'testArea',
    resizable: true,
    width: 160,
  },
  {
    title: 'Device Id',
    key: 'deviceId',
    resizable: true,
    width: 160,
  },
  {
    title: 'Lot Id',
    key: 'lotId',
    resizable: true,
    width: 160,
  },
  {
    title: 'Wafer No',
    key: 'waferNo',
    resizable: true,
    width: 160,
  },
  {
    title: 'Lot Type',
    key: 'lotType',
    resizable: true,
    width: 160,
  },
  {
    title: 'Test Stage',
    key: 'testStage',
    resizable: true,
    width: 160,
  },
  {
    title: 'Sblot Id',
    key: 'sblotId',
    resizable: true,
    width: 160,
  },
  {
    title: 'Create Time',
    key: 'createTime',
    resizable: true,
    width: 160,
  },
  {
    title: 'Update Time',
    fixed: 'right',
    key: 'updateTime',
    resizable: true,
    width: 160,
  },
  {
    title: 'Process Status',
    fixed: 'right',
    key: 'processStatus',
    resizable: true,
    width: 160,
    render: (rowData) => {
      if (rowData?.processStatus === StatusEnum.SUCCESS) {
        return <span style={{ color: 'rgb(25,160,88)' }}>SUCCESS</span>;
      } else if (rowData?.processStatus === StatusEnum.PROCESSING) {
        return <span style={{ color: 'rgb(240,160,32)' }}>PROCESSING</span>;
      } else if (rowData?.processStatus === StatusEnum.FAIL) {
        return (
          <span
            style={{ color: 'rgb(208,48,80)', cursor: 'pointer' }}
            onClick={() => showErrorMessage(rowData.errorMessage, rowData.exceptionMessage, rowData.exceptionType)}
          >
            FAIL
          </span>
        );
      } else {
        return <span>CREATE</span>;
      }
    },
  },
  {
    title: '操作',
    key: 'tool',
    width: 200,
    fixed: 'right',
    render: (rowData) => {
      return (
        <div style={{ display: 'flex' }}>
          <NButton
            type="info"
            text
            style={{ width: '28px', marginRight: '40px' }}
            onClick={() => retryBtn(rowData.id)}
            loading={rowData.loading}
            disabled={!(rowData.processStatus === StatusEnum.SUCCESS || rowData.processStatus === StatusEnum.FAIL)}
          >
            重试
          </NButton>

          <NButton
            type="info"
            text
            style={{ width: '84px' }}
            loading={rowData.downloadLoading}
            onClick={() => downloadBtn(rowData.id)}
            disabled={!(rowData.processStatus === StatusEnum.SUCCESS && rowData.previewDetailFileHdfsPath)}
          >
            下载明细信息
          </NButton>
        </div>
      );
    },
  },
];

//  批量操作列表
export const DROPDOWN_OPTIONS = [
  {
    label: '批量重试',
    key: 'reRun',
  },
];

//  -------------------------------- 文件上传弹窗 -------------------------------
export const GivenOptions = [
  {
    label: 'CP',
    value: 'CP',
  },
  {
    label: 'FT',
    value: 'FT',
  },
];
export const FileCategoryOptions = [
  {
    label: 'STDF',
    value: 'STDF',
  },
  {
    label: 'RAW_DATA',
    value: 'RAW_DATA',
  },
];

//  -------------------------------- 预览结果对比 -------------------------------
export const previewTable = (
  selectedIds: number[],
  tableData: ListItem[],
  openMetaDialog: (item: ConfResult, metaData: string, finalResult: string) => void,
  openSnapDialog: (snapJson: string) => void,
) => {
  const tableObj = {
    columns: [] as DataTableColumns<ConfResult>,
    data: [] as { [key: string]: any }[],
  };

  tableData?.map((item) => {
    if (selectedIds?.some((id) => id === item.id)) {
      try {
        const obj: { confResults: ConfResult[]; metaData: string; settingSnapshot: string } = JSON.parse(
          item.previewResult,
        );
        const snapJson = JSON.stringify(obj?.settingSnapshot ?? '', null, 2);
        const dataObj: { [key: string]: any } = {
          fileName: item.fileName,
        };
        const columns: DataTableColumns<ConfResult> = [
          {
            title: 'fileName',
            key: 'fileName',
            fixed: 'left',
            resizable: true,
            width: 210,
          },
        ];

        obj.confResults?.map((ite) => {
          if (!ite?.tip) {
            columns.push({
              title: ite.field,
              key: ite.field,
              resizable: true,
              width: 180,
              render: (rowData: any) => {
                return (
                  <div
                    onClick={() => openMetaDialog(ite, obj.metaData, rowData?.[ite.field])}
                    style={{ cursor: 'pointer', height: '28px' }}
                  >
                    {rowData?.[ite.field]}
                  </div>
                );
              },
            });
          } else {
            columns.push({
              title: () => {
                return (
                  <div>
                    <span style={{ color: 'red' }}>{ite.field}</span>
                  </div>
                );
              },
              key: ite.field,
              resizable: true,
              width: 180,
              render: (rowData: any) => {
                return (
                  <NTooltip>
                    {{
                      trigger: () => (
                        <div
                          style={{ color: 'red', height: '28px', cursor: 'pointer' }}
                          onClick={() => openMetaDialog(ite, obj.metaData, rowData?.[ite.field])}
                        >
                          {rowData?.[ite.field]}
                        </div>
                      ),
                      default: () => <span>{ite?.tip}</span>,
                    }}
                  </NTooltip>
                );
              },
            });
          }

          dataObj[ite.field] =
            isString(ite.value) || isNull(ite.value) || isNumber(ite.value) ? ite.value : ite.value + '';
        });

        columns.push({
          title: 'settingSnapshot',
          key: 'settingSnapshot',
          fixed: 'right',
          resizable: true,
          width: 160,
          render: () => {
            return (
              <NButton type="info" text onClick={() => openSnapDialog(snapJson)}>
                查看
              </NButton>
            );
          },
        });

        tableObj.columns = columns;
        tableObj.data.push(dataObj);
      } catch (err) {
        console.error(err);
      }
    }
  });

  return tableObj;
};
