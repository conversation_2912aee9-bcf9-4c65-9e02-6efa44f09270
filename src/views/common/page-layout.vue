<template>
  <div class="pageLayout v">
    <div class="header h">
      <slot name="header"></slot>
    </div>

    <div class="content h">
      <slot name="content"></slot>
    </div>
  </div>
</template>

<style scoped lang="scss">
.pageLayout {
  height: 100%;
  padding: 0 16px 16px;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .header {
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
  }
  
  .content {
    flex: 1;
    flex-direction: column;
    overflow: hidden;
    min-height: 0;
  }
}
</style>