<template>
  <div class="priorityBox">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="任务优先级调整" :name="TAB_ENUM.TASK" />
      <el-tab-pane label="自动入库优先级配置" :name="TAB_ENUM.AUTO" />
    </el-tabs>

    <component :is="dom" />
  </div>
</template>

<script setup lang="ts">
import AutoPage from './AutoPage/AutoPage.vue';
import TaskPage from './TaskPage/TaskPage.vue';
import { TAB_ENUM } from './typing';

const activeTab = ref(TAB_ENUM.TASK);
const dom = computed(() => {
  return activeTab.value === TAB_ENUM.TASK ? TaskPage : AutoPage;
});
</script>

<style lang="scss" scoped>
.priorityBox {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;
  padding: 0 8px;
}
</style>
