// 导入搜索组件相关类型
import { Api } from '@form-create/element-ui';
import { SearchItem, SearchTypeEnum } from '@guwave/components';

// 搜索列表配置
export const SearchList: SearchItem[] = [
  {
    type: SearchTypeEnum.INPUT,
    label: 'Customer', // 客户
    key: 'customer',
  },
  {
    type: SearchTypeEnum.INPUT,
    label: 'Sub Customer', // 子客户
    key: 'subCustomer',
  },
  {
    type: SearchTypeEnum.INPUT,
    label: 'Factory', // 工厂
    key: 'factory',
  },
  {
    type: SearchTypeEnum.INPUT,
    label: 'Factory Site',
    key: 'factorySite',
  },
  {
    type: SearchTypeEnum.SELECT,
    label: 'Test Area', // 测试区域
    key: 'testArea',
    options: [
      { label: 'CP', value: 'CP' },
      { label: 'FT', value: 'FT' },
      { label: 'CP(Map)', value: 'CP(Map)' },
      { label: 'CP(InklessMap)', value: 'CP(InklessMap)' },
      { label: 'BUMP', value: 'BUMP' },
      { label: 'BUMP(Map)', value: 'BUMP(Map)' },
      { label: 'BURNIN', value: 'BURNIN' },
      { label: 'BURNIN(Map)', value: 'BURNIN(Map)' },
      { label: 'WLT', value: 'WLT' },
      { label: 'WLT(Map)', value: 'WLT(Map)' },
      { label: 'REL(Wafer)', value: 'REL(Wafer)' },
      { label: 'REL(Map)', value: 'REL(Map)' },
      { label: 'ASSY(Wafer)', value: 'ASSY(Wafer)' },
      { label: 'ASSY(Map)', value: 'ASSY(Map)' },
      { label: 'ASSY', value: 'ASSY' },
      { label: 'REL', value: 'REL' },
      { label: 'SLT', value: 'SLT' },
      { label: 'MT', value: 'MT' },
      { label: 'NA', value: 'NA' },
      { label: 'CAL', value: 'CAL' },
    ],
  },
  {
    type: SearchTypeEnum.INPUT,
    label: 'Device Id', // 设备ID
    key: 'deviceId',
  },
  {
    type: SearchTypeEnum.INPUT,
    label: 'Test Stage',
    key: 'testStage',
  },
  {
    type: SearchTypeEnum.INPUT,
    label: 'Lot Id',
    key: 'lotId',
  },
  {
    type: SearchTypeEnum.INPUT,
    label: 'Wafer No',
    key: 'waferNo',
  },
  {
    type: SearchTypeEnum.SELECT,
    label: 'Lot Type', // 批次类型
    key: 'lotType',
    options: [
      { label: 'PRODUCTION', value: 'PRODUCTION' }, // 生产批次
      { label: 'ENGINEERING', value: 'ENGINEERING' }, // 工程批次
      { label: 'QUALIFICATION', value: 'QUALIFICATION' }, // 认证批次
      { label: 'EMPTY', value: 'EMPTY' }, // 空批次
    ],
  },
  {
    type: SearchTypeEnum.SELECT,
    label: 'File Category', // 文件类别
    key: 'fileCategory',
    options: [
      { label: 'STDF', value: 'STDF' }, // STDF文件
      { label: 'RAW_DATA', value: 'RAW_DATA' }, // 原始数据
    ],
  },
];

// 表格列配置
export const TableHeader = [
  // Customer
  {
    prop: 'customer',
    label: 'Customer', // 客户
  },
  // Sub Customer
  {
    prop: 'subCustomer',
    label: 'Sub Customer', // 子客户
  },
  {
    prop: 'factory',
    label: 'Factory', // 工厂
  },
  {
    prop: 'factorySite',
    label: 'Factory Site', // 工厂站点
  },
  // Test Area
  {
    prop: 'testArea',
    label: 'Test Area', // 测试区域
  },
  {
    prop: 'deviceId',
    label: 'Device Id', // 设备ID
  },
  {
    prop: 'testStage',
    label: 'Test Stage',
  },
  {
    prop: 'lotId',
    label: 'Lot Id',
  },
  {
    prop: 'waferNo',
    label: 'Wafer No',
  },
  {
    prop: 'lotType',
    label: 'Lot Type', // 批次类型
  },
  {
    prop: 'fileCategory',
    label: 'File Category', // 文件类别
  },
  {
    prop: 'priority',
    label: 'Priority', // 优先级
  },
  {
    prop: 'startTime',
    label: 'Start Time', // 开始时间
  },
  {
    prop: 'ftpModifyTime',
    label: 'Ftp Modify Time', // FTP修改时间
  },
  {
    prop: 'ftpPath',
    label: 'Ftp Path', // FTP路径
  },
  {
    prop: 'createTime',
    label: 'Create Time', // 创建时间
  },
  {
    prop: 'updateTime',
    label: 'Update Time', // 更新时间
  },
];

// 表单配置
export const RULE = [
  // Customer
  {
    type: 'input',
    title: 'Customer',
    field: 'customer',
    col: {
      span: 12,
    },
    props: {
      placeholder: '请输入',
    },
    validate: [{ required: true, message: '请输入' }],
  },
  // Sub Customer
  {
    type: 'input',
    title: 'Sub Customer',
    field: 'subCustomer',
    col: {
      span: 12,
    },
    props: {
      placeholder: '请输入',
    },
    validate: [{ required: true, message: '请输入' }],
  },
  //  Factory
  {
    type: 'input',
    title: 'Factory',
    field: 'factory',
    col: {
      span: 12,
    },
    props: {
      placeholder: '请输入',
    },
    validate: [{ required: true, message: '请输入' }],
  },
  //  FactorySite
  {
    type: 'input',
    title: 'Factory Site',
    field: 'factorySite',
    col: {
      span: 12,
    },
    props: {
      placeholder: '请输入',
    },
    validate: [{ required: true, message: '请输入' }],
  },
  //  Lot Type
  {
    type: 'select',
    title: 'Lot Type',
    field: 'lotType',
    col: {
      span: 12,
    },
    props: {
      placeholder: '请选择',
    },
    validate: [{ required: true, message: '请选择' }],
    options: [
      { label: 'PRODUCTION', value: 'PRODUCTION' }, // 生产批次
      { label: 'ENGINEERING', value: 'ENGINEERING' }, // 工程批次
      { label: 'QUALIFICATION', value: 'QUALIFICATION' }, // 认证批次
      { label: 'EMPTY', value: 'EMPTY' }, // 空批次
    ],
  },
  //  Test Area
  {
    type: 'select',
    title: 'Test Area',
    field: 'testArea',
    col: {
      span: 12,
    },
    props: {
      placeholder: '请选择',
    },
    validate: [{ required: true, message: '请选择' }],
    options: [
      { label: 'CP', value: 'CP' },
      { label: 'FT', value: 'FT' },
      { label: 'CP(Map)', value: 'CP(Map)' },
      { label: 'CP(InklessMap)', value: 'CP(InklessMap)' },
      { label: 'BUMP', value: 'BUMP' },
      { label: 'BUMP(Map)', value: 'BUMP(Map)' },
      { label: 'BURNIN', value: 'BURNIN' },
      { label: 'BURNIN(Map)', value: 'BURNIN(Map)' },
      { label: 'WLT', value: 'WLT' },
      { label: 'WLT(Map)', value: 'WLT(Map)' },
      { label: 'REL(Wafer)', value: 'REL(Wafer)' },
      { label: 'REL(Map)', value: 'REL(Map)' },
      { label: 'ASSY(Wafer)', value: 'ASSY(Wafer)' },
      { label: 'ASSY(Map)', value: 'ASSY(Map)' },
      { label: 'ASSY', value: 'ASSY' },
      { label: 'REL', value: 'REL' },
      { label: 'SLT', value: 'SLT' },
      { label: 'MT', value: 'MT' },
      { label: 'NA', value: 'NA' },
      { label: 'CAL', value: 'CAL' },
    ],
  },
  //  File Category
  {
    type: 'select',
    title: 'File Category',
    field: 'fileCategory',
    col: {
      span: 12,
    },
    props: {
      placeholder: '请选择',
    },
    validate: [{ required: true, message: '请选择' }],
    options: [
      { label: 'STDF', value: 'STDF' }, // STDF文件
      { label: 'RAW_DATA', value: 'RAW_DATA' }, // 原始数据
    ],
  },
  //  Device Id
  {
    type: 'input',
    title: 'Device Id',
    field: 'deviceId',
    col: {
      span: 12,
    },
    props: {
      placeholder: '请输入',
    },
    validate: [{ required: false, message: '请输入' }],
  },
  //  Test Stage
  {
    type: 'input',
    title: 'Test Stage',
    field: 'testStage',
    col: {
      span: 12,
    },
    props: {
      placeholder: '请输入',
      clearable: true,
    },
  },
  //  Lot Id
  {
    type: 'input',
    title: 'Lot Id',
    field: 'lotId',
    col: {
      span: 12,
    },
    props: {
      placeholder: '请输入',
      clearable: true,
    },
  },
  //  Wafer No
  {
    type: 'input',
    title: 'Wafer No',
    field: 'waferNo',
    col: {
      span: 12,
    },
    props: {
      placeholder: '请输入',
      clearable: true,
    },
  },
  //  Start Time
  {
    type: 'datePicker',
    title: 'Start Time',
    field: 'time',
    col: {
      span: 12,
    },
    props: {
      type: 'datetimerange',
      startPlaceholder: '开始时间',
      endPlaceholder: '结束时间',
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    validate: [{ required: false, message: '请选择' }],
    update(val: any, rule: any, api: Api) {
      if (!val) {
        api.setValue('time', undefined);
        api.setValue('minStartT', undefined);
        api.setValue('maxStartT', undefined);
      }
    },
  },
  //  Ftp Modify Time
  {
    type: 'datePicker',
    title: 'Ftp Modify Time',
    field: 'ftpTime',
    col: {
      span: 12,
    },
    props: {
      type: 'datetimerange',
      startPlaceholder: '开始时间',
      endPlaceholder: '结束时间',
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    validate: [{ required: false, message: '请选择' }],
    update(val: any, rule: any, api: Api) {
      if (!val) {
        api.setValue('ftpTime', undefined);
        api.setValue('minFtpMtime', undefined);
        api.setValue('maxFtpMtime', undefined);
      }
    },
  },
  //  Ftp Path
  {
    type: 'input',
    title: 'Ftp Path',
    field: 'ftpPath',
    col: {
      span: 12,
    },
    props: {
      placeholder: '请输入',
    },
    validate: [{ required: false, message: '请输入' }],
  },
  //  Deferred
  {
    type: 'select',
    title: 'Deferred',
    field: 'deferred',
    col: {
      span: 12,
    },
    value: 0,
    validate: [{ required: false, message: '请输入' }],
    options: [
      {
        label: '启用',
        value: 1,
      },
      {
        label: '不启用',
        value: 0,
      },
    ],
    control: [
      {
        value: 1,
        condition: '!=',
        method: 'disabled',
        rule: ['priority'],
      },
      {
        value: 1,
        condition: '!=',
        method: 'required',
        rule: ['priority'],
      },
    ],
    update(val: any, rule: any, api: Api) {
      if (val === 1) {
        api.setValue('priority', 'deferred');
      } else {
        api.setValue('priority', undefined);
      }
    },
  },
  //  Priority
  {
    type: 'input',
    title: 'Priority',
    field: 'priority',
    col: {
      span: 12,
    },
    props: {
      placeholder: '请输入',
    },
    validate: [{ required: false, message: '请输入' }],
  },
];

// 表单配置选项
export const OPTION = {
  submitBtn: false, // 不显示提交按钮
  col: {
    labelWidth: '130px', // 标签宽度
  },
};
