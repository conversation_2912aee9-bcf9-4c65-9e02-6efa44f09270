<template>
  <div class="autoPage">
    <SearchList :searchList="SEARCH_LIST" @search="search" />

    <div class="tableBox">
      <CommonTable border title="Table List" :data="tableData" v-model:pagination="pagination" v-loading="loading">
        <template #headerRightStart>
          <el-button type="primary" @click="addRow">新建</el-button>
        </template>

        <template v-for="item in TableHeader" :key="item.prop">
          <!-- 特殊处理开始时间 -->
          <el-table-column v-if="item.prop === 'startTime'" v-bind="item" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.minStartT ? scope.row.minStartT + ' - ' + scope.row.maxStartT : '-' }}
            </template>
          </el-table-column>
          <!-- 特殊处理FTP修改时间 -->
          <el-table-column v-else-if="item.prop === 'ftpModifyTime'" v-bind="item" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.minFtpMtime ? scope.row.minFtpMtime + ' - ' + scope.row.maxFtpMtime : '-' }}
            </template>
          </el-table-column>

          <el-table-column v-bind="item" show-overflow-tooltip v-else />
        </template>

        <el-table-column label="操作" fixed="right" width="160">
          <template #default="scope">
            <el-button text @click="editRow(scope.row)">编辑</el-button>
            <el-button type="danger" text @click="copyRow(scope.row)">复制</el-button>
            <el-button type="danger" text @click="delRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </CommonTable>
    </div>

    <FormDialog v-model:visible="visible" :getList="search" :formData="currentRow" :isUpdate="isUpdate" />
  </div>
</template>

<script setup lang="ts">
import { ElMessageBox } from 'element-plus';
import { ConfigListItem } from '../typing';
import { SearchList as SEARCH_LIST, TableHeader } from './data';
import FormDialog from './FormDialog.vue';
import { useAuto } from './hooks/useAuto';
import { SearchList, CommonTable } from '@guwave/components';
import { deleteConfig } from '@/service/priority';
import { RESPONSE_SUCCESS_CODE } from '@/utils/http';

const visible = ref(false);
const isUpdate = ref(false);
const currentRow = ref<ConfigListItem | undefined>(undefined);
const { tableData, pagination, search, loading } = useAuto();

//  添加行
const addRow = () => {
  isUpdate.value = false;
  currentRow.value = undefined;
  visible.value = true;
};
//  编辑行
const editRow = (row: ConfigListItem) => {
  isUpdate.value = true;
  currentRow.value = row;
  visible.value = true;
};
//  复制行
const copyRow = (row: ConfigListItem) => {
  isUpdate.value = false;
  currentRow.value = row;
  visible.value = true;
};
//  删除行
const delRow = (row: ConfigListItem) => {
  ElMessageBox.confirm('确认是否删除!', '警告', {
    type: 'warning',
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  }).then(async () => {
    try {
      const res = await deleteConfig(row.id as number);

      if (res.data.code === RESPONSE_SUCCESS_CODE) {
        search();
      }
    } catch (err) {
      console.error(err);
    }
  });
};

onMounted(() => {
  search();
});
</script>

<style lang="scss" scoped>
.autoPage {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 0;

  .search-list {
    margin: 0 16px;
    background: #fff;
    box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.02), 0px 2px 4px 0px rgba(0, 0, 0, 0.04);
    border-radius: 4px;
    padding: 16px;
    :deep(.search-list) {
      flex-wrap: wrap;
      max-height: none !important;
      overflow: visible !important;
    }
    :deep(.search-item) {
      display: flex !important;
    }
    :deep(.expend-btn-box) {
      display: none !important;
    }
  }
  .tableBox {
    margin: 16px;
    margin-bottom: 24px;
    padding: 12px 16px 0;
    background: #fff;
    border-radius: 4px;
    flex: 1;
    height: 0;

    :deep(.el-table .cell) {
      white-space: nowrap;
    }
  }
}
</style>
