<template>
  <el-dialog v-model="visible" :title="title" width="920px" @open="openFn" v-loading="loading">
    <FormCreate v-model:api="fApi" :rule="form.rule" :option="form.option" />

    <template #footer>
      <div class="btnbox">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="submit" :loading="loading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { useModel } from 'vue';
import { useForm } from './hooks/useForm';
import { ElMessage } from 'element-plus';
import { RESPONSE_SUCCESS_CODE } from '@/utils/http';
import { ConfigListItem } from '../typing';
import { addConfig, updateConfig } from '@/service/priority';
import { dayjs } from 'element-plus/es';
import { debounce } from 'lodash';
interface Props {
  visible: boolean;
  isUpdate?: boolean;
  formData?: ConfigListItem;
  getList: () => void;
}
const props = defineProps<Props>();
const visible = useModel(props, 'visible');

const loading = ref(false);
const title = computed(() => {
  if (props.isUpdate) {
    return '编辑任务优先级默认配置';
  }
  if (props.formData) {
    return '复制任务优先级默认配置';
  }

  return '新建任务优先级默认配置';
});

const { fApi, form, FormCreate } = useForm();

//  数据组装函数
const formatterForm = (data: ConfigListItem) => {
  const newData: ConfigListItem = { ...data, createTime: undefined, updateTime: undefined };

  if (newData.testStage) {
    newData.testStage = newData.testStage.trim();
  }
  if (newData.lotId) {
    newData.lotId = newData.lotId.trim();
  }
  if (newData.waferNo) {
    newData.waferNo = newData.waferNo.trim();
  }

  if (newData.time) {
    newData.minStartT = dayjs(newData.time[0]).format('YYYY-MM-DD HH:mm:ss');
    newData.maxStartT = dayjs(newData.time[1]).format('YYYY-MM-DD HH:mm:ss');
  }
  if (newData.ftpTime) {
    newData.minFtpMtime = dayjs(newData.ftpTime[0]).format('YYYY-MM-DD HH:mm:ss');
    newData.maxFtpMtime = dayjs(newData.ftpTime[1]).format('YYYY-MM-DD HH:mm:ss');
  }

  if (newData.deferred === 1) {
    newData.priority = 'deferred';
  }

  return newData;
};

// 表单提交
const submit = debounce(
  async () => {
    if (loading.value) return;

    fApi.value?.submit(async () => {
      const fData: ConfigListItem = fApi.value?.formData() as any;
      const params: ConfigListItem = formatterForm(fData) as ConfigListItem;

      if (isNaN(Number(params.priority)) && params.priority !== 'deferred') {
        ElMessage.warning('优先级必须为数字');
        return;
      }
      if ((Number(params.priority) < 1 || Number(params.priority) > 10) && params.priority !== 'deferred') {
        ElMessage.warning('优先级范围为1-10');
        return;
      }

      //  判断params中是否有属性为''，如果有则将其设置为undefined
      Object.keys(params).forEach((key) => {
        if (params[key as keyof ConfigListItem] === '') {
          params[key as keyof ConfigListItem] = undefined;
        }
      });

      try {
        loading.value = true;
        const res = props.isUpdate ? await updateConfig(params) : await addConfig(params);

        if (res.data.code === RESPONSE_SUCCESS_CODE) {
          ElMessage.success('执行成功!');
          visible.value = false;
          props.getList();
        } else {
          loading.value = false;
        }
      } catch (err) {
        loading.value = false;
        console.error(err);
      }
    });
  },
  500,
  { leading: true },
);

// 打开对话框
const openFn = () => {
  loading.value = false;

  if (props.formData) {
    const data = {
      ...props.formData,
      deferred: isNaN(Number(props.formData?.priority)) ? 1 : 0,
      time: [props.formData?.minStartT, props.formData?.maxStartT],
      ftpTime: [props.formData?.minFtpMtime, props.formData?.maxFtpMtime],
    };
    fApi.value?.coverValue && fApi.value?.coverValue(data);

    //反显时间
    if (props.formData?.minStartT && props.formData?.maxStartT) {
      fApi.value?.setValue('time', [props.formData?.minStartT, props.formData?.maxStartT]);
    } else {
      fApi.value?.setValue('time', undefined);
    }
    if (props.formData?.minFtpMtime && props.formData?.maxFtpMtime) {
      fApi.value?.setValue('ftpTime', [props.formData?.minFtpMtime, props.formData?.maxFtpMtime]);
    } else {
      fApi.value?.setValue('ftpTime', undefined);
    }

    // 编辑态禁用多个字段
    const disableFields = [
      'customer',
      'subCustomer',
      'factory',
      'factorySite',
      'deviceId',
      'testStage',
      'lotType',
      'testArea',
      'fileCategory',
      'lotId',
      'waferNo',
    ];
    disableFields.forEach((field) => {
      fApi.value?.updateRule(field, {
        props: {
          placeholder: '请输入',
          disabled: props.isUpdate,
        },
      });
    });
  } else {
    // 新增态启用多个字段
    const disableFields = [
      'customer',
      'subCustomer',
      'testArea',
      'fileCategory',
      'factory',
      'factorySite',
      'lotType',
      'deviceId',
      'testStage',
      'lotId',
      'waferNo',
    ];
    disableFields.forEach((field) => {
      fApi.value?.updateRule(field, {
        props: {
          placeholder: '请输入',
          disabled: false,
        },
      });
    });
    //  清空时间
    fApi.value?.setValue('time', undefined);
    fApi.value?.setValue('ftpTime', undefined);
    fApi.value?.setValue('minStartT', undefined);
    fApi.value?.setValue('maxStartT', undefined);
    fApi.value?.setValue('minFtpMtime', undefined);
    fApi.value?.setValue('maxFtpMtime', undefined);

    fApi.value?.resetFields && fApi.value?.resetFields();
    setTimeout(() => {
      fApi.value?.clearValidateState && fApi.value?.clearValidateState();
    });
  }
};
</script>

<style scoped lang="scss">
:deep(.el-row) {
  .el-col {
    margin-bottom: 16px;

    .el-select,
    .el-date-editor {
      width: 100%;
    }
  }
}

:deep(.el-input.is-disabled) {
  .el-input__wrapper {
    background: #f5f5f5 !important;
  }
}
:deep(.el-select .is-disabled) {
  background: #f5f5f5 !important;
}

.btnbox {
  display: flex;

  .el-button {
    &:first-child {
      margin-left: auto;
    }
    &:last-child {
      margin-right: 16px;
    }
  }
}
</style>
