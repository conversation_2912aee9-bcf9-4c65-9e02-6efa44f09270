// 导入分页类型
import { PaginationType } from '@guwave/components';
// 导入获取配置列表接口
import { getConfigList } from '@/service/priority';
// 导入响应成功状态码
import { RESPONSE_SUCCESS_CODE } from '@/utils/http';
// 导入配置列表项和参数类型
import { ConfigListItem, ConfigListParam } from '../../typing';
import { debounce } from 'lodash';
// 自动配置Hook
export const useAuto = () => {
  // 加载状态
  const loading = ref(false);
  // 搜索参数
  const searchParam = ref<ConfigListParam>({
    pageSize: 20,
    pageIndex: 1,
  });
  // 表格数据
  const tableData = ref<ConfigListItem[]>([]);

  // 分页配置
  const pagination = reactive<PaginationType>({
    total: 0, // 总条数
    pageSize: 20, // 每页条数
    pageIndex: 1, // 当前页码
    // 页码改变回调
    currentChange: (current: number) => {
      pagination.pageIndex = current;
      search();
    },
    // 每页条数改变回调
    sizeChange: (size: number) => {
      pagination.pageIndex = 1;
      pagination.pageSize = size;
      search();
    },
  });

  // 搜索方法
  const search = debounce(
    async (param?: ConfigListParam) => {
      let sParam: { [k: string]: any } = {};
      // 如果传入参数则合并分页信息
      if (param) {
        sParam = { ...param, pageSize: pagination.pageSize, pageIndex: pagination.pageIndex };
        searchParam.value = sParam;
      } else {
        sParam = { ...searchParam.value, pageSize: pagination.pageSize, pageIndex: pagination.pageIndex };
      }

      // 判断sParam中是否存在给''的属性，如果有则将其设为undefined
      Object.keys(sParam).forEach((key: string) => {
        if (sParam[key] === '') {
          sParam[key] = undefined;
        }
      });

      let res = undefined;

      try {
        // 开启加载状态
        loading.value = true;

        // 调用获取配置列表接口
        res = await getConfigList(sParam);

        // 请求成功处理响应数据
        if (res.data.code === RESPONSE_SUCCESS_CODE) {
          const rData = res.data.data;
          pagination.pageIndex = rData.pageIndex;
          pagination.pageSize = rData.pageSize;
          pagination.total = rData.total;
          tableData.value = rData.data;
        }
      } catch (err) {
        console.error(err);
      } finally {
        // 关闭加载状态
        setTimeout(() => {
          loading.value = false;
        }, 200);
      }
    },
    500,
    { leading: true },
  );

  // 返回Hook数据和方法
  return {
    search,
    loading,
    tableData,
    pagination,
  };
};
