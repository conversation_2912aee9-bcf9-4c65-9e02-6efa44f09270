// 导入表单创建工具和API类型
import formCreate, { Api } from '@form-create/element-ui';
// 导入表单规则和配置选项
import { RULE, OPTION } from '../data';

// 表单Hook
export const useForm = () => {
  // 创建表单实例
  const FormCreate = formCreate.$form();

  // 表单API引用
  const fApi = ref<Api>();
  // 表单响应式数据
  const form = reactive({
    rule: RULE, // 表单验证规则
    option: OPTION, // 表单配置选项
  });

  // 返回表单相关数据和方法
  return {
    fApi,
    form,
    FormCreate,
  };
};
