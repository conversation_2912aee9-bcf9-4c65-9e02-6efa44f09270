// 定义标签页枚举
export enum TAB_ENUM {
  TASK = 'TASK', // 任务标签
  AUTO = 'AUTO', // 自动标签
}

// 定义单选按钮枚举
export enum RADIO_ENUM {
  AUTO = 'AUTO', // 自动模式
  MANUAL = 'MANUAL', // 手动模式
  SPARK = 'SPARK', // Spark模式
}

// 搜索参数接口
export interface SearchParam {
  pageIndex?: number; // 页码
  pageSize?: number; // 每页条数

  factory?: string; // 工厂
  factorySite?: string; // 工厂站点
  deviceId?: string; // 设备ID
  lotType?: string; // 批次类型
  lotId?: string; // 批次ID
  waferNo?: string; // 晶圆编号
  testStage?: string; // 测试阶段
  testArea?: string; // 测试区域
  fileCategory?: string; // 文件类别

  appName?: string; // 应用名称
  queue?: string; // 队列
  computeType?: string; // 计算类型
  processStatus?: string; // 处理状态

  fileName?: string; // 文件名
}

// 自动列表项接口
export interface AutoListItem {
  id: number; // ID
  priority: string; // 优先级
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
  factory: string; // 工厂
  deviceId: string; // 设备ID
  lotType: string; // 批次类型
  fileCategory: string; // 文件类别
  lotId: string; // 批次ID
  waferNo: string; // 晶圆编号
  testArea: string; // 测试区域
  testStage: string; // 测试阶段
  rank: number; // 排名
  loading?: boolean; // 加载状态
}

// 手动列表项接口
export interface ManualListItem {
  id: number; // ID
  priority: string; // 优先级
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
  fileName: string; // 文件名
  user: string; // 用户
  fileSize: number; // 文件大小
  rank: number; // 排名
  loading?: boolean; // 加载状态
}

// Spark列表项接口
export interface SparkListItem {
  id: number; // ID
  priority: string; // 优先级
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
  appName: string; // 应用名称
  computeType: string; // 计算类型
  dieCnt: number; // Die数量
  testItemCnt: number; // 测试项数量
  queue: string; // 队列
  processStatus: string; // 处理状态
  rank: number; // 排名
  loading?: boolean; // 加载状态
}

// 优先级参数接口
export interface ProorityParam {
  taskType?: string; // 任务类型
  priority?: string; // 优先级
  taskIds?: number[]; // 任务ID列表
}

// 配置列表参数接口
export interface ConfigListParam {
  pageIndex?: number; // 页码
  pageSize?: number; // 每页条数
  factory?: string; // 工厂
  factorySite?: string; // 工厂站点
  deviceId?: string; // 设备ID
  testStage?: string; // 测试阶段
  lotType?: string; // 批次类型
  testArea?: string; // 测试区域
  fileCategory?: string; // 文件类别
  lotId?: string; // 批次ID
  waferNo?: string; // 晶圆编号
  priority?: string; // 优先级
  minStartT?: string; // 最小开始时间
  maxStartT?: string; // 最大开始时间
  minFtpMtime?: string; // 最小FTP修改时间
  maxFtpMtime?: string; // 最大FTP修改时间
  ftpPath?: string; // FTP路径
  deferred?: number; // 延迟
}

// 配置列表项接口
export interface ConfigListItem {
  id?: number; // ID
  factory?: string; // 工厂
  factorySite?: string; // 工厂站点
  deviceId?: string; // 设备ID
  testStage?: string; // 测试阶段
  lotType?: string; // 批次类型
  testArea?: string; // 测试区域
  fileCategory?: string; // 文件类别
  lotId?: string; // 批次ID
  waferNo?: string; // 晶圆编号
  priority?: string; // 优先级
  minStartT?: string; // 最小开始时间
  maxStartT?: string; // 最大开始时间
  minFtpMtime?: string; // 最小FTP修改时间
  maxFtpMtime?: string; // 最大FTP修改时间
  ftpPath?: string; // FTP路径
  createTime?: string; // 创建时间
  updateTime?: string; // 更新时间
  deferred?: number; // 延迟
  time?: [Date, Date]; // 时间范围
  ftpTime?: [Date, Date]; // FTP时间范围
}

// 更新配置参数接口
export interface UpdateConfigParam {
  id: number; // ID
  pageIndex: number; // 页码
  pageSize: number; // 每页条数
  priority: string; // 优先级
  minStartT: string; // 最小开始时间
  maxStartT: string; // 最大开始时间
  minFtpMtime: string; // 最小FTP修改时间
  maxFtpMtime: string; // 最大FTP修改时间
  ftpPath: string; // FTP路径
}
