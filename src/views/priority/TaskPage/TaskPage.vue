<template>
  <!-- 主要容器 -->
  <div class="taskPage">
    <!-- 任务类型切换区域 -->
    <el-radio-group v-model="activeFlag" @change="tabChange" style="margin-bottom: 30px">
      <el-radio-button :value="RADIO_ENUM.AUTO">自动入库</el-radio-button>
      <el-radio-button :value="RADIO_ENUM.MANUAL">手动上传</el-radio-button>
      <el-radio-button :value="RADIO_ENUM.SPARK">Spark任务</el-radio-button>
    </el-radio-group>

    <!-- 主要内容区域 -->
    <div class="taskBody" v-loading="loading">
      <!-- 搜索表单组件 -->
      <SearchList :searchList="searchList" @search="search" v-if="!loading" />

      <!-- 表格展示区域 -->
      <div class="tableBox">
        <CommonTable
          border
          isSelect
          :data="tableData"
          v-loading="tableLoading"
          v-model:pagination="pagination"
          :row-class-name="tableRowClassName"
          @selection-change="selectionChange"
        >
          <template #headerRightStart>
            <el-button type="primary" @click="openPriorityDialog">调整优先级</el-button>
          </template>

          <!-- 动态渲染表格列 -->
          <template v-for="item in tableHeader" :key="item.prop">
            <!-- 特殊处理process status列 -->
            <el-table-column
              prop="processStatus"
              label="Process Status"
              v-if="item.prop === 'processStatus'"
              width="140"
            >
              <template #default="scope">
                <el-tag :type="scope.row.processStatus === 'PROCESSING' ? 'warning' : 'info'">
                  {{ statusText(scope.row.processStatus) }}
                </el-tag>
              </template>
            </el-table-column>

            <!-- 特殊处理文件大小列 -->
            <el-table-column prop="fileSize" label="File Size" v-else-if="item.prop === 'fileSize'" width="140">
              <template #default="scope">
                {{ fileSizeFormat(scope.row.fileSize) }}
              </template>
            </el-table-column>

            <el-table-column v-bind="item" show-overflow-tooltip v-else />
          </template>

          <!-- 操作按钮列 -->
          <el-table-column label="操作" fixed="right" width="120">
            <template #default="scope">
              <el-button
                text
                @click.stop="changePriority(scope.row)"
                :loading="scope.row.loading"
                :disabled="scope.row.processStatus === 'PROCESSING'"
              >
                提升优先级
              </el-button>
            </template>
          </el-table-column>
        </CommonTable>
      </div>
    </div>

    <!-- 批量提升优先级对话框 -->
    <PriorityDialog v-model:visible="priorityVisible" :submit="changePriority" />
  </div>
</template>

<script setup lang="ts">
/* 导入依赖 */
import { CommonTable, SearchList } from '@guwave/components';
import { RADIO_ENUM, SparkListItem, AutoListItem, ManualListItem, ProorityParam } from '../typing';
import { useTask } from './useTask';
import { isString } from 'element-plus/es/utils/types.mjs';
import { updatePriority } from '@/service/priority';
import { RESPONSE_SUCCESS_CODE } from '@/utils/http';
import { ElMessage } from 'element-plus';
import PriorityDialog from './PriorityDialog.vue';

/* 状态定义 */
// 记录最近更新的任务ID列表
const idList = ref<number[]>([]);
// 批量提升优先级对话框是否可见
const priorityVisible = ref(false);
// 存储表格中选中的行数据,可以是自动入库、手动上传或Spark任务类型的数据
const selectedRows = ref<(AutoListItem | ManualListItem | SparkListItem)[]>([]);

// 从useTask hook获取相关状态和方法
const {
  search,
  activeFlag,
  isReload,
  searchList,
  loading,
  tableData,
  tableHeader,
  pagination,
  searchParam,
  tableLoading,
} = useTask();

/* 事件处理函数 */
// 切换任务类型时重置分页并重新搜索
const tabChange = () => {
  searchParam.value = {
    pageIndex: 1,
    pageSize: 20,
  };
  isReload.value = true;
  search();
};

/* 事件处理函数 */
// 表格选中行变化时更新selectedRows
const selectionChange = (rows: (AutoListItem | ManualListItem | SparkListItem)[]) => {
  selectedRows.value = rows;
};

/* 业务逻辑函数 */
// 打开批量提升优先级对话框
const openPriorityDialog = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择任务!');
    return;
  }
  if (selectedRows.value.some((item) => (item as SparkListItem).processStatus === 'PROCESSING')) {
    ElMessage.warning('勾选项中存在处理中的任务，请等待任务处理完成!');
    return;
  }
  priorityVisible.value = true;
};

// 根据状态码返回对应的中文文本
const statusText = (status: string) => {
  return status === 'PROCESSING' ? '处理中' : '排队中';
};

// 更新任务优先级
const changePriority = async (row: (AutoListItem | ManualListItem | SparkListItem) | string) => {
  let param: ProorityParam = {};
  // 处理批量更新的情况
  if (isString(row)) {
    param = {
      taskType: activeFlag.value,
      priority: row,
      taskIds: selectedRows.value.map((item) => item.id),
    };
  } else {
    // 处理单条更新的情况
    const pri = isNaN(Number(row?.priority)) ? 1 : Number(row?.priority) + 1;

    param = {
      taskType: activeFlag.value,
      priority: pri + '',
      taskIds: [row?.id],
    };
  }

  if (isNaN(Number(param.priority)) && param.priority !== 'deferred') {
    ElMessage.warning('优先级必须为数字');
    return;
  }

  if ((Number(param.priority) < 1 || Number(param.priority) > 10) && param.priority !== 'deferred') {
    ElMessage.warning('优先级范围为1-10');
    return;
  }

  try {
    // 设置loading状态
    tableData.value?.map((item) => {
      if (param.taskIds?.some((id) => id === item.id)) {
        item.loading = true;
      }
    });

    // 调用更新接口
    const res = await updatePriority(param);
    if (res.data.code === RESPONSE_SUCCESS_CODE) {
      idList.value = param.taskIds as number[];
      search();
    }
  } catch (err) {
    console.error(err);
  } finally {
    // 清除loading状态
    tableData.value?.map((item) => {
      item.loading = false;
    });
  }
};

/* 工具函数 */
// 设置更新后的行高亮样式
const tableRowClassName = ({ row }: { row: AutoListItem | ManualListItem | SparkListItem }) => {
  if (idList.value?.some((id) => id === row.id)) return 'highlight';
  else return '';
};
// 文件大小格式化(Byte转KB、MB、GB)
const fileSizeFormat = (size: number) => {
  if (size < 1024) return `${size} Byte`;
  else if (size < 1024 * 1024) return `${(size / 1024).toFixed(2)} KB`;
  else if (size < 1024 * 1024 * 1024) return `${(size / 1024 / 1024).toFixed(2)} MB`;
  else return `${(size / 1024 / 1024 / 1024).toFixed(2)} GB`;
};

/* 生命周期钩子 */
// 组件挂载时执行搜索
onMounted(() => {
  search();
});
</script>

<style lang="scss" scoped>
/* 页面主样式 */
.taskPage {
  display: flex;
  flex-direction: column;
  height: 0;
  flex: 1;

  /* 单选按钮组样式 */
  .el-radio-group {
    margin: 0 16px;
    margin-bottom: 16px !important;
  }

  /* 内容区域样式 */
  .taskBody {
    flex: 1;
    height: 0;
    display: flex;
    flex-direction: column;

    /* 搜索表单样式 */
    .search-list {
      margin: 0 16px;
      background: #fff;
      box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.02), 0px 2px 4px 0px rgba(0, 0, 0, 0.04);
      border-radius: 4px;
      padding: 16px;
    }

    /* 表格区域样式 */
    .tableBox {
      margin: 16px;
      margin-bottom: 24px;
      padding: 12px 16px 0;
      background: #fff;
      border-radius: 4px;
      flex: 1;
      height: 0;

      /* 表格单元格样式 */
      :deep(.el-table .cell) {
        white-space: nowrap;
      }

      /* 高亮行样式及动画 */
      :deep(.el-table .highlight) {
        animation: highlight 2s;

        @keyframes highlight {
          from {
            background: #e1d9f1;
          }
          to {
            background: #fff;
          }
        }
      }
    }
  }
}
</style>
