<template>
  <el-dialog v-model="visible" title="调整优先级" width="500px" @open="openFn">
    <div class="flex items-center">
      <el-input v-model="priority" placeholder="请输入优先级 （1-10）" class="input" :disabled="deferred" />
      <el-checkbox v-model="deferred" @change="deferredChange">Deferred</el-checkbox>
    </div>

    <template #footer>
      <div class="btnbox">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="submitFn">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
// 导入vue的useModel hook用于双向绑定
import { ElMessage } from 'element-plus';
import { useModel } from 'vue';

// 定义组件Props接口
interface Props {
  visible: boolean; // 控制对话框显示/隐藏
  submit: (priority: string) => void; // 提交回调函数
}
const props = defineProps<Props>();
// 创建visible的双向绑定
const visible = useModel(props, 'visible');

// 优先级输入值
const priority = ref('');
// 是否为deferred状态
const deferred = ref(false);

// deferred复选框change事件处理
const deferredChange = (value: boolean) => {
  deferred.value = value;
  if (value) {
    priority.value = 'deferred';
  }
};

// 提交表单处理函数
const submitFn = () => {
  // 如果选中deferred,则将优先级设为deferred
  if (deferred.value) {
    priority.value = 'deferred';
  }

  if (isNaN(Number(priority.value)) && priority.value !== 'deferred') {
    ElMessage.warning('优先级必须为数字');
    return;
  }

  if ((Number(priority.value) < 1 || Number(priority.value) > 10) && priority.value !== 'deferred') {
    ElMessage.warning('优先级范围为1-10');
    return;
  }

  // 调用父组件的submit方法并传入优先级值
  props.submit(priority.value);
  // 关闭对话框
  visible.value = false;
};

// 对话框打开时的处理函数
const openFn = () => {
  // 重置表单数据
  priority.value = '';
  deferred.value = false;
};
</script>

<style scoped lang="scss">
.input {
  margin-right: 8px;
}
.btnbox {
  display: flex;

  .el-button {
    &:first-child {
      margin-left: auto;
    }
    &:last-child {
      margin-right: 16px;
    }
  }
}
</style>
