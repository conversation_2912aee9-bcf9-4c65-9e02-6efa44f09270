import { PaginationType } from '@guwave/components';
import { AutoListItem, ManualListItem, SparkListItem, RADIO_ENUM, SearchParam } from '../typing';
import { getAutoList, getManualList, getSparkList } from '@/service/priority';
import { RESPONSE_SUCCESS_CODE } from '@/utils/http';
import { debounce } from 'lodash';
import {
  AutoSearchList,
  AutoTableHeader,
  ManualSearchList,
  ManualTableHeader,
  SparkSearchList,
  SparkTableHeader,
} from './data';

export const useTask = () => {
  const loading = ref(false);
  const isReload = ref(false);
  const tableLoading = ref(false);
  const activeFlag = ref(RADIO_ENUM.AUTO);
  const searchParam = ref<SearchParam>({
    pageIndex: 1,
    pageSize: 20,
  });
  const tableData = ref<AutoListItem[] | ManualListItem[] | SparkListItem[]>([]);

  const pagination = reactive<PaginationType>({
    total: 0,
    pageSize: 20,
    pageIndex: 1,
    currentChange: (current: number) => {
      pagination.pageIndex = current;
      search();
    },
    sizeChange: (size: number) => {
      pagination.pageIndex = 1;
      pagination.pageSize = size;
      search();
    },
  });

  //  ?搜索项
  const searchList = computed(() => {
    if (activeFlag.value === RADIO_ENUM.AUTO) {
      return AutoSearchList;
    } else if (activeFlag.value === RADIO_ENUM.MANUAL) {
      return ManualSearchList;
    } else {
      return SparkSearchList;
    }
  });
  //  ?表头
  const tableHeader = computed(() => {
    if (activeFlag.value === RADIO_ENUM.AUTO) {
      return AutoTableHeader;
    } else if (activeFlag.value === RADIO_ENUM.MANUAL) {
      return ManualTableHeader;
    } else {
      return SparkTableHeader;
    }
  });

  //  防抖搜索

  const search = debounce(
    async (param?: SearchParam) => {
      let sParam: Record<string, any> = {};
      if (param) {
        sParam = { ...param, pageSize: pagination.pageSize, pageIndex: pagination.pageIndex };

        // 判断param对象中是否存在属性为''的值，如果有则删除该属性
        Object.keys(sParam).forEach((key) => {
          if (sParam[key] === '') {
            delete sParam[key];
          }
        });

        if (Object.keys(sParam).length === 0) {
          return;
        }

        searchParam.value = sParam;
      } else {
        sParam = { ...searchParam.value, pageSize: pagination.pageSize, pageIndex: pagination.pageIndex };
      }

      let res = undefined;

      try {
        if (isReload.value) loading.value = true;
        tableLoading.value = true;
        if (activeFlag.value === RADIO_ENUM.AUTO) {
          res = await getAutoList(sParam);
        } else if (activeFlag.value === RADIO_ENUM.MANUAL) {
          res = await getManualList(sParam);
        } else {
          res = await getSparkList(sParam);
        }

        if (res.data.code === RESPONSE_SUCCESS_CODE) {
          const rData = res.data.data;
          pagination.pageIndex = rData.pageIndex;
          pagination.pageSize = rData.pageSize;
          pagination.total = rData.total;

          tableData.value = rData.data;
        }
      } catch (err) {
        console.error(err);
      } finally {
        isReload.value = false;
        loading.value = false;
        setTimeout(() => {
          tableLoading.value = false;
        }, 200);
      }
    },
    500,
    { leading: true },
  );

  return {
    search,
    loading,
    isReload,
    tableData,
    searchList,
    tableHeader,
    pagination,
    activeFlag,
    searchParam,
    tableLoading,
  };
};
