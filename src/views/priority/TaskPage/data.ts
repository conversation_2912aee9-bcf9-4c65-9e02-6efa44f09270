// 导入搜索组件相关类型
import { SearchItem, SearchTypeEnum } from '@guwave/components';

// 自动模式搜索列表配置
export const AutoSearchList: SearchItem[] = [
  {
    type: SearchTypeEnum.INPUT,
    label: 'Factory', // 工厂
    key: 'factory',
  },
  {
    type: SearchTypeEnum.INPUT,
    label: 'Device Id', // 设备ID
    key: 'deviceId',
  },
  {
    type: SearchTypeEnum.SELECT,
    label: 'Lot Type', // 批次类型
    key: 'lotType',
    options: [
      { label: 'PRODUCTION', value: 'PRODUCTION' }, // 生产批次
      { label: 'ENGINEERING', value: 'ENGINEERING' }, // 工程批次
      { label: 'QUALIFICATION', value: 'QUALIFICATION' }, // 认证批次
      { label: 'EMPTY', value: 'EMPTY' }, // 空批次
    ],
  },
  {
    type: SearchTypeEnum.INPUT,
    label: 'Lot Id', // 批次ID
    key: 'lotId',
  },
  {
    type: SearchTypeEnum.INPUT,
    label: 'Wafer No', // 晶圆编号
    key: 'waferNo',
  },
  {
    type: SearchTypeEnum.SELECT,
    label: 'File Category', // 文件类别
    key: 'fileCategory',
    options: [
      { label: 'STDF', value: 'STDF' }, // STDF文件
      { label: 'RAW_DATA', value: 'RAW_DATA' }, // 原始文件
      { label: 'BIT_MEM', value: 'BIT_MEM' }, // BitMem文件
    ],
  },
  {
    type: SearchTypeEnum.SELECT,
    label: 'Test Area', // 测试区域
    key: 'testArea',
    options: [
      { label: 'CP', value: 'CP' },
      { label: 'FT', value: 'FT' },
      { label: 'CP(Map)', value: 'CP(Map)' },
      { label: 'CP(InklessMap)', value: 'CP(InklessMap)' },
      { label: 'BUMP', value: 'BUMP' },
      { label: 'BUMP(Map)', value: 'BUMP(Map)' },
      { label: 'BURNIN', value: 'BURNIN' },
      { label: 'BURNIN(Map)', value: 'BURNIN(Map)' },
      { label: 'WLT', value: 'WLT' },
      { label: 'WLT(Map)', value: 'WLT(Map)' },
      { label: 'REL(Wafer)', value: 'REL(Wafer)' },
      { label: 'REL(Map)', value: 'REL(Map)' },
      { label: 'ASSY(Wafer)', value: 'ASSY(Wafer)' },
      { label: 'ASSY(Map)', value: 'ASSY(Map)' },
      { label: 'ASSY', value: 'ASSY' },
      { label: 'REL', value: 'REL' },
      { label: 'SLT', value: 'SLT' },
      { label: 'MT', value: 'MT' },
      { label: 'NA', value: 'NA' },
      { label: 'CAL', value: 'CAL' },
    ],
  },
];

// 手动模式搜索列表配置
export const ManualSearchList: SearchItem[] = [
  {
    type: SearchTypeEnum.INPUT,
    label: 'File Name', // 文件名
    key: 'fileName',
  },
];

// Spark模式搜索列表配置
export const SparkSearchList: SearchItem[] = [
  {
    type: SearchTypeEnum.SELECT,
    label: 'Queue', // 队列
    key: 'queue',
    options: [
      { label: 'Test Item', value: 'testitem' }, // 测试项队列
      { label: 'Manual', value: 'manual' }, // 手动队列
      { label: 'Die', value: 'die' }, // Die队列
    ],
  },
  {
    type: SearchTypeEnum.INPUT,
    label: 'App Name', // 应用名称
    key: 'appName',
  },
  {
    type: SearchTypeEnum.SELECT,
    label: 'Compute Type', // 计算类型
    key: 'computeType',
    options: [
      { label: 'LOGIC', value: 'LOGIC' }, // 逻辑计算
      { label: 'SQL', value: 'SQL' }, // SQL计算
      { label: 'ETL', value: 'ETL' }, // ETL计算
    ],
  },
  {
    type: SearchTypeEnum.SELECT,
    label: 'Process Status', // 处理状态
    key: 'processStatus',
    options: [
      { label: '排队中', value: 'CREATE' }, // 创建状态
      { label: '处理中', value: 'PROCESSING' }, // 处理中状态
    ],
  },
];

// 自动模式表格列配置
export const AutoTableHeader = [
  {
    prop: 'rank',
    label: 'Rank', // 排名
    width: 70,
  },
  {
    prop: 'factory',
    label: 'Factory', // 工厂
  },
  {
    prop: 'deviceId',
    label: 'Device Id', // 设备ID
  },
  {
    prop: 'lotType',
    label: 'Lot Type', // 批次类型
  },
  {
    prop: 'fileCategory',
    label: 'File Category', // 文件类别
  },
  {
    prop: 'lotId',
    label: 'Lot Id', // 批次ID
  },
  {
    prop: 'waferNo',
    label: 'Wafer No', // 晶圆编号
  },
  {
    prop: 'testArea',
    label: 'Test Area', // 测试区域
  },
  {
    prop: 'testStage',
    label: 'Test Stage', // 测试阶段
  },
  {
    prop: 'priority',
    label: 'Priority', // 优先级
  },
  {
    prop: 'updateTime',
    label: 'Update Time', // 更新时间
  },
];

// 手动模式表格列配置
export const ManualTableHeader = [
  {
    prop: 'rank',
    label: 'Rank', // 排名
    width: 70,
  },
  {
    prop: 'fileName',
    label: 'File Name', // 文件名
  },
  {
    prop: 'user',
    label: 'User', // 用户
  },
  {
    prop: 'fileSize',
    label: 'File Size', // 文件大小
  },
  {
    prop: 'priority',
    label: 'Priority', // 优先级
    width: 90,
  },
  {
    prop: 'createTime',
    label: 'Create Time', // 创建时间
  },
  {
    prop: 'updateTime',
    label: 'Update Time', // 更新时间
  },
];

// Spark模式表格列配置
export const SparkTableHeader = [
  {
    prop: 'rank',
    label: 'Rank', // 排名
    width: 70,
  },
  {
    prop: 'appName',
    label: 'App Name', // 应用名称
  },
  {
    prop: 'computeType',
    label: 'Compute Type', // 计算类型
  },
  {
    prop: 'dieCnt',
    label: 'Die Count', // Die数量
  },
  {
    prop: 'testItemCnt',
    label: 'Test Item Count', // 测试项数量
  },
  {
    prop: 'queue',
    label: 'Queue', // 队列
  },
  {
    prop: 'processStatus',
    label: 'Process Status', // 处理状态
  },
  {
    prop: 'priority',
    label: 'Priority', // 优先级
    width: 90,
  },
  {
    prop: 'createTime',
    label: 'Create Time', // 创建时间
  },
  {
    prop: 'updateTime',
    label: 'Update Time', // 更新时间
  },
];
