import { deleteRule, switchRuleStatus } from '@/service/dataQuality';
import { ListItem } from '@/types/dataQuality';
import { jumpTo } from '@/utils/common';
import { SUCCESS_CODE } from '@/utils/http';
import { DataTableColumns, NButton, NPopconfirm, NSwitch } from 'naive-ui';

export enum SQLEnum {
  INIT = 0, //初始化
  INDEX_RESULT = 1, //指标结果
}
export enum ExecuteTimeEnum {
  DAY = 1, //每天一次
  HALF_HOUR = 2, //每半小时一次
}
export enum TableChartEnum {
  TABLE = 'TABLE',
  CHART = 'LINE',
}

export const FILE_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

//  跳转详情
export const jumpDetail = (id?: number) => {
  const url = '/data-quality-detail';
  jumpTo(url, id ? { id: id } : {}, { title: '数据质量详情' });
};

//  表头
export const COLUMNS: (getList: () => void) => DataTableColumns<ListItem> = (getList) => {
  return [
    {
      title: '指标类型',
      key: 'sqlType',
      resizable: true,
      render: (rowData) => {
        return rowData.sqlType === SQLEnum.INIT ? '初始化' : '指标结果';
      },
    },
    {
      title: '执行间隔',
      key: 'executeTimeType',
      resizable: true,
      render: (rowData) => {
        return rowData.executeTimeType === ExecuteTimeEnum.DAY ? '每天一次' : '每半小时一次';
      },
    },
    {
      title: '指标名称',
      key: 'qualityName',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '备注',
      key: 'comment',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '状态',
      key: 'status',
      resizable: true,
      render: (rowData) => {
        const switchFlag = ref(rowData.status === 1);

        return (
          <NSwitch
            v-model:value={switchFlag.value}
            onUpdate:value={async (val: boolean) => {
              switchFlag.value = val;
              const res = await switchRuleStatus({ id: rowData.id, status: val ? 1 : 0 });
              if (res.data.code === SUCCESS_CODE) {
                getList();
              }
            }}
          />
        );
      },
    },
    {
      title: '操作',
      key: 'tool',
      fixed: 'right',
      render: (rowData) => {
        return (
          <>
            <NButton
              type="info"
              text
              style={{ marginRight: '16px' }}
              onClick={() => {
                jumpDetail(rowData.id);
              }}
            >
              详情
            </NButton>

            <NPopconfirm
              onPositiveClick={async () => {
                const res = await deleteRule(rowData.id);
                if (res.data.code === SUCCESS_CODE) {
                  getList();
                }
              }}
            >
              {{
                trigger: (
                  <NButton type="error" text>
                    删除
                  </NButton>
                ),
                default: <span>确认删除?</span>,
              }}
            </NPopconfirm>
          </>
        );
      },
    },
  ];
};

//  指标类型下拉框列表
export const SQL_OPTIONS = [
  {
    label: '初始化',
    value: SQLEnum.INIT,
  },
  {
    label: '指标结果',
    value: SQLEnum.INDEX_RESULT,
  },
];
//  执行间隔下拉框列表
export const EXECUTE_TIME_OPTIONS = [
  {
    label: '每天一次',
    value: ExecuteTimeEnum.DAY,
  },
  {
    label: '每半小时一次',
    value: ExecuteTimeEnum.HALF_HOUR,
  },
];
