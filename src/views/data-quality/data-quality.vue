<template>
  <PageLayout>
    <template #header>
      <h2>数据质量</h2>

      <div class="btnBox h">
        <n-button type="primary" size="small" @click="jumpDetail()">新增</n-button>
        <n-upload @beforeUpload="importFn" accept=".xlsx">
          <n-button type="primary" size="small">导入</n-button>
        </n-upload>

        <n-button size="small" @click="exportRule()">导出</n-button>
      </div>
    </template>
    <template #content>
      <div class="tableBox v">
        <n-data-table
          :columns="COLUMNS(getDataList)"
          :data="table.data"
          bottom-bordered
          :bordered="true"
          :loading="loading"
          :single-line="false"
          :max-height="'calc(100vh - 180px)'"
        />
        <n-pagination
          :showSizePicker="true"
          :pageSizes="[10, 20, 30, 40, 50]"
          v-model:page="table.pagination.pageIndex"
          v-model:pageSize="table.pagination.pageSize"
          :item-count="table.pagination.total"
          :prefix="table.pagination.prefix"
          @update:page="table.pagination.onChange"
          @update:pageSize="table.pagination.onUpdatePageSize"
        />
      </div>
    </template>
  </PageLayout>
</template>

<script setup lang="ts">
import PageLayout from '../common/page-layout.vue';
import { PaginationInfo, useMessage } from 'naive-ui';
import { COLUMNS, FILE_TYPE, jumpDetail } from './data';
import { getList, exportRule, importRule } from '@/service/dataQuality';
import { SUCCESS_CODE } from '@/utils/http';
import { ListItem } from '@/types/dataQuality';

const loading = ref(false);
const message = useMessage();

//  表格数据
const table = reactive<{ data: ListItem[]; pagination: any }>({
  data: [],
  pagination: {
    total: 0,
    pageSize: 20,
    pageIndex: 1,
    prefix: (info: PaginationInfo) => {
      return `共${info.itemCount}条`;
    },
    onChange: (page: number) => {
      table.pagination.pageIndex = page;
      getDataList();
    },
    onUpdatePageSize: (pageSize: number) => {
      table.pagination.index = 1;
      table.pagination.pageSize = pageSize;

      getDataList(1);
    },
  },
});

const getDataList = async (pageIndex?: number) => {
  loading.value = true;

  const res = await getList({
    pageIndex: pageIndex ?? table.pagination.pageIndex,
    pageSize: table.pagination.pageSize,
  });
  if (res.data.code === SUCCESS_CODE) {
    const rData = res.data.data;
    table.data = rData.data;
    table.pagination.total = rData.total;
    table.pagination.pageIndex = rData.pageIndex;
    table.pagination.pageSize = rData.pageSize;

    loading.value = false;
  }
};

const importFn = async (options: any) => {
  const file = options?.file?.file;

  if (file?.type !== FILE_TYPE) {
    message.warning('文件格式有误!');
  } else if (file?.size > 10000000) {
    message.warning('文件过大!');
  } else {
    const formData = new FormData();
    formData.append('file', file);

    const res = await importRule(formData);
    if (res.data.code === SUCCESS_CODE) {
      getDataList();
    }
  }
};

onMounted(() => {
  getDataList();
});
</script>

<style scoped lang="scss">
.btnBox {
  .n-button {
    width: 80px;
    margin: auto 0 auto 10px;
  }
  > .n-upload {
    margin: auto 0;

    :deep(.n-upload-file-list) {
      display: none !important;
    }
  }
}

.tableBox {
  flex: 1;

  > .n-data-table {
    height: calc(100vh - 130px);
  }
  > .n-pagination {
    margin: 16px 0 0 auto;
  }
}
</style>
