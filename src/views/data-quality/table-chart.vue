<template>
  <div class="tableChart v" :style="{ width: props.width ? props.width : '90%' }">
    <div class="select h" v-if="!props.isTry">
      <span>指标结果:</span>
      <n-select v-model:value="indexResult" :options="dataKeyOptions(props.dayKeys)" @update:value="changeTime" />
    </div>

    <n-data-table
      :columns="table.columns"
      :data="table.data"
      bottom-bordered
      :bordered="true"
      :loading="loading"
      :single-line="false"
      :table-layout="'fixed'"
      :scroll-x="3000"
      :max-height="170"
      :style="{ height: 220 + 'px' }"
      v-if="info.chartType === TableChartEnum.TABLE"
    />
    <line-chart :options="getChartOption()" v-else />
  </div>
</template>

<script setup lang="ts">
import { DataQualityRuleResult, ListItem, QueryRuleParam } from '@/types/dataQuality';
import { DataTableColumns } from 'naive-ui';
import { EChartsOption } from 'echarts';
import { DropdownItem, KeyValue } from '@/types/common';
import { queryRuleResult } from '@/service/dataQuality';
import { SUCCESS_CODE } from '@/utils/http';
import { TableChartEnum } from './data';
import { cloneDeep } from 'lodash';

const props = defineProps<{
  dayKeys: string[];
  id: number;
  tableChartInfo?: DataQualityRuleResult;
  width?: string;
  isTry?: boolean;
}>();

const indexResult = ref();
const loading = ref(false);

const info = shallowRef<DataQualityRuleResult>({
  chartType: 'LINE',
});

const table = reactive<{ columns: DataTableColumns<ListItem>; data: { [key: string]: string }[] }>({
  columns: [],
  data: [],
});

//  指标结果下拉列表
const dataKeyOptions = (list: string[]) => {
  const options: DropdownItem[] = [];
  list?.map((item) => {
    options.push({ label: item, value: item });
  });

  return options;
};
//  表格数据
const setTable = () => {
  const columns: DataTableColumns<ListItem> = [];
  const dataList: { [key: string]: string }[] = [];

  info.value?.tableResult?.titles?.map((item: KeyValue<string>) => {
    columns.push({
      title: item.key,
      key: item.value,
      resizable: true,
      width: 180,
    });
  });

  info.value?.tableResult?.rows?.map((item: KeyValue<string>[]) => {
    let obj: { [key: string]: string } = {};
    for (let i = 0; i < item?.length; i++) {
      obj = { ...obj, [item[i].key]: item[i].value };
    }
    dataList.push(obj);
  });

  table.columns = columns;
  table.data = dataList;
};
//  图表数据
const getChartOption = () => {
  const series: any = [];
  info.value?.lineResult?.yaxisList?.map((item: { title: string; values: string[] }) => {
    series.push({
      type: 'line',
      name: item.title,
      data: item.values,
    });
  });

  const options: EChartsOption = {
    xAxis: {
      type: 'category',
      data: info.value?.lineResult?.xaxis?.values ?? [],
    },
    yAxis: {
      type: 'value',
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {},
    grid: {
      top: 30,
      bottom: 35,
      right: 65,
    },
    series: series,
  };

  return options;
};

//  改变时间
const changeTime = async (val: string) => {
  loading.value = true;

  const param: QueryRuleParam = {
    id: props.id,
    dayKey: val,
  };
  const res = await queryRuleResult(param);
  if (res.data.code === SUCCESS_CODE) {
    info.value = res.data.data;
    setTable();

    loading.value = false;
  }
};

onMounted(() => {
  if (!indexResult.value && props.dayKeys?.length > 0 && !props.isTry) {
    indexResult.value = props.dayKeys[0];
    changeTime(props.dayKeys[0]);
  }

  if (props.tableChartInfo && props.isTry) {
    info.value = cloneDeep(props.tableChartInfo);
    setTable();
  }
});
watch(
  () => props.dayKeys,
  () => {
    if (!indexResult.value && props.dayKeys?.length > 0 && !props.isTry) {
      indexResult.value = props.dayKeys[0];
      changeTime(props.dayKeys[0]);
    }
  },
);
// watch(
//   () => props.tableChartInfo,
//   () => {
//     console.log(props.tableChartInfo, '123');

//     if (props.tableChartInfo) {
//       info.value = cloneDeep(props.tableChartInfo);
//       setTable();
//     }
//   },
//   {
//     deep: true,
//   },
// );
</script>

<style scoped lang="scss">
.tableChart {
  width: 90%;
  height: 315px;
  margin: auto auto 16px;
  background: #fff;

  > .select {
    margin: 16px 0 0 16px;

    span {
      margin: auto 16px;
      flex-shrink: 0;
    }
    .n-select {
      width: 220px;
    }
  }

  > .n-data-table {
    width: 95%;
    margin: 16px auto;
    height: 220px;
  }
}
</style>
