<template>
  <div class="detail">
    <div class="searchBox h">
      <div class="h">
        <span>指标类型:</span>
        <n-select v-model:value="detailInfo.sqlType" :options="SQL_OPTIONS" @update:value="sqlTypeChange" />
      </div>

      <div class="h">
        <span>执行间隔:</span>
        <n-select v-model:value="detailInfo.executeTimeType" :options="EXECUTE_TIME_OPTIONS" />
      </div>

      <div class="h">
        <span>状态:</span>
        <n-switch v-model:value="swithFlag" @update:value="switchChange" />
      </div>

      <div class="btnBox h">
        <n-button type="primary" @click="tryRunFn">试运行</n-button>
        <n-button type="primary" @click="save">保存</n-button>
      </div>
    </div>

    <div class="editorBox v">
      <n-form
        ref="formRef"
        :model="detailInfo"
        :rules="rules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item path="qualityName" label="指标名称">
          <n-input v-model:value="detailInfo.qualityName" :maxlength="100" />
        </n-form-item>

        <n-form-item path="comment" label="备注">
          <n-input type="textarea" v-model:value="detailInfo.comment" :rows="3" :maxlength="1000" />
        </n-form-item>

        <n-form-item path="mysqlQuerySql" label="mysql查询sql">
          <n-input
            type="textarea"
            v-model:value="detailInfo.mysqlQuerySql"
            :rows="3"
            :maxlength="1000"
            :disabled="mysqlDisabled"
          />
        </n-form-item>

        <n-form-item path="ckTempTableSql" label="创建ck临时表的sql">
          <n-input
            type="textarea"
            v-model:value="detailInfo.ckTempTableSql"
            :rows="4"
            :disabled="ckTempTableSqlDisabled"
            :maxlength="1000"
          />
        </n-form-item>

        <n-form-item path="ckQuerySql" label="ck查询sql">
          <n-input
            type="textarea"
            v-model:value="detailInfo.ckQuerySql"
            :rows="4"
            :disabled="ckQuerySqlDisabled"
            :maxlength="1000"
          />
        </n-form-item>
      </n-form>
    </div>

    <TableChart
      :day-keys="detailInfo.dayKeys"
      :id="detailInfo.id"
      v-if="isUpdate && detailInfo.sqlType === SQLEnum.INDEX_RESULT"
    />

    <TryRunDialog
      v-model:visible="visible"
      v-model:tryRunObj="tryRunObj"
      :detailInfo="detailInfo"
      :isUpdate="isUpdate"
    />
  </div>
</template>

<script setup lang="ts">
import { queryDetail, saveRule, tryRun } from '@/service/dataQuality';
import { DataQualityRuleResult, ListItem, SaveParam } from '@/types/dataQuality';
import { getQueryString } from '@/utils/common';
import { SUCCESS_CODE } from '@/utils/http';
import { useMessage } from 'naive-ui';
import { SQL_OPTIONS, EXECUTE_TIME_OPTIONS, SQLEnum } from './data';
import { isEmpty } from 'lodash';
import TryRunDialog from './try-run-dialog.vue';
import TableChart from './table-chart.vue';

const visible = ref(false);
const swithFlag = ref(false);
const isUpdate = ref(false);
const formRef = ref();
const message = useMessage();

//  详情表单
const detailInfo = ref<ListItem>({
  id: 0,
  sqlType: 1, //指标类型
  executeTimeType: 1, //执行间隔
  qualityName: '', //指标名称
  comment: '', //备注
  mysqlQuerySql: '', //mysql查询sql
  ckTempTableSql: '', //创建ck临时表的sql
  ckQuerySql: '', //ck查询sql
  status: 0, //status
  dayKeys: [], //时间下拉框
  dataQualityRuleResult: {
    chartType: 'LINE',
    tableResult: {
      titles: [],
      rows: [],
    },
    lineResult: {
      xaxis: {
        title: '',
        values: [],
      },
      yaxisList: [],
    },
  },
});
const tryRunObj = shallowRef<DataQualityRuleResult>({
  chartType: 'LINE',
});

//  禁用逻辑判断
const mysqlDisabled = computed(() => {
  if (detailInfo.value.sqlType === SQLEnum.INDEX_RESULT && !isEmpty(detailInfo.value.ckQuerySql)) return true;
  return false;
});
const ckTempTableSqlDisabled = computed(() => {
  if (detailInfo.value.sqlType === SQLEnum.INDEX_RESULT) return true;
  return false;
});
const ckQuerySqlDisabled = computed(() => {
  if (detailInfo.value.sqlType === SQLEnum.INIT) return true;
  if (detailInfo.value.sqlType === SQLEnum.INDEX_RESULT && !isEmpty(detailInfo.value.mysqlQuerySql)) return true;
  return false;
});

//  校验对象
const rules = computed(() => ({
  qualityName: [
    {
      required: true,
      message: '请输入指标名称',
    },
  ],
  comment: [
    {
      required: true,
      message: '请输入备注',
    },
  ],
  mysqlQuerySql: [
    {
      required: !mysqlDisabled.value,
      message: '请输入mysql查询sql',
    },
  ],
  ckTempTableSql: [
    {
      required: !ckTempTableSqlDisabled.value,
      message: '请输入创建ck临时表的sql',
    },
  ],
  ckQuerySql: [
    {
      required: !ckQuerySqlDisabled.value,
      message: '请输入ck查询sql',
    },
  ],
}));

//  sqltype修改
const sqlTypeChange = (val: number) => {
  if (val === 1 && !isEmpty(detailInfo.value.ckQuerySql)) {
    detailInfo.value.mysqlQuerySql = '';
  }
  if (val === 1 && !isEmpty(detailInfo.value.mysqlQuerySql)) {
    detailInfo.value.ckQuerySql = '';
  }
};
//  状态修改
const switchChange = (val: boolean) => {
  detailInfo.value.status = val ? 1 : 0;
};

//  获取详情
const getDetail = async (id: number) => {
  const res = await queryDetail(id);
  if (res.data.code === SUCCESS_CODE) {
    detailInfo.value = res.data.data;
    swithFlag.value = res.data.data.status === 1;
  }
};
//  保存
const saveApi = async (saveParam: SaveParam) => {
  const res = await saveRule(saveParam);
  if (res.data.code === SUCCESS_CODE) {
    message.success('保存成功!');
  }
};
const tryRunApi = async () => {
  const tryRunParam: SaveParam = {
    ...detailInfo.value,
    dataQualityRuleResult: undefined,
    dayKeys: undefined,
    id: isUpdate.value ? detailInfo.value.id : undefined,
  };
  const res = await tryRun(tryRunParam);
  if (res.data.code === SUCCESS_CODE) {
    if (detailInfo.value.sqlType === SQLEnum.INIT) {
      message.success('试运行成功!');
    } else {
      tryRunObj.value = res.data.data;

      visible.value = true;
    }
  }
};
const save = () => {
  const saveParam: SaveParam = {
    ...detailInfo.value,
    dataQualityRuleResult: undefined,
    dayKeys: undefined,
    id: isUpdate.value ? detailInfo.value.id : undefined,
  };
  formRef.value?.validate((errors: string) => {
    if (!errors) {
      saveApi(saveParam);
    }
  });
};
//  试运行
const tryRunFn = () => {
  formRef.value?.validate((errors: string) => {
    if (!errors) {
      tryRunApi();
    }
  });
};

onMounted(() => {
  const id = getQueryString(window.location.href, 'id');
  if (id) {
    isUpdate.value = true;
    getDetail(parseInt(id));
  } else {
    isUpdate.value = false;
  }
});
</script>

<style scoped lang="scss">
.detail {
  height: 100vh;
  overflow-y: auto;

  .searchBox {
    width: 90%;
    margin: 0 auto 8px;
    height: 60px;

    > div {
      margin: 16px 32px 0 0;

      > span {
        margin: auto 16px auto 0;
        flex-shrink: 0;
      }
      > .n-select {
        width: 220px;
        margin: auto;
      }
      > .n-switch {
        margin: auto;
      }
    }

    > .btnBox {
      margin-left: auto;
      margin-right: 0;

      > .n-button {
        margin: auto 0 auto 8px;
      }
    }
  }

  .editorBox {
    width: 90%;
    margin: 0 auto 16px;
    background: #fff;
    padding: 32px 0 0;
    border-radius: 4px;

    > .n-form {
      width: 95%;
      margin: auto;
    }
  }

  .tableChart {
    width: 90%;
    height: 315px;
    margin: auto auto 16px;
    background: #fff;

    > .table {
      width: 50%;
      height: 100%;

      > div {
        margin: 16px 0 0;

        span {
          margin: auto 16px;
          flex-shrink: 0;
        }
        .n-select {
          width: 220px;
        }
      }

      > .n-data-table {
        width: 95%;
        margin: 16px auto;
        height: 220px;
        tbody {
          height: 100px;
          overflow: auto;
        }
      }
    }
    > .chart {
      width: 50%;
      height: 100%;
    }
  }
}
</style>
