<template>
  <n-modal
    :show="visible"
    :mask-closable="false"
    preset="dialog"
    title="试运行"
    negative-text="取消"
    style="width: 1000px"
    display-directive="'if'"
    @close="clear"
    @negative-click="clear"
  >
    <div class="tryRun">
      <TableChart
        :day-keys="detailInfo.dayKeys"
        :id="detailInfo.id"
        :tableChartInfo="tryRunObj"
        :width="'100%'"
        :isTry="true"
      />
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { DataQualityRuleResult, ListItem } from '@/types/dataQuality';
import TableChart from './table-chart.vue';

const props = defineProps<{
  visible: boolean;
  detailInfo: ListItem;
  tryRunObj: DataQualityRuleResult;
  isUpdate: boolean;
}>();
const emits = defineEmits(['update:visible', 'update:tryRunObj', 'submit']);
const visible = useVModel(props, 'visible', emits);
const tryRunObj = useVModel(props, 'tryRunObj', emits);

const clear = () => {
  visible.value = false;
};
</script>

<style scoped lang="scss">
.tryRun {
  height: 280px;
  padding: 0 16px;
}
</style>
