<template>
  <PageTemplate>
    <template #header>
      <div>数据准确性校验</div>
    </template>
    <template #filterNormal>
      <SearchForm
        ref="searchFormRef"
        :searchList="searchList"
        @search="onSearch"
        :clearFn="onClear"
        @valueChange="handleValueChange"
      />
    </template>
    <template #KPICard>
      <Card :data="cardData?.data" :loading="cardLoading" />
    </template>
    <template #table>
      <Table
        :loading="yieldDetailLoading"
        :testAreaMap="testAreaMap"
        :searchParams="searchParams"
        v-model:tableData="tableData"
        v-model:pageInfo="pageInfo"
        @sort="handleSort"
      />
    </template>
  </PageTemplate>
</template>

<script setup lang="ts">
import { SEARCH_LIST } from './constant';
import { PageTemplate, SearchForm, SearchFormParam, PaginationType, SearchFormItem } from '@guwave/components';
import { getConstantMap, getExactMatchDetail, getExactYieldDetail } from '@/service/dataCenterQuality';
import { isNil, unset } from 'lodash';

import Card from './components/Card.vue';
import Table from './components/Table.vue';
import { getQueryString } from '@guwave/utils';
import { dayjs } from 'element-plus';
import { getSearchItemUrlParams } from '../utils';
import useDashUrlInfoStore from '@/store/system/dashboardUrl';

const dashBoardInfo = useDashUrlInfoStore();
const { factoryMode } = storeToRefs(dashBoardInfo);

const { data: cardData, isLoading: cardLoading, execute: fetchMatchDetail } = getExactMatchDetail();
const { data: yieldDetailRes, isLoading: yieldDetailLoading, execute: fetchYieldDetail } = getExactYieldDetail();

const searchFormRef = ref(null);
const searchList = ref<SearchFormItem[]>(SEARCH_LIST());
const defaultSearch = ref();
let searchItemUrlParams = {};

const testAreaMap = ref({});
const tableData = ref<Record<string, any>[]>();

const searchParams = ref<SearchFormParam>();
const sortParams = reactive<{ sortField?: string; sortDirection?: 'ASC' | 'DESC' }>({});
const pageInfo = reactive<PaginationType>({
  pageIndex: 1,
  pageSize: 20,
  total: 0,
  // 页码改变回调
  currentChange: (current: number) => {
    pageInfo.pageIndex = current;
    getTableData();
  },
  // 每页条数改变回调
  sizeChange: (size: number) => {
    pageInfo.pageSize = size;
    getTableData();
  },
});

// 获取卡片数据
const getCardData = () => {
  const params = {
    ...searchParams.value,
  };

  fetchMatchDetail({ data: params });
};
// 获取表格数据
const getTableData = () => {
  const params = {
    ...searchParams.value,
    ...sortParams,
    pageIndex: pageInfo.pageIndex,
    pageSize: pageInfo.pageSize,
  };

  const testAreaType = testAreaMap.value[params?.testArea as string];
  const isCP = testAreaType === 'CP';

  if (!isCP && sortParams.sortField === 'waferNo') {
    unset(params, 'sortField');
    unset(params, 'sortDirection');
  }

  fetchYieldDetail({ data: params });
};

const onSearch = (params) => {
  pageInfo.pageIndex = 1;
  const testAreaType = testAreaMap.value[params?.testArea as string];
  const isCP = testAreaType === 'CP';

  const newParams = {
    ...params,
    storageTime: undefined,
    startTime: params?.storageTime ? dayjs(params.storageTime?.[0]).format('YYYY-MM-DD 00:00:00') : undefined,
    endTime: params?.storageTime ? dayjs(params.storageTime?.[1]).format('YYYY-MM-DD 23:59:59') : undefined,
    waferNoList: isCP ? params?.waferNoList : undefined,
    sblotIdList: isCP ? undefined : params?.sblotIdList,
  };

  searchParams.value = newParams;

  getCardData();
  getTableData();
};

const onClear = () => {
  // 恢复默认值
  handleValueChange({ key: 'testArea', value: 'CP' }); // handleValueChange不会自动触发
  searchFormRef.value?.setSearchVal(defaultSearch.value, { searchImmediate: false }); // 不要触发搜索，因为reset会触发一次搜索
};

watch(yieldDetailRes, (yieldDetailVal) => {
  if (yieldDetailVal?.data) {
    tableData.value = yieldDetailVal.data.data;
    pageInfo.pageIndex = yieldDetailVal.data.pageIndex;
    pageInfo.pageSize = yieldDetailVal.data.pageSize;
    pageInfo.total = yieldDetailVal.data.total;
  } else {
    tableData.value = [];
    pageInfo.pageIndex = 1;
    pageInfo.pageSize = 20;
    pageInfo.total = 0;
  }
});

// 点击表格排序的响应
const handleSort = ({ prop, order }) => {
  if (isNil(order)) {
    sortParams.sortField = undefined;
    sortParams.sortDirection = undefined;
  } else {
    sortParams.sortField = prop;
    sortParams.sortDirection = order === 'ascending' ? 'ASC' : 'DESC';
  }
  getTableData();
};

// testStage会影响展示的筛选项
const handleValueChange = ({ value, key }) => {
  if (key === 'testArea') {
    const testAreaType = testAreaMap.value[value];
    const isCP = testAreaType === 'CP';
    searchList.value.forEach((item) => {
      if (isCP && item.key === 'sblotIdList') {
        item.hidden = true;
      } else if (!isCP && item.key === 'waferNoList') {
        item.hidden = true;
      } else if (!factoryMode.value && item.key === 'subCustomerList') {
        item.hidden = true;
      } else {
        item.hidden = false;
      }
    });
  }
};

const init = () => {
  // 获取跳转参数
  const url = decodeURIComponent(location.href);

  // 默认时间：默认->startTime && endTime->storageTime
  const startTime = getQueryString(url, 'startTime');
  const endTime = getQueryString(url, 'endTime');

  let start = dayjs().subtract(4, 'week').format('YYYY-MM-DD');
  let end = dayjs().format('YYYY-MM-DD');
  // TODO：startTime和endTime 待废弃不要再使用，用storageTime代替,storageTime在url里用逗号链接。
  if (startTime && endTime) {
    // 如果url有startTime和endTime，则以url为准
    start = startTime;
    end = endTime;
  }

  let storageTime = [start, end];
  const urlTime = getQueryString(url, 'storageTime')?.trim();
  if (urlTime) {
    if (urlTime === 'null' || urlTime === 'undefined' || urlTime === '' || urlTime === '[]') {
      storageTime = [];
    } else {
      const arr = urlTime.split(',');
      storageTime = [arr?.[0], arr?.[1]];
    }
  }

  // 默认 testArea
  const testArea = getQueryString(url, 'testArea') ?? 'CP'; // 如果url有testArea，则以url为准
  handleValueChange({ key: 'testArea', value: testArea }); // 根据testArea 更新searchList

  defaultSearch.value = {
    ...searchItemUrlParams,
    testArea, // 单选（覆盖上面searchItemUrlParams里的testArea：[]）
    storageTime: storageTime.length > 0 ? storageTime : undefined, //searchItemUrlParams没有处理时间
  };

  searchFormRef.value?.setSearchVal(defaultSearch.value); // 会触发一次handleSearch
};

// 如果factoryMode请求的慢则handleValueChange内没有正确处理factoryMode，需要这个watch兜底
watch(
  factoryMode,
  (factoryMode) => {
    if (isNil(factoryMode)) return;
    // 处理 factoryMode

    if (!factoryMode) {
      searchList.value.forEach((item) => {
        // 只处理subCustomerList，否则会影响之前处理的
        if (item.key === 'subCustomerList') {
          item.hidden = true;
        }
      });
    }
  },
  { deep: true, immediate: true },
);

onMounted(async () => {
  const res = await getConstantMap();
  testAreaMap.value = res?.data?.data?.testArea ?? {};

  // 跳转参数不应该允许subCustomerList。所以直接传SEARCH_LIST()，但是getSearchItemUrlParams里的subCustomerList永远是undefined
  searchItemUrlParams = getSearchItemUrlParams(SEARCH_LIST());
  init();
});
</script>

<style scoped lang="scss"></style>
