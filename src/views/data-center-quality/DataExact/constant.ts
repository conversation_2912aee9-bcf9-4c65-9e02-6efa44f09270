import { SearchFormTypeEnum, SearchFormItem, DATE_SHORTCUTS } from '@guwave/components';
import { getExactOptions } from '@/service/dataCenterQuality';

import { dayjs } from 'element-plus';
import { getSubCustomer } from '@/service/common';

export const PASS_PARTS_RETESTED = 'PASS_PARTS_RETESTED';

export const SEARCH_LIST = (): SearchFormItem[] => {
  const list = [
    {
      type: SearchFormTypeEnum.DATE,
      label: '数据入库时间',
      key: 'storageTime',
      nativeAttr: {
        type: 'daterange',
        unlinkPanels: true,
        rangeSeparator: '-',
        startPlaceholder: '开始时间',
        endPlaceholder: '结束时间',
        shortcuts: DATE_SHORTCUTS,
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      label: 'Sub Customer',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'subCustomerList',
      api: getSubCustomer,
      dropdownResFn: (resData) => {
        const options = resData.map((item) => ({
          label: item.subCustomerCode,
          value: item.subCustomerCode,
        }));
        return options;
      },
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Factory',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'factoryList',
      dropdownKey: 'FACTORY',
      api: getExactOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Device',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'deviceIdList',
      api: getExactOptions,
      dropdownKey: 'DEVICE_ID',
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Test Area',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'testArea',
      dropdownKey: 'TEST_AREA',
      api: getExactOptions,
      nativeAttr: {
        clearable: false,
      },
    },
    {
      label: 'Test Stage',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'testStageList',
      dropdownKey: 'TEST_STAGE',
      api: getExactOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Lot',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'lotIdList',
      dropdownKey: 'LOT_ID',
      api: getExactOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Wafer',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'waferNoList',
      dropdownKey: 'WAFER_NO',
      api: getExactOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Sub Lot',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'sblotIdList',
      dropdownKey: 'SBLOT_ID',
      api: getExactOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Lot Type',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'lotTypeList',
      dropdownKey: 'LOT_TYPE',
      api: getExactOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'MES Match Flag',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'mesMatchFlagList',
      dropdownKey: 'MES_MATCH_FLAG',
      api: getExactOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Data Accuracy Check',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'dataAccuracyCheckList',
      dropdownKey: 'DATA_ACCURACY_CHECK',
      api: getExactOptions,
      nativeAttr: {
        multiple: true,
      },
    },
  ];
  // return list;

  // 重写api
  const res = list.map((item) => {
    // 删除filterKey 重写api的请求参数
    return {
      ...item,
      api: (params) => {
        if (item.key === 'subCustomerList') {
          return item.api(); //subCustomer不需要其他参数
        }
        const newParams = {
          ...params,
          filterField: params.field,
          field: undefined,
          startTime: params?.storageTime ? dayjs(params.storageTime?.[0]).format('YYYY-MM-DD 00:00:00') : undefined,
          endTime: params?.storageTime ? dayjs(params.storageTime?.[1]).format('YYYY-MM-DD 23:59:59') : undefined,
          storageTime: undefined,
        };

        return item.api(newParams);
      },
    };
  });
  return res;
};

export const COLUMN_LIST = [
  {
    key: 'deviceName',
    label: 'Device',
    minWidth: 140,
  },
  {
    key: 'testStage',
    label: 'Test Stage',
    minWidth: 140,
  },
  {
    key: 'lotId',
    label: 'Lot',
    minWidth: 140,
  },
  {
    key: 'waferNo',
    label: 'Wafer',
    minWidth: 140,
  },
  {
    key: 'lotType',
    label: 'Lot Type',
    minWidth: 140,
  },
  {
    key: 'accuracyCheck',
    label: 'Data Accuracy Check',
    minWidth: 200,
  },
  {
    key: 'firstYield',
    label: 'First Yield',
    minWidth: 120,
  },
  {
    key: 'finalYield',
    label: 'Final Yield',
    minWidth: 130,
  },
  {
    key: 'inputCount',
    label: 'Input Count',
    minWidth: 140,
  },
  {
    key: 'passCount',
    label: 'Pass Count',
    minWidth: 130,
  },
  {
    key: 'matchFlag',
    label: 'MES Match Flag',
    minWidth: 170,
  },
  {
    key: 'stdFileCount',
    label: 'File Count',
    minWidth: 120,
  },
  {
    key: 'stdStartTime',
    label: 'Start Time',
    minWidth: 180,
  },
];
