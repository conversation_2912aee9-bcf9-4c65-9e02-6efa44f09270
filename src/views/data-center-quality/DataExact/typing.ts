export type MatchDetailRes = {
  yieldAccuracy: number;
  normalRate: number;
  matchRate: number;
  mesInputRate: number;
  dataAccuracyCheckPieItems: {
    category: string;
    rate: number;
  }[];
  mesMatchTypePieItems: {
    category: string;
    rate: number;
  }[];
};

export type DetailDialogParams = {
  deviceName: string;
  testArea: string;
  lotId: string;
  testStage?: string;
  waferId?: string;
  dataSource?: string;
};
