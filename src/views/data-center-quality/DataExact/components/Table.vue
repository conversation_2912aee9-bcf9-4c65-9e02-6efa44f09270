<template>
  <CommonTable
    v-loading="loading"
    title="批次良率详情"
    :data="tableData"
    v-model:pagination="pageInfo"
    @sort-change="handleSort"
    border
  >
    <template #headerRightEnd>
      <el-button :loading="exportDataLoading" @click="handleExportData" :disabled="tableData?.length === 0">
        导出数据
      </el-button>
    </template>

    <el-table-column
      v-for="item in colList"
      :key="item.key"
      show-overflow-tooltip
      :prop="item.key"
      :label="item.label"
      :min-width="item.minWidth"
      sortable="custom"
    >
      <template #default="scoped">
        <template v-if="item.key === 'accuracyCheck'">
          <template v-if="scoped.row.accuracyCheckType == PASS_PARTS_RETESTED">
            <el-button
              type="text"
              style="padding: 0px !important"
              :loading="scoped.row.loading"
              @click="() => handleAcccuracyDown({ row: scoped.row, index: scoped.$index })"
            >
              {{ scoped.row.accuracyCheck }}
            </el-button>
          </template>
          <span v-else>{{ scoped.row.accuracyCheck }}</span>
        </template>
      </template>
    </el-table-column>

    <el-table-column width="80" :align="'left'" fixed="right" label="操作">
      <template #default="scoped">
        <el-button
          type="text"
          style="padding: 0px !important"
          :disabled="scoped.stdFileCount < 1"
          @click="openDetail(scoped.row)"
        >
          详情
        </el-button>
      </template>
    </el-table-column>
  </CommonTable>
  <DetailDialog
    v-if="detailDialogVisible"
    :visible="detailDialogVisible"
    :data="detailDialogData"
    @on-close="detailDialogVisible = false"
  />
</template>

<script setup lang="ts">
import { downloadGoodRetest, getExactExport } from '@/service/dataCenterQuality';

import { PASS_PARTS_RETESTED, COLUMN_LIST } from '../constant';
import { PaginationType, CommonTable } from '@guwave/components';

import DetailDialog from './DetailDialog.vue';
import { pick } from 'lodash';
import { downloadExcelFileFromBlobRes } from '../../utils';

const props = defineProps<{
  loading: boolean;
  searchParams: any;
  testAreaMap: Record<string, string>;
}>();

const pageInfo = defineModel<PaginationType>('pageInfo');
const tableData = defineModel<any[]>('tableData');

const emits = defineEmits(['sort']);

// 非cp不展示waferId列
const colList = computed(() => {
  const { testArea } = props.searchParams ?? {};

  const testAreaType = props.testAreaMap?.[testArea] ?? 'CP';
  const isCP = testAreaType === 'CP';

  if (testArea && !isCP) {
    return COLUMN_LIST.filter((item) => item.key !== 'waferNo');
  }
  return COLUMN_LIST;
});

// 点击表格排序的响应
const handleSort = ({ prop, order }) => {
  emits('sort', { prop, order });
};

// 好品复测的下载文件响应
const handleAcccuracyDown = async ({ row, index }) => {
  tableData.value[index].loading = true;

  const postParam = {
    downloadType: 'LIST',
    deviceId: row?.deviceName,
    ...pick(row, ['testArea', 'testStage', 'lotId', 'waferNo', 'lotType']),
  };
  const res = await downloadGoodRetest(postParam);
  await downloadExcelFileFromBlobRes(res);
  tableData.value[index].loading = false;
};

const detailDialogData = ref<Record<string, any>>();
const detailDialogVisible = ref<boolean>(false);
const openDetail = (row: Record<string, any>) => {
  detailDialogData.value = row;
  detailDialogVisible.value = true;
};

// 点击导出数据按钮的响应
const exportDataLoading = ref<boolean>(false);
const handleExportData = async () => {
  exportDataLoading.value = true;
  const res = await getExactExport({ ...props.searchParams });
  await downloadExcelFileFromBlobRes(res, '数据准确性');
  exportDataLoading.value = false;
};
</script>
<style scoped lang="scss"></style>
