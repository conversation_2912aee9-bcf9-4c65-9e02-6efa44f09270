<template>
  <el-dialog
    :model-value="visible"
    :title="dialogTitle + ' 数据准确性详情'"
    :width="'100%'"
    top="36px"
    @close="emits('onClose')"
  >
    <div v-loading="isLoading">
      <div class="detail-table-part">
        <div class="header">Device, Test Stage, Test Program, Lot & {{ isFT ? 'Sub Lot' : 'Wafer' }} 维度良率展示</div>
        <el-table fit size="small" :data="detailTableData" border height="195px">
          <el-table-column
            v-for="item in yieldTableCols"
            :key="item.key"
            :prop="item.key"
            :label="item.label"
            :min-width="item?.minWidth"
            :width="item.key === 'testProgram' ? calWidth(detailTableData, item.key) : undefined"
            show-overflow-tooltip
          >
            <template #default="scoped">
              <template v-if="item.key === 'accuracyCheck'">
                <el-button
                  v-if="scoped.row.accuracyCheckType == PASS_PARTS_RETESTED"
                  :loading="scoped.row.loading"
                  :disabled="isNil(scoped.row.testProgram) || scoped.row.testProgram === ''"
                  size="small"
                  link
                  @click="handleAcccuracyDown({ index: scoped.$index, row: scoped.row })"
                >
                  {{ scoped.row.accuracyCheck }}
                </el-button>
                <span v-else>{{ scoped.row.accuracyCheck }}</span>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="file-table-part">
        <div class="header">
          <div>原始文件列表</div>
          <el-button type="primary" :disabled="selectedFileIds.length === 0" @click="handleClickBatchRepairData">
            批量数据修复
          </el-button>
        </div>
        <el-table
          fit
          size="small"
          :border="true"
          height="calc(100vh - 460px)"
          :data="fileTableData"
          @selection-change="handleSelectionChange"
        >
          <template v-slot:empty><dc-empty /></template>
          <el-table-column type="selection" width="40" :selectable="setCanSelect" />
          <el-table-column
            v-for="item in fileTableCols"
            :key="item.key"
            :prop="item.key"
            :label="item.label"
            :min-width="item?.minWidth"
            :width="['testProgram', 'fileName'].includes(item.key) ? calWidth(fileTableData, item.key) : undefined"
            show-overflow-tooltip
          >
            <template #default="scoped">
              <template v-if="item.key === 'fileFlag'">
                <el-button
                  v-if="scoped.row.fileFlag == '片内复测'"
                  size="small"
                  link
                  :loading="scoped.row.loading"
                  @click="() => handleFileFlagDown({ row: scoped.row, index: scoped.$index })"
                >
                  {{ scoped.row.fileFlag }}
                </el-button>
                <span v-else>{{ scoped.row.fileFlag }}</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="110">
            <template #default="scoped">
              <el-button
                link
                size="small"
                style="padding: 0px !important"
                :disabled="!scoped.row.supportDataRepair"
                @click="clickRepairData(scoped.row)"
              >
                数据修复
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { downloadFileRetest, downloadGoodRetest, getExactDetailList } from '@/service/dataCenterQuality';
import { PASS_PARTS_RETESTED } from '../constant';
import { getTextWidth } from '@/utils/common';

import { max, isNil, pick } from 'lodash';
import { DETAIL_YIELD_COLS, DETAIL_FILE_COLS } from './contants';
import { downloadExcelFileFromBlobRes } from '../../utils';
import { addTag } from '@guwave/utils';
import qs from 'qs';

const props = defineProps<{ visible: boolean; data: Record<string, any> }>();

const emits = defineEmits<{ (e: 'onClose'): void }>();

const { data: listRes, isLoading, execute: fetchList } = getExactDetailList();

const isFT = computed<boolean>(() => props.data.testArea === 'FT');

const yieldTableCols = computed(() => {
  if (isFT.value) {
    return DETAIL_YIELD_COLS.filter((item) => item.key !== 'waferId');
  } else {
    return DETAIL_YIELD_COLS.filter((item) => item.key !== 'sbLotId');
  }
});

const fileTableCols = computed(() => {
  if (isFT.value) {
    return DETAIL_FILE_COLS.filter((item) => item.key !== 'waferId');
  } else {
    return DETAIL_FILE_COLS.filter((item) => !['fileFlag', 'sbLotId'].includes(item.key));
  }
});

const calWidth = (tabel: any[], key: string) => {
  const width = tabel.map((item) => getTextWidth(item[key], 12));
  const res = max([100, ...width]) + 20;
  return res;
};

watch(listRes, (listResVal) => {
  if (listResVal?.data) {
    const { detailList, fileList } = listResVal.data;
    detailTableData.value = detailList;
    fileTableData.value = fileList.map((file) => {
      if (file.fileName.endsWith('.zip')) {
        return {
          ...file,
          fileName: file.fileName.slice(0, -4),
        };
      }
      return file;
    });
  } else {
    detailTableData.value = [];
    fileTableData.value = [];
  }
});

const detailTableData = ref<Record<string, any>[]>([]);
const fileTableData = ref<Record<string, any>[]>([]);
const selectedFileIds = ref<number[]>([]);

const dialogTitle = computed<string>(() => `${props.data?.deviceName},${props.data?.testStage},${props.data?.lotId}`);

// 好品复测的下载文件响应
const handleAcccuracyDown = async ({ row, index }) => {
  detailTableData.value[index].loading = true;
  const postParam = {
    downloadType: 'DETAIL',
    deviceId: row?.deviceName,
    testArea: row?.testArea,
    testStage: row?.testStage,
    lotId: row?.lotId,
    waferNo: row?.waferNo,
    testProgram: row?.testProgram,
    lotType: row?.lotType,
  };
  if (row.testArea === 'FT') {
    postParam['sblotId'] = row?.sbLotId;
  }

  const res = await downloadGoodRetest(postParam);

  await downloadExcelFileFromBlobRes(res);

  detailTableData.value[index].loading = false;
};

//片内复测的下载文件响应
const handleFileFlagDown = async ({ row, index }) => {
  fileTableData.value[index].loading = true;
  const postParam = {
    fileId: row?.fileId,
    deviceId: row?.deviceName,
    testStage: row?.testStage,
    lotId: row?.lotId,
    sblotId: row?.sbLotId,
  };

  const res = await downloadFileRetest(postParam);

  await downloadExcelFileFromBlobRes(res);

  fileTableData.value[index].loading = false;
};

const handleSelectionChange = (rows) => {
  selectedFileIds.value = rows.map((item) => item.fileId);
};

const setCanSelect = (row) => row.supportDataRepair;

const clickRepairData = (row) => {
  jumpRepairData([row.fileId]);
};
const handleClickBatchRepairData = () => {
  jumpRepairData(selectedFileIds.value);
};

const jumpRepairData = (fileIds: number[]) => {
  const title = dialogTitle.value;
  addTag({
    baseUrl: '/bpms',
    tagName: title,
    url: `/#/dataRecovery?${qs.stringify({ fileIds, title })} `,
  });
};

onMounted(() => {
  const params = {
    deviceId: props.data.deviceName,
    ...pick(props.data, ['testArea', 'lotId', 'testStage', 'waferNo']),
  };
  fetchList({ data: params });
});
</script>

<style scoped lang="scss">
.detail-table-part {
  .header {
    height: 32px;
    line-height: 32px;
    margin-bottom: 8px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 700;
  }
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}
.file-table-part {
  margin-top: 8px;
  .header {
    display: flex;
    justify-content: space-between;
    height: 32px;
    line-height: 32px;
    margin-bottom: 8px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 700;
  }
}
</style>
