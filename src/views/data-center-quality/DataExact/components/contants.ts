export const DETAIL_YIELD_COLS = [
  {
    key: 'deviceName',
    label: 'Device',
    minWidth: 120,
  },
  {
    key: 'testStage',
    label: 'Test Stage',
    minWidth: 80,
  },
  {
    key: 'testProgram',
    label: 'Test Program',
  },
  {
    key: 'lotId',
    label: 'Lot',
    minWidth: 90,
  },
  {
    key: 'waferId',
    label: 'Wafer',
    minWidth: 90,
  },
  {
    key: 'sbLotId',
    label: 'Sub Lot',
    minWidth: '90',
  },
  {
    key: 'lotType',
    label: 'Lot Type',
    minWidth: 100,
  },
  {
    key: 'accuracyCheck',
    label: 'Data Accuracy Check',
    minWidth: 150,
  },
  {
    key: 'firstYield',
    label: 'First Yield',
    minWidth: 80,
  },
  {
    key: 'finalYield',
    label: 'Final Yield',
    minWidth: 80,
  },
  {
    key: 'inputCount',
    label: 'Input Count',
    minWidth: 90,
  },
  {
    key: 'passCount',
    label: 'Pass Count',
    minWidth: 90,
  },
  {
    key: 'summaryYield',
    label: 'MES Yield',
    minWidth: 80,
  },
  {
    key: 'summaryCount',
    label: 'MES Count',
    minWidth: 90,
  },
  {
    key: 'summaryPassCount',
    label: 'MES Pass Count',
    minWidth: 120,
  },
  {
    key: 'matchFlag',
    label: 'MES Match Flag',
    minWidth: 120,
  },
  {
    key: 'stdFileCount',
    label: 'File Count',
    minWidth: 80,
  },
  {
    key: 'lossCount',
    label: 'Loss Count',
    minWidth: 85,
  },
  {
    key: 'defectCount',
    label: 'Defect Count',
    minWidth: 100,
  },
  {
    key: 'stdStartTime',
    label: 'Start Time',
    minWidth: 145,
  },
];

export const DETAIL_FILE_COLS = [
  {
    key: 'deviceName',
    label: 'Device',
    minWidth: 120,
  },
  {
    key: 'testStage',
    label: 'Test Stage',
    minWidth: 80,
  },
  {
    key: 'testProgram',
    label: 'Test Program',
  },
  {
    key: 'lotId',
    label: 'Lot',
    minWidth: 90,
  },
  {
    key: 'waferId',
    label: 'Wafer',
    minWidth: 90,
  },
  {
    key: 'sbLotId',
    label: 'Sub Lot',
    minWidth: 90,
  },

  {
    key: 'flowId',
    label: 'Flow ID',
    minWidth: 70,
  },
  {
    key: 'fileFlag',
    label: '线上复测Flag',
    minWidth: 100,
  },
  {
    key: 'retestHardBin',
    label: 'Retest HBin Number',
    minWidth: 150,
  },
  {
    key: 'fileName',
    label: 'File Name',
  },

  {
    key: 'stdStartTime',
    label: 'Start Time',
    minWidth: 145,
  },
  {
    key: 'endTime',
    label: 'End Time',
    minWidth: 145,
  },

  {
    key: 'inputCount',
    label: 'Input Count',
    minWidth: 90,
  },
  {
    key: 'passCount',
    label: 'Pass Count',
    minWidth: 90,
  },
  {
    key: 'failCount',
    label: 'Fail Count',
    minWidth: 90,
  },
];
