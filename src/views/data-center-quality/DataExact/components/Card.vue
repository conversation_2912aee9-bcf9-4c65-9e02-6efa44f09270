<template>
  <div class="card-container">
    <div class="header">批次匹配详情</div>
    <div class="batch-match-detail" v-loading="loading">
      <div class="batch-match-detail-left card">
        <div class="batch-match-detail-left-title">批次数据准确率</div>
        <div class="batch-match-detail-left-context">{{ toPercent(data?.yieldAccuracy) }}</div>
      </div>
      <div class="batch-match-detail-right card">
        <div>
          <div class="title">批次数据准确性分布图</div>
          <div class="context">
            <div class="show-info-warp">
              <div class="show-info-title">正常数据比例</div>
              <div class="show-info-value">{{ toPercent(data?.normalRate) }}</div>
            </div>
            <div class="chart-wrap">
              <Pie
                :data="
                  (data?.dataAccuracyCheckPieItems || []).map(({ category, rate }) => ({
                    indexKey: category,
                    indexValue: rate,
                  }))
                "
              />
            </div>
          </div>
        </div>
        <div>
          <div class="title">测试文件数据流与MES数据流数据一致性</div>
          <div class="context">
            <div class="show-info-warp">
              <div class="show-info-title">MES数据入库比例</div>
              <div class="show-info-value">{{ toPercent(data?.mesInputRate) }}</div>
              <div class="show-info-title">批次良率匹配比例</div>
              <div class="show-info-value">{{ toPercent(data?.matchRate) }}</div>
            </div>
            <div class="chart-wrap">
              <Pie
                :data="
                  (data?.mesMatchTypePieItems || []).map(({ category, rate }) => ({
                    indexKey: category,
                    indexValue: rate,
                  }))
                "
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Pie from '../../components/Pie.vue';

import { toPercent } from '@/utils/number';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const props = defineProps<{
  data: any;
  loading: boolean;
}>();
</script>

<style scoped lang="scss">
.card-container {
  .header {
    font-size: 16px;
    font-weight: 700;
  }
  .batch-match-detail {
    display: flex;
    .batch-match-detail-left {
      flex: 0 0 160px;
      margin-right: 8px;
      .batch-match-detail-left-title {
        line-height: 42px;
        font-size: 14px;
        color: #565656;
      }
      .batch-match-detail-left-context {
        font-size: 48px;
        color: #161616;
        line-height: 64px;
        margin-top: 8px;
        font-family: Segoe UI-Semibold, Segoe UI;
        font-weight: 600;
      }
    }

    .batch-match-detail-right {
      flex: 1 1 auto;
      display: flex;
      height: 240px;
      > div {
        flex: 1 1;
      }
      > div:first-child {
        border-right: 1px solid #e6e6e6;
        padding-right: 8px;
      }
      > div:last-child {
        padding-left: 8px;
      }
      .title {
        font-weight: 700;
        line-height: 24px;
      }
      .context {
        display: flex;
        .show-info-warp {
          flex: 0 0 150px;
          .show-info-title {
            margin-top: 16px;
            font-size: 12px;
            color: #565656;
            line-height: 24px;
          }
          .show-info-value {
            font-size: 36px;
            line-height: 48px;
            font-family: Segoe UI-Semibold, Segoe UI;
            font-weight: 600;
          }
        }
        .chart-wrap {
          flex: 1 1;
          height: 200px;
        }
      }
    }
    .card {
      padding: 8px;
      background: #fff;
      -webkit-box-shadow: 1px 1px 4px 1px rgba(0, 0, 0, 0.2);
      box-shadow: $gBoxShadowLighter;
      border-radius: 4px;
    }
  }
}
</style>
