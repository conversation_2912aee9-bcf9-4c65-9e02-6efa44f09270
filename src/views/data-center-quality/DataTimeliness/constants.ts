import { DATE_SHORTCUTS, SearchFormItem, SearchFormTypeEnum } from '@guwave/components';
import { getTimelinessOptions } from '@/service/dataCenterQuality';

import { dayjs } from 'element-plus';
import { getSubCustomer } from '@/service/common';

export const SEARCH_LIST = (): SearchFormItem[] => {
  const list = [
    {
      type: SearchFormTypeEnum.DATE,
      label: '数据入库时间',
      key: 'storageTime',
      nativeAttr: {
        type: 'daterange',
        unlinkPanels: true,
        rangeSeparator: '-',
        startPlaceholder: '开始时间',
        endPlaceholder: '结束时间',
        shortcuts: DATE_SHORTCUTS,
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      label: 'Sub Customer',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'subCustomerList',
      api: getSubCustomer,
      dropdownResFn: (resData) => {
        const options = resData.map((item) => ({
          label: item.subCustomerCode,
          value: item.subCustomerCode,
        }));
        return options;
      },
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Factory',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'factoryList',
      dropdownKey: 'FACTORY',
      api: getTimelinessOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Device',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'deviceIdList',
      dropdownKey: 'DEVICE_ID',
      api: getTimelinessOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Test Area',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'testArea',
      dropdownKey: 'TEST_AREA',
      api: getTimelinessOptions,
      nativeAttr: {
        clearable: false,
      },
    },
    {
      label: 'Test Program',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'testProgramList',
      dropdownKey: 'TEST_PROGRAM',
      api: getTimelinessOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Test Stage',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'testStageList',
      dropdownKey: 'TEST_STAGE',
      api: getTimelinessOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Lot',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'lotIdList',
      dropdownKey: 'LOT_ID',
      api: getTimelinessOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Wafer',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'waferNoList',
      api: getTimelinessOptions,
      dropdownKey: 'WAFER_NO',
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Sub Lot',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'sblotIdList',
      api: getTimelinessOptions,
      dropdownKey: 'SBLOT_ID',
      nativeAttr: {
        multiple: true,
      },
    },
  ];

  // 重写api
  const res = list.map((item) => {
    // 删除filterKey 重写api的请求参数
    return {
      ...item,
      api: (params) => {
        if (item.key === 'subCustomerList') {
          return item.api(); //subCustomer不需要其他参数
        }

        const newParams = {
          ...params,
          filterField: params.field,
          field: undefined,
          startTime: params?.storageTime ? dayjs(params.storageTime?.[0]).format('YYYY-MM-DD 00:00:00') : undefined,
          endTime: params?.storageTime ? dayjs(params.storageTime?.[1]).format('YYYY-MM-DD 23:59:59') : undefined,
          storageTime: undefined,
        };
        return item.api(newParams);
      },
    };
  });

  return res;
};

export const TOP_10_COLUMNS = [
  {
    title: 'Pos.',
    dataIndex: 'index',
    width: 60,
  },
  {
    title: 'Factory',
    dataIndex: 'factory',
    width: 100,
  },
  {
    title: 'Tester',
    dataIndex: 'tester',
    width: 80,
  },
  {
    title: 'File Name',
    dataIndex: 'fileName',
    width: 100,
  },
  {
    title: 'File Location',
    dataIndex: 'fileLocation',
    width: 120,
  },
  {
    title: 'FTP 加载时间',
    dataIndex: 'ftpLoadTime',
    width: 160,
  },
  {
    title: 'Update Time',
    dataIndex: 'updateTime',
    width: 160,
  },
  {
    title: 'End Time',
    dataIndex: 'endTime',
    width: 160,
  },
  {
    title: '整体耗时/h',
    dataIndex: 'costTime',
    width: 120,
  },
  {
    title: 'File Size',
    dataIndex: 'fileSize',
    width: 100,
  },
];
