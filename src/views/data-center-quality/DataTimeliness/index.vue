<template>
  <PageTemplate v-loading="isLoading">
    <template #header>
      <div>数据入库时效性分析</div>
    </template>
    <template #filterNormal>
      <SearchForm
        ref="searchFormRef"
        :searchList="searchList"
        @search="onSearch"
        :clearFn="onClear"
        @valueChange="handleValueChange"
      />
    </template>
    <template #KPICard>
      <Card :data="timeLinessRes" :inDbStream="inDbStream" :top10Data="top10Data" />
    </template>
  </PageTemplate>
</template>

<script setup lang="ts">
import { SEARCH_LIST } from './constants';
import { getConstantMap, getTimeliness } from '@/service/dataCenterQuality';
import { get, isEmpty, isNil } from 'lodash';
import { toPercent } from '@/utils/number';
import { PageTemplate, SearchFormItem, SearchForm, SearchFormParam } from '@guwave/components';

import Card from './Card.vue';
import useDashUrlInfoStore from '@/store/system/dashboardUrl';
import { dayjs } from 'element-plus';

const dashBoardInfo = useDashUrlInfoStore();
const { factoryMode } = storeToRefs(dashBoardInfo);

const searchFormRef = ref(null);
const searchList = ref<SearchFormItem[]>(SEARCH_LIST()); // 默认是CP
const testAreaMap = ref({});

const { data: timeLinessRes, isLoading, execute: fetchData } = getTimeliness();

const inDbStream = ref<{ total: string; steps: string[] }>();
const top10Data = ref<Record<string, any>[]>([]);

const defaultSearch = ref();

const onSearch = (params: SearchFormParam) => {
  const testAreaType = testAreaMap.value[params?.testArea as string];
  const isCP = testAreaType === 'CP';

  const newParams = {
    ...params,
    storageTime: undefined,
    startTime: params?.storageTime ? dayjs(params.storageTime?.[0]).format('YYYY-MM-DD 00:00:00') : undefined,
    endTime: params?.storageTime ? dayjs(params.storageTime?.[1]).format('YYYY-MM-DD 23:59:59') : undefined,
    waferNoList: isCP ? params?.waferNoList : undefined,
    sblotIdList: isCP ? undefined : params?.sblotIdList,
  };

  fetchData({ data: newParams });
};

const onClear = () => {
  // 恢复默认值
  handleValueChange({ key: 'testArea', value: 'CP' }); // handleValueChange不会自动触发
  searchFormRef.value?.setSearchVal(defaultSearch.value, { searchImmediate: false }); // 不要触发搜索，因为reset会触发一次搜索
};

watch(timeLinessRes, (timeLinessVal) => {
  if (timeLinessVal?.data) {
    const { inDbStream: inDbStreamData, top10Data: _top10Data } = timeLinessVal.data;
    if (!isEmpty(get(inDbStreamData, 'detail'))) {
      inDbStream.value = {
        total: `${get(inDbStreamData, 'baseIndexValue')}h`,
        steps: get(inDbStreamData, 'detail', []).map(({ indexKey, indexValue, percent }) => {
          return `${indexValue}${indexKey === 'FTP_FILE' ? '' : 'h'}, ${toPercent(percent)}`;
        }),
      };
    }

    top10Data.value = (_top10Data || []).map((item, index) => ({ ...item, index: index + 1 }));
  }
});

// testStage会影响展示的筛选项
const handleValueChange = ({ value, key }) => {
  if (key === 'testArea') {
    const testAreaType = testAreaMap.value[value];
    const isCP = testAreaType === 'CP';

    searchList.value.forEach((item) => {
      if (isCP && item.key === 'sblotIdList') {
        item.hidden = true;
      } else if (!isCP && item.key === 'waferNoList') {
        item.hidden = true;
      } else if (!factoryMode.value && item.key === 'subCustomerList') {
        item.hidden = true;
      } else {
        item.hidden = false;
      }
    });
  }
};

const init = () => {
  const start = dayjs().subtract(4, 'week').format('YYYY-MM-DD');
  const end = dayjs().format('YYYY-MM-DD');

  const testArea = 'CP'; // 默认 testArea
  handleValueChange({ key: 'testArea', value: testArea }); // 根据testArea 更新searchList

  defaultSearch.value = {
    testArea,
    storageTime: [start, end],
  };

  searchFormRef.value?.setSearchVal(defaultSearch.value); // 会触发一次handleSearch
};

// 如果factoryMode请求的慢则handleValueChange内没有正确处理factoryMode，需要这个watch兜底
watch(
  factoryMode,
  (factoryMode) => {
    if (isNil(factoryMode)) return;
    // 处理 factoryMode
    if (!factoryMode) {
      searchList.value.forEach((item) => {
        // 只处理subCustomerList，否则会影响之前处理的
        if (item.key === 'subCustomerList') {
          item.hidden = true;
        }
      });
    }
  },
  { deep: true, immediate: true },
);

onMounted(async () => {
  const res = await getConstantMap();
  testAreaMap.value = res?.data?.data?.testArea ?? {};
  init();
});
</script>

<style scoped lang="scss"></style>
