<template>
  <div class="text-[16px] font-bold mt-[8px] mb-[4px]">数据入库时间流</div>
  <div class="flex justify-between">
    <div class="card flex-[0_0_220px] mr-[8px] p-[8px]">
      <div class="mt-[4px] leading-[24px] text-[14px] text-[#565656]">
        整体入库耗时<br />Avg（End Time - Create Time）
      </div>
      <div
        v-if="inDbStream?.total"
        class="text-[48px] text-[#161616] leading-[116px] font-[600] font-[Segoe UI-Semibold, Segoe UI]"
      >
        {{ inDbStream.total }}
      </div>
    </div>
    <div class="card flex-[1_1] h-[240px] flex flex-col p-[8px]">
      <div class="font-bold leading-[24px] text-[14px]">数据入库时间流</div>
      <StepTimeline v-if="inDbStream?.steps" :data="inDbStream.steps" />
    </div>
  </div>
  <div class="flex text-[14px] h-full">
    <div class="flex-[1_1] mr-[8px]">
      <div class="card">
        <div class="card-header">入库耗时 By Factory</div>
        <div class="card-body">
          <div class="h-[314px]">
            <TreeMap v-if="!isEmpty(data?.data?.groupByFactoryData)" :data="data?.data?.groupByFactoryData" />
          </div>
        </div>
      </div>
      <div class="card mt-[8px]">
        <div class="card-header">入库耗时 By Factory&Tester</div>
        <div class="card-body">
          <div class="h-[314px]">
            <StackedBar
              v-if="!isEmpty(data?.data?.groupByFactoryAndTester)"
              :data="data?.data.groupByFactoryAndTester"
              :is-horizontal="true"
              :sort-by-group="'asc'"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="flex-[1_1]">
      <div class="card">
        <div class="card-header">数据入库耗时趋势图</div>
        <div class="card-body">
          <div class="h-[314px]">
            <Line v-if="!isEmpty(data?.data?.groupByWeek)" :data="data?.data?.groupByWeek" />
          </div>

          <div class="table-container">
            <div class="flex justify-between items-center h-[48px] leading-[48px]">
              <div class="font-bold">Top 10 耗时最高文件列表</div>
              <el-button v-if="top10Data && top10Data.length > 0" @click="downloadCSVFile()"> 导出数据 </el-button>
            </div>
            <div class="table-wrap">
              <div>
                <el-table fit size="small" :border="true" height="324px" :data="top10Data">
                  <el-table-column
                    v-for="(column, columnIndex) in TOP_10_COLUMNS"
                    :key="column.dataIndex"
                    :prop="column.dataIndex"
                    :label="column.title"
                    :sortable="!!columnIndex"
                    :show-overflow-tooltip="!!columnIndex"
                    :min-width="column.width"
                  />
                </el-table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { isEmpty } from 'lodash';
import { downloadCSV } from '@/utils/download';
import { TOP_10_COLUMNS } from './constants';

import StepTimeline from '../components/StepTimeline.vue';
import TreeMap from '../components/TreeMap.vue';
import StackedBar from '../components/StackedBar.vue';
import Line from '../components/Line.vue';

const props = defineProps<{ data: any; inDbStream: any; top10Data: any }>();

const downloadCSVFile = () => {
  downloadCSV(TOP_10_COLUMNS, props.top10Data);
};
</script>
<style scoped lang="scss">
.card {
  background: #fff;
  box-shadow: $gBoxShadowLighter;
  border-radius: 4px;
  font-family: apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Microsoft YaHei, Helvetica Neue, Helvetica,
    Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
}

.card-header {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #161616;
  padding: 0 8px;
  border-bottom: 1px solid #d7d8dc;
}
.card-body {
  padding: 8px;
}
.table-container {
  display: flex;
  flex-direction: column;
  height: 372px;
  padding: 0;
  margin-top: 8px;
  box-sizing: border-box;
  border-top: 1px solid #d7d8dc;
}
.table-wrap {
  flex: 1;
  position: relative;
  > div {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
}
</style>
