export enum PageTypeEnum {
  OVERVIEW = 'OVERVIEW',
  ACCURACY = 'ACCURACY',
  TIMELY = 'TIMELY',
  INTEGRITY = 'INTEGRITY',
}

export enum FilterListEnum {
  DEVICE = 'deviceList',
  FACTORY = 'factoryList',
  LOT_ID = 'lotIdList',
  TEST_PROGRAM = 'testProgramList',
  TEST_STAGE = 'testStageList',
  WAFER_ID = 'waferIdList',
  SB_LOT_ID = 'sblotIdList',
  LOT_TYPE = 'lotTypeList',
  DATA_LOADED = 'dataLoadedList',
  LOADING_FAILED_TYPE = 'loadingFailedTypeList',
  MATCH_FLAG = 'matchFlagList',
  ACCURACY_CHECK = 'accuracyCheckList',
  TEST_AREA = 'testAreaList',
  FILE_LOCATION = 'fileLocation',
  FILE_NAME = 'fileName',
  DATA_SOURCE = 'dataSource',
}

export enum FilterKeyEnum {
  DEVICE = 'DEVICE',
  FACTORY = 'OSAT',
  LOT_ID = 'LOT_ID',
  TEST_PROGRAM = 'TEST_PROGRAM',
  TEST_STAGE = 'TEST_STAGE',
  WAFER_ID = 'WAFER_ID',
  SB_LOT_ID = 'SUB_LOT_ID',
  LOT_TYPE = 'LOT_TYPE',
  DATA_LOADED = 'DATA_LOADED',
  DATA_SOURCE = 'DATA_SOURCE',
  LOADING_FAILED_TYPE = 'DATA_LOADING_FAILED_TYPE',
  MATCH_FLAG = 'MATCH_FLAG',
  ACCURACY_CHECK = 'ACCURACY_CHECK',
  TEST_AREA = 'TEST_AREA',
  FILE_LOCATION = 'FILE_LOCATION',
  FILE_NAME = 'FILE_Name',
}

export type DataQualityBaseDataItem = {
  baseIndexValue: number;
  calculateIndexType: string;
  detail: DataQualityBaseDataDetailItem[];
};

export type DataQualityBaseDataDetailItem = {
  indexKey: string;
  indexValue: number;
  percent?: number;
  colorByKey?: string;
};

export type repairInfo = {
  filedName: string;
  modifyRemark: string;
  modifyValue: string;
  originalValue: string;
  repairField: string;
};

export interface RepairInfosType {
  fileId?: number;
  repairInfo?: repairInfo[];
}

export interface StepOneListType {
  checked?: boolean;
  deviceId?: string;
  failCount?: string;
  fileId?: number;
  fileName?: string;
  flowId?: string;
  inputCount?: string;
  isRepaired?: boolean;
  lotId?: string;
  lotType?: string;
  passCount?: number;
  endTime?: string;
  repairTimes?: number;
  retestHardBin?: string;
  sblotId?: string;
  startTime?: string;
  testArea?: string;
  testProgram?: string;
  testStage?: string;
  stdfFileListStr?: string;
  fileNames?: string[];
  stdfFileListStrOne?: string;
  modifyValue?: string;
  modifyRemark?: string;
}

export interface StepTwoListType {
  modifyValue?: string;
  modifyRemark?: string;
  field?: string;
  fieldShowName?: string;
  fileId?: number;
  fileName?: string;
  originalFieldValue?: string;
}
