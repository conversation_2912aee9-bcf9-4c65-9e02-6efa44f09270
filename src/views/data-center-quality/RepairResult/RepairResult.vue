<template>
  <div class="repairResult">
    <Componment />
  </div>
</template>

<script setup lang="ts">
import { Componment } from './index';
</script>

<style lang="scss">
.repairResult {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  > .el-icon {
    margin: 0px auto 0;
    font-size: 40px;
  }

  > .success {
    color: rgb(17, 159, 93);
  }
  > .error {
    color: rgb(218, 30, 39);
  }

  > p {
    font-size: 16px;
    margin: 22px auto 0;
  }
  > .b {
    font-weight: bold;
  }
  > .l {
    margin: 20px auto 0;
    font-size: 14px;
    color: #a6a6a6;
  }

  > .btn {
    width: 100%;
    height: 48px;
    display: flex;
    border-top: 1px solid rgba(215, 216, 220, 1);
    margin: 70px auto auto;

    > .el-button:first-child {
      margin: auto 4px auto auto;
    }
    > .el-button:last-child {
      margin: auto auto auto 4px;
    }
  }
}
</style>
