import { ElButton } from 'element-plus';
import { dataQuality } from '@/store/dataQuality';
import { closeTag, isProductOnline } from '@/utils/common';
import SvgIcon from '@/components/SvgIcon.vue';
import { addTag } from '@guwave/utils';

const dataQualityStore = dataQuality();

const back = () => {
  if (isProductOnline()) {
    closeTag();
  } else {
    dataQualityStore.dataRecovery.resultDialog = false;
  }
};
const next = () => {
  addTag({
    baseUrl: '/bpms',
    tagName: '数据修复',
    url: `/#/dataRecoveryManagement`,
  });
};

const SuccessCom = () => (
  <>
    <SvgIcon class="el-icon" name={'success'} />

    <p class={'b'}>已触发修复，请在数据修复页面查看进度。</p>

    <div class="btn">
      <ElButton type={'primary'} onClick={next}>
        进入数据修复
      </ElButton>
      <ElButton onClick={back}>返回数据准确性</ElButton>
    </div>
  </>
);
const ErrorCom = () => (
  <>
    <SvgIcon class="el-icon" name={'alert_red'} />

    <p class={'b'}>触发解析失败</p>
    <p class={'l'}>{dataQualityStore.dataRecovery.errorMsg}</p>

    <div class="btn">
      <ElButton type={'primary'} onClick={back}>
        返回数据准确性
      </ElButton>
      <ElButton onClick={next}>进入数据修复</ElButton>
    </div>
  </>
);
export const Componment = () => (dataQualityStore.dataRecovery.status ? SuccessCom() : ErrorCom());
