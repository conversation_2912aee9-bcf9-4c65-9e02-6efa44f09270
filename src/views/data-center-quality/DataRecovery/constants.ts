import { $t } from '@/i18n';
import { getSubCustomer } from '@/service/common';
import { getBatchChangeOptions, getFileDeleteOptions, getFileRecoveryOptions } from '@/service/dataCenterQuality';

import { SearchFormTypeEnum, SearchFormItem } from '@guwave/components';

export const BATCH_SEARCH_LIST = (): SearchFormItem[] => {
  const list = [
    {
      label: 'Sub Customer',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'subCustomerList',
      api: getSubCustomer,
      dropdownResFn: (resData) => {
        const options = resData.map((item) => ({
          label: item.subCustomerCode,
          value: item.subCustomerCode,
        }));
        return options;
      },
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: $t('common.fields.device'),
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'deviceList',
      dropdownKey: 'deviceId',
      api: getBatchChangeOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Test Stage',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'testStageList',
      dropdownKey: 'testStage',
      api: getBatchChangeOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: $t('common.fields.lot'),
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'lotIdList',
      dropdownKey: 'lotId',
      api: getBatchChangeOptions,
      nativeAttr: {
        multiple: true,
      },
    },
  ];

  // 重写api
  const newList = list.map((item) => {
    return {
      ...item,
      api: (params) => {
        if (item.key === 'subCustomerList') {
          return item.api(); //subCustomer不需要其他参数
        }
        const newParams = {
          ...params,
          filterField: params.field,
          field: undefined,
        };
        return item.api(newParams);
      },
    };
  });

  return newList;
};

export const RECOVERY_SEARCH_LIST = (): SearchFormItem[] => {
  const list = [
    {
      label: 'Sub Customer',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'subCustomerList',
      api: getSubCustomer,
      dropdownResFn: (resData) => {
        const options = resData.map((item) => ({
          label: item.subCustomerCode,
          value: item.subCustomerCode,
        }));
        return options;
      },
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: $t('common.fields.device'),
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'deviceIdList',
      dropdownKey: 'deviceId',
      api: getFileRecoveryOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Test Stage',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'testStageList',
      dropdownKey: 'testStage',
      api: getFileRecoveryOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: $t('common.fields.lot'),
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'lotIdList',
      dropdownKey: 'lotId',
      api: getFileRecoveryOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'File Name',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'fileNameList',
      dropdownKey: 'fileName',
      api: getFileRecoveryOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Repair Status',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'repairStatusList',
      dropdownKey: 'repairStatus',
      api: getFileRecoveryOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Repair Category',
      type: SearchFormTypeEnum.SELECT_V2,
      multiple: true,
      key: 'repairCategoryList',
      dropdownKey: 'repairCategory',
      api: getFileRecoveryOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Last Repair Type',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'lastRepairTypeList',
      dropdownKey: 'lastRepairType',
      api: getFileRecoveryOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Repair Times',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'repairTimesList',
      dropdownKey: 'repairTimes',
      api: getFileRecoveryOptions,
      nativeAttr: {
        multiple: true,
      },
    },
  ];

  // 重写api
  const newList = list.map((item) => {
    return {
      ...item,
      api: (params) => {
        if (item.key === 'subCustomerList') {
          return item.api(); //subCustomer不需要其他参数
        }
        const newParams = {
          ...params,
          filterField: params.field,
          field: undefined,
        };
        return item.api(newParams);
      },
    };
  });

  return newList;
};

export const DELETE_SEARCH_LIST = (): SearchFormItem[] => {
  const list = [
    {
      label: 'Sub Customer',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'subCustomerList',
      api: getSubCustomer,
      dropdownResFn: (resData) => {
        const options = resData.map((item) => ({
          label: item.subCustomerCode,
          value: item.subCustomerCode,
        }));
        return options;
      },
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'File Name',
      type: SearchFormTypeEnum.INPUT,
      key: 'fileName',
      api: getFileDeleteOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Delete by',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'deleteByList',
      dropdownKey: 'DELETE_BY',
      api: getFileDeleteOptions,
      nativeAttr: {
        multiple: true,
      },
    },
  ];

  // 重写api

  const newList = list.map((item) => {
    return {
      ...item,
      api: (params) => {
        if (item.key === 'subCustomerList') {
          return item.api(); //subCustomer不需要其他参数
        }
        const newParams = {
          ...params,
          filterField: params.field,
          field: undefined,
        };
        return item.api(newParams);
      },
    };
  });

  return newList;
};
