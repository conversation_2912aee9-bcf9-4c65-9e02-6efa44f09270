import { getDeleteList } from '@/service/dataCenterQuality';
import { PaginationType, SearchParam } from '@guwave/components';

export const useDelete = () => {
  const searchParams = ref<SearchParam>({});
  const pageInfo = reactive<PaginationType>({
    pageIndex: 1,
    pageSize: 20,
    total: 0,
    // 页码改变回调
    currentChange: (current: number) => {
      pageInfo.pageIndex = current;
      getTableList();
    },
    // 每页条数改变回调
    sizeChange: (size: number) => {
      pageInfo.pageSize = size;
      getTableList();
    },
  });

  const tableData = ref([]);
  const loading = ref<boolean>(false);
  const getTableList = async () => {
    loading.value = true;
    try {
      const params = {
        ...searchParams.value,
        pageIndex: pageInfo.pageIndex,
        pageSize: pageInfo.pageSize,
      };
      const res = await getDeleteList(params);
      const resData = res.data;

      if (resData.code === 200 || resData.code === '00000001') {
        const { data, pageIndex, pageSize, total } = resData.data;

        tableData.value = data;
        pageInfo.pageIndex = pageIndex;
        pageInfo.pageSize = pageSize;
        pageInfo.total = total;
      }
    } catch (error) {
      console.error(error);
    }
    loading.value = false;
  };
  return {
    pageInfo,
    loading,
    tableData,
    getTableList,
    searchParams,
  };
};
