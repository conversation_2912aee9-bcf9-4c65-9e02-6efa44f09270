<template>
  <el-dialog v-model="visible" title="文件记录" width="100%">
    <div class="content">
      <div class="operation">
        <el-button link @click="onShowAllFileRecord"> 查看所有文件记录 > </el-button>
      </div>
      <div class="recovery-table-container">
        <FileTable :tableData="tableData" :loading="loading" />
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { getFileDialogList } from '@/service/dataCenterQuality';
import FileTable from './FileTable.vue';
import qs from 'qs';
import { addTag } from '@guwave/utils';

const props = defineProps<{
  batchId: string;
}>();

const visible = defineModel<boolean>('visible');

const loading = ref<boolean>(false);
const tableData = ref<Record<string, any>[]>([]);

const getTableData = async () => {
  loading.value = true;
  try {
    const res = await getFileDialogList(props.batchId);
    if (res.data.data) {
      tableData.value = res.data.data;
    }
  } catch (error) {
    console.error(error);
  }
  loading.value = false;
};
// TODO:有bug 产品说先不管
const onShowAllFileRecord = () => {
  addTag({
    baseUrl: '/bpms',
    tagName: '数据修复',
    url: `/#/dataRecoveryManagement?${qs.stringify({ tabType: 'file' })} `,
  });
};

onMounted(() => {
  getTableData();
});
</script>

<style scoped lang="scss">
.content {
  height: 650px;
  display: flex;
  flex-direction: column;
  .operation {
    height: 32px;
    line-height: 32px;
    text-align: right;
    margin-bottom: 7px;
  }
  .recovery-table-container {
    flex: 1;
    overflow: hidden;
  }
}
</style>
