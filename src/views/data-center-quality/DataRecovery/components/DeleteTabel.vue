<template>
  <div class="w-full h-full">
    <CommonTable :data="tableData" v-loading="loading" v-model:pagination="pageInfo" border scrollbar-always-on>
      <el-table-column
        v-for="item in DELETE_TABLE_COLUMNS"
        :key="item.key"
        :prop="item.key"
        :label="item.label"
        :width="item.width"
        show-overflow-tooltip
      />
    </CommonTable>
  </div>
</template>

<script setup lang="ts">
import { CommonTable, PaginationType } from '@guwave/components';
import { DELETE_TABLE_COLUMNS } from './constants';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const props = defineProps<{
  type?: 'dialogTable';
  tableData: any[];
  loading: boolean;
}>();

const pageInfo = defineModel<PaginationType>('pageInfo');
</script>
<style scoped lang="scss"></style>
