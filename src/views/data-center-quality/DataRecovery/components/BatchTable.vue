<template>
  <div class="batch-table-container">
    <CommonTable
      :row-key="(row) => row.id"
      :data="tableData"
      v-loading="loading"
      v-model:pagination="pageInfo"
      border
      scrollbar-always-on
    >
      <template #empty>
        <SvgIcon class="el-icon" name="empty_nodata" :style="{ fontSize: 60 }" />
        <div class="no-data-p-color">暂无可用数据</div>
      </template>
      <el-table-column
        v-for="item in BATCH_TABLE_COLUMNS"
        :key="item.code"
        :prop="item.code"
        :label="item.name"
        :show-overflow-tooltip="true"
        :minWidth="
          item?.width
            ? min([getColWidth(item.code, tableData, item.name, 14), item.width])
            : getColWidth(item.code, tableData, item.name, 14) ?? 160
        "
      />

      <el-table-column fixed="right" label="操作" minWidth="130" key="Operations">
        <template #default="scope">
          <el-button text @click="handleClickShowFileResult(scope.row)"> 查看文件记录 </el-button>
        </template>
      </el-table-column>
    </CommonTable>
    <FileDialog v-if="fileRecordVisible" v-model:visible="fileRecordVisible" :batchId="clickedBatchId" />
  </div>
</template>

<script setup lang="ts">
import { CommonTable, PaginationType } from '@guwave/components';
import FileDialog from './FileDialog.vue';
import { min } from 'lodash';
import { BATCH_TABLE_COLUMNS } from './constants';
import { getColWidth } from '@/utils/common';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const props = defineProps<{
  tableData: any[];
  loading: boolean;
}>();

const pageInfo = defineModel<PaginationType>('pageInfo');

const clickedBatchId = ref<string>('');
const fileRecordVisible = ref<boolean>(false);
const handleClickShowFileResult = (row) => {
  clickedBatchId.value = row?.id ?? '';
  fileRecordVisible.value = true;
};
</script>

<style scoped lang="scss">
.batch-table-container {
  height: 100%;
  width: 100%;
}
</style>
