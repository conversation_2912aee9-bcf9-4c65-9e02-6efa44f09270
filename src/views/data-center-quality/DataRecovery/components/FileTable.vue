<template>
  <div class="w-full h-full">
    <CommonTable
      :row-key="(row) => row.id"
      :data="tableData"
      v-loading="loading"
      v-model:pagination="pageInfo"
      scrollbar-always-on
      border
    >
      <el-table-column
        v-for="item in FILE_TABLE_COLUMNS"
        :key="item.code"
        :prop="item.code"
        :label="item.name"
        :show-overflow-tooltip="true"
        :minWidth="item?.minWidth ?? getColWidth(item.code, tableData, item.name, 14) ?? 160"
      >
        <template #default="scope">
          <el-button v-if="item.code === 'Operations'" link @click="handleClickRepairLog(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </CommonTable>

    <RepairDialog v-if="visible" v-model:visible="visible" :fileId="repairLogFileId" />
  </div>
</template>

<script setup lang="ts">
import { FILE_TABLE_COLUMNS } from './constants';
import RepairDialog from './RepairDialog.vue';
import { CommonTable, PaginationType } from '@guwave/components';
import { getColWidth } from '@/utils/common';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const props = defineProps<{
  tableData: Record<string, any>[];
  loading: boolean;
}>();
const pageInfo = defineModel<PaginationType>('pageInfo');

const repairLogFileId = ref<number>(undefined);
const visible = ref<boolean>(false);

const handleClickRepairLog = (row) => {
  visible.value = true;
  repairLogFileId.value = row.fileId;
};
</script>

<style scoped lang="scss"></style>
