import { $t } from '@/i18n';
import { Column } from 'element-plus';

export const BATCH_TABLE_COLUMNS = [
  {
    code: 'deviceId',
    name: $t('common.fields.device'),
  },
  {
    code: 'testStage',
    name: 'Test Stage',
  },
  {
    code: 'lotId',
    name: $t('common.fields.lot'),
  },
  {
    code: 'testProgram',
    name: 'Test Program',
  },
  {
    code: 'sblotId',
    name: $t('common.fields.subLot'),
  },
  {
    code: 'waferId',
    name: $t('common.fields.wafer'),
  },
  {
    code: 'repairNo',
    name: 'Repair No.',
  },
  {
    code: 'fileCountBefore',
    name: 'File Count-before',
  },
  {
    code: 'fileListBefore',
    name: 'File List-before',
    width: 570,
  },
  {
    code: 'fileCountAfter',
    name: 'File Count-after',
  },
  {
    code: 'fileListAfter',
    name: 'File List-after',
    width: 570,
  },
  {
    code: 'repairFiles',
    name: 'Repair Files',
    width: 570,
  },
  {
    code: 'updateTime',
    name: 'Repair Date',
  },
  {
    code: 'createUser',
    name: 'Repair By',
  },
];

export const FILE_TABLE_COLUMNS = [
  {
    code: 'fileName',
    name: 'File Name',
  },
  {
    code: 'repairCategory',
    name: 'Repair Category',
  },
  {
    code: 'lastRepairType',
    name: 'Last Repair Type',
  },
  {
    code: 'repairTimes',
    name: 'Repair Times',
  },
  {
    code: 'Operations',
    name: 'Repair Log',
    minWidth: 100,
  },
  {
    code: 'repairStatus',
    name: 'Repair Status',
  },
  {
    code: 'modifyDeviceId',
    name: $t('common.fields.device'),
  },
  {
    code: 'modifyTestStage',
    name: 'Test Stage',
  },
  {
    code: 'modifyLotId',
    name: $t('common.fields.lot'),
  },
  {
    code: 'modifySblotId',
    name: $t('common.fields.subLot'),
  },
  {
    code: 'waferId',
    name: $t('common.fields.wafer'),
  },
  {
    code: 'modifyTestProgram',
    name: 'Test Program',
  },
  {
    code: 'modifyFlowId',
    name: 'Flow ID',
  },
  {
    code: 'modifyRetestHbinNumber',
    name: 'Retest HBin Number',
  },
  {
    code: 'updateTime',
    name: 'Update Time',
  },
  {
    code: 'startTime',
    name: 'Start Time',
  },
  {
    code: 'endTime',
    name: 'End Time',
  },
];

export const DELETE_TABLE_COLUMNS = [
  {
    key: 'fileName',
    label: 'File Name',
  },
  {
    key: 'deleteTime',
    label: 'Delete Time',
    width: 200,
  },
  {
    key: 'deleteBy',
    label: 'Delete by',
    width: 250,
  },
  {
    key: 'remark',
    label: '备注',
    width: 400,
  },
];

export const REPAIRE_DIALOG_COLUMNS: Column<any>[] = [
  {
    key: 'fileName',
    dataKey: 'fileName',
    title: 'STDF File Name',
    width: 200,
  },
  {
    key: 'updateTime',
    dataKey: 'updateTime',
    title: '修改时间',
    width: 200,
  },
  {
    key: 'updateUser',
    dataKey: 'updateUser',
    title: '修改用户',
    width: 200,
  },
  {
    key: 'repairTimes',
    dataKey: 'repairTimes',
    title: 'Repair No.',
    width: 200,
  },
  {
    key: 'repairCategory',
    dataKey: 'repairCategory',
    title: 'Repair Category',
    width: 200,
  },
  {
    key: 'lastRepairType',
    dataKey: 'lastRepairType',
    title: 'Last Repair Type',
    width: 200,
  },
  {
    key: 'originalInfo',
    dataKey: 'originalInfo',
    title: '修改前数据',
    width: 200,
  },
  {
    key: 'modifyInfo',
    dataKey: 'modifyInfo',
    title: '修改后数据',
    width: 200,
  },
  {
    key: 'modifyRemark',
    dataKey: 'modifyRemark',
    title: '修改原因',
    width: 200,
  },
  {
    key: 'repairStatus',
    dataKey: 'repairStatus',
    title: 'Repair Status',
    width: 200,
  },
];
