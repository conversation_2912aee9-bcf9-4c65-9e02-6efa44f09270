<template>
  <el-dialog v-model="visible" title="Repair Log" width="900px">
    <PublicTable :columns="REPAIRE_DIALOG_COLUMNS" :data="data" :width="860" :height="400" />
  </el-dialog>
</template>

<script setup lang="ts">
import { DialogTableType } from '../typing';
import { REPAIRE_DIALOG_COLUMNS } from './constants';
import { PublicTable } from '@/components/PublicTable/index';
import { getDialogTableData } from '@/service/dataCenterQuality';

const props = defineProps<{ fileId }>();

const visible = defineModel<boolean>('visible');

const data = ref<DialogTableType[]>([]);

const getDialog = async () => {
  if (props.fileId) {
    const res = await getDialogTableData(props.fileId);
    data.value = res.data.data;
  }
};

onMounted(() => {
  getDialog();
});
</script>
