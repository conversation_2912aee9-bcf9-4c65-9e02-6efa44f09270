<template>
  <PageTemplate>
    <template #header>
      <div>数据修复</div>
    </template>
    <template #tabs>
      <el-tabs class="demo-tabs" v-model="tabType">
        <el-tab-pane label="批次变更记录" :name="TabType.BatchChange"></el-tab-pane>
        <el-tab-pane label="文件修复记录" :name="TabType.RecoveryFile"></el-tab-pane>
        <el-tab-pane label="文件删除记录" :name="TabType.DeleteFile"></el-tab-pane>
      </el-tabs>
    </template>
    <template #filterNormal>
      <SearchForm
        ref="batchSearchRef"
        v-show="tabType === TabType.BatchChange"
        :searchList="batchSearchList"
        @search="onSearch"
      />
      <SearchForm
        ref="recoverySearchRef"
        v-show="tabType === TabType.RecoveryFile"
        :searchList="recoverySearchList"
        @search="onSearch"
      />
      <SearchForm
        ref="deleteSearchRef"
        v-show="tabType === TabType.DeleteFile"
        :searchList="deleteSearchList"
        @search="onSearch"
      />
    </template>
    <template #table>
      <BatchTable
        v-show="tabType === TabType.BatchChange"
        :tableData="batchTableData"
        :loading="batchLoading"
        v-model:pageInfo="batchPageInfo"
      />
      <FileTable
        v-show="tabType === TabType.RecoveryFile"
        :tableData="recoveryTableData"
        :loading="recoveryLoading"
        v-model:pageInfo="recoveryPageInfo"
      />
      <DeleteTable
        v-show="tabType === TabType.DeleteFile"
        :tableData="deleteTableData"
        :loading="deleteLoading"
        v-model:pageInfo="deletePageInfo"
      />
    </template>
  </PageTemplate>
</template>

<script setup lang="ts">
import BatchTable from './components/BatchTable.vue';
import FileTable from './components/FileTable.vue';
import DeleteTable from './components/DeleteTabel.vue';
import { isNil } from 'lodash';
import { getQueryString } from '@/utils/common';
import { TabType } from './typing';
import { PageTemplate, SearchFormItem, SearchForm } from '@guwave/components';
import { BATCH_SEARCH_LIST, RECOVERY_SEARCH_LIST, DELETE_SEARCH_LIST } from './constants';
import { useBatch } from './hooks/useBatch';
import { useRecovery } from './hooks/useRecovery';
import { useDelete } from './hooks/useDelete';
import useDashUrlInfoStore from '@/store/system/dashboardUrl';

const dashBoardInfo = useDashUrlInfoStore();
const { factoryMode } = storeToRefs(dashBoardInfo);

const tabType = ref<TabType>(TabType.BatchChange);

const batchSearchRef = ref(null);
const recoverySearchRef = ref(null);
const deleteSearchRef = ref(null);

const batchSearchList = ref<SearchFormItem[]>(BATCH_SEARCH_LIST());
const recoverySearchList = ref<SearchFormItem[]>(RECOVERY_SEARCH_LIST());
const deleteSearchList = ref<SearchFormItem[]>(DELETE_SEARCH_LIST());
watch(
  factoryMode,
  (_factoryMode) => {
    if (isNil(_factoryMode)) return;
    if (!_factoryMode) {
      batchSearchList.value = BATCH_SEARCH_LIST().filter((item) => item.key !== 'subCustomerList');
      recoverySearchList.value = RECOVERY_SEARCH_LIST().filter((item) => item.key !== 'subCustomerList');
      deleteSearchList.value = DELETE_SEARCH_LIST().filter((item) => item.key !== 'subCustomerList');
    }
  },
  { immediate: true },
);

const {
  pageInfo: batchPageInfo,
  tableData: batchTableData,
  loading: batchLoading,
  getTableList: batchGetTableList,
  searchParams: batchSearchParams,
} = useBatch();
const {
  pageInfo: recoveryPageInfo,
  tableData: recoveryTableData,
  loading: recoveryLoading,
  getTableList: recoveryGetTableList,
  searchParams: recoverySearchParams,
} = useRecovery();
const {
  pageInfo: deletePageInfo,
  tableData: deleteTableData,
  loading: deleteLoading,
  getTableList: deleteGetTableList,
  searchParams: deleteSearchParams,
} = useDelete();

onMounted(() => {
  const decodeHref = decodeURI(location.href);
  const urlTabType = getQueryString(decodeHref, 'tabType') as TabType;
  if (!isNil(urlTabType)) {
    tabType.value = urlTabType;
  }
  batchGetTableList();
  recoveryGetTableList();
  deleteGetTableList();
});

const onSearch = (params) => {
  if (tabType.value === TabType.BatchChange) {
    batchSearchParams.value = params;
    batchPageInfo.pageIndex = 1;
    batchGetTableList();
  } else if (tabType.value === TabType.RecoveryFile) {
    recoverySearchParams.value = params;
    recoveryPageInfo.pageIndex = 1;
    recoveryGetTableList();
  } else if (tabType.value === TabType.DeleteFile) {
    deleteSearchParams.value = params;
    deletePageInfo.pageIndex = 1;
    deleteGetTableList();
  }
};
</script>

<style scoped lang="scss">
.container {
  height: 100%;

  padding: 16px 18px 8px 16px;
  .header {
    height: 32px;
    line-height: 32px;
    color: #161616;
    font-size: 18px;
    font-weight: 700;
    // margin-bottom: 8px;
  }
}
</style>
