export enum TabType {
  BatchChange = 'batchChange',
  RecoveryFile = 'recoveryFile',
  DeleteFile = 'deleteFile',
}

export interface DialogTableType {
  createTim?: string;
  createUser?: string;
  customer?: string;
  deleteFlag?: boolean;
  endTime?: string;
  fileId?: number;
  fileName?: string;
  id?: number;
  lastRepairType?: string;
  modifyInfo?: string;
  modifyRemark?: string;
  originalInfo?: string;
  remark?: string;
  repairBatchId?: number;
  repairCategory?: string;
  repairErrorInfo?: number;
  repairStatus?: string;
  repairTimes?: number;
  sblotId?: string;
  startTime?: string;
  subCustomer?: string;
  testArea?: string;
  updateTime?: string;
  updateUser?: string;
  waferId?: string;
}

export interface StepOneListType {
  checked?: boolean;
  deviceId?: string;
  failCount?: string;
  fileId?: number;
  fileName?: string;
  flowId?: string;
  inputCount?: string;
  isRepaired?: boolean;
  lotId?: string;
  lotType?: string;
  passCount?: number;
  endTime?: string;
  repairTimes?: number;
  retestHardBin?: string;
  sblotId?: string;
  startTime?: string;
  testArea?: string;
  testProgram?: string;
  testStage?: string;
  stdfFileListStr?: string;
  fileNames?: string[];
  stdfFileListStrOne?: string;
  modifyValue?: string;
  modifyRemark?: string;
}
export interface StepTwoListType {
  modifyValue?: string;
  modifyRemark?: string;
  field?: string;
  fieldShowName?: string;
  fileId?: number;
  fileName?: string;
  originalFieldValue?: string;
}
