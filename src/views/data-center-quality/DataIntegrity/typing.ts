export enum DialogTypeEnum {
  Delete = 'delete',
  Rerun = 'rerun',
  Remark = 'remark',
  Repair = 'repair',
  ChangeFileLabel = 'changeFileLabel',
}

export enum OptionFromEnum {
  Single = 'single',
  Batch = 'batch',
}

export interface ParamsObj {
  [key: string]: string | undefined;
}

export interface TableSortInfoType {
  column: any;
  order: 'ascending' | 'descending' | null;
  prop: string;
}
