<template>
  <PageTemplate>
    <template #header>
      <div>数据完整性校验</div>
    </template>
    <template #filterNormal>
      <SearchForm ref="searchFormRef" :searchList="searchList" @search="onSearch" :clearFn="onClear" />
    </template>
    <template #KPICard>
      <Card :data="baseRes" :loading="baseLoading" />
    </template>
    <template #table>
      <Table
        ref="tableRef"
        :loading="listLoading"
        :searchParams="searchParams"
        v-model:tableData="tableData"
        v-model:pageInfo="pageInfo"
        @sort="handleSort"
        @refresh="onRefreshTable"
      />
    </template>
  </PageTemplate>
</template>

<script setup lang="ts">
import { SEARCH_LIST } from './constant';

import { PageTemplate, SearchForm, SearchFormParam, PaginationType, SearchFormItem } from '@guwave/components';

import { getDataQualityIntegrityBase, getDataQualityIntegrityList } from '@/service/dataCenterQuality';

import Card from './components/Card.vue';
import Table from './components/Table.vue';

import { isEmpty, isNil } from 'lodash';
import { getQueryString } from '@guwave/utils';
import { dayjs } from 'element-plus';
import useDashUrlInfoStore from '@/store/system/dashboardUrl';

const dashBoardInfo = useDashUrlInfoStore();
const { factoryMode } = storeToRefs(dashBoardInfo);

const tableRef = ref(null);
const tableData = ref<Record<string, any>[]>();

const pageInfo = reactive<PaginationType>({
  pageIndex: 1,
  pageSize: 20,
  total: 0,
  // 页码改变回调
  currentChange: (current: number) => {
    pageInfo.pageIndex = current;
    getTableData();
  },
  // 每页条数改变回调
  sizeChange: (size: number) => {
    pageInfo.pageSize = size;
    getTableData();
  },
});

const { data: baseRes, isLoading: baseLoading, execute: fetchBase } = getDataQualityIntegrityBase();
const { data: listRes, isLoading: listLoading, execute: fetchList } = getDataQualityIntegrityList();

const searchFormRef = ref(null);
const searchList = ref<SearchFormItem[]>(SEARCH_LIST());
const defaultSearch = ref();

const searchParams = ref<SearchFormParam>({});
const sortParams = reactive<{ orderByField?: string; orderByType?: 'ASC' | 'DESC' }>({});

// 点击表格排序的响应
const handleSort = ({ prop, order }) => {
  if (isNil(order)) {
    sortParams.orderByField = undefined;
    sortParams.orderByType = undefined;
  } else {
    sortParams.orderByField = prop;
    sortParams.orderByType = order === 'ascending' ? 'ASC' : 'DESC';
  }
  getTableData();
};

const getCardData = () => {
  const params = {
    ...searchParams.value,
  };
  fetchBase({ data: params });
};
const getTableData = () => {
  const params = {
    ...searchParams.value,
    ...sortParams,
    pageIndex: pageInfo.pageIndex,
    pageSize: pageInfo.pageSize,
  };
  fetchList({ data: params });
};

watch(listRes, (listVal) => {
  if (listVal?.data) {
    tableData.value = listVal.data.data;
    pageInfo.pageIndex = listVal.data.pageIndex;
    pageInfo.pageSize = listVal.data.pageSize;
    pageInfo.total = listVal.data.total;
  } else {
    tableData.value = [];
    pageInfo.pageIndex = 1;
    pageInfo.pageSize = 20;
    pageInfo.total = 0;
  }
});

const onSearch = (params: SearchFormParam) => {
  // 勾选清空
  if (tableRef.value) {
    tableRef.value.clearTableSelection();
  }

  pageInfo.pageIndex = 1;
  const url = decodeURIComponent(location.href);
  const fileIdList = getQueryString(url, 'fileIdList');

  const newParams = {
    ...params,
    loadingTime: undefined,
    startTime: params?.loadingTime ? dayjs(params.loadingTime?.[0]).format('YYYY-MM-DD 00:00:00') : undefined,
    endTime: params?.loadingTime ? dayjs(params.loadingTime?.[1]).format('YYYY-MM-DD 23:59:59') : undefined,
    fileIdList: fileIdList?.split(',')?.map(Number) ?? undefined, // 请求需要一直带上fileIdList
  };
  searchParams.value = newParams;

  getCardData();
  getTableData();
};
const onClear = () => {
  // 恢复默认值
  searchFormRef.value?.setSearchVal(defaultSearch.value, { searchImmediate: false }); // 不要触发搜索，因为reset会触发一次搜索
};

const onRefreshTable = () => {
  getTableData();
};

watch(
  factoryMode,
  (factoryMode) => {
    if (isNil(factoryMode)) return;
    // 处理 factoryMode
    if (!factoryMode) {
      searchList.value.forEach((item) => {
        // 只处理subCustomerList，否则会影响之前处理的
        if (item.key === 'subCustomerList') {
          item.hidden = true;
        }
      });
    }
  },
  { deep: true, immediate: true },
);

const init = () => {
  // 获取跳转参数
  const url = decodeURIComponent(location.href);

  // 默认时间
  const fileIdList = getQueryString(url, 'fileIdList');
  const startTime = getQueryString(url, 'startTime');
  const endTime = getQueryString(url, 'endTime');

  let dateTypeValue = 4;
  let dateUnitValue = 'week';
  if (!isEmpty(fileIdList)) {
    dateTypeValue = 10;
    dateUnitValue = 'year';
  }
  let start = dayjs().subtract(dateTypeValue, dateUnitValue).format('YYYY-MM-DD');
  let end = dayjs().format('YYYY-MM-DD');
  if (startTime && endTime) {
    // 如果url有startTime和endTime，则以url为准
    start = startTime;
    end = endTime;
  }

  defaultSearch.value = {
    loadingTime: [start, end],
  };

  searchFormRef.value?.setSearchVal(defaultSearch.value); // 会触发一次handleSearch
};

// 完整性的跳转参数只有 fileIdList
onMounted(async () => {
  init();
});
</script>

<style scoped lang="scss"></style>
