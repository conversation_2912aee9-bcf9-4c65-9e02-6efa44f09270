export const MUST_NOT_EMPTY_FIELD = [
  'deviceId',
  'testArea',
  'lotType',
  'testStage',
  'lotId',
  'sblotId',
  'waferNo',
  'startT',
  'finishT',
  'offlineRetest',
  'interrupt',
  'dupRetest',
  'batchNum',
];

export const FLOW_ID_FIELD = ['offlineRetest', 'interrupt', 'dupRetest', 'batchNum'];

export const MUST_NOT_EMPTY_FIELD_SHOW_OBJ = {
  deviceId: 'Device',
  lotId: 'Lot',
  waferNo: 'Wafer Number',
  testArea: 'Test Area',
  lotType: 'Lot Type',
  testStage: 'Test Stage',
  sblotId: 'Sub Lot',
  startT: 'Start Time',
  finishT: 'End Time',
  flowId: 'Flow Id',
};

export const FIELD_NEW_VALUE_KEY = {
  deviceId: 'DEVICE_ID',
  lotId: 'LOT_ID',
  waferNo: 'WAFER_NO',
  testArea: 'TEST_AREA',
  lotType: 'LOT_TYPE',
  testStage: 'TEST_STAGE',
  sblotId: 'SBLOT_ID',
  startT: 'START_T',
  finishT: 'FINISH_T',
  flowId: 'FLOW_ID',
};

export const TEAT_AREA_OPTIONS = [
  { key: 'CP', label: 'CP' },
  { key: 'FT', label: 'FT' },
  { key: 'WAT', label: 'WAT' },
  { key: 'SLT', label: 'SLT' },
];

export const LOT_TYPE_OPTIONS = [
  { key: 'ENGINEERING', label: 'ENGINEERING' },
  { key: 'PRODUCTION', label: 'PRODUCTION' },
];

export const TABLE_COLUMNS = [
  { key: 'fileName', label: 'File Name', minWidth: 200, sortable: 'custom', fixed: true },
  { key: 'fileLocation', label: 'File Location', minWidth: 130 },
  { key: 'dataLoaded', label: 'File Loaded', minWidth: 130, sortable: 'custom' },
  { key: 'dataLoadingFailedStep', label: 'File Loading Failed Step', minWidth: 220, sortable: true },
  { key: 'loadingFailedType', label: 'File Loading Failed Type', minWidth: 220 },
  { key: 'updateTime', label: 'Update Time', minWidth: 180, sortable: 'custom' },
  { key: 'factory', label: 'Factory', minWidth: 180 },
  { key: 'deviceName', label: 'Device', minWidth: 130 },
  { key: 'testStage', label: 'Test Stage', minWidth: 120 },
  { key: 'lotId', label: 'Lot', minWidth: 120 },
  { key: 'sblotId', label: 'Sub Lot', minWidth: 220 },
  { key: 'testProgram', label: 'Test Program', minWidth: 220 },
  { key: 'waferId', label: 'Wafer', minWidth: 120 },
  { key: 'lotType', label: 'Lot Type', minWidth: 220 },
  { key: 'startTime', label: 'Start Time', minWidth: 180 },
  { key: 'endTime', label: 'End Time', minWidth: 180 },
  { key: 'fileLabel', label: 'File Label', minWidth: 180 },
  { key: 'remark', label: '备注', minWidth: 180 },
];
