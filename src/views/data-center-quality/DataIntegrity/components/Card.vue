<template>
  <div class="text-[16px] leading-[32px] font-bold">量产数据入库详情</div>
  <div class="flex" v-loading="loading">
    <div class="card left-card">
      <div class="text-[14px] text-[#565656]">文件成功入库率</div>
      <div class="leading-[64px] text-[48px] text-[#161616] mt-[8px] font-[600]">{{ inDbStatics?.successRatio }}</div>
      <div>
        <div class="flex">
          <div class="left">
            <span class="sub-title">总文件数</span>
            <div class="number">{{ inDbStatics?.totalCnt }}</div>
          </div>
          <div>
            <span class="sub-title">已成功入库文件数</span>
            <div class="number">{{ inDbStatics?.successCnt }}</div>
          </div>
        </div>
        <div class="flex" style="margin-top: 16px">
          <div class="left">
            <span class="sub-title">正在入库文件数</span>
            <div class="number">{{ inDbStatics?.processCnt }}</div>
          </div>
          <div>
            <span class="sub-title">入库失败文件数</span>
            <div class="number">{{ inDbStatics?.failNumCnt }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="card flex-[1_1]">
      <div class="leading-[24px] text-[14px] font-bold">量产数据入库过程</div>
      <StepRing class="h-[200px]" v-if="steps" :data="steps" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { toPercent } from '@/utils/number';
import StepRing from '../../components/StepRing.vue';

const props = defineProps<{
  data: any;
  loading: boolean;
}>();

const inDbStatics = ref<{
  successRatio: string;
  failNumCnt: number;
  processCnt: number;
  successCnt: number;
  totalCnt: number;
}>();
const steps = ref<{ key: string; value: string; progress: number }[]>([]);
watch(
  () => props.data,
  (baseVal) => {
    if (baseVal?.data) {
      const { baseIndexValue, failNumCnt, processCnt, successCnt, totalCnt, detail } = baseVal.data;

      inDbStatics.value = {
        successRatio: toPercent(baseIndexValue),
        failNumCnt,
        processCnt,
        successCnt,
        totalCnt,
      };
      steps.value = (detail || []).map(({ indexKey, indexValue, percent }) => ({
        key: indexKey,
        value: `${indexValue}, ${toPercent(percent)}`,
        progress: percent,
      }));
    }
  },
);
</script>

<style scoped lang="scss">
.left-card {
  width: 312px;
  height: 240px;
  padding-left: 16px;
  padding-top: 16px;
  margin-right: 8px;
  .sub-title {
    color: #565656;
    font-size: 12px;
  }
  .number {
    color: #161616;
    font-weight: 700;
    font-size: 20px;
  }
  .left {
    width: 142px;
  }
}
.card {
  padding: 8px;
  background: #fff;
  box-shadow: $gBoxShadowLighter;
  border-radius: 4px;
  font-family: apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Microsoft YaHei, Helvetica Neue, Helvetica,
    Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
}
</style>
