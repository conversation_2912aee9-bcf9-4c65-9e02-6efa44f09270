<template>
  <div class="data-integrity-delete-dialog">
    <el-dialog v-model="visible" title="修复数据" width="1040" :before-close="handleClose">
      <div class="content" v-loading="loading">
        <div class="title">
          修复字段
          <el-button size="small" type="text" @click="goToRecord"> 查看所有文件记录> </el-button>
        </div>
        <el-table :data="tableData" class="show-table" border fit :span-method="objectSpanMethod">
          <el-table-column show-overflow-tooltip prop="fileName" label="文件名称" min-width="500" />
          <el-table-column prop="field" label="字段名称" min-width="180">
            <template #default="scope">
              {{ MUST_NOT_EMPTY_FIELD_SHOW_OBJ[scope.row.field] }}
            </template>
          </el-table-column>
          <el-table-column label="填充值" min-width="320">
            <template #default="scope">
              <el-input-number
                v-if="scope.row.field === 'waferNo'"
                v-model="scope.row.value"
                :min="1"
                :max="25"
                :precision="0"
                controls-position="right"
                style="width: 100%"
              />
              <FlowId
                v-else-if="scope.row.field === 'flowId'"
                :defaultVal="333"
                v-model="scope.row.value"
                v-model:flowIdIsError="flowIdIsError"
              />
              <el-select
                v-else-if="['testArea', 'lotType'].includes(scope.row.field)"
                v-model="scope.row.value"
                placeholder="请选择"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="option in scope.row.field === 'testArea' ? TEAT_AREA_OPTIONS : LOT_TYPE_OPTIONS"
                  :key="option.key"
                  :label="option.label"
                  :value="option.key"
                />
              </el-select>
              <el-date-picker
                v-else-if="['startT', 'finishT'].includes(scope.row.field)"
                v-model="scope.row.value"
                type="datetime"
                placeholder="请选择"
                value-format="YYYY-MM-DD hh:mm:ss"
                style="width: 100%"
              />
              <el-input v-else class="input" v-model="scope.row.value" style="width: 100%" placeholder="请输入" />
            </template>
          </el-table-column>
        </el-table>
        <div v-if="dialogOptionFrom === OptionFromEnum.Single" class="remark">
          <span>备注</span>
          <el-input class="input" v-model="remarkInput" style="width: 507px" placeholder="请输入" />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleOk"> 确认 </el-button>
          <el-button @click="handleClose"> 取消 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import {
  MUST_NOT_EMPTY_FIELD,
  MUST_NOT_EMPTY_FIELD_SHOW_OBJ,
  FLOW_ID_FIELD,
  TEAT_AREA_OPTIONS,
  LOT_TYPE_OPTIONS,
  FIELD_NEW_VALUE_KEY,
} from './constants';
import uuid from '@/utils/uuid';
import { isEmpty, find, isNil } from 'lodash';
import { repairDataQualityIntegrityList } from '@/service/dataCenterQuality';

import { OptionFromEnum } from '../typing';
import FlowId from '@/views/data-center-quality/DataRecoveryPage/Steps/FlowId.vue';
import { addTag } from '@guwave/utils';

const props = defineProps<{
  fileList: any[];
  dialogOptionFrom: OptionFromEnum;
}>();
const visible = defineModel<boolean>('visible');
const emits = defineEmits(['finish']);

const flowIdIsError = ref<boolean>(false);

const tableData = ref([]);
const ableRepaireTable = ref([]);
const emptyMap = new Map();
const remarkInput = ref(null);
let oldRemark = null;

onMounted(() => {
  // 过滤不可以被修复的
  const list = props.fileList.filter((item) => item.repairFlag === true);
  ableRepaireTable.value = list;

  // 备注反显
  if (props.dialogOptionFrom === OptionFromEnum.Single && ableRepaireTable.value?.length == 1) {
    remarkInput.value = ableRepaireTable.value[0]?.remark ?? null;
    oldRemark = ableRepaireTable.value[0]?.remark ?? null;
  }

  // 生成emptyMap
  list.forEach((item) => {
    const failedFields = item?.failedFields?.split(',') ?? [];
    // 兼容历史数据
    // if (isNil(item.deviceName) && !failedFields.includes('deviceId')) {
    //   failedFields.push('deviceId');
    // }
    const emptyArr = [];
    MUST_NOT_EMPTY_FIELD.forEach((field) => {
      if (failedFields.includes(field)) {
        // 是否是flowid相关字段
        const isFlowIdField = FLOW_ID_FIELD.some((str) => str === field);
        if (isFlowIdField) {
          if (!emptyArr.includes('flowId')) {
            emptyArr.push('flowId');
          }
          return; // 如果已有flowId字段，就不再处理
        }
        emptyArr.push(field);
      }
    });
    // 更新数据
    if (!isEmpty(emptyArr)) {
      emptyMap.set(item.id, emptyArr);
    }
  });

  // 生成展示的table数据
  const data = [];
  for (const [key, value] of emptyMap) {
    // key是id , value 是 field 数组
    const file = find(list, { id: key });
    const count = value.length;
    value.forEach((field, index) => {
      const obj = {
        key: uuid(),
        fileId: file.id,
        fileName: file.fileName,
        field,
        value: null,
        rowSpanLen: index === 0 ? count : 0,
      };
      data.push(obj);
    });
  }

  tableData.value = data;
});

const objectSpanMethod = ({ row, columnIndex }) => {
  // 找到每个文件的第一个,给他设置他的个数
  if (columnIndex === 0) {
    if (row.rowSpanLen > 0) {
      return {
        rowspan: row.rowSpanLen,
        colspan: 1,
      };
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      };
    }
  }
};
const loading = ref<boolean>(false);
const handleOk = async () => {
  // 全填校验：
  const isAllFill = !tableData.value.some((row) => {
    return isNil(row.value) || row.value === '';
  });
  if (!isAllFill) {
    ElMessage.error(`请填写全部内容`);
    return;
  }
  loading.value = true;
  const repairInfos = [];

  for (const [key, value] of emptyMap) {
    // key是文件Id，value是field数组
    const fileName = find(ableRepaireTable.value, { id: key })?.fileName;

    const fileNewInfo = {}; // 当前文件的新值
    tableData.value.forEach((row) => {
      if (row.fileId === key) {
        fileNewInfo[row.field] = row.value;
      }
    });

    const repairFields = value.map((field) => {
      return {
        fieldName: FIELD_NEW_VALUE_KEY[field],
        originalValue: null,
        modifyValue: fileNewInfo[field], // 修改值
      };
    });
    repairInfos.push({
      integrityInfo: { fileName },
      repairFields,
    });
  }
  // const remarkInfo = remarkInput.value === '' || isNil(remarkInput.value) ? undefined : remarkInput.value;

  let remarkInfo = remarkInput.value;
  if (remarkInput.value === '' || isNil(remarkInput.value)) {
    if (isNil(oldRemark) || oldRemark === '') {
      remarkInfo = undefined;
    } else {
      remarkInfo = '';
    }
  }
  // 仅单条信息有备注
  const finalRemark =
    props.dialogOptionFrom === OptionFromEnum.Single && ableRepaireTable.value?.length == 1 ? remarkInfo : undefined;

  const params = {
    remark: finalRemark,
    repairInfos,
  };

  await repairDataQualityIntegrityList(params);
  emits('finish');
  loading.value = false;
  visible.value = false;
};
const handleClose = () => {
  visible.value = false;
};
const goToRecord = () => {
  addTag({
    baseUrl: '/bpms',
    tagName: '数据修复',
    url: `/#/dataRecoveryManagement `,
    isRefresh: false,
  });
};
</script>
<style scoped lang="scss">
.data-integrity-delete-dialog {
  .content {
    width: 100%;
    max-height: calc(90vh - 100px);
    overflow: scroll;
    // display: flex;
    // gap: 32px;
    .title {
      color: #000000;
      font-weight: 700;
      font-size: 14px;
      display: flex;
      justify-content: space-between;
      :deep(.el-button) {
        font-size: 14px;
        padding-right: 0px;
      }
    }

    .show-table {
      width: 100%;
      margin-top: 8px;
    }
    .remark {
      margin-top: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
      > span {
        color: #161616;
      }
    }
  }

  :deep(.el-dialog) {
    max-height: 90vh;
    margin-top: 2vh;
    margin-bottom: 2vh;
  }
}
</style>
