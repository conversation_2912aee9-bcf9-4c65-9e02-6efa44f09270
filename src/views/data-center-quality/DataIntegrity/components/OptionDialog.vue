<template>
  <div class="data-integrity-option-dialog">
    <el-dialog v-model="visible" :title="title" width="708" :before-close="handleClose" destroy-on-close>
      <!-- 删除/备注/改label -->
      <div
        v-if="[DialogTypeEnum.Delete, DialogTypeEnum.Remark, DialogTypeEnum.ChangeFileLabel].includes(type)"
        class="content"
        v-loading="loading"
      >
        <div class="title">
          已选择文件数量 <span class="font-bold">{{ ableFileList.length }} </span> 个
        </div>
        <div class="row">
          <div class="text">File Name</div>
          <!-- 小于10 -->
          <div v-if="ableFileList.length <= 10" class="name-list">
            <div v-for="row in ableFileList" :key="row.fileName" class="file-name">
              <el-tooltip effect="dark" :content="row.fileName" placement="top">
                <span> {{ row.fileName }}</span>
              </el-tooltip>
            </div>
          </div>
          <!-- 大于10 -->
          <div v-else class="name-list">
            <div v-show="!fileNameExpandedFlag">
              <div v-for="row in ableFileList.slice(0, 10)" :key="row.fileName" class="file-name">
                <el-tooltip effect="dark" :content="row.fileName" placement="top">
                  <span> {{ row.fileName }}</span>
                </el-tooltip>
              </div>
              <el-button size="small" type="text" @click="fileNameExpandedFlag = true"> 展开 </el-button>
            </div>
            <div v-show="fileNameExpandedFlag">
              <div v-for="row in ableFileList" :key="row.fileName" class="file-name">
                <el-tooltip effect="dark" :content="row.fileName" placement="top">
                  <span> {{ row.fileName }}</span>
                </el-tooltip>
              </div>
              <el-button size="small" type="text" @click="fileNameExpandedFlag = false"> 收起 </el-button>
            </div>
          </div>
        </div>
        <div v-if="type === DialogTypeEnum.ChangeFileLabel" class="items-center row">
          <span class="text">File Label</span>
          <el-select v-model="fileLabel" placeholder="Select" style="width: 240px">
            <el-option key="PENDING" label="待处理" value="PENDING"></el-option>
            <el-option key="PROCESSED" label="已处理" value="PROCESSED"></el-option>
            <el-option key="NOT_PROCESSED" label="未处理" value="NOT_PROCESSED"></el-option>
          </el-select>
        </div>
        <div
          v-if="dialogOptionFrom === OptionFromEnum.Single || type === DialogTypeEnum.Remark"
          class="items-center row"
        >
          <span class="text">备注</span>
          <el-input class="input" v-model="remarkInput" style="width: 507px" placeholder="请输入" />
        </div>
      </div>

      <!-- 重跑 -->
      <div v-else class="content" v-loading="loading">
        <div class="title">
          载入失败类型 <span class="font-bold">{{ nonEmptyCount }}</span> 个，已选择文件数量
          <span class="font-bold">{{ ableFileList.length }}</span> 个
        </div>
        <!-- 解析失败 -->
        <div v-if="parseFailFileList.length > 0">
          <div class="row">
            <span class="rerun-text">File Loading Failed Type</span>
            <div class="text-[#808080]">解析失败</div>
          </div>
          <div class="row">
            <span class="rerun-text">File Name</span>
            <!-- 小于10 -->
            <div v-if="parseFailFileList.length <= 10" class="name-list">
              <div v-for="row in parseFailFileList" :key="row.fileName" class="file-name">
                <el-tooltip effect="dark" :content="row.fileName" placement="top">
                  <span> {{ row.fileName }}</span>
                </el-tooltip>
              </div>
            </div>
            <!-- 大于10 -->
            <div v-else class="name-list">
              <div v-show="!failParseExpandedFlag">
                <div v-for="row in parseFailFileList.slice(0, 10)" :key="row.fileName" class="file-name">
                  <el-tooltip effect="dark" :content="row.fileName" placement="top">
                    <span> {{ row.fileName }}</span>
                  </el-tooltip>
                </div>
                <el-button size="small" type="text" @click="failParseExpandedFlag = true"> 展开 </el-button>
              </div>
              <div v-show="failParseExpandedFlag">
                <div v-for="row in parseFailFileList" :key="row.fileName" class="file-name">
                  <el-tooltip effect="dark" :content="row.fileName" placement="top">
                    <span> {{ row.fileName }}</span>
                  </el-tooltip>
                </div>
                <el-button size="small" type="text" @click="failParseExpandedFlag = false"> 收起 </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- STDF缺失PRR -->
        <div v-if="stdfLackFileList.length > 0">
          <div class="row">
            <span class="rerun-text">File Loading Failed Type</span>
            <div class="flex items-center gap-[8px] text-[#808080]">
              STDF缺失PRR
              <el-tooltip effect="dark" content="去掉有缺失的当前touchdown记录，再入库" placement="top">
                <SvgIcon name="tip_icon" class="cursor-pointer text-gTextColorSecondary hover:opacity-60" />
              </el-tooltip>
            </div>
          </div>
          <div class="row">
            <span class="rerun-text">File Name</span>
            <!-- 小于10 -->
            <div v-if="stdfLackFileList.length <= 10" class="name-list">
              <div v-for="row in stdfLackFileList" :key="row.fileName" class="file-name">{{ row.fileName }}</div>
            </div>
            <!-- 大于10 -->
            <div v-else class="name-list">
              <div v-show="!lackPrrExpandedFlag">
                <div v-for="row in stdfLackFileList.slice(0, 10)" :key="row.fileName" class="file-name">
                  {{ row.fileName }}
                </div>
                <el-button size="small" type="text" @click="lackPrrExpandedFlag = true"> 展开 </el-button>
              </div>
              <div v-show="lackPrrExpandedFlag">
                <div v-for="row in stdfLackFileList" :key="row.fileName" class="file-name">{{ row.fileName }}</div>
                <el-button size="small" type="text" @click="lackPrrExpandedFlag = fasle"> 收起 </el-button>
              </div>
            </div>
          </div>
        </div>

        <div v-if="dialogOptionFrom === OptionFromEnum.Single" class="items-center row">
          <span :class="{ 'rerun-text': nonEmptyCount > 0 }">备注</span>
          <el-input class="input" v-model="remarkInput" style="width: 507px" placeholder="请输入" />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleOk"> 确认 </el-button>
          <el-button @click="handleClose"> 取消 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { DialogTypeEnum, OptionFromEnum } from '../typing';
import {
  changeFileLabelDataQualityIntegrityList,
  deleteDataQualityIntegrityList,
  remarkDataQualityIntegrityList,
  rerunDataQualityIntegrityList,
} from '@/service/dataCenterQuality';
import { isEmpty, isNil } from 'lodash';

const props = defineProps<{
  type: DialogTypeEnum;
  dialogOptionFrom: OptionFromEnum;
  fileList: any[];
  selectedFileList: any[];
}>();

const emits = defineEmits(['finish', 'selectChange']);

const visible = defineModel<boolean>('visible');

const title = ref<string>('');

const ableFileList = ref<any>([]);
const parseFailFileList = ref<any>([]);
const stdfLackFileList = ref<any>([]);
const nonEmptyCount = ref<number>(0);
let oldRemark = null;
onMounted(() => {
  const titleObj = {
    [DialogTypeEnum.Delete]: '删除数据',
    [DialogTypeEnum.Rerun]: '重跑数据',
    [DialogTypeEnum.Remark]: '编辑备注',
    [DialogTypeEnum.Repair]: '修复数据',
    [DialogTypeEnum.ChangeFileLabel]: '编辑 File Label',
  };
  title.value = titleObj[props.type];

  // 过滤可以操作的数据
  const flagObj = {
    [DialogTypeEnum.Delete]: 'deleteFlag',
    [DialogTypeEnum.Repair]: 'repairFlag',
    [DialogTypeEnum.Rerun]: 'reRunFlag',
  };
  if ([DialogTypeEnum.Delete, DialogTypeEnum.Repair, DialogTypeEnum.Rerun].includes(props.type)) {
    ableFileList.value = props.fileList.filter((item) => {
      const flag = flagObj[props.type];
      return item[flag];
    });
  } else {
    ableFileList.value = props.fileList;
  }

  if (props.type === DialogTypeEnum.Rerun) {
    parseFailFileList.value = ableFileList.value.filter((item) => item.originalFailedType === 'PARSE_FILE_EXCEPTION');
    stdfLackFileList.value = ableFileList.value.filter(
      (item) => item.originalFailedType === 'TOUCH_DOWN_NOT_FOUND_PRR_EXCEPTION',
    );
    nonEmptyCount.value = [parseFailFileList.value, stdfLackFileList.value].filter((arr) => !isEmpty(arr)).length;
  }
  // 备注反显
  if (props.dialogOptionFrom === OptionFromEnum.Single && ableFileList.value?.length == 1) {
    remarkInput.value = ableFileList.value[0]?.remark ?? null;
    oldRemark = ableFileList.value[0]?.remark ?? null;
  }
  // file label反显
  if (
    props.type === DialogTypeEnum.ChangeFileLabel &&
    props.dialogOptionFrom === OptionFromEnum.Single &&
    ableFileList.value?.length == 1
  ) {
    fileLabel.value = ableFileList.value[0]?.fileLabel ?? 'PENDING';
  }
});

const remarkInput = ref(null);
const fileLabel = ref<'PENDING' | 'PROCESSED' | 'NOT_PROCESSED'>('PENDING');
const requestObj = {
  [DialogTypeEnum.Delete]: deleteDataQualityIntegrityList,
  [DialogTypeEnum.Rerun]: rerunDataQualityIntegrityList,
  [DialogTypeEnum.Remark]: remarkDataQualityIntegrityList,
  [DialogTypeEnum.ChangeFileLabel]: changeFileLabelDataQualityIntegrityList,
};

const fileNameExpandedFlag = ref<boolean>(false);
const failParseExpandedFlag = ref<boolean>(false);
const lackPrrExpandedFlag = ref<boolean>(false);
const loading = ref<boolean>(false);

const handleOk = async () => {
  loading.value = true;
  const request = requestObj[props.type];
  const fileNameList = ableFileList.value.map((item) => item.fileName);

  let remarkInfo = remarkInput.value;
  if (remarkInput.value === '' || isNil(remarkInput.value)) {
    if (isNil(oldRemark) || oldRemark === '') {
      remarkInfo = undefined;
    } else {
      // 清空
      remarkInfo = '';
    }
  }
  // 仅单条信息有备注/ 批量备注有备注
  const finalRemark =
    props.type === DialogTypeEnum.Remark ||
    (props.dialogOptionFrom === OptionFromEnum.Single && ableFileList.value?.length == 1)
      ? remarkInfo
      : undefined;
  let params = {};
  if (props.type === DialogTypeEnum.Delete) {
    params = {
      fileNames: fileNameList,
      // dataIntegrityDTOS: ableFileList.value,
      remark: finalRemark,
    };
  } else if ([DialogTypeEnum.Remark, DialogTypeEnum.Rerun].includes(props.type)) {
    params = {
      fileNames: fileNameList,
      remark: finalRemark,
    };
  } else if (props.type === DialogTypeEnum.ChangeFileLabel) {
    params = {
      fileNames: fileNameList,
      fileLabel: fileLabel.value,
      remark: finalRemark,
    };
  }
  await request(params);
  // 删除时要把已选中列表里的该项也删掉
  if (props.type === DialogTypeEnum.Delete) {
    const ids = ableFileList.value.map((file) => file.id);
    const newList = props.selectedFileList.filter((item) => {
      return !ids.includes(item.id);
    });
    emits('selectChange', newList);
  }
  emits('finish');
  loading.value = false;
  visible.value = false;
};
const handleClose = () => {
  visible.value = false;
};
</script>
<style scoped lang="scss">
.data-integrity-option-dialog {
  .content {
    width: 100%;
    max-height: calc(95vh - 100px);
    overflow: scroll;

    .title {
      height: 40px;
      border-radius: 2px;
      background-color: #eeeeee;
      color: #161616;
      font-weight: 400;
      font-size: 14px;
      line-height: 40px;
      padding-left: 16px;
    }
    .row {
      width: 100%;
      margin-top: 12px;
      display: flex;
      gap: 8px;
      overflow: hidden;
      .text {
        width: 80px;
        text-align: right;
        color: #161616;
      }
      .rerun-text {
        width: 162px !important;
        text-align: right;
        color: #161616;
      }
      .name-list {
        flex: 1;
        overflow: hidden;
        color: #808080;
        .file-name {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  :deep(.el-dialog) {
    max-height: 95vh;
    margin-top: 2vh;
    margin-bottom: 2vh;
  }
}
</style>
