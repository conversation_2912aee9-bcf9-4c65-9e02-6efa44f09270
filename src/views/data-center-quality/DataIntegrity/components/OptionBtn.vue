<template>
  <div class="btn-container">
    <el-button :disabled="!rowData.enableDownload" :loading="downloadLoading" link @click="handleDownload">
      导出原文件
    </el-button>
    <el-button :disabled="!rowData.repairFlag" link @click="handleClick(DialogTypeEnum.Repair)"> 修复 </el-button>
    <el-button :disabled="!rowData.reRunFlag" link @click="handleClick(DialogTypeEnum.Rerun)"> 重跑 </el-button>

    <el-dropdown trigger="click" @command="handleMoreOption">
      <el-button link class="ml-[12px]"> 更多 </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item :command="DialogTypeEnum.Remark"> 备注</el-dropdown-item>
          <el-dropdown-item :command="DialogTypeEnum.Delete" :disabled="!rowData.deleteFlag">删除</el-dropdown-item>
          <el-dropdown-item :command="DialogTypeEnum.ChangeFileLabel">修改File Label</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup lang="ts">
import { downLoadDataExactStdfFile } from '@/service/dataCenterQuality';

import { DialogTypeEnum } from '../typing';
import { downloadExcelFileFromBlobRes } from '../../utils';

const props = defineProps<{
  rowData: any;
}>();

const emits = defineEmits(['openDialog']);

const downloadLoading = ref<boolean>(false);
const handleDownload = async () => {
  downloadLoading.value = true;
  const res = await downLoadDataExactStdfFile({ fileName: props.rowData.fileName });
  await downloadExcelFileFromBlobRes(res, props.rowData.fileName, { needAppendName: false });
  downloadLoading.value = false;
};
const handleMoreOption = (command: DialogTypeEnum.Remark | DialogTypeEnum.Delete | DialogTypeEnum.ChangeFileLabel) => {
  handleClick(command);
};

const handleClick = (btnType: DialogTypeEnum) => {
  emits('openDialog', [props.rowData], btnType);
};
</script>
<style scoped lang="scss">
.btn-container {
  display: flex;
}
</style>
