<template>
  <CommonTable
    title="接收文件列表"
    ref="tableRef"
    row-key="id"
    :data="tableData"
    v-model:pagination="pageInfo"
    v-loading="loading"
    @selection-change="handleSelectionChange"
    @sort-change="handleSort"
    border
  >
    <template #headerRightEnd>
      <el-dropdown @command="handleBatchOption">
        <el-button :disabled="selectedFileList?.length < 1">
          批量操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="exportFile" :disabled="disabledFlags.download">导出原文件</el-dropdown-item>
            <el-dropdown-item :command="DialogTypeEnum.Repair" :disabled="disabledFlags.repair">修复</el-dropdown-item>
            <el-dropdown-item :command="DialogTypeEnum.Rerun" :disabled="disabledFlags.reRun">重跑</el-dropdown-item>
            <el-dropdown-item :command="DialogTypeEnum.Remark"> 备注</el-dropdown-item>
            <el-dropdown-item :command="DialogTypeEnum.Delete" :disabled="disabledFlags.delete">删除</el-dropdown-item>
            <el-dropdown-item :command="DialogTypeEnum.ChangeFileLabel">修改File Label</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <el-button
        class="ml-[8px]"
        :loading="exportDataLoading"
        @click="handleExportData"
        :disabled="tableData?.length === 0"
      >
        下载
      </el-button>
    </template>
    <el-table-column type="selection" width="55" fixed :reserve-selection="true" />
    <el-table-column
      v-for="item in TABLE_COLUMNS"
      :key="item.key"
      :prop="item.key"
      :label="item.label"
      :min-width="item?.minWidth"
      show-overflow-tooltip
      :fixed="item?.fixed ?? false"
      :sortable="item.sortable ?? false"
    >
      <template #default="scope">
        <span v-if="item.key === 'fileLabel'"> {{ FileLabelTextObj[scope.row[item.key]] }} </span>
      </template>
    </el-table-column>
    <el-table-column width="240" fixed="right" label="操作">
      <template #default="scope">
        <OptionBtn :rowData="scope.row" @openDialog="handleOpenDialog" />
      </template>
    </el-table-column>
  </CommonTable>
  <OptionDialog
    v-if="dialogVisible"
    v-model:visible="dialogVisible"
    :selectedFileList="selectedFileList"
    :type="dialogType"
    :dialogOptionFrom="dialogOptionFrom"
    :fileList="dialogDealFileList"
    @finish="onDialogClose"
    @selectChange="handleSelectionChange"
  />
  <RepairDialog
    v-if="repairDialogVisible"
    v-model:visible="repairDialogVisible"
    :fileList="dialogDealFileList"
    :dialogOptionFrom="dialogOptionFrom"
    @finish="onDialogClose"
  />
</template>

<script setup lang="ts">
import { CommonTable, PaginationType, SearchFormParam } from '@guwave/components';
import { DialogTypeEnum, OptionFromEnum } from '../typing';
import { downLoadDataExactStdfFile, getIntegrityExport } from '@/service/dataCenterQuality';

import { TABLE_COLUMNS } from './constants';

import OptionBtn from './OptionBtn.vue';
import OptionDialog from './OptionDialog.vue';
import RepairDialog from './RepairDialog.vue';
import { ArrowDown } from '@element-plus/icons-vue';
import { downloadExcelFileFromBlobRes } from '../../utils';

const props = defineProps<{
  searchParams: SearchFormParam;
  loading: boolean;
}>();

const pageInfo = defineModel<PaginationType>('pageInfo');
const tableData = defineModel<any[]>('tableData');

const emits = defineEmits(['sort', 'refresh']);

const tableRef = ref(null);

const handleBatchOption = (command: 'exportFile' | DialogTypeEnum) => {
  if (command === 'exportFile') {
    batchDownload();
    return;
  }
  handleOpenDialog(selectedFileList.value, command, OptionFromEnum.Batch);
};

// 点击表格排序的响应
const handleSort = ({ prop, order }) => {
  emits('sort', { prop, order });
};

const selectedFileList = ref<any[]>([]); // 当前表格的选中项
const handleSelectionChange = (val: any[]) => {
  selectedFileList.value = val;
};

const clearTableSelection = () => {
  // 勾选清空
  tableRef.value?.tableRef.clearSelection();
  selectedFileList.value = [];
};

const disabledFlags = computed(() => {
  return {
    download: selectedFileList.value?.every((item) => !item.enableDownload),
    repair: selectedFileList.value?.every((item) => !item.repairFlag),
    reRun: selectedFileList.value?.every((item) => !item.reRunFlag),
    delete: selectedFileList.value?.every((item) => !item.deleteFlag),
  };
});

const batchDownload = () => {
  if (selectedFileList.value.length > 50) {
    ElMessage.error(`最多导出50个原文件`);
    return;
  }
  selectedFileList.value.forEach(async (item) => {
    const res = await downLoadDataExactStdfFile({ fileName: item.fileName });
    await downloadExcelFileFromBlobRes(res, item.fileName, { needAppendName: false });
  });
};

const dialogVisible = ref<boolean>(false);
const repairDialogVisible = ref<boolean>(false);
const dialogType = ref<DialogTypeEnum>(DialogTypeEnum.Delete);
const dialogOptionFrom = ref<OptionFromEnum>(OptionFromEnum.Single);
const dialogDealFileList = ref<any[]>([]); // 弹窗处理的的list
const handleOpenDialog = (
  rowList: any[],
  modalType: DialogTypeEnum,
  optionFrom: OptionFromEnum = OptionFromEnum.Single,
) => {
  if (rowList.length > 50) {
    ElMessage.error(`最多编辑50个文件`);
    return;
  }
  dialogDealFileList.value = rowList;
  dialogType.value = modalType;
  dialogOptionFrom.value = optionFrom;
  if (modalType === DialogTypeEnum.Repair) {
    repairDialogVisible.value = true;
  } else {
    dialogVisible.value = true;
  }
};

const onDialogClose = () => {
  emits('refresh');
  clearTableSelection();
};

// 导出全部文件
const exportDataLoading = ref<boolean>(false);
const handleExportData = async () => {
  exportDataLoading.value = true;
  const res = await getIntegrityExport({
    ...props.searchParams, // 里面有fileIdList
  });
  await downloadExcelFileFromBlobRes(res, '数据完整性');
  exportDataLoading.value = false;
};

const FileLabelTextObj = {
  PENDING: '待处理',
  PROCESSED: '已处理',
  NOT_PROCESSED: '未处理',
};

// 明确暴露方法给父组件
defineExpose({
  clearTableSelection,
});
</script>
<style scoped lang="scss"></style>
