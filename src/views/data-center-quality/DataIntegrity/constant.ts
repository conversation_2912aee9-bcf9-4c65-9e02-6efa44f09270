import { DATE_SHORTCUTS, SearchFormItem, SearchFormTypeEnum } from '@guwave/components';
import { getIntegrityOptions } from '@/service/dataCenterQuality';

import { dayjs } from 'element-plus';
import { getQueryString } from '@guwave/utils';
import { getSubCustomer } from '@/service/common';

export const SEARCH_LIST = (): SearchFormItem[] => {
  const list = [
    {
      type: SearchFormTypeEnum.DATE,
      label: 'FTP文件加载时间',
      key: 'loadingTime',
      nativeAttr: {
        type: 'daterange',
        unlinkPanels: true,
        rangeSeparator: '-',
        startPlaceholder: '开始时间',
        endPlaceholder: '结束时间',
        shortcuts: DATE_SHORTCUTS,
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      label: 'Sub Customer',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'subCustomerList',
      api: getSubCustomer,
      dropdownResFn: (resData) => {
        const options = resData.map((item) => ({
          label: item.subCustomerCode,
          value: item.subCustomerCode,
        }));
        return options;
      },
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Factory',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'factoryList',
      dropdownKey: 'FACTORY',
      api: getIntegrityOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Test Area',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'testAreaList',
      dropdownKey: 'TEST_AREA',
      api: getIntegrityOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Device',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'deviceIdList',
      dropdownKey: 'DEVICE_ID',
      api: getIntegrityOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Test Stage',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'testStageList',
      dropdownKey: 'TEST_STAGE',
      api: getIntegrityOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Lot Type',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'lotTypeList',
      dropdownKey: 'LOT_TYPE',
      api: getIntegrityOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Lot',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'lotIdList',
      dropdownKey: 'LOT_ID',
      api: getIntegrityOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Sub Lot',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'sblotIdList',
      dropdownKey: 'SBLOT_ID',
      api: getIntegrityOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Wafer',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'waferIdList',
      dropdownKey: 'WAFER_ID',
      api: getIntegrityOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'File Loaded',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'dataLoadedList',
      dropdownKey: 'DATA_LOADED',
      api: getIntegrityOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'File Loading Failed Type',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'loadingFailedTypeList',
      dropdownKey: 'DATA_LOADING_FAILED_TYPE',
      api: getIntegrityOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Test Program',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'testProgramList',
      dropdownKey: 'TEST_PROGRAM',
      api: getIntegrityOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'File Location',
      type: SearchFormTypeEnum.INPUT,
      key: 'fileLocation',
    },
    {
      label: 'File Name',
      type: SearchFormTypeEnum.INPUT,
      key: 'fileName',
    },
  ];

  // 重写api
  const url = decodeURIComponent(location.href);
  const fileIdList = getQueryString(url, 'fileIdList');
  const res = list.map((item) => {
    // 删除filterKey 重写api的请求参数
    return {
      ...item,
      api: (params) => {
        if (item.key === 'subCustomerList') {
          return item.api(); //subCustomer不需要其他参数
        }
        const newParams = {
          ...params,
          filterField: params.field,
          field: undefined,
          startTime: params?.loadingTime ? dayjs(params.loadingTime?.[0]).format('YYYY-MM-DD 00:00:00') : undefined,
          endTime: params?.loadingTime ? dayjs(params.loadingTime?.[1]).format('YYYY-MM-DD 23:59:59') : undefined,
          loadingTime: undefined,
          fileIdList: fileIdList?.split(',')?.map(Number) ?? undefined, // fileIdList是url带来的参数
        };

        return item.api(newParams);
      },
    };
  });

  return res;
};
