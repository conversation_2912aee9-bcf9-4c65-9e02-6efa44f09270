<template>
  <div class="steps-outer-wrap">
    <div class="steps-inner-wrap">
      <div class="step-ring">
        <template v-for="(step, i) in data" :key="i">
          <div class="step">
            <div>
              <div class="title">
                <span v-if="i" class="font-[700]">{{ `Step${i}` }}</span>
                {{ getStepTitle(step.key) }}
              </div>
              <div class="value font-[600] f_segoe">{{ step.value }}</div>
            </div>

            <div class="h-[110px]">
              <Ring :rate="step.progress" />
            </div>
          </div>

          <img v-if="i < props.data.length - 1" class="arrow" src="@/assets/icon/svg/arrow.svg" />
        </template>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import Ring from './Ring.vue';
const props = defineProps<{ data: any }>();
const getStepTitle = (key) => {
  return (
    {
      FTP_FILE: 'FTP 文件数',
      CALCULATE_PLATFORM: '传输至大数据存储计算平台',
      TRIGGER_PARSE: '触发解析',
      KEY_FIELD_CHECK: '关键字段验证',
      PARSE_AND_IN_DB: '解析入库',
    }[key || ''] || ''
  );
};
</script>
<style scoped lang="scss">
.steps-outer-wrap {
  flex: 1;
  position: relative;
  margin-bottom: -8px;

  .steps-inner-wrap {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: auto hidden;
  }
}
.step-ring {
  display: flex;
  justify-content: space-between;
  height: 100%;
  padding-top: 8px;
  margin-right: 30px;

  .step {
    flex: none;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .title {
      font-size: 12px;
      line-height: 24px;
      color: #565656;

      > span:first-child {
        padding-right: 8px;
      }
    }

    .value {
      font-size: 30px;
      line-height: 48px;
    }

    .progress {
      display: flex;
      justify-content: center;
    }
  }
  .arrow {
    height: 24px;
    width: 32px;
    margin-top: 126px;
  }
}
</style>
