<template>
  <div class="chart">
    <div ref="containerRef" :style="{ height: '100%' }"></div>
  </div>
</template>

<script setup lang="ts">
import useEcharts from '@/hooks/echarts/useEcharts';
import { DataQualityBaseDataDetailItem } from '../typing';
import { ECHARTS_COMMON_OPTIONS } from '@/utils/echarts/constants';
import { formatInDBStep } from './utils';

const props = defineProps<{
  //画图的数据
  data: DataQualityBaseDataDetailItem[];
}>();

const containerRef = ref();

const { echartsRef } = useEcharts(containerRef);

const buildOption = (data: DataQualityBaseDataDetailItem[]) => {
  return {
    tooltip: ECHARTS_COMMON_OPTIONS.tooltip,
    series: [
      {
        type: 'treemap',
        breadcrumb: { show: false },
        nodeClick: false,
        top: -16,
        bottom: 8,
        left: 8,
        right: 8,
        label: { show: true, overflow: 'break', formatter: ({ name, value }) => `${name}\n${value}` },
        upperLabel: { show: true },
        data: (data || []).map((item) => ({
          name: `${formatInDBStep(item.colorByKey)} ${item.indexKey}`,
          value: item.indexValue,
        })),
      },
    ],
  };
};

const initChart = () => {
  const myChart = echartsRef.value;

  if (!myChart) return;
  if (!props?.data) return;

  const option = buildOption(props.data);

  // myChart.clear();
  myChart.setOption(option, true);
};

watch([() => props.data, echartsRef], () => {
  initChart();
});

onMounted(() => {
  initChart();
});
</script>
<style lang="scss" scoped>
.chart {
  height: 100%;
  position: relative;
  > div {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
}
</style>
