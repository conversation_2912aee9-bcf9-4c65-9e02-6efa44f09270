<template>
  <div class="chart">
    <div ref="containerRef" :style="{ height: '100%' }"></div>
  </div>
</template>

<script setup lang="ts">
import useEcharts from '@/hooks/echarts/useEcharts';
import { DataQualityBaseDataDetailItem } from '../typing';
import { ECHARTS_COMMON_OPTIONS } from '@/utils/echarts/constants';

const props = defineProps<{
  //画图的数据
  data: DataQualityBaseDataDetailItem[];
}>();

const containerRef = ref();

const { echartsRef } = useEcharts(containerRef);

const buildOption = (data: DataQualityBaseDataDetailItem[]) => {
  const xAxisData = data.map((item) => item.indexKey);
  const seriesData = data.map((item) => item.indexValue);

  return {
    tooltip: {
      ...ECHARTS_COMMON_OPTIONS.tooltip,
      trigger: 'axis',
    },

    grid: {
      top: 35,
      left: 15,
      right: 30,
      bottom: 10,
      containLabel: true,
    },
    legend: {
      show: false,
    },
    xAxis: {
      type: 'category',
      axisTick: {
        alignWithLabel: true,
      },
      data: xAxisData,
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '',
        type: 'bar',
        barMaxWidth: 15,

        data: seriesData,
      },
    ],
  };
};

watch([() => props.data, echartsRef], ([_data]) => {
  const myChart = echartsRef.value;

  if (!myChart) return;
  myChart.clear();
  if (!_data) return;

  const option = buildOption(_data);

  myChart.setOption(option, true);
});

onMounted(() => {
  const myChart = echartsRef.value;

  if (!myChart) return;
  myChart.clear();
  if (!props?.data) return;

  const option = buildOption(props.data);

  myChart.setOption(option, true);
});
</script>
<style lang="scss" scoped>
.chart {
  height: 100%;
  position: relative;
  > div {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
}
</style>
