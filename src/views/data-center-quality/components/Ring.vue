<template>
  <div class="chart">
    <div ref="containerRef" :style="{ height: '100%' }"></div>
  </div>
</template>

<script setup lang="ts">
import useEcharts from '@/hooks/echarts/useEcharts';

import { isNumber } from 'lodash';

const props = defineProps<{
  //画图的数据
  rate: number;
}>();

const containerRef = ref();

const { echartsRef } = useEcharts(containerRef);

const buildOption = (rate: number) => {
  return {
    series: [
      {
        emphasis: {
          scale: false,
        },
        name: '',
        label: {
          show: false,
        },
        type: 'pie',
        radius: ['68%', '90%'],
        data: [
          {
            name: '入库',
            value: rate,
          },
          {
            name: '未入库',
            value: 1 - rate,
            itemStyle: {
              color: '#eee',
            },
          },
        ],
      },
    ],
  };
};

const initChart = () => {
  const myChart = echartsRef.value;

  if (!myChart) return;
  if (!isNumber(props?.rate)) return;

  const option = buildOption(props.rate);

  // myChart.clear();
  myChart.setOption(option, true);
};

watch([() => props.rate, echartsRef], () => {
  initChart();
});

onMounted(() => {
  initChart();
});
</script>
<style lang="scss" scoped>
.chart {
  height: 100%;
  position: relative;
  > div {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
}
</style>
