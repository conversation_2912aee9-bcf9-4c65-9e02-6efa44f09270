<template>
  <div class="chart">
    <div ref="containerRef" :style="{ height: '100%' }"></div>
  </div>
</template>

<script setup lang="ts">
import useEcharts from '@/hooks/echarts/useEcharts';
import { DataQualityBaseDataDetailItem } from '../typing';
import { ECHARTS_COMMON_OPTIONS } from '@/utils/echarts/constants';
import { sumBy } from 'lodash';
import { toPercent } from '@/utils/number';

const props = defineProps<{
  //画图的数据
  data: DataQualityBaseDataDetailItem[];
}>();

const containerRef = ref();

const { echartsRef } = useEcharts(containerRef);

const buildOption = (data: DataQualityBaseDataDetailItem[]) => {
  const total = sumBy(data, 'indexValue');
  return {
    tooltip: {
      ...ECHARTS_COMMON_OPTIONS.tooltip,
      formatter: function (params) {
        const { name, value } = params;
        return `${name}：${toPercent(value / total)}`;
      },
    },
    legend: {
      type: 'scroll',
      icon: 'circle',
      left: 'center',
      itemGap: 4,
      itemHeight: 10,
      itemWidth: 10,
      textStyle: {
        width: 150,
        overflow: 'truncate',
      },
      itemStyle: {
        borderWidth: 0,
      },
      pageIconSize: [10, 10],
      pageTextStyle: {
        fontSize: 12,
      },
    },
    series: [
      {
        emphasis: {
          scale: false,
        },
        name: '',
        type: 'pie',
        radius: '70%',
        top: 24,
        avoidLabelOverlap: true,
        data: (data || []).map(({ indexKey, indexValue }) => ({ name: indexKey || 'Other', value: indexValue })),
        label: {
          formatter: function (params) {
            const { name, value } = params;
            return `${name} ${toPercent(value / total)}`;
          },
        },
      },
    ],
  };
};

const initChart = () => {
  const myChart = echartsRef.value;

  if (!myChart) return;
  myChart.clear();

  if (!props?.data) return;

  const option = buildOption(props.data);

  myChart.setOption(option, true);
};

watch([() => props.data, echartsRef], () => {
  initChart();
});

onMounted(() => {
  initChart();
});
</script>
<style lang="scss" scoped>
.chart {
  height: 100%;
  position: relative;
  > div {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
}
</style>
