<template>
  <div class="timeline-outer-wrap">
    <div class="timeline-inner-wrap">
      <div class="step-timeline">
        <div>
          <div class="top-item"></div>
          <div class="bottom-item">
            <div class="bg f_b"></div>
            <div class="fixed-label f_b">测试数据文件生成</div>
          </div>
        </div>
        <div>
          <div class="top-item">
            <div>
              <span>文件上传耗时</span>
              <span>Avg(End Time - FTP加载时间)</span>
              <span class="f_b_600 f_segoe font-[600]">{{ props.data[0] }}</span>
            </div>
          </div>
          <div class="bottom-item">
            <div class="bg f_b">文件上传</div>
            <div class="fixed-label f_b p_r">FTP</div>
          </div>
        </div>
        <div>
          <div class="top-item">
            <div>
              <span>文件下载耗时</span>
              <span>Avg(FTP加载时间 - 数据接收时间)</span>
              <span class="f_b_600 f_segoe font-[600]">{{ props.data[1] }}</span>
            </div>
          </div>
          <div class="bottom-item">
            <div class="bg f_b">文件下载</div>
            <div class="fixed-label f_b p_r">大数据存储计算平台</div>
          </div>
        </div>
        <div>
          <div class="top-item">
            <div>
              <span>解析入库耗时</span>
              <span>Avg(数据接收时间 - Create Time)</span>
              <span class="f_b_600 f_segoe font-[600]">{{ props.data[2] }}</span>
            </div>
          </div>
          <div class="bottom-item">
            <div class="bg f_b">解析入库</div>
          </div>
        </div>
        <div>
          <div class="top-item"></div>
          <div class="bottom-item">
            <div class="bg"></div>
            <div class="fixed-label f_b">数据成功入库</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps<{ data: string[] }>();
</script>
<style scoped lang="scss">
.timeline-outer-wrap {
  flex: 1;
  position: relative;
  margin-bottom: -8px;

  .timeline-inner-wrap {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: auto hidden;
  }
}
.step-timeline {
  flex: 1;
  display: flex;
  height: 100%;
  padding-bottom: 8px;

  > div {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .top-item {
      display: flex;
      justify-content: center;
      height: 96px;
      margin-top: 8px;
      padding: 0 12px;
      border-right: 1px solid #d7d8dc;

      span {
        display: block;
        font-size: 12px;
        color: #565656;
        line-height: 24px;
        white-space: nowrap;
      }
      span:last-child {
        font-size: 30px;
        color: #161616;
        line-height: 48px;
      }
    }
    .bottom-item {
      position: relative;
      height: 32px;
      line-height: 32px;
      text-align: center;
      color: #fff;
      font-size: 12px;

      > .bg {
        height: 100%;
        margin: 0 2px;
        background-color: #d9d9d9;
      }
    }
  }

  > div:last-child .top-item {
    border-right: none;
  }

  > div:first-child,
  > div:last-child {
    flex: 0 0 104px;
  }
  > div:nth-child(2) {
    .bg {
      background: #75adff;
    }
  }
  > div:nth-child(3) {
    .bg {
      background: #3c8afa;
    }
  }
  > div:nth-child(4) {
    .bg {
      background: #1764d2;
    }
  }

  .fixed-label {
    display: block;
    position: absolute;
    top: -100%;
    width: 100%;
    font-size: 12px;
    color: #565656;
    text-align: center;
    z-index: 2;

    &.p_r {
      left: 50%;
    }

    &::before {
      position: absolute;
      height: 24px;
      top: 22px;
      left: 50%;
      transform: translateX(-50%);
      border-right: 1px solid #565656;
      content: '';
    }
    &::after {
      position: absolute;
      height: 12px;
      width: 12px;
      top: calc(100% + 10px);
      left: 50%;
      transform: translateX(-50%);
      background: #ffffff;
      border: 1px solid #565656;
      border-radius: 50%;
      content: '';
    }
  }
}
</style>
