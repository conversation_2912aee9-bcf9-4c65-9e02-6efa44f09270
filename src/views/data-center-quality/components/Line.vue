<template>
  <div class="chart">
    <div ref="containerRef" :style="{ height: '100%' }"></div>
  </div>
</template>

<script setup lang="ts">
import useEcharts from '@/hooks/echarts/useEcharts';
import { DataQualityBaseDataDetailItem } from '../typing';
import { ECHARTS_COMMON_OPTIONS } from '@/utils/echarts/constants';
import { formatInDBStep } from './utils';
import { get, groupBy, isEmpty, sortBy, uniqBy } from 'lodash';

const props = defineProps<{
  //画图的数据
  data: DataQualityBaseDataDetailItem[];
}>();

const containerRef = ref();

const { echartsRef } = useEcharts(containerRef);

const buildOption = (oriData: DataQualityBaseDataDetailItem[]) => {
  const data = oriData.map((item) => ({
    x: item.indexKey,
    y: item.indexValue,
    type: formatInDBStep(item.colorByKey),
  }));

  if (isEmpty(data)) return;
  const sortedData = sortBy(data, 'x');
  const series = sortBy(Object.entries(groupBy(sortedData, 'type')), ([key]) => key).map(([key, items]) => ({
    name: key,
    type: 'line',

    data: items.map((item) => get(item, 'y')),
  }));
  const xAxis = {
    type: 'category',
    data: uniqBy(sortedData, 'x').map((item) => get(item, 'x')),
  };

  return {
    tooltip: {
      ...ECHARTS_COMMON_OPTIONS.tooltip,
      trigger: 'axis',
    },
    grid: {
      top: 35,
      left: 15,
      right: 30,
      bottom: 10,
      containLabel: true,
    },
    legend: {
      left: 'center',
      top: 0,
      icon: 'circle',
      itemGap: 4,
      itemHeight: 10,
      itemWidth: 10,
    },
    yAxis: {
      type: 'value',
    },
    xAxis,
    series,
  };
};

const initChart = () => {
  const myChart = echartsRef.value;

  if (!myChart) return;
  if (!props?.data) return;

  const option = buildOption(props.data);

  // myChart.clear();
  myChart.setOption(option, true);
};

watch([() => props.data, echartsRef], () => {
  initChart();
});

onMounted(() => {
  initChart();
});
</script>
<style lang="scss" scoped>
.chart {
  height: 100%;
  position: relative;
  > div {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
}
</style>
