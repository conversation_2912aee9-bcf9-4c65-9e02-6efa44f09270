<template>
  <div class="chart">
    <div ref="containerRef" :style="{ height: '100%' }"></div>
  </div>
</template>

<script setup lang="ts">
import useEcharts from '@/hooks/echarts/useEcharts';
import { formatInDBStep } from './utils';
import { DataQualityBaseDataDetailItem } from '../typing';
import { ECHARTS_COMMON_OPTIONS } from '@/utils/echarts/constants';
import { groupBy, isEmpty, get, sortBy, uniqBy, zip, sum } from 'lodash';

const props = defineProps<{
  //画图的数据
  data: DataQualityBaseDataDetailItem[];
  sortByGroup?: string;
  isHorizontal?: boolean;
}>();

const containerRef = ref();

const { echartsRef } = useEcharts(containerRef);

const buildOption = (oriData: DataQualityBaseDataDetailItem[]) => {
  const data = oriData.map((item) => ({
    x: item.indexKey,
    y: item.indexValue,
    type: formatInDBStep(item.colorByKey),
  }));

  if (isEmpty(data)) return;
  const sortedData = sortBy(data, 'x');
  const series = sortBy(Object.entries(groupBy(sortedData, 'type')), ([key]) => key).map(([key, items]) => ({
    name: key,
    type: 'bar',
    stack: 'firstGroup',
    data: items.map((item) => get(item, 'y')),
  }));
  const xAxis = {
    type: 'category',
    data: uniqBy(sortedData, 'x').map((item) => get(item, 'x')),
  };
  // 横向叠加柱状图时，默认降序排序
  if (props?.sortByGroup) {
    // sortBy asc
    const sortedData = sortBy(
      zip(...series.map((item) => item.data)).map((arr, i) => {
        return {
          total: sum(arr),
          x: xAxis.data[i],
          index: i,
        };
      }),
      'total',
    );
    if (props?.sortByGroup === 'desc') {
      sortedData.reverse();
    }
    xAxis.data = sortedData.map(({ x }) => x);
    series.forEach((item) => {
      item.data = sortedData.map(({ index }) => item.data[index]);
    });
  }

  return {
    tooltip: {
      ...ECHARTS_COMMON_OPTIONS.tooltip,
      trigger: 'axis',
    },
    grid: {
      top: 35,
      left: 15,
      right: 30,
      bottom: 10,
      containLabel: true,
    },
    legend: {
      left: 'center',
      top: 0,
      icon: 'circle',
      itemGap: 4,
      itemHeight: 10,
      itemWidth: 10,
    },
    [props?.isHorizontal ? 'xAxis' : 'yAxis']: {
      type: 'value',
    },
    [props?.isHorizontal ? 'yAxis' : 'xAxis']: xAxis,
    series,
  };
};

watch([() => props.data, echartsRef], ([_data]) => {
  const myChart = echartsRef.value;

  if (!myChart) return;
  myChart.clear();

  if (!_data) return;

  const option = buildOption(_data);

  option && myChart.setOption(option, true);
});

onMounted(() => {
  const myChart = echartsRef.value;

  if (!myChart) return;
  if (!props?.data) return;

  const option = buildOption(props.data);

  // myChart.clear();
  option && myChart.setOption(option, true);
});
</script>
<style lang="scss" scoped>
.chart {
  height: 100%;
  position: relative;
  > div {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
}
</style>
