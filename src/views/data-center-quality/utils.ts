import { SearchFormItem, SearchFormTypeEnum } from '@guwave/components';
import { getQueryString } from '@guwave/utils';

import { AxiosResponse } from 'axios';
import { ResponseType } from '@/types/common';

import { downloadFile } from '@/utils/download';
import { isNil } from 'lodash';
import { parseTime } from '@/utils/time';

// 根据searchList确定url里的参数
export const getSearchItemUrlParams = (SEARCH_LIST: SearchFormItem[]) => {
  const url = decodeURIComponent(location.href);
  const list = SEARCH_LIST.filter((item) => ![SearchFormTypeEnum.CUSTOM, SearchFormTypeEnum.DATE].includes(item.type));
  const res = list.reduce((acc, item) => {
    const urlString = getQueryString(url, item.key);
    const val = urlString ? urlString.split(',') : undefined;
    return { ...acc, [item.key]: val };
  }, {});
  return res;
};

// 数据质量的好品复测和片内复测的返回数据blob流，从header里读取文件名，并且拼接上时间戳，以xlsx文件下载下的通用方法
// 若不从返回的blob流 的header读取文件名，则需要外部传入fileName
export const downloadExcelFileFromBlobRes = async (
  res: AxiosResponse<ResponseType<any>, any>,
  fileName?: string,
  option?: { needAppendName?: boolean },
) => {
  if (res.data) {
    const needAppendName = isNil(option?.needAppendName) ? true : option?.needAppendName;

    let finalFileName: string = '';
    if (fileName) {
      finalFileName = fileName + (needAppendName ? parseTime(new Date()) + '.xlsx' : '');
    } else {
      const name = res.headers['content-disposition'];
      finalFileName = name ? name.split('attachment;filename=')[1] : parseTime(new Date()) + '.xlsx';
    }

    await downloadFile(res.data as any, finalFileName);
  }
};
