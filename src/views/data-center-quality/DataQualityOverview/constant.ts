import { SearchFormTypeEnum, DATE_SHORTCUTS, SearchFormItem } from '@guwave/components';
import { getQualityOverviewOptions } from '@/service/dataCenterQuality';
import { dayjs } from 'element-plus';
import { getSubCustomer } from '@/service/common';

export const SEARCH_LIST = (): SearchFormItem[] => {
  const list = [
    {
      type: SearchFormTypeEnum.DATE,
      label: '数据入库时间',
      key: 'storageTime',
      nativeAttr: {
        type: 'daterange',
        unlinkPanels: true,
        rangeSeparator: '-',
        startPlaceholder: '开始时间',
        endPlaceholder: '结束时间',
        shortcuts: DATE_SHORTCUTS,
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      label: 'Sub Customer',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'subCustomerList',
      api: getSubCustomer,
      dropdownResFn: (resData) => {
        const options = resData.map((item) => ({
          label: item.subCustomerCode,
          value: item.subCustomerCode,
        }));
        return options;
      },
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Factory',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'factoryList',
      dropdownKey: 'FACTORY',
      api: getQualityOverviewOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Device',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'deviceIdList',
      dropdownKey: 'DEVICE_ID',
      api: getQualityOverviewOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Test Area',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'testArea',
      dropdownKey: 'TEST_AREA',
      api: getQualityOverviewOptions,
      nativeAttr: {
        clearable: false,
      },
    },
    {
      label: 'Test Program',
      type: SearchFormTypeEnum.SELECT_V2,
      key: 'testProgramList',
      dropdownKey: 'TEST_PROGRAM',
      api: getQualityOverviewOptions,
      nativeAttr: {
        multiple: true,
      },
    },
    {
      label: 'Test Stage',
      type: SearchFormTypeEnum.SELECT_V2,
      multiple: true,
      key: 'testStageList',
      dropdownKey: 'TEST_STAGE',
      api: getQualityOverviewOptions,
      nativeAttr: {
        multiple: true,
      },
    },
  ];

  // 重写api
  const newList = list.map((item) => {
    return {
      ...item,
      api: (params) => {
        if (item.key === 'subCustomerList') {
          return item.api(); //subCustomer不需要其他参数
        }
        const newParams = {
          ...params,
          filterField: params.field,
          field: undefined,
          startTime: params?.storageTime ? dayjs(params.storageTime?.[0]).format('YYYY-MM-DD 00:00:00') : undefined,
          endTime: params?.storageTime ? dayjs(params.storageTime?.[1]).format('YYYY-MM-DD 23:59:59') : undefined,
          storageTime: undefined,
        };
        return item.api(newParams);
      },
    };
  });

  return newList;
};
