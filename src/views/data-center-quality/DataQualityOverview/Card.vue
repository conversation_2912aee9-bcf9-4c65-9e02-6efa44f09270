<template>
  <div v-if="data">
    <div class="overview-row">
      <div class="overview-context-item">
        <div class="title">入库产品数</div>
        <div class="card">
          <div class="header">
            <span>累计新增产品数（个）</span>
            <span>{{ thousandthFormatter(data['PRODUCT_COUNT']?.baseIndexValue) }}</span>
          </div>
          <div class="chart-wrap">
            <div class="chart-title">新增产品数 weekly 趋势图</div>
            <div class="chart-context"><Bar :data="data['PRODUCT_COUNT']?.detail" /></div>
          </div>
        </div>
      </div>
      <div class="overview-context-item">
        <div class="title">入库批次数</div>
        <div class="card">
          <div class="header">
            <span>累计入库批次数（个）</span>
            <span>{{ thousandthFormatter(data['BATCH_COUNT']?.baseIndexValue) }}</span>
          </div>
          <div class="chart-wrap">
            <div class="chart-title">入库批次数 weekly 趋势图</div>
            <div class="chart-context"><Bar :data="data['BATCH_COUNT']?.detail" /></div>
          </div>
        </div>
      </div>
      <div class="overview-context-item">
        <div class="title">入库芯片数</div>
        <div class="card">
          <div class="header">
            <span>累计入库芯片数（颗）</span>
            <span>{{ thousandthFormatter(data['CHIP_COUNT']?.baseIndexValue) }}</span>
          </div>
          <div class="chart-wrap">
            <div class="chart-title">入库芯片数 weekly 趋势图</div>
            <div class="chart-context"><Bar :data="data['CHIP_COUNT']?.detail" /></div>
          </div>
        </div>
      </div>
    </div>
    <div class="overview-row">
      <div class="overview-context-item">
        <div class="title">入库耗时</div>
        <div class="card">
          <div class="header">
            <span>文件入库平均耗时（h）</span>
            <span>{{ thousandthFormatter(data['IN_DB_TIME']?.baseIndexValue) }}</span>
          </div>
          <div class="chart-wrap">
            <div class="chart-title">文件入库耗时 weekly 趋势图</div>
            <div class="chart-context"><StackedBar :data="data['IN_DB_TIME']?.detail" /></div>
          </div>
        </div>
      </div>
      <div class="overview-context-item overview-context-item-f_2">
        <div class="title">数据准确性</div>
        <div class="card">
          <div class="header">
            <span>批次数据准确率</span>
            <span>{{ toPercent(data['YIELD_ACCURACY']?.baseIndexValue) }}</span>
          </div>
          <div class="chart-wrap-combination">
            <div class="chart-wrap">
              <div class="chart-title">批次数据准确性分布图</div>
              <div class="chart-context"><Pie :data="data['NORMAL_RATE']?.detail" /></div>
            </div>
            <div class="chart-wrap">
              <div class="chart-title">测试文件数据流与MES数据流数据一致性</div>
              <div class="chart-context"><Pie :data="data['MATCH_RATE']?.detail" /></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Bar from '../components/Bar.vue';
import Pie from '../components/Pie.vue';
import StackedBar from '../components/StackedBar.vue';

import { thousandthFormatter, toPercent } from '@/utils/number';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const props = defineProps<{ data: any }>();
</script>
<style scoped lang="scss">
.overview-row {
  display: flex;
  margin-top: 8px;
  gap: 8px;
  .overview-context-item {
    flex: 1 1;
    font-size: 14px;
    .title {
      font-size: 16px;
      height: 36px;
      line-height: 36px;
      font-weight: 700;
    }
    .card {
      box-shadow: $gBoxShadowLighter;
      border-radius: 4px;
      background: #fff;
      .header {
        border-bottom: 1px solid #d7d8dc;
        height: 48px;
        padding: 0 8px;
        display: flex;
        align-items: center;
        > span:first-child {
          margin-right: 16px;
          color: #565656;
        }
        > span:nth-child(2) {
          font-size: 24px;
          font-weight: 700;
        }
      }
    }
    .chart-wrap {
      height: 270px;
      .chart-title {
        line-height: 28px;
        padding: 0 8px;
        font-size: 12px;
        font-weight: 700;
      }
      .chart-context {
        width: 100%;
        height: 240px;
        position: relative;
      }
    }
    .chart-wrap-combination {
      display: flex;
      > div {
        width: 50%;
      }
      > div:not(last-child) {
        border-right: 1px solid #d7d8dc;
      }
    }
  }
  .overview-context-item-f_2 {
    flex: 2 2;
  }
}
</style>
