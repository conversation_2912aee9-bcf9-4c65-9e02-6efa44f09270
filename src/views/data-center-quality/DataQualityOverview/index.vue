<template>
  <div v-loading="isLoading">
    <PageTemplate>
      <template #header>
        <div>数据质量概览</div>
      </template>
      <template #filterNormal>
        <SearchForm ref="searchFormRef" :searchList="searchList" @search="onSearch" :clearFn="onClear" />
      </template>
      <template #KPICard>
        <Card :data="cardData" />
      </template>
    </PageTemplate>
  </div>
</template>

<script setup lang="ts">
import { SEARCH_LIST } from './constant';
import { getQualityOverviewData } from '@/service/dataCenterQuality';
import { isNil, keyBy } from 'lodash';
import { PageTemplate, SearchFormParam, SearchFormItem, SearchForm } from '@guwave/components';
import { dayjs } from 'element-plus';
import useDashUrlInfoStore from '@/store/system/dashboardUrl';
import Card from './Card.vue';

const dashBoardInfo = useDashUrlInfoStore();
const { factoryMode } = storeToRefs(dashBoardInfo);

const searchFormRef = ref(null);
const searchList = ref<SearchFormItem[]>(SEARCH_LIST());
const defaultSearch = ref();

const { data: overViewDataRes, isLoading, execute: fetchData } = getQualityOverviewData();

const cardData = ref<Record<string, any>>();

const onSearch = (params: SearchFormParam) => {
  const newParams = {
    ...params,
    storageTime: undefined,
    startTime: params?.storageTime ? dayjs(params.storageTime?.[0]).format('YYYY-MM-DD 00:00:00') : undefined,
    endTime: params?.storageTime ? dayjs(params.storageTime?.[1]).format('YYYY-MM-DD 23:59:59') : undefined,
  };
  fetchData({ data: newParams });
};

// 恢复默认值的显示
const onClear = () => {
  searchFormRef.value?.setSearchVal(defaultSearch.value, { searchImmediate: false }); // 不要触发搜索，因为reset会触发一次搜索
};

watch(overViewDataRes, (overViewDataVal) => {
  if (overViewDataVal?.data) {
    cardData.value = keyBy(overViewDataVal.data, 'calculateIndexType');
  }
});

watch(
  factoryMode,
  (factoryMode) => {
    if (isNil(factoryMode)) return;
    // 处理 factoryMode
    if (!factoryMode) {
      searchList.value.forEach((item) => {
        // 只处理subCustomerList，否则会影响之前处理的
        if (item.key === 'subCustomerList') {
          item.hidden = true;
        }
      });
    }
  },
  { deep: true, immediate: true },
);

const init = () => {
  const start = dayjs().subtract(4, 'week').format('YYYY-MM-DD');
  const end = dayjs().format('YYYY-MM-DD');

  defaultSearch.value = {
    testArea: 'CP', // 单选
    storageTime: [start, end], // 四个页面的时间key都是storageTime
  };

  searchFormRef.value?.setSearchVal(defaultSearch.value); // 会触发一次handleSearch
};

onMounted(() => {
  init();
});
</script>

<style scoped lang="scss"></style>
