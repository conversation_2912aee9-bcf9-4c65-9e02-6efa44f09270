import Step1 from './StepOne.vue';
import Step2 from './StepTwo.vue';
import Step3 from './StepThree.vue';
import Step4 from './StepFour.vue';
import { column1, column2, column3, column4 } from './data';

const steps = [Step1, Step2, Step3, Step4];
const columns = [column1, column2, column3, column4];
const indexStep = ref();

export const getStep = (step) => {
  indexStep.value = step;
  return steps[step];
};
export const getColumn = (showDialog?: (val) => void) => columns[indexStep.value](showDialog);
