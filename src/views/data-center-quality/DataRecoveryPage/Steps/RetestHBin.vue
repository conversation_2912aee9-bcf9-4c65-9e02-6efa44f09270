<template>
  <div class="waferId" :class="isError ? 'errorColor' : ''">
    <el-input v-model="input" class="item" @input="change" />
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{ data; change }>();

const input = ref(props.data);
const isError = ref<boolean>(false);

const change = (value) => {
  if (value === 'ALL' || value === 'all') {
    isError.value = false;
  } else if (value[value?.length - 1] === ',') {
    isError.value = true;
  } else if (!value?.split(',')?.some((i) => isNaN(Number(i)))) {
    isError.value = false;
  } else {
    isError.value = true;
  }

  props.change(value);
};
</script>

<style scoped lang="scss">
.waferId {
  display: flex;
  justify-content: space-between;

  .item {
    margin: auto 4px;
  }
}

.errorColor {
  .el-select,
  .el-input,
  .el-input-number {
    :deep(.el-input__inner) {
      color: red;
    }
  }
}
</style>
