<template>
  <div class="waferId" :class="isError ? 'errorColor' : ''">
    <el-select
      v-model="select1"
      size="small"
      @change="(a) => changeFn(a, 'select1')"
      clearable
      :disabled="props.disabled"
      placeholder="请选择"
      :style="{ marginRight: '4px' }"
    >
      <el-option label="P" value="P" />
      <el-option label="R" value="R" />
    </el-select>
    <el-input-number
      :model-value="input1"
      size="small"
      @change="(a) => changeFn(a, 'input1')"
      :controls="false"
      :precision="0"
      :disabled="props?.disabled"
    />
    <span>- I</span>
    <el-input-number
      :model-value="input2"
      size="small"
      @change="(a) => changeFn(a, 'input2')"
      :controls="false"
      :disabled="props?.disabled"
    />
    <span>- R</span>
    <el-input-number
      :model-value="input3"
      size="small"
      @change="(a) => changeFn(a, 'input3')"
      :controls="false"
      :precision="0"
      :disabled="props?.disabled"
    />
  </div>
</template>

<script setup lang="ts">
import { isNil, isEmpty, isNumber } from 'lodash';

const props = defineProps<{ defaultVal?: any; disabled?: boolean }>();

const data = defineModel();
const isError = defineModel('flowIdIsError');

let select1 = ref(undefined);
let input1 = ref(undefined);
let input2 = ref(undefined);
let input3 = ref(undefined);

const strIsNumber = (value) => {
  return isNumber(value) && Number.isFinite(value);
};

const isVaild = (select1, input1, input2, input3) => {
  if (!isNil(select1) && !isEmpty(select1) && !['P', 'R'].includes(select1)) return false;
  if (!isNil(input1) && !strIsNumber(input1)) return false;
  if (!isNil(input2) && !strIsNumber(input2)) return false;
  if (!isNil(input3) && !strIsNumber(input3)) return false;

  const allEmpty = (isNil(select1) || isEmpty(select1)) && isNil(input1) && isNil(input2) && isNil(input3);
  return allEmpty || (!isNil(select1) && !isEmpty(select1) && !isNil(input1));
};

const conbineResString = (select1, input1, input2, input3) => {
  // 此时已经经过验证，所以 select1，input1 必有
  const str1 = select1 + (input1 ?? '');
  const str2 = !isNil(input2) ? `-I${input2}` : ''; // input情空后也是null
  const str3 = !isNil(input3) ? `-R${input3}` : '';
  return str1 + str2 + str3;
};

// 首次值的反显
// 要是有初值，必须设置defaultVal做反显，光设置data不可以，这时data==defaultVal
// 为什么defaultVal和data分开设置：后续会改变data,不想再触发这个watch
watch(
  () => props.defaultVal,
  (defaultVal) => {
    if (isNil(defaultVal) || isEmpty(defaultVal)) return;
    const value = String(defaultVal).split('-');

    const s1 = value[0]?.[0] ?? undefined;
    const i1 = value[0]?.slice(1) ?? undefined;
    const i2 = defaultVal?.[1]?.[0] === 'I' ? defaultVal?.[1]?.slice(1) ?? undefined : undefined;
    const i3 =
      defaultVal?.[1]?.[0] === 'I' ? defaultVal?.[2]?.slice(1) ?? undefined : defaultVal?.[1]?.slice(1) ?? undefined;
    const valid = isVaild(s1, i1, i2, i3);
    // console.log('反显的值----');
    // console.log('defaultVal', defaultVal);
    // console.log('s1',s1);
    // console.log('i1', i1);
    // console.log('i2',i2);
    // console.log('i3', i3);
    // console.log('valid', valid);

    if (valid) {
      select1.value = s1;
      input1.value = i1;
      input2.value = i2;
      input3.value = i2;
    }
  },
  {
    immediate: true,
  },
);

// 要设置change函数，直接watch的话有时number-input不能监听到变化
// 这里更新data
const changeFn = (val, flag) => {
  if (flag === 'input1') input1.value = val;
  if (flag === 'input2') input2.value = val;
  if (flag === 'input3') input3.value = val;
  // console.log('值发生变化----');
  // console.log('select1', select1.value);
  // console.log('input1', input1.value);
  // console.log('input2', input2.value);
  // console.log('input3', input3.value);
  const valid = isVaild(select1.value, input1.value, input2.value, input3.value);
  isError.value = !valid;
  if (valid) {
    data.value = conbineResString(select1.value, input1.value, input2.value, input3.value);
  } else {
    data.value = null;
  }
};
</script>

<style scoped lang="scss">
.waferId {
  display: flex;
  justify-content: space-between;

  > span {
    margin: auto 4px;
    flex-shrink: 0;
  }
  :deep(.el-select) {
    .el-input__wrapper {
      margin-right: 4px;
      width: 70px;
    }
  }
  :deep(.el-input) {
    flex: 1;
  }
  // > .el-input-number {
  //   margin-left: 4px;
  // }

  // .item {
  //   margin: auto 4px;
  // }
}

.errorColor {
  .el-select,
  .el-input-number {
    :deep(.el-input__inner) {
      color: red;
    }
  }
}
</style>
