import {
  CheckboxValueType,
  Column,
  ElButton,
  ElIcon,
  ElInput,
  ElInputNumber,
  ElOption,
  ElSelect,
  ElTooltip,
} from 'element-plus';
import { FunctionalComponent } from 'vue';
import { dataQuality } from '@/store/dataQuality';
import { QuestionFilled } from '@element-plus/icons-vue';
import WaferId from './WaferId.vue';
import RetestHBin from './RetestHBin.vue';
import { $t } from '@/i18n';
import { getColWidth } from '@/utils/common';

const dataQualityStore = dataQuality();

const InputCell: FunctionalComponent<{
  disabled?: boolean;
  value: string;
  onChange?: (value: CheckboxValueType) => void;
  forwardRef?: (value: CheckboxValueType) => void;
  onBlur?: (value: CheckboxValueType) => void;
  onKeydownEnter?: (value: CheckboxValueType) => void;
  isError?: boolean;
}> = ({ value, onChange, forwardRef, disabled, isError }) => {
  return (
    <ElInput
      ref={forwardRef as any}
      onInput={onChange}
      modelValue={value}
      disabled={disabled}
      style={{ color: isError ? 'red' : '#161616' }}
    />
  );
};

export const column1: (showDialog) => Column<any>[] = (showDialog) => [
  {
    key: 'deviceId',
    dataKey: 'deviceId',
    title: $t('common.fields.device'),
    width: getColWidth('deviceId', dataQualityStore.dataRecovery.stepOneData, $t('common.fields.device')) ?? 200,
  },
  {
    key: 'testStage',
    dataKey: 'testStage',
    title: 'Test Stage',
    width: getColWidth('testStage', dataQualityStore.dataRecovery.stepOneData, 'Test Stage') ?? 200,
  },
  {
    key: 'lotId',
    dataKey: 'lotId',
    title: $t('common.fields.lot'),
    width: getColWidth('lotId', dataQualityStore.dataRecovery.stepOneData, $t('common.fields.lot')) ?? 200,
  },
  {
    key: 'sblotId',
    dataKey: 'sblotId',
    title: $t('common.fields.subLot'),
    width: getColWidth('sblotId', dataQualityStore.dataRecovery.stepOneData, $t('common.fields.subLot')) ?? 200,
  },
  {
    key: 'testProgram',
    dataKey: 'testProgram',
    title: 'Test Program',
    width: getColWidth('testProgram', dataQualityStore.dataRecovery.stepOneData, 'Test Program') ?? 200,
  },
  {
    key: 'flowId',
    dataKey: 'flowId',
    title: 'Flow ID',
    width: getColWidth('flowId', dataQualityStore.dataRecovery.stepOneData, 'Flow Id') ?? 200,
  },
  {
    key: 'retestHardBin',
    dataKey: 'retestHardBin',
    title: 'Retest HBIN Number',
    width: getColWidth('retestHardBin', dataQualityStore.dataRecovery.stepOneData, 'Retest HBIN Number') ?? 200,
  },
  {
    key: 'fileName',
    dataKey: 'fileName',
    title: 'STDF File Name',
    width: getColWidth('fileName', dataQualityStore.dataRecovery.stepOneData, 'STDF File Name') ?? 200,
  },
  {
    key: 'startTime',
    dataKey: 'startTime',
    title: 'Start Time',
    width: getColWidth('startTime', dataQualityStore.dataRecovery.stepOneData, 'Start Time') ?? 200,
  },
  {
    key: 'endTime',
    dataKey: 'endTime',
    title: 'Record Time',
    width: getColWidth('endTime', dataQualityStore.dataRecovery.stepOneData, 'Record Time') ?? 200,
  },
  {
    key: 'inputCount',
    dataKey: 'inputCount',
    title: 'Input Count',
    width: getColWidth('inputCount', dataQualityStore.dataRecovery.stepOneData, 'Input Count') ?? 200,
  },
  {
    key: 'passCount',
    dataKey: 'passCount',
    title: 'Pass Count',
    width: getColWidth('passCount', dataQualityStore.dataRecovery.stepOneData, 'Pass Count') ?? 200,
  },
  {
    key: 'failCount',
    dataKey: 'failCount',
    title: 'Fail Count',
    width: getColWidth('failCount', dataQualityStore.dataRecovery.stepOneData, 'Fail Count') ?? 200,
  },
  {
    key: 'isRepaired',
    dataKey: 'isRepaired',
    title: 'Is Repaired?',
    width: getColWidth('isRepaired', dataQualityStore.dataRecovery.stepOneData, 'Is Repaired') ?? 200,
    cellRenderer: ({ rowData }) => {
      return <span>{rowData.isRepaired ? 'True' : 'False'}</span>;
    },
  },
  {
    key: 'repairTimes',
    dataKey: 'repairTimes',
    title: 'Repair Times',
    width: getColWidth('repairTime', dataQualityStore.dataRecovery.stepOneData, 'Repair Times') ?? 200,
  },
  {
    key: 'repairLog',
    dataKey: 'repairLog',
    title: 'Repair Log',
    width: 200,
    cellRenderer: ({ rowData }) => {
      return (
        <ElButton size="small" type="text" disabled={rowData?.repairTimes < 1} onClick={() => showDialog(rowData)}>
          查看
        </ElButton>
      );
    },
  },
];
export const column2: () => Column<any>[] = () => {
  const getWidth = ref(
    getColWidth('fieldShowName', dataQualityStore.dataRecovery.stepTwoData, '字段名称') +
      getColWidth('fileName', dataQualityStore.dataRecovery.stepTwoData, '文件名称') +
      getColWidth('originalFieldValue', dataQualityStore.dataRecovery.stepTwoData, '原始值'),
  );

  return [
    {
      key: 'fileName',
      dataKey: 'fileName',
      title: '文件名称',
      width: getColWidth('fileName', dataQualityStore.dataRecovery.stepTwoData, '文件名称') ?? 200,
    },
    {
      key: 'fieldShowName',
      dataKey: 'fieldShowName',
      title: '字段名称',
      width: getColWidth('fieldShowName', dataQualityStore.dataRecovery.stepTwoData, '字段名称') ?? 200,
    },
    {
      key: 'originalFieldValue',
      dataKey: 'originalFieldValue',
      title: '原始值',
      width: getColWidth('originalFieldValue', dataQualityStore.dataRecovery.stepTwoData, '原始值') ?? 200,
    },
    {
      key: 'modifyValue',
      dataKey: 'modifyValue',
      title: '修改值',
      width:
        dataQualityStore.dataRecovery.width > getWidth.value * 1.2
          ? (dataQualityStore.dataRecovery.width - getWidth.value) / 2
          : 200,
      cellRenderer: ({ rowData, column }) => {
        const onChange = (value: string | number) => {
          rowData[column.dataKey!] = value;
        };

        if (rowData?.field === 'WAFER_NO') {
          return (
            <>
              <ElInputNumber
                controls={false}
                min={1}
                max={100}
                precision={0}
                modelValue={rowData[column.dataKey!]}
                onChange={onChange}
              />
            </>
          );
        } else if (rowData?.field === 'LOT_TYPE') {
          return (
            <>
              <ElSelect modelValue={rowData[column.dataKey!]} onChange={onChange}>
                <ElOption label={'请选择'} value={null} />
                <ElOption label={'Production'} value={'PRODUCTION'} />
                <ElOption label={'Engineering'} value={'ENGINEERING'} />
              </ElSelect>
            </>
          );
        } else {
          return (
            <>
              <InputCell value={rowData[column.dataKey!]} onChange={onChange} />
            </>
          );
        }
      },
      headerCellRenderer: () => {
        return (
          <>
            <span>修改值</span>
          </>
        );
      },
    },
    {
      key: 'modifyRemark',
      dataKey: 'modifyRemark',
      title: '修改原因',
      width:
        dataQualityStore.dataRecovery.width > getWidth.value * 1.2
          ? (dataQualityStore.dataRecovery.width - getWidth.value) / 2
          : 200,
      cellRenderer: ({ rowData, column }) => {
        const onChange = (value: string) => {
          rowData[column.dataKey!] = value;
        };

        return <InputCell value={rowData[column.dataKey!]} onChange={onChange} />;
      },
    },
  ];
};
export const column3: () => Column<any>[] = () => {
  const getWidth = ref(
    getColWidth('deviceId', dataQualityStore.dataRecovery.stepThreeData, $t('common.fields.device')) +
      getColWidth('testStage', dataQualityStore.dataRecovery.stepThreeData, 'TestStage') +
      getColWidth('lotId', dataQualityStore.dataRecovery.stepThreeData, $t('common.fields.lot')) +
      getColWidth('sblotId', dataQualityStore.dataRecovery.stepThreeData, $t('common.fields.subLot')) +
      getColWidth('testProgram', dataQualityStore.dataRecovery.stepThreeData, 'Test Program') +
      getColWidth('fileName', dataQualityStore.dataRecovery.stepThreeData, 'File Name'),
  );

  return [
    {
      key: 'fileName',
      dataKey: 'fileName',
      title: 'File Name',
      width: getColWidth('fileName', dataQualityStore.dataRecovery.stepThreeData, 'File Name') ?? 200,
    },
    {
      key: 'deviceId',
      dataKey: 'deviceId',
      title: $t('common.fields.device'),
      width: getColWidth('deviceId', dataQualityStore.dataRecovery.stepThreeData, $t('common.fields.device')) ?? 160,
    },
    {
      key: 'testStage',
      dataKey: 'testStage',
      title: 'Test Stage',
      width: getColWidth('testStage', dataQualityStore.dataRecovery.stepThreeData, 'TestStage') ?? 160,
    },
    {
      key: 'lotId',
      dataKey: 'lotId',
      title: $t('common.fields.lot'),
      width: getColWidth('lotId', dataQualityStore.dataRecovery.stepThreeData, $t('common.fields.lot')) ?? 160,
    },
    {
      key: 'sblotId',
      dataKey: 'sblotId',
      title: $t('common.fields.subLot'),
      width: getColWidth('sblotId', dataQualityStore.dataRecovery.stepThreeData, $t('common.fields.subLot')) ?? 160,
    },
    {
      key: 'testProgram',
      dataKey: 'testProgram',
      title: 'Test Program',
      width: getColWidth('testProgram', dataQualityStore.dataRecovery.stepThreeData, 'Test Program') ?? 200,
    },
    {
      key: 'flowId',
      dataKey: 'flowId',
      title: 'Flow ID',
      width: 320,
      headerCellRenderer: () => {
        return (
          <>
            <span>Flow ID</span>
          </>
        );
      },
      cellRenderer: ({ rowData, column }) => {
        const change = (val) => {
          if (val[0] === 'P') {
            rowData['retestHardBin'] = 'NA';
          } else {
            rowData['retestHardBin'] = 'ALL';
          }

          rowData[column.dataKey!] = val;
        };

        return rowData['repairFile'] ? (
          <>{<WaferId data={rowData['flowId']} change={change} />}</>
        ) : (
          <>{rowData[column.dataKey!]}</>
        );
      },
    },
    {
      key: 'retestHardBin',
      dataKey: 'retestHardBin',
      title: 'Retest HBin Number',
      width:
        dataQualityStore.dataRecovery.width > getWidth.value
          ? (dataQualityStore.dataRecovery.width - getWidth.value) / 2
          : 200,
      headerCellRenderer: () => {
        return (
          <>
            <span>Retest HBin Number</span>
            <ElTooltip class="box-item" placement="bottom" content={'请输入"ALL"或者数字,数字","隔开'}>
              {{
                default: () => (
                  <ElIcon style={{ marginLeft: '3px' }}>
                    <QuestionFilled />
                  </ElIcon>
                ),
              }}
            </ElTooltip>
          </>
        );
      },
      cellRenderer: ({ rowData, column }) => {
        const onChange = (value: string) => {
          rowData[column.dataKey!] = value;
        };
        return rowData['repairFile'] && rowData['flowId'][0] === 'R' ? (
          <>
            <RetestHBin data={rowData[column.dataKey!]} change={onChange} />
          </>
        ) : (
          <>{rowData[column.dataKey!]}</>
        );
      },
    },
  ];
};
export const column4: () => Column<any>[] = () => {
  const getWidth = ref(
    getColWidth('deviceId', dataQualityStore.dataRecovery.stepFourData, $t('common.fields.device')) +
      getColWidth('testStage', dataQualityStore.dataRecovery.stepFourData, 'Test Stage') +
      getColWidth('lotId', dataQualityStore.dataRecovery.stepFourData, $t('common.fields.lot')) +
      getColWidth('sblotId', dataQualityStore.dataRecovery.stepFourData, $t('common.fields.subLot')) +
      getColWidth('testProgram', dataQualityStore.dataRecovery.stepFourData, 'Test Program') +
      getColWidth('lotType', dataQualityStore.dataRecovery.stepFourData, 'Lot Type') +
      getColWidth('inputCnt', dataQualityStore.dataRecovery.stepFourData, 'Input Count') +
      getColWidth('finalGoodCnt', dataQualityStore.dataRecovery.stepFourData, 'Pass Count') +
      getColWidth('finalFailCnt', dataQualityStore.dataRecovery.stepFourData, 'Fail Count') +
      getColWidth('firstYield', dataQualityStore.dataRecovery.stepFourData, 'First Yield') +
      getColWidth('finalYield', dataQualityStore.dataRecovery.stepFourData, 'Final Yield') +
      getColWidth('dataAccuracyCheck', dataQualityStore.dataRecovery.stepFourData, 'Data Accuracy Check'),
  );

  return [
    {
      key: 'deviceId',
      dataKey: 'deviceId',
      title: $t('common.fields.device'),
      width: getColWidth('deviceId', dataQualityStore.dataRecovery.stepFourData, $t('common.fields.device')) ?? 200,
    },
    {
      key: 'testStage',
      dataKey: 'testStage',
      title: 'Test Stage',
      width: getColWidth('testStage', dataQualityStore.dataRecovery.stepFourData, 'Test Stage') ?? 200,
    },
    {
      key: 'lotId',
      dataKey: 'lotId',
      title: $t('common.fields.lot'),
      width: getColWidth('lotId', dataQualityStore.dataRecovery.stepFourData, $t('common.fields.lot')) ?? 200,
    },
    {
      key: 'sblotId',
      dataKey: 'sblotId',
      title: $t('common.fields.subLot'),
      width: getColWidth('sblotId', dataQualityStore.dataRecovery.stepFourData, $t('common.fields.subLot')) ?? 200,
    },
    {
      key: 'testProgram',
      dataKey: 'testProgram',
      title: 'Test Program',
      width: getColWidth('testProgram', dataQualityStore.dataRecovery.stepFourData, 'Test Program') ?? 200,
    },
    {
      key: 'lotType',
      dataKey: 'lotType',
      title: 'Lot Type',
      width: getColWidth('lotType', dataQualityStore.dataRecovery.stepFourData, 'Lot Type') ?? 200,
    },
    {
      key: 'inputCnt',
      dataKey: 'inputCnt',
      title: 'Input Count',
      width: getColWidth('inputCnt', dataQualityStore.dataRecovery.stepFourData, 'Input Count') ?? 200,
    },
    {
      key: 'finalGoodCnt',
      dataKey: 'finalGoodCnt',
      title: 'Pass Count',
      width: getColWidth('finalGoodCnt', dataQualityStore.dataRecovery.stepFourData, 'Pass Count') ?? 200,
    },
    {
      key: 'finalFailCnt',
      dataKey: 'finalFailCnt',
      title: 'Fail Count',
      width: getColWidth('finalFailCnt', dataQualityStore.dataRecovery.stepFourData, 'Fail Count') ?? 200,
    },
    {
      key: 'firstYield',
      dataKey: 'firstYield',
      title: 'First Yield',
      width: getColWidth('firstYield', dataQualityStore.dataRecovery.stepFourData, 'First Yield') ?? 200,

      cellRenderer: ({ rowData }) => {
        return <>{rowData?.firstYieldShowName}</>;
      },
    },
    {
      key: 'finalYield',
      dataKey: 'finalYield',
      title: 'Final Yield',
      width: getColWidth('finalYield', dataQualityStore.dataRecovery.stepFourData, 'Final Yield') ?? 200,

      cellRenderer: ({ rowData }) => {
        return <>{rowData?.finalYieldShowName}</>;
      },
    },
    {
      key: 'dataAccuracyCheck',
      dataKey: 'dataAccuracyCheck',
      title: 'Data Accuracy Check',
      width: getColWidth('dataAccuracyCheck', dataQualityStore.dataRecovery.stepFourData, 'Data Accuracy Check'),

      cellRenderer: ({ rowData }) => {
        return <>{rowData?.dataAccuracyCheckShowName}</>;
      },
    },
    {
      key: 'stdfFileList',
      dataKey: 'stdfFileList',
      title: 'STDF File List',
      width:
        dataQualityStore.dataRecovery.width - getWidth.value >
        getColWidth('stdfFileListStrOne', dataQualityStore.dataRecovery.stepFourData, 'STDF File List')
          ? dataQualityStore.dataRecovery.width - getWidth.value
          : getColWidth('stdfFileListStrOne', dataQualityStore.dataRecovery.stepFourData, 'STDF File List') ?? 200,

      cellRenderer: ({ rowData }) => {
        return (
          <ElTooltip placement="bottom" content={rowData.stdfFileListStr}>
            {{
              default: () => (
                <>
                  {rowData.fileNames?.length > 1
                    ? rowData.stdfFileListStr?.split(',')[0] + '...'
                    : rowData.stdfFileListStr?.split(',')[0]}
                </>
              ),
              content: () => (
                <div>
                  {rowData.fileNames?.map((item) => (
                    <p>{item}</p>
                  ))}
                </div>
              ),
            }}
          </ElTooltip>
        );
      },
    },
  ];
};
