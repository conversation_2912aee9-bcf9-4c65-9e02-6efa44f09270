<template>
  <div class="waferId" :class="isError ? 'errorColor' : ''">
    <el-select
      v-model="select1"
      @change="(a) => changeFn(a, 'select1')"
      clearable
      :disabled="props.disabled"
      placeholder="请选择"
      size="small"
    >
      <el-option label="P" value="P" />
      <el-option label="R" value="R" />
    </el-select>
    <el-input-number
      :model-value="input1"
      @change="(a) => changeFn(a, 'input1')"
      :controls="false"
      :precision="0"
      :min="1"
      :disabled="props?.disabled"
    />
    <span>- I</span>
    <el-input-number
      :model-value="input2"
      @change="(a) => changeFn(a, 'input2')"
      :controls="false"
      :disabled="props?.disabled"
    />
    <span>- R</span>
    <el-input-number
      :model-value="input3"
      @change="(a) => changeFn(a, 'input3')"
      :controls="false"
      :precision="0"
      :disabled="props?.disabled"
    />
  </div>
</template>

<script setup lang="ts">
import { isNil } from 'lodash';

const props = defineProps<{ data; change; disabled?: boolean }>();

const isError = ref<boolean>(false);
let select1 = ref(null); // 清空了是''
let input1 = ref(null); // 清空了是null
let input2 = ref(null);
let input3 = ref(null);

const splitFn = (str: string | null) => {
  const res = {
    hasError: false,
    select1: null,
    input1: null,
    input2: null,
    input3: null,
  };

  const strList = str?.split('-') ?? [];

  if (strList.length > 3) {
    res.hasError = true;
    return res;
  }
  const str1 = strList?.[0] ?? '';
  const str2 = strList?.[1] ?? '';
  const str3 = strList?.[2] ?? '';
  // 验证第一组
  if (str1.length) {
    const regex = /^[PR]\d+$/i;
    if (regex.test(str1)) {
      res.select1 = str1[0].toUpperCase();
      res.input1 = Number(str1.slice(1));
    } else {
      res.hasError = true;
      return res;
    }
  }
  // 验证第二组
  if (str2.length) {
    if (strList.length === 3) {
      const regex = /^I\d*$/i; // 有3组，那第二组一定是I
      if (regex.test(str2)) {
        res.input2 = Number(str2.slice(1));
      } else {
        res.hasError = true;
        return res;
      }
    } else if (strList.length === 2) {
      const regex = /^[IR]\d*$/i; // 有2组，那第二组可能是 I/R
      if (regex.test(str2)) {
        if (str2[0].toUpperCase() === 'I') {
          res.input2 = Number(str2.slice(1));
        } else {
          res.input3 = Number(str2.slice(1));
        }
      } else {
        res.hasError = true;
        return res;
      }
    }
  }
  // 验证第三组
  if (str3.length) {
    const regex = /^[R]\d+$/i;
    if (regex.test(str3)) {
      res.input3 = Number(str3.slice(1));
    } else {
      res.hasError = true;
      return res;
    }
  }

  return res;
};
// 反显
watch(
  () => props.data,
  (newVal) => {
    // console.log('newVal', newVal);
    // console.log('isError.value', isError.value);
    const { hasError, select1: s1, input1: i1, input2: i2, input3: i3 } = splitFn(newVal);

    if (isError.value || hasError) return;

    select1.value = s1;
    input1.value = i1;
    input2.value = i2;
    input3.value = i3;

    // console.log('select1.value', select1.value);
    // console.log('input1.value', input1.value);
    // console.log('input2.value', input2.value);
    // console.log('input3.value', input3.value);
  },
  {
    immediate: true,
  },
);

// 设置
const changeFn = (val, flag) => {
  if (flag === 'select1') select1.value = val === '' ? null : val;
  if (flag === 'input1') input1.value = val;
  if (flag === 'input2') input2.value = val;
  if (flag === 'input3') input3.value = val;

  // console.log('select1.value', select1.value);
  // console.log('input1.value', input1.value);
  // console.log('input2.value', input2.value);
  // console.log('input3.value', input3.value);

  const allEmpty = isNil(select1.value) && isNil(input1.value) && isNil(input2.value) && isNil(input3.value);

  // 不是全空的时候 ，select1 input1 必须要有
  isError.value = !allEmpty && (isNil(select1.value) || isNil(input1.value)); // 外部会根据isError判断是否使用新值

  const str1 = (select1.value ?? '') + (input1.value ?? '');
  const str2 = !isNil(input2.value) ? `-I${input2.value}` : ''; // input情空后也是null
  const str3 = !isNil(input3.value) ? `-R${input3.value}` : '';
  // console.log(str1 + str2 + str3);
  props.change(str1 + str2 + str3, isError.value);
};
</script>

<style scoped lang="scss">
.waferId {
  display: flex;
  justify-content: space-between;

  > span {
    margin: auto 4px;
    flex-shrink: 0;
  }
  :deep(.el-select) {
    .el-input__wrapper {
      margin-right: 4px;
      width: 70px;
    }
  }
  :deep(.el-input) {
    flex: 1;
  }
  // > .el-input-number {
  //   margin-left: 4px;
  // }

  // .item {
  //   margin: auto 4px;
  // }
}

.errorColor {
  .el-select,
  .el-input-number {
    :deep(.el-input__inner) {
      color: red;
    }
  }
}
</style>
