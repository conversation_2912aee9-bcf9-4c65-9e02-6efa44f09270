<template>
  <div class="main_view" ref="view" v-loading="dataQualityStore.dataRecovery.tableLoading">
    <div class="head">目标文件关键字段修复</div>
    <PublicTable
      v-if="!dataQualityStore.dataRecovery.tableLoading"
      :columns="getColumn()"
      :data="dataQualityStore.dataRecovery.stepTwoData ?? []"
      :width="dataQualityStore.dataRecovery.width"
      :height="dataQualityStore.dataRecovery.height - 35"
    />
  </div>
</template>

<script setup lang="ts">
import { getColumn } from './index';
import { PublicTable } from '@/components/PublicTable/index';
import { dataQuality } from '@/store/dataQuality';

const dataQualityStore = dataQuality();
</script>
