<template>
  <div class="main_view" ref="view" v-loading="dataQualityStore.dataRecovery.tableLoading">
    <div class="head">目标文件测试顺序修复（所有相关文件已展示）</div>

    <PublicTable
      v-if="!dataQualityStore.dataRecovery.tableLoading"
      :columns="columns()"
      :data="dataQualityStore.dataRecovery.stepThreeData ?? []"
      :width="dataQualityStore.dataRecovery.width"
      :height="dataQualityStore.dataRecovery.height - 35"
    />
  </div>
</template>

<script setup lang="ts">
import { getColumn } from './index';
import { PublicTable } from '@/components/PublicTable/index';
import { dataQuality } from '@/store/dataQuality';
import { $t } from '@/i18n';
import { getColWidth } from '@/utils/common';

const dataQualityStore = dataQuality();

const columns = () => {
  const col = getColumn();
  const testArea = dataQualityStore.dataRecovery.stepOneData[0]?.testArea;

  if (testArea === 'CP') {
    col.splice(4, 1, {
      key: 'waferId',
      dataKey: 'waferId',
      title: $t('common.fields.wafer'),
      width: getColWidth('waferId', dataQualityStore.dataRecovery.stepOneData, $t('common.fields.wafer')) ?? 200,
    });
  }

  return col;
};
</script>
