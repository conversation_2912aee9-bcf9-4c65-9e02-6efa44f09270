<template>
  <div class="main_view" ref="view">
    <div class="head">原始文件列表 （含数据修复记录）</div>
    <PublicTable
      :columns="columns()"
      :data="dataQualityStore.dataRecovery.stepOneData ?? []"
      :width="dataQualityStore.dataRecovery.width"
      :height="dataQualityStore.dataRecovery.height - 35"
      :isSelect="true"
    />
    <RepairDialog v-if="visible" v-model:visible="visible" :fileId="fileId" />
  </div>
</template>

<script setup lang="ts">
import { getColumn } from './index';
import { PublicTable } from '@/components/PublicTable/index';
import { dataQuality } from '@/store/dataQuality';
import RepairDialog from '@/views/data-center-quality/DataRecovery/components/RepairDialog.vue';

import { $t } from '@/i18n';
import { getColWidth } from '@/utils/common';

const dataQualityStore = dataQuality();

const fileId = ref<number>(undefined);
const visible = ref<boolean>(false);

const showDialog = (rowData) => {
  fileId.value = rowData.fileId;
  visible.value = true;
};

const columns = () => {
  const col = getColumn(showDialog);
  const testArea = dataQualityStore.dataRecovery.stepOneData[0]?.testArea;

  if (testArea === 'CP') {
    col.splice(3, 1, {
      key: 'waferId',
      dataKey: 'waferId',
      title: $t('common.fields.wafer'),
      width: getColWidth('waferId', dataQualityStore.dataRecovery.stepOneData, $t('common.fields.wafer')) ?? 200,
    });
  }

  return col;
};
</script>
