<template>
  <div class="main_view" ref="view">
    <div class="head">
      修复后 {{ $t('common.fields.device') }}, Test Stage, Test Program, {{ $t('common.fields.lot') }} &
      {{ $t('common.fields.subLot') }} 维度良率展示
    </div>
    <PublicTable
      :columns="columns()"
      :data="dataQualityStore.dataRecovery.stepFourData ?? []"
      :width="dataQualityStore.dataRecovery.width"
      :height="dataQualityStore.dataRecovery.height - 35"
    />
  </div>
</template>

<script setup lang="ts">
import { getColumn } from './index';
import { PublicTable } from '@/components/PublicTable/index';
import { dataQuality } from '@/store/dataQuality';
import { $t } from '@/i18n';
import { getColWidth } from '@/utils/common';

const dataQualityStore = dataQuality();

const columns = () => {
  const col = getColumn();
  const testArea = dataQualityStore.dataRecovery.stepOneData[0]?.testArea;

  if (testArea === 'CP') {
    col.splice(3, 1, {
      key: 'waferId',
      dataKey: 'waferId',
      title: $t('common.fields.wafer'),
      width: getColWidth('waferId', dataQualityStore.dataRecovery.stepOneData, $t('common.fields.wafer')) ?? 200,
    });
  }

  return col;
};
</script>
