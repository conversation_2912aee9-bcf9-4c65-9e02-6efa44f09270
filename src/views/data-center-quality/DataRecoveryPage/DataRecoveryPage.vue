<template>
  <div class="dataRecovery" v-loading="dataQualityStore.dataRecovery.resultLoading">
    <div class="header">
      <span>数据修复</span>
      <span>{{ head }} - 数据准确性</span>
    </div>

    <div class="step">
      <el-steps :active="stepFlag" align-center finish-status="success">
        <el-step title="确认文件" />
        <el-step title="修复测试信息" />
        <el-step title="修复测试顺序信息" />
        <el-step title="预览" />
      </el-steps>
    </div>

    <component :is="getStep(stepFlag)" class="view" id="view" ref="view" />

    <div class="bottom">
      <el-button v-if="stepFlag === 3" type="primary" size="default" @click="dataQualityStore.stepSaveFn()"
        >进入修复</el-button
      >
      <el-button v-if="stepFlag != 0" size="default" @click="back()">上一步</el-button>
      <el-button v-if="stepFlag != 3" type="primary" size="default" @click="next[stepFlag]()">下一步</el-button>
      <el-button v-if="stepFlag != 3 && stepFlag != 0" size="default" @click="reset[stepFlag]">重置</el-button>
    </div>

    <el-dialog
      v-model="dataQualityStore.dataRecovery.resultDialog"
      title="数据修复结果"
      width="440px"
      class="resultDialog"
    >
      <RepairResult />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { getStep } from './Steps/index';
import { dataQuality } from '@/store/dataQuality';

import { ElMessage } from 'element-plus';
import { isEmpty, isNil } from 'lodash';
import RepairResult from '../RepairResult/RepairResult.vue';
import { getUrlParams } from '@/utils/common';

const view = ref(null);
const stepFlag = ref(0);
const head = ref('');
const fileIds = ref<number[]>([]);

const dataQualityStore = dataQuality();

const checkFn = (value) => {
  if (value === 'ALL' || value === 'all' || value === 'NA') {
    return false;
  } else if (!value?.split(',')?.some((i) => isNaN(Number(i)))) {
    return false;
  } else {
    return true;
  }
};

// 上一步
const back = () => {
  if (stepFlag.value === 0) fileIds.value = [];
  stepFlag.value--;
};

// 下一步
const next = {
  // 第二步
  0: async () => {
    dataQualityStore.dataRecovery.stepOneData.map((item) => {
      if (item?.checked) {
        fileIds.value.push(item.fileId);
      }
    });
    if (fileIds.value.length < 1) {
      ElMessage.warning('请勾选!');
    } else {
      const res = dataQualityStore.getStepTwo(fileIds.value);
      if (res) stepFlag.value++;
    }
  },

  // 第三步
  1: async () => {
    const checkStageData = [];
    const checkDeviceData = [];
    const checkLotTypeData = [];

    for (let i = 0; i < dataQualityStore.dataRecovery.stepTwoData.length; i++) {
      const item = dataQualityStore.dataRecovery.stepTwoData[i];

      if (item.modifyValue == item.originalFieldValue) {
        ElMessage.warning('修改值不能与原始值相同!');
        return;
      }

      if (item.field === 'TEST_STAGE') {
        checkStageData.push(item.modifyValue);
      }
      if (item.field === 'DEVICE_ID') {
        checkDeviceData.push(item.modifyValue);
      }
      if (item.field === 'LOT_TYPE') {
        checkLotTypeData.push(item.modifyValue);
      }

      if (item.field === 'WAFER_NO') {
        if (!isNil(item?.modifyValue) && (item?.modifyRemark?.length < 2 || isEmpty(item?.modifyRemark))) {
          ElMessage.warning('请填写修改原因且不少于两个字符！');
          return;
        }
      }
      if (!isEmpty(item?.modifyValue) && (item?.modifyRemark?.length < 2 || isEmpty(item?.modifyRemark))) {
        ElMessage.warning('请填写修改原因且不少于两个字符！');
        return;
      }
    }

    const res = await dataQualityStore.getStepThree();
    if (res) stepFlag.value++;
  },

  // 第四步
  2: async () => {
    const twoFlag = dataQualityStore.dataRecovery.stepTwoData.every((item) => isNil(item.modifyValue));
    const threeFlag = dataQualityStore.dataRecovery.stepThreeData?.every(
      (item, index) =>
        item.flowId === dataQualityStore.dataRecovery.originStepThreeData[index].flowId &&
        item.retestHardBin === dataQualityStore.dataRecovery.originStepThreeData[index].retestHardBin,
    );

    if (twoFlag && threeFlag) {
      ElMessage.warning('存在未变更数据的文件！');
      return;
    }

    if (dataQualityStore.dataRecovery.stepThreeData?.some((item) => checkFn(item?.retestHardBin))) {
      ElMessage.warning('Retest HBin Number有误!');
      return;
    }

    if (
      dataQualityStore.dataRecovery.stepThreeData?.some((i) => {
        return !i.flowId?.split('-')[0]?.slice(1);
      })
    ) {
      ElMessage.warning('请输入合理Flow ID!');
      return;
    }

    const res = await dataQualityStore.getStepFour();
    if (res) stepFlag.value++;
  },
};

//  重置
const reset = {
  1: () => {
    dataQualityStore.changeTableLoading();
    dataQualityStore.getStepTwo(fileIds.value);
    setTimeout(() => {
      dataQualityStore.changeTableLoading();
    }, 800);
  },
  2: async () => {
    dataQualityStore.changeTableLoading();
    dataQualityStore.getStepThree();

    setTimeout(() => {
      dataQualityStore.changeTableLoading();
    }, 800);
  },
};

onMounted(() => {
  dataQualityStore.setHeight(document.getElementById('view').offsetHeight);
  dataQualityStore.setWidth(document.getElementById('view').offsetWidth);

  window.onresize = () => {
    dataQualityStore.setHeight(document.getElementById('view').offsetHeight);
    dataQualityStore.setWidth(document.getElementById('view').offsetWidth);
  };

  if (dataQualityStore.dataRecovery.fileIds?.length > 0 && dataQualityStore.dataRecovery.title) {
    dataQualityStore.getStepOne(dataQualityStore.dataRecovery.fileIds);
    return;
  }

  // const decodeHref = decodeURI(location.href);

  // const fileIds = getQueryString(decodeHref, 'fileIds');
  // const title = getQueryString(decodeHref, 'title');

  const searchParams = getUrlParams();
  const { fileIds, title } = searchParams;

  if (fileIds) {
    dataQualityStore.setFileIds(fileIds);
    dataQualityStore.getStepOne(fileIds);
  }
  if (title) {
    dataQualityStore.setTitle(title);
    head.value = title as string;
  }
});
</script>

<style scoped lang="scss">
.dataRecovery {
  padding: 16px;
  display: flex;
  height: 100%;
  flex-direction: column;

  .header {
    > span:first-child {
      font-size: 18px;
      color: #161616;
      font-weight: bold;
    }
    > span:last-child {
      font-size: 18px;
      color: #161616;
      margin-left: 8px;
    }
  }

  .step {
    width: 100%;
    margin: 20px auto 15px;
    display: flex;

    .el-steps {
      width: 75%;
      margin: auto;

      .el-step {
        margin: auto;

        :deep(.el-step__head) {
          .el-step__line {
            background: #dbdbdb;
          }
          .el-step__icon {
            width: 30px;
            height: 30px;
            border: none;
            color: #565656;
            font-size: 14px;
            background: #dbdbdb;
            border: 1px solid #c6c6c6;
          }
        }
        :deep(.is-process) {
          .el-step__icon {
            color: #fff;
            background: #5d2dcd;
          }
        }
        :deep(.is-success) {
          .el-step__line {
            background: #109e5c;
          }
          .el-step__icon {
            color: #fff;
            background: #109e5c;
          }
        }
        :deep(.el-step__main) {
          .el-step__title {
            color: #161616;
            font-size: 14px;
          }
          .is-process {
            font-weight: normal;
          }
        }
      }
    }
  }

  .view {
    flex: 1;
    margin-bottom: 40px;
  }

  .main_view {
    display: flex;
    flex-direction: column;

    :deep(.head) {
      font-weight: bold;
      margin-bottom: 8px;
    }
  }

  .bottom {
    left: 0;
    bottom: 0;
    width: 100%;
    height: 48px;
    display: flex;
    position: fixed;
    background: #fff;
    border-top: 1px solid rgba(215, 216, 220, 1);

    .el-button {
      margin: auto 0;
    }

    .el-button:first-child {
      margin-left: auto;
    }
    .el-button:nth-child(2) {
      margin: auto 10px;
    }
    .el-button:last-child {
      margin-right: auto;
    }
  }

  :deep(.el-input-number) {
    .el-input__inner {
      text-align: left;
    }
  }

  :deep(.resultDialog) {
    .el-dialog__body {
      padding: 24px 0px 0;
    }
  }
}
</style>
