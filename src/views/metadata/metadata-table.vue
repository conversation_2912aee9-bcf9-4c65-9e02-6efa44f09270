<template>
  <div class="metadataTable v">
    <div class="tableHead h">
      <div class="btns">
        <n-button type="primary" @click="getList(true)" v-if="props.baseForm.datasource !== DatabaseEnum.MYSQL">
          从表重新解析
        </n-button>
        <n-button type="error" @click="batchFn()">批量删除</n-button>
      </div>
    </div>

    <n-data-table
      :loading="table.pagination.loading"
      :columns="COLUMNS(deleteFieldApi)"
      :data="table.data"
      :scroll-x="1640"
      :max-height="'calc(100vh - 550px)'"
      :row-key="(row:FieldItem) => row.id"
      :checked-row-keys="selectedIds"
      @update:checked-row-keys="(ids:number[])=>selectedIds = ids"
    />
    <n-pagination
      :showSizePicker="true"
      :pageSizes="[10, 20, 30, 40, 50]"
      v-model:page="table.pagination.pageIndex"
      v-model:pageSize="table.pagination.pageSize"
      :item-count="table.pagination.total"
      :prefix="table.pagination.prefix"
      @update:page="table.pagination.onChange"
      @update:pageSize="table.pagination.onUpdatePageSize"
    />
  </div>
</template>

<script setup lang="ts">
import { DeleteFieldParam, DescriptionItem, FieldItem, FieldListParam } from '@/types/metadata';
import { COLUMNS, DatabaseEnum } from './data';
import { PaginationInfo, useMessage } from 'naive-ui';
import { AddNull } from '@/types/common';
import { getSchema, reloadSchema, deleteField } from '@/service/metadata';
import { SUCCESS_CODE } from '@/utils/http';
import { cloneDeep, debounce } from 'lodash';

const props = defineProps<{ baseForm: AddNull<DescriptionItem>; tableFlag: boolean; changeTableFlag: () => void }>();

const message = useMessage();
const table = reactive<{ data: FieldItem[]; pagination: any }>({
  data: [],
  pagination: {
    total: 0,
    pageSize: 20,
    pageIndex: 1,
    loading: false,
    prefix: (info: PaginationInfo) => {
      return `共${info.itemCount}条`;
    },
    onChange: (page: number) => {
      table.pagination.pageIndex = page;
      getList();
    },
    onUpdatePageSize: (pageSize: number) => {
      table.pagination.pageIndex = 1;
      table.pagination.pageSize = pageSize;
      getList();
    },
  },
});
const selectedIds = ref<number[]>([]);

const getList = async (isReload?: boolean) => {
  if (props.baseForm.datasource && props.baseForm.databaseName && props.baseForm.tableName) {
    const param: FieldListParam = {
      datasource: props.baseForm.datasource,
      databaseName: props.baseForm.databaseName,
      tableName: props.baseForm.tableName,
      pageIndex: table.pagination.pageIndex,
      pageSize: table.pagination.pageSize,
    };

    table.pagination.loading = true;
    const res = isReload ? await reloadSchema(param) : await getSchema(param);
    if (res.data.code === SUCCESS_CODE) {
      const rData = cloneDeep(res.data.data);

      table.data = rData.data;
      table.pagination.total = rData.total;
      table.pagination.pageSize = rData.pageSize;
      table.pagination.pageIndex = rData.pageIndex;

      table.pagination.loading = false;

      selectedIds.value = [];

      props.changeTableFlag();
    }
  }
};
const deleteFieldApi = async (ids: number[]) => {
  const params: DeleteFieldParam = {
    ids: ids,
    datasource: props.baseForm.datasource ?? '',
    databaseName: props.baseForm.databaseName ?? '',
    tableName: props.baseForm.tableName ?? '',
  };
  const res = await deleteField(params);
  if (res.data.code === SUCCESS_CODE) {
    getList();
    message.success('删除成功!');
  }
};

//  批量操作
const batchFn = debounce(async () => {
  if (selectedIds.value?.length < 1) {
    message.warning('请选择!');
  } else {
    deleteFieldApi(selectedIds.value);
  }
}, 200);

watch(
  () => props.tableFlag,
  () => {
    if (props.tableFlag) getList();
  },
);
</script>

<style scoped lang="scss">
.metadataTable {
  flex: 1;
  padding: 0 16px;
  background: #fff;

  > .tableHead {
    margin: 16px 0;
    > .btns {
      margin-left: auto;

      > .n-button {
        margin: 0 0 0 8px;
      }
    }
  }

  > .n-data-table {
    height: calc(100vh - 500px);
  }
  > .n-pagination {
    margin: 8px 0 8px auto;
  }
}
</style>
