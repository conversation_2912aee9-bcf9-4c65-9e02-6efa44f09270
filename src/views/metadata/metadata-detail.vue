<template>
  <div class="metadataDetail v">
    <div class="searchBox h">
      <div class="h">
        <span>数据库:</span>
        <n-select v-model:value="baseForm.datasource" :options="DatabaseOptions" @update:value="datasourceChange" />
      </div>
      <div class="h">
        <span>库名:</span>
        <n-select
          v-model:value="baseForm.databaseName"
          :options="databaseNameOptions"
          @update:value="databaseNameChange"
          @update:show="getDatabaseOptions"
        />
      </div>
      <div class="h">
        <span>表名:</span>
        <n-select
          v-model:value="baseForm.tableName"
          :options="tableNameOptions"
          @update:value="tableNameChange"
          @update:show="getTableOptions"
        />
      </div>
    </div>

    <div ref="mindDom" class="mindBox" :style="{ width: visible ? 'calc(100% - 500px)' : '100%' }"></div>

    <n-drawer v-model:show="visible" :width="502" placement="right" display-directive="show" :show-mask="'transparent'">
      <n-drawer-content title="字段信息">
        <div class="drawer v">
          <n-input v-model:value="fieldSearch" @update:value="searchChange" />

          <div class="listBox">
            <n-radio-group v-model:value="activeField" :default-value="activeField" @update:value="fieldChange">
              <n-grid :x-gap="12" :y-gap="8" :cols="2">
                <n-grid-item v-for="field in fieldList" :key="field.id">
                  <n-tooltip trigger="hover">
                    <template #trigger>
                      <n-radio :value="field.id">
                        {{ field.name?.length > 15 ? field.name?.slice(0, 15) + '...' : field.name }}
                      </n-radio>
                    </template>
                    {{ field.name }}
                  </n-tooltip>
                </n-grid-item>
              </n-grid>
            </n-radio-group>
          </div>

          <div class="infoBox" :loading="fieldLoading">
            <div class="h">
              <span>字段名:</span>
              <span>{{ fieldInfo?.fieldName }}</span>
            </div>
            <div class="h">
              <span>字段类型:</span>
              <span>{{ fieldInfo?.type }}</span>
            </div>
            <div class="h">
              <span>缺省值:</span>
              <span>{{ fieldInfo?.defaultValue }}</span>
            </div>
            <div class="h">
              <span>字段注释:</span>
              <span>{{ fieldInfo?.comment }}</span>
            </div>
            <div class="h">
              <span>CP计算逻辑:</span>
              <span>{{ fieldInfo?.cpLogic }}</span>
            </div>
            <div class="h">
              <span>FT计算逻辑:</span>
              <span>{{ fieldInfo?.ftLogic }}</span>
            </div>
            <div class="h">
              <span>备注:</span>
              <span>{{ fieldInfo?.info }}</span>
            </div>
          </div>
        </div>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<script setup lang="ts">
import { getQueryString, getTextWidth, jumpTo } from '@/utils/common';
import { DatabaseParam, FieldItem, FieldLineage, TableLineageItem } from '@/types/metadata';
import { getDatabases, getFieldInfo, getFieldLineage, getTableLineage, getTableNames } from '@/service/metadata';
import { SUCCESS_CODE } from '@/utils/http';
import { debounce } from 'lodash';
import { DatabaseOptions } from './data';
import useMind, { ITEM_HEIGHT } from './useMind';
import { DropdownItem } from '@/types/common';
import { useDialog, useMessage } from 'naive-ui';

const dialog = useDialog();
const message = useMessage();
const mindDom = shallowRef();
const mind = shallowRef();
const clickFlag = shallowRef(false);
const fieldSearch = shallowRef('');
const visible = shallowRef<boolean>(false);
const fieldLoading = shallowRef<boolean>(false);
const defaultInfo = shallowRef<TableLineageItem>({
  fields: {},
});

const activeField = shallowRef<number>();
const fieldList = shallowRef<{ id: number; name: string }[]>([]);
const fieldListCopy = shallowRef<{ id: number; name: string }[]>([]);
const baseForm = shallowReactive<DatabaseParam>({
  datasource: '',
  databaseName: '',
  tableName: '',
});
const cellList = shallowRef();
const fieldInfo = shallowRef<FieldItem>();
const fieldLineage = shallowRef<FieldLineage[]>([]);

const fieldChange = debounce((id: number) => {
  getFieldInfoApi(id);
  getFieldLineageApi(id);
}, 800);

const searchChange = (val: string) => {
  var reg = new RegExp(val.toLocaleLowerCase());

  const newList: { id: number; name: string }[] = [];
  fieldListCopy.value?.map((item) => {
    if (item.name.toLocaleLowerCase().match(reg)) {
      newList.push(item);
    }
  });
  fieldList.value = newList;
};

//  下拉列表
const databaseNameOptions = shallowRef<DropdownItem[]>([]);
const tableNameOptions = shallowRef<DropdownItem[]>([]);

//  下拉值改变
const datasourceChange = () => {
  baseForm.databaseName = '';
  baseForm.tableName = '';
};
const databaseNameChange = () => {
  baseForm.tableName = '';
};
const tableNameChange = () => {
  activeField.value = undefined;
  getTableLineageApi();
};
//  获取下拉框数据
const getDatabaseOptions = async (flag: boolean) => {
  if (flag && baseForm.datasource) {
    const res = await getDatabases(baseForm.datasource);
    if (res.data.code === SUCCESS_CODE) {
      databaseNameOptions.value = res.data.data?.map((item: string) => ({ label: item, value: item }));
    }
  }
};
const getTableOptions = async (flag: boolean) => {
  if (flag && baseForm.datasource && baseForm.databaseName) {
    const res = await getTableNames({ datasource: baseForm.datasource, databaseName: baseForm.databaseName });
    if (res.data.code === SUCCESS_CODE) {
      tableNameOptions.value = res.data.data?.map((item: string) => ({ label: item, value: item }));
    }
  }
};

//  配置mind
const setMindData = (mindData: TableLineageItem) => {
  const list: any = [];
  const cells: any = [];

  const lineageList = fieldLineage.value?.map((item) => ({
    key: item.table.tableName,
    value: Object.keys(item.fields),
  }));

  //  获取做大宽度
  const getMaxWidth = (list: DatabaseParam[] | undefined) => {
    let max = 0;
    list?.map((item: DatabaseParam) => {
      if (getTextWidth(item.databaseName + '.' + item.tableName, 12, 'Arial') > max) {
        max = getTextWidth(item.databaseName + '.' + item.tableName, 12, 'Arial');
      }
    });

    return max + 32;
  };
  //  获取高度
  const getHeight = (list: DatabaseParam[] | undefined, index: number) => {
    if (!list) return 180;

    let max = 0;
    for (let i = 0; i < index; i++) {
      if (lineageList?.length > 0) {
        max +=
          ITEM_HEIGHT +
          ((lineageList?.find((ite) => ite.key === list[i]?.tableName) as any)?.value?.length * ITEM_HEIGHT || 0);
      } else {
        max += ITEM_HEIGHT;
      }
    }
    return max;
  };

  //  添加节点
  list.push({
    id: 'root',
    shape: 'er-rect',
    label: baseForm.databaseName + '.' + baseForm.tableName,
    width: getTextWidth(baseForm.databaseName + '.' + baseForm.tableName, 12, 'Arial') + 32,
    height: ITEM_HEIGHT,
    position: {
      x: getMaxWidth(mindData.fromTables) + 200,
      y: 200,
    },
    ports: activeField.value
      ? [
          {
            id: activeField.value,
            group: 'list',
            attrs: {
              portBody: {
                width:
                  getTextWidth(activeField.value + '', 12, 'Arial') <
                  getTextWidth(baseForm.databaseName + '.' + baseForm.tableName, 12, 'Arial') + 32
                    ? getTextWidth(baseForm.databaseName + '.' + baseForm.tableName, 12, 'Arial') + 32
                    : getTextWidth(activeField.value + '', 12, 'Arial'),
              },
              portNameLabel: {
                text: fieldList.value?.find((item) => item.id === activeField.value)?.name,
              },
            },
            options: {
              silent: true,
            },
          },
        ]
      : [],
    attrs: {
      body: {
        fill: 'rgba(205,205,205,1)',
        stroke: 'rgba(202,205,209,.9)',
      },
    },
  });
  mindData.fromTables?.map((item, index: number) => {
    list.push({
      id: item.datasource + '.' + item.databaseName + '.' + item.tableName,
      shape: 'er-rect',
      parent: 'root',
      label: item.databaseName + '.' + item.tableName,
      width: getTextWidth(item.databaseName + '.' + item.tableName, 12, 'Arial') + 32,
      height: ITEM_HEIGHT,
      position: {
        x: 100,
        y: getHeight(mindData.fromTables, index) + 180 + (20 * index || 0),
      },
      ports: lineageList
        ?.find((ite) => ite.key === item.tableName)
        ?.value?.map((it, index: number) => ({
          id: it + index,
          group: 'list',

          attrs: {
            portBody: {
              width:
                getTextWidth(it, 12, 'Arial') < getTextWidth(item.databaseName + '.' + item.tableName, 12, 'Arial') + 32
                  ? getTextWidth(item.databaseName + '.' + item.tableName, 12, 'Arial') + 32
                  : getTextWidth(it, 12, 'Arial'),
            },
            portNameLabel: {
              text: it,
            },
          },
          options: {
            silent: true,
          },
        })),
      attrs: {
        body: {
          color: 'red',
          fill: item.datasource === 'clickhouse' ? 'rgba(222,120,2,.7)' : 'rgba(36,91,219,.7)',
          stroke: item.datasource === 'clickhouse' ? 'rgba(222,120,2,.9)' : 'rgba(36,91,219,.9)',
        },
      },
    });
  });

  mindData.toTables?.map((item, index: number) => {
    list.push({
      id: item.datasource + '.' + item.databaseName + '.' + item.tableName,
      parent: 'root',
      shape: 'er-rect',
      label: item.databaseName + '.' + item.tableName,
      width: getTextWidth(item.databaseName + '.' + item.tableName, 12, 'Arial') + 32,
      height: ITEM_HEIGHT,
      position: {
        x:
          getMaxWidth(mindData.fromTables) +
          getTextWidth(baseForm.databaseName + '.' + baseForm.tableName, 12, 'Arial') +
          300,
        y: getHeight(mindData.toTables, index) + 180 + (20 * index || 0),
      },
      ports: lineageList
        ?.find((ite) => ite.key === item.tableName)
        ?.value?.map((it, index: number) => ({
          id: it + index,
          group: 'list',
          attrs: {
            portBody: {
              width:
                getTextWidth(it, 12, 'Arial') < getTextWidth(item.databaseName + '.' + item.tableName, 12, 'Arial') + 32
                  ? getTextWidth(item.databaseName + '.' + item.tableName, 12, 'Arial') + 32
                  : getTextWidth(it, 12, 'Arial'),
            },
            portNameLabel: {
              text: it,
            },
          },
          options: {
            silent: true,
          },
        })),
      attrs: {
        body: {
          fill: item.datasource === 'clickhouse' ? 'rgba(222,120,2,.7)' : 'rgba(36,91,219,.7)',
          stroke: item.datasource === 'clickhouse' ? 'rgba(222,120,2,.9)' : 'rgba(36,91,219,.9)',
        },
      },
    });
  });

  //  添加连线
  mindData.fromTables?.map((item, index: number) => {
    list.push({
      id: item.tableName + index,
      shape: 'edge',
      source: {
        cell: item.datasource + '.' + item.databaseName + '.' + item.tableName,
      },
      target: {
        cell: 'root',
      },
      attrs: {
        line: {
          stroke: '#A2B1C3',
          strokeWidth: 2,
        },
      },
      zIndex: 0,
    });
  });
  mindData.toTables?.map((item, index: number) => {
    list.push({
      id: item.tableName + index,
      shape: 'edge',
      source: {
        cell: 'root',
      },
      target: {
        cell: item.datasource + '.' + item.databaseName + '.' + item.tableName,
      },
      attrs: {
        line: {
          stroke: '#A2B1C3',
          strokeWidth: 2,
        },
      },
      zIndex: 0,
    });
  });

  //  渲染
  list.forEach((item: any) => {
    if (item.shape === 'edge') {
      cells.push(mind.value.createEdge(item));
    } else {
      cells.push(mind.value.createNode(item));
    }
  });

  cellList.value = cells;
  mind.value.resetCells(cells);

  //  添加事件
  if (clickFlag.value) return;
  mind.value.on('cell:click', ({ cell }: any) => {
    if (cell?.id === 'root') {
      if (baseForm.tableName) {
        visible.value = true;

        mind.value.translate(-100, 0);
      } else {
        message.warning('请先选择数据表!');
      }
    } else {
      dialog.warning({
        title: '提示',
        content: '是否跳转至元数据管理页面?',
        positiveText: '确认',
        onPositiveClick: () => {
          if (cell?.id) {
            const url = '/metadata';
            jumpTo(
              url,
              {
                datasource: cell.id?.split('.')[0],
                databaseName: cell.id?.split('.')[1],
                tableName: cell.id?.split('.')[2],
              },
              { title: '元数据管理' },
            );
          }
        },
      });
    }
  });
  clickFlag.value = true;
};
//  主表字段
const setFieldList = (fields: { [key: string]: number }) => {
  if (!fields) return;

  const list: { id: number; name: string }[] = [];
  Object.keys(fields)?.map((key: string) => {
    list.push({ name: key, id: fields[key] });
  });
  fieldList.value = list;
  fieldListCopy.value = list;
  // activeField.value = list[0]?.id;
  // getFieldInfoApi(list[0]?.id);
};

//  血缘列表信息
const getTableLineageApi = async () => {
  const res = await getTableLineage(baseForm);
  if (res.data.code === SUCCESS_CODE) {
    setFieldList(res.data.data.fields);
    defaultInfo.value = res.data.data;
    setMindData(res.data.data);
  }
};
//  血缘字段信息
const getFieldInfoApi = async (id: number) => {
  fieldLoading.value = true;
  const res = await getFieldInfo(id);
  if (res.data.code === SUCCESS_CODE) {
    fieldInfo.value = res.data.data;
    fieldLoading.value = false;
  }
};
const getFieldLineageApi = async (id: number) => {
  const res = await getFieldLineage(id);
  if (res.data.code === SUCCESS_CODE) {
    fieldLineage.value = res.data.data;

    setMindData(defaultInfo.value);
  }
};

onMounted(() => {
  baseForm.datasource = decodeURIComponent(getQueryString(window.location.href, 'datasource') + '');
  baseForm.databaseName = decodeURIComponent(getQueryString(window.location.href, 'databaseName') + '');
  baseForm.tableName = decodeURIComponent(getQueryString(window.location.href, 'tableName') + '');

  getTableLineageApi();

  nextTick(() => {
    mind.value = useMind(mindDom.value);
  });
});

watch(visible, () => {
  if (!visible.value) {
    mind.value.translate(0, 0);
  }
});
</script>

<style scoped lang="scss">
.metadataDetail {
  flex: 1;

  .searchBox {
    > div {
      margin: 16px 32px 0 16px;

      > span {
        margin: auto 16px auto 0;
        flex-shrink: 0;
      }
      > .n-select {
        width: 220px;
      }
    }
  }

  .mindBox {
    flex: 1;
    margin-top: 16px;
    background: #fff;
  }
}

.drawer {
  height: 100%;
  justify-content: space-between;

  > .n-input {
    margin-bottom: 16px;
  }

  > .listBox {
    max-height: 420px;
    overflow: auto;

    padding-left: 8px;
  }

  > .infoBox {
    margin: 16px 0 8px;
    height: calc(100% - 420px);
    border: 1px solid rgb(239, 239, 245);
    padding: 8px;

    > div {
      font-size: 16px;
      margin: 8px 0;

      > span {
        &:first-child {
          flex-shrink: 0;
          width: 90px;
          text-align: right;
        }
        &:last-child {
          margin-left: 8px;
        }
      }
    }
  }
}
</style>
