import { updateField } from '@/service/metadata';
import { FieldItem, UpdateFieldParam } from '@/types/metadata';
import { SUCCESS_CODE } from '@/utils/http';
import { DataTableColumns, NButton, NInput } from 'naive-ui';

export enum DatabaseEnum {
  CLICKHOUSE = 'clickhouse',
  MYSQL = 'mysql',
}

export const DatabaseOptions = [
  {
    label: 'clickhouse',
    value: DatabaseEnum.CLICKHOUSE,
  },
  {
    label: 'mysql',
    value: DatabaseEnum.MYSQL,
  },
];

export const COLUMNS: (deleteFieldApi: (ids: number[]) => Promise<void>) => DataTableColumns<FieldItem> = (
  deleteFieldApi,
) => {
  return [
    {
      type: 'selection',
      fixed: 'left',
    },
    {
      title: '字段名',
      key: 'fieldName',
      resizable: true,
      width: 160,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '字段类型',
      key: 'type',
      resizable: true,
      width: 160,
    },
    {
      title: '缺省值',
      key: 'defaultValue',
      resizable: true,
      width: 160,
    },
    {
      title: '字段注释',
      key: 'comment',
      resizable: true,
      width: 220,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: 'CP计算逻辑',
      key: 'cpLogic',
      resizable: true,
      width: 220,
      ellipsis: {
        tooltip: true,
      },
      render: (rowData: FieldItem) => {
        return rowData?.isEdit ? <NInput v-model:value={rowData.cpLogic} maxlength={1000} /> : <>{rowData.cpLogic}</>;
      },
    },
    {
      title: 'FT计算逻辑',
      key: 'ftLogic',
      resizable: true,
      width: 220,
      ellipsis: {
        tooltip: true,
      },
      render: (rowData: FieldItem) => {
        return rowData?.isEdit ? <NInput v-model:value={rowData.ftLogic} maxlength={1000} /> : <>{rowData.ftLogic}</>;
      },
    },
    {
      title: '备注',
      key: 'info',
      resizable: true,
      width: 220,
      ellipsis: {
        tooltip: true,
      },
      render: (rowData: FieldItem) => {
        return rowData?.isEdit ? <NInput v-model:value={rowData.info} maxlength={1000} /> : <>{rowData.info}</>;
      },
    },

    {
      title: '操作',
      key: 'tool',
      width: 220,
      fixed: 'right',
      render: (rowData) => {
        const save = async () => {
          const param: UpdateFieldParam = {
            id: rowData.id,
            cpLogic: rowData.cpLogic,
            ftLogic: rowData.ftLogic,
            info: rowData.info,
          };

          rowData.editLoading = true;
          const res = await updateField(param);
          if (res.data.code === SUCCESS_CODE) {
            rowData.editLoading = false;
            rowData.isEdit = false;
          }
        };
        const del = async (ids: number[]) => {
          rowData.delLoading = true;
          await deleteFieldApi(ids);
          rowData.delLoading = false;
        };

        return (
          <>
            <NButton type="info" text onClick={() => (rowData.isEdit = !rowData.isEdit)}>
              {rowData.isEdit ? '取消编辑' : '编辑'}
            </NButton>
            <NButton
              style={{ margin: '0 16px' }}
              type="info"
              text
              onClick={save}
              loading={rowData.editLoading}
              disabled={!rowData.isEdit}
            >
              保存
            </NButton>
            <NButton type="error" text onClick={() => del([rowData.id])} loading={rowData.delLoading}>
              删除
            </NButton>
          </>
        );
      },
    },
  ];
};
