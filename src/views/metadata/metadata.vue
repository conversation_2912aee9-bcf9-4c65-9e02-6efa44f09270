<template>
  <div class="metadata v">
    <div class="searchBox h">
      <div class="h">
        <span>数据库:</span>
        <n-select
          clearable
          v-model:value="baseForm.datasource"
          :options="DatabaseOptions"
          @clear="warningLog('datasource', true)"
          @update:value="warningLog('datasource', false)"
        />
      </div>
      <div class="h" v-if="!isEmpty(baseForm.datasource)">
        <span>库名:</span>
        <n-select
          clearable
          v-model:value="baseForm.databaseName"
          :options="databaseNameOptions"
          :disabled="isEmpty(baseForm.datasource)"
          @clear="warningLog('databaseName', true)"
          @update:show="getDatabaseOptions"
          @update:value="warningLog('databaseName', false)"
        />
      </div>
      <div class="h" v-if="!isEmpty(baseForm.datasource) && !isEmpty(baseForm.databaseName)">
        <span>表名:</span>
        <n-select
          clearable
          v-model:value="baseForm.tableName"
          :options="tableNameOptions"
          :disabled="isEmpty(baseForm.datasource) || isEmpty(baseForm.databaseName)"
          @clear="warningLog('tableName', true)"
          @update:show="getTableOptions"
          @update:value="warningLog('tableName', false)"
        />
      </div>

      <n-button type="primary" v-if="isCheckTable" @click="jumpDetail">表血缘</n-button>
    </div>

    <div class="infoBox v">
      <div class="h">
        <span>描述</span>
        <n-input
          type="textarea"
          v-model:value="baseForm.description"
          :disabled="descriptionDisabled"
          :autosize="{ minRows: 3, maxRows: 3 }"
          :maxlength="1000"
        />
        <div class="btn v">
          <n-button @click="descriptionDisabled = false" :disabled="!isCheckTable || !descriptionDisabled">
            编辑
          </n-button>
          <n-button type="primary" @click="updateDescriptionApi" :disabled="!isCheckTable || descriptionDisabled">
            保存
          </n-button>
        </div>
      </div>
      <div class="h">
        <span>链接</span>
        <n-input v-model:value="linkObj.name" placeholder="Name" :maxlength="100" />
        <n-input v-model:value="linkObj.link" placeholder="Link" :maxlength="200" />
        <n-button
          type="primary"
          @click="addLinkApi"
          :loading="linkObj.addLoading"
          :disabled="!isCheckTable || isEmpty(linkObj.name) || isEmpty(linkObj.link)"
        >
          添加
        </n-button>
      </div>

      <div class="urlList v">
        <div class="h" v-for="item in linkObj.linkList" :key="item.id">
          <span @click="openUrl(item.link)">{{ item.linkName }}</span>
          <n-button type="error" text @click="delLink(item.id)" :loading="linkObj.delLoading">删除</n-button>
        </div>
      </div>
    </div>

    <MetadataTable :baseForm="baseForm" :tableFlag="tableFlag" :changeTableFlag="() => (tableFlag = false)" />
  </div>
</template>

<script setup lang="ts">
import { AddNull, DropdownItem } from '@/types/common';
import { DatabaseOptions } from './data';
import { AddLinkParam, DatabaseParam, DescriptionItem, LinkItem, UpdateDescriptionParam } from '@/types/metadata';
import {
  addLink,
  deleteLink,
  getDatabases,
  getDescription,
  getLinks,
  getTableNames,
  updateDescription,
} from '@/service/metadata';
import { SUCCESS_CODE } from '@/utils/http';
import { useDialog, useMessage } from 'naive-ui';
import { isEmpty } from 'lodash';
import MetadataTable from './metadata-table.vue';
import { getQueryString, jumpTo } from '@/utils/common';

const dialog = useDialog();
const message = useMessage();

//  数据库信息
const oldBaseForm = shallowReactive<AddNull<DescriptionItem>>({
  id: null,
  datasource: null,
  databaseName: null,
  tableName: null,
  description: null,
});
const baseForm = shallowReactive<AddNull<DescriptionItem>>({
  id: null,
  datasource: null,
  databaseName: null,
  tableName: null,
  description: null,
});
const tableFlag = shallowRef(false);
const descriptionDisabled = shallowRef(true);

//  链接信息
const linkObj = shallowReactive<{
  name: string;
  link: string;
  addLoading: boolean;
  delLoading: boolean;
  linkList: LinkItem[];
}>({
  name: '',
  link: '',
  addLoading: false,
  delLoading: false,
  linkList: [],
});

//  下拉列表
const databaseNameOptions = shallowRef<DropdownItem[]>([]);
const tableNameOptions = shallowRef<DropdownItem[]>([]);

//  选中表状态
const isCheckTable = computed(() => {
  return !isEmpty(baseForm.databaseName) && !isEmpty(baseForm.datasource) && !isEmpty(baseForm.tableName);
});

//  跳转详情
const jumpDetail = () => {
  const url = '/metadata-detail';
  jumpTo(
    url,
    { datasource: baseForm.datasource, databaseName: baseForm.databaseName, tableName: baseForm.tableName },
    { title: '表血缘详情' },
  );
};
//  打开链接
const openUrl = (url: string) => {
  window.open(url);
};

//  下拉列表清空拦截
const warningLog = (key: 'datasource' | 'databaseName' | 'tableName', isClear: boolean) => {
  if (baseForm.databaseName && baseForm.datasource && baseForm.tableName && !descriptionDisabled.value) {
    dialog.warning({
      title: '提示',
      content: '请确认正在编辑的信息已保存!',
      positiveText: '确认',
      negativeText: '取消',
      onNegativeClick: () => {
        baseForm[key] = oldBaseForm[key];
      },
      onPositiveClick: () => {
        baseForm[key] = isClear ? null : baseForm[key];
        if (key === 'datasource') {
          baseForm.databaseName = null;
          baseForm.tableName = null;
          baseForm.description = null;
          linkObj.linkList = [];
        }
        if (key === 'databaseName') {
          baseForm.tableName = null;
          baseForm.description = null;
          linkObj.linkList = [];
        }
        if (key === 'tableName') {
          tableFlag.value = true;
          getDescriptionApi();
          getLinkApi();
        }
      },
    });
  } else {
    if (key === 'datasource') {
      baseForm.databaseName = null;
      baseForm.tableName = null;
    }
    if (key === 'databaseName') {
      baseForm.tableName = null;
    }

    if (key === 'tableName') {
      tableFlag.value = true;
      getDescriptionApi();
      getLinkApi();
    }
  }
};

//  获取下拉框数据
const getDatabaseOptions = async (flag: boolean) => {
  if (flag && baseForm.datasource) {
    const res = await getDatabases(baseForm.datasource);
    if (res.data.code === SUCCESS_CODE) {
      databaseNameOptions.value = res.data.data?.map((item: string) => ({ label: item, value: item }));
    }
  }
};
const getTableOptions = async (flag: boolean) => {
  if (flag && baseForm.datasource && baseForm.databaseName) {
    const res = await getTableNames({ datasource: baseForm.datasource, databaseName: baseForm.databaseName });
    if (res.data.code === SUCCESS_CODE) {
      tableNameOptions.value = res.data.data?.map((item: string) => ({ label: item, value: item }));
    }
  }
};
//  获取描述
const getDescriptionApi = async () => {
  if (baseForm.databaseName && baseForm.datasource && baseForm.tableName) {
    const param: DatabaseParam = {
      datasource: baseForm.datasource,
      databaseName: baseForm.databaseName,
      tableName: baseForm.tableName,
    };

    const res = await getDescription(param);

    if (res.data.code === SUCCESS_CODE) {
      const rData = res.data.data;

      baseForm.id = rData.id;
      baseForm.description = rData.description;

      oldBaseForm.datasource = rData.datasource;
      oldBaseForm.databaseName = rData.databaseName;
      oldBaseForm.tableName = rData.tableName;

      descriptionDisabled.value = true;
    }
  }
};
//  更新描述
const updateDescriptionApi = async () => {
  if (baseForm.databaseName && baseForm.datasource && baseForm.tableName && baseForm.description) {
    const param: UpdateDescriptionParam = {
      datasource: baseForm.datasource,
      databaseName: baseForm.databaseName,
      tableName: baseForm.tableName,
      description: baseForm.description,
    };

    const res = await updateDescription(param);
    if (res.data.code === SUCCESS_CODE) {
      descriptionDisabled.value = true;
      message.success('保存成功!');

      getDescriptionApi();
    }
  } else {
    message.warning('描述不能为空!');
  }
};

//  获取链接
const getLinkApi = async () => {
  if (baseForm.databaseName && baseForm.datasource && baseForm.tableName) {
    const param: DatabaseParam = {
      datasource: baseForm.datasource,
      databaseName: baseForm.databaseName,
      tableName: baseForm.tableName,
    };

    const res = await getLinks(param);
    if (res.data.code === SUCCESS_CODE) {
      linkObj.linkList = res.data.data;
    }
  }
};
//  添加链接
const addLinkApi = async () => {
  if (isEmpty(linkObj.name) || isEmpty(linkObj.link)) {
    message.warning('链接请填写完整!');
  } else if (!/^https?:\/\//.test(linkObj.link)) {
    message.warning('请输入有效链接!');
  } else {
    if (baseForm.databaseName && baseForm.datasource && baseForm.tableName) {
      linkObj.addLoading = true;
      const param: AddLinkParam = {
        datasource: baseForm.datasource,
        databaseName: baseForm.databaseName,
        tableName: baseForm.tableName,
        name: linkObj.name,
        link: linkObj.link,
      };

      const res = await addLink(param);

      linkObj.addLoading = false;
      if (res.data.code === SUCCESS_CODE) {
        message.success('链接添加成功!');
        linkObj.name = '';
        linkObj.link = '';

        getLinkApi();
      }
    } else {
      message.warning('请先选择数据表!');
    }
  }
};
//  删除链接
const delLink = async (id: number) => {
  linkObj.delLoading = true;

  const res = await deleteLink(id);
  if (res.data.code === SUCCESS_CODE) {
    linkObj.delLoading = false;
    getLinkApi();
    message.success('删除链接成功!');
  }
};

onMounted(() => {
  const ds = decodeURIComponent(getQueryString(window.location.href, 'datasource') + '');
  const dn = decodeURIComponent(getQueryString(window.location.href, 'databaseName') + '');
  const tn = decodeURIComponent(getQueryString(window.location.href, 'tableName') + '');

  if (ds !== 'null' && dn !== 'null' && tn !== 'null') {
    baseForm.datasource = ds;
    baseForm.databaseName = dn;
    baseForm.tableName = tn;

    tableFlag.value = true;
    getDescriptionApi();
    getLinkApi();
  }
});
</script>

<style scoped lang="scss">
.metadata {
  flex: 1;

  .searchBox {
    > div {
      margin: 16px 32px 0 16px;

      > span {
        margin: auto 16px auto 0;
        flex-shrink: 0;
      }
      > .n-select {
        width: 220px;
      }
    }

    > .n-button {
      margin: auto 0 0;
    }
  }

  .infoBox {
    height: 270px;
    margin: 16px 0;
    background: #fff;
    padding: 16px 32px;

    > div {
      margin-top: 16px;

      > span {
        width: 120px;
        margin: auto;
        flex-shrink: 0;
        text-align: center;
      }

      > .n-input {
        flex-shrink: 1;
        margin: 0 16px;
      }
      > .n-button {
        width: 120px;
      }

      > .btn {
        > .n-button {
          width: 120px;
          margin: 8px 0;
        }
      }
    }

    > .urlList {
      max-height: 120px;
      overflow: auto;

      > div {
        margin-left: 130px;

        > span {
          margin: auto 16px;
          text-decoration: underline;

          &:hover {
            color: rgba(86, 86, 86, 0.8);
            cursor: pointer;
          }
        }
      }
    }
  }
}
</style>
