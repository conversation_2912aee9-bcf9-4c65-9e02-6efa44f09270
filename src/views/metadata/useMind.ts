import { Graph, Shape } from '@antv/x6';

export const ITEM_WIDTH = 290;
export const ITEM_HEIGHT = 24;

const GRAPH_OPTIONS = {
  inherit: 'rect',
  markup: [
    {
      tagName: 'rect',
      selector: 'body',
    },
    {
      tagName: 'text',
      selector: 'label',
    },
  ],
  attrs: {
    rect: {
      strokeWidth: 1,
      stroke: '#5F95FF',
      fill: '#5F95FF',
    },
    label: {
      fontWeight: 'bold',
      fill: '#ffffff',
      fontSize: 12,
    },
  },
  ports: {
    groups: {
      list: {
        markup: [
          {
            tagName: 'rect',
            selector: 'portBody',
          },
          {
            tagName: 'text',
            selector: 'portNameLabel',
          },
          {
            tagName: 'text',
            selector: 'portTypeLabel',
          },
        ],
        attrs: {
          portBody: {
            width: ITEM_WIDTH,
            height: ITEM_HEIGHT,
            strokeWidth: 1,
            stroke: '#5F95FF',
            fill: '#EFF4FF',
            magnet: true,
          },
          portNameLabel: {
            ref: 'portBody',
            refX: 6,
            refY: 6,
            fontSize: 10,
          },
          portTypeLabel: {
            ref: 'portBody',
            refX: 95,
            refY: 6,
            fontSize: 10,
          },
        },
        position: 'erPortPosition',
      },
    },
  },
  options: {
    silent: true,
  },
};

Graph.registerPortLayout(
  'erPortPosition',
  (portsPositionArgs) => {
    return portsPositionArgs.map((_, index) => {
      return {
        position: {
          x: 0,
          y: (index + 1) * 24,
        },
        angle: 0,
      };
    });
  },
  true,
);
Graph.registerNode('er-rect', GRAPH_OPTIONS, true); // 渲染元素

const useMind = (domID: HTMLElement) =>
  new Graph({
    container: domID,
    mousewheel: true,
    panning: true,
    translating: { restrict: true },
    magnetThreshold: 100000,
    interacting: false,

    connecting: {
      snap: false, //是否自动吸附
      allowBlank: false,
      allowLoop: false, //是否允许创建循环连线
      allowEdge: false, //是否允许边连接到节点
      allowNode: false, //是否允许边连接到节点
      allowMulti: false, //是否允许边链接到连接桩
      highlight: false, //拖动边时，是否高亮显示所有可用的连接桩或节点

      router: {
        name: 'er',
        args: {
          offset: 25,
          direction: 'H',
        },
      },
      createEdge() {
        return new Shape.Edge({
          attrs: {
            line: {
              stroke: '#A2B1C3',
              strokeWidth: 2,
            },
          },
        });
      },
    },
  });

export default useMind;
