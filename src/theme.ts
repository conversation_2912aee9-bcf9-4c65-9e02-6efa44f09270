const THEME = {
  common: {
    fontFamily:
      '-apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol', //字体
    primaryColor: 'rgba(93, 45, 205, 1)',
    primaryColorHover: 'rgba(143, 111, 218, 1)',
    primaryColorPressed: 'rgba(68, 34, 149, 1)',

    //  small
    heightSmall: '24px',
    fontSizeSmall: '12px',

    //  large
    heightLarge: '32px',
    fontSizeLarge: '14px',
  },
  Button: {
    textColor: '#5D2DCD',
    textColorFocus: '#5D2DCD',
    border: '1px solid #5D2DCD',
    colorHover: 'rgba(231, 225, 243, 1)',
    colorPressed: 'rgba(217, 215, 222, 1)',
    textColorPressed: 'rgba(68, 34, 149, 1)',

    // primary
    colorFocusPrimary: '#5D2DCD',
    textColorTextPrimary: 'rgba(255, 255, 255, 1)',
    textColorTextHoverPrimary: 'rgba(255, 255, 255, 1)',
    textColorTextPressedPrimary: 'rgba(255, 255, 255, 1)',

    // text
    textColorTextInfo: 'rgba(93, 45, 205, 1)',
    textColorTextHoverInfo: 'rgba(143, 111, 218, 1)',
  },
};

export default THEME;
