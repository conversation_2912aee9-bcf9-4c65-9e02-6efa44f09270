import { BASE_INFO, TableType } from '@/views/conf-mgt/components/constants';
import { defineStore } from 'pinia';

export const useConfStore = defineStore('conf', {
  state: () => {
    return {
      constantOptionMap: {
        dataClearRuleType: [],
        fileCategory: [],
        parseableField: [],
        metadata: [],
        stdfFieldType: [],
        testArea: [],
      },
      editRuleInfo: {
        visible: false,
        content: '',
        tableType: TableType.SubCustomer,
        rowData: {},
        colItem: {},
      },
      saveRequestStatusMap: {
        [BASE_INFO]: undefined,
        [TableType.SubCustomer]: undefined,
        [TableType.FileRename]: undefined,
        [TableType.SpecialLogic]: undefined,
        [TableType.ConvertScript]: undefined,
        [TableType.CriticalField]: undefined,
      },
    };
  },
  getters: {},
  actions: {
    resetSaveRequestStatusMap() {
      return (this.saveRequestStatusMap = {
        [BASE_INFO]: undefined,
        [TableType.SubCustomer]: undefined,
        [TableType.FileRename]: undefined,
        [TableType.SpecialLogic]: undefined,
        [TableType.ConvertScript]: undefined,
        [TableType.CriticalField]: undefined,
      });
    },
  },
});
