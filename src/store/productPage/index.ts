import { getDeviceDropdownApi, getDropdownApi } from '@/service/productPage';
import { defineStore } from 'pinia';

export const useProductPage = defineStore('useProductPage', {
  state: () => {
    return {
      products: [],
      isEdit: false,
      pagination: {
        pageIndex: 1,
        pageSize: 20
      },
      productFamilyDropdown: [],
      productTypeDropdown: [
        {
          label: 'Production',
          value: 'Production'
        },
        {
          label: 'Engineering',
          value: 'Engineering'
        }
      ],
      deviceNameDropdown: [],
      testAreaDropdown: [
        {
          label: 'CP',
          value: 'CP'
        },
        {
          label: 'FT',
          value: 'FT'
        },
        {
          label: 'WAT',
          value: 'WAT'
        },
        {
          label: 'SLT',
          value: 'SLT'
        }
      ],
      testStageCPDropdown: [],
      testStageFTDropdown: [],
      testStageWATDropdown: [],
      testStageSLTDropdown: [],
      factoryDropdown: [],
      factorySiteDropdown: [],
      dataSourceDropdown: [
        {
          label: 'Test Raw Data',
          value: 'Test Raw Data'
        },
        {
          label: 'CP Map',
          value: 'CP Map'
        },
        {
          label: 'Inkless Map',
          value: 'Inkless Map'
        }
      ]
    };
  },
  getters: {},
  actions: {
    setProducts(val) {
      this.products = val;
    },
    setPagination(val) {
      this.pagination = val;
    },
    setIsEdit(val) {
      this.isEdit = val;
    },

    async getDropdown(productId?: number) {
      const list = [
        {
          key: 'productFamilyDropdown',
          url: 'one_data_product_family'
        },
        {
          key: 'testStageCPDropdown',
          url: 'one_data_test_stage_cp'
        },
        {
          key: 'testStageFTDropdown',
          url: 'one_data_test_stage_ft'
        },
        {
          key: 'testStageWATDropdown',
          url: 'one_data_test_stage_wat'
        },
        {
          key: 'testStageSLTDropdown',
          url: 'one_data_test_stage_slt'
        },
        {
          key: 'testStageCPDropdown',
          url: 'one_data_test_stage_cp'
        },
        {
          key: 'factoryDropdown',
          url: 'one_data_test_factory'
        },
        {
          key: 'factorySiteDropdown',
          url: 'one_data_test_factory_site'
        }
      ];

      list?.map(async (item) => {
        const res = await getDropdownApi(item.url);
        if (res.data.code == '200') {
          this[item.key] = res.data.data?.map((item) => ({
            label: item.dictLabel,
            value: item.dictValue
          }));
        }
      });

      const res = await getDeviceDropdownApi(productId);
      if (res.data.msg === 'Success') {
        this.deviceNameDropdown = res.data.data?.map((item) => ({ label: item, value: item }));
      }
    }
  }
});
