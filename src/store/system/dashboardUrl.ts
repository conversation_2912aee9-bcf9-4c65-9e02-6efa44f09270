import { getSysConfigInfo } from '@/service/common';
import { defineStore } from 'pinia';

import { ref } from 'vue';

const useDashUrlInfoStore = defineStore('system', () => {
  const dashboardUrl = ref();
  const moveMass = ref();
  const factoryMode = ref();

  async function getDashboardUrl() {
    const res = await getSysConfigInfo('fileUpload_dashboard_url');
    const { data } = res.data || {};
    dashboardUrl.value = data;
  }
  async function getMoveMass() {
    const res = await getSysConfigInfo('open_manual_production');
    const { data } = res.data || {};
    moveMass.value = data;
  }
  return { dashboardUrl, moveMass, factoryMode, getDashboardUrl, getMoveMass };
});

export default useDashUrlInfoStore;
