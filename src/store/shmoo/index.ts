import { DateFieldEnum, DateType } from '@/components/filter-date/typing';
import { defineStore } from 'pinia';

export const SHMOO_STORE_ID = 'shmoo';

/**
 * shmoo图的全局store
 */

export const useShmooStore = defineStore<string, ShmooStoreState, {}, ShmooStoreActions>(SHMOO_STORE_ID, {
  state: () => ({
    filters: {
      date: {
        timeType: DateFieldEnum.UPLOAD_TIME,
        dateType: 'All',
        datePicker: undefined,
      },
    },
  }),
  getters: {},
  actions: {
    updateFiltersDate(payload = {}) {
      Object.assign(this.filters.date, payload);
    },
  },
});

export interface ShmooStoreState {
  filters: {
    date: {
      timeType?: DateFieldEnum;
      dateType?: DateType;
      datePicker?: [Date, Date];
    };
  };
}

export interface ShmooStoreActions {
  updateFiltersDate: (payload: Partial<ShmooStoreState['filters']['date']>) => void;
}
