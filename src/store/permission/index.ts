import { defineStore } from "pinia";
import { IUserInfo } from "@/types/permission";
import { getPermissionInfo } from "@/service/permission";
import { ref } from "vue";

const usePermissionStore = defineStore("permission", () => {
  const userInfo = ref<IUserInfo>();
  const roles = ref<string[]>();
  const permissions = ref<string[]>();
  const hasInit = ref(false);

  async function getPermission() {
    try {
      const res = await getPermissionInfo();
      const { permissions: perm = [], roles: r, user: u } = res.data || {};
      hasInit.value = true;
      permissions.value = perm.slice(0);
      roles.value = r;
      userInfo.value = u;
      return perm;
    } catch (e) {
      hasInit.value = false;
    }
    return [];
  }

  return { userInfo, roles, permissions, hasInit, getPermission };
});

export default usePermissionStore;
