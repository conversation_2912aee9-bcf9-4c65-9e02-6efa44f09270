import {
  getStepFourData,
  getStepOneData,
  getStepThreeData,
  getStepTwoData,
  stepSave,
} from '@/service/dataCenterQuality';
import { RepairInfosType, StepOneListType, StepTwoListType } from '@/views/data-center-quality/typing';
import { ElMessage } from 'element-plus';
import { cloneDeep, pick } from 'lodash';
import { defineStore } from 'pinia';

export const dataQuality = defineStore('dataQuality', {
  state: () => {
    return {
      dataRecovery: {
        repairList: [],
        title: undefined as string,
        fileIds: [] as number[],
        width: undefined as number,
        height: undefined as number,
        selectedFileIds: [] as number[],
        repairInfos: [] as RepairInfosType[],
        stepOneData: [] as StepOneListType[],
        stepTwoData: [] as StepTwoListType[],
        stepThreeData: [] as StepOneListType[],
        originStepThreeData: [] as StepOneListType[],
        stepFourData: [] as StepOneListType[],
        status: undefined as boolean,
        subCustomer: undefined as string,
        resultDialog: false as boolean,
        resultLoading: false as boolean,
        tableLoading: false as boolean,
        errorMsg: undefined as string,
      },
    };
  },

  getters: {},

  actions: {
    async getStepOne(fileIds) {
      const res = await getStepOneData({ fileIds: fileIds });
      if (res.data.code === 200 || res.data.code === '00000001') {
        this.dataRecovery.stepOneData = res.data.data.map((item) => {
          return { ...item, checked: true };
        });
        return true;
      } else {
        ElMessage.error(res.data.msg);
        return false;
      }
    },
    async getStepTwo(fileIds) {
      this.dataRecovery.selectedFileIds = fileIds;
      const res = await getStepTwoData({ fileIds: fileIds });
      if (res.data.code === 200 || res.data.code === '00000001') {
        this.dataRecovery.stepTwoData = res.data.data;
        return true;
      } else {
        ElMessage.error(res.data.msg);
        return false;
      }
    },
    async getStepThree() {
      const data = [];
      this.dataRecovery.stepTwoData.map((item) => {
        data.push({
          fileId: item.fileId,
          fieldName: item.fieldShowName,
          repairField: item.field,
          originalValue: item.originalFieldValue,
          modifyValue: item.modifyValue,
          modifyRemark: item.modifyRemark,
        });
      });
      this.dataRecovery.repairList = data;

      this.dataRecovery.repairInfos = {
        fileIds: this.dataRecovery.selectedFileIds,
        repairInfo: this.dataRecovery.repairList,
      };

      if (this.dataRecovery.fileIds?.length > 0) {
        const res = await getStepThreeData(this.dataRecovery.repairInfos);
        if (res.data.code === 200 || res.data.code === '00000001') {
          const list = [...res.data.data];
          list?.forEach((item) => {
            if (item.flowId[0] === 'P') item.retestHardBin = 'NA';
          });

          this.dataRecovery.stepThreeData = res.data.data;
          this.dataRecovery.originStepThreeData = cloneDeep(res.data.data);
          return true;
        } else {
          ElMessage.error(res.data.msg);
          return false;
        }
      }
    },
    async getStepFour() {
      const list = [...this.dataRecovery.stepThreeData];
      const newList = list
        ?.filter((item) => item.repairFile === true)
        ?.map((item, index) => {
          return {
            ...pick(item, ['fileId', 'fileName', 'testArea']),
            modifyFlowId: item?.flowId,
            modifyRetestHbinNumber: item?.retestHardBin,
            originalRetestHbinNumber: this.dataRecovery.originStepThreeData[index].retestHardBin,
            originalFlowId: this.dataRecovery.originStepThreeData[index].flowId,
          };
        });

      const repairFileInfos = {
        repairFieldInfoVos: this.dataRecovery.repairList,
        repairFileInfos: newList,
      };

      const res = await getStepFourData(repairFileInfos);
      if (res.data.code === 200 || res.data.code === '00000001') {
        this.dataRecovery.stepFourData = res.data.data?.map((item) => {
          return { ...item, stdfFileListStr: item?.fileNames.join(','), stdfFileListStrOne: item?.fileNames[0] };
        });
        return true;
      } else {
        ElMessage.error(res.data.msg);
        return false;
      }
    },
    async stepSaveFn() {
      const list = [...this.dataRecovery.stepThreeData];
      const newList = list
        ?.filter((item) => item.repairFile === true)
        ?.map((item, index) => {
          return {
            ...pick(item, ['fileId', 'fileName', 'testArea']),
            modifyFlowId: item?.flowId,
            modifyRetestHbinNumber: item?.retestHardBin,
            originalRetestHbinNumber: this.dataRecovery.originStepThreeData[index].retestHardBin,
            originalFlowId: this.dataRecovery.originStepThreeData[index].flowId,
          };
        });

      const repairFileInfos = {
        repairFieldInfoVos: this.dataRecovery.repairList,
        repairFileInfos: newList,
      };

      this.dataRecovery.resultLoading = true;
      const res = await stepSave(repairFileInfos);
      this.dataRecovery.resultLoading = false;

      this.dataRecovery.status = res.data.code === 200 || res.data.code === '00000001' ? true : false;
      if (res.data.code !== 200 && res.data.code !== '00000001') {
        this.dataRecovery.errorMsg = res.data.msg;
      }
      this.dataRecovery.resultDialog = true;
    },

    changeTableLoading() {
      this.dataRecovery.tableLoading = !this.dataRecovery.tableLoading;
    },
    setFileIds(val) {
      this.dataRecovery.fileIds = val;
    },
    setTitle(val) {
      this.dataRecovery.title = val;
    },
    setStepTwoData(val) {
      this.dataRecovery.stepTwoData = val;
    },
    setWidth(val) {
      this.dataRecovery.width = val;
    },
    setHeight(val) {
      this.dataRecovery.height = val;
    },
  },
});
