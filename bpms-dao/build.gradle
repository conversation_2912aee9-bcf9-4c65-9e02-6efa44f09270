description = 'bpms dao'

dependencies {
  implementation platform("org.springframework.boot:spring-boot-dependencies:$springBootVersion")
  api group: 'org.springframework.boot', name: 'spring-boot-starter-data-jpa'
  api group: 'org.mybatis.spring.boot', name: 'mybatis-spring-boot-starter', version: mybatisVersion

  api group: 'com.github.housepower', name: 'clickhouse-native-jdbc', version: clickhouseNativeJdbcVersion
  api group: 'com.guwave.onedata', name: 'dataware-common', version: datawareVersion
  api group: 'com.guwave.onedata', name: 'dataware-dao-mysql', version: datawareVersion
  api group: 'com.guwave.onedata', name: 'linkx-dao-mysql', version: linkxVersion
  api group: 'com.guwave.onedata', name: 'linkx-common', version: linkxVersion
  api group: 'com.guwave.onedata', name: 'next-compute-dao-mysql', version: nextComputeVersion
  api group: 'com.guwave.onedata', name: 'next-compute-common', version: nextComputeVersion
  implementation group: 'com.alibaba', name: 'fastjson', version: fastJsonVersion
  implementation group: 'com.fasterxml.jackson.core', name: 'jackson-databind'
  implementation project(':bpms-common')
}
