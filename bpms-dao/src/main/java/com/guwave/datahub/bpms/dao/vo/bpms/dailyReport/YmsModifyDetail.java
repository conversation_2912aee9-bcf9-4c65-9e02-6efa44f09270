package com.guwave.datahub.bpms.dao.vo.bpms.dailyReport;


import com.guwave.onedata.next.compute.common.constant.ProcessStatus;

public class YmsModifyDetail {
    private ProcessStatus processStatus;

    private Long modifyTaskCnt;

    private String modifyDuration;

    private String generateReportTime;

    public ProcessStatus getProcessStatus() {
        return processStatus;
    }

    public YmsModifyDetail setProcessStatus(ProcessStatus processStatus) {
        this.processStatus = processStatus;
        return this;
    }

    public Long getModifyTaskCnt() {
        return modifyTaskCnt;
    }

    public YmsModifyDetail setModifyTaskCnt(Long modifyTaskCnt) {
        this.modifyTaskCnt = modifyTaskCnt;
        return this;
    }

    public String getModifyDuration() {
        return modifyDuration;
    }

    public YmsModifyDetail setModifyDuration(String modifyDuration) {
        this.modifyDuration = modifyDuration;
        return this;
    }

    public String getGenerateReportTime() {
        return generateReportTime;
    }

    public YmsModifyDetail setGenerateReportTime(String generateReportTime) {
        this.generateReportTime = generateReportTime;
        return this;
    }
}
