package com.guwave.datahub.bpms.dao.converter;

import com.guwave.onedata.dataware.common.contant.ProcessStatus;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * ProcessStatusConverter
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2023-07-26 09:18:20
 */
@Converter(autoApply = true)
public class ProcessStatusConverter implements AttributeConverter<ProcessStatus, String> {

    @Override
    public String convertToDatabaseColumn(ProcessStatus status) {
        return null == status ? null : status.name();
    }

    @Override
    public ProcessStatus convertToEntityAttribute(String status) {
        return ProcessStatus.of(status);
    }
}
