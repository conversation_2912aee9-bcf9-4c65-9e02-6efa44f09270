package com.guwave.datahub.bpms.dao.vo.bpms.dailyReport;

public class ReplayTaskResult {
    private Long totalFileCnt;
    private Long successTaskCnt;
    private Long totalTaskCnt;

    public Long getTotalFileCnt() {
        return totalFileCnt;
    }

    public void setTotalFileCnt(Long totalFileCnt) {
        this.totalFileCnt = totalFileCnt;
    }

    public Long getSuccessTaskCnt() {
        return successTaskCnt;
    }

    public void setSuccessTaskCnt(Long successTaskCnt) {
        this.successTaskCnt = successTaskCnt;
    }


    public Long getTotalTaskCnt() {
        return totalTaskCnt;
    }

    public void setTotalTaskCnt(Long totalTaskCnt) {
        this.totalTaskCnt = totalTaskCnt;
    }
}
