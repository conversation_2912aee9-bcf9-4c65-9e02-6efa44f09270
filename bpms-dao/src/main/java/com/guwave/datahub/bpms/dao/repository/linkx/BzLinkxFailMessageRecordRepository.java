package com.guwave.datahub.bpms.dao.repository.linkx;

import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.linkx.dao.mysql.domain.bz.BzLinkxFailMessageRecord;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface BzLinkxFailMessageRecordRepository extends CrudRepository<BzLinkxFailMessageRecord, Long> {

    List<BzLinkxFailMessageRecord> findAllByProcessStatusAndDeleteFlag(ProcessStatus processStatus, Integer deleteFlag);
}
