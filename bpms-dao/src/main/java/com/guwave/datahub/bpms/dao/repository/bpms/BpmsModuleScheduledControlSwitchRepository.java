package com.guwave.datahub.bpms.dao.repository.bpms;


import com.guwave.datahub.bpms.dao.domain.ModuleScheduledControlSwitch;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import javax.transaction.Transactional;
import java.util.List;

public interface BpmsModuleScheduledControlSwitchRepository extends CrudRepository<ModuleScheduledControlSwitch, Long> {

    List<ModuleScheduledControlSwitch> findAllByModuleOrderByModule(String module);


    @Transactional
    @Modifying
    @Query("update com.guwave.datahub.bpms.dao.domain.ModuleScheduledControlSwitch a set a.stopFlag = :stopFLag , a.updateTime = now() where a.module in (:modules)")
    void updateStopFlag(@Param("modules") List<String> modules, @Param("stopFLag") Integer stopFLag);


    @Transactional
    @Modifying
    @Query("update com.guwave.datahub.bpms.dao.domain.ModuleScheduledControlSwitch a set a.stopFlag = :stopFLag , a.updateTime = now()")
    void updateAllStopFlag(@Param("stopFLag") Integer stopFLag);
}
