package com.guwave.datahub.bpms.dao.domain.dailyReport;

import com.guwave.datahub.bpms.common.constant.DataCode;
import com.guwave.datahub.bpms.common.constant.ReportModule;
import com.guwave.datahub.bpms.dao.converter.DataCodeConverter;
import com.guwave.datahub.bpms.dao.converter.ReportModuleConverter;

import javax.persistence.*;

import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "bz_daily_report_result")
public class DailyReportResult {
    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = IDENTITY)
    private Long id;

    @Column(name = "report_module")
    @Convert(converter = ReportModuleConverter.class)
    private ReportModule reportModule;

    @Column(name = "data_code")
    @Convert(converter = DataCodeConverter.class)
    private DataCode dataCode;

    @Column(name = "data", columnDefinition = "LONGTEXT")
    private String data;


    @Column(name = "day")
    private String day;

    @Column(name = "latest_flag")
    private Integer latestFlag;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "update_user")
    private String updateUser;

    public Long getId() {
        return id;
    }

    public DailyReportResult setId(Long id) {
        this.id = id;
        return this;
    }

    public ReportModule getReportModule() {
        return reportModule;
    }

    public DailyReportResult setReportModule(ReportModule reportModule) {
        this.reportModule = reportModule;
        return this;
    }

    public DataCode getDataCode() {
        return dataCode;
    }

    public DailyReportResult setDataCode(DataCode dataCode) {
        this.dataCode = dataCode;
        return this;
    }

    public String getData() {
        return data;
    }

    public DailyReportResult setData(String data) {
        this.data = data;
        return this;
    }

    public String getDay() {
        return day;
    }

    public DailyReportResult setDay(String day) {
        this.day = day;
        return this;
    }

    public Integer getLatestFlag() {
        return latestFlag;
    }

    public DailyReportResult setLatestFlag(Integer latestFlag) {
        this.latestFlag = latestFlag;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public DailyReportResult setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public DailyReportResult setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public DailyReportResult setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public DailyReportResult setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }
}
