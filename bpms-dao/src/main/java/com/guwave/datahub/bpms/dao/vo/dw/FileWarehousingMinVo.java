package com.guwave.datahub.bpms.dao.vo.dw;

import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import lombok.Builder;
import lombok.Data;

import java.util.Objects;

/**
 * 2025/5/15 14:21
 * WaferVo
 *
 * <AUTHOR>
 */
@Data
@Builder
public class FileWarehousingMinVo {
    private Long id;
    private String customer;
    private String subCustomer;
    private String factory;
    private String factorySite;
    private TestArea testArea;
    private FileCategory fileCategory;
    private String deviceId;
    private LotType lotType;
    private String testStage;
    private String lotId;
    private String waferNo;
    private String sblotId;
    private String fileName;

    public FileWarehousingMinVo() {
    }

    public FileWarehousingMinVo(Long id, String customer, String subCustomer, String factory, String factorySite, TestArea testArea, FileCategory fileCategory, String deviceId, LotType lotType, String testStage, String lotId, String waferNo, String sblotId, String fileName) {
        this.id = id;
        this.customer = customer;
        this.subCustomer = subCustomer;
        this.factory = factory;
        this.factorySite = factorySite;
        this.testArea = testArea;
        this.fileCategory = fileCategory;
        this.deviceId = deviceId;
        this.lotType = lotType;
        this.testStage = testStage;
        this.lotId = lotId;
        this.waferNo = waferNo;
        this.sblotId = sblotId;
        this.fileName = fileName;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        FileWarehousingMinVo that = (FileWarehousingMinVo) o;
        return Objects.equals(id, that.id) && Objects.equals(customer, that.customer) && Objects.equals(subCustomer, that.subCustomer) && Objects.equals(factory, that.factory) && Objects.equals(factorySite, that.factorySite) && testArea == that.testArea && fileCategory == that.fileCategory && Objects.equals(deviceId, that.deviceId) && lotType == that.lotType && Objects.equals(testStage, that.testStage) && Objects.equals(lotId, that.lotId) && Objects.equals(waferNo, that.waferNo) && Objects.equals(sblotId, that.sblotId) && Objects.equals(fileName, that.fileName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, customer, subCustomer, factory, factorySite, testArea, fileCategory, deviceId, lotType, testStage, lotId, waferNo, sblotId, fileName);
    }
}
