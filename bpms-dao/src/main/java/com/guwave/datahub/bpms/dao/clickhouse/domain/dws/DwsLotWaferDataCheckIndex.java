package com.guwave.datahub.bpms.dao.clickhouse.domain.dws;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 2025/4/28 10:20
 * DwsLotWaferDataCheckIndex
 *
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "dws_lot_wafer_data_check_index_cluster")
public class DwsLotWaferDataCheckIndex {

    @Id
    @Column(name = "CUSTOMER")
    private String customer;

    @Column(name = "SUB_CUSTOMER")
    private String subCustomer;

    @Column(name = "UPLOAD_TYPE")
    private String uploadType;

    @Column(name = "FILE_ID")
    private Integer fileId;

    @Column(name = "FILE_NAME")
    private String fileName;

    @Column(name = "FACTORY")
    private String factory;

    @Column(name = "FACTORY_SITE")
    private String factorySite;

    @Column(name = "FAB")
    private String fab;

    @Column(name = "FAB_SITE")
    private String fabSite;

    @Column(name = "TEST_AREA")
    private String testArea;

    @Column(name = "TEST_STAGE")
    private String testStage;

    @Column(name = "LOT_TYPE")
    private String lotType;

    @Column(name = "DEVICE_ID")
    private String deviceId;

    @Column(name = "LOT_ID")
    private String lotId;

    @Column(name = "PROCESS")
    private String process;

    @Column(name = "SBLOT_ID")
    private String sblotId;

    @Column(name = "WAFER_ID")
    private String waferId;

    @Column(name = "WAFER_ID_KEY")
    private String waferIdKey;

    @Column(name = "WAFER_NO")
    private String waferNo;

    @Column(name = "WAFER_NO_KEY")
    private String waferNoKey;

    @Column(name = "MES_FILE_NAME")
    private String mesFileName;

    @Column(name = "LOSS_CNT")
    private Integer lossCnt;

    @Column(name = "DAMAGE_CNT")
    private Integer damageCnt;

    @Column(name = "TOTAL_CNT")
    private Integer totalCnt;

    @Column(name = "PASS_CNT")
    private Integer passCnt;

    @Column(name = "FAIL_CNT")
    private Integer failCnt;

    @Column(name = "START_TIME")
    private LocalDateTime startTime;

    @Column(name = "END_TIME")
    private LocalDateTime endTime;

    @Column(name = "START_HOUR_KEY")
    private String startHourKey;

    @Column(name = "START_DAY_KEY")
    private String startDayKey;

    @Column(name = "END_HOUR_KEY")
    private String endHourKey;

    @Column(name = "END_DAY_KEY")
    private String endDayKey;

//    @Column(name = "MES_MATCH_TYPE_DETAIL")
//    private Map<String, Integer> mesMatchTypeDetail;

    @Column(name = "MES_MATCH_TYPE")
    private String mesMatchType;

    @Column(name = "B2G_DIE")
    private String b2gDie;

    @Column(name = "G2B_DIE")
    private String g2bDie;

    @Column(name = "G2L_DIE")
    private String g2lDie;

    @Column(name = "B2L_DIE")
    private String b2lDie;

    @Column(name = "CREATE_HOUR_KEY")
    private String createHourKey;

    @Column(name = "CREATE_DAY_KEY")
    private String createDayKey;

    @Column(name = "CREATE_TIME")
    private LocalDateTime createTime;

    @Column(name = "CREATE_USER")
    private String createUser;

    @Column(name = "UPLOAD_TIME")
    private LocalDateTime uploadTime;

    @Column(name = "VERSION")
    private Long version;

    @Column(name = "IS_DELETE")
    private Byte isDelete;
}
