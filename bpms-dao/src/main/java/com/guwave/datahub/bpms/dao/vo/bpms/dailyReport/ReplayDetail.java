package com.guwave.datahub.bpms.dao.vo.bpms.dailyReport;

public class ReplayDetail {
    private String day;
    private Long totalTaskCnt;
    private Long successTaskCnt;
    private Long totalFileCnt;

    public ReplayDetail(String day, Long totalTaskCnt, Long successTaskCnt, Long totalFileCnt) {
        this.day = day;
        this.totalTaskCnt = totalTaskCnt;
        this.successTaskCnt = successTaskCnt;
        this.totalFileCnt = totalFileCnt;
    }

    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public Long getTotalTaskCnt() {
        return totalTaskCnt;
    }

    public void setTotalTaskCnt(Long totalTaskCnt) {
        this.totalTaskCnt = totalTaskCnt;
    }

    public Long getSuccessTaskCnt() {
        return successTaskCnt;
    }

    public void setSuccessTaskCnt(Long successTaskCnt) {
        this.successTaskCnt = successTaskCnt;
    }

    public Long getTotalFileCnt() {
        return totalFileCnt;
    }

    public void setTotalFileCnt(Long totalFileCnt) {
        this.totalFileCnt = totalFileCnt;
    }
}
