package com.guwave.datahub.bpms.dao.repository.bpms;

import com.guwave.datahub.bpms.dao.domain.dailySummary.DailySummary;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DailySummaryRepository extends CrudRepository<DailySummary, Long> {

    List<DailySummary> findAllByOrderByDayDesc(Pageable pageable);

    List<DailySummary> findAllByDay(String day);
}
