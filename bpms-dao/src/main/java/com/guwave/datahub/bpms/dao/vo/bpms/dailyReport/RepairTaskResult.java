package com.guwave.datahub.bpms.dao.vo.bpms.dailyReport;

public class RepairTaskResult {
    private Long successFileCnt;
    private Long totalFileCnt;
    private Long successLotWaferCnt;
    private Long totalLotWaferCnt;

    public Long getSuccessFileCnt() {
        return successFileCnt;
    }

    public void setSuccessFileCnt(Long successFileCnt) {
        this.successFileCnt = successFileCnt;
    }

    public Long getTotalFileCnt() {
        return totalFileCnt;
    }

    public void setTotalFileCnt(Long totalFileCnt) {
        this.totalFileCnt = totalFileCnt;
    }

    public Long getSuccessLotWaferCnt() {
        return successLotWaferCnt;
    }

    public void setSuccessLotWaferCnt(Long successLotWaferCnt) {
        this.successLotWaferCnt = successLotWaferCnt;
    }

    public Long getTotalLotWaferCnt() {
        return totalLotWaferCnt;
    }

    public void setTotalLotWaferCnt(Long totalLotWaferCnt) {
        this.totalLotWaferCnt = totalLotWaferCnt;
    }
}
