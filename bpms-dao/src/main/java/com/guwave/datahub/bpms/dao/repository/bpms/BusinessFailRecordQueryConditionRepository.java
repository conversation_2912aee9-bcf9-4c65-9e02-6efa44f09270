package com.guwave.datahub.bpms.dao.repository.bpms;

import com.guwave.datahub.bpms.dao.domain.retryFailRecord.BusinessFailRecordQueryCondition;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;


@Repository
public interface BusinessFailRecordQueryConditionRepository extends CrudRepository<BusinessFailRecordQueryCondition, Long>, JpaSpecificationExecutor<BusinessFailRecordQueryCondition> {

}
