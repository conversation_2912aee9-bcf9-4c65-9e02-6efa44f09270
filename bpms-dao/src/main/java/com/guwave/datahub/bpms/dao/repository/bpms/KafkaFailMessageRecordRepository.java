package com.guwave.datahub.bpms.dao.repository.bpms;

import com.guwave.datahub.bpms.dao.domain.processMessage.KafkaFailMessageRecord;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface KafkaFailMessageRecordRepository extends CrudRepository<KafkaFailMessageRecord, Long> {

    List<KafkaFailMessageRecord> findAllByProcessStatusAndDeleteFlagAndFailCntLessThanOrderByCreateTime(ProcessStatus processStatus, Integer deleteFlag, Integer failCnt);

}
