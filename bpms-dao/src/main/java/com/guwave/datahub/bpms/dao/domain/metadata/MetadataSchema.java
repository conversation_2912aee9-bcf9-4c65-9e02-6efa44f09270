package com.guwave.datahub.bpms.dao.domain.metadata;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Data
@Entity
@Table(name = "bz_metadata_schema")
public class MetadataSchema implements Serializable {

    private static final long serialVersionUID = -5662857690040939642L;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @Column(name = "datasource")
    private String datasource;

    @Column(name = "database_name")
    private String databaseName;

    @Column(name = "table_name")
    private String tableName;

    @Column(name = "field_name")
    private String fieldName;

    @Column(name = "field_type")
    private String fieldType;

    @Column(name = "default_value")
    private String defaultValue;

    @Column(name = "position")
    private Integer position;

    @Column(name = "field_comment")
    private String fieldComment;

    @Column(name = "cp_logic")
    @Lob
    private String cpLogic;

    @Column(name = "ft_logic")
    @Lob
    private String ftLogic;

    @Column(name = "info")
    @Lob
    private String info;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;
}
