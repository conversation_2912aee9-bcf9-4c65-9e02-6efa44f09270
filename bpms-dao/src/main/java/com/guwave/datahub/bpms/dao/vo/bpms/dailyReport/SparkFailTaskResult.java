package com.guwave.datahub.bpms.dao.vo.bpms.dailyReport;

import java.util.Map;

public class SparkFailTaskResult {
    private Long failTaskCnt;
    private Map<String, Long> ExceptionTypeMap;

    public Long getFailTaskCnt() {
        return failTaskCnt;
    }

    public void setFailTaskCnt(Long failTaskCnt) {
        this.failTaskCnt = failTaskCnt;
    }

    public Map<String, Long> getExceptionTypeMap() {
        return ExceptionTypeMap;
    }

    public void setExceptionTypeMap(Map<String, Long> exceptionTypeMap) {
        ExceptionTypeMap = exceptionTypeMap;
    }
}
