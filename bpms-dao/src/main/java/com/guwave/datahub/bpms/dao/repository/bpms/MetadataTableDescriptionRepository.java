package com.guwave.datahub.bpms.dao.repository.bpms;

import com.guwave.datahub.bpms.dao.domain.metadata.MetadataSchema;
import com.guwave.datahub.bpms.dao.domain.metadata.MetadataTableDescription;
import com.guwave.datahub.bpms.dao.domain.metadata.MetadataTableLink;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * MetadataTableDescriptionRepository
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-03-13 17:02:05
 */
@Repository
public interface MetadataTableDescriptionRepository extends JpaRepository<MetadataTableDescription, Long> {

    @Query("select t from MetadataTableDescription t where t.datasource=:source and t.databaseName=:database and t.tableName=:table")
    Optional<MetadataTableDescription> findByTable(@Param("source") String source, @Param("database") String database, @Param("table") String table);

}
