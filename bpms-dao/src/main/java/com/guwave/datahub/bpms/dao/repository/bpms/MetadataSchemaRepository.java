package com.guwave.datahub.bpms.dao.repository.bpms;

import com.guwave.datahub.bpms.dao.domain.metadata.MetadataSchema;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * MetadataSchemaRepository
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-03-13 17:02:05
 */
@Repository
public interface MetadataSchemaRepository extends JpaRepository<MetadataSchema, Long> {

    @Query("select distinct t.databaseName from MetadataSchema t where t.datasource=:source")
    List<String> getDatabases(@Param("source") String source);

    @Query("select distinct t.tableName from MetadataSchema t where t.datasource=:source and t.databaseName=:database")
    List<String> getTableNames(@Param("source") String source, @Param("database") String database);

    @Query("select t from MetadataSchema t where t.datasource=:source and t.databaseName=:database and t.tableName=:table order by t.position")
    List<MetadataSchema> findByTable(@Param("source") String source, @Param("database") String database, @Param("table") String table);

    @Query("select t from MetadataSchema t where t.datasource=:source and t.databaseName=:database and t.tableName=:table order by t.position")
    Page<MetadataSchema> findByTable(@Param("source") String source, @Param("database") String database, @Param("table") String table, Pageable pageable);

    Integer countByDatasource(String datasource);
}
