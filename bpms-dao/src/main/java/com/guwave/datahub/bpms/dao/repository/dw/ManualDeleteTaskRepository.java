package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualDeleteTask;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Repository("BpmsManualDeleteTaskRepository")
public interface ManualDeleteTaskRepository extends CrudRepository<ManualDeleteTask, Long> {
    List<ManualDeleteTask> findByProcessStatusOrderByIdAsc(ProcessStatus processStatus);

    @Transactional
    @Modifying
    @Query(value = "update dw_manual_delete_task\n" +
            "set process_status = 'CREATE'\n" +
            "where id in (select *\n" +
            "             from (select id from dw_manual_delete_task where process_status = 'PROCESSING') x)", nativeQuery = true)
    void updateProcessingToCreate();
}
