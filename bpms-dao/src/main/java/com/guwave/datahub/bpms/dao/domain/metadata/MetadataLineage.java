package com.guwave.datahub.bpms.dao.domain.metadata;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Data
@Entity
@Table(name = "bz_metadata_lineage")
public class MetadataLineage implements Serializable {

    private static final long serialVersionUID = -777492787372967536L;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    /**
     * 起点数据源
     */
    @Column(name = "from_source")
    private String fromSource;

    /**
     * 起点库名
     */
    @Column(name = "from_database")
    private String fromDatabase;

    /**
     * 起点表名
     */
    @Column(name = "from_table")
    private String fromTable;

    /**
     * 起点字段名
     */
    @Column(name = "from_field")
    private String fromField;

    /**
     * 终点数据源
     */
    @Column(name = "to_source")
    private String toSource;

    /**
     * 终点库名
     */
    @Column(name = "to_database")
    private String toDatabase;

    /**
     * 终点表名
     */
    @Column(name = "to_table")
    private String toTable;

    /**
     * 终点字段名
     */
    @Column(name = "to_field")
    private String toField;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;
    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;
}
