package com.guwave.datahub.bpms.dao.vo.bpms.dailyReport;

import com.guwave.onedata.dataware.common.contant.ManualType;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualCalculateTask;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Objects;

@AllArgsConstructor
@NoArgsConstructor
public class ManualTaskDetail {
    private String manualType;
    private String processStatus;

    public String getManualType() {
        return manualType;
    }

    public void setManualType(String manualType) {
        this.manualType = manualType;
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ManualTaskDetail that = (ManualTaskDetail) o;
        return Objects.equals(manualType, that.manualType) && Objects.equals(processStatus, that.processStatus);
    }

    @Override
    public int hashCode() {
        return Objects.hash(manualType, processStatus);
    }

    public static ManualTaskDetail buildManualTaskDetail(ManualCalculateTask manualCalculateTask) {
        ManualTaskDetail manualTaskDetail = new ManualTaskDetail();
        manualTaskDetail.setManualType(manualCalculateTask.getManualType().getType());
        manualTaskDetail.setProcessStatus(manualCalculateTask.getProcessStatus().name());
        return manualTaskDetail;
    }
}
