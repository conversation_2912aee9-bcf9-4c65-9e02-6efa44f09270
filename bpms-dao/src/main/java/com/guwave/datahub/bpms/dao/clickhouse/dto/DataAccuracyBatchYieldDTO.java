package com.guwave.datahub.bpms.dao.clickhouse.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 2025/4/30 10:34
 * DataAccuracyDTO
 *
 * <AUTHOR>
 */
@Data
public class DataAccuracyBatchYieldDTO {
    private String deviceName;
    private String testStage;
    private String testArea;
    private String lotId;
    private String sbLotId;
    private String waferId;
    private String waferNo;
    private String lotType;
    private String accuracyCheck;
    private BigDecimal firstYield;
    private BigDecimal finalYield;
    private Long inputCount;
    private Long passCount;
    private Long stdFileCount;
    private String matchFlag;
    private String stdStartTime;
}
