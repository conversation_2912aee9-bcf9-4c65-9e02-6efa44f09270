package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotWaferWarehousingRecord;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;

@Repository("BpmsLotWaferWarehousingRecordRepository")
public interface LotWaferWarehousingRecordRepository extends CrudRepository<LotWaferWarehousingRecord, Long> {

    @Query(nativeQuery = true, value = "" +
            "select b.id\n" +
            "from (select customer,\n" +
            "             factory,\n" +
            "             device_id,\n" +
            "             test_area,\n" +
            "             lot_id,\n" +
            "             wafer_no,\n" +
            "             test_stage,\n" +
            "             lot_type,\n" +
            "             file_category\n" +
            "      from dw_warehousing_task_record\n" +
            "      where id in :warehousingTaskRecordIds) a\n" +
            "         join dw_lot_wafer_warehousing_record b\n" +
            "              on a.customer = b.customer and a.factory = b.factory and a.device_id = b.device_id and\n" +
            "                 a.test_area = b.test_area and a.lot_id = b.lot_id and a.wafer_no = b.wafer_no and\n" +
            "                 a.test_stage = b.test_stage and a.lot_type = b.lot_type and a.file_category = b.file_category\n" +
            "group by b.id")
    List<Long> queryWithWarehousingTaskRecordIds(@Param("warehousingTaskRecordIds") List<Long> warehousingTaskRecordIds);

    @Transactional
    @Modifying
    @Query("update LotWaferWarehousingRecord a set a.freezeFlag = :freezeFlag,a.updateTime = now() where a.id in :ids")
    void updateFreezeFlag(@Param("ids") List ids, @Param("freezeFlag") Integer freezeFlag);

    List<LotWaferWarehousingRecord> findAllByCustomerAndTestAreaAndFactoryAndDeviceIdAndLotTypeAndTestStageAndLotIdAndWaferNoAndFileCategory(String customer, TestArea testArea, String factory, String deviceId, LotType lotType, String testStage, String lotId, String waferNo, FileCategory fileCategory);

}
