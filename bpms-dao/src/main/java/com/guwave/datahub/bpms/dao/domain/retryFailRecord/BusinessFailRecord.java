package com.guwave.datahub.bpms.dao.domain.retryFailRecord;

import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.dao.mysql.converter.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "bz_business_fail_record")
public class BusinessFailRecord implements Serializable {

    private static final long serialVersionUID = -463842765430431198L;

    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    /**
     * 数据库名
     */
    @Column(name = "db")
    private String db;

    /**
     * 表名
     */
    @Column(name = "`table`")
    private String table;

    /**
     * 表里面的那条记录的id
     */
    @Column(name = "record_id")
    private Long recordId;

    /**
     * 记录的重试次数
     */
    @Column(name = "record_retry_cnt")
    private Integer recordRetryCnt = 0;

    /**
     * 记录的计算状态
     */
    @Column(name = "record_process_status")
    @Convert(converter = ProcessStatusConverter.class)
    private ProcessStatus recordProcessStatus = ProcessStatus.FAIL;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;
    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

}
