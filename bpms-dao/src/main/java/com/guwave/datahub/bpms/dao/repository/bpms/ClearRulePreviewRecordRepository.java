package com.guwave.datahub.bpms.dao.repository.bpms;

import com.guwave.datahub.bpms.dao.domain.clearRulePreview.ClearRulePreviewRecord;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.List;


@Repository
public interface ClearRulePreviewRecordRepository extends CrudRepository<ClearRulePreviewRecord, Long>, JpaSpecificationExecutor<ClearRulePreviewRecord> {

    @Query(value = "select case " +
            "           when :filterField = 'TEST_AREA' then test_area " +
            "           when :filterField = 'DEVICE_ID' then device_id " +
            "           when :filterField = 'LOT_ID' then lot_id " +
            "           when :filterField = 'WAFER_NO' then wafer_no " +
            "           when :filterField = 'LOT_TYPE' then lot_type " +
            "           when :filterField = 'TEST_STAGE' then test_stage " +
            "           when :filterField = 'FACTORY' then factory " +
            "           when :filterField = 'FACTORY_SITE' then factory_site " +
            "           when :filterField = 'SBLOT_ID' then sblot_id " +
            "           when :filterField = 'EXCEPTION_TYPE' then exception_type " +
            "           end as result " +
            "from bz_clear_rule_preview_record " +
            "      where customer = :customer and delete_flag = 0  " +
            "        and (:testArea IS NULL OR test_area = :testArea)  " +
            "        and (:deviceId IS NULL OR device_id = :deviceId)  " +
            "        and (:lotId IS NULL OR lot_id = :lotId)  " +
            "        and (:waferNo IS NULL OR wafer_no = :waferNo)  " +
            "        and (:lotType IS NULL OR lot_type = :lotType)  " +
            "        and (:testStage IS NULL OR test_stage = :testStage)  " +
            "        and (:factory IS NULL OR factory = :factory)  " +
            "        and (:factorySite IS NULL OR factory_site = :factorySite)  " +
            "        and (:sblotId IS NULL OR sblot_id = :sblotId)  " +
            "        and (:exceptionType IS NULL OR exception_type = :exceptionType)  " +
            "group by result " +
            "order by result ",
            nativeQuery = true)
    List<String> filterDropDown(
            @Param("customer") String customer,
            @Param("testArea") String testArea,
            @Param("deviceId") String deviceId,
            @Param("lotId") String lotId,
            @Param("waferNo") String waferNo,
            @Param("lotType") String lotType,
            @Param("testStage") String testStage,
            @Param("factory") String factory,
            @Param("factorySite") String factorySite,
            @Param("sblotId") String sblotId,
            @Param("exceptionType") String exceptionType,
            @Param("filterField") String filterField
    );

    @Query(value = "select * from bz_clear_rule_preview_record" +
            "      where customer = :customer and delete_flag = 0  " +
            "        and (:testArea IS NULL OR test_area = :testArea)  " +
            "        and (:deviceId IS NULL OR device_id = :deviceId)  " +
            "        and (:lotId IS NULL OR lot_id = :lotId)  " +
            "        and (:waferNo IS NULL OR wafer_no = :waferNo)  " +
            "        and (:lotType IS NULL OR lot_type = :lotType)  " +
            "        and (:testStage IS NULL OR test_stage = :testStage)  " +
            "        and (:factory IS NULL OR factory = :factory)  " +
            "        and (:factorySite IS NULL OR factory_site = :factorySite)  " +
            "        and (:sblotId IS NULL OR sblot_id = :sblotId)  " +
            "        and (:fileCategory IS NULL OR file_category = :fileCategory)  " +
            "        and (:processStatus IS NULL OR process_status = :processStatus)  " +
            "        and (:remoteOriginFilePath IS NULL OR remote_origin_file_path like concat('%',:remoteOriginFilePath,'%'))  " +
            "        and (:remoteOriginFileName IS NULL OR remote_origin_file_name like concat('%',:remoteOriginFileName,'%'))  " +
            "        and (:fileName IS NULL OR file_name like concat('%',:fileName,'%'))  " +
            "        and (:startTime IS NULL OR update_time >= :startTime)  " +
            "        and (:endTime IS NULL OR update_time <= :endTime)  " +
            "        and (:exceptionType IS NULL OR exception_type = :exceptionType)  " +
            "        order by update_time desc ,id"
            ,
            countQuery = "select count(1) from bz_clear_rule_preview_record" +
                    "      where customer = :customer and delete_flag = 0  " +
                    "        and (:testArea IS NULL OR test_area = :testArea)  " +
                    "        and (:deviceId IS NULL OR device_id = :deviceId)  " +
                    "        and (:lotId IS NULL OR lot_id = :lotId)  " +
                    "        and (:waferNo IS NULL OR wafer_no = :waferNo)  " +
                    "        and (:lotType IS NULL OR lot_type = :lotType)  " +
                    "        and (:testStage IS NULL OR test_stage = :testStage)  " +
                    "        and (:factory IS NULL OR factory = :factory)  " +
                    "        and (:factorySite IS NULL OR factory_site = :factorySite)  " +
                    "        and (:sblotId IS NULL OR sblot_id = :sblotId)  " +
                    "        and (:fileCategory IS NULL OR file_category = :fileCategory)  " +
                    "        and (:processStatus IS NULL OR process_status = :processStatus)  " +
                    "        and (:remoteOriginFilePath IS NULL OR remote_origin_file_path like concat('%',:remoteOriginFilePath,'%'))  " +
                    "        and (:remoteOriginFileName IS NULL OR remote_origin_file_name like concat('%',:remoteOriginFileName,'%'))  " +
                    "        and (:fileName IS NULL OR file_name like concat('%',:fileName,'%'))  " +
                    "        and (:startTime IS NULL OR update_time >= :startTime)  " +
                    "        and (:endTime IS NULL OR update_time <= :endTime)  " +
                    "        and (:exceptionType IS NULL OR exception_type = :exceptionType)  "
            ,
            nativeQuery = true)
    Page<ClearRulePreviewRecord> queryRecord(
            @Param("customer") String customer,
            @Param("testArea") String testArea,
            @Param("deviceId") String deviceId,
            @Param("lotId") String lotId,
            @Param("waferNo") String waferNo,
            @Param("lotType") String lotType,
            @Param("testStage") String testStage,
            @Param("factory") String factory,
            @Param("factorySite") String factorySite,
            @Param("sblotId") String sblotId,
            @Param("fileCategory") String fileCategory,
            @Param("processStatus") String processStatus,
            @Param("remoteOriginFilePath") String remoteOriginFilePath,
            @Param("remoteOriginFileName") String remoteOriginFileName,
            @Param("fileName") String fileName,
            @Param("exceptionType") String exceptionType,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime,
            Pageable pageable
    );

    List<ClearRulePreviewRecord> findByIdInAndDeleteFlag(Collection<Long> id, Integer deleteFlag);

    List<ClearRulePreviewRecord> findByProcessStatusAndDeleteFlagOrderByUpdateTimeAscIdAsc(ProcessStatus processStatus, Integer deleteFlag, Pageable pageable);


    @Transactional
    @Modifying
    @Query("update ClearRulePreviewRecord a set a.processStatus = 'PROCESSING',a.updateTime = now() where a.id = :id and a.processStatus = 'CREATE' and a.deleteFlag = 0")
    int updateProcessing(@Param("id") Long id);
}
