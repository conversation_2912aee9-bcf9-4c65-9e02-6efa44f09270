package com.guwave.datahub.bpms.dao.domain.warehousingRule;

import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.dao.mysql.converter.FileCategoryConverter;
import com.guwave.onedata.dataware.dao.mysql.converter.TestAreaConverter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "bz_warehousing_rule_flow")
public class WarehousingRuleFlow implements Serializable {

    private static final long serialVersionUID = -8889528434552477591L;
    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    /**
     * 流程名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 客户
     */
    @Column(name = "customer")
    private String customer;

    /**
     * 子客户
     */
    @Column(name = "sub_customer")
    private String subCustomer;

    /**
     * 工厂
     */
    @Column(name = "factory")
    private String factory;

    /**
     * 测试大阶段：CP/FT/WAT
     */
    @Column(name = "test_area")
    @Convert(converter = TestAreaConverter.class)
    private TestArea testArea;

    /**
     * 文件类型，比如STDF、RAW_DATA等
     */
    @Column(name = "file_category")
    @Convert(converter = FileCategoryConverter.class)
    private FileCategory fileCategory;

    /**
     * 备注
     */
    @Column(name = "remarks")
    private String remarks;

    /**
     * 删除标记：0->有效,1->删除
     */
    @Column(name = "delete_flag")
    @Builder.Default
    private Integer deleteFlag = 0;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    @Builder.Default
    private String createUser = "System";

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    @Builder.Default
    private String updateUser = "System";
}