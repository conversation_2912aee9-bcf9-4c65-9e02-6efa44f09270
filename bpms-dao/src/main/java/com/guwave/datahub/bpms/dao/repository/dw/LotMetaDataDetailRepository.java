package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.datahub.bpms.dao.projection.RankedLotMetaDataDetail;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotMetaDataDetail;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.data.repository.query.QueryByExampleExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;


@Repository("BpmsLotMetaDataDetailRepository")
public interface LotMetaDataDetailRepository extends CrudRepository<LotMetaDataDetail, Long>, QueryByExampleExecutor<LotMetaDataDetail> {
    /**
     * 查询自动入库可以调整优先级的数据
     * 需要从dw_lot_stocking_detail中过滤掉continue_flag=0的
     */
    @Query(value = "SELECT " +
            "x.min_id id, " +
            "x.test_area testArea, " +
            "x.factory, " +
            "x.device_id deviceId, " +
            "x.file_category fileCategory, " +
            "x.lot_id lotId, " +
            "x.wafer_no waferNo, " +
            "x.lot_type lotType, " +
            "x.test_stage testStage, " +
            "x.max_priority priority, " +
            "x.min_create_time createTime, " +
            "x.min_update_time updateTime, " +
            "x.rank_num rankNum " +
            "FROM (" +
            "  SELECT a.*, ROW_NUMBER() OVER (ORDER BY max_priority DESC, min_update_time) AS rank_num FROM (" +
            "    SELECT " +
            "      min(id) min_id, " +
            "      customer, " +
            "      sub_customer, " +
            "      factory, " +
            "      factory_site, " +
            "      device_id, " +
            "      test_area, " +
            "      lot_id, " +
            "      wafer_no, " +
            "      test_stage, " +
            "      lot_type, " +
            "      file_category, " +
            "      MAX(priority) AS max_priority, " +
            "      MIN(update_time) AS min_update_time, " +
            "      MIN(create_time) AS min_create_time " +
            "    FROM dw_lot_meta_data_detail d1 " +
            "    WHERE process_status = 'CREATE' " +
            "      AND NOT EXISTS ( " +
            "        SELECT 1 " +
            "        FROM dw_lot_meta_data_detail d2 " +
            "        WHERE d2.customer = d1.customer " +
            "          AND d2.sub_customer = d1.sub_customer " +
            "          AND d2.factory = d1.factory " +
            "          AND d2.factory_site = d1.factory_site " +
            "          AND d2.device_id = d1.device_id " +
            "          AND d2.test_area = d1.test_area " +
            "          AND d2.lot_id = d1.lot_id " +
            "          AND d2.wafer_no = d1.wafer_no " +
            "          AND d2.test_stage = d1.test_stage " +
            "          AND d2.lot_type = d1.lot_type " +
            "          AND d2.file_category = d1.file_category " +
            "          AND d2.process_status = 'PROCESSING') " +
            "    GROUP BY customer, sub_customer, factory, factory_site, device_id, test_area, " +
            "             lot_id, wafer_no, test_stage, lot_type, file_category" +
            "  ) a LEFT JOIN (" +
            "    SELECT customer, sub_customer, factory, factory_site, device_id, test_area, " +
            "           lot_id, wafer_no, test_stage, lot_type, file_category " +
            "    FROM dw_lot_stocking_detail " +
            "    WHERE continue_flag = 0 " +
            "    GROUP BY customer, sub_customer, factory, factory_site, device_id, test_area, " +
            "             lot_id, wafer_no, test_stage, lot_type, file_category" +
            "  ) b ON a.customer = b.customer " +
            "    AND a.sub_customer = b.sub_customer " +
            "    AND a.factory = b.factory " +
            "    AND a.factory_site = b.factory_site " +
            "    AND a.device_id = b.device_id " +
            "    AND a.test_area = b.test_area " +
            "    AND a.lot_id = b.lot_id " +
            "    AND a.wafer_no = b.wafer_no " +
            "    AND a.test_stage = b.test_stage " +
            "    AND a.lot_type = b.lot_type " +
            "    AND a.file_category = b.file_category " +
            "  WHERE b.lot_id IS NULL " +
            ") x " +
            "WHERE (:testArea IS NULL OR x.test_area = :testArea) " +
            "AND (:factory IS NULL OR x.factory = :factory) " +
            "AND (:deviceId IS NULL OR x.device_id = :deviceId) " +
            "AND (:fileCategory IS NULL OR x.file_category = :fileCategory) " +
            "AND (:lotId IS NULL OR x.lot_id = :lotId) " +
            "AND (:waferNo IS NULL OR x.wafer_no = :waferNo) " +
            "AND (:lotType IS NULL OR x.lot_type = :lotType)" +
            "ORDER BY rank_num",
            countQuery = "SELECT COUNT(*) FROM (" +
                    "  SELECT a.* FROM (" +
                    "    SELECT customer, sub_customer, factory, factory_site, device_id, test_area, " +
                    "           lot_id, wafer_no, test_stage, lot_type, file_category, " +
                    "           MAX(priority) AS priority " +
                    "    FROM dw_lot_meta_data_detail d1 " +
                    "    WHERE process_status = 'CREATE' " +
                    "      AND NOT EXISTS ( " +
                    "        SELECT 1 " +
                    "        FROM dw_lot_meta_data_detail d2 " +
                    "        WHERE d2.customer = d1.customer " +
                    "          AND d2.sub_customer = d1.sub_customer " +
                    "          AND d2.factory = d1.factory " +
                    "          AND d2.factory_site = d1.factory_site " +
                    "          AND d2.device_id = d1.device_id " +
                    "          AND d2.test_area = d1.test_area " +
                    "          AND d2.lot_id = d1.lot_id " +
                    "          AND d2.wafer_no = d1.wafer_no " +
                    "          AND d2.test_stage = d1.test_stage " +
                    "          AND d2.lot_type = d1.lot_type " +
                    "          AND d2.file_category = d1.file_category " +
                    "          AND d2.process_status = 'PROCESSING') " +
                    "    GROUP BY customer, sub_customer, factory, factory_site, device_id, test_area, " +
                    "             lot_id, wafer_no, test_stage, lot_type, file_category" +
                    "  ) a LEFT JOIN (" +
                    "    SELECT customer, sub_customer, factory, factory_site, device_id, test_area, " +
                    "           lot_id, wafer_no, test_stage, lot_type, file_category " +
                    "    FROM dw_lot_stocking_detail " +
                    "    WHERE continue_flag = 0 " +
                    "    GROUP BY customer, sub_customer, factory, factory_site, device_id, test_area, " +
                    "             lot_id, wafer_no, test_stage, lot_type, file_category" +
                    "  ) b ON a.customer = b.customer " +
                    "    AND a.sub_customer = b.sub_customer " +
                    "    AND a.factory = b.factory " +
                    "    AND a.factory_site = b.factory_site " +
                    "    AND a.device_id = b.device_id " +
                    "    AND a.test_area = b.test_area " +
                    "    AND a.lot_id = b.lot_id " +
                    "    AND a.wafer_no = b.wafer_no " +
                    "    AND a.test_stage = b.test_stage " +
                    "    AND a.lot_type = b.lot_type " +
                    "    AND a.file_category = b.file_category " +
                    "  WHERE b.lot_id IS NULL" +
                    ") x " +
                    "WHERE (:testArea IS NULL OR x.test_area = :testArea) " +
                    "AND (:factory IS NULL OR x.factory = :factory) " +
                    "AND (:deviceId IS NULL OR x.device_id = :deviceId) " +
                    "AND (:fileCategory IS NULL OR x.file_category = :fileCategory) " +
                    "AND (:lotId IS NULL OR x.lot_id = :lotId) " +
                    "AND (:waferNo IS NULL OR x.wafer_no = :waferNo) " +
                    "AND (:lotType IS NULL OR x.lot_type = :lotType)",
            nativeQuery = true)
    Page<RankedLotMetaDataDetail> findAutoTasksByConditions(
            @Param("testArea") String testArea,
            @Param("factory") String factory,
            @Param("deviceId") String deviceId,
            @Param("fileCategory") String fileCategory,
            @Param("lotId") String lotId,
            @Param("waferNo") String waferNo,
            @Param("lotType") String lotType,
            Pageable pageable
    );

    List<LotMetaDataDetail> findByProcessStatusOrderByIdDesc(ProcessStatus processStatus);


    List<LotMetaDataDetail> findByProcessStatusAndFactoryAndFactorySiteAndLotTypeAndTestAreaAndFileCategory(ProcessStatus processStatus, String factory, String factorySite, LotType lotType, TestArea testArea, FileCategory fileCategory);

    List<LotMetaDataDetail> findByProcessStatusAndFactoryAndFactorySiteAndLotTypeAndTestAreaAndFileCategoryAndDeviceId(ProcessStatus processStatus, String factory, String factorySite, LotType lotType, TestArea testArea, FileCategory fileCategory, String DeviceId);

    @Transactional
    @Modifying
    @Query(value = "update dw_lot_meta_data_detail\n" +
            "set process_status = 'CREATE'\n" +
            "where id in (select * from (select id from dw_lot_meta_data_detail where process_status = 'PROCESSING') x)", nativeQuery = true)
    void updateProcessingToCreate();

    @Transactional
    @Modifying
    @Query(value = "update dw_lot_meta_data_detail\n" +
            "set process_status = 'CREATE'\n" +
            "where id in (select *\n" +
            "             from (select id\n" +
            "                   from dw_lot_meta_data_detail\n" +
            "                   where file_name in (select file_name from dw_lot_stocking_detail where is_finish = 0)) x)", nativeQuery = true)
    void updateNotCalFinishToCreate();

    /**
     * 通过id查询相应计算维度的所有数据
     */
    @Query(value = "SELECT lmd.* FROM dw_lot_meta_data_detail lmd " +
            "INNER JOIN (" +
            "    SELECT customer, sub_customer, factory, factory_site, device_id, test_area, " +
            "           lot_id, wafer_no, test_stage, lot_type, file_category " +
            "    FROM dw_lot_meta_data_detail " +
            "    WHERE id IN :taskIds and process_status = 'CREATE'" +
            ") dim ON lmd.customer = dim.customer " +
            "    AND lmd.sub_customer = dim.sub_customer " +
            "    AND lmd.factory = dim.factory " +
            "    AND lmd.factory_site = dim.factory_site " +
            "    AND lmd.device_id = dim.device_id " +
            "    AND lmd.test_area = dim.test_area " +
            "    AND lmd.lot_id = dim.lot_id " +
            "    AND lmd.wafer_no = dim.wafer_no " +
            "    AND lmd.test_stage = dim.test_stage " +
            "    AND lmd.lot_type = dim.lot_type " +
            "    AND lmd.file_category = dim.file_category " +
            "WHERE lmd.process_status = 'CREATE'",
            nativeQuery = true)
    List<LotMetaDataDetail> findAllRelatedByIds(@Param("taskIds") List<Long> taskIds);

    @Transactional
    @Modifying
    @Query(value = "UPDATE dw_lot_meta_data_detail " +
            "SET priority = :priority, " +
            "    update_time = :updateTime, " +
            "    update_user = :userName " +
            "WHERE customer = :customer " +
            "AND sub_customer = :subCustomer " +
            "AND factory = :factory " +
            "AND factory_site = :factorySite " +
            "AND device_id = :deviceId " +
            "AND test_area = :testArea " +
            "AND lot_type = :lotType " +
            "AND file_category = :fileCategory " +
            "AND test_stage = :testStage " +
            "AND lot_id = :lotId " +
            "AND wafer_no = :waferNo ",
            nativeQuery = true)
    void updatePriorityByCalculateDimension(
            @Param("customer") String customer,
            @Param("subCustomer") String subCustomer,
            @Param("factory") String factory,
            @Param("factorySite") String factorySite,
            @Param("deviceId") String deviceId,
            @Param("testArea") String testArea,
            @Param("lotType") String lotType,
            @Param("fileCategory") String fileCategory,
            @Param("testStage") String testStage,
            @Param("lotId") String lotId,
            @Param("waferNo") String waferNo,
            @Param("priority") Integer priority,
            @Param("userName") String userName,
            @Param("updateTime") Date updateTime
    );

    @Transactional
    @Modifying
    @Query(value = "UPDATE dw_lot_meta_data_detail " +
            "SET priority = :priority, " +
            "    update_time = NOW(), " +
            "    update_user = :userName " +
            "WHERE id IN :ids ",
            nativeQuery = true)
    void updatePriorityByIds(
            @Param("ids") List<Long> ids,
            @Param("priority") Integer priority,
            @Param("userName") String userName
    );

    List<LotMetaDataDetail> findByFileNameIn(Collection<String> fileNames);
}
