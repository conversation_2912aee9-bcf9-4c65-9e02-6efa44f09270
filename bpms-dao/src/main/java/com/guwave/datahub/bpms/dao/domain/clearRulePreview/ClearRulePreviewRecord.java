package com.guwave.datahub.bpms.dao.domain.clearRulePreview;

import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.dao.mysql.converter.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.guwave.onedata.dataware.common.contant.Constant.EMPTY;
import static javax.persistence.GenerationType.IDENTITY;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "bz_clear_rule_preview_record")
public class ClearRulePreviewRecord implements Serializable {

    private static final long serialVersionUID = -463842765430431198L;

    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    /**
     * 客户名
     */
    @Column(name = "customer")
    private String customer;

    /**
     * 子客户
     */
    @Column(name = "sub_customer")
    private String subCustomer;

    /**
     * 给定的testArea，CP/FT
     */
    @Column(name = "given_test_area")
    @Convert(converter = TestAreaConverter.class)
    private TestArea givenTestArea;

    /**
     * 测试工厂
     */
    @Column(name = "factory")
    private String factory;

    /**
     * 测试工厂子厂
     */
    @Column(name = "factory_site")
    private String factorySite;

    /**
     * 晶圆的生产厂
     */
    @Column(name = "fab")
    private String fab;

    /**
     * 晶圆的生产厂子厂
     */
    @Column(name = "fab_site")
    private String fabSite;

    /**
     * 文件类型，STDF、RAW_DATA
     */
    @Column(name = "file_category")
    @Convert(converter = FileCategoryConverter.class)
    private FileCategory fileCategory;

    /**
     * hdfs上zip压缩后的文件名
     */
    @Column(name = "file_name")
    private String fileName;

    /**
     * hdfs上zip压缩后的文件大小(byte)
     */
    @Column(name = "file_size")
    private Long fileSize;

    /**
     * 原始文件解压出来的文件名
     */
    @Column(name = "origin_file_name")
    private String originFileName;

    /**
     * 原始文件解压出来的文件大小(byte)
     */
    @Column(name = "origin_file_size")
    private Long originFileSize;

    /**
     * 文件在ftp上的原始文件名
     */
    @Column(name = "remote_origin_file_name")
    private String remoteOriginFileName;

    /**
     * 文件在ftp上的原始目录路径
     */
    @Column(name = "remote_origin_file_path")
    private String remoteOriginFilePath;

    /**
     * hdfs全路径
     */
    @Column(name = "hdfs_path")
    private String hdfsPath;

    /**
     * 文件在ftp上的原始全路径
     */
    @Column(name = "ftp_path")
    private String ftpPath;

    /**
     * 实际的testArea，CP/FT/SLT等
     */
    @Column(name = "test_area")
    @Convert(converter = TestAreaConverter.class)
    private TestArea testArea;

    /**
     * device id
     */
    @Column(name = "device_id")
    private String deviceId;

    /**
     * lot id
     */
    @Column(name = "lot_id")
    private String lotId;

    /**
     * 标准化后的wafer_id
     */
    @Column(name = "wafer_id")
    private String waferId;

    /**
     * origin_wafer_id
     */
    @Column(name = "origin_wafer_id")
    private String originWaferId;

    /**
     * wafer no
     */
    @Column(name = "wafer_no")
    private String waferNo;

    /**
     * Lot类型，Engineering/Production等
     */
    @Column(name = "lot_type")
    @Convert(converter = LotTypeConverter.class)
    private LotType lotType;

    /**
     * offline_retest
     */
    @Column(name = "offline_retest")
    private Integer offlineRetest;

    /**
     * interrupt
     */
    @Column(name = "interrupt")
    private Integer interrupt;

    /**
     * dup_retest
     */
    @Column(name = "dup_retest")
    private Integer dupRetest;

    /**
     * batch_num
     */
    @Column(name = "batch_num")
    private Integer batchNum;

    /**
     * sblot_id
     */
    @Column(name = "sblot_id")
    private String sblotId;

    /**
     * test_cod
     */
    @Column(name = "test_cod")
    private String testCod;

    /**
     * test_stage
     */
    @Column(name = "test_stage")
    private String testStage;

    /**
     * start_t
     */
    @Column(name = "start_t")
    private String startT;

    /**
     * origin_start_t
     */
    @Column(name = "origin_start_t")
    private String originStartT;

    /**
     * finish_t
     */
    @Column(name = "finish_t")
    private String finishT;

    /**
     * origin_finish_t
     */
    @Column(name = "origin_finish_t")
    private String originFinishT;

    /**
     * pos_x
     */
    @Column(name = "pos_x")
    private String posX;

    /**
     * pos_y
     */
    @Column(name = "pos_y")
    private String posY;

    /**
     * notch
     */
    @Column(name = "notch")
    private String notch;

    /**
     * floor_id
     */
    @Column(name = "floor_id")
    private String floorId;

    /**
     * test_temperature
     */
    @Column(name = "test_temperature")
    private String testTemperature;

    /**
     * tester_name
     */
    @Column(name = "tester_name")
    private String testerName;

    /**
     * tester_type
     */
    @Column(name = "tester_type")
    private String testerType;

    /**
     * probecard_loadboard_id
     */
    @Column(name = "probecard_loadboard_id")
    private String probecardLoadboardId;

    /**
     * test_program
     */
    @Column(name = "test_program")
    private String testProgram;

    /**
     * test_program_version
     */
    @Column(name = "test_program_version")
    private String testProgramVersion;

    /**
     * pkg_typ
     */
    @Column(name = "pkg_typ")
    private String pkgTyp;

    /**
     * retest_bin_num
     */
    @Column(name = "retest_bin_num")
    private String retestBinNum;

    /**
     * process
     */
    @Column(name = "process")
    private String process;

    /**
     * condition_set
     */
    @Column(name = "condition_set")
    @Lob
    @Convert(converter = MapToJsonConverter.class)
    private Map<String, String> conditionSet;

    /**
     * real_wafer_id
     */
    @Column(name = "real_wafer_id")
    private String realWaferId;

    /**
     * 处理状态（PROCESSING->处理中；SUCCESS->处理成功；FAIL->处理失败；）
     */
    @Column(name = "process_status")
    @Convert(converter = ProcessStatusConverter.class)
    private ProcessStatus processStatus;

    /**
     * 预览结果json
     */
    @Column(name = "preview_result")
    @Lob
    private String previewResult;

    /**
     * 预览详细信息文件hdfs全路径
     */
    @Column(name = "preview_detail_file_hdfs_path")
    private String previewDetailFileHdfsPath;

    /**
     * 异常类型（KEY_FIELD_NULL->关键字段为空；OTHER_EXCEPTION->处理失败；）
     */
    @Column(name = "exception_type")
    @Convert(converter = ExceptionTypeConverter.class)
    private ExceptionType exceptionType;

    /**
     * 异常信息
     */
    @Column(name = "exception_message")
    private String exceptionMessage;

    /**
     * 错误信息
     */
    @Lob
    @Column(name = "error_message")
    private String errorMessage;

    /**
     * 导致失败的异常字段,多个用英文逗号分隔
     */
    @Column(name = "failed_fields")
    private String failedFields;

    /**
     * 删除标记：0->有效,1->删除
     */
    @Column(name = "delete_flag")
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;
    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    public void initParseData() {
        testArea = null;
        deviceId = EMPTY;
        lotId = EMPTY;
        waferId = EMPTY;
        originWaferId = EMPTY;
        waferNo = EMPTY;
        lotType = null;
        offlineRetest = null;
        interrupt = null;
        dupRetest = null;
        batchNum = null;
        sblotId = EMPTY;
        testCod = EMPTY;
        testStage = EMPTY;
        startT = EMPTY;
        originStartT = EMPTY;
        finishT = EMPTY;
        originFinishT = EMPTY;
        posX = EMPTY;
        posY = EMPTY;
        notch = EMPTY;
        floorId = EMPTY;
        testTemperature = EMPTY;
        testerName = EMPTY;
        testerType = EMPTY;
        probecardLoadboardId = EMPTY;
        testProgram = EMPTY;
        testProgramVersion = EMPTY;
        pkgTyp = EMPTY;
        retestBinNum = Constant.ALL;
        process = EMPTY;
        conditionSet = new HashMap<>();
        realWaferId = EMPTY;
        previewResult = EMPTY;
        previewDetailFileHdfsPath = EMPTY;
        exceptionType = null;
        exceptionMessage = EMPTY;
        errorMessage = EMPTY;
        failedFields = EMPTY;
    }
}
