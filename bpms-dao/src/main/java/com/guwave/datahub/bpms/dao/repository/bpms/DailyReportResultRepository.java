package com.guwave.datahub.bpms.dao.repository.bpms;

import com.guwave.datahub.bpms.common.constant.DataCode;
import com.guwave.datahub.bpms.common.constant.ReportModule;
import com.guwave.datahub.bpms.dao.domain.dailyReport.DailyReportResult;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Repository
public interface DailyReportResultRepository extends CrudRepository<DailyReportResult, Long> {


    @Transactional
    @Modifying
    @Query(value = "update bz_daily_report_result set latest_flag = :latestFlag, update_time = now() where day = :day",
            nativeQuery = true)
    void updateLastestFlag(@Param("day") String day, @Param("latestFlag") Integer latestFlag);


    List<DailyReportResult> findAllByReportModuleAndDataCodeAndLatestFlagAndDayBetween(ReportModule reportModule, DataCode dataCode, Integer lastestFlag, String startTime, String endTime);
}
