package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.dao.mysql.domain.dw.FileRenameRule;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.List;

@Repository("BpmsFileRenameRuleRepository")
public interface FileRenameRuleRepository extends CrudRepository<FileRenameRule, Long> {

    @Query(value = "select *\n" +
            "from dw_sftp_file_rename_rule \n" +
            "where (customer = :customer) \n" +
            " and (coalesce(:subCustomer,null) IS NULL OR (('(null)' in (:subCustomer) AND sub_customer IS NULL) OR sub_customer in (:subCustomer))) \n" +
            " order by create_time",
            nativeQuery = true)
    Page<FileRenameRule> findAllByCustomerAndSubCustomerInOrderByUpdateTimeDesc(
            @Param("customer") String customer,
            @Param("subCustomer") List<String> subCustomer,
            Pageable pageable);

    @Transactional
    @Modifying
    @Query("delete from FileRenameRule a where a.id in (:ids) ")
    void deleteAllByIds(@Param("ids") Collection<Long> ids);

    List<FileRenameRule> findAllByCustomer(String customer);

    @Transactional
    @Modifying
    @Query(value = "delete from FileRenameRule a where a.customer = :customer")
    void deleteAllByCustomer(@Param("customer") String customer);

    // 根据customer和sub_customer查询全部id
    @Query(value = "select id\n" +
            "from dw_sftp_file_rename_rule \n" +
            "where customer = :customer \n" +
            " and sub_customer = :subCustomer",
            nativeQuery = true)
    List<Long> findAllIdsByCustomerAndSubCustomer(
            @Param("customer") String customer,
            @Param("subCustomer") String subCustomer);

}
