package com.guwave.datahub.bpms.dao.vo.bpms.dailyReport;

import java.util.Map;

public class WarehousingFailFileResult {

    private Long failFileCnt;
    private Map<String, Long> ExceptionTypeMap;
    private Map<String, Long> fileCategoryMap;

    public Long getFailFileCnt() {
        return failFileCnt;
    }

    public void setFailFileCnt(Long failFileCnt) {
        this.failFileCnt = failFileCnt;
    }

    public Map<String, Long> getExceptionTypeMap() {
        return ExceptionTypeMap;
    }

    public void setExceptionTypeMap(Map<String, Long> exceptionTypeMap) {
        ExceptionTypeMap = exceptionTypeMap;
    }

    public Map<String, Long> getFileCategoryMap() {
        return fileCategoryMap;
    }

    public void setFileCategoryMap(Map<String, Long> fileCategoryMap) {
        this.fileCategoryMap = fileCategoryMap;
    }
}
