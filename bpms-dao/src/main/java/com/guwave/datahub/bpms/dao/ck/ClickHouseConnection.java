package com.guwave.datahub.bpms.dao.ck;

import com.github.housepower.jdbc.BalancedClickhouseDataSource;
import com.github.housepower.settings.SettingKey;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.io.Serializable;
import java.security.SecureRandom;
import java.sql.Connection;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.COMMA;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * ClickHouseConnection
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-10-29 14:21:11
 */
@Component("appClickHouseConnection")
public class ClickHouseConnection {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClickHouseConnection.class);

    private static String clickhouseAddress;

    private static String clickhouseUsername;

    private static String clickhousePassword;

    private static int maximumPoolSize;

    private static int minimumIdle;

    private static long connectionTimeout;

    private static final AtomicBoolean initialized = new AtomicBoolean(false);

    // 存储每个地址对应的连接池
    private static final Map<String, HikariDataSource> connectionPools = new ConcurrentHashMap<>();

    @Value("${spring.datasource.clickhouse.maximum-pool-size:10}")
    private void setMaximumPoolSize(int value) {
        maximumPoolSize = value;
    }

    @Value("${spring.datasource.clickhouse.minimum-idle:2}")
    private void setMinimumIdle(int value) {
        minimumIdle = value;
    }

    @Value("${spring.datasource.clickhouse.idle-timeout:30000}")
    private void setConnectionTimeout(long value) {
        connectionTimeout = value;
    }

    @Value("${spring.data.clickhouse.main.address}")
    public void setClickhouseAddress(String address) {
        clickhouseAddress = address;
    }

    @Value("${spring.data.clickhouse.username}")
    public void setClickhouseUsername(String username) {
        clickhouseUsername = username;
    }

    @Value("${spring.data.clickhouse.password}")
    public void setClickhousePassword(String password) {
        clickhousePassword = password;
    }

    public static void init() {
        String[] addresses = getAllCkAddress();
        for (String address : addresses) {
            initializePool(address);
        }
    }

    public static DataSource getClickHouseDataSource(String address) {
        Map<SettingKey, Serializable> properties = new HashMap<>();
        properties.put(SettingKey.user, clickhouseUsername);
        properties.put(SettingKey.password, clickhousePassword);
        return new BalancedClickhouseDataSource(address, properties);
    }

    private static void initializePool(String address) {
        HikariConfig config = new HikariConfig();
        config.setDataSource(getClickHouseDataSource(address));
        config.setMaximumPoolSize(maximumPoolSize);
        config.setMinimumIdle(minimumIdle);
        config.setConnectionTimeout(connectionTimeout);
        config.setPoolName("HikariPool-Clickhouse-" + address);
        // 连接测试配置
        config.setConnectionTestQuery("SELECT 1");
        config.setValidationTimeout(30000);
        config.setKeepaliveTime(60000);
        HikariDataSource dataSource = new HikariDataSource(config);
        LOGGER.info("初始化ck连接 address: {}, maximumPoolSize: {}, minimumIdle: {}, connectionTimeout: {}",
                address, maximumPoolSize, minimumIdle, connectionTimeout);
        connectionPools.put(address, dataSource);
    }

    public static String getCkAddress() {
        String[] detail = clickhouseAddress.split(COMMA);
        int len = detail.length;
        SecureRandom random = new SecureRandom();
        return detail[random.nextInt(len)];
    }

    public static String[] getAllCkAddress() {
        return clickhouseAddress.split(COMMA);
    }

    public static String getCkAddressForPartition(String partition) {
        String[] detail = clickhouseAddress.split(COMMA);
        int dataStoreIndex = Math.abs(partition.hashCode() % detail.length);
        return detail[dataStoreIndex];
    }

    public static Connection getConnection() {
        String address = getCkAddress();
        return getAddrConnection(address);
    }

    private static Connection getAddrConnection(String address) {
        try {
            if (!initialized.get()) {
                synchronized (initialized) {
                    // 第二次检查
                    if (!initialized.get()) {
                        init();
                        initialized.set(true);
                    }
                }
            }
            LOGGER.info("从{}获取连接", address);
            HikariDataSource dataSource = connectionPools.get(address);
            if (dataSource == null) {
                LOGGER.error("无法找到地址{}的连接池", address);
                throw new RuntimeException("连接池未初始化");
            }
            return dataSource.getConnection();
        } catch (Exception e) {
            LOGGER.error("从连接池获取连接失败", e);
            throw new RuntimeException("获取连接失败", e);
        }
    }

    public static List<Connection> getAllConnection() {
        return Arrays.stream(getAllCkAddress())
                .map(ClickHouseConnection::getAddrConnection)
                .collect(Collectors.toList());
    }

    public static Connection getConnection(String partition) {
        String address = getCkAddressForPartition(partition);
        return getAddrConnection(address);
    }
}
