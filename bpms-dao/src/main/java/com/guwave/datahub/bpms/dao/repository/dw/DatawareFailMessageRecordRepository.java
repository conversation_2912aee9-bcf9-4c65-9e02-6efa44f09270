package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.DatawareFailMessageRecord;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("BpmsDatawareFailMessageRecordRepository")
public interface DatawareFailMessageRecordRepository extends CrudRepository<DatawareFailMessageRecord, Long> {

    List<DatawareFailMessageRecord> findAllByProcessStatusAndDeleteFlag(ProcessStatus processStatus, Integer deleteFlag);

}
