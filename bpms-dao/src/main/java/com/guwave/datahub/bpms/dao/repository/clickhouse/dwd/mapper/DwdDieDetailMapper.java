package com.guwave.datahub.bpms.dao.repository.clickhouse.dwd.mapper;

import com.guwave.datahub.bpms.dao.clickhouse.domain.dwd.DwdDieDetail;
import com.guwave.datahub.bpms.dao.clickhouse.dto.GoodRetestBinDTO;
import com.guwave.datahub.bpms.dao.clickhouse.dto.OnlineRetestBinDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * DwdDieDetailMapper
 */
@Mapper
public interface DwdDieDetailMapper {

    List<GoodRetestBinDTO> selectGoodRetestBins(
            @Param("deviceId") String deviceId,
            @Param("testArea") String testArea,
            @Param("testStage") String testStage,
            @Param("lotId") String lotId,
            @Param("waferNo") String waferNo,
            @Param("sblotId") String sblotId,
            @Param("testProgram") String testProgram,
            @Param("lotType") String lotType
    );

    List<OnlineRetestBinDTO> selectOnlineRetestDies(
            @Param("deviceId") String deviceId,
            @Param("testStage") String testStage,
            @Param("lotId") String lotId,
            @Param("sblotId") String sblotId,
            @Param("ecidList") List<String> ecidList
    );

    List<DwdDieDetail> findSlimDieDetailByLotWafer(
            @Param("customer") String customer,
            @Param("subCustomer") String subCustomer,
            @Param("factory") String factory,
            @Param("testArea") String testArea,
            @Param("deviceId") String deviceId,
            @Param("testStage") String testStage,
            @Param("lotId") String lotId,
            @Param("waferNo") String waferNo,
            @Param("lotType") String lotType
    );

}
    