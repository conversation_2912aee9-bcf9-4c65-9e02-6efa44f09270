package com.guwave.datahub.bpms.dao.repository.clickhouse.dws.mapper;

import com.guwave.datahub.bpms.dao.clickhouse.dto.AccuracyLotWaferDetailDTO;
import com.guwave.datahub.bpms.dao.clickhouse.dto.DataAccuracyBatchMatchDTO;
import com.guwave.datahub.bpms.dao.clickhouse.dto.DataAccuracyBatchYieldDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;

/**
 * 2025/5/14 18:12
 * DwsFlowidBinIndexMapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DwsLotWaferDataCheckIndexMapper {

    List<AccuracyLotWaferDetailDTO> findLotWaferDetail(
            @Param("customer") String customer,
            @Param("subCustomer") String subCustomer,
            @Param("factory") String factory,
            @Param("testArea") String testArea,
            @Param("deviceId") String deviceId,
            @Param("testStage") String testStage,
            @Param("lotId") String lotId,
            @Param("waferNo") String waferNo,
            @Param("lotType") String lotType,
            @Param("isCp") boolean isCp
            );


    List<String> findDynamicField(
            @Param("customer") String customer,
            @Param("filterField") String filterField,
            @Param("subCustomerList") List<String> subCustomerList,
            @Param("factoryList") List<String> factoryList,
            @Param("testAreaList") Collection<String> testAreaList,
            @Param("deviceIdList") List<String> deviceIdList,
            @Param("testStageList") List<String> testStageList,
            @Param("lotIdList") List<String> lotIdList,
            @Param("waferNoList") List<String> waferNoList,
            @Param("sblotIdList") List<String> sblotIdList,
            @Param("lotTypeList") List<String> lotTypeList,
            @Param("mesMatchTypeList") List<String> mesMatchTypeList,
            @Param("dataAccuracyCheckList") List<String> dataAccuracyCheckList,
            @Param("isCp") boolean isCp
    );

    List<DataAccuracyBatchMatchDTO> findDataAccuracyBatchMatchDTO(
            @Param("customer") String customer,
            @Param("subCustomerList") Collection<String> subCustomerList,
            @Param("factoryList") Collection<String> factoryList,
            @Param("testAreaList") Collection<String> testAreaList,
            @Param("deviceIdList") Collection<String> deviceIdList,
            @Param("testStageList") Collection<String> testStageList,
            @Param("lotIdList") Collection<String> lotIdList,
            @Param("waferNoList") Collection<String> waferNoList,
            @Param("sblotIdList") List<String> sblotIdList,
            @Param("lotTypeList") Collection<String> lotTypeList,
            @Param("mesMatchTypeList") Collection<String> mesMatchTypeList,
            @Param("dataAccuracyCheckList") Collection<String> dataAccuracyCheckList,
            @Param("isCp") boolean isCp
    );

    List<DataAccuracyBatchYieldDTO> findDataAccuracyBatchYieldDTOPage(
            @Param("customer") String customer,
            @Param("subCustomerList") Collection<String> subCustomerList,
            @Param("factoryList") Collection<String> factoryList,
            @Param("testAreaList") Collection<String> testAreaList,
            @Param("deviceIdList") Collection<String> deviceIdList,
            @Param("testStageList") Collection<String> testStageList,
            @Param("lotIdList") Collection<String> lotIdList,
            @Param("waferNoList") Collection<String> waferNoList,
            @Param("sblotIdList") List<String> sblotIdList,
            @Param("lotTypeList") Collection<String> lotTypeList,
            @Param("mesMatchTypeList") Collection<String> mesMatchTypeList,
            @Param("dataAccuracyCheckList") Collection<String> dataAccuracyCheckList,
            @Param("isCp") boolean isCp,
            @Param("sortField") String sortField,
            @Param("sortDirection") String sortDirection,
            @Param("offset") long offset,
            @Param("pageSize") Integer pageSize
    );
    Long countDataAccuracyBatchYieldDTO(
            @Param("customer") String customer,
            @Param("subCustomerList") Collection<String> subCustomerList,
            @Param("factoryList") Collection<String> factoryList,
            @Param("testAreaList") Collection<String> testAreaList,
            @Param("deviceIdList") Collection<String> deviceIdList,
            @Param("testStageList") Collection<String> testStageList,
            @Param("lotIdList") Collection<String> lotIdList,
            @Param("waferNoList") Collection<String> waferNoList,
            @Param("sblotIdList") List<String> sblotIdList,
            @Param("lotTypeList") Collection<String> lotTypeList,
            @Param("mesMatchTypeList") Collection<String> mesMatchTypeList,
            @Param("dataAccuracyCheckList") Collection<String> dataAccuracyCheckList,
            @Param("isCp") boolean isCp
    );

}
