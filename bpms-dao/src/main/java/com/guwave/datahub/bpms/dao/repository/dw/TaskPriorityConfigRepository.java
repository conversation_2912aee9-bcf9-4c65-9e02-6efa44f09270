package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.DwTaskPriorityConfig;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Optional;

@Repository("BpmsTaskPriorityConfigRepository")
public interface TaskPriorityConfigRepository extends JpaRepository<DwTaskPriorityConfig, Long> {

    @Query(value = "SELECT * FROM dw_task_priority_config t WHERE " +
            "t.delete_flag = 0 AND " +
            "(:customer IS NULL OR t.customer = :customer) AND " +
            "(:subCustomer IS NULL OR t.sub_customer = :subCustomer) AND " +
            "(:factory IS NULL OR t.factory = :factory) AND " +
            "(:factorySite IS NULL OR t.factory_site = :factorySite) AND " +
            "(:deviceId IS NULL OR t.device_id = :deviceId) AND " +
            "(:testStage IS NULL OR t.test_stage = :testStage) AND " +
            "(:lotType IS NULL OR t.lot_type = :lotType) AND " +
            "(:lotId IS NULL OR t.lot_id = :lotId) AND " +
            "(:waferNo IS NULL OR t.wafer_no = :waferNo) AND " +
            "(:testArea IS NULL OR t.test_area = :testArea) AND " +
            "(:fileCategory IS NULL OR t.file_category = :fileCategory) order by id desc",
            countQuery = "SELECT count(*) FROM dw_task_priority_config t WHERE " +
                    "t.delete_flag = 0 AND " +
                    "(:customer IS NULL OR t.customer = :customer) AND " +
                    "(:subCustomer IS NULL OR t.sub_customer = :subCustomer) AND " +
                    "(:factory IS NULL OR t.factory = :factory) AND " +
                    "(:factorySite IS NULL OR t.factory_site = :factorySite) AND " +
                    "(:deviceId IS NULL OR t.device_id = :deviceId) AND " +
                    "(:testStage IS NULL OR t.test_stage = :testStage) AND " +
                    "(:lotType IS NULL OR t.lot_type = :lotType) AND " +
                    "(:lotId IS NULL OR t.lot_id = :lotId) AND " +
                    "(:waferNo IS NULL OR t.wafer_no = :waferNo) AND " +
                    "(:testArea IS NULL OR t.test_area = :testArea) AND " +
                    "(:fileCategory IS NULL OR t.file_category = :fileCategory) order by id desc",
            nativeQuery = true)
    Page<DwTaskPriorityConfig> findByConditions(
            @Param("customer") String customer,
            @Param("subCustomer") String subCustomer,
            @Param("factory") String factory,
            @Param("factorySite") String factorySite,
            @Param("deviceId") String deviceId,
            @Param("testStage") String testStage,
            @Param("lotType") String lotType,
            @Param("lotId") String lotId,
            @Param("waferNo") String waferNo,
            @Param("testArea") String testArea,
            @Param("fileCategory") String fileCategory,
            Pageable pageable
    );

    Optional<DwTaskPriorityConfig> findByIdAndDeleteFlag(Long id, Integer deleteFlag);

    boolean existsByCustomerAndSubCustomerAndFactoryAndFactorySiteAndDeviceIdAndTestStageAndLotTypeAndTestAreaAndFileCategoryAndLotIdAndWaferNoAndDeleteFlag(
            String customer,
            String subCustomer,
            String factory,
            String factorySite,
            String deviceId,
            String testStage,
            LotType lotType,
            TestArea testArea,
            FileCategory fileCategory,
            String lotId,
            String waferNo,
            Integer deleteFlag
    );

    @Transactional
    @Modifying
    @Query(value = "UPDATE dw_task_priority_config " +
            "SET priority = :priority, " +
            "    max_start_t = :maxStartT, " +
            "    min_start_t = :minStartT, " +
            "    max_ftp_mtime = :maxFtpMtime, " +
            "    min_ftp_mtime = :minFtpMtime, " +
            "    ftp_path = :ftpPath, " +
            "    update_time = NOW(), " +
            "    update_user = :userName " +
            "WHERE id = :id " +
            "AND delete_flag = 0",
            nativeQuery = true)
    void updateConfig(
            @Param("id") Long id,
            @Param("priority") Integer priority,
            @Param("maxStartT") Date maxStartT,
            @Param("minStartT") Date minStartT,
            @Param("maxFtpMtime") Date maxFtpMtime,
            @Param("minFtpMtime") Date minFtpMtime,
            @Param("ftpPath") String ftpPath,
            @Param("userName") String userName
    );

    @Transactional
    @Modifying
    @Query(value = "UPDATE dw_task_priority_config " +
            "SET delete_flag = 1, " +
            "    update_time = NOW(), " +
            "    update_user = :userName " +
            "WHERE id = :id " +
            "AND delete_flag = 0",
            nativeQuery = true)
    void deleteConfig(
            @Param("id") Long id,
            @Param("userName") String userName
    );
}
