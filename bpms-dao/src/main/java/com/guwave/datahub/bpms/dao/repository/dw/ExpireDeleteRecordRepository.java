package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.datahub.bpms.dao.vo.bpms.dataFreeze.DataFreezeRecord;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ExpireDeleteRecord;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Repository("BpmsExpireDeleteRecordRepository")
public interface ExpireDeleteRecordRepository extends CrudRepository<ExpireDeleteRecord, Long> {


    @Query(value = "\n" +
            "select customer,\n" +
            "       sub_customer  subCustomer,\n" +
            "       factory,\n" +
            "       factory_site  factorySite,\n" +
            "       test_area     testArea,\n" +
            "       lot_type      lotType,\n" +
            "       device_id     deviceId,\n" +
            "       lot_id        lotId,\n" +
            "       wafer_no      waferNo,\n" +
            "       test_stage    testStage,\n" +
            "       file_category fileCategory,\n" +
            "       id            warehousingTaskRecordId\n" +
            "from dw_warehousing_task_record\n" +
            "where process_status in ('SUCCESS','RELOAD_SUCCESS')\n" +
            "  and (customer, factory, device_id, lot_id) in (select customer, factory, device_id, lot_id\n" +
            "                                                 from dw_lot_stocking_detail\n" +
            "                                                 group by customer, factory, device_id, lot_id\n" +
            "                                                 having max(if(:updateTime is null,\n" +
            "                                                               if(start_t is null or start_t = '', 1704038400,\n" +
            "                                                                  cast(start_t as signed)), update_time)) <\n" +
            "                                                        if(:updateTime is null, :startT, :updateTime) and if(:deviceId is null, 1, device_id = :deviceId))"
            , nativeQuery = true)
    List<DataFreezeRecord> findExpireData(@Param("updateTime") Date updateTime, @Param("startT") Long startT, @Param("deviceId") String deviceId);

    List<ExpireDeleteRecord> findAllByPlatformAndDeleteFromDiskFlagOrderById(Platform platform, Integer deleteFromDiskFlag, Pageable pageable);

    List<ExpireDeleteRecord> findAllByPlatformAndDeleteFromDiskFlagAndCustomerAndTestAreaAndFactoryAndSubCustomerAndDeviceId(Platform platform, Integer deleteFromDiskFlag, String customer, TestArea testArea, String factory, String subCustomer, String deviceId);

    List<ExpireDeleteRecord> findAllByCustomerAndSubCustomerAndFactoryAndFactorySiteAndTestAreaAndLotTypeAndDeviceIdAndLotIdAndWaferNoAndTestStageAndFileCategoryAndPlatformAndProcessStatus(String customer, String subCustomer, String factory, String factorySite, TestArea testArea, LotType lotType, String deviceId, String lotId, String waferNo, String testStage, FileCategory fileCategory, Platform platform, ProcessStatus processStatus);

    @Modifying
    @Transactional
    @Query("update ExpireDeleteRecord a set a.processStatus = :newProcessStatus,a.uniqueId = :uniqueId,a.updateTime = now(),a.exceptionType = :exceptionType,a.errorMessage = :errorMessage where a.id in :ids and a.processStatus = :oldProcessStatus")
    void updateProcessStatus(@Param("ids") List<Long> ids, @Param("uniqueId") String uniqueId, @Param("exceptionType") ExceptionType exceptionType, @Param("errorMessage") String errorMessage, @Param("newProcessStatus") ProcessStatus newProcessStatus, @Param("oldProcessStatus") ProcessStatus oldProcessStatus);

    @Modifying
    @Transactional
    @Query("update ExpireDeleteRecord a set a.deleteFromDiskFlag = 1,a.updateTime = now() where a.id in :ids")
    void updateDeleteFromDiskFlag(@Param("ids") List<Long> ids);

    List<ExpireDeleteRecord> findByUniqueIdAndProcessStatus(String uniqueId, ProcessStatus processStatus);
}
