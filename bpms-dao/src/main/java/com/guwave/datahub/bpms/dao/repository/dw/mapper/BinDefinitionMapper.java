package com.guwave.datahub.bpms.dao.repository.dw.mapper;

import com.guwave.datahub.bpms.dao.vo.bpms.binDefinition.BinDefinitionDetail;
import com.guwave.datahub.bpms.dao.vo.bpms.binDefinition.ConfiguredTestProgramVo;
import com.guwave.datahub.bpms.dao.vo.bpms.binDefinition.UnconfiguredTestProgramVo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.TestProgramBinDefinition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Test Program Bin Definition MyBatis Mapper
 *
 * <AUTHOR>
 * @version 1.0
 */
@Mapper
public interface BinDefinitionMapper {


    /**
     * 搜索测试程序
     */
    List<String> searchTestPrograms(@Param("customer") String customer,
                                    @Param("subCustomer") String subCustomer,
                                    @Param("filterField") String filterField);

    /**
     * 根据客户权限查询已配置的测试程序统计信息（分页）
     */
    List<ConfiguredTestProgramVo> findConfiguredTestPrograms(
            @Param("customer") String customer,
            @Param("subCustomer") String subCustomer,
            @Param("testPrograms") List<String> testPrograms,
            @Param("orderByField") String orderByField,
            @Param("orderByType") String orderByType,
            @Param("offset") Long offset,
            @Param("size") Long size
    );

    /**
     * 统计已配置的测试程序总数
     */
    Long countConfiguredTestPrograms(
            @Param("customer") String customer,
            @Param("subCustomer") String subCustomer,
            @Param("testPrograms") List<String> testPrograms
    );

    /**
     * 查询未配置的测试程序（分页）
     */
    List<UnconfiguredTestProgramVo> findUnconfiguredTestPrograms(
            @Param("customer") String customer,
            @Param("subCustomer") String subCustomer,
            @Param("testPrograms") List<String> testPrograms,
            @Param("orderByField") String orderByField,
            @Param("orderByType") String orderByType,
            @Param("offset") Long offset,
            @Param("size") Long size
    );

    /**
     * 统计未配置的测试程序总数
     */
    Long countUnconfiguredTestPrograms(@Param("customer") String customer,
                                       @Param("subCustomer") String subCustomer,
                                       @Param("testPrograms") List<String> testPrograms);

    /**
     * 根据测试程序和客户权限查询Bin定义
     */
    List<BinDefinitionDetail> findByTestProgramAndPermissions(@Param("customer") String customer,
                                                              @Param("subCustomer") String subCustomer,
                                                              @Param("testPrograms") List<String> testPrograms,
                                                              @Param("orderByField") String orderByField,
                                                              @Param("orderByType") String orderByType
    );

    /**
     * 根据测试程序和客户权限查询Bin定义
     */
    List<BinDefinitionDetail> findExportUnconfigured(@Param("customer") String customer,
                                                              @Param("subCustomer") String subCustomer,
                                                              @Param("testPrograms") List<String> testPrograms,
                                                              @Param("orderByField") String orderByField,
                                                              @Param("orderByType") String orderByType
    );

    /**
     * 检查Bin编号在同一测试程序和Bin类型下是否唯一
     */
    Long countByTestProgramAndBinTypeAndBinNumAndIdNot(@Param("testProgram") String testProgram,
                                                       @Param("binType") String binType,
                                                       @Param("binNum") Long binNum,
                                                       @Param("id") Long id);

    /**
     * 插入Bin定义
     */
    void insert(TestProgramBinDefinition binDefinition);

    /**
     * 批量插入Bin定义
     */
    void insertBatch(@Param("list") List<TestProgramBinDefinition> binDefinitions);

    /**
     * 根据测试程序删除所有Bin定义
     */
    void deleteByTestProgramAndPermissions(@Param("testProgram") String testProgram,
                                           @Param("customer") String customer,
                                           @Param("subCustomer") String subCustomer);


    /**
     * 检查测试程序是否已存在
     */
    Long countByTestProgramAndPermissions(@Param("testProgram") String testProgram,
                                          @Param("customer") String customer,
                                          @Param("subCustomer") String subCustomer);

    /**
     * 删除所有指定客户的Bin定义（用于清除导入）
     */
    int deleteAllByPermissions(@Param("customer") String customer,
                               @Param("subCustomer") String subCustomer);
}