package com.guwave.datahub.bpms.dao.vo.dw;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 2025/5/15 14:21
 * WaferVo
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WaferVo {
    private String customer;
    private String subCustomer;
    private String factory;
    private String factorySite;
    private String testArea;
    private String fileCategory;
    private String deviceId;
    private String lotType;
    private String testStage;
    private String lotId;
    private String waferNo;
    private String sblotId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WaferVo waferVo = (WaferVo) o;
        return Objects.equals(customer, waferVo.customer) && Objects.equals(subCustomer, waferVo.subCustomer) && Objects.equals(factory, waferVo.factory) && Objects.equals(factorySite, waferVo.factorySite) && Objects.equals(testArea, waferVo.testArea) && Objects.equals(fileCategory, waferVo.fileCategory) && Objects.equals(deviceId, waferVo.deviceId) && Objects.equals(lotType, waferVo.lotType) && Objects.equals(testStage, waferVo.testStage) && Objects.equals(lotId, waferVo.lotId) && Objects.equals(waferNo, waferVo.waferNo) && Objects.equals(sblotId, waferVo.sblotId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(customer, subCustomer, factory, factorySite, testArea, fileCategory, deviceId, lotType, testStage, lotId, waferNo, sblotId);
    }
}
