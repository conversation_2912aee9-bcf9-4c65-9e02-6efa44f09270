package com.guwave.datahub.bpms.dao.repository.dw.mapper;

import com.guwave.datahub.bpms.dao.clickhouse.dto.DataTimelinessDTO;
import com.guwave.datahub.bpms.dao.domain.qualityOverview.FileLoadInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface FileLoadingLogMapper {

    List<DataTimelinessDTO> querySuccessFileLogByCond(
            @Param("customer") String customer,
            @Param("subCustomerList") List<String> subCustomerList,
            @Param("factoryList") List<String> factoryList,
            @Param("deviceIdList") List<String> deviceIdList,
            @Param("testArea") String testArea,
            @Param("testProgramList") List<String> testProgramList,
            @Param("testStageList") List<String> testStageList,
            @Param("lotIdList") List<String> lotIdList,
            @Param("sblotIdList") List<String> sblotIdList,
            @Param("waferNoList") List<String> waferNoList,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime,
            @Param("allSupportTestArea") List<String> allSupportTestArea
    );

    List<String> findCpDynamicField(
            @Param("customer") String customer,
            @Param("filterField") String filterField,
            @Param("subCustomerList") List<String> subCustomerList,
            @Param("factoryList") List<String> factoryList,
            @Param("testArea") String testArea,
            @Param("deviceIdList") List<String> deviceIdList,
            @Param("testProgramList") List<String> testProgramList,
            @Param("testStageList") List<String> testStageList,
            @Param("lotIdList") List<String> lotIdList,
            @Param("waferNoList") List<String> waferNoList,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime
    );

    List<String> findFtDynamicField(
            @Param("customer") String customer,
            @Param("filterField") String filterField,
            @Param("subCustomerList") List<String> subCustomerList,
            @Param("factoryList") List<String> factoryList,
            @Param("testArea") String testArea,
            @Param("deviceIdList") List<String> deviceIdList,
            @Param("testProgramList") List<String> testProgramList,
            @Param("testStageList") List<String> testStageList,
            @Param("lotIdList") List<String> lotIdList,
            @Param("sblotIdList") List<String> sblotIdList,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime
    );

    List<String> findOverviewFilter(
            @Param("customer") String customer,
            @Param("filterField") String filterField,
            @Param("subCustomerList") List<String> subCustomerList,
            @Param("factoryList") List<String> factoryList,
            @Param("deviceIdList") List<String> deviceIdList,
            @Param("testArea") String testArea,
            @Param("testProgramList") List<String> testProgramList,
            @Param("testStageList") List<String> testStageList,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime);

    List<FileLoadInfo> querySuccessFileOverviewByCond(
            @Param("customer") String customer,
            @Param("subCustomerList") List<String> subCustomerList,
            @Param("factoryList") List<String> factoryList,
            @Param("deviceIdList") List<String> deviceIdList,
            @Param("testArea") String testArea,
            @Param("testProgramList") List<String> testProgramList,
            @Param("testStageList") List<String> testStageList,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime);
}
