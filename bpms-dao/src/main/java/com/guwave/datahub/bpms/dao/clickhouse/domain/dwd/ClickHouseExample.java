package com.guwave.datahub.bpms.dao.clickhouse.domain.dwd;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * ClickHouse示例实体类
 */
@Entity
@Table(name = "dwd_test_item_detail_cluster")
public class ClickHouseExample {

    @Id
    @Column(name = "ID")
    private String id;

    @Column(name = "CUSTOMER")
    private String customer;

    @Column(name = "ECID")
    private String ecid;

    @Column(name = "IS_DELETE")
    private Boolean isDelete;

    @Column(name = "TEST_VALUE")
    private BigDecimal testValue;

    public String getId() {
        return id;
    }

    public ClickHouseExample setId(String id) {
        this.id = id;
        return this;
    }

    public String getCustomer() {
        return customer;
    }

    public ClickHouseExample setCustomer(String customer) {
        this.customer = customer;
        return this;
    }

    public String getEcid() {
        return ecid;
    }

    public ClickHouseExample setEcid(String ecid) {
        this.ecid = ecid;
        return this;
    }

    public BigDecimal getTestValue() {
        return testValue;
    }

    public ClickHouseExample setTestValue(BigDecimal testValue) {
        this.testValue = testValue;
        return this;
    }

    public Boolean getDelete() {
        return isDelete;
    }

    public ClickHouseExample setDelete(Boolean delete) {
        isDelete = delete;
        return this;
    }

    @Override
    public String toString() {
        return "ClickHouseExample{" +
                "id='" + id + '\'' +
                ", customer='" + customer + '\'' +
                ", ecid='" + ecid + '\'' +
                ", isDelete=" + isDelete +
                ", testValue=" + testValue +
                '}';
    }
}
