package com.guwave.datahub.bpms.dao.converter;

import com.guwave.datahub.bpms.common.constant.ExceptionType;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = false)
public class ExceptionTypeConverter implements AttributeConverter<ExceptionType, String> {

    @Override
    public String convertToDatabaseColumn(ExceptionType exceptionType) {
        return null == exceptionType ? null : exceptionType.getType();
    }

    @Override
    public ExceptionType convertToEntityAttribute(String dbData) {
        return dbData == null ? null : ExceptionType.of(dbData);
    }

}