package com.guwave.datahub.bpms.dao.domain.repair;

import org.hibernate.annotations.Comment;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 2025/5/6 17:40
 * DataRepairEffectBatch
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "bz_data_repair_effect_batch")
public class DataRepairEffectBatch {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "customer")
    @Comment("客户")
    private String customer;

    @Column(name = "sub_customer")
    @Comment("子客户")
    private String subCustomer;

    @Column(name = "factory")
    @Comment("factory")
    private String factory;

    @Column(name = "factory_site")
    @Comment("factory_site")
    private String factorySite;

    @Column(name = "repair_batch_id")
    @Comment("处理批次")
    private Long repairBatchId;

    @Column(name = "repair_status")
    @Comment("修改状态 Success,Fail,Process")
    private String repairStatus;

    @Column(name = "device_id")
    @Comment("device_id")
    private String deviceId;

    @Column(name = "lot_id")
    @Comment("lot_id")
    private String lotId;

    @Column(name = "test_stage")
    @Comment("test stage")
    private String testStage;

    @Column(name = "test_program")
    @Comment("test_program")
    private String testProgram;

    @Column(name = "test_area")
    @Comment("测试阶段，CP/FT")
    private String testArea;

    @Column(name = "sblot_id")
    @Comment("sblot_id")
    private String sblotId;

    @Column(name = "wafer_id")
    @Comment("wafer_id")
    private String waferId;

    @Column(name = "wafer_no")
    @Comment("wafer_no")
    private String waferNo;

    @Column(name = "repair_no", nullable = false)
    @Comment("批次累积受影响次数")
    private Integer repairNo = 0;

    @Column(name = "file_count_before")
    @Comment("批次变更前文件个数")
    private Long fileCountBefore;

    @Column(name = "file_list_before", columnDefinition = "text")
    @Comment("批次变更前文件名列表")
    private String fileListBefore;

    @Column(name = "file_count_after")
    @Comment("批次变更后文件个数")
    private Long fileCountAfter;

    @Column(name = "file_list_after", columnDefinition = "text")
    @Comment("批次变更后文件名列表")
    private String fileListAfter;

    @Column(name = "repair_files", columnDefinition = "text")
    @Comment("变更文件名")
    private String repairFiles;

    @Column(name = "repair_info_ids")
    @Comment("文件变更id")
    private String repairInfoIds;

    @Column(name = "delete_flag", nullable = false)
    @Comment("删除标志,0:未删除，1:已删除")
    private Boolean deleteFlag = false;

    @Column(name = "create_time")
    @Comment("创建时间")
    private Date createTime;

    @Column(name = "update_time")
    @Comment("更新时间")
    private Date updateTime;

    @Column(name = "create_user")
    @Comment("创建用户")
    private String createUser = "System";

    @Column(name = "update_user")
    @Comment("更新用户")
    private String updateUser = "System";

    public Long getId() {
        return id;
    }

    public DataRepairEffectBatch setId(Long id) {
        this.id = id;
        return this;
    }

    public String getCustomer() {
        return customer;
    }

    public DataRepairEffectBatch setCustomer(String customer) {
        this.customer = customer;
        return this;
    }

    public String getSubCustomer() {
        return subCustomer;
    }

    public DataRepairEffectBatch setSubCustomer(String subCustomer) {
        this.subCustomer = subCustomer;
        return this;
    }

    public String getFactory() {
        return factory;
    }

    public DataRepairEffectBatch setFactory(String factory) {
        this.factory = factory;
        return this;
    }

    public String getFactorySite() {
        return factorySite;
    }

    public DataRepairEffectBatch setFactorySite(String factorySite) {
        this.factorySite = factorySite;
        return this;
    }

    public Long getRepairBatchId() {
        return repairBatchId;
    }

    public DataRepairEffectBatch setRepairBatchId(Long repairBatchId) {
        this.repairBatchId = repairBatchId;
        return this;
    }

    public String getRepairStatus() {
        return repairStatus;
    }

    public DataRepairEffectBatch setRepairStatus(String repairStatus) {
        this.repairStatus = repairStatus;
        return this;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public DataRepairEffectBatch setDeviceId(String deviceId) {
        this.deviceId = deviceId;
        return this;
    }

    public String getLotId() {
        return lotId;
    }

    public DataRepairEffectBatch setLotId(String lotId) {
        this.lotId = lotId;
        return this;
    }

    public String getTestStage() {
        return testStage;
    }

    public DataRepairEffectBatch setTestStage(String testStage) {
        this.testStage = testStage;
        return this;
    }

    public String getTestProgram() {
        return testProgram;
    }

    public DataRepairEffectBatch setTestProgram(String testProgram) {
        this.testProgram = testProgram;
        return this;
    }

    public String getTestArea() {
        return testArea;
    }

    public DataRepairEffectBatch setTestArea(String testArea) {
        this.testArea = testArea;
        return this;
    }

    public String getSblotId() {
        return sblotId;
    }

    public DataRepairEffectBatch setSblotId(String sblotId) {
        this.sblotId = sblotId;
        return this;
    }

    public String getWaferId() {
        return waferId;
    }

    public DataRepairEffectBatch setWaferId(String waferId) {
        this.waferId = waferId;
        return this;
    }

    public String getWaferNo() {
        return waferNo;
    }

    public DataRepairEffectBatch setWaferNo(String waferNo) {
        this.waferNo = waferNo;
        return this;
    }

    public Integer getRepairNo() {
        return repairNo;
    }

    public DataRepairEffectBatch setRepairNo(Integer repairNo) {
        this.repairNo = repairNo;
        return this;
    }

    public Long getFileCountBefore() {
        return fileCountBefore;
    }

    public DataRepairEffectBatch setFileCountBefore(Long fileCountBefore) {
        this.fileCountBefore = fileCountBefore;
        return this;
    }

    public String getFileListBefore() {
        return fileListBefore;
    }

    public DataRepairEffectBatch setFileListBefore(String fileListBefore) {
        this.fileListBefore = fileListBefore;
        return this;
    }

    public Long getFileCountAfter() {
        return fileCountAfter;
    }

    public DataRepairEffectBatch setFileCountAfter(Long fileCountAfter) {
        this.fileCountAfter = fileCountAfter;
        return this;
    }

    public String getFileListAfter() {
        return fileListAfter;
    }

    public DataRepairEffectBatch setFileListAfter(String fileListAfter) {
        this.fileListAfter = fileListAfter;
        return this;
    }

    public String getRepairFiles() {
        return repairFiles;
    }

    public DataRepairEffectBatch setRepairFiles(String repairFiles) {
        this.repairFiles = repairFiles;
        return this;
    }

    public String getRepairInfoIds() {
        return repairInfoIds;
    }

    public DataRepairEffectBatch setRepairInfoIds(String repairInfoIds) {
        this.repairInfoIds = repairInfoIds;
        return this;
    }

    public Boolean getDeleteFlag() {
        return deleteFlag;
    }

    public DataRepairEffectBatch setDeleteFlag(Boolean deleteFlag) {
        this.deleteFlag = deleteFlag;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public DataRepairEffectBatch setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public DataRepairEffectBatch setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public DataRepairEffectBatch setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public DataRepairEffectBatch setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }
}
