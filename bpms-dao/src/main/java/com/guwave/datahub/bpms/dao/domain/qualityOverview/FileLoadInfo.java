package com.guwave.datahub.bpms.dao.domain.qualityOverview;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.Date;

@Data
public class FileLoadInfo {

    private Long id;
    private String stepEndTimeWeek;
    private String testArea;
    private String factory;
    private String factorySite;
    private String fab;
    private String fabSite;
    private String fileCategory;
    private String fileName;
    private Long fileSize;
    private Long originFileSize;
    private String originFileName;
    private Integer convertFlag;
    private String sourceFileNames;
    private String hdfsPath;
    private String ftpPath;
    private Date remoteFileMtime;
    private Integer step;
    private Date stepStartTime;
    private Date stepEndTime;
    private String deviceId;
    private String lotId;
    private String sbLotId;
    private String waferId;
    private String waferNo;
    private String testStage;
    private String testerName;
    private String testerType;
    private String testProgram;
    private String testProgramVersion;
    private Date startT;
    private Date finishT;

    /**
     * 有合并文件的时候 step 1000 最大的结束时间
     */
    private Date maxFirstStepEndTime;
    private String processStatus;
    private String exceptionType;
    private String failedType;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    private String errorMessage;

    /**
     * 排序时间
     */
    private Long top10SortTime;

    public static FileLoadInfo of(DataIntegrity data) {
        FileLoadInfo dto = new FileLoadInfo();
        BeanUtils.copyProperties(data, dto);
        dto.setDeviceId(data.getDeviceName());
        return dto;
    }
}
