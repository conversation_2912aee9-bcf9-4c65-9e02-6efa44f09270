package com.guwave.datahub.bpms.dao.repository.clickhouse.dws;

import com.guwave.datahub.bpms.dao.clickhouse.domain.dws.DwsFlowidBinIndex;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * 2025/4/28 10:08
 * DwsSblotIndexRepository
 *
 * <AUTHOR>
 */
@Repository
public interface DwsFlowidBinIndexRepository extends JpaRepository<DwsFlowidBinIndex, String> {

    List<DwsFlowidBinIndex> findAllByCustomerAndUploadTypeAndIsDeleteAndFinalFlagAndFileIdIn(
            @Param("customer") String customer,
            @Param("uploadType") String uploadType,
            @Param("isDelete") Byte isDelete,
            @Param("finalFlag") Byte finalFlag,
            @Param("fileId") Collection<Long> fileIds
    );


    List<DwsFlowidBinIndex> findAllByCustomerAndUploadTypeAndIsDeleteAndSubCustomerAndFactoryAndFactorySiteAndTestAreaAndDeviceIdInAndLotTypeInAndTestStageInAndLotIdInAndWaferNoInAndFinalFlag(
            @Param("customer") String customer,
            @Param("uploadType") String uploadType,
            @Param("isDelete") Byte isDelete,
            @Param("subCustomer") String subCustomer,
            @Param("factory") String factory,
            @Param("factorySite") String factorySite,
            @Param("testArea") String testArea,
            @Param("deviceId") Collection<String> deviceId,
            @Param("lotType") Collection<String> lotType,
            @Param("testStage") Collection<String> testStage,
            @Param("lotId") Collection<String> lotId,
            @Param("waferNo") Collection<String> waferNo,
            @Param("finalFlag") byte finalFlag
    );

    List<DwsFlowidBinIndex> findAllByCustomerAndUploadTypeAndIsDeleteAndSubCustomerAndFactoryAndFactorySiteAndTestAreaAndDeviceIdInAndLotTypeInAndTestStageInAndLotIdInAndFinalFlag(
            @Param("customer") String customer,
            @Param("uploadType") String uploadType,
            @Param("isDelete") Byte isDelete,
            @Param("subCustomer") String subCustomer,
            @Param("factory") String factory,
            @Param("factorySite") String factorySite,
            @Param("testArea") String testArea,
            @Param("deviceId") Collection<String> deviceId,
            @Param("lotType") Collection<String> lotType,
            @Param("testStage") Collection<String> testStage,
            @Param("lotId") Collection<String> lotId,
            @Param("finalFlag") byte finalFlag
    );


}
