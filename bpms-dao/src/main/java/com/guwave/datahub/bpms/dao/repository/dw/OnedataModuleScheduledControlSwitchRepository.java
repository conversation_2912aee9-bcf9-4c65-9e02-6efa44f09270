package com.guwave.datahub.bpms.dao.repository.dw;



import com.guwave.onedata.dataware.dao.mysql.domain.dw.ModuleScheduledControlSwitch;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;

@Repository("BpmsOnedataModuleScheduledControlSwitchRepository")
public interface OnedataModuleScheduledControlSwitchRepository extends CrudRepository<ModuleScheduledControlSwitch, Long> {

    @Transactional
    @Modifying
    @Query("update com.guwave.onedata.dataware.dao.mysql.domain.dw.ModuleScheduledControlSwitch a set a.stopFlag = :stopFLag , a.updateTime = now() where a.module in (:modules)")
    void updateStopFlag(@Param("modules") List<String> modules, @Param("stopFLag") Integer stopFLag);


    @Transactional
    @Modifying
    @Query("update com.guwave.onedata.dataware.dao.mysql.domain.dw.ModuleScheduledControlSwitch a set a.stopFlag = :stopFLag , a.updateTime = now()")
    void updateAllStopFlag(@Param("stopFLag") Integer stopFLag);
}
