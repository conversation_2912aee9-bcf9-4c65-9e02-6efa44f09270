package com.guwave.datahub.bpms.dao.repository.clickhouse.dws.mapper;

import com.guwave.datahub.bpms.dao.clickhouse.dto.AccuracyLotWaferFileDetailDTO;
import com.guwave.datahub.bpms.dao.clickhouse.domain.dws.DwsFlowidBinIndex;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 2025/5/14 18:12
 * DwsFlowidBinIndexMapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DwsFlowidBinIndexMapper {

    DwsFlowidBinIndex selectOnlineRetestBins(
            @Param("fileId") String fileId,
            @Param("deviceId") String deviceId,
            @Param("testStage") String testStage,
            @Param("lotId") String lotId,
            @Param("sblotId") String sblotId
    );

    List<AccuracyLotWaferFileDetailDTO> findLotWaferFileDetail(
            @Param("customer") String customer,
            @Param("subCustomer") String subCustomer,
            @Param("factory") String factory,
            @Param("testArea") String testArea,
            @Param("deviceId") String deviceId,
            @Param("testStage") String testStage,
            @Param("lotId") String lotId,
            @Param("waferNo") String waferNo,
            @Param("lotType") String lotType
    );

}
