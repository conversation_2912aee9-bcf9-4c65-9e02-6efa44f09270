package com.guwave.datahub.bpms.dao.vo.bpms.dailyReport;

import java.util.Date;

public class TimelinessResult {

    private Date createTime;
    private String testArea;
    private String factory;
    private String deviceId;
    private String lotId;
    private String waferNo;
    private String testStage;
    private String lotType;
    private Long calCnt;
    private Long repairCnt;
    private Long replayCnt;
    private Long dieCount;
    private Long testItemCount;
    private Date maxFinishTime;
    private Date minFtpMtime;
    private Date maxFtpMtime;
    private Long scanWaitDuration;
    private String lastFileDownloadStart;
    private Long uploadToDownloadDuration;
    private Long totalFileScriptParseDuration;
    private Long downloadToPreparseGap;
    private String lastFilePreparseStart;
    private Long totalPreparseDuration;
    private Long preparseToParseGap;
    private String lastFileParseStart;
    private Long totalParseDuration;
    private Long parseToDieGap;
    private String dieCalculationStart;
    private Long dieCalculationDuration;
    private Long dieToTestitemGap;
    private String testitemCalculationStart;
    private Long testitemCalculationDuration;
    private Long dieTotalDuration;
    private Long dieDurationWithoutDownload;
    private Long totalDuration;
    private Long totalDurationWithoutDownload;

    public TimelinessResult(Date createTime, String testArea, String factory, String deviceId, String lotId, String waferNo, String testStage, String lotType, Long calCnt, Long repairCnt, Long replayCnt, Long dieCount, Long testItemCount, Date maxFinishTime, Date minFtpMtime, Date maxFtpMtime, Long scanWaitDuration, String lastFileDownloadStart, Long uploadToDownloadDuration, Long totalFileScriptParseDuration, Long downloadToPreparseGap, String lastFilePreparseStart, Long totalPreparseDuration, Long preparseToParseGap, String lastFileParseStart, Long totalParseDuration, Long parseToDieGap, String dieCalculationStart, Long dieCalculationDuration, Long dieToTestitemGap, String testitemCalculationStart, Long testitemCalculationDuration, Long dieTotalDuration, Long dieDurationWithoutDownload, Long totalDuration, Long totalDurationWithoutDownload) {
        this.createTime = createTime;
        this.testArea = testArea;
        this.factory = factory;
        this.deviceId = deviceId;
        this.lotId = lotId;
        this.waferNo = waferNo;
        this.testStage = testStage;
        this.lotType = lotType;
        this.calCnt = calCnt;
        this.repairCnt = repairCnt;
        this.replayCnt = replayCnt;
        this.dieCount = dieCount;
        this.testItemCount = testItemCount;
        this.maxFinishTime = maxFinishTime;
        this.minFtpMtime = minFtpMtime;
        this.maxFtpMtime = maxFtpMtime;
        this.scanWaitDuration = scanWaitDuration;
        this.lastFileDownloadStart = lastFileDownloadStart;
        this.uploadToDownloadDuration = uploadToDownloadDuration;
        this.totalFileScriptParseDuration = totalFileScriptParseDuration;
        this.downloadToPreparseGap = downloadToPreparseGap;
        this.lastFilePreparseStart = lastFilePreparseStart;
        this.totalPreparseDuration = totalPreparseDuration;
        this.preparseToParseGap = preparseToParseGap;
        this.lastFileParseStart = lastFileParseStart;
        this.totalParseDuration = totalParseDuration;
        this.parseToDieGap = parseToDieGap;
        this.dieCalculationStart = dieCalculationStart;
        this.dieCalculationDuration = dieCalculationDuration;
        this.dieToTestitemGap = dieToTestitemGap;
        this.testitemCalculationStart = testitemCalculationStart;
        this.testitemCalculationDuration = testitemCalculationDuration;
        this.dieTotalDuration = dieTotalDuration;
        this.dieDurationWithoutDownload = dieDurationWithoutDownload;
        this.totalDuration = totalDuration;
        this.totalDurationWithoutDownload = totalDurationWithoutDownload;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getTestArea() {
        return testArea;
    }

    public void setTestArea(String testArea) {
        this.testArea = testArea;
    }

    public String getFactory() {
        return factory;
    }

    public void setFactory(String factory) {
        this.factory = factory;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getLotId() {
        return lotId;
    }

    public void setLotId(String lotId) {
        this.lotId = lotId;
    }

    public String getWaferNo() {
        return waferNo;
    }

    public void setWaferNo(String waferNo) {
        this.waferNo = waferNo;
    }

    public String getTestStage() {
        return testStage;
    }

    public void setTestStage(String testStage) {
        this.testStage = testStage;
    }

    public String getLotType() {
        return lotType;
    }

    public void setLotType(String lotType) {
        this.lotType = lotType;
    }

    public Long getCalCnt() {
        return calCnt;
    }

    public void setCalCnt(Long calCnt) {
        this.calCnt = calCnt;
    }

    public Long getRepairCnt() {
        return repairCnt;
    }

    public void setRepairCnt(Long repairCnt) {
        this.repairCnt = repairCnt;
    }

    public Long getReplayCnt() {
        return replayCnt;
    }

    public void setReplayCnt(Long replayCnt) {
        this.replayCnt = replayCnt;
    }

    public Long getDieCount() {
        return dieCount;
    }

    public void setDieCount(Long dieCount) {
        this.dieCount = dieCount;
    }

    public Long getTestItemCount() {
        return testItemCount;
    }

    public void setTestItemCount(Long testItemCount) {
        this.testItemCount = testItemCount;
    }

    public Date getMaxFinishTime() {
        return maxFinishTime;
    }

    public void setMaxFinishTime(Date maxFinishTime) {
        this.maxFinishTime = maxFinishTime;
    }

    public Date getMinFtpMtime() {
        return minFtpMtime;
    }

    public void setMinFtpMtime(Date minFtpMtime) {
        this.minFtpMtime = minFtpMtime;
    }

    public Date getMaxFtpMtime() {
        return maxFtpMtime;
    }

    public void setMaxFtpMtime(Date maxFtpMtime) {
        this.maxFtpMtime = maxFtpMtime;
    }

    public Long getScanWaitDuration() {
        return scanWaitDuration;
    }

    public void setScanWaitDuration(Long scanWaitDuration) {
        this.scanWaitDuration = scanWaitDuration;
    }

    public String getLastFileDownloadStart() {
        return lastFileDownloadStart;
    }

    public void setLastFileDownloadStart(String lastFileDownloadStart) {
        this.lastFileDownloadStart = lastFileDownloadStart;
    }

    public Long getUploadToDownloadDuration() {
        return uploadToDownloadDuration;
    }

    public void setUploadToDownloadDuration(Long uploadToDownloadDuration) {
        this.uploadToDownloadDuration = uploadToDownloadDuration;
    }

    public Long getTotalFileScriptParseDuration() {
        return totalFileScriptParseDuration;
    }

    public void setTotalFileScriptParseDuration(Long totalFileScriptParseDuration) {
        this.totalFileScriptParseDuration = totalFileScriptParseDuration;
    }

    public Long getDownloadToPreparseGap() {
        return downloadToPreparseGap;
    }

    public void setDownloadToPreparseGap(Long downloadToPreparseGap) {
        this.downloadToPreparseGap = downloadToPreparseGap;
    }

    public String getLastFilePreparseStart() {
        return lastFilePreparseStart;
    }

    public void setLastFilePreparseStart(String lastFilePreparseStart) {
        this.lastFilePreparseStart = lastFilePreparseStart;
    }

    public Long getTotalPreparseDuration() {
        return totalPreparseDuration;
    }

    public void setTotalPreparseDuration(Long totalPreparseDuration) {
        this.totalPreparseDuration = totalPreparseDuration;
    }

    public Long getPreparseToParseGap() {
        return preparseToParseGap;
    }

    public void setPreparseToParseGap(Long preparseToParseGap) {
        this.preparseToParseGap = preparseToParseGap;
    }

    public String getLastFileParseStart() {
        return lastFileParseStart;
    }

    public void setLastFileParseStart(String lastFileParseStart) {
        this.lastFileParseStart = lastFileParseStart;
    }

    public Long getTotalParseDuration() {
        return totalParseDuration;
    }

    public void setTotalParseDuration(Long totalParseDuration) {
        this.totalParseDuration = totalParseDuration;
    }

    public Long getParseToDieGap() {
        return parseToDieGap;
    }

    public void setParseToDieGap(Long parseToDieGap) {
        this.parseToDieGap = parseToDieGap;
    }

    public String getDieCalculationStart() {
        return dieCalculationStart;
    }

    public void setDieCalculationStart(String dieCalculationStart) {
        this.dieCalculationStart = dieCalculationStart;
    }

    public Long getDieCalculationDuration() {
        return dieCalculationDuration;
    }

    public void setDieCalculationDuration(Long dieCalculationDuration) {
        this.dieCalculationDuration = dieCalculationDuration;
    }

    public Long getDieToTestitemGap() {
        return dieToTestitemGap;
    }

    public void setDieToTestitemGap(Long dieToTestitemGap) {
        this.dieToTestitemGap = dieToTestitemGap;
    }

    public String getTestitemCalculationStart() {
        return testitemCalculationStart;
    }

    public void setTestitemCalculationStart(String testitemCalculationStart) {
        this.testitemCalculationStart = testitemCalculationStart;
    }

    public Long getTestitemCalculationDuration() {
        return testitemCalculationDuration;
    }

    public void setTestitemCalculationDuration(Long testitemCalculationDuration) {
        this.testitemCalculationDuration = testitemCalculationDuration;
    }

    public Long getDieTotalDuration() {
        return dieTotalDuration;
    }

    public void setDieTotalDuration(Long dieTotalDuration) {
        this.dieTotalDuration = dieTotalDuration;
    }

    public Long getDieDurationWithoutDownload() {
        return dieDurationWithoutDownload;
    }

    public void setDieDurationWithoutDownload(Long dieDurationWithoutDownload) {
        this.dieDurationWithoutDownload = dieDurationWithoutDownload;
    }

    public Long getTotalDuration() {
        return totalDuration;
    }

    public void setTotalDuration(Long totalDuration) {
        this.totalDuration = totalDuration;
    }

    public Long getTotalDurationWithoutDownload() {
        return totalDurationWithoutDownload;
    }

    public void setTotalDurationWithoutDownload(Long totalDurationWithoutDownload) {
        this.totalDurationWithoutDownload = totalDurationWithoutDownload;
    }
}
