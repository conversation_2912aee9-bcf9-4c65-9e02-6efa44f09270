package com.guwave.datahub.bpms.dao.vo.bpms.dataIntegrity;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataIntegrityRecord {

    private Long id;

    private String fileName;

    private String fileLocation;

    private String dataLoaded;

    private String dataLoadingFailedStep;

    private String loadingFailedType;

    private String originalFailedType;

    private String updateTime;

    private String factory;

    private String deviceName;

    private String testStage;

    private String lotId;

    private String sblotId;

    private String testProgram;

    private String waferId;

    private String waferNo;

    private String lotType;

    private String fileCategory;

    private String testArea;

    private String subCustomer;

    private String startTime;

    private String endTime;

    private String fileLabel;

    private String remark;

    private String failedFields;

    private Integer enableDownload;

    private Integer deleteFlag;

    private Integer reRunFlag;

    private Integer repairFlag;
}
