package com.guwave.datahub.bpms.dao.domain.warehousingRule;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * 2025/3/4 15:11
 * ModifyWarehousingRuleRecord
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "bz_modify_warehousing_rule_record")
public class ModifyWarehousingRuleRecord implements Serializable {

    private static final long serialVersionUID = 4578154400225054822L;
    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    /**
     * 客户名
     */
    @Column(name = "customer")
    private String customer;

    /**
     * 修改的表名：dw_sftp_file_rename_rule，dw_sftp_sub_customer_rule，dw_source_standard_field_rule，dw_source_standard_device_id_rule，dw_data_clear_rule，dw_sftp_file_convert_script
     */
    @Column(name = "rule_table")
    private String ruleTable;

    /**
     * 操作类型：DELETE、UPDATE、INSERT、OVERWRITE
     */
    @Column(name = "operation_type")
    private String operationType;

    /**
     * 操作的数据，DELETE和UPDATE时为原来的数据，OVERWRITE和INSERT时为空
     */
    @Lob
    @Column(name = "data_snapshot")
    private String dataSnapshot;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    @Builder.Default
    private String createUser = "System";

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    @Builder.Default
    private String updateUser = "System";
}
