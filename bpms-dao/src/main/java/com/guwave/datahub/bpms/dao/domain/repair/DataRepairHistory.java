package com.guwave.datahub.bpms.dao.domain.repair;

import org.hibernate.annotations.Comment;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 2025/5/6 17:37
 * DataRepairHistory
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "bz_data_repair_history")
public class DataRepairHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "customer")
    @Comment("客户")
    private String customer;

    @Column(name = "sub_customer")
    @Comment("子客户")
    private String subCustomer;

    @Column(name = "factory")
    @Comment("factory")
    private String factory;

    @Column(name = "factory_site")
    @Comment("factory_site")
    private String factorySite;

    @Column(name = "repair_batch_id")
    @Comment("处理批次")
    private Long repairBatchId;

    @Column(name = "data_repair_id")
    @Comment("data_repair_id")
    private Long dataRepairId;

    @Column(name = "file_id")
    @Comment("fileId")
    private Long fileId;

    @Column(name = "file_name", nullable = false)
    @Comment("文件名称")
    private String fileName;

    @Column(name = "wafer_id")
    @Comment("wafer_id")
    private String waferId;

    @Column(name = "test_area")
    @Comment("测试阶段，CP/FT")
    private String testArea;

    @Column(name = "repair_category")
    @Comment("修改类别 DataAccuracy,DataCompletion")
    private String repairCategory;

    @Column(name = "last_repair_type")
    @Comment("修改类型 准确性 默认为关键字段调整")
    private String lastRepairType;

    @Column(name = "repair_status")
    @Comment("修改状态 Success,Fail,Process")
    private String repairStatus;

    @Column(name = "repair_error_info", columnDefinition = "text")
    @Comment("修改异常信息")
    private String repairErrorInfo;

    @Column(name = "original_device_id")
    @Comment("原始的device_id")
    private String originalDeviceId;

    @Column(name = "original_test_stage")
    @Comment("原始的test stage")
    private String originalTestStage;

    @Column(name = "original_lot_id")
    @Comment("原始的lot_id")
    private String originalLotId;

    @Column(name = "original_wafer_no")
    @Comment("原始的wafer_no")
    private String originalWaferNo;

    @Column(name = "original_wafer_id")
    @Comment("原始的wafer_id")
    private String originalWaferId;

    @Column(name = "original_sblot_id")
    @Comment("原始的sblot_id")
    private String originalSblotId;

    @Column(name = "original_test_program")
    @Comment("原始的test_program")
    private String originalTestProgram;

    @Column(name = "original_lot_type")
    @Comment("原始的lot_type")
    private String originalLotType;

    @Column(name = "original_retest_hbin_number")
    @Comment("原始的retest_hbin_number")
    private String originalRetestHbinNumber;

    @Column(name = "original_flow_id")
    @Comment("原始的flow_id")
    private String originalFlowId;

    @Column(name = "modify_device_id")
    @Comment("修改的device_id")
    private String modifyDeviceId;

    @Column(name = "modify_test_stage")
    @Comment("修改的test stage")
    private String modifyTestStage;

    @Column(name = "modify_lot_id")
    @Comment("修改的lot_id")
    private String modifyLotId;

    @Column(name = "modify_wafer_no")
    @Comment("修改的wafer_no")
    private String modifyWaferNo;

    @Column(name = "modify_sblot_id")
    @Comment("修改的sblot_id")
    private String modifySblotId;

    @Column(name = "modify_test_program")
    @Comment("修改的test_program")
    private String modifyTestProgram;

    @Column(name = "modify_lot_type")
    @Comment("修改的lot_type")
    private String modifyLotType;

    @Column(name = "modify_retest_hbin_number")
    @Comment("修改的retest_hbin_number")
    private String modifyRetestHbinNumber;

    @Column(name = "modify_flow_id")
    @Comment("修改的flow_id")
    private String modifyFlowId;

    @Column(name = "repair_json", columnDefinition = "text")
    @Comment("修改内容json")
    private String repairJson;

    @Column(name = "repair_times", nullable = false)
    @Comment("当前修改次数")
    private Integer repairTimes = 0;

    @Column(name = "delete_flag", nullable = false)
    @Comment("删除标志,0:未删除，1:已删除")
    private Boolean deleteFlag = false;

    @Column(name = "create_time")
    @Comment("创建时间")
    private Date createTime;

    @Column(name = "update_time")
    @Comment("更新时间")
    private Date updateTime;

    @Column(name = "create_user")
    @Comment("创建用户")
    private String createUser = "System";

    @Column(name = "update_user")
    @Comment("更新用户")
    private String updateUser = "System";

    public Long getId() {
        return id;
    }

    public DataRepairHistory setId(Long id) {
        this.id = id;
        return this;
    }

    public String getCustomer() {
        return customer;
    }

    public DataRepairHistory setCustomer(String customer) {
        this.customer = customer;
        return this;
    }

    public String getSubCustomer() {
        return subCustomer;
    }

    public DataRepairHistory setSubCustomer(String subCustomer) {
        this.subCustomer = subCustomer;
        return this;
    }

    public String getFactory() {
        return factory;
    }

    public DataRepairHistory setFactory(String factory) {
        this.factory = factory;
        return this;
    }

    public String getFactorySite() {
        return factorySite;
    }

    public DataRepairHistory setFactorySite(String factorySite) {
        this.factorySite = factorySite;
        return this;
    }

    public Long getRepairBatchId() {
        return repairBatchId;
    }

    public DataRepairHistory setRepairBatchId(Long repairBatchId) {
        this.repairBatchId = repairBatchId;
        return this;
    }

    public Long getDataRepairId() {
        return dataRepairId;
    }

    public DataRepairHistory setDataRepairId(Long dataRepairId) {
        this.dataRepairId = dataRepairId;
        return this;
    }

    public Long getFileId() {
        return fileId;
    }

    public DataRepairHistory setFileId(Long fileId) {
        this.fileId = fileId;
        return this;
    }

    public String getFileName() {
        return fileName;
    }

    public DataRepairHistory setFileName(String fileName) {
        this.fileName = fileName;
        return this;
    }

    public String getWaferId() {
        return waferId;
    }

    public DataRepairHistory setWaferId(String waferId) {
        this.waferId = waferId;
        return this;
    }

    public String getTestArea() {
        return testArea;
    }

    public DataRepairHistory setTestArea(String testArea) {
        this.testArea = testArea;
        return this;
    }

    public String getRepairCategory() {
        return repairCategory;
    }

    public DataRepairHistory setRepairCategory(String repairCategory) {
        this.repairCategory = repairCategory;
        return this;
    }

    public String getLastRepairType() {
        return lastRepairType;
    }

    public DataRepairHistory setLastRepairType(String lastRepairType) {
        this.lastRepairType = lastRepairType;
        return this;
    }

    public String getRepairStatus() {
        return repairStatus;
    }

    public DataRepairHistory setRepairStatus(String repairStatus) {
        this.repairStatus = repairStatus;
        return this;
    }

    public String getRepairErrorInfo() {
        return repairErrorInfo;
    }

    public DataRepairHistory setRepairErrorInfo(String repairErrorInfo) {
        this.repairErrorInfo = repairErrorInfo;
        return this;
    }

    public String getOriginalDeviceId() {
        return originalDeviceId;
    }

    public DataRepairHistory setOriginalDeviceId(String originalDeviceId) {
        this.originalDeviceId = originalDeviceId;
        return this;
    }

    public String getOriginalTestStage() {
        return originalTestStage;
    }

    public DataRepairHistory setOriginalTestStage(String originalTestStage) {
        this.originalTestStage = originalTestStage;
        return this;
    }

    public String getOriginalLotId() {
        return originalLotId;
    }

    public DataRepairHistory setOriginalLotId(String originalLotId) {
        this.originalLotId = originalLotId;
        return this;
    }

    public String getOriginalWaferNo() {
        return originalWaferNo;
    }

    public DataRepairHistory setOriginalWaferNo(String originalWaferNo) {
        this.originalWaferNo = originalWaferNo;
        return this;
    }

    public String getOriginalWaferId() {
        return originalWaferId;
    }

    public DataRepairHistory setOriginalWaferId(String originalWaferId) {
        this.originalWaferId = originalWaferId;
        return this;
    }

    public String getOriginalSblotId() {
        return originalSblotId;
    }

    public DataRepairHistory setOriginalSblotId(String originalSblotId) {
        this.originalSblotId = originalSblotId;
        return this;
    }

    public String getOriginalTestProgram() {
        return originalTestProgram;
    }

    public DataRepairHistory setOriginalTestProgram(String originalTestProgram) {
        this.originalTestProgram = originalTestProgram;
        return this;
    }

    public String getOriginalLotType() {
        return originalLotType;
    }

    public DataRepairHistory setOriginalLotType(String originalLotType) {
        this.originalLotType = originalLotType;
        return this;
    }

    public String getOriginalRetestHbinNumber() {
        return originalRetestHbinNumber;
    }

    public DataRepairHistory setOriginalRetestHbinNumber(String originalRetestHbinNumber) {
        this.originalRetestHbinNumber = originalRetestHbinNumber;
        return this;
    }

    public String getOriginalFlowId() {
        return originalFlowId;
    }

    public DataRepairHistory setOriginalFlowId(String originalFlowId) {
        this.originalFlowId = originalFlowId;
        return this;
    }

    public String getModifyDeviceId() {
        return modifyDeviceId;
    }

    public DataRepairHistory setModifyDeviceId(String modifyDeviceId) {
        this.modifyDeviceId = modifyDeviceId;
        return this;
    }

    public String getModifyTestStage() {
        return modifyTestStage;
    }

    public DataRepairHistory setModifyTestStage(String modifyTestStage) {
        this.modifyTestStage = modifyTestStage;
        return this;
    }

    public String getModifyLotId() {
        return modifyLotId;
    }

    public DataRepairHistory setModifyLotId(String modifyLotId) {
        this.modifyLotId = modifyLotId;
        return this;
    }

    public String getModifyWaferNo() {
        return modifyWaferNo;
    }

    public DataRepairHistory setModifyWaferNo(String modifyWaferNo) {
        this.modifyWaferNo = modifyWaferNo;
        return this;
    }

    public String getModifySblotId() {
        return modifySblotId;
    }

    public DataRepairHistory setModifySblotId(String modifySblotId) {
        this.modifySblotId = modifySblotId;
        return this;
    }

    public String getModifyTestProgram() {
        return modifyTestProgram;
    }

    public DataRepairHistory setModifyTestProgram(String modifyTestProgram) {
        this.modifyTestProgram = modifyTestProgram;
        return this;
    }

    public String getModifyLotType() {
        return modifyLotType;
    }

    public DataRepairHistory setModifyLotType(String modifyLotType) {
        this.modifyLotType = modifyLotType;
        return this;
    }

    public String getModifyRetestHbinNumber() {
        return modifyRetestHbinNumber;
    }

    public DataRepairHistory setModifyRetestHbinNumber(String modifyRetestHbinNumber) {
        this.modifyRetestHbinNumber = modifyRetestHbinNumber;
        return this;
    }

    public String getModifyFlowId() {
        return modifyFlowId;
    }

    public DataRepairHistory setModifyFlowId(String modifyFlowId) {
        this.modifyFlowId = modifyFlowId;
        return this;
    }

    public String getRepairJson() {
        return repairJson;
    }

    public DataRepairHistory setRepairJson(String repairJson) {
        this.repairJson = repairJson;
        return this;
    }

    public Integer getRepairTimes() {
        return repairTimes;
    }

    public DataRepairHistory setRepairTimes(Integer repairTimes) {
        this.repairTimes = repairTimes;
        return this;
    }

    public Boolean getDeleteFlag() {
        return deleteFlag;
    }

    public DataRepairHistory setDeleteFlag(Boolean deleteFlag) {
        this.deleteFlag = deleteFlag;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public DataRepairHistory setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public DataRepairHistory setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public DataRepairHistory setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public DataRepairHistory setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }
}