package com.guwave.datahub.bpms.dao.domain.schedule;

import com.guwave.datahub.bpms.common.constant.ExceptionType;
import com.guwave.datahub.bpms.common.constant.ExecuteEngine;
import com.guwave.datahub.bpms.dao.converter.ExceptionTypeConverter;
import com.guwave.datahub.bpms.dao.converter.ExecuteEngineConverter;
import com.guwave.datahub.bpms.dao.converter.ProcessStatusConverter;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 定时任务执行记录表实体
 */
@Data
@Entity
@Table(name = "bz_schedule_task_record", indexes = {
        @Index(name = "idx_task_definition_id", columnList = "task_definition_id")
})
public class ScheduleTaskRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "task_definition_id", nullable = false)
    private Long taskDefinitionId;

    @Column(name = "task_name", nullable = false)
    private String taskName;

    @Column(name = "execute_engine")
    @Convert(converter = ExecuteEngineConverter.class)
    private ExecuteEngine executeEngine;

    @Column(name = "cron_expression", nullable = false)
    private String cronExpression;

    @Lob
    @Column(name = "command")
    private String command;

    @Column(name = "unique_id")
    private String uniqueId; // SPARK任务唯一标识

    @Enumerated(EnumType.STRING)
    @Column(name = "process_status")
    @Convert(converter = ProcessStatusConverter.class)
    private ProcessStatus processStatus;

    @Column(name = "schedule_time")
    private Date scheduleTime; // 调度任务生成时间

    @Column(name = "start_time")
    private Date startTime;

    @Column(name = "end_time")
    private Date endTime;

    @Column(name = "execute_time")
    private Long executeTime;

    @Column(name = "exception_type")
    @Convert(converter = ExceptionTypeConverter.class)
    private ExceptionType exceptionType;

    @Lob
    @Column(name = "error_message")
    private String errorMessage;

    @Column(name = "create_time", updatable = false)
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "create_user", updatable = false)
    private String createUser = "System";

    @Column(name = "update_user")
    private String updateUser = "System";

    public Long getId() {
        return id;
    }

    public ScheduleTaskRecord setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getTaskDefinitionId() {
        return taskDefinitionId;
    }

    public ScheduleTaskRecord setTaskDefinitionId(Long taskDefinitionId) {
        this.taskDefinitionId = taskDefinitionId;
        return this;
    }

    public String getTaskName() {
        return taskName;
    }

    public ScheduleTaskRecord setTaskName(String taskName) {
        this.taskName = taskName;
        return this;
    }

    public ExecuteEngine getExecuteEngine() {
        return executeEngine;
    }

    public ScheduleTaskRecord setExecuteEngine(ExecuteEngine executeEngine) {
        this.executeEngine = executeEngine;
        return this;
    }

    public String getCronExpression() {
        return cronExpression;
    }

    public ScheduleTaskRecord setCronExpression(String cronExpression) {
        this.cronExpression = cronExpression;
        return this;
    }

    public String getCommand() {
        return command;
    }

    public ScheduleTaskRecord setCommand(String command) {
        this.command = command;
        return this;
    }

    public String getUniqueId() {
        return uniqueId;
    }

    public ScheduleTaskRecord setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
        return this;
    }

    public ProcessStatus getProcessStatus() {
        return processStatus;
    }

    public ScheduleTaskRecord setProcessStatus(ProcessStatus processStatus) {
        this.processStatus = processStatus;
        return this;
    }

    public Date getScheduleTime() {
        return scheduleTime;
    }

    public ScheduleTaskRecord setScheduleTime(Date scheduleTime) {
        this.scheduleTime = scheduleTime;
        return this;
    }

    public Date getStartTime() {
        return startTime;
    }

    public ScheduleTaskRecord setStartTime(Date startTime) {
        this.startTime = startTime;
        return this;
    }

    public Date getEndTime() {
        return endTime;
    }

    public ScheduleTaskRecord setEndTime(Date endTime) {
        this.endTime = endTime;
        return this;
    }

    public Long getExecuteTime() {
        return executeTime;
    }

    public ScheduleTaskRecord setExecuteTime(Long executeTime) {
        this.executeTime = executeTime;
        return this;
    }

    public ExceptionType getExceptionType() {
        return exceptionType;
    }

    public ScheduleTaskRecord setExceptionType(ExceptionType exceptionType) {
        this.exceptionType = exceptionType;
        return this;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public ScheduleTaskRecord setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public ScheduleTaskRecord setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public ScheduleTaskRecord setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public ScheduleTaskRecord setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public ScheduleTaskRecord setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }
}