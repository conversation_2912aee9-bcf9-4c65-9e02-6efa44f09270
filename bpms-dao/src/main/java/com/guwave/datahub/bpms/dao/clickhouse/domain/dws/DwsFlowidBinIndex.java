package com.guwave.datahub.bpms.dao.clickhouse.domain.dws;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 2025/4/28 10:20
 * DwsFlowidBinIndex
 *
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "dws_flowid_bin_index_cluster")
public class DwsFlowidBinIndex implements Serializable {

    private static final long serialVersionUID = -5160840369823072748L;

    @Id
    @Column(name = "FILE_ID", columnDefinition = "UInt32")
    private Long fileId;

    @Column(name = "CUSTOMER", columnDefinition = "String")
    private String customer;

    @Column(name = "SUB_CUSTOMER", columnDefinition = "String")
    private String subCustomer;

    @Column(name = "UPLOAD_TYPE", columnDefinition = "String")
    private String uploadType;

    @Column(name = "FACTORY", columnDefinition = "String")
    private String factory;

    @Column(name = "FACTORY_SITE", columnDefinition = "String")
    private String factorySite;

    @Column(name = "FAB", columnDefinition = "String")
    private String fab;

    @Column(name = "FAB_SITE", columnDefinition = "String")
    private String fabSite;

    @Column(name = "TEST_AREA", columnDefinition = "String")
    private String testArea;

    @Column(name = "TEST_STAGE", columnDefinition = "String")
    private String testStage;

    @Column(name = "LOT_TYPE", columnDefinition = "String")
    private String lotType;

    @Column(name = "DEVICE_ID", columnDefinition = "String")
    private String deviceId;

    @Column(name = "LOT_ID", columnDefinition = "String")
    private String lotId;

    @Column(name = "PROCESS", columnDefinition = "String")
    private String process;

    @Column(name = "SBLOT_ID", columnDefinition = "String")
    private String sblotId;

    @Column(name = "WAFER_LOT_ID", columnDefinition = "String")
    private String waferLotId;

    @Column(name = "WAFER_ID", columnDefinition = "String")
    private String waferId;

    @Column(name = "WAFER_ID_KEY", columnDefinition = "String")
    private String waferIdKey;

    @Column(name = "WAFER_NO", columnDefinition = "String")
    private String waferNo;

    @Column(name = "WAFER_NO_KEY", columnDefinition = "String")
    private String waferNoKey;

    @Column(name = "TEST_PROGRAM", columnDefinition = "String")
    private String testProgram;

    @Column(name = "TEST_TEMPERATURE", columnDefinition = "String")
    private String testTemperature;

    @Column(name = "TEST_PROGRAM_VERSION", columnDefinition = "String")
    private String testProgramVersion;

    @Column(name = "OFFLINE_RETEST", columnDefinition = "Nullable(UInt8)")
    private Byte offlineRetest;

    @Column(name = "INTERRUPT", columnDefinition = "Nullable(UInt8)")
    private Byte interrupt;

    @Column(name = "DUP_RETEST", columnDefinition = "Nullable(UInt8)")
    private Byte dupRetest;

    @Column(name = "BATCH_NUM", columnDefinition = "Nullable(UInt8)")
    private Byte batchNum;

    @Column(name = "OFFLINE_RETEST_IGNORE_TP", columnDefinition = "Nullable(UInt8)")
    private Byte offlineRetestIgnoreTp;

    @Column(name = "INTERRUPT_IGNORE_TP", columnDefinition = "Nullable(UInt8)")
    private Byte interruptIgnoreTp;

    @Column(name = "DUP_RETEST_IGNORE_TP", columnDefinition = "Nullable(UInt8)")
    private Byte dupRetestIgnoreTp;

    @Column(name = "BATCH_NUM_IGNORE_TP", columnDefinition = "Nullable(UInt8)")
    private Byte batchNumIgnoreTp;

    @Column(name = "FILE_NAME", columnDefinition = "String")
    private String fileName;

    @Column(name = "FILE_TYPE", columnDefinition = "String")
    private String fileType;

    @Column(name = "TESTER_NAME", columnDefinition = "String")
    private String testerName;

    @Column(name = "TESTER_TYPE", columnDefinition = "String")
    private String testerType;

    @Column(name = "PROBER_HANDLER_ID", columnDefinition = "String")
    private String proberHandlerId;

    @Column(name = "PROBECARD_LOADBOARD_ID", columnDefinition = "String")
    private String probecardLoadboardId;

    @Column(name = "START_TIME", columnDefinition = "Nullable(DateTime)")
    private LocalDateTime startTime;

    @Column(name = "END_TIME", columnDefinition = "Nullable(DateTime)")
    private LocalDateTime endTime;

    @Column(name = "START_HOUR_KEY", columnDefinition = "String")
    private String startHourKey;

    @Column(name = "START_DAY_KEY", columnDefinition = "String")
    private String startDayKey;

    @Column(name = "END_HOUR_KEY", columnDefinition = "String")
    private String endHourKey;

    @Column(name = "END_DAY_KEY", columnDefinition = "String")
    private String endDayKey;

    @Column(name = "FLOW_ID", columnDefinition = "String")
    private String flowId;

    @Column(name = "FLOW_ID_IGNORE_TP", columnDefinition = "String")
    private String flowIdIgnoreTp;

    @Column(name = "FINAL_FLAG", columnDefinition = "UInt8")
    private Byte finalFlag;

    @Column(name = "INPUT_CNT", columnDefinition = "Nullable(UInt32)")
    private Long inputCnt;

    @Column(name = "DIE_CNT", columnDefinition = "Nullable(UInt32)")
    private Integer dieCnt;

    @Column(name = "PASS_CNT", columnDefinition = "Nullable(UInt32)")
    private Long passCnt;

    @Column(name = "FAIL_CNT", columnDefinition = "Nullable(UInt32)")
    private Long failCnt;

    @Column(name = "YIELD", columnDefinition = "Nullable(Decimal(38, 18))")
    private BigDecimal yield;

//    @ElementCollection
//    @Column(name = "HBIN_PASS_CNT", columnDefinition = "Map(UInt32, UInt32)")
//    private Map<Integer, Integer> hbinPassCnt;
//
//    @ElementCollection
//    @Column(name = "HBIN_FAIL_CNT", columnDefinition = "Map(UInt32, UInt32)")
//    private Map<Integer, Integer> hbinFailCnt;
//
//    @ElementCollection
//    @Column(name = "HBIN_UNKNOWN_CNT", columnDefinition = "Map(UInt32, UInt32)")
//    private Map<Integer, Integer> hbinUnknownCnt;
//
//    @ElementCollection
//    @Column(name = "SBIN_PASS_CNT", columnDefinition = "Map(UInt32, UInt32)")
//    private Map<Integer, Integer> sbinPassCnt;
//
//    @ElementCollection
//    @Column(name = "SBIN_FAIL_CNT", columnDefinition = "Map(UInt32, UInt32)")
//    private Map<Integer, Integer> sbinFailCnt;
//
//    @ElementCollection
//    @Column(name = "SBIN_UNKNOWN_CNT", columnDefinition = "Map(UInt32, UInt32)")
//    private Map<Integer, Integer> sbinUnknownCnt;
//
//    @ElementCollection
//    @Column(name = "HBIN_PASS_RATE", columnDefinition = "Map(UInt32, Decimal(38, 18))")
//    private Map<Integer, BigDecimal> hbinPassRate;
//
//    @ElementCollection
//    @Column(name = "HBIN_FAIL_RATE", columnDefinition = "Map(UInt32, Decimal(38, 18))")
//    private Map<Integer, BigDecimal> hbinFailRate;
//
//    @ElementCollection
//    @Column(name = "SBIN_PASS_RATE", columnDefinition = "Map(UInt32, Decimal(38, 18))")
//    private Map<Integer, BigDecimal> sbinPassRate;
//
//    @ElementCollection
//    @Column(name = "SBIN_FAIL_RATE", columnDefinition = "Map(UInt32, Decimal(38, 18))")
//    private Map<Integer, BigDecimal> sbinFailRate;

    @Column(name = "RETEST_BIN_NUM", columnDefinition = "String")
    private String retestBinNum;

    @Column(name = "IS_ONLINE_RETEST", columnDefinition = "UInt8")
    private Byte isOnlineRetest;

    @Column(name = "ONLINE_RETEST_DIES", columnDefinition = "String")
    private String onlineRetestDies;

    @Column(name = "CREATE_HOUR_KEY", columnDefinition = "String")
    private String createHourKey;

    @Column(name = "CREATE_DAY_KEY", columnDefinition = "String")
    private String createDayKey;

    @Column(name = "CREATE_TIME", columnDefinition = "DateTime")
    private LocalDateTime createTime;

    @Column(name = "CREATE_USER", columnDefinition = "String")
    private String createUser;

    @Column(name = "UPLOAD_TIME", columnDefinition = "DateTime")
    private LocalDateTime uploadTime;

    @Column(name = "VERSION", columnDefinition = "Int64")
    private Long version;

    @Column(name = "IS_DELETE", columnDefinition = "UInt8")
    private Byte isDelete;
}

