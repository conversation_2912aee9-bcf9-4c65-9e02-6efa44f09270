package com.guwave.datahub.bpms.dao.repository.clickhouse.dwd;

import com.guwave.datahub.bpms.dao.clickhouse.domain.dwd.ClickHouseExample;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * ClickHouse示例存储库
 */
@Repository
public interface ClickHouseExampleRepository extends JpaRepository<ClickHouseExample, String> {

    /**
     * 使用原生SQL查询示例
     * @param status 状态
     * @return 匹配的记录列表
     */
    @Query(value = "SELECT ID, CUSTOMER, ECID, TEST_VALUE, IS_DELETE FROM dwd_test_item_detail_local WHERE CUSTOMER = :customer LIMIT 5", nativeQuery = true)
    List<ClickHouseExample> findSampleData(@Param("customer") String customer);

    List<ClickHouseExample> findTop10ByCustomer(String customer);
}
