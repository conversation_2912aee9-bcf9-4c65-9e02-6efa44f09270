package com.guwave.datahub.bpms.dao.domain.dailySummary;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "bz_daily_summary_query_config")
public class DailySummaryQueryConfig {
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @Column(name = "type")
    private String type;
    @Column(name = "db")
    private String db;
    @Column(name = "code")
    private String code;

    @Column(name = "query_name")
    private String queryName;
    @Column(name = "query_desc")
    private String queryDesc;

    @Column(name = "fields_query", columnDefinition = "LONGTEXT")
    private String fieldsQuery;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "update_user")
    private String updateUser;

}
