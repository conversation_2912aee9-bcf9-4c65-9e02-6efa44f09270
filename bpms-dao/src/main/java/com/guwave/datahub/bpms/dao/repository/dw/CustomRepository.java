package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpSubCustomerRule;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 2025/3/5 14:50
 * CustomRepository
 *
 * <AUTHOR>
 */
@Repository("BpmsCustomRepository")
public interface CustomRepository extends CrudRepository<SftpSubCustomerRule, Long> {

    @Query(value = "select * from (\n" +
            "select distinct sub_customer from dw_sftp_sub_customer_rule where customer = :customer and sub_customer != '' and sub_customer is not null\n" +
            "union\n" +
            "select distinct sub_customer from dw_sftp_file_rename_rule where customer = :customer and sub_customer != '' and sub_customer is not null\n" +
            "union\n" +
            "select distinct sub_customer from dw_data_clear_rule where customer = :customer and sub_customer != '' and sub_customer is not null\n" +
            "union\n" +
            "select distinct sub_customer from dw_sftp_file_convert_script where customer = :customer and sub_customer != '' and sub_customer is not null\n" +
            "union\n" +
            "select distinct sub_customer from dw_source_standard_device_id_rule where customer = :customer and sub_customer != '' and sub_customer is not null\n" +
            "union\n" +
            "select distinct sub_customer from dw_source_standard_field_rule where customer = :customer and sub_customer != '' and sub_customer is not null\n" +
            "              ) t\n" +
            "order by sub_customer",
            nativeQuery = true)
    List<String> findWarehousingRuleSubCustomer(@Param("customer") String customer);

    @Query(value = "select * from (\n" +
            "select distinct factory from dw_sftp_sub_customer_rule where customer = :customer and factory != '' and factory is not null\n" +
            "union\n" +
            "select distinct factory from dw_data_clear_rule where customer = :customer and factory != '' and factory is not null\n" +
            "union\n" +
            "select distinct factory from dw_sftp_file_convert_script where customer = :customer and factory != '' and factory is not null\n" +
            "union\n" +
            "select distinct factory from dw_source_standard_device_id_rule where customer = :customer and factory != '' and factory is not null\n" +
            "union\n" +
            "select distinct factory from dw_source_standard_field_rule where customer = :customer and factory != '' and factory is not null\n" +
            "              ) t\n" +
            "order by factory",
            nativeQuery = true)
    List<String> findWarehousingRuleFactory(@Param("customer") String customer);

    @Query(value = "select * from (\n" +
            "select distinct test_area from dw_sftp_sub_customer_rule where customer = :customer and test_area != '' and test_area is not null\n" +
            "union\n" +
            "select distinct test_area from dw_data_clear_rule where customer = :customer and test_area != '' and test_area is not null\n" +
            "union\n" +
            "select distinct test_area from dw_sftp_file_convert_script where customer = :customer and test_area != '' and test_area is not null\n" +
            "union\n" +
            "select distinct test_area from dw_source_standard_device_id_rule where customer = :customer and test_area != '' and test_area is not null\n" +
            "union\n" +
            "select distinct test_area from dw_source_standard_field_rule where customer = :customer and test_area != '' and test_area is not null\n" +
            "              ) t\n" +
            "order by test_area",
            nativeQuery = true)
    List<String> findWarehousingRuleTestArea(@Param("customer") String customer);

    @Query(value = "select distinct file_category from dw_sftp_file_convert_script where customer = :customer and file_category != '' and file_category is not null order by file_category",
            nativeQuery = true)
    List<String> findWarehousingRuleFileCategory(@Param("customer") String customer);

    @Query(value = "select sum(cnt) from (\n" +
            "(select count(1) as cnt from dw_sftp_sub_customer_rule where customer = :customer\n" +
            "union all\n" +
            "select count(1) as cnt from dw_sftp_file_rename_rule where customer = :customer\n" +
            "union all\n" +
            "select count(1) as cnt from dw_data_clear_rule where customer = :customer\n" +
            "union all\n" +
            "select count(1) as cnt from dw_sftp_file_convert_script where customer = :customer\n" +
            "union all\n" +
            "select count(1) as cnt from dw_source_standard_device_id_rule where customer = :customer\n" +
            "union all\n" +
            "select count(1) as cnt from dw_source_standard_field_rule where customer = :customer))t ",
            nativeQuery = true)
    Long getDataCnt(@Param("customer") String customer);
}
