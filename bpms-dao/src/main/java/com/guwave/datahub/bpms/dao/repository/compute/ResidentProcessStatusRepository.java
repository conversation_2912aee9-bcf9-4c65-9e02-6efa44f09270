package com.guwave.datahub.bpms.dao.repository.compute;

import com.guwave.onedata.next.compute.common.constant.ResidentStatus;
import com.guwave.onedata.next.compute.dao.mysql.domain.ResidentProcessStatus;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


public interface ResidentProcessStatusRepository extends CrudRepository<ResidentProcessStatus, Long> {
    List<ResidentProcessStatus> findByStatusInOrderByIdDesc(List<ResidentStatus> status);

    @Transactional
    @Modifying
    @Query(value = "update bz_resident_process_status\n" +
            "set status = 'DEAD'\n" +
            "where id in (select * from (select id from bz_resident_process_status where status in ('ACTIVE', 'IDLE', 'STOP')) x)", nativeQuery = true)
    void updateNotFinishToDead();

}
