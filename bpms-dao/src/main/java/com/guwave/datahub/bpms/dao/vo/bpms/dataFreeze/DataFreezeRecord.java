package com.guwave.datahub.bpms.dao.vo.bpms.dataFreeze;


public interface DataFreezeRecord {

    String getCustomer();

    String getSubCustomer();

    String getFactory();

    String getFactorySite();

    String getTestArea();

    String getLotType();

    String getDeviceId();

    String getLotId();

    String getWaferNo();

    String getTestStage();

    String getFileCategory();

    Long getWarehousingTaskRecordId();
}
