package com.guwave.datahub.bpms.dao.repository.dw;


import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.DwBzCalculateConfig;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository("BpmsDwBzCalculateConfigRepository")
public interface DwBzCalculateConfigRepository extends JpaRepository<DwBzCalculateConfig, Long>, JpaSpecificationExecutor<DwBzCalculateConfig> {

    Page<DwBzCalculateConfig> findAllByCustomerAndSubCustomerAndDeleteFlagOrderByIdDesc(String customer, String subCustomer, Integer deleteFlag, Pageable pageable);

    Optional<DwBzCalculateConfig> findByIdAndCustomerAndSubCustomerAndDeleteFlag(Long id, String customer, String subCustomer, Integer deleteFlag);

    boolean existsByCustomerAndFactoryAndFactorySiteAndDeviceIdAndTestStageAndLotTypeAndTestAreaAndFileCategoryAndDeleteFlag(String customer, String factory, String factorySite, String deviceId, String testStage, LotType lotType, TestArea testArea, FileCategory fileCategory, Integer deleteFlag);
}
