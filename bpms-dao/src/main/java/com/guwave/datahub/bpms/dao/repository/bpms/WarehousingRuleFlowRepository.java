package com.guwave.datahub.bpms.dao.repository.bpms;

import com.guwave.datahub.bpms.dao.domain.warehousingRule.WarehousingRuleFlow;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface WarehousingRuleFlowRepository extends JpaRepository<WarehousingRuleFlow, Long> {
    Optional<WarehousingRuleFlow> findFirstByIdAndDeleteFlag(Long id, int deleteFlag);

    @Query(value = "select case " +
            "           when LOWER(:filterField) = LOWER('name') then NAME " +
            "           when LOWER(:filterField) = LOWER('sub_customer') then SUB_CUSTOMER " +
            "           when LOWER(:filterField) = LOWER('factory') then FACTORY " +
            "           when LOWER(:filterField) = LOWER('test_area') then TEST_AREA " +
            "           when LOWER(:filterField) = LOWER('file_category') then FILE_CATEGORY " +
            "           when LOWER(:filterField) = LOWER('create_user') then CREATE_USER " +
            "           when LOWER(:filterField) = LOWER('update_user') then UPDATE_USER " +
            "           end as result " +
            "from bz_warehousing_rule_flow " +
            "      where (customer = :customer) " +
            "        and delete_flag = 0 " +
            "group by result " +
            "order by result",
            nativeQuery = true)
    List<String> findDynamicField(@Param("customer") String customer, @Param("filterField") String filterField);


    @Query(value = "select * from bz_warehousing_rule_flow where customer = :customer and delete_flag = 0  and (name LIKE CONCAT(:name, '%'))",
            nativeQuery = true)
    List<WarehousingRuleFlow> findAllByPrefixName(@Param("customer") String customer, @Param("name") String name);

    List<WarehousingRuleFlow> findAllByCustomerAndNameAndDeleteFlag(@Param("customer") String customer, @Param("name") String name, @Param("deleteFlag") int deleteFlag);

    @Transactional
    @Modifying
    @Query("update WarehousingRuleFlow a set a.deleteFlag = 1 where a.id in (:ids) and a.deleteFlag = 0")
    void deleteAllByIds(@Param("ids") Collection<Long> ids);

    @Query(value = "select * from bz_warehousing_rule_flow " +
            "where delete_flag = 0 " +
            "and (:customer is null or customer = :customer) " +
            "and (coalesce(:nameList,null) IS NULL OR name in (:nameList)) " +
            "and (coalesce(:subCustomerList,null) IS NULL OR sub_customer in (:subCustomerList)) " +
            "and (coalesce(:factoryList,null) IS NULL OR factory in (:factoryList)) " +
            "and (coalesce(:testAreaList,null) IS NULL OR test_area in (:testAreaList)) " +
            "and (coalesce(:fileCategoryList,null) IS NULL OR file_category in (:fileCategoryList)) " +
            "and (coalesce(:createUserList,null) IS NULL OR create_user in (:createUserList)) " +
            "and (coalesce(:updateUserList,null) IS NULL OR update_user in (:updateUserList)) " +
            "and (:createTimeStart is null or create_time >= :createTimeStart) " +
            "and (:createTimeEnd is null or create_time <= :createTimeEnd) " +
            "and (:updateTimeStart is null or update_time >= :updateTimeStart) " +
            "and (:updateTimeEnd is null or update_time <= :updateTimeEnd) ",
            nativeQuery = true)
    Page<WarehousingRuleFlow> findPageByFilters(
            @Param("nameList") Collection<String> nameList,
            @Param("customer") String customer,
            @Param("subCustomerList") Collection<String> subCustomerList,
            @Param("factoryList") Collection<String> factoryList,
            @Param("testAreaList") Collection<String> testAreaList,
            @Param("fileCategoryList") Collection<String> fileCategoryList,
            @Param("createUserList") Collection<String> createUserList,
            @Param("updateUserList") Collection<String> updateUserList,
            @Param("createTimeStart") String createTimeStart,
            @Param("createTimeEnd") String createTimeEnd,
            @Param("updateTimeStart") String updateTimeStart,
            @Param("updateTimeEnd") String updateTimeEnd,
            Pageable pageable
    );

    List<WarehousingRuleFlow> findAllByCustomerAndDeleteFlag(String customer, int deleteFlag);

    @Transactional
    @Modifying
    @Query(value = "delete from WarehousingRuleFlow a where a.customer = :customer")
    void deleteAllByCustomer(@Param("customer") String customer);

    @Query(value = "select count(1) from bz_warehousing_rule_flow where customer = :customer",
            nativeQuery = true)
    Long getDataCnt(@Param("customer") String customer);
}