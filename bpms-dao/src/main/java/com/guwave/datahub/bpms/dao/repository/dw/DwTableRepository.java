package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.common.contant.DwLayer;
import com.guwave.onedata.dataware.common.contant.UploadType;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.DwTable;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository("BpmsDwTableRepository")
public interface DwTableRepository extends CrudRepository<DwTable, Long> {

    List<DwTable> findAllByUploadTypeAndDwLayerInAndCalculateFlag(UploadType uploadType, Collection<DwLayer> dwLayer, Integer calculateFlag);

}
