package com.guwave.datahub.bpms.dao.repository.clickhouse.dws.mapper;

import com.guwave.datahub.bpms.dao.domain.qualityOverview.StatisticsIndex;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DwsSblotIndexMapper {

    List<String> queryOverviewDropdownFieldFt(
            @Param("customer") String customer,
            @Param("filterField") String filterField,
            @Param("subCustomerList") List<String> subCustomerList,
            @Param("factoryList") List<String> factoryList,
            @Param("deviceIdList") List<String> deviceIdList,
            @Param("testArea") String testArea,
            @Param("testProgramList") List<String> testProgramList,
            @Param("testStageList") List<String> testStageList);

    List<StatisticsIndex> selectStatisticsDataFt(
            @Param("subCustomerList") List<String> subCustomerList,
            @Param("factoryList") List<String> factoryList,
            @Param("deviceIdList") List<String> deviceIdList,
            @Param("testArea") String testArea,
            @Param("testProgramList") List<String> testProgramList,
            @Param("testStageList") List<String> testStageList);
}
    