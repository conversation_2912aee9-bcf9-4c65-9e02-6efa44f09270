package com.guwave.datahub.bpms.dao.repository.bpms;

import com.guwave.datahub.bpms.common.constant.ExecuteEngine;
import com.guwave.datahub.bpms.dao.domain.schedule.ScheduleTaskRecord;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * TaskRecord Repository
 */
@Repository
public interface ScheduleTaskRecordRepository extends JpaRepository<ScheduleTaskRecord, Long> {
    List<ScheduleTaskRecord> findByProcessStatusAndUniqueId(ProcessStatus processStatus, String uniqueId);
    ScheduleTaskRecord findFirstByProcessStatusAndExecuteEngineInOrderByIdAsc(ProcessStatus processStatus, List<ExecuteEngine> executeEngine);
}