package com.guwave.datahub.bpms.dao.vo.bpms.dailyReport;

import java.util.Map;

public class CollectxTimeoutPullingResult {

    private Long timeoutBatchCnt;
    private Map<Long, Long> timeoutRuleMap;

    public Long getTimeoutBatchCnt() {
        return timeoutBatchCnt;
    }

    public void setTimeoutBatchCnt(Long timeoutBatchCnt) {
        this.timeoutBatchCnt = timeoutBatchCnt;
    }

    public Map<Long, Long> getTimeoutRuleMap() {
        return timeoutRuleMap;
    }

    public void setTimeoutRuleMap(Map<Long, Long> timeoutRuleMap) {
        this.timeoutRuleMap = timeoutRuleMap;
    }
}
