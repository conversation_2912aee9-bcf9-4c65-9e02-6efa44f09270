package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.dao.mysql.domain.dw.CleanupTask;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("BpmsCleanupTaskRepository")
public interface CleanupTaskRepository extends CrudRepository<CleanupTask, Long> {

    List<CleanupTask> findAllByDataReplayRecordId(Long dataReplayRecordId);

    @Query(value = "select * from dw_cleanup_task" +
            "      where (data_replay_record_id = ?1)  " +
            "        and (customer = ?2)  " +
            "        and (coalesce(?3,null) IS NULL OR lot_id in (?3)) " +
            "        and (coalesce(?4,null) IS NULL OR wafer_no in (?4)) " +
            "        and (coalesce(?5,null) IS NULL OR test_stage in (?5)) " +
            "        and (coalesce(?6,null) IS NULL OR process_status in (?6))" +
            "        and (coalesce(?7,null) IS NULL OR exception_message in (?7))" +
            "ORDER BY FIELD(process_status, 'FAIL', 'PROCESSING', 'CREATE', 'SUCCESS'), update_time DESC",
            nativeQuery = true)
    List<CleanupTask> findAllByReplayRecordIdAndFilters(
            @Param("replayRecordId") Long replayRecordId,
            @Param("customer") String customer,
            @Param("lotId") List<String> lotId,
            @Param("waferNo") List<String> waferNo,
            @Param("testStage") List<String> testStage,
            @Param("processStatus") List<String> processStatus,
            @Param("exception") List<String> exception
    );

    @Query(value = "select * from dw_cleanup_task" +
            "      where (data_replay_record_id = ?1)  " +
            "        and (customer = ?2)  " +
            "        and (coalesce(?3,null) IS NULL OR lot_id in (?3)) " +
            "        and (coalesce(?4,null) IS NULL OR wafer_no in (?4)) " +
            "        and (coalesce(?5,null) IS NULL OR test_stage in (?5)) " +
            "        and (coalesce(?6,null) IS NULL OR process_status in (?6))" +
            "        and (coalesce(?7,null) IS NULL OR exception_message in (?7))" +
            "ORDER BY FIELD(process_status, 'FAIL', 'PROCESSING', 'CREATE', 'SUCCESS'), update_time DESC",
            countQuery = "select count(1) from dw_cleanup_task" +
                    "      where (data_replay_record_id = ?1)  " +
                    "        and (customer = ?2)  " +
                    "        and (coalesce(?3,null) IS NULL OR lot_id in (?3)) " +
                    "        and (coalesce(?4,null) IS NULL OR wafer_no in (?4)) " +
                    "        and (coalesce(?5,null) IS NULL OR test_stage in (?5)) " +
                    "        and (coalesce(?6,null) IS NULL OR process_status in (?6))" +
                    "        and (coalesce(?7,null) IS NULL OR exception_message in (?7))",
            nativeQuery = true)
    Page<CleanupTask> findPageByReplayRecordIdAndFilters(
            @Param("replayRecordId") Long replayRecordId,
            @Param("customer") String customer,
            @Param("lotId") List<String> lotId,
            @Param("waferNo") List<String> waferNo,
            @Param("testStage") List<String> testStage,
            @Param("processStatus") List<String> processStatus,
            @Param("exception") List<String> exception,
            Pageable pageable
    );

    @Query(value = "select case " +
            "           when LOWER(?8) = LOWER('lot_id') then lot_id " +
            "           when LOWER(?8) = LOWER('wafer_no') then wafer_no " +
            "           when LOWER(?8) = LOWER('test_stage') then test_stage " +
            "           when LOWER(?8) = LOWER('exception_message') then exception_message " +
            "           end as result " +
            "from dw_cleanup_task " +
            "      where (data_replay_record_id = ?1)  " +
            "        and (customer = ?2)  " +
            "        and (coalesce(?3,null) IS NULL OR lot_id in (?3)) " +
            "        and (coalesce(?4,null) IS NULL OR wafer_no in (?4)) " +
            "        and (coalesce(?5,null) IS NULL OR test_stage in (?5)) " +
            "        and (coalesce(?6,null) IS NULL OR exception_message in (?6)) " +
            "        and (coalesce(?7,null) IS NULL OR process_status in (?7)) " +
            "group by result " +
            "order by result",
            nativeQuery = true)
    List<String> findDynamicField(
            @Param("replayRecordId") Long replayRecordId,
            @Param("customer") String customer,
            @Param("lotId") List<String> lotId,
            @Param("waferNo") List<String> waferNo,
            @Param("testStage") List<String> testStage,
            @Param("exceptionMessage") List<String> exceptionMessage,
            @Param("status") List<String> status,
            @Param("filterField") String filterField
    );

}
