package com.guwave.datahub.bpms.dao.repository.bpms;

import com.guwave.datahub.bpms.dao.domain.repair.DataRepairEffectBatch;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 2025/3/5 14:50
 * DataRepairEffectBatchRepository
 *
 * <AUTHOR>
 */
@Repository("DataRepairEffectBatchRepository")
public interface DataRepairEffectBatchRepository extends CrudRepository<DataRepairEffectBatch, Long> {

    Long countAllByCustomerAndTestAreaAndFactoryAndFactorySiteAndDeviceIdAndTestStageAndLotIdAndWaferNoAndRepairStatusIn(
            String customer, String testArea, String factory, String factorySite, String deviceId, String testStage, String lotId, String waferNo, Collection<String> repairStatus
    );

    Optional<DataRepairEffectBatch> findFirstByCustomerAndTestAreaAndFactoryAndFactorySiteAndDeviceIdAndTestStageAndLotIdAndWaferNoOrderByRepairNoDesc(
            String customer, String testArea, String factory, String factorySite, String deviceId, String testStage, String lotId, String waferNo
    );

    List<DataRepairEffectBatch> findAllByCustomerAndTestAreaAndFactoryAndFactorySiteAndDeviceIdAndTestStageAndLotIdAndWaferNo(
            String customer, String testArea, String factory, String factorySite, String deviceId, String testStage, String lotId, String waferNo
    );

    List<DataRepairEffectBatch> findAllByRepairBatchIdAndDeleteFlag(Long repairBatchId, Boolean deleteFlag);

    DataRepairEffectBatch findRepairInfoIdsById(Long id);

    @Query(nativeQuery = true,
            value = "SELECT DISTINCT result " +
                    "FROM ( " +
                    "    SELECT DISTINCT CASE :filterField " +
                    "        WHEN 'subCustomer' THEN b.sub_customer " +
                    "        WHEN 'deviceId' THEN b.device_id " +
                    "        WHEN 'testStage' THEN b.test_stage " +
                    "        WHEN 'lotId' THEN b.lot_id " +
                    "    END AS result " +
                    "FROM bz_data_repair_effect_batch b " +
                    "WHERE b.delete_flag = 0 " +
                    "AND b.customer = :customer " +
                    "      AND (coalesce(:subCustomerList, null) IS NULL OR sub_customer IN (:subCustomerList)) " +
                    "      AND (coalesce(:deviceIdList, null) IS NULL OR device_id IN (:deviceIdList)) " +
                    "      AND (coalesce(:testStageList, null) IS NULL OR test_stage IN (:testStageList)) " +
                    "      AND (coalesce(:lotIdList, null) IS NULL OR lot_id IN (:lotIdList)) " +
                    ") a " +
                    "ORDER BY result")
    List<String> getBatchListDropdown(
            @Param("customer") String customer,
            @Param("filterField") String filterField,
            @Param("subCustomerList") List<String> subCustomerList,
            @Param("deviceIdList") List<String> deviceIdList,
            @Param("testStageList") List<String> testStageList,
            @Param("lotIdList") List<String> lotIdList);

    @Query(nativeQuery = true,
            value = "SELECT * " +
                    "FROM bz_data_repair_effect_batch b " +
                    "WHERE b.delete_flag = 0 " +
                    "  AND b.customer = :customer " +
                    "  AND (coalesce(:subCustomerList , null) IS NULL OR b.sub_customer IN (:subCustomerList)) " +
                    "  AND (coalesce(:deviceIdList, null) IS NULL OR b.device_id IN (:deviceIdList)) " +
                    "  AND (coalesce(:testStageList, null) IS NULL OR b.test_stage IN (:testStageList)) " +
                    "  AND (coalesce(:lotIdList, null) IS NULL OR b.lot_id IN (:lotIdList)) " +
                    "ORDER BY b.update_time DESC",
            countQuery = "SELECT COUNT(*) FROM bz_data_repair_effect_batch b " +
                    "WHERE b.delete_flag = 0 " +
                    "  AND b.customer = :customer " +
                    "  AND (coalesce(:subCustomerList , null) IS NULL OR b.sub_customer IN (:subCustomerList)) " +
                    "  AND (coalesce(:deviceIdList, null) IS NULL OR b.device_id IN (:deviceIdList)) " +
                    "  AND (coalesce(:testStageList, null) IS NULL OR b.test_stage IN (:testStageList)) " +
                    "  AND (coalesce(:lotIdList, null) IS NULL OR b.lot_id IN (:lotIdList))")
    Page<DataRepairEffectBatch> getBatchRecordList(
            @Param("customer") String customer,
            @Param("subCustomerList") List<String> subCustomerList,
            @Param("deviceIdList") List<String> deviceIdList,
            @Param("testStageList") List<String> testStageList,
            @Param("lotIdList") List<String> lotIdList,
            Pageable pageable);
}
