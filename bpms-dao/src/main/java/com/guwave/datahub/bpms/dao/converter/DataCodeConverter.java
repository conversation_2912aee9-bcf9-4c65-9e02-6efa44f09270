package com.guwave.datahub.bpms.dao.converter;

import com.guwave.datahub.bpms.common.constant.DataCode;
import com.guwave.datahub.bpms.common.constant.ReportModule;

import javax.persistence.AttributeConverter;

public class DataCodeConverter implements AttributeConverter<DataCode, String> {
    @Override
    public String convertToDatabaseColumn(DataCode code) {
        return null == code ? null : code.getCode();
    }

    @Override
    public DataCode convertToEntityAttribute(String code) {
        return DataCode.of(code);
    }
}
