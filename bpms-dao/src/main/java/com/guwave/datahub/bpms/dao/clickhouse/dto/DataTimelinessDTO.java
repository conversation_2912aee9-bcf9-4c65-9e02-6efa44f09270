package com.guwave.datahub.bpms.dao.clickhouse.dto;

import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import lombok.Data;

import java.util.Date;

@Data
public class DataTimelinessDTO {
    private String fileName;
    private String factory;
    private String testerName;
    private int step;
    private ProcessStatus ProcessStatus;
    private Date remoteFileMtime;
    private Date stepStartTime;
    private Date stepEndTime;
    private Date finishT;
    private Long originFileSize;
    private String hdfsPath;
    private Date updateTime;
}
