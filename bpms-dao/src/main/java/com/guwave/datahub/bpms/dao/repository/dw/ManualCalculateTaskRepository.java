package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.datahub.bpms.dao.projection.RankedManualCalculateTask;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualCalculateTask;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Repository("BpmsManualCalculateTaskRepository")
public interface ManualCalculateTaskRepository extends CrudRepository<ManualCalculateTask, Long> {

    List<ManualCalculateTask> findByProcessStatusOrderByIdDesc(ProcessStatus processStatus);

    @Transactional
    @Modifying
    @Query(value = "update dw_manual_calculate_task\n" +
            "set process_status = 'CREATE'\n" +
            "where id in (select *\n" +
            "             from (select id from dw_manual_calculate_task where process_status = 'PROCESSING' and dw_layer = 'ODS') x)", nativeQuery = true)
    void updateOdsProcessingToCreate();

    @Transactional
    @Modifying
    @Query(value = "update dw_manual_calculate_task\n" +
            "set process_status = 'FAIL'\n" +
            "where id in (select *\n" +
            "             from (select id from dw_manual_calculate_task where process_status = 'PROCESSING' and dw_layer = 'DWD') x)", nativeQuery = true)
    void updateDwdProcessingToFail();

    @Query(value = "WITH ranked_data AS (" +
            "  SELECT t.*, " +
            "  f.file_size, " +
            "  ROW_NUMBER() OVER (ORDER BY t.priority DESC, t.create_time) as rank_num " +
            "  FROM dw_manual_calculate_task t " +
            "  LEFT JOIN dw_manual_file_info f ON t.file_id = f.file_id " +
            "  WHERE t.process_status = 'CREATE' " +
            "  OR (t.process_status = 'PROCESSING' AND t.dw_layer = 'ODS')" +
            "  AND f.delete_flag = 0 " +
            ") " +
            "SELECT " +
            "rd.file_name fileName, " +
            "rd.file_owner fileOwner, " +
            "rd.file_size fileSize, " +
            "rd.id, " +
            "rd.rank_num rankNum, " +
            "rd.priority, " +
            "rd.create_time createTime, " +
            "rd.update_time updateTime " +
            "FROM ranked_data rd " +
            "WHERE (:fileName IS NULL OR rd.file_name LIKE CONCAT('%', :fileName, '%')) ",
            countQuery = "SELECT COUNT(*) FROM dw_manual_calculate_task t " +
                    "LEFT JOIN dw_manual_file_info f ON t.file_id = f.file_id " +
                    "WHERE t.process_status = 'CREATE' " +
                    "OR (t.process_status = 'PROCESSING' AND t.dw_layer = 'ODS')" +
                    "AND f.delete_flag = 0 " +
                    "AND (:fileName IS NULL OR t.file_name LIKE CONCAT('%', :fileName, '%')) ",
            nativeQuery = true)
    Page<RankedManualCalculateTask> findByFileName(
            @Param("fileName") String fileName,
            Pageable pageable
    );

    @Query("SELECT t FROM ManualCalculateTask t WHERE t.fileId IN " +
            "(SELECT DISTINCT m.fileId FROM ManualCalculateTask m WHERE m.id IN :ids)")
    List<ManualCalculateTask> findAllByFileIds(@Param("ids") List<Long> ids);

    @Transactional
    @Modifying
    @Query(value = "UPDATE dw_manual_calculate_task " +
            "SET priority = :priority, " +
            "    update_time = NOW(), " +
            "    update_user = :userName " +
            "WHERE id IN :ids ",
            nativeQuery = true)
    void updatePriorityByIds(
            @Param("ids") List<Long> ids,
            @Param("priority") Integer priority,
            @Param("userName") String userName
    );

    @Query(value = "SELECT id,\n" +
            "       message_id,\n" +
            "       manual_type,\n" +
            "       dw_layer,\n" +
            "       file_id,\n" +
            "       file_name,\n" +
            "       file_category,\n" +
            "       upload_type,\n" +
            "       need_read_file_ids,\n" +
            "       origin_file_modify_fields,\n" +
            "       need_delete_file_ids,\n" +
            "       file_owner,\n" +
            "       customer,\n" +
            "       sub_customer,\n" +
            "       test_area,\n" +
            "       factory,\n" +
            "       factory_site,\n" +
            "       device_id,\n" +
            "       lot_id,\n" +
            "       wafer_no,\n" +
            "       lot_type,\n" +
            "       test_stage,\n" +
            "       num_executors,\n" +
            "       executor_cores,\n" +
            "       executor_memory,\n" +
            "       driver_memory,\n" +
            "       parallelism,\n" +
            "       extra_conf,\n" +
            "       unique_id,\n" +
            "       priority,\n" +
            "       process_status,\n" +
            "       run_mode,\n" +
            "       version,\n" +
            "       create_time,\n" +
            "       update_time,\n" +
            "       create_user,\n" +
            "       update_user\n" +
            "FROM (SELECT *,\n" +
            "             ROW_NUMBER() OVER (PARTITION BY file_id ORDER BY update_time DESC) AS rn\n" +
            "      FROM dw_manual_calculate_task\n" +
            "      WHERE update_time >= :startTime\n" +
            "        AND update_time <= :endTime) AS ranked\n" +
            "WHERE rn = 1",
    nativeQuery = true)
    List<ManualCalculateTask> findAllByUpdateTimeBetween(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
