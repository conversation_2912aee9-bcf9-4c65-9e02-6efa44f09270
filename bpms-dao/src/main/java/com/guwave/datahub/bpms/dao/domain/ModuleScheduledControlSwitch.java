package com.guwave.datahub.bpms.dao.domain;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "bz_module_scheduled_control_switch")
public class ModuleScheduledControlSwitch implements Serializable {

    private static final long serialVersionUID = -9041989422377087027L;
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @Column(name = "module")
    private String module;

    @Column(name = "stop_flag")
    private Integer stopFlag;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "update_user")
    private String updateUser;

    public Long getId() {
        return id;
    }

    public ModuleScheduledControlSwitch setId(Long id) {
        this.id = id;
        return this;
    }

    public String getModule() {
        return module;
    }

    public ModuleScheduledControlSwitch setModule(String module) {
        this.module = module;
        return this;
    }

    public Integer getStopFlag() {
        return stopFlag;
    }

    public ModuleScheduledControlSwitch setStopFlag(Integer stopFlag) {
        this.stopFlag = stopFlag;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public ModuleScheduledControlSwitch setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public ModuleScheduledControlSwitch setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public ModuleScheduledControlSwitch setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public ModuleScheduledControlSwitch setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }
}
