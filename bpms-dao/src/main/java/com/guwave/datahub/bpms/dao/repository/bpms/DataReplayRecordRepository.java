package com.guwave.datahub.bpms.dao.repository.bpms;

import com.guwave.datahub.bpms.dao.domain.replay.DataReplayRecord;
import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.ReplayDetail;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Optional;


@Repository
public interface DataReplayRecordRepository extends CrudRepository<DataReplayRecord, Long> {

    Long countByCustomerAndProcessStatusIn(@Param("customer") String customer, @Param("processStatus") List<ProcessStatus> processStatus);

    @Query(value = "select case " +
            "           when LOWER(:filterField) = LOWER('test_area') then test_area " +
            "           when LOWER(:filterField) = LOWER('factory') then factory " +
            "           when LOWER(:filterField) = LOWER('device_id') then device_id " +
            "           when LOWER(:filterField) = LOWER('lot_id') then lot_id_list " +
            "           when LOWER(:filterField) = LOWER('wafer_no') then wafer_no_list " +
            "           when LOWER(:filterField) = LOWER('sblot_id') then sblot_id_list " +
            "           when LOWER(:filterField) = LOWER('lot_type') then lot_type " +
            "           when LOWER(:filterField) = LOWER('test_stage') then test_stage_list " +
            "           when LOWER(:filterField) = LOWER('test_program') then test_program_list " +
            "           when LOWER(:filterField) = LOWER('replay_type') then replay_type " +
            "           when LOWER(:filterField) = LOWER('step') then step " +
            "           when LOWER(:filterField) = LOWER('filter_process_status') then filter_process_status_list " +
            "           when LOWER(:filterField) = LOWER('exception_message') then filter_exception_message " +
            "           end as result " +
            "from bz_data_replay_record " +
            "      where (customer = :customer)  " +
            "        and (ifnull(:testArea,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :testArea AND test_area IS NULL) OR test_area = :testArea)) " +
            "        and (ifnull(:factory,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :factory AND factory IS NULL) OR json_overlaps(factory, :factory))) " +
            "        and (ifnull(:deviceId,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :deviceId AND device_id IS NULL) OR json_overlaps(device_id, :deviceId))) " +
            "        and (ifnull(:fileCategory,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :fileCategory AND file_category IS NULL) OR file_category = :fileCategory)) " +
            "        and (ifnull(:lotId,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :lotId AND lot_id_list IS NULL) OR json_overlaps(lot_id_list, :lotId))) " +
            "        and (ifnull(:waferNo,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :waferNo AND wafer_no_list IS NULL) OR json_overlaps(wafer_no_list, :waferNo))) " +
            "        and (ifnull(:sblotId,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :sblotId AND sblot_id_list IS NULL) OR json_overlaps(sblot_id_list, :sblotId))) " +
            "        and (ifnull(:lotType,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :lotType AND lot_type IS NULL) OR lot_type = :lotType)) " +
            "        and (ifnull(:testStage,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :testStage AND test_stage_list IS NULL) OR json_overlaps(test_stage_list, :testStage))) " +
            "        and (ifnull(:testProgram,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :testProgram AND test_program_list IS NULL) OR json_overlaps(test_program_list, :testProgram))) " +
            "        and (coalesce(:replayType,null) IS NULL OR (('(null)' in (:replayType) AND replay_type IS NULL) OR replay_type in (:replayType))) " +
            "        and (coalesce(:stepList,null) IS NULL OR (('(null)' in (:stepList) AND step IS NULL) OR step in (:stepList))) " +
            "        and (ifnull(:filterStatusList,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :filterStatusList AND filter_process_status_list IS NULL) OR json_overlaps(filter_process_status_list, :filterStatusList))) " +
            "        and (coalesce(:exceptionMessageList,null) IS NULL OR (('(null)' in (:exceptionMessageList) AND filter_exception_message IS NULL) OR filter_exception_message in (:exceptionMessageList))) " +
            "        and (ifnull(:filterStartCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :filterStartCreateTime AND start_create_time IS NULL) OR start_create_time >= :filterStartCreateTime)) " +
            "        and (ifnull(:filterEndCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :filterEndCreateTime AND end_create_time IS NULL) OR end_create_time <= :filterEndCreateTime)) " +
            "        and (ifnull(:filterStartUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :filterStartUpdateTime AND start_update_time IS NULL) OR start_update_time >= :filterStartUpdateTime)) " +
            "        and (ifnull(:filterEndUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :filterEndUpdateTime AND end_update_time IS NULL) OR end_update_time <= :filterEndUpdateTime)) " +
            "        and (ifnull(:startCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :startCreateTime AND create_time IS NULL) OR create_time >= :startCreateTime)) " +
            "        and (ifnull(:endCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :endCreateTime AND create_time IS NULL) OR create_time <= :endCreateTime)) " +
            "        and (ifnull(:startUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :startUpdateTime AND update_time IS NULL) OR update_time >= :startUpdateTime)) " +
            "        and (ifnull(:endUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :endUpdateTime AND update_time IS NULL) OR update_time <= :endUpdateTime)) " +
            "        and (coalesce(:status,null) IS NULL OR process_status in (:status)) " +
            "group by result " +
            "order by result",
            nativeQuery = true)
    List<String> findDynamicField(
            @Param("filterField") String filterField,
            @Param("customer") String customer,
            @Param("testArea") String testArea,
            @Param("factory") String factoryArrayJson,
            @Param("deviceId") String deviceIdArrayJson,
            @Param("fileCategory") String fileCategory,
            @Param("lotId") String lotIdArrayJson,
            @Param("waferNo") String waferNoArrayJson,
            @Param("sblotId") String sblotIdArrayJson,
            @Param("lotType") String lotType,
            @Param("testStage") String testStageArrayJson,
            @Param("testProgram") String testProgramArrayJson,
            @Param("replayType") List<String> replayType,
            @Param("stepList") List<Integer> stepList,
            @Param("filterStatusList") String filterStatusArrayJson,
            @Param("exceptionMessageList") List<String> exceptionMessageList,
            @Param("filterStartCreateTime") String filterStartCreateTime,
            @Param("filterEndCreateTime") String filterEndCreateTime,
            @Param("filterStartUpdateTime") String filterStartUpdateTime,
            @Param("filterEndUpdateTime") String filterEndUpdateTime,
            @Param("startCreateTime") String startCreateTime,
            @Param("endCreateTime") String endCreateTime,
            @Param("startUpdateTime") String startUpdateTime,
            @Param("endUpdateTime") String endUpdateTime,
            @Param("status") List<String> status
    );

    @Query(value = "select * from bz_data_replay_record " +
            "      where (customer = :customer)  " +
            "        and (ifnull(:testArea,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :testArea AND test_area IS NULL) OR test_area = :testArea)) " +
            "        and (ifnull(:factory,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :factory AND factory IS NULL) OR json_overlaps(factory, :factory))) " +
            "        and (ifnull(:deviceId,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :deviceId AND device_id IS NULL) OR json_overlaps(device_id, :deviceId))) " +
            "        and (ifnull(:fileCategory,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :fileCategory AND file_category IS NULL) OR file_category = :fileCategory)) " +
            "        and (ifnull(:lotId,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :lotId AND lot_id_list IS NULL) OR json_overlaps(lot_id_list, :lotId))) " +
            "        and (ifnull(:waferNo,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :waferNo AND wafer_no_list IS NULL) OR json_overlaps(wafer_no_list, :waferNo))) " +
            "        and (ifnull(:sblotId,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :sblotId AND sblot_id_list IS NULL) OR json_overlaps(sblot_id_list, :sblotId))) " +
            "        and (ifnull(:lotType,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :lotType AND lot_type IS NULL) OR lot_type = :lotType)) " +
            "        and (ifnull(:testStage,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :testStage AND test_stage_list IS NULL) OR json_overlaps(test_stage_list, :testStage))) " +
            "        and (ifnull(:testProgram,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :testProgram AND test_program_list IS NULL) OR json_overlaps(test_program_list, :testProgram))) " +
            "        and (coalesce(:replayType,null) IS NULL OR (('(null)' in (:replayType) AND replay_type IS NULL) OR replay_type in (:replayType))) " +
            "        and (coalesce(:stepList,null) IS NULL OR (('(null)' in (:stepList) AND step IS NULL) OR step in (:stepList))) " +
            "        and (ifnull(:filterStatusList,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :filterStatusList AND filter_process_status_list IS NULL) OR json_overlaps(filter_process_status_list, :filterStatusList))) " +
            "        and (coalesce(:exceptionMessageList,null) IS NULL OR (('(null)' in (:exceptionMessageList) AND filter_exception_message IS NULL) OR filter_exception_message in (:exceptionMessageList))) " +
            "        and (ifnull(:filterStartCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :filterStartCreateTime AND start_create_time IS NULL) OR start_create_time >= :filterStartCreateTime)) " +
            "        and (ifnull(:filterEndCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :filterEndCreateTime AND end_create_time IS NULL) OR end_create_time <= :filterEndCreateTime)) " +
            "        and (ifnull(:filterStartUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :filterStartUpdateTime AND start_update_time IS NULL) OR start_update_time >= :filterStartUpdateTime)) " +
            "        and (ifnull(:filterEndUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :filterEndUpdateTime AND end_update_time IS NULL) OR end_update_time <= :filterEndUpdateTime)) " +
            "        and (ifnull(:startCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :startCreateTime AND create_time IS NULL) OR create_time >= :startCreateTime)) " +
            "        and (ifnull(:endCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :endCreateTime AND create_time IS NULL) OR create_time <= :endCreateTime)) " +
            "        and (ifnull(:startUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :startUpdateTime AND update_time IS NULL) OR update_time >= :startUpdateTime)) " +
            "        and (ifnull(:endUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :endUpdateTime AND update_time IS NULL) OR update_time <= :endUpdateTime)) " +
            "        and (coalesce(:status,null) IS NULL OR process_status in (:status)) " +
            "        and (ifnull(:fileName,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :fileName AND file_name IS NULL) OR file_name REGEXP :fileName)) " +
            "ORDER BY " +
            "    CASE " +
            "        WHEN process_status = 'PROCESSING' THEN 1 " +
            "        WHEN process_status = 'CREATE' THEN 2 " +
            "        WHEN process_status IN ('FAIL', 'SUCCESS', 'CANCEL') THEN 3 " +
            "        ELSE 4 " +
            "    END, " +
            "    topped_time DESC, " +
            "    CASE " +
            "        WHEN process_status = 'CREATE' THEN create_time " +
            "        END, " +
            "    CASE " +
            "        WHEN process_status IN ('FAIL', 'SUCCESS', 'CANCEL') THEN update_time " +
            "        END DESC",
            countQuery = "select count(1) from bz_data_replay_record" +
                    "      where (customer = :customer)  " +
                    "        and (ifnull(:testArea,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :testArea AND test_area IS NULL) OR test_area = :testArea)) " +
                    "        and (ifnull(:factory,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :factory AND factory IS NULL) OR json_overlaps(factory, :factory))) " +
                    "        and (ifnull(:deviceId,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :deviceId AND device_id IS NULL) OR json_overlaps(device_id, :deviceId))) " +
                    "        and (ifnull(:fileCategory,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :fileCategory AND file_category IS NULL) OR file_category = :fileCategory)) " +
                    "        and (ifnull(:lotId,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :lotId AND lot_id_list IS NULL) OR json_overlaps(lot_id_list, :lotId))) " +
                    "        and (ifnull(:waferNo,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :waferNo AND wafer_no_list IS NULL) OR json_overlaps(wafer_no_list, :waferNo))) " +
                    "        and (ifnull(:sblotId,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :sblotId AND sblot_id_list IS NULL) OR json_overlaps(sblot_id_list, :sblotId))) " +
                    "        and (ifnull(:lotType,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :lotType AND lot_type IS NULL) OR lot_type = :lotType)) " +
                    "        and (ifnull(:testStage,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :testStage AND test_stage_list IS NULL) OR json_overlaps(test_stage_list, :testStage))) " +
                    "        and (ifnull(:testProgram,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :testProgram AND test_program_list IS NULL) OR json_overlaps(test_program_list, :testProgram))) " +
                    "        and (coalesce(:replayType,null) IS NULL OR (('(null)' in (:replayType) AND replay_type IS NULL) OR replay_type in (:replayType))) " +
                    "        and (coalesce(:stepList,null) IS NULL OR (('(null)' in (:stepList) AND step IS NULL) OR step in (:stepList))) " +
                    "        and (ifnull(:filterStatusList,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :filterStatusList AND filter_process_status_list IS NULL) OR json_overlaps(filter_process_status_list, :filterStatusList))) " +
                    "        and (coalesce(:exceptionMessageList,null) IS NULL OR (('(null)' in (:exceptionMessageList) AND filter_exception_message IS NULL) OR filter_exception_message in (:exceptionMessageList))) " +
                    "        and (ifnull(:filterStartCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :filterStartCreateTime AND start_create_time IS NULL) OR start_create_time >= :filterStartCreateTime)) " +
                    "        and (ifnull(:filterEndCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :filterEndCreateTime AND end_create_time IS NULL) OR end_create_time <= :filterEndCreateTime)) " +
                    "        and (ifnull(:filterStartUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :filterStartUpdateTime AND start_update_time IS NULL) OR start_update_time >= :filterStartUpdateTime)) " +
                    "        and (ifnull(:filterEndUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :filterEndUpdateTime AND end_update_time IS NULL) OR end_update_time <= :filterEndUpdateTime)) " +
                    "        and (ifnull(:startCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :startCreateTime AND create_time IS NULL) OR create_time >= :startCreateTime)) " +
                    "        and (ifnull(:endCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :endCreateTime AND create_time IS NULL) OR create_time <= :endCreateTime)) " +
                    "        and (ifnull(:startUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :startUpdateTime AND update_time IS NULL) OR update_time >= :startUpdateTime)) " +
                    "        and (ifnull(:endUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :endUpdateTime AND update_time IS NULL) OR update_time <= :endUpdateTime)) " +
                    "        and (coalesce(:status,null) IS NULL OR process_status in (:status)) " +
                    "        and (ifnull(:fileName,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :fileName AND file_name IS NULL) OR file_name REGEXP :fileName)) ",
            nativeQuery = true)
    Page<DataReplayRecord> findDataReplayRecord(
            @Param("customer") String customer,
            @Param("testArea") String testArea,
            @Param("factory") String factoryArrayJson,
            @Param("deviceId") String deviceIdArrayJson,
            @Param("fileCategory") String fileCategory,
            @Param("lotId") String lotIdArrayJson,
            @Param("waferNo") String waferNoArrayJson,
            @Param("sblotId") String sblotIdArrayJson,
            @Param("lotType") String lotType,
            @Param("testStage") String testStageArrayJson,
            @Param("testProgram") String testProgramArrayJson,
            @Param("replayType") List<String> replayType,
            @Param("stepList") List<Integer> stepList,
            @Param("filterStatusList") String filterStatusArrayJson,
            @Param("exceptionMessageList") List<String> exceptionMessageList,
            @Param("filterStartCreateTime") String filterStartCreateTime,
            @Param("filterEndCreateTime") String filterEndCreateTime,
            @Param("filterStartUpdateTime") String filterStartUpdateTime,
            @Param("filterEndUpdateTime") String filterEndUpdateTime,
            @Param("startCreateTime") String startCreateTime,
            @Param("endCreateTime") String endCreateTime,
            @Param("startUpdateTime") String startUpdateTime,
            @Param("endUpdateTime") String endUpdateTime,
            @Param("status") List<String> status,
            @Param("fileName") String fileName,
            Pageable pageable
    );

    List<DataReplayRecord> findAllByCustomerAndProcessStatusAndUpdateTimeBefore(String customer, ProcessStatus processStatus, Date updateTime);

    Optional<DataReplayRecord> findByIdAndCustomer(Long id, String customer);

    @Transactional
    @Modifying
    @Query("update DataReplayRecord a set a.updateTime = now(), a.processStatus = :processStatus,a.totalTaskCnt = :totalTaskCnt,a.successTaskCnt = :successTaskCnt,a.failTaskCnt = :failTaskCnt where a.id= :id")
    void updateProcessStatusById(@Param("id") Long id, @Param("processStatus") ProcessStatus processStatus, @Param("totalTaskCnt") Integer totalTaskCnt,
                                 @Param("successTaskCnt") Integer successTaskCnt, @Param("failTaskCnt") Integer failTaskCnt);

    @Transactional
    @Modifying
    @Query("update DataReplayRecord a set a.updateTime = now(), a.processStatus = :processStatus where a.id= :id and a.processStatus = :preProcessStatus")
    void updateProcessStatusByIdAndProcessStatus(@Param("id") Long id,
                                                 @Param("preProcessStatus") ProcessStatus preProcessStatus,
                                                 @Param("processStatus") ProcessStatus processStatus);
    @Transactional
    @Modifying
    @Query("UPDATE DataReplayRecord r SET r.toppedTime = CURRENT_TIMESTAMP " +
            "WHERE r.id = :id AND r.processStatus = :status AND r.customer = :customer")
    int updateToppedTimeByIdAndProcessStatus(
            @Param("id") Long id,
            @Param("status") ProcessStatus status,
            @Param("customer") String customer
    );

    boolean existsByIdAndCustomer(@Param("id") Long id, @Param("customer") String customer);

    List<DataReplayRecord> findAllByProcessStatusInOrderByToppedTimeDescCreateTimeAsc(Collection<ProcessStatus> processStatus);

    List<DataReplayRecord> findAllByCreateTimeBetween(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Query(value = "SELECT\n" +
            "    DATE_FORMAT(create_time, '%Y%m%d') AS day,\n" +
            "    COUNT(*) AS totalTaskCnt,\n" +
            "    SUM(process_status = 'SUCCESS') AS successTaskCnt,\n" +
            "    SUM(COALESCE(replay_file_cnt, 0)) as totalFileCnt\n" +
            "FROM bz_data_replay_record\n" +
            "WHERE create_time >= :startTime\n" +
            "  AND create_time <= :endTime\n" +
            "GROUP BY day\n" +
            "ORDER BY day;",
    nativeQuery = true)
    List<Object[]> findReplayDetailByDay(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
