package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.dao.mysql.domain.dw.EcidRule;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("BpmsEcidRuleRepository")
public interface EcidRuleRepository extends CrudRepository<EcidRule, Long> {
    List<EcidRule> findByCustomerAndDeviceIdAndTestStage(String customer, String deviceId, String testStage);
}
