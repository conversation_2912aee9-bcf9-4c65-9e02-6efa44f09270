package com.guwave.datahub.bpms.dao.domain.schedule;

import com.guwave.datahub.bpms.common.constant.ExecuteEngine;
import com.guwave.datahub.bpms.dao.converter.ExecuteEngineConverter;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 定时任务配置表实体
 */
@Data
@Entity
@Table(name = "bz_schedule_task_definition")
public class ScheduleTaskDefinition {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "task_name", nullable = false, unique = true)
    private String taskName;

    @Column(name = "task_description")
    private String taskDescription;

    @Column(name = "execute_engine")
    @Convert(converter = ExecuteEngineConverter.class)
    private ExecuteEngine executeEngine;

    @Column(name = "cron_expression", nullable = false)
    private String cronExpression;

    @Lob
    @Column(name = "command")
    private String command;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Column(name = "delete_flag", nullable = false)
    private Boolean deleteFlag = false;

    @Column(name = "create_time", updatable = false)
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "create_user", updatable = false)
    private String createUser = "System";

    @Column(name = "update_user")
    private String updateUser = "System";

    public Long getId() {
        return id;
    }

    public ScheduleTaskDefinition setId(Long id) {
        this.id = id;
        return this;
    }

    public String getTaskName() {
        return taskName;
    }

    public ScheduleTaskDefinition setTaskName(String taskName) {
        this.taskName = taskName;
        return this;
    }

    public String getTaskDescription() {
        return taskDescription;
    }

    public ScheduleTaskDefinition setTaskDescription(String taskDescription) {
        this.taskDescription = taskDescription;
        return this;
    }

    public ExecuteEngine getExecuteEngine() {
        return executeEngine;
    }

    public ScheduleTaskDefinition setExecuteEngine(ExecuteEngine executeEngine) {
        this.executeEngine = executeEngine;
        return this;
    }

    public String getCronExpression() {
        return cronExpression;
    }

    public ScheduleTaskDefinition setCronExpression(String cronExpression) {
        this.cronExpression = cronExpression;
        return this;
    }

    public String getCommand() {
        return command;
    }

    public ScheduleTaskDefinition setCommand(String command) {
        this.command = command;
        return this;
    }

    public Boolean getActive() {
        return isActive;
    }

    public ScheduleTaskDefinition setActive(Boolean active) {
        isActive = active;
        return this;
    }

    public Boolean getDeleteFlag() {
        return deleteFlag;
    }

    public ScheduleTaskDefinition setDeleteFlag(Boolean deleteFlag) {
        this.deleteFlag = deleteFlag;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public ScheduleTaskDefinition setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public ScheduleTaskDefinition setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public ScheduleTaskDefinition setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public ScheduleTaskDefinition setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }
}