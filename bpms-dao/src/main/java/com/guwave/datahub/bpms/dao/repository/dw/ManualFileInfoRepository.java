package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.ManualTaskDetail;
import com.guwave.onedata.dataware.common.contant.UploadType;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualFileInfo;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;


@Repository("BpmsManualFileInfoRepository")
public interface ManualFileInfoRepository extends CrudRepository<ManualFileInfo, Long> {

    @Query(value = "select *\n" +
            "from dw_manual_file_info\n" +
            "where max_record_test_item_cnt > 1000000\n" +
            "  and create_time >= :startTime\n" +
            "  AND create_time <= :endTime",
            nativeQuery = true)
    List<ManualFileInfo> findAbnormalFileByCreateTime(@Param("startTime") String startTime, @Param("endTime") String endTime);

}
