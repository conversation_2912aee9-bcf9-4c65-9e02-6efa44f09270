package com.guwave.datahub.bpms.dao.clickhouse.dto;

import com.guwave.onedata.dataware.common.contant.TestArea;
import lombok.Data;

/**
 * AccuracyLotWaferFileDetailDTO
 *
 * <AUTHOR>
 */
@Data
public class AccuracyLotWaferFileDetailDTO {
    private String deviceName;
    private String testArea;
    private String testStage;
    private String testProgram;
    private String lotId;
    private String waferId;
    private String waferNo;
    private String sbLotId;

    private String flowId;
    private String retestHardBin;
    private String fileName;
    private String fileId;
    private String stdStartTime;
    private String endTime;
    private Long inputCount;
    private Long passCount;
    private Long failCount;
    private Boolean supportDataRepair;
    private String fileFlag;
    private Integer onlineRetest;

    public String getQueryKey() {
        StringBuffer buffer = new StringBuffer();
        buffer.append(deviceName).append("-");
        buffer.append(testArea).append("-");
        buffer.append(testStage).append("-");
        buffer.append(testProgram).append("-");
        buffer.append(lotId).append("-");
        if (TestArea.getCPList().contains(TestArea.of(testArea))) {
            buffer.append(waferId).append("-");
        } else {
            buffer.append(sbLotId).append("-");
        }
        return buffer.toString();
    }

}
