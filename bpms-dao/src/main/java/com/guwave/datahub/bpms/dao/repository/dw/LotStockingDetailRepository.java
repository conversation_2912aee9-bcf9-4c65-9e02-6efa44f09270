package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotStockingDetail;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository("BpmsLotStockingDetailRepository")
public interface LotStockingDetailRepository extends CrudRepository<LotStockingDetail, Long> {

    @Transactional
    @Modifying
    @Query(value = "delete\n" +
            "from dw_lot_stocking_detail\n" +
            "where id in (select * from (select id from dw_lot_stocking_detail where is_finish = 0) x)", nativeQuery = true)
    void deleteNotCalFinish();

    List<LotStockingDetail> findAllByCustomerAndTestAreaAndFactoryAndFactorySiteAndDeviceIdAndLotTypeAndTestStageAndLotIdAndWaferNoAndFileCategory(String customer, TestArea testArea, String factory, String factorySite, String deviceId, LotType lotType, String testStage, String lotId, String waferNo, FileCategory fileCategory);

    @Modifying
    @Transactional
    @Query("update LotStockingDetail a set a.updateTime = now() where a.id in :ids")
    void updateUpdateTime(@Param("ids") List<Long> ids);

    @Query(value = "select *\n" +
            "from dw_lot_stocking_detail\n" +
            "where max_record_test_item_cnt > 1000000\n" +
            "  and create_time >= :startTime\n" +
            "  AND create_time <= :endTime",
    nativeQuery = true)
    List<LotStockingDetail> findAbnormalFileByCreateTime(@Param("startTime") String startTime, @Param("endTime") String endTime);

}
