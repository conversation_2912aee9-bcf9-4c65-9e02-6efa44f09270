package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.MesParseLog;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository("BpmsMesParseLogRepository")
public interface MesParseLogRepository extends CrudRepository<MesParseLog, Long> {
    List<MesParseLog> findByProcessStatusOrderByIdDesc(ProcessStatus processStatus);

    @Transactional
    @Modifying
    @Query(value = "update dw_mes_parse_log\n" +
            "set process_status = 'CREATE'\n" +
            "where id in (select * from (select id from dw_mes_parse_log where process_status = 'PROCESSING') x)", nativeQuery = true)
    void updateProcessingToCreate();
}
