package com.guwave.datahub.bpms.dao.repository.linkx;

import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.linkx.common.constant.OptType;
import com.guwave.onedata.linkx.dao.mysql.domain.bz.BzAppInstanceRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/10
 * @description BzYmsWarehousingTaskRecordRepository
 */

public interface BzAppInstanceRecordRepository extends JpaRepository<BzAppInstanceRecord, Long> {

    @Query(value = "select case" +
            "              when LOWER(?1) = LOWER('test_area') then test_area " +
            "              when LOWER(?1) = LOWER('factory') then factory " +
            "              when LOWER(?1) = LOWER('device_id') then device_id " +
            "              when LOWER(?1) = LOWER('test_stage') then test_stage " +
            "              when LOWER(?1) = LOWER('lot_id') then lot_id " +
            "              when LOWER(?1) = LOWER('wafer_no') then wafer_no " +
            "              when LOWER(?1) = LOWER('lot_type') then lot_type " +
            "              end as result " +
            "   from bz_app_instance_record " +
            "         where (coalesce(?2,null) IS NULL OR test_area in (?2))  " +
            "           and (coalesce(?3,null) IS NULL OR factory in (?3)) " +
            "           and (coalesce(?4,null) IS NULL OR device_id in (?4)) " +
            "           and (coalesce(?5,null) IS NULL OR test_stage in (?5)) " +
            "           and (coalesce(?6,null) IS NULL OR lot_id in (?6)) " +
            "           and (coalesce(?7,null) IS NULL OR wafer_no in (?7)) " +
            "           and (coalesce(?8,null) IS NULL OR lot_type in (?8)) " +
            "           and (coalesce(?9,null) IS NULL OR process_status in (?9)) " +
            "           and (coalesce(?10,null) IS NULL OR opt_type in (?10)) " +
            "           and (coalesce(?11,null) IS NULL OR execute_engine in (?11)) " +
            "           and customer=?12 " +
            "           and opt_type in ('UPDATE_PRODUCT', 'UPDATE_WAFERMAP_CONFIG','UPDATE_MES_BATCH_INFO','NEW_MES_DATA','NEW_WAT_DATA','NEW_TEST_ITEM_DATA','NEW_BITMEM_TEST_ITEM_DATA') " +
            "   group by result " +
            "   order by result ",
            nativeQuery = true)
    List<String> findDynamicField(
            @Param("filterField") String filterField,
            @Param("testArea") List<String> testArea,
            @Param("factory") List<String> factory,
            @Param("deviceId") List<String> deviceId,
            @Param("testStage") List<String> testStage,
            @Param("lotId") List<String> lotId,
            @Param("waferNo") List<String> waferNo,
            @Param("lotType") List<String> lotType,
            @Param("processStatus") List<String> processStatus,
            @Param("optType") List<String> optType,
            @Param("executeEngine") List<String> executeEngine,
            @Param("customer") String customer
    );

    @Query(value = "select * " +
            "      from bz_app_instance_record " +
            "      where (coalesce(?1, null) is null or test_area in (?1)) " +
            "             and (coalesce(?2, null) is null or factory in (?2)) " +
            "             and (coalesce(?3, null) is null or device_id in (?3)) " +
            "             and (coalesce(?4, null) is null or test_stage in (?4)) " +
            "             and (coalesce(?5, null) is null or lot_id in (?5)) " +
            "             and (coalesce(?6, null) is null or wafer_no in (?6)) " +
            "             and (coalesce(?7, null) is null or lot_type in (?7)) " +
            "             and (coalesce(?8, null) is null or process_status in (?8)) " +
            "             and (coalesce(?9, null) is null or opt_type in (?9)) " +
            "             and (coalesce(?10, null) is null or execute_engine in (?10)) " +
            "             and customer=?11 " +
            "             and opt_type in ('UPDATE_PRODUCT', 'UPDATE_WAFERMAP_CONFIG','UPDATE_MES_BATCH_INFO','NEW_MES_DATA','NEW_WAT_DATA','NEW_TEST_ITEM_DATA','NEW_BITMEM_TEST_ITEM_DATA') " +
            "      order by field(process_status, 'FAIL', 'CREATE', 'PROCESSING', 'SUCCESS'), update_time desc ",
            nativeQuery = true)
    Page<BzAppInstanceRecord> findYmsTasksByFilters(
            @Param("testArea") List<String> testArea,
            @Param("factory") List<String> factory,
            @Param("deviceId") List<String> deviceId,
            @Param("testStage") List<String> testStage,
            @Param("lotId") List<String> lotId,
            @Param("waferNo") List<String> waferNo,
            @Param("lotType") List<String> lotType,
            @Param("processStatus") List<String> processStatus,
            @Param("optType") List<String> optType,
            @Param("executeEngine") List<String> executeEngine,
            @Param("customer") String customer,
            Pageable pageable
    );
}
