package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotRelationSync;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("BpmsLotRelationSyncRepository")
public interface LotRelationSyncRepository extends CrudRepository<LotRelationSync, Long> {
    List<LotRelationSync> findAllByCustomerAndFtDeviceIdAndFtLotId(String customer, String ftDeviceId, String ftLotId);
}
