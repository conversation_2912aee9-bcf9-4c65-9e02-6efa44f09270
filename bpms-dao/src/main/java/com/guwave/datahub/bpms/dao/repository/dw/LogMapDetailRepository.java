package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LogMapDetail;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Repository("BpmsLogMapDetailRepository")
public interface LogMapDetailRepository extends CrudRepository<LogMapDetail, Long> {
    List<LogMapDetail> findByProcessStatusOrderByIdDesc(ProcessStatus processStatus);

    @Transactional
    @Modifying
    @Query(value = "update dw_log_map_detail\n" +
            "set process_status = 'CREATE'\n" +
            "where id in (select * from (select id from dw_log_map_detail where process_status = 'PROCESSING') x)", nativeQuery = true)
    void updateMergeProcessingToCreate();
}
