package com.guwave.datahub.bpms.dao.converter;

import com.guwave.datahub.bpms.common.constant.ReportModule;
import com.guwave.onedata.dataware.common.contant.BatchInfoStatus;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class ReportModuleConverter implements AttributeConverter<ReportModule, String> {

    @Override
    public String convertToDatabaseColumn(ReportModule module) {
        return null == module ? null : module.getModule();
    }

    @Override
    public ReportModule convertToEntityAttribute(String module) {
        return ReportModule.of(module);
    }
}
