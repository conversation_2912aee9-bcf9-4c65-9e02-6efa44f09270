package com.guwave.datahub.bpms.dao.repository.bpms;

import com.guwave.datahub.bpms.common.constant.ExecuteEngine;
import com.guwave.datahub.bpms.dao.domain.schedule.ScheduleTaskDefinition;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * TaskDefinition Repository
 */
@Repository
public interface ScheduleTaskDefinitionRepository extends JpaRepository<ScheduleTaskDefinition, Long> {
    List<ScheduleTaskDefinition> findAllByIsActiveAndDeleteFlag(Boolean isActive, Boolean deleteFlag);
    List<ScheduleTaskDefinition> findAllByDeleteFlagAndExecuteEngine(Boolean deleteFlag, ExecuteEngine executeEngine);
}