package com.guwave.datahub.bpms.dao.domain.processMessage;

import com.guwave.onedata.dataware.dao.mysql.converter.ProjectEnumConverter;
import com.guwave.onedata.dataware.common.contant.ModuleEnum;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.contant.ProjectEnum;
import com.guwave.onedata.dataware.dao.mysql.converter.ModuleEnumConverter;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.DatawareFailMessageRecord;
import com.guwave.onedata.linkx.dao.mysql.converter.ProcessStatusConverter;
import com.guwave.onedata.linkx.dao.mysql.domain.bz.BzLinkxFailMessageRecord;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputeFailMessageRecord;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

import static com.guwave.onedata.dataware.common.contant.Constant.SYSTEM;
import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "bz_kafka_fail_message_record")
public class KafkaFailMessageRecord implements Serializable {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    /**
     * 发送该消息的项目
     */
    @Column(name = "project")
    @Convert(converter = ProjectEnumConverter.class)
    private ProjectEnum project;

    /**
     * 发送该消息的模块
     */
    @Column(name = "module")
    @Convert(converter = ModuleEnumConverter.class)
    private ModuleEnum module;

    /**
     * 该条消息的topic
     */
    @Column(name = "topic")
    private String topic;

    /**
     * 该条消息的key
     */
    @Column(name = "\"key\"", columnDefinition = "LONGTEXT")
    private String key;

    /**
     * 消息体
     */
    @Column(name = "value", columnDefinition = "LONGTEXT")
    private String value;

    /**
     * 消息处理状态
     */
    @Column(name = "process_status")
    @Convert(converter = ProcessStatusConverter.class)
    private ProcessStatus processStatus;

    /**
     * 发送失败次数
     */
    @Column(name = "fail_cnt")
    private Integer failCnt;

    /**
     * 删除标记：0->有效,1->删除
     */
    @Column(name = "delete_flag")
    private Integer deleteFlag;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "update_user")
    private String updateUser;

    public Long getId() {
        return id;
    }

    public KafkaFailMessageRecord setId(Long id) {
        this.id = id;
        return this;
    }

    public ProjectEnum getProject() {
        return project;
    }

    public KafkaFailMessageRecord setProject(ProjectEnum project) {
        this.project = project;
        return this;
    }

    public ModuleEnum getModule() {
        return module;
    }

    public KafkaFailMessageRecord setModule(ModuleEnum module) {
        this.module = module;
        return this;
    }

    public String getTopic() {
        return topic;
    }

    public KafkaFailMessageRecord setTopic(String topic) {
        this.topic = topic;
        return this;
    }

    public String getKey() {
        return key;
    }

    public KafkaFailMessageRecord setKey(String key) {
        this.key = key;
        return this;
    }

    public String getValue() {
        return value;
    }

    public KafkaFailMessageRecord setValue(String value) {
        this.value = value;
        return this;
    }

    public ProcessStatus getProcessStatus() {
        return processStatus;
    }

    public KafkaFailMessageRecord setProcessStatus(ProcessStatus processStatus) {
        this.processStatus = processStatus;
        return this;
    }

    public Integer getFailCnt() {
        return failCnt;
    }

    public KafkaFailMessageRecord setFailCnt(Integer failCnt) {
        this.failCnt = failCnt;
        return this;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public KafkaFailMessageRecord setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public KafkaFailMessageRecord setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public KafkaFailMessageRecord setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public KafkaFailMessageRecord setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public KafkaFailMessageRecord setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }

    public static KafkaFailMessageRecord of(DatawareFailMessageRecord datawareFailMessageRecord) {
        return new KafkaFailMessageRecord()
                .setProject(datawareFailMessageRecord.getProject())
                .setModule(datawareFailMessageRecord.getModule())
                .setTopic(datawareFailMessageRecord.getTopic())
                .setKey(datawareFailMessageRecord.getKey())
                .setValue(datawareFailMessageRecord.getValue())
                .setProcessStatus(ProcessStatus.FAIL)
                .setFailCnt(1)
                .setDeleteFlag(0)
                .setCreateUser(SYSTEM)
                .setUpdateUser(SYSTEM)
                .setCreateTime(new Date())
                .setUpdateTime(new Date());
    }

    public static KafkaFailMessageRecord of(BzLinkxFailMessageRecord linkxFailMessageRecord) {
        return new KafkaFailMessageRecord()
                .setProject(linkxFailMessageRecord.getProject())
                .setModule(linkxFailMessageRecord.getModule())
                .setTopic(linkxFailMessageRecord.getTopic())
                .setKey(linkxFailMessageRecord.getKey())
                .setValue(linkxFailMessageRecord.getValue())
                .setProcessStatus(ProcessStatus.FAIL)
                .setFailCnt(1)
                .setDeleteFlag(0)
                .setCreateUser(SYSTEM)
                .setUpdateUser(SYSTEM)
                .setCreateTime(new Date())
                .setUpdateTime(new Date());
    }

    public static KafkaFailMessageRecord of(ComputeFailMessageRecord computeFailMessageRecord) {
        return new KafkaFailMessageRecord()
                .setProject(ProjectEnum.of(computeFailMessageRecord.getProject().name()))
                .setModule(ModuleEnum.of(computeFailMessageRecord.getModule().name()))
                .setTopic(computeFailMessageRecord.getTopic())
                .setKey(computeFailMessageRecord.getKey())
                .setValue(computeFailMessageRecord.getValue())
                .setProcessStatus(ProcessStatus.FAIL)
                .setFailCnt(1)
                .setDeleteFlag(0)
                .setCreateUser(SYSTEM)
                .setUpdateUser(SYSTEM)
                .setCreateTime(new Date())
                .setUpdateTime(new Date());
    }
}
