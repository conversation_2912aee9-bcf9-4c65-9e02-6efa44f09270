package com.guwave.datahub.bpms.dao.vo.bpms.dailyReport;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
public class RepairDetail {
    private String day;
    private Long totalCnt;
    private Long successCnt;

    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public Long getSuccessCnt() {
        return successCnt;
    }

    public void setSuccessCnt(Long successCnt) {
        this.successCnt = successCnt;
    }

    public Long getTotalCnt() {
        return totalCnt;
    }

    public void setTotalCnt(Long totalCnt) {
        this.totalCnt = totalCnt;
    }
}
