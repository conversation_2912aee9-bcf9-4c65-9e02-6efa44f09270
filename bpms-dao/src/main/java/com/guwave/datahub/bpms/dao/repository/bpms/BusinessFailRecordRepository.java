package com.guwave.datahub.bpms.dao.repository.bpms;

import com.guwave.datahub.bpms.dao.domain.retryFailRecord.BusinessFailRecord;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;


@Repository
public interface BusinessFailRecordRepository extends CrudRepository<BusinessFailRecord, Long>, JpaSpecificationExecutor<BusinessFailRecord> {
    List<BusinessFailRecord> findAllByRecordIdInAndTableAndDb(Collection<Long> recordId, String table, String db);

    List<BusinessFailRecord> findAllByRecordProcessStatusAndRecordRetryCntBetween(ProcessStatus recordProcessStatus, Integer minRecordRetryCnt, Integer maxRecordRetryCnt);

}
