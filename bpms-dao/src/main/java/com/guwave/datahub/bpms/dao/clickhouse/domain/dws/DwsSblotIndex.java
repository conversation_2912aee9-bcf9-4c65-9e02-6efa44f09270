package com.guwave.datahub.bpms.dao.clickhouse.domain.dws;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 2025/4/28 10:20
 * DwsSblotIndex
 *
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "dws_sblot_index_cluster")
public class DwsSblotIndex implements Serializable {

    private static final long serialVersionUID = 3949681930742267270L;
    @Id
    @Column(name = "CUSTOMER")
    private String customer;

    @Column(name = "SUB_CUSTOMER")
    private String subCustomer;

    @Column(name = "UPLOAD_TYPE")
    private String uploadType;

    @Column(name = "FILE_ID")
    private Integer fileId;

    @Column(name = "FILE_NAME")
    private String fileName;

    @Column(name = "FACTORY")
    private String factory;

    @Column(name = "FACTORY_SITE")
    private String factorySite;

    @Column(name = "FAB")
    private String fab;

    @Column(name = "FAB_SITE")
    private String fabSite;

    @Column(name = "TEST_AREA")
    private String testArea;

    @Column(name = "TEST_STAGE")
    private String testStage;

    @Column(name = "DEVICE_ID")
    private String deviceId;

    @Column(name = "LOT_TYPE")
    private String lotType;

    @Column(name = "LOT_ID")
    private String lotId;

    @Column(name = "SBLOT_ID")
    private String sblotId;

    @Column(name = "PROCESS")
    private String process;

    @Column(name = "WAFER_LOT_ID")
    private String waferLotId;

    @Column(name = "WAFER_ID")
    private String waferId;

    @Column(name = "WAFER_NO")
    private String waferNo;

    @Column(name = "TEST_PROGRAM")
    private String testProgram;

    @Column(name = "TEST_PROGRAM_VERSION")
    private String testProgramVersion;

    @Column(name = "TEST_TEMPERATURE")
    private String testTemperature;

    @Column(name = "TESTER_NAME")
    private String testerName;

    @Column(name = "TESTER_TYPE")
    private String testerType;

    @Column(name = "OPERATOR_NAME")
    private String operatorName;

    @Column(name = "PROBER_HANDLER_ID")
    private String proberHandlerId;

    @Column(name = "PROBECARD_LOADBOARD_ID")
    private String probecardLoadboardId;

    @Column(name = "START_TIME")
    private LocalDateTime startTime;

    @Column(name = "END_TIME")
    private LocalDateTime endTime;

    @Column(name = "START_HOUR_KEY")
    private String startHourKey;

    @Column(name = "START_DAY_KEY")
    private String startDayKey;

    @Column(name = "END_HOUR_KEY")
    private String endHourKey;

    @Column(name = "END_DAY_KEY")
    private String endDayKey;

    @Column(name = "TOTAL_CNT")
    private Integer totalCnt;

    @Column(name = "FIRST_PASS_CNT")
    private Integer firstPassCnt;

    @Column(name = "FIRST_FAIL_CNT")
    private Integer firstFailCnt;

    @Column(name = "FINAL_PASS_CNT")
    private Integer finalPassCnt;

    @Column(name = "FINAL_FAIL_CNT")
    private Integer finalFailCnt;

    @Column(name = "RECOVERY_CNT")
    private Integer recoveryCnt;

    @Column(name = "FIRST_YIELD")
    private BigDecimal firstYield;

    @Column(name = "FINAL_YIELD")
    private BigDecimal finalYield;

    @Column(name = "RECOVERY_YIELD")
    private BigDecimal recoveryYield;

    @Column(name = "LAST_RETEST_PASS_CNT")
    private Integer lastRetestPassCnt;

    @Column(name = "LAST_RETEST_FAIL_CNT")
    private Integer lastRetestFailCnt;

    @Column(name = "LAST_RETEST_YIELD")
    private BigDecimal lastRetestYield;

    @Column(name = "OFFLINE_RETEST_CNT")
    private String offlineRetestCnt;

    @Column(name = "OFFLINE_RETEST_PASS_CNT")
    private String offlineRetestPassCnt;

    @Column(name = "OFFLINE_RETEST_FAIL_CNT")
    private String offlineRetestFailCnt;

    @Column(name = "DATA_ACCURACY_CHECK")
    private String dataAccuracyCheck;

    @Column(name = "CREATE_HOUR_KEY")
    private String createHourKey;

    @Column(name = "CREATE_DAY_KEY")
    private String createDayKey;

    @Column(name = "CREATE_TIME")
    private LocalDateTime createTime;

    @Column(name = "CREATE_USER")
    private String createUser;

    @Column(name = "UPLOAD_TIME")
    private LocalDateTime uploadTime;

    @Column(name = "VERSION")
    private Long version;

    @Column(name = "IS_DELETE")
    private Byte isDelete;
}
