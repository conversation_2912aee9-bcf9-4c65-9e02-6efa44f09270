package com.guwave.datahub.bpms.dao.domain.qualityOverview;

import com.guwave.onedata.dataware.common.contant.TestArea;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.Set;
import java.util.stream.Collectors;

@Data
public class StatisticDws {
    private String factory;
    private String deviceId;
    private String testArea;
    private String testStage;
    private String lotId;
    private String sblotId;
    private String waferId;
    private String testProgram;

    private static final Set<String> SUPPORT_FT_TEST_AREA_NAMES = TestArea.getFTList().stream()
            .map(TestArea::getArea)
            .collect(Collectors.toSet());

    public static StatisticDws of(StatisticsIndex e) {
        StatisticDws dto = new StatisticDws();
        BeanUtils.copyProperties(e, dto);
        dto.setTestProgram(null);
        if ((SUPPORT_FT_TEST_AREA_NAMES.contains(e.getTestArea()))) {
            dto.setWaferId(null);
        }
        return dto;
    }

    public static StatisticDws of(FileLoadInfo e) {
        StatisticDws dto = new StatisticDws();
        BeanUtils.copyProperties(e, dto);
        if ((SUPPORT_FT_TEST_AREA_NAMES.contains(e.getTestArea()))) {
            dto.setWaferId(null);
        }
        return dto;
    }

}
