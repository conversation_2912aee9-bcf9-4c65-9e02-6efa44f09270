package com.guwave.datahub.bpms.dao.domain.qualityOverview;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class StatisticsIndex {
    private String week;
    private String factory;
    private String tester;
    private String testArea;
    private String testStage;
    private String deviceId;
    private String testProgram;
    private Long finalTotalCount;
    private String lotId;
    private String sbLotId;
    private String waferId;
    private String waferNo;
    private String lotWaferId;
    private Date endTime;
    private Date createTime;
    private BigDecimal firstYield;
    private BigDecimal finalYield;
    private String summaryMatchType;
    private Long summaryCount;
    private Long summaryPassCount;
    private Integer stdfFileCount;
    private BigDecimal summaryYield;
    private String dataAccuracyCheck;
}
