package com.guwave.datahub.bpms.dao.domain.retryFailRecord;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "bz_business_fail_record_query_condition")
public class BusinessFailRecordQueryCondition implements Serializable {

    private static final long serialVersionUID = -463842765430431198L;

    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    /**
     * 数据库名
     */
    @Column(name = "db")
    private String db;

    /**
     * 表名
     */
    @Column(name = "table")
    private String table;

    /**
     * 查询需要重试的失败记录的查询条件
     */
    @Column(name = "query_condition", columnDefinition = "LONGTEXT")
    private String queryCondition;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;
    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

}
