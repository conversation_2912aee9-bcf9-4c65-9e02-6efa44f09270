package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.common.model.vo.DeviceInfoDto;
import com.guwave.onedata.dataware.dao.mysql.domain.dc.DeviceInfo;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("BpmsDeviceInfoRepository")
public interface DeviceInfoRepository extends CrudRepository<DeviceInfo, Long> {

    @Query(value = "\n" +
            "select \n" +
            "       ifnull(product, '')     as product, \n" +
            "       ifnull(test_area, '')   as testArea, \n" +
            "       ifnull(device_id, '')   as deviceId, \n" +
            "       ifnull(test_stage, '')  as testStage, \n" +
            "       ifnull(data_source, '') as dataSource \n" +
            "from (select product_id, test_area, device_id, test_stage, data_source \n" +
            "      from dc_device_info \n" +
            "      where customer = :customer \n" +
            "        and factory = :factory \n" +
            "        and factory_site = :factorySite \n" +
            "        and delete_flag = 0 \n" +
            "        and (ifnull(product_id, 0), ifnull(test_area, ''), ifnull(data_source, '')) in (select ifnull(product_id, 0), ifnull(test_area, ''), ifnull(data_source, '') \n" +
            "                                                                 from dc_device_info \n" +
            "                                                                 where customer = :customer \n" +
            "                                                                   and factory = :factory \n" +
            "                                                                   and factory_site = :factorySite \n" +
            "                                                                   and device_id = :deviceId \n" +
            "                                                                   and delete_flag = 0)) x \n" +
            "         left join dc_product_info y on x.product_id = y.id \n"
            , nativeQuery = true)
    List<DeviceInfoDto> findDeviceInfo(
            @Param("customer") String customer,
            @Param("factory") String factory,
            @Param("factorySite") String factorySite,
            @Param("deviceId") String deviceId
    );

    @Query(value = "select distinct device_id\n" +
            "from dc_device_info a\n" +
            "         left join dc_product_info b on a.product_id = b.id\n" +
            "where a.delete_flag = 0\n" +
            "  and a.customer = :customer\n" +
            "  and (coalesce(:products) is null or b.product in (:products))\n" +
            "  and (coalesce(:productLines) is null or b.product_line in (:productLines))\n" +
            "  and (coalesce(:productFamilies) is null or b.product_family in (:productFamilies))"
            , nativeQuery = true)
    List<String> listDeviceIdByProductInfo(
            @Param("customer") String customer,
            @Param("products") List<String> products,
            @Param("productLines") List<String> productLines,
            @Param("productFamilies") List<String> productFamilies
    );
}
