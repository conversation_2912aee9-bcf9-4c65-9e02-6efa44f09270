package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.common.contant.UploadType;
import com.guwave.onedata.dataware.dao.mysql.domain.dc.FileInfo;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository("BpmsDcFileInfoRepository")
public interface DcFileInfoRepository extends CrudRepository<FileInfo, Long> {
    List<FileInfo> findAllByFileNameInAndUploadTypeAndDeleteFlag(Collection<String> fileName, UploadType uploadType, Boolean deleteFlag);
    List<FileInfo> findAllByIdInAndUploadTypeAndDeleteFlag(Collection<Long> id, UploadType uploadType, Boolean deleteFlag);
}
