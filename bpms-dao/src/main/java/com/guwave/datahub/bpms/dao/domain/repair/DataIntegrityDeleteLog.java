package com.guwave.datahub.bpms.dao.domain.repair;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;


@Entity
@Table(name = "bz_data_integrity_delete_log")
public class DataIntegrityDeleteLog implements Serializable {
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @Column(name = "customer")
    private String customer;

    @Column(name = "sub_customer")
    private String subCustomer;

    @Column(name = "file_id")
    private Long fileId;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "remark")
    private String remark;

    @Column(name = "delete_flag")
    private Integer deleteFlag;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "update_user")
    private String updateUser;

    public Long getId() {
        return id;
    }

    public DataIntegrityDeleteLog setId(Long id) {
        this.id = id;
        return this;
    }

    public String getCustomer() {
        return customer;
    }

    public DataIntegrityDeleteLog setCustomer(String customer) {
        this.customer = customer;
        return this;
    }

    public String getSubCustomer() {
        return subCustomer;
    }

    public DataIntegrityDeleteLog setSubCustomer(String subCustomer) {
        this.subCustomer = subCustomer;
        return this;
    }

    public Long getFileId() {
        return fileId;
    }

    public DataIntegrityDeleteLog setFileId(Long fileId) {
        this.fileId = fileId;
        return this;
    }

    public String getFileName() {
        return fileName;
    }

    public DataIntegrityDeleteLog setFileName(String fileName) {
        this.fileName = fileName;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public DataIntegrityDeleteLog setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public DataIntegrityDeleteLog setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public DataIntegrityDeleteLog setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public DataIntegrityDeleteLog setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public DataIntegrityDeleteLog setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public DataIntegrityDeleteLog setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }
}
