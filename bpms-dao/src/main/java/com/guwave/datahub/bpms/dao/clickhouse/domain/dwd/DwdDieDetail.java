package com.guwave.datahub.bpms.dao.clickhouse.domain.dwd;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 2025/5/8 9:39
 * DwdDieDetail
 *
 * <AUTHOR>
 */

@Data
@Entity
@Table(name = "dwd_die_detail_cluster")
public class DwdDieDetail {
    @Id
    @Column(name = "ID", columnDefinition = "String")
    private String id;

    @Column(name = "CUSTOMER", columnDefinition = "String")
    private String customer;

    @Column(name = "SUB_CUSTOMER", columnDefinition = "String")
    private String subCustomer;

    @Column(name = "UPLOAD_TYPE", columnDefinition = "String")
    private String uploadType;

    @Column(name = "FILE_ID", columnDefinition = "UInt32")
    private Long fileId;

    @Column(name = "FILE_NAME", columnDefinition = "String")
    private String fileName;

    @Column(name = "FILE_TYPE", columnDefinition = "String")
    private String fileType;

    @Column(name = "DEVICE_ID", columnDefinition = "String")
    private String deviceId;

    @Column(name = "FACTORY", columnDefinition = "String")
    private String factory;

    @Column(name = "FACTORY_SITE", columnDefinition = "String")
    private String factorySite;

    @Column(name = "FAB", columnDefinition = "String")
    private String fab;

    @Column(name = "FAB_SITE", columnDefinition = "String")
    private String fabSite;

    @Column(name = "LOT_TYPE", columnDefinition = "String")
    private String lotType;

    @Column(name = "LOT_ID", columnDefinition = "String")
    private String lotId;

    @Column(name = "PROCESS", columnDefinition = "String")
    private String process;

    @Column(name = "SBLOT_ID", columnDefinition = "String")
    private String sblotId;

    @Column(name = "WAFER_LOT_ID", columnDefinition = "String")
    private String waferLotId;

    @Column(name = "TEST_AREA", columnDefinition = "String")
    private String testArea;

    @Column(name = "TEST_STAGE", columnDefinition = "String")
    private String testStage;

    @Column(name = "OFFLINE_RETEST", columnDefinition = "Nullable(UInt8)")
    private Byte offlineRetest;

    @Column(name = "ONLINE_RETEST", columnDefinition = "Nullable(UInt8)")
    private Byte onlineRetest;

    @Column(name = "INTERRUPT", columnDefinition = "Nullable(UInt8)")
    private Byte interrupt;

    @Column(name = "DUP_RETEST", columnDefinition = "Nullable(UInt8)")
    private Byte dupRetest;

    @Column(name = "BATCH_NUM", columnDefinition = "Nullable(UInt8)")
    private Byte batchNum;

    @Column(name = "OFFLINE_RETEST_IGNORE_TP", columnDefinition = "Nullable(UInt8)")
    private Byte offlineRetestIgnoreTp;

    @Column(name = "INTERRUPT_IGNORE_TP", columnDefinition = "Nullable(UInt8)")
    private Byte interruptIgnoreTp;

    @Column(name = "DUP_RETEST_IGNORE_TP", columnDefinition = "Nullable(UInt8)")
    private Byte dupRetestIgnoreTp;

    @Column(name = "BATCH_NUM_IGNORE_TP", columnDefinition = "Nullable(UInt8)")
    private Byte batchNumIgnoreTp;

    @Column(name = "MAX_OFFLINE_RETEST", columnDefinition = "Nullable(UInt8)")
    private Byte maxOfflineRetest;

    @Column(name = "MAX_ONLINE_RETEST", columnDefinition = "Nullable(UInt8)")
    private Byte maxOnlineRetest;

    @Column(name = "IS_FIRST_TEST", columnDefinition = "Nullable(UInt8)")
    private Byte isFirstTest;

    @Column(name = "IS_FINAL_TEST", columnDefinition = "Nullable(UInt8)")
    private Byte isFinalTest;

    @Column(name = "IS_FIRST_TEST_IGNORE_TP", columnDefinition = "Nullable(UInt8)")
    private Byte isFirstTestIgnoreTp;

    @Column(name = "IS_FINAL_TEST_IGNORE_TP", columnDefinition = "Nullable(UInt8)")
    private Byte isFinalTestIgnoreTp;

    @Column(name = "IS_DUP_FIRST_TEST", columnDefinition = "Nullable(UInt8)")
    private Byte isDupFirstTest;

    @Column(name = "IS_DUP_FINAL_TEST", columnDefinition = "Nullable(UInt8)")
    private Byte isDupFinalTest;

    @Column(name = "IS_DUP_FIRST_TEST_IGNORE_TP", columnDefinition = "Nullable(UInt8)")
    private Byte isDupFirstTestIgnoreTp;

    @Column(name = "IS_DUP_FINAL_TEST_IGNORE_TP", columnDefinition = "Nullable(UInt8)")
    private Byte isDupFinalTestIgnoreTp;

    @Column(name = "NUM_TEST", columnDefinition = "Nullable(Int32)")
    private Integer numTest;

    @Column(name = "TEST_PROGRAM", columnDefinition = "String")
    private String testProgram;

    @Column(name = "TEST_TEMPERATURE", columnDefinition = "String")
    private String testTemperature;

    @Column(name = "TEST_PROGRAM_VERSION", columnDefinition = "String")
    private String testProgramVersion;

    @Column(name = "SPEC_NAM", columnDefinition = "String")
    private String specNam;

    @Column(name = "SPEC_VER", columnDefinition = "String")
    private String specVer;

    @Column(name = "HBIN_NUM", columnDefinition = "Nullable(UInt32)")
    private Long hbinNum;

    @Column(name = "SBIN_NUM", columnDefinition = "Nullable(UInt32)")
    private Integer sbinNum;

    @Column(name = "SBIN_PF", columnDefinition = "String")
    private String sbinPf;

    @Column(name = "SBIN_NAM", columnDefinition = "String")
    private String sbinNam;

    @Column(name = "HBIN_PF", columnDefinition = "String")
    private String hbinPf;

    @Column(name = "HBIN_NAM", columnDefinition = "String")
    private String hbinNam;

    @Column(name = "HBIN", columnDefinition = "String")
    private String hbin;

    @Column(name = "SBIN", columnDefinition = "String")
    private String sbin;

    @Column(name = "TEST_HEAD", columnDefinition = "Nullable(UInt32)")
    private Integer testHead;

    @Column(name = "TESTER_NAME", columnDefinition = "String")
    private String testerName;

    @Column(name = "TESTER_TYPE", columnDefinition = "String")
    private String testerType;

    @Column(name = "OPERATOR_NAME", columnDefinition = "String")
    private String operatorName;

    @Column(name = "PROBER_HANDLER_TYP", columnDefinition = "String")
    private String proberHandlerTyp;

    @Column(name = "PROBER_HANDLER_ID", columnDefinition = "String")
    private String proberHandlerId;

    @Column(name = "PROBECARD_LOADBOARD_TYP", columnDefinition = "String")
    private String probecardLoadboardTyp;

    @Column(name = "PROBECARD_LOADBOARD_ID", columnDefinition = "String")
    private String probecardLoadboardId;

    @Column(name = "PART_FLG", columnDefinition = "String")
    private String partFlg;

    @Column(name = "PART_ID", columnDefinition = "String")
    private String partId;

    @Column(name = "C_PART_ID", columnDefinition = "Nullable(UInt32)")
    private Integer cPartId;

    @Column(name = "UID", columnDefinition = "String")
    private String uid;

    @Column(name = "ECID", columnDefinition = "String")
    private String ecid;

    @Column(name = "ECID_EXT", columnDefinition = "String")
    private String ecidExt;

//    @ElementCollection
//    @Column(name = "ECID_EXTRA", columnDefinition = "Map(String, String)")
//    private Map<String, String> ecidExtra;

    @Column(name = "IS_STANDARD_ECID", columnDefinition = "Nullable(UInt8)")
    private Byte isStandardEcid;

    @Column(name = "X_COORD", columnDefinition = "Nullable(Int32)")
    private Integer xCoord;

    @Column(name = "Y_COORD", columnDefinition = "Nullable(Int32)")
    private Integer yCoord;

    @Column(name = "DIE_X", columnDefinition = "Nullable(Int32)")
    private Integer dieX;

    @Column(name = "DIE_Y", columnDefinition = "Nullable(Int32)")
    private Integer dieY;

    @Column(name = "TEST_TIME", columnDefinition = "Nullable(UInt32)")
    private Integer testTime;

    @Column(name = "PART_TXT", columnDefinition = "String")
    private String partTxt;

    @Column(name = "PART_FIX", columnDefinition = "String")
    private String partFix;

    @Column(name = "TOUCH_DOWN_ID", columnDefinition = "Nullable(UInt32)")
    private Integer touchDownId;

    @Column(name = "SITE", columnDefinition = "Nullable(UInt32)")
    private Integer site;

    @Column(name = "SITE_GRP", columnDefinition = "Nullable(UInt32)")
    private Integer siteGrp;

    @Column(name = "SITE_CNT", columnDefinition = "Nullable(UInt32)")
    private Integer siteCnt;

    @Column(name = "SITE_NUMS", columnDefinition = "String")
    private String siteNums;

    @Column(name = "TEXT_DAT", columnDefinition = "String")
    private String textDat;

    @Column(name = "START_TIME", columnDefinition = "Nullable(DateTime)")
    private LocalDateTime startTime;

    @Column(name = "END_TIME", columnDefinition = "Nullable(DateTime)")
    private LocalDateTime endTime;

    @Column(name = "START_HOUR_KEY", columnDefinition = "String")
    private String startHourKey;

    @Column(name = "START_DAY_KEY", columnDefinition = "String")
    private String startDayKey;

    @Column(name = "END_HOUR_KEY", columnDefinition = "String")
    private String endHourKey;

    @Column(name = "END_DAY_KEY", columnDefinition = "String")
    private String endDayKey;

    @Column(name = "WAFER_ID", columnDefinition = "String")
    private String waferId;

    @Column(name = "WAFER_NO", columnDefinition = "String")
    private String waferNo;

    @Column(name = "WAFER_SIZE", columnDefinition = "Nullable(Decimal(38, 18))")
    private BigDecimal waferSize;

    @Column(name = "WAFER_MARGIN", columnDefinition = "Nullable(Decimal(38, 18))")
    private BigDecimal waferMargin;

    @Column(name = "DIE_HEIGHT", columnDefinition = "Nullable(Decimal(38, 18))")
    private BigDecimal dieHeight;

    @Column(name = "DIE_WIDTH", columnDefinition = "Nullable(Decimal(38, 18))")
    private BigDecimal dieWidth;

    @Column(name = "WF_UNITS", columnDefinition = "Nullable(UInt32)")
    private Integer wfUnits;

    @Column(name = "WF_FLAT", columnDefinition = "String")
    private String wfFlat;

    @Column(name = "CENTER_X", columnDefinition = "Nullable(Int32)")
    private Integer centerX;

    @Column(name = "CENTER_Y", columnDefinition = "Nullable(Int32)")
    private Integer centerY;

    @Column(name = "CENTER_OFFSET_X", columnDefinition = "Nullable(Decimal(38, 18))")
    private BigDecimal centerOffsetX;

    @Column(name = "CENTER_OFFSET_Y", columnDefinition = "Nullable(Decimal(38, 18))")
    private BigDecimal centerOffsetY;

    @Column(name = "CENTER_RETICLE_X", columnDefinition = "Nullable(Int32)")
    private Integer centerReticleX;

    @Column(name = "CENTER_RETICLE_Y", columnDefinition = "Nullable(UInt8)")
    private Byte centerReticleY;

    @Column(name = "CENTER_RETICLE_OFFSET_X", columnDefinition = "Nullable(Decimal(38, 18))")
    private BigDecimal centerReticleOffsetX;

    @Column(name = "CENTER_RETICLE_OFFSET_Y", columnDefinition = "Nullable(Decimal(38, 18))")
    private BigDecimal centerReticleOffsetY;

    @Column(name = "POS_X", columnDefinition = "String")
    private String posX;

    @Column(name = "POS_Y", columnDefinition = "String")
    private String posY;

    @Column(name = "DIE_CNT", columnDefinition = "Nullable(UInt32)")
    private Integer dieCnt;

    @Column(name = "RETICLE_T_X", columnDefinition = "Nullable(Int32)")
    private Integer reticleTX;

    @Column(name = "RETICLE_T_Y", columnDefinition = "Nullable(Int32)")
    private Integer reticleTY;

    @Column(name = "RETICLE_X", columnDefinition = "Nullable(Int32)")
    private Integer reticleX;

    @Column(name = "RETICLE_Y", columnDefinition = "Nullable(Int32)")
    private Integer reticleY;

    @Column(name = "RETICLE_ROW", columnDefinition = "Nullable(UInt32)")
    private Integer reticleRow;

    @Column(name = "RETICLE_COLUMN", columnDefinition = "Nullable(UInt32)")
    private Integer reticleColumn;

    @Column(name = "RETICLE_ROW_CENTER_OFFSET", columnDefinition = "Nullable(Int32)")
    private Integer reticleRowCenterOffset;

    @Column(name = "RETICLE_COLUMN_CENTER_OFFSET", columnDefinition = "Nullable(Int32)")
    private Integer reticleColumnCenterOffset;

    @Column(name = "ORIGINAL_WAFER_SIZE", columnDefinition = "Nullable(Decimal(38, 18))")
    private BigDecimal originalWaferSize;

    @Column(name = "ORIGINAL_WAFER_MARGIN", columnDefinition = "Nullable(Decimal(38, 18))")
    private BigDecimal originalWaferMargin;

    @Column(name = "ORIGINAL_WF_UNITS", columnDefinition = "Nullable(UInt32)")
    private Integer originalWfUnits;

    @Column(name = "ORIGINAL_WF_FLAT", columnDefinition = "String")
    private String originalWfFlat;

    @Column(name = "ORIGINAL_POS_X", columnDefinition = "String")
    private String originalPosX;

    @Column(name = "ORIGINAL_POS_Y", columnDefinition = "String")
    private String originalPosY;

    @Column(name = "ORIGINAL_DIE_WIDTH", columnDefinition = "Nullable(Decimal(38, 18))")
    private BigDecimal originalDieWidth;

    @Column(name = "ORIGINAL_DIE_HEIGHT", columnDefinition = "Nullable(Decimal(38, 18))")
    private BigDecimal originalDieHeight;

    @Column(name = "ORIGINAL_RETICLE_ROW", columnDefinition = "Nullable(UInt32)")
    private Integer originalReticleRow;

    @Column(name = "ORIGINAL_RETICLE_COLUMN", columnDefinition = "Nullable(UInt32)")
    private Integer originalReticleColumn;

    @Column(name = "ORIGINAL_RETICLE_ROW_CENTER_OFFSET", columnDefinition = "Nullable(Int32)")
    private Integer originalReticleRowCenterOffset;

    @Column(name = "ORIGINAL_RETICLE_COLUMN_CENTER_OFFSET", columnDefinition = "Nullable(Int32)")
    private Integer originalReticleColumnCenterOffset;

    @Column(name = "ORIGINAL_CENTER_X", columnDefinition = "Nullable(Int32)")
    private Integer originalCenterX;

    @Column(name = "ORIGINAL_CENTER_Y", columnDefinition = "Nullable(Int32)")
    private Integer originalCenterY;

    @Column(name = "ORIGINAL_CENTER_RETICLE_X", columnDefinition = "Nullable(Int32)")
    private Integer originalCenterReticleX;

    @Column(name = "ORIGINAL_CENTER_RETICLE_Y", columnDefinition = "Nullable(UInt8)")
    private Byte originalCenterReticleY;

    @Column(name = "ORIGINAL_CENTER_OFFSET_X", columnDefinition = "Nullable(Decimal(38, 18))")
    private BigDecimal originalCenterOffsetX;

    @Column(name = "ORIGINAL_CENTER_OFFSET_Y", columnDefinition = "Nullable(Decimal(38, 18))")
    private BigDecimal originalCenterOffsetY;

    @Column(name = "ORIGINAL_CENTER_RETICLE_OFFSET_X", columnDefinition = "Nullable(Decimal(38, 18))")
    private BigDecimal originalCenterReticleOffsetX;

    @Column(name = "ORIGINAL_CENTER_RETICLE_OFFSET_Y", columnDefinition = "Nullable(Decimal(38, 18))")
    private BigDecimal originalCenterReticleOffsetY;

    @Column(name = "SITE_ID", columnDefinition = "String")
    private String siteId;

    @Column(name = "PART_CNT", columnDefinition = "Nullable(UInt32)")
    private Integer partCnt;

    @Column(name = "RTST_CNT", columnDefinition = "Nullable(UInt32)")
    private Integer rtstCnt;

    @Column(name = "ABRT_CNT", columnDefinition = "Nullable(UInt32)")
    private Integer abrtCnt;

    @Column(name = "GOOD_CNT", columnDefinition = "Nullable(UInt32)")
    private Integer goodCnt;

    @Column(name = "FUNC_CNT", columnDefinition = "Nullable(UInt32)")
    private Integer funcCnt;

    @Column(name = "FABWF_ID", columnDefinition = "String")
    private String fabwfId;

    @Column(name = "FRAME_ID", columnDefinition = "String")
    private String frameId;

    @Column(name = "MASK_ID", columnDefinition = "String")
    private String maskId;

    @Column(name = "WAFER_USR_DESC", columnDefinition = "String")
    private String waferUsrDesc;

    @Column(name = "WAFER_EXC_DESC", columnDefinition = "String")
    private String waferExcDesc;

    @Column(name = "SETUP_T", columnDefinition = "Nullable(DateTime)")
    private LocalDateTime setupT;

    @Column(name = "STAT_NUM", columnDefinition = "Nullable(UInt32)")
    private Integer statNum;

    @Column(name = "MODE_COD", columnDefinition = "String")
    private String modeCod;

    @Column(name = "PROT_COD", columnDefinition = "String")
    private String protCod;

    @Column(name = "BURN_TIM", columnDefinition = "Nullable(UInt32)")
    private Integer burnTim;

    @Column(name = "CMOD_COD", columnDefinition = "String")
    private String cmodCod;

    @Column(name = "EXEC_TYP", columnDefinition = "String")
    private String execTyp;

    @Column(name = "EXEC_VER", columnDefinition = "String")
    private String execVer;

    @Column(name = "USER_TXT", columnDefinition = "String")
    private String userTxt;

    @Column(name = "AUX_FILE", columnDefinition = "String")
    private String auxFile;

    @Column(name = "PKG_TYP", columnDefinition = "String")
    private String pkgTyp;

    @Column(name = "FAMLY_ID", columnDefinition = "String")
    private String famlyId;

    @Column(name = "DATE_COD", columnDefinition = "String")
    private String dateCod;

    @Column(name = "FACIL_ID", columnDefinition = "String")
    private String facilId;

    @Column(name = "FLOOR_ID", columnDefinition = "String")
    private String floorId;

    @Column(name = "PROC_ID", columnDefinition = "String")
    private String procId;

    @Column(name = "OPER_FRQ", columnDefinition = "String")
    private String operFrq;

    @Column(name = "FLOW_ID", columnDefinition = "String")
    private String flowId;

    @Column(name = "FLOW_ID_IGNORE_TP", columnDefinition = "String")
    private String flowIdIgnoreTp;

    @Column(name = "SETUP_ID", columnDefinition = "String")
    private String setupId;

    @Column(name = "DSGN_REV", columnDefinition = "String")
    private String dsgnRev;

    @Column(name = "ENG_ID", columnDefinition = "String")
    private String engId;

    @Column(name = "ROM_COD", columnDefinition = "String")
    private String romCod;

    @Column(name = "SERL_NUM", columnDefinition = "String")
    private String serlNum;

    @Column(name = "SUPR_NAM", columnDefinition = "String")
    private String suprNam;

    @Column(name = "DISP_COD", columnDefinition = "String")
    private String dispCod;

    @Column(name = "LOT_USR_DESC", columnDefinition = "String")
    private String lotUsrDesc;

    @Column(name = "LOT_EXC_DESC", columnDefinition = "String")
    private String lotExcDesc;

    @Column(name = "DIB_TYP", columnDefinition = "String")
    private String dibTyp;

    @Column(name = "DIB_ID", columnDefinition = "String")
    private String dibId;

    @Column(name = "CABL_TYP", columnDefinition = "String")
    private String cablTyp;

    @Column(name = "CABL_ID", columnDefinition = "String")
    private String cablId;

    @Column(name = "CONT_TYP", columnDefinition = "String")
    private String contTyp;

    @Column(name = "CONT_ID", columnDefinition = "String")
    private String contId;

    @Column(name = "LASR_TYP", columnDefinition = "String")
    private String lasrTyp;

    @Column(name = "LASR_ID", columnDefinition = "String")
    private String lasrId;

    @Column(name = "EXTR_TYP", columnDefinition = "String")
    private String extrTyp;

    @Column(name = "EXTR_ID", columnDefinition = "String")
    private String extrId;

//    @ElementCollection
//    @Column(name = "EFUSE_EXTRA", columnDefinition = "Map(String, String)")
//    private Map<String, String> efuseExtra;

    @Column(name = "CHIP_ID", columnDefinition = "String")
    private String chipId;

    @Column(name = "RETEST_BIN_NUM", columnDefinition = "String")
    private String retestBinNum;

//    @ElementCollection
//    @Column(name = "LONG_ATTRIBUTE_SET", columnDefinition = "Map(String, Int64)")
//    private Map<String, Long> longAttributeSet;

//    @ElementCollection
//    @Column(name = "STRING_ATTRIBUTE_SET", columnDefinition = "Map(String, String)")
//    private Map<String, String> stringAttributeSet;

//    @ElementCollection
//    @Column(name = "FLOAT_ATTRIBUTE_SET", columnDefinition = "Map(String, Decimal(38, 18))")
//    private Map<String, BigDecimal> floatAttributeSet;

    @Column(name = "CREATE_HOUR_KEY", columnDefinition = "String")
    private String createHourKey;

    @Column(name = "CREATE_DAY_KEY", columnDefinition = "String")
    private String createDayKey;

    @Column(name = "CREATE_TIME", columnDefinition = "DateTime")
    private LocalDateTime createTime;

    @Column(name = "CREATE_USER", columnDefinition = "String")
    private String createUser;

    @Column(name = "UPLOAD_TIME", columnDefinition = "DateTime")
    private LocalDateTime uploadTime;

    @Column(name = "DATA_VERSION", columnDefinition = "Int64")
    private Long dataVersion;

    @Column(name = "LOT_BUCKET", columnDefinition = "Int32")
    private Integer lotBucket;

    @Column(name = "IS_DELETE", columnDefinition = "UInt8")
    private Byte isDelete;
}
