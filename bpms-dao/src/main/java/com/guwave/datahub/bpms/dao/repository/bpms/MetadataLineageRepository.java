package com.guwave.datahub.bpms.dao.repository.bpms;

import com.guwave.datahub.bpms.dao.domain.metadata.MetadataLineage;
import com.guwave.datahub.bpms.dao.vo.bpms.metadata.TableInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * MetadataLineageRepository
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-03-13 17:02:05
 */
@Repository
public interface MetadataLineageRepository extends JpaRepository<MetadataLineage, Long> {

    @Query("select distinct new com.guwave.datahub.bpms.dao.vo.bpms.metadata.TableInfo(t.fromSource, t.fromDatabase, t.fromTable) from MetadataLineage t where t.toSource=:source and t.toDatabase=:database and t.toTable=:table")
    List<TableInfo> findParentTables(@Param("source") String source, @Param("database") String database, @Param("table") String table);

    @Query("select distinct new com.guwave.datahub.bpms.dao.vo.bpms.metadata.TableInfo(t.toSource, t.toDatabase, t.toTable) from MetadataLineage t where t.fromSource=:source and t.fromDatabase=:database and t.fromTable=:table")
    List<TableInfo> findChildTables(@Param("source") String source, @Param("database") String database, @Param("table") String table);

    @Query("select t from MetadataLineage t where t.toSource=:source and t.toDatabase=:database and t.toTable=:table and t.toField=:field")
    List<MetadataLineage> findParentFields(@Param("source") String source, @Param("database") String database, @Param("table") String table, @Param("field") String field);

    @Query("select t from MetadataLineage t where t.fromSource=:source and t.fromDatabase=:database and t.fromTable=:table and t.fromField=:field")
    List<MetadataLineage> findChildFields(@Param("source") String source, @Param("database") String database, @Param("table") String table, @Param("field") String field);
}
