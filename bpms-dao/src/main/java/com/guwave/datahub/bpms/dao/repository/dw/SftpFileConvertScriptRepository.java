package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpFileConvertScript;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.List;

@Repository("BpmsSftpFileConvertScriptRepository")
public interface SftpFileConvertScriptRepository extends CrudRepository<SftpFileConvertScript, Long> {

    @Query(nativeQuery = true, value = "select t.* from dw_sftp_file_convert_script t where t.customer = :customer and t.sub_customer = :subCustomer and t.test_area = :testArea and t.factory = :factory and t.factory_site = :factorySite and t.file_category = :fileCategory and :sftpFileName REGEXP t.reg_rule ")
    List<SftpFileConvertScript> findAllBySftpFileName(
            @Param("customer") String customer,
            @Param("subCustomer") String subCustomer,
            @Param("testArea") String testArea,
            @Param("factory") String factory,
            @Param("factorySite") String factorySite,
            @Param("fileCategory") String fileCategory,
            @Param("sftpFileName") String sftpFileName
    );

    List<SftpFileConvertScript> findAllByCustomer(String customer);

    @Query(value = "select *\n" +
            "from dw_sftp_file_convert_script \n" +
            "where (customer = :customer) \n" +
            " and (coalesce(:subCustomer,null) IS NULL OR (('(null)' in (:subCustomer) AND sub_customer IS NULL) OR sub_customer in (:subCustomer))) \n" +
            " and (coalesce(:factory,null) IS NULL OR (('(null)' in (:factory) AND factory IS NULL) OR factory in (:factory))) \n" +
            " and (coalesce(:testArea,null) IS NULL OR (('(null)' in (:testArea) AND test_area IS NULL) OR test_area in (:testArea))) \n" +
            " and (coalesce(:fileCategory,null) IS NULL OR (('(null)' in (:fileCategory) AND file_category IS NULL) OR file_category in (:fileCategory))) \n" +
            " order by create_time",
            nativeQuery = true)
    Page<SftpFileConvertScript> findAllByCustomerAndSubCustomerInAndFactoryInAndTestAreaInAndFileCategoryInOrderByUpdateTimeDesc(
            @Param("customer") String customer,
            @Param("subCustomer") List<String> subCustomer,
            @Param("factory") List<String> factory,
            @Param("testArea") List<String> testArea,
            @Param("fileCategory") List<String> fileCategory,
            Pageable pageable);

    @Transactional
    @Modifying
    @Query("delete from SftpFileConvertScript a where a.id in (:ids) ")
    void deleteAllByIds(@Param("ids") Collection<Long> ids);

    @Transactional
    @Modifying
    @Query(value = "delete from SftpFileConvertScript a where a.customer = :customer")
    void deleteAllByCustomer(@Param("customer") String customer);

    // 根据customer, sub_customer, factory, file_category, test_area查询全部id
    @Query(value = "select id\n" +
            "from dw_sftp_file_convert_script \n" +
            "where customer = :customer \n" +
            " and sub_customer = :subCustomer \n" +
            " and factory = :factory \n" +
            " and file_category = :fileCategory \n" +
            " and test_area = :testArea",
            nativeQuery = true)
    List<Long> findAllIdsByCustomerAndSubCustomerAndFactoryAndFileCategoryAndTestArea(
            @Param("customer") String customer,
            @Param("subCustomer") String subCustomer,
            @Param("factory") String factory,
            @Param("fileCategory") String fileCategory,
            @Param("testArea") String testArea);

}


