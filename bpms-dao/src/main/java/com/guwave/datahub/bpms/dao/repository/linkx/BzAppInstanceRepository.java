package com.guwave.datahub.bpms.dao.repository.linkx;

import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.TriggerTypeCount;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.linkx.common.constant.OptType;
import com.guwave.onedata.linkx.dao.mysql.domain.bz.BzAppInstance;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * Copyright (C), 2023, guwave
 * <p>
 * BzAppInstanceRepository
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2023-07-25 17:44:01
 */
@Repository
public interface BzAppInstanceRepository extends JpaRepository<BzAppInstance, Long> {

    BzAppInstance findFirstByCustomerAndSubCustomerAndFactoryAndTestAreaAndDeviceIdAndTestStageAndLotIdAndWaferNoAndLotTypeAndOptTypeAndAppDefinitionIdOrderByDataVersionDescUpdateTimeDesc(String customer, String subCustomer, String factory, TestArea testArea, String deviceId, String testStage, String lotId, String waferNo, LotType lotType, OptType optType, Long appDefinitionId);

    List<BzAppInstance> findAllByCustomerAndSubCustomerAndFactoryAndTestAreaAndDeviceIdAndTestStageAndLotIdAndWaferNoAndLotTypeAndOptTypeAndAppDefinitionId(
            String customer, String subCustomer, String factory, TestArea testArea, String deviceId, String testStage, String lotId, String waferNo, LotType lotType, OptType optType, Long appDefinitionId);


    List<BzAppInstance> findByProcessStatusOrderByIdDesc(ProcessStatus processStatus);

    @Transactional
    @Modifying
    @Query(value = "update bz_app_instance\n" +
            "set process_status = 'CREATE'\n" +
            "where id in (select * from (select id from bz_app_instance where process_status = 'PROCESSING') x)", nativeQuery = true)
    void updateProcessingToCreate();

    @Transactional
    @Modifying
    @Query(value = "update bz_app_instance\n" +
            "set process_status = 'FAIL'\n" +
            "where id in (select * from (select id from bz_app_instance where process_status = 'CREATE' and opt_type in ('NEW_TEST_ITEM_DATA','NEW_BITMEM_TEST_ITEM_DATA')) x)", nativeQuery = true)
    void updateTestItemCreateToFail();

    @Transactional
    @Modifying
    @Query(value = "UPDATE bz_app_instance " +
            "SET priority = :priority, " +
            "    update_time = :updateTime, " +
            "    update_user = :userName " +
            "WHERE customer = :customer " +
            "AND sub_customer = :subCustomer " +
            "AND factory = :factory " +
            "AND factory_site = :factorySite " +
            "AND ((:deviceId IS NULL) OR device_id = :deviceId)" +
            "AND test_area = :testArea " +
            "AND lot_type = :lotType " +
            "AND file_category = :fileCategory " +
            "AND ((:testStage IS NULL) OR test_stage = :testStage)" +
            "AND ((:lotId IS NULL) OR lot_id = :lotId)" +
            "AND ((:waferNo IS NULL) OR wafer_no = :waferNo)" +
            "AND process_status = 'CREATE'" +
            "AND opt_type in ('NEW_TEST_ITEM_DATA','NEW_BITMEM_TEST_ITEM_DATA')",
            nativeQuery = true)
    void updatePriority(
            @Param("customer") String customer,
            @Param("subCustomer") String subCustomer,
            @Param("factory") String factory,
            @Param("factorySite") String factorySite,
            @Param("deviceId") String deviceId,
            @Param("testArea") String testArea,
            @Param("lotType") String lotType,
            @Param("fileCategory") String fileCategory,
            @Param("testStage") String testStage,
            @Param("lotId") String lotId,
            @Param("waferNo") String waferNo,
            @Param("priority") Integer priority,
            @Param("userName") String userName,
            @Param("updateTime") Date updateTime
    );

    @Query(value = "SELECT COUNT(1) AS nums " +
            "FROM bz_app_instance " +
            "WHERE opt_type = :optType " +
            "AND process_status = 'SUCCESS' " +
            "AND create_time BETWEEN :startTime AND :endTime", nativeQuery = true)
    int findCountByOptTypeAndProcessStatusAndTimeRange(
            @Param("optType") String optType,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );

    @Query(value = "SELECT COUNT(1) AS nums, trigger_type " +
            "FROM bz_app_instance " +
            "WHERE opt_type IN (:optTypes) " +
            "AND create_time BETWEEN :startTime AND :endTime " +
            "GROUP BY trigger_type", nativeQuery = true)
    List<Object[]> findCountByOptTypeAndTimeRangeGroupByTriggerType(
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime,
            @Param("optTypes") List<String> optTypes
    );

    @Query(value = "SELECT COUNT(1) AS nums " +
            "FROM bz_app_instance " +
            "WHERE opt_type IN (:optTypes) " +
            "AND create_time BETWEEN :startTime AND :endTime", nativeQuery = true)
    Long findCountByOptTypeAndTimeRange(
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime,
            @Param("optTypes") List<String> optTypes
    );
}
