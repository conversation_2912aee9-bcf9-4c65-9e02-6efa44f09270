package com.guwave.datahub.bpms.dao.vo.bpms.dailyReport;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.persistence.ColumnResult;
import javax.persistence.ConstructorResult;
import javax.persistence.Entity;
import javax.persistence.SqlResultSetMapping;

@AllArgsConstructor
@NoArgsConstructor
public class WarehousingSuccessFileResult {
    private Long successFileCnt;
    private Double successFileSize;
    private Long successBitMemFileCnt;
    private Double successBitMemFileSize;
    private Long successSummaryFileCnt;
    private Double successSummaryFileSize;
    private Long successRawDataFileCnt;
    private Double successRawDataFileSize;
    private Long successStdfFileCnt;
    private Double successStdfFileSize;

    public Long getSuccessFileCnt() {
        return successFileCnt;
    }

    public void setSuccessFileCnt(Long successFileCnt) {
        this.successFileCnt = successFileCnt;
    }

    public Double getSuccessFileSize() {
        return successFileSize;
    }

    public void setSuccessFileSize(Double successFileSize) {
        this.successFileSize = successFileSize;
    }

    public Long getSuccessBitMemFileCnt() {
        return successBitMemFileCnt;
    }

    public void setSuccessBitMemFileCnt(Long successBitMemFileCnt) {
        this.successBitMemFileCnt = successBitMemFileCnt;
    }

    public Double getSuccessBitMemFileSize() {
        return successBitMemFileSize;
    }

    public void setSuccessBitMemFileSize(Double successBitMemFileSize) {
        this.successBitMemFileSize = successBitMemFileSize;
    }

    public Long getSuccessSummaryFileCnt() {
        return successSummaryFileCnt;
    }

    public void setSuccessSummaryFileCnt(Long successSummaryFileCnt) {
        this.successSummaryFileCnt = successSummaryFileCnt;
    }

    public Double getSuccessSummaryFileSize() {
        return successSummaryFileSize;
    }

    public void setSuccessSummaryFileSize(Double successSummaryFileSize) {
        this.successSummaryFileSize = successSummaryFileSize;
    }

    public Long getSuccessRawDataFileCnt() {
        return successRawDataFileCnt;
    }

    public void setSuccessRawDataFileCnt(Long successRawDataFileCnt) {
        this.successRawDataFileCnt = successRawDataFileCnt;
    }

    public Double getSuccessRawDataFileSize() {
        return successRawDataFileSize;
    }

    public void setSuccessRawDataFileSize(Double successRawDataFileSize) {
        this.successRawDataFileSize = successRawDataFileSize;
    }

    public Long getSuccessStdfFileCnt() {
        return successStdfFileCnt;
    }

    public void setSuccessStdfFileCnt(Long successStdfFileCnt) {
        this.successStdfFileCnt = successStdfFileCnt;
    }

    public Double getSuccessStdfFileSize() {
        return successStdfFileSize;
    }

    public void setSuccessStdfFileSize(Double successStdfFileSize) {
        this.successStdfFileSize = successStdfFileSize;
    }
}
