package com.guwave.datahub.bpms.dao.converter;

import com.alibaba.fastjson.JSON;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.List;


@Converter(autoApply = true)
public class ListToJsonArrayIntegerConverter implements AttributeConverter<List<Integer>, String> {

    @Override
    public String convertToDatabaseColumn(List<Integer> attribute) {
        return attribute == null ? null : JSON.toJSONString(attribute);
    }

    @Override
    public List<Integer> convertToEntityAttribute(String dbData) {
        return dbData == null ? null : JSON.parseArray(dbData, Integer.class);
    }

}
