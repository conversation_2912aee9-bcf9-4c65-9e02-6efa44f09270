package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.datahub.bpms.dao.vo.bpms.testItemTestPlan.TestItemTestPlanDimensionInterface;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.TestProgramTestPlan;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("BpmsTestProgramTestPlanRepository")
public interface TestProgramTestPlanRepository extends CrudRepository<TestProgramTestPlan, Long> {

    @Query(value = "select customer,\n" +
            "       sub_customer subCustomer,\n" +
            "       device_id    deviceId,\n" +
            "       test_stage   testStage,\n" +
            "       test_program testProgram,\n" +
            "       test_area    testArea,\n" +
            "       lot_type     lotType,\n" +
            "       factory,\n" +
            "       factory_site factorySite,\n" +
            "       upload_type  uploadType\n" +
            "from dw_test_program_test_plan\n" +
            "where customer = :customer\n" +
            "  and sub_customer = :subCustomer\n" +
            "  and device_id = :deviceId\n" +
            "  and test_stage = :testStage\n" +
            "  and test_program = :testProgram\n" +
            "  and if(:testArea IS NULL, 1, test_area = :testArea)\n" +
            "  and if(:lotType IS NULL, 1, lot_type = :lotType)\n" +
            "  and if(:factory IS NULL, 1, factory = :factory)\n" +
            "  and if(:factorySite IS NULL, 1, factory_site = :factorySite)\n" +
            "  and if(:uploadType IS NULL, 1, upload_type = :uploadType)\n" +
            "group by customer,\n" +
            "         sub_customer,\n" +
            "         device_id,\n" +
            "         test_stage,\n" +
            "         test_program,\n" +
            "         test_area,\n" +
            "         lot_type,\n" +
            "         factory,\n" +
            "         factory_site,\n" +
            "         upload_type"
            , nativeQuery = true)
    List<TestItemTestPlanDimensionInterface> findWithDimension(
            @Param("customer") String customer,
            @Param("subCustomer") String subCustomer,
            @Param("deviceId") String deviceId,
            @Param("testStage") String testStage,
            @Param("testProgram") String testProgram,
            @Param("testArea") String testArea,
            @Param("lotType") String lotType,
            @Param("factory") String factory,
            @Param("factorySite") String factorySite,
            @Param("uploadType") String uploadType
    );

    List<TestProgramTestPlan> findByCustomerAndSubCustomerAndDeviceIdAndTestStageAndTestProgramAndTestAreaAndLotTypeAndFactoryAndFactorySiteAndUploadType(
            String customer, String subCustomer, String deviceId, String testStage,
            String testProgram, String testArea, String lotType, String factory,
            String factorySite, String uploadType
    );
}
