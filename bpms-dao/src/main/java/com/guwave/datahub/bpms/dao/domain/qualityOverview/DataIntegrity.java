package com.guwave.datahub.bpms.dao.domain.qualityOverview;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DataIntegrity {
    private Long id;
    private String deviceName;
    private String testArea;
    private String testStage;
    private String lotId;
    private String waferId;
    private String fileName;
    private String fileLocation;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    private String dataLoaded;
    private String dataLoadingFailedStep;

    @JsonProperty("loadingFailedType")
    private String failedType;
    private String originalFailedType;
    private String sblotId;
    private String lotType;
    private String testProgram;
    private boolean enableDownload;
    private String exceptionMsg;
    private String failedFields;
    private Integer step;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @JsonIgnore
    private String processStatus;
    @JsonIgnore
    private String exceptionType;

    private String remark;

    private String fileLabel;

    private Boolean deleteFlag;

    private Boolean reRunFlag;

    private Boolean repairFlag;

    private String factory;

    private String factorySite;

    private Long fileId;

    private String waferNo;

    private String fileCategory;

    private String customer;

    private String subCustomer;
}
