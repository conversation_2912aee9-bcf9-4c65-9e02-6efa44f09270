package com.guwave.datahub.bpms.dao.repository.compute;

import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputeFailMessageRecord;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface ComputeFailMessageRecordRepository extends CrudRepository<ComputeFailMessageRecord, Long> {

    List<ComputeFailMessageRecord> findAllByProcessStatusAndDeleteFlag(ProcessStatus processStatus, Integer deleteFlag);
}
