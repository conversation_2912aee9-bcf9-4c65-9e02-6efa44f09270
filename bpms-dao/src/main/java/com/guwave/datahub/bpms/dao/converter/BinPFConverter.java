package com.guwave.datahub.bpms.dao.converter;

import com.guwave.datahub.bpms.common.constant.BinPF;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * BinPF枚举转换器
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Converter(autoApply = true)
public class BinPFConverter implements AttributeConverter<BinPF, String> {

    @Override
    public String convertToDatabaseColumn(BinPF binPF) {
        return null == binPF ? null : binPF.name();
    }

    @Override
    public BinPF convertToEntityAttribute(String binPF) {
        return BinPF.of(binPF);
    }
}