package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.dao.mysql.domain.dw.UidRule;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("BpmsUidRuleRepository")
public interface UidRuleRepository extends CrudRepository<UidRule, Long> {

    List<UidRule> findByCustomerAndSubCustomerAndTestAreaAndFactoryAndFactorySite(String customer, String subCustomer, String testArea, String factory, String factorySite);
}
