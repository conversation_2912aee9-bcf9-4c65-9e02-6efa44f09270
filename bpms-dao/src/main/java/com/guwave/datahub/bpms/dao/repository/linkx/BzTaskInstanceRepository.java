package com.guwave.datahub.bpms.dao.repository.linkx;

import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.TaskExecutionDetail;
import com.guwave.onedata.linkx.dao.mysql.domain.bz.BzTaskInstance;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * Copyright (C), 2023, guwave
 * <p>
 * BzTaskInstanceRepository
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2023-07-25 17:44:01
 */
@Repository
public interface BzTaskInstanceRepository extends CrudRepository<BzTaskInstance, Long> {

    List<BzTaskInstance> findAllByAppInstanceId(Long appInstanceId);

    @Query(value = "SELECT MAX(id) FROM bz_task_instance WHERE create_time BETWEEN :startTime AND :endTime", nativeQuery = true)
    Long findMaxIdByCreateTimeBetween(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
    @Query(value = "SELECT MIN(id) FROM bz_task_instance WHERE create_time BETWEEN :startTime AND :endTime", nativeQuery = true)
    Long findMinIdByCreateTimeBetween(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Query(value = "SELECT l.name, r.execute_time " +
            "FROM (" +
            "    SELECT execute_time, task_definition_id " +
            "    FROM bz_task_instance i " +
            "    WHERE create_time BETWEEN :startTime AND :endTime " +
            ") r " +
            "JOIN (" +
            "    SELECT t.id AS id, t.name AS name " +
            "    FROM bz_task_definition t " +
            "    JOIN (" +
            "        SELECT id " +
            "        FROM bz_app_definition " +
            "        WHERE opt_type = :optType " + // 使用 :optType 参数
            "    ) a ON t.app_definition_id = a.id " +
            ") l ON r.task_definition_id = l.id", nativeQuery = true)
    List<Object[]> findTaskExecutionDetails(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("optType") String optType);
}
