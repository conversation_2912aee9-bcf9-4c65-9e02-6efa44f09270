package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpFileDetail;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository("BpmsSftpFileDetailRepository")
public interface SftpFileDetailRepository extends CrudRepository<SftpFileDetail, Long> {

    List<SftpFileDetail> findByProcessStatusAndConvertFlagOrderByIdDesc(ProcessStatus processStatus, Integer convertFlag);

    @Transactional
    @Modifying
    @Query(value = "delete\n" +
            "from dw_sftp_file_detail\n" +
            "where id in (select * from (select id from dw_sftp_file_detail where transfer_status = 'PROCESSING') x)", nativeQuery = true)
    void deleteTransferProcessing();

    @Transactional
    @Modifying
    @Query(value = "update dw_sftp_file_detail\n" +
            "set batch_status = 'SUCCESS'\n" +
            "where id in (select * from (select id from dw_sftp_file_detail where batch_status = 'PROCESSING') x)", nativeQuery = true)
    void updateBatchProcessingToSuccess();

    @Transactional
    @Modifying
    @Query(value = "update dw_sftp_file_detail\n" +
            "set process_status = 'CREATE'\n" +
            "where id in\n" +
            "      (select * from (select id from dw_sftp_file_detail where process_status = 'PROCESSING' and convert_flag = 1) x)", nativeQuery = true)
    void updateConvertProcessingToCreate();

    @Transactional
    @Modifying
    @Query(value = "update dw_sftp_file_detail\n" +
            "set process_status = 'CREATE'\n" +
            "where id in\n" +
            "      (select * from (select id from dw_sftp_file_detail where process_status = 'PROCESSING' and convert_flag = 0) x)", nativeQuery = true)
    void updateParseProcessingToCreate();
}
