package com.guwave.datahub.bpms.dao.projection;

import java.util.Date;

public interface RankedLotMetaDataDetail {
    Long getId();
    String getTestArea();
    String getFactory();
    String getDeviceId();
    String getFileCategory();
    String getLotId();
    String getWaferNo();
    String getLotType();
    String getTestStage();
    Integer getPriority();
    Date getCreateTime();
    Date getUpdateTime();
    Long getRankNum();
}
