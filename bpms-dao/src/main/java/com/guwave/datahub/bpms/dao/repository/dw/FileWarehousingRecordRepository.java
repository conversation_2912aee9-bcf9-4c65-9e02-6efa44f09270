package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.datahub.bpms.dao.vo.dw.FileWarehousingMinVo;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.FileWarehousingRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository("BpmsFileWarehousingRecordRepository")
public interface FileWarehousingRecordRepository extends CrudRepository<FileWarehousingRecord, Long>, JpaSpecificationExecutor<FileWarehousingRecord> {

    @Query(value = "select case " +
            "           when LOWER(:filterField) = LOWER('test_area') then test_area " +
            "           when LOWER(:filterField) = LOWER('factory') then factory " +
            "           when LOWER(:filterField) = LOWER('device_id') then device_id " +
            "           when LOWER(:filterField) = LOWER('lot_id') then lot_id " +
            "           when LOWER(:filterField) = LOWER('wafer_no') then wafer_no " +
            "           when LOWER(:filterField) = LOWER('sblot_id') then sblot_id " +
            "           when LOWER(:filterField) = LOWER('lot_type') then lot_type " +
            "           when LOWER(:filterField) = LOWER('test_stage') then test_stage " +
            "           when LOWER(:filterField) = LOWER('test_program') then test_program " +
            "           when LOWER(:filterField) = LOWER('step') then step " +
            "           when LOWER(:filterField) = LOWER('process_status') then process_status " +
            "           when LOWER(:filterField) = LOWER('exception_message') then exception_message " +
            "           end as result " +
            "from dw_file_warehousing_record " +
            "      where (customer = :customer)  " +
            "        and (ifnull(:testArea,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :testArea AND test_area IS NULL) OR test_area = :testArea)) " +
            "        and (coalesce(:factory,null) IS NULL OR (('(null)' in (:factory) AND factory IS NULL) OR factory in (:factory))) " +
            "        and (coalesce(:deviceId,null) IS NULL OR (('(null)' in (:deviceId) AND device_id IS NULL) OR device_id in (:deviceId))) " +
            "        and (ifnull(:fileCategory,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :fileCategory AND file_category IS NULL) OR file_category = :fileCategory)) " +
            "        and (coalesce(:lotId,null) IS NULL OR (('(null)' in (:lotId) AND lot_id IS NULL) OR lot_id in (:lotId))) " +
            "        and (coalesce(:waferNo,null) IS NULL OR (('(null)' in (:waferNo) AND wafer_no IS NULL) OR wafer_no in (:waferNo))) " +
            "        and (coalesce(:sblotId,null) IS NULL OR (('(null)' in (:sblotId) AND sblot_id IS NULL) OR sblot_id in (:sblotId))) " +
            "        and (ifnull(:lotType,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :lotType AND lot_type IS NULL) OR lot_type = :lotType)) " +
            "        and (coalesce(:testStage,null) IS NULL OR (('(null)' in (:testStage) AND test_stage IS NULL) OR test_stage in (:testStage))) " +
            "        and (coalesce(:testProgram,null) IS NULL OR (('(null)' in (:testProgram) AND test_program IS NULL) OR test_program in (:testProgram))) " +
            "        and (coalesce(:step,null) IS NULL OR (('(null)' in (:step) AND step IS NULL) OR step in (:step))) " +
            "        and (coalesce(:processStatus,null) IS NULL OR (('(null)' in (:processStatus) AND process_status IS NULL) OR process_status in (:processStatus))) " +
            "        and (ifnull(:exceptionMessage,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :exceptionMessage AND exception_message IS NULL) OR exception_message = :exceptionMessage)) " +
            "        and (ifnull(:startCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR create_time >= :startCreateTime) " +
            "        and (ifnull(:endCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR create_time <= :endCreateTime)" +
            "        and (ifnull(:startUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR update_time >= :startUpdateTime) " +
            "        and (ifnull(:endUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR update_time <= :endUpdateTime)" +
            "        and (coalesce(:allSupportFileCategory,null) IS NULL OR (('(null)' in (:allSupportFileCategory) AND file_category IS NULL) OR file_category in (:allSupportFileCategory))) " +
            "        and (file_name not like '/%')" +
            "        and (convert_flag = 0 or process_status in ('FAIL', 'PROCESSING'))" +
            "group by result " +
            "order by result",
            nativeQuery = true)
    List<String> findDynamicField(
            @Param("filterField") String filterField,
            @Param("customer") String customer,
            @Param("testArea") String testArea,
            @Param("factory") List<String> factory,
            @Param("deviceId") List<String> deviceId,
            @Param("fileCategory") String fileCategory,
            @Param("lotId") List<String> lotId,
            @Param("waferNo") List<String> waferNo,
            @Param("sblotId") List<String> sblotId,
            @Param("lotType") String lotType,
            @Param("testStage") List<String> testStage,
            @Param("testProgram") List<String> testProgram,
            @Param("step") List<Integer> step,
            @Param("processStatus") List<String> processStatus,
            @Param("exceptionMessage") String exceptionMessage,
            @Param("startCreateTime") String startCreateTime,
            @Param("endCreateTime") String endCreateTime,
            @Param("startUpdateTime") String startUpdateTime,
            @Param("endUpdateTime") String endUpdateTime,
            @Param("allSupportFileCategory") List<String> allSupportFileCategory
    );

    @Query(value = "select case " +
            "           when LOWER(:filterField) = LOWER('test_area') then test_area " +
            "           when LOWER(:filterField) = LOWER('factory') then factory " +
            "           when LOWER(:filterField) = LOWER('step') then step " +
            "           when LOWER(:filterField) = LOWER('exception_message') then exception_message " +
            "           end as result " +
            "from dw_file_warehousing_record " +
            "      where (customer = :customer)  " +
            "        and (ifnull(:testArea,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :testArea AND test_area IS NULL) OR test_area = :testArea)) " +
            "        and (coalesce(:factoryList,null) IS NULL OR (('(null)' in (:factoryList) AND factory IS NULL) OR factory in (:factoryList))) " +
            "        and (ifnull(:fileCategory,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :fileCategory AND file_category IS NULL) OR file_category = :fileCategory)) " +
            "        and (coalesce(:stepList,null) IS NULL OR (('(null)' in (:stepList) AND step IS NULL) OR step in (:stepList))) " +
            "        and (coalesce(:statusList,null) IS NULL OR (('(null)' in (:statusList) AND process_status IS NULL) OR process_status in (:statusList))) " +
            "        and (ifnull(:exceptionMessage,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :exceptionMessage AND exception_message IS NULL) OR exception_message = :exceptionMessage)) " +
            "        and (ifnull(:startCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :startCreateTime AND create_time IS NULL) OR create_time >= :startCreateTime)) " +
            "        and (ifnull(:endCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :endCreateTime AND create_time IS NULL) OR create_time <= :endCreateTime)) " +
            "        and (ifnull(:startUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :startUpdateTime AND update_time IS NULL) OR update_time >= :startUpdateTime)) " +
            "        and (ifnull(:endUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :endUpdateTime AND update_time IS NULL) OR update_time <= :endUpdateTime)) " +
            "        and (coalesce(:allSupportFileCategory,null) IS NULL OR (('(null)' in (:allSupportFileCategory) AND file_category IS NULL) OR file_category in (:allSupportFileCategory))) " +
            "        and (file_name not like '/%')" +
            "        and (convert_flag = 0 or process_status in ('FAIL', 'PROCESSING'))" +
            "group by result " +
            "order by result",
            nativeQuery = true)
    List<String> findDynamicFieldByFile(
            @Param("filterField") String filterField,
            @Param("customer") String customer,
            @Param("testArea") String testArea,
            @Param("factoryList") List<String> factoryList,
            @Param("fileCategory") String fileCategory,
            @Param("stepList") List<Integer> stepList,
            @Param("statusList") List<String> statusList,
            @Param("exceptionMessage") String exceptionMessage,
            @Param("startCreateTime") String startCreateTime,
            @Param("endCreateTime") String endCreateTime,
            @Param("startUpdateTime") String startUpdateTime,
            @Param("endUpdateTime") String endUpdateTime,
            @Param("allSupportFileCategory") List<String> allSupportFileCategory
    );

    @Query(value = "select * from dw_file_warehousing_record" +
            "      where (customer = :customer)  " +
            "        and (ifnull(:fileName,'bpms_mock_null') = 'bpms_mock_null' OR file_name REGEXP :fileName) " +
            "        and (ifnull(:remoteFilePath,'bpms_mock_null') = 'bpms_mock_null' OR ftp_path LIKE CONCAT(:remoteFilePath, '%')) " +
            "        and (ifnull(:testArea,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :testArea AND test_area IS NULL) OR test_area = :testArea)) " +
            "        and (coalesce(:factoryList,null) IS NULL OR (('(null)' in (:factoryList) AND factory IS NULL) OR factory in (:factoryList))) " +
            "        and (ifnull(:fileCategory,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :fileCategory AND file_category IS NULL) OR file_category = :fileCategory)) " +
            "        and (coalesce(:stepList,null) IS NULL OR (('(null)' in (:stepList) AND step IS NULL) OR step in (:stepList))) " +
            "        and (coalesce(:statusList,null) IS NULL OR (('(null)' in (:statusList) AND process_status IS NULL) OR process_status in (:statusList))) " +
            "        and (ifnull(:exceptionMessage,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :exceptionMessage AND exception_message IS NULL) OR exception_message = :exceptionMessage)) " +
            "        and (ifnull(:startCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :startCreateTime AND create_time IS NULL) OR create_time >= :startCreateTime)) " +
            "        and (ifnull(:endCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :endCreateTime AND create_time IS NULL) OR create_time <= :endCreateTime)) " +
            "        and (ifnull(:startUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :startUpdateTime AND update_time IS NULL) OR update_time >= :startUpdateTime)) " +
            "        and (ifnull(:endUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :endUpdateTime AND update_time IS NULL) OR update_time <= :endUpdateTime)) " +
            "        and (coalesce(:allSupportFileCategory,null) IS NULL OR (('(null)' in (:allSupportFileCategory) AND file_category IS NULL) OR file_category in (:allSupportFileCategory))) " +
            "        and (file_name not like '/%')" +
            "        and (convert_flag = 0 or process_status in ('FAIL', 'PROCESSING'))",
            countQuery = "select count(1) from dw_file_warehousing_record" +
                    "      where (customer = :customer)  " +
                    "        and (ifnull(:fileName,'bpms_mock_null') = 'bpms_mock_null' OR file_name REGEXP :fileName) " +
                    "        and (ifnull(:remoteFilePath,'bpms_mock_null') = 'bpms_mock_null' OR ftp_path LIKE CONCAT(:remoteFilePath, '%')) " +
                    "        and (ifnull(:testArea,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :testArea AND test_area IS NULL) OR test_area = :testArea)) " +
                    "        and (coalesce(:factoryList,null) IS NULL OR (('(null)' in (:factoryList) AND factory IS NULL) OR factory in (:factoryList))) " +
                    "        and (ifnull(:fileCategory,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :fileCategory AND file_category IS NULL) OR file_category = :fileCategory)) " +
                    "        and (coalesce(:stepList,null) IS NULL OR (('(null)' in (:stepList) AND step IS NULL) OR step in (:stepList))) " +
                    "        and (coalesce(:statusList,null) IS NULL OR (('(null)' in (:statusList) AND process_status IS NULL) OR process_status in (:statusList))) " +
                    "        and (ifnull(:exceptionMessage,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :exceptionMessage AND exception_message IS NULL) OR exception_message = :exceptionMessage)) " +
                    "        and (ifnull(:startCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :startCreateTime AND create_time IS NULL) OR create_time >= :startCreateTime)) " +
                    "        and (ifnull(:endCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :endCreateTime AND create_time IS NULL) OR create_time <= :endCreateTime)) " +
                    "        and (ifnull(:startUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :startUpdateTime AND update_time IS NULL) OR update_time >= :startUpdateTime)) " +
                    "        and (ifnull(:endUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :endUpdateTime AND update_time IS NULL) OR update_time <= :endUpdateTime)) " +
                    "        and (coalesce(:allSupportFileCategory,null) IS NULL OR (('(null)' in (:allSupportFileCategory) AND file_category IS NULL) OR file_category in (:allSupportFileCategory))) " +
                    "        and (file_name not like '/%')" +
                    "        and (convert_flag = 0 or process_status in ('FAIL', 'PROCESSING'))",
            nativeQuery = true)
    Page<FileWarehousingRecord> findWarehousingFileByFileFilters(
            @Param("customer") String customer,
            @Param("fileName") String fileName,
            @Param("remoteFilePath") String remoteFilePath,
            @Param("testArea") String testArea,
            @Param("factoryList") List<String> factoryList,
            @Param("fileCategory") String fileCategory,
            @Param("stepList") List<Integer> stepList,
            @Param("statusList") List<String> statusList,
            @Param("exceptionMessage") String exceptionMessage,
            @Param("startCreateTime") String startCreateTime,
            @Param("endCreateTime") String endCreateTime,
            @Param("startUpdateTime") String startUpdateTime,
            @Param("endUpdateTime") String endUpdateTime,
            @Param("allSupportFileCategory") List<String> allSupportFileCategory,
            Pageable pageable
    );

    @Query(value = "select id from dw_file_warehousing_record" +
            "      where (customer = :customer)  " +
            "        and (ifnull(:fileName,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :fileName AND file_name IS NULL) OR file_name REGEXP :fileName)) " +
            "        and (ifnull(:remoteFilePath,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :remoteFilePath AND ftp_path IS NULL) OR ftp_path LIKE CONCAT(:remoteFilePath, '%'))) " +
            "        and (ifnull(:testArea,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :testArea AND test_area IS NULL) OR test_area = :testArea)) " +
            "        and (coalesce(:factoryList,null) IS NULL OR (('(null)' in (:factoryList) AND factory IS NULL) OR factory in (:factoryList))) " +
            "        and (ifnull(:fileCategory,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :fileCategory AND file_category IS NULL) OR file_category = :fileCategory)) " +
            "        and (coalesce(:stepList,null) IS NULL OR (('(null)' in (:stepList) AND step IS NULL) OR step in (:stepList))) " +
            "        and (coalesce(:statusList,null) IS NULL OR (('(null)' in (:statusList) AND process_status IS NULL) OR process_status in (:statusList))) " +
            "        and (ifnull(:exceptionMessage,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :exceptionMessage AND exception_message IS NULL) OR exception_message = :exceptionMessage)) " +
            "        and (ifnull(:startCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :startCreateTime AND create_time IS NULL) OR create_time >= :startCreateTime)) " +
            "        and (ifnull(:endCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :endCreateTime AND create_time IS NULL) OR create_time <= :endCreateTime)) " +
            "        and (ifnull(:startUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :startUpdateTime AND update_time IS NULL) OR update_time >= :startUpdateTime)) " +
            "        and (ifnull(:endUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :endUpdateTime AND update_time IS NULL) OR update_time <= :endUpdateTime)) " +
            "        and (coalesce(:allSupportFileCategory,null) IS NULL OR (('(null)' in (:allSupportFileCategory) AND file_category IS NULL) OR file_category in (:allSupportFileCategory))) " +
            "        and (file_name not like '/%')" +
            "        and (convert_flag = 0 or process_status in ('FAIL', 'PROCESSING'))",
            nativeQuery = true)
    List<Long> findWarehousingRecordIdByFileFilters(
            @Param("customer") String customer,
            @Param("fileName") String fileName,
            @Param("remoteFilePath") String remoteFilePath,
            @Param("testArea") String testArea,
            @Param("factoryList") List<String> factoryList,
            @Param("fileCategory") String fileCategory,
            @Param("stepList") List<Integer> stepList,
            @Param("statusList") List<String> statusList,
            @Param("exceptionMessage") String exceptionMessage,
            @Param("startCreateTime") String startCreateTime,
            @Param("endCreateTime") String endCreateTime,
            @Param("startUpdateTime") String startUpdateTime,
            @Param("endUpdateTime") String endUpdateTime,
            @Param("allSupportFileCategory") List<String> allSupportFileCategory
    );

    List<FileWarehousingRecord> findAllByFileNameIn(List<String> fileName);

    Long countByIdInAndProcessStatusAndStepLessThan(List<Long> ids, ProcessStatus processStatus, Integer step);


    @Query(value = "select * from dw_file_warehousing_record" +
            "      where (customer = :customer)  " +
            "        and (ifnull(:testArea,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :testArea AND test_area IS NULL) OR test_area = :testArea)) " +
            "        and (coalesce(:factory,null) IS NULL OR (('(null)' in (:factory) AND factory IS NULL) OR factory in (:factory))) " +
            "        and (coalesce(:deviceId,null) IS NULL OR (('(null)' in (:deviceId) AND device_id IS NULL) OR device_id in (:deviceId))) " +
            "        and (ifnull(:fileCategory,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :fileCategory AND file_category IS NULL) OR file_category = :fileCategory)) " +
            "        and (coalesce(:lotId,null) IS NULL OR (('(null)' in (:lotId) AND lot_id IS NULL) OR lot_id in (:lotId))) " +
            "        and (coalesce(:waferNo,null) IS NULL OR (('(null)' in (:waferNo) AND wafer_no IS NULL) OR wafer_no in (:waferNo))) " +
            "        and (coalesce(:sblotId,null) IS NULL OR (('(null)' in (:sblotId) AND sblot_id IS NULL) OR sblot_id in (:sblotId))) " +
            "        and (ifnull(:lotType,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :lotType AND lot_type IS NULL) OR lot_type = :lotType)) " +
            "        and (coalesce(:testStage,null) IS NULL OR (('(null)' in (:testStage) AND test_stage IS NULL) OR test_stage in (:testStage))) " +
            "        and (coalesce(:testProgram,null) IS NULL OR (('(null)' in (:testProgram) AND test_program IS NULL) OR test_program in (:testProgram))) " +
            "        and (coalesce(:step,null) IS NULL OR (('(null)' in (:step) AND step IS NULL) OR step in (:step))) " +
            "        and (coalesce(:processStatus,null) IS NULL OR (('(null)' in (:processStatus) AND process_status IS NULL) OR process_status in (:processStatus))) " +
            "        and (ifnull(:exceptionMessage,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :exceptionMessage AND exception_message IS NULL) OR exception_message = :exceptionMessage)) " +
            "        and (ifnull(:startCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR create_time >= :startCreateTime) " +
            "        and (ifnull(:endCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR create_time <= :endCreateTime)" +
            "        and (ifnull(:startUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR update_time >= :startUpdateTime) " +
            "        and (ifnull(:endUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR update_time <= :endUpdateTime)" +
            "        and (coalesce(:allSupportFileCategory,null) IS NULL OR (('(null)' in (:allSupportFileCategory) AND file_category IS NULL) OR file_category in (:allSupportFileCategory))) " +
            "        and (file_name not like '/%')" +
            "        and (convert_flag = 0 or process_status in ('FAIL', 'PROCESSING'))",
            countQuery = "select count(1) from dw_file_warehousing_record" +
                    "      where (customer = :customer)  " +
                    "        and (ifnull(:testArea,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :testArea AND test_area IS NULL) OR test_area = :testArea)) " +
                    "        and (coalesce(:factory,null) IS NULL OR (('(null)' in (:factory) AND factory IS NULL) OR factory in (:factory))) " +
                    "        and (coalesce(:deviceId,null) IS NULL OR (('(null)' in (:deviceId) AND device_id IS NULL) OR device_id in (:deviceId))) " +
                    "        and (ifnull(:fileCategory,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :fileCategory AND file_category IS NULL) OR file_category = :fileCategory)) " +
                    "        and (coalesce(:lotId,null) IS NULL OR (('(null)' in (:lotId) AND lot_id IS NULL) OR lot_id in (:lotId))) " +
                    "        and (coalesce(:waferNo,null) IS NULL OR (('(null)' in (:waferNo) AND wafer_no IS NULL) OR wafer_no in (:waferNo))) " +
                    "        and (coalesce(:sblotId,null) IS NULL OR (('(null)' in (:sblotId) AND sblot_id IS NULL) OR sblot_id in (:sblotId))) " +
                    "        and (ifnull(:lotType,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :lotType AND lot_type IS NULL) OR lot_type = :lotType)) " +
                    "        and (coalesce(:testStage,null) IS NULL OR (('(null)' in (:testStage) AND test_stage IS NULL) OR test_stage in (:testStage))) " +
                    "        and (coalesce(:testProgram,null) IS NULL OR (('(null)' in (:testProgram) AND test_program IS NULL) OR test_program in (:testProgram))) " +
                    "        and (coalesce(:step,null) IS NULL OR (('(null)' in (:step) AND step IS NULL) OR step in (:step))) " +
                    "        and (coalesce(:processStatus,null) IS NULL OR (('(null)' in (:processStatus) AND process_status IS NULL) OR process_status in (:processStatus))) " +
                    "        and (ifnull(:exceptionMessage,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :exceptionMessage AND exception_message IS NULL) OR exception_message = :exceptionMessage)) " +
                    "        and (ifnull(:startCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR create_time >= :startCreateTime) " +
                    "        and (ifnull(:endCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR create_time <= :endCreateTime)" +
                    "        and (ifnull(:startUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR update_time >= :startUpdateTime) " +
                    "        and (ifnull(:endUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR update_time <= :endUpdateTime)" +
                    "        and (coalesce(:allSupportFileCategory,null) IS NULL OR (('(null)' in (:allSupportFileCategory) AND file_category IS NULL) OR file_category in (:allSupportFileCategory))) " +
                    "        and (file_name not like '/%')" +
                    "        and (convert_flag = 0 or process_status in ('FAIL', 'PROCESSING'))",
            nativeQuery = true)
    Page<FileWarehousingRecord> findWarehousingFileByLotWaferFilters(
            @Param("customer") String customer,
            @Param("testArea") String testArea,
            @Param("factory") List<String> factory,
            @Param("deviceId") List<String> deviceId,
            @Param("fileCategory") String fileCategory,
            @Param("lotId") List<String> lotId,
            @Param("waferNo") List<String> waferNo,
            @Param("sblotId") List<String> sblotId,
            @Param("lotType") String lotType,
            @Param("testStage") List<String> testStage,
            @Param("testProgram") List<String> testProgram,
            @Param("step") List<Integer> step,
            @Param("processStatus") List<String> processStatus,
            @Param("exceptionMessage") String exceptionMessage,
            @Param("startCreateTime") String startCreateTime,
            @Param("endCreateTime") String endCreateTime,
            @Param("startUpdateTime") String startUpdateTime,
            @Param("endUpdateTime") String endUpdateTime,
            @Param("allSupportFileCategory") List<String> allSupportFileCategory,
            Pageable pageable
    );

    @Query(value = "select id from dw_file_warehousing_record" +
            "      where (customer = :customer)  " +
            "        and (ifnull(:testArea,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :testArea AND test_area IS NULL) OR test_area = :testArea)) " +
            "        and (coalesce(:factory,null) IS NULL OR (('(null)' in (:factory) AND factory IS NULL) OR factory in (:factory))) " +
            "        and (coalesce(:deviceId,null) IS NULL OR (('(null)' in (:deviceId) AND device_id IS NULL) OR device_id in (:deviceId))) " +
            "        and (ifnull(:fileCategory,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :fileCategory AND file_category IS NULL) OR file_category = :fileCategory)) " +
            "        and (coalesce(:lotId,null) IS NULL OR (('(null)' in (:lotId) AND lot_id IS NULL) OR lot_id in (:lotId))) " +
            "        and (coalesce(:waferNo,null) IS NULL OR (('(null)' in (:waferNo) AND wafer_no IS NULL) OR wafer_no in (:waferNo))) " +
            "        and (coalesce(:sblotId,null) IS NULL OR (('(null)' in (:sblotId) AND sblot_id IS NULL) OR sblot_id in (:sblotId))) " +
            "        and (ifnull(:lotType,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :lotType AND lot_type IS NULL) OR lot_type = :lotType)) " +
            "        and (coalesce(:testStage,null) IS NULL OR (('(null)' in (:testStage) AND test_stage IS NULL) OR test_stage in (:testStage))) " +
            "        and (coalesce(:testProgram,null) IS NULL OR (('(null)' in (:testProgram) AND test_program IS NULL) OR test_program in (:testProgram))) " +
            "        and (coalesce(:step,null) IS NULL OR (('(null)' in (:step) AND step IS NULL) OR step in (:step))) " +
            "        and (coalesce(:processStatus,null) IS NULL OR (('(null)' in (:processStatus) AND process_status IS NULL) OR process_status in (:processStatus))) " +
            "        and (ifnull(:exceptionMessage,'bpms_mock_null') = 'bpms_mock_null' OR (('(null)' = :exceptionMessage AND exception_message IS NULL) OR exception_message = :exceptionMessage)) " +
            "        and (ifnull(:startCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR create_time >= :startCreateTime) " +
            "        and (ifnull(:endCreateTime,'bpms_mock_null') = 'bpms_mock_null' OR create_time <= :endCreateTime)" +
            "        and (ifnull(:startUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR update_time >= :startUpdateTime) " +
            "        and (ifnull(:endUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR update_time <= :endUpdateTime)" +
            "        and (coalesce(:allSupportFileCategory,null) IS NULL OR (('(null)' in (:allSupportFileCategory) AND file_category IS NULL) OR file_category in (:allSupportFileCategory))) " +
            "        and (file_name not like '/%')" +
            "        and (convert_flag = 0 or process_status in ('FAIL', 'PROCESSING'))",
            nativeQuery = true)
    List<Long> findFileWarehousingRecordIdByLotWaferFilters(
            @Param("customer") String customer,
            @Param("testArea") String testArea,
            @Param("factory") List<String> factory,
            @Param("deviceId") List<String> deviceId,
            @Param("fileCategory") String fileCategory,
            @Param("lotId") List<String> lotId,
            @Param("waferNo") List<String> waferNo,
            @Param("sblotId") List<String> sblotId,
            @Param("lotType") String lotType,
            @Param("testStage") List<String> testStage,
            @Param("testProgram") List<String> testProgram,
            @Param("step") List<Integer> step,
            @Param("processStatus") List<String> processStatus,
            @Param("exceptionMessage") String exceptionMessage,
            @Param("startCreateTime") String startCreateTime,
            @Param("endCreateTime") String endCreateTime,
            @Param("startUpdateTime") String startUpdateTime,
            @Param("endUpdateTime") String endUpdateTime,
            @Param("allSupportFileCategory") List<String> allSupportFileCategory);


    @Query(value = "select count(1) from dw_file_warehousing_record where source_file_names like (select concat('%',file_name,'%') from dw_file_warehousing_record where id = :id and convert_flag = '1')",
            nativeQuery = true)
    Integer countConvertedFile(@Param("id") Long id);


    @Query(value = "select distinct " +
            "               sub_customer, " +
            "               factory, " +
            "               test_area, " +
            "               device_id, " +
            "               test_stage, " +
            "               lot_id, " +
            "               if(:isCp = 'true', wafer_no, '') wafer_no, " +
            "               if(:isCp = 'true', '', sblot_id) sblot_id, " +
            "               lot_type " +
            "from dw_lot_wafer_warehousing_record" +
            "      where (customer = :customer)  " +
            "        and (coalesce(:subCustomerList,null) IS NULL OR subCustomerList IS NULL OR sub_customer in (:subCustomerListsubCustomerList)) " +
            "        and (coalesce(:factoryList,null) IS NULL OR factoryList IS NULL OR factory in (:factoryList)) " +
            "        and (test_area = :testArea) " +
            "        and (coalesce(:deviceIdList,null) IS NULL OR deviceIdList IS NULL OR device_id in (:deviceIdList)) " +
            "        and (coalesce(:testStageList,null) IS NULL OR testStageList IS NULL OR test_stage in (:testStageList)) " +
            "        and (coalesce(:lotIdList,null) IS NULL OR lotIdList IS NULL OR lot_id in (:lotIdList)) " +
            "        and (:isCp = 'true' and (coalesce(:waferNoList,null) IS NULL OR waferNoList IS NULL OR wafer_no in (:waferNoList))) " +
            "        and (:isCp = 'false' and (coalesce(:sblotIdList,null) IS NULL OR sblotIdList IS NULL OR sblot_id in (:sblotIdList))) " +
            "        and (coalesce(:lotTypeList,null) IS NULL OR lotTypeList IS NULL OR lot_type in (:lotTypeList)) " +
            "        and (ifnull(:startUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR update_time >= :startUpdateTime) " +
            "        and (ifnull(:endUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR update_time <= :endUpdateTime)" +
            "        and step = '7100' " +
            "        and convert_flag = '0' " +
            "        and process_status = 'SUCCESS' " +
            "        and file_category in (:allSupportFileCategory))) ",
            nativeQuery = true)
    List<FileWarehousingRecord> find7100SuccessFileWarehousingByFilters(
            @Param("customer") String customer,
            @Param("subCustomerList") List<String> subCustomerList,
            @Param("factoryList") List<String> factoryList,
            @Param("testArea") String testArea,
            @Param("deviceIdList") List<String> deviceIdList,
            @Param("testStageList") List<String> testStageList,
            @Param("lotIdList") List<String> lotIdList,
            @Param("waferNoList") List<String> waferNoList,
            @Param("sblotIdList") List<String> sblotIdList,
            @Param("lotTypeList") List<String> lotTypeList,
            @Param("startUpdateTime") String startUpdateTime,
            @Param("endUpdateTime") String endUpdateTime,
            @Param("allSupportFileCategory") List<String> allSupportFileCategory,
            @Param("isCp") boolean isCp
    );

    @Query("select new com.guwave.datahub.bpms.dao.vo.dw.FileWarehousingMinVo(t.id, t.customer, t.subCustomer, t.factory, t.factorySite, t.testArea, t.fileCategory, t.deviceId, t.lotType, t.testStage, t.lotId, t.waferNo, t.sblotId, t.fileName) from FileWarehousingRecord t where t.id in (:id) order by t.customer, t.subCustomer, t.factory, t.factorySite, t.testArea, t.fileCategory, t.deviceId, t.lotType, t.testStage, t.lotId, t.waferNo, t.sblotId, t.fileName")
    List<FileWarehousingMinVo> findFileWarehousingMinVoByIdIn(@Param("id") Collection<Long> id);

}
