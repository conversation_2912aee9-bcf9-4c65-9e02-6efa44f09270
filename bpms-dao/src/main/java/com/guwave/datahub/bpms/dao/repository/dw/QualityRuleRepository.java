package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.dao.mysql.domain.dw.QualityRule;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;


@Repository("BpmsQualityRuleRepository")
public interface QualityRuleRepository extends CrudRepository<QualityRule, Long> {
    Page<QualityRule> findAllByDeleteFlagOrderByExecuteTimeTypeDescSqlTypeAscStatusDescQualityNameAsc(Integer deleteFlag, Pageable pageable);

    List<QualityRule> findAllByDeleteFlagOrderByExecuteTimeTypeDescSqlTypeAscQualityNameAsc(Integer deleteFlag);

    QualityRule findByIdAndDeleteFlag(Long id, Integer deleteFlag);

    List<QualityRule> findByQualityNameInAndDeleteFlag(List<String> qualityName, Integer deleteFlag);
}
