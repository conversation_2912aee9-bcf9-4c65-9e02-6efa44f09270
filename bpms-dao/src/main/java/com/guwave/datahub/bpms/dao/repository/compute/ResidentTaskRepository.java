package com.guwave.datahub.bpms.dao.repository.compute;

import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.dao.mysql.domain.ResidentTask;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


public interface ResidentTaskRepository extends CrudRepository<ResidentTask, Long> {
    List<ResidentTask> findByProcessStatusInOrderByIdDesc(List<ProcessStatus> processStatus);

    @Transactional
    @Modifying
    @Query(value = "update bz_resident_task\n" +
            "set process_status = 'FAIL',message_flag = 1\n" +
            "where id in (select * from (select id from bz_resident_task where process_status in ('CREATE', 'PROCESSING', 'COMMITTED')) x)", nativeQuery = true)
    void updateNotFinishToFail();
}
