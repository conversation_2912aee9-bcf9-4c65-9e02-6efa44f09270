package com.guwave.datahub.bpms.dao.converter;

import com.guwave.datahub.bpms.common.constant.BinType;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * BinType枚举转换器
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Converter(autoApply = true)
public class BinTypeConverter implements AttributeConverter<BinType, String> {

    @Override
    public String convertToDatabaseColumn(BinType binType) {
        return null == binType ? null : binType.name();
    }

    @Override
    public BinType convertToEntityAttribute(String binType) {
        return BinType.of(binType);
    }
}