package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.dao.mysql.domain.dw.QualityDailyResult;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;


@Repository("BpmsQualityDailyResultRepository")
public interface QualityDailyResultRepository extends CrudRepository<QualityDailyResult, Long> {

    @Query("select dayKey from QualityDailyResult where qualityRuleId = :qualityRuleId group by dayKey order by dayKey desc")
    List<String> findDayKeys(@Param("qualityRuleId") Long qualityRuleId, Pageable pageable);

    List<QualityDailyResult> findByQualityRuleIdAndDayKey(Long qualityRuleId, String dayKey);
}
