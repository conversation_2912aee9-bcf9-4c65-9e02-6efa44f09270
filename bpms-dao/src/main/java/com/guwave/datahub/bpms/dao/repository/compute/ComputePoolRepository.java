package com.guwave.datahub.bpms.dao.repository.compute;

import com.guwave.datahub.bpms.dao.projection.RankedComputePool;
import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputePool;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * ComputePoolRepository
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-14 15:54:40
 */
@Repository("BpmsComputePoolRepository")
public interface ComputePoolRepository extends CrudRepository<ComputePool, Long> {

    Optional<ComputePool> findByUniqueIdAndProcessStatus(String uniqueId, ProcessStatus processStatus);

    List<ComputePool> findByProcessStatusInOrderByIdDesc(List<ProcessStatus> processStatus);

    @Query(value = "WITH ranked_data AS (" +
            "  SELECT " +
            "cp.id, " +
            "cp.app_name, " +
            "cp.compute_type, " +
            "cp.die_cnt, " +
            "cp.test_item_cnt, " +
            "cp.queue, " +
            "cp.priority_group, " +
            "cp.process_status, " +
            "cp.create_time, " +
            "cp.update_time, " +
            "ROW_NUMBER() OVER (ORDER BY " +
            "    CASE cp.process_status " +
            "      WHEN 'PROCESSING' THEN 0 " +
            "      WHEN 'CREATE' THEN 1 " +
            "      ELSE 2 " +
            "    END, " +
            "    cp.priority_group, " +
            "    cp.priority" +
            "  ) as rank_num " +
            "  FROM bz_compute_pool cp " +
            "  WHERE cp.process_status IN ('CREATE', 'PROCESSING')" +
            ") " +
            "SELECT " +
            "rd.id, " +
            "rd.app_name appName, " +
            "rd.compute_type computeType, " +
            "rd.die_cnt dieCnt, " +
            "rd.test_item_cnt testItemCnt, " +
            "rd.queue, " +
            "rd.priority_group priorityGroup, " +
            "rd.process_status processStatus, " +
            "rd.create_time createTime, " +
            "rd.update_time updateTime, " +
            "rd.rank_num rankNum " +
            "FROM ranked_data rd " +
            "WHERE (:appName IS NULL OR rd.app_name LIKE CONCAT('%', :appName, '%')) " +
            "AND (:queue IS NULL OR rd.queue = :queue) " +
            "AND (:computeType IS NULL OR rd.compute_type = :computeType) " +
            "AND (:processStatus IS NULL OR rd.process_status = :processStatus) ",
            countQuery = "SELECT COUNT(*) FROM bz_compute_pool " +
                    "WHERE process_status IN ('CREATE', 'PROCESSING')" +
                    "AND (:appName IS NULL OR app_name LIKE CONCAT('%', :appName, '%')) " +
                    "AND (:queue IS NULL OR queue = :queue) " +
                    "AND (:computeType IS NULL OR compute_type = :computeType) " +
                    "AND (:processStatus IS NULL OR process_status = :processStatus) ",
            nativeQuery = true)
    Page<RankedComputePool> findByConditions(
            @Param("appName") String appName,
            @Param("queue") String queue,
            @Param("computeType") String computeType,
            @Param("processStatus") String processStatus,
            Pageable pageable
    );

    @Transactional
    @Modifying
    @Query(value = "update bz_compute_pool\n" +
            "set process_status = 'FAIL'\n" +
            "where id in (select * from (select id from bz_compute_pool where process_status in ('CREATE', 'PROCESSING', 'COMMITTED')) x)", nativeQuery = true)
    void updateNotFinishToFail();

    @Transactional
    @Modifying
    @Query(value = "UPDATE bz_compute_pool " +
            "SET priority_group = :priorityGroup, " +
            "    update_time = NOW(), " +
            "    update_user = :userName " +
            "WHERE id IN :ids ",
            nativeQuery = true)
    void updatePriorityByIds(
            @Param("ids") List<Long> ids,
            @Param("priorityGroup") Integer priorityGroup,
            @Param("userName") String userName
    );

    List<ComputePool> findAllByProcessStatusAndUpdateTimeBetween(ProcessStatus processStatus, Date startTime, Date endTime);
}
