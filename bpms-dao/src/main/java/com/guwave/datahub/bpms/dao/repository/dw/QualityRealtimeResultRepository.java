package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.QueueDetail;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.QualityRealtimeResult;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;


@Repository("BpmsQualityRealtimeResultRepository")
public interface QualityRealtimeResultRepository extends CrudRepository<QualityRealtimeResult, Long> {

    @Query("select dayKey from QualityRealtimeResult where qualityRuleId = :qualityRuleId group by dayKey order by dayKey desc")
    List<String> findDayKeys(@Param("qualityRuleId") Long qualityRuleId, Pageable pageable);

    List<QualityRealtimeResult> findByQualityRuleIdAndDayKeyOrderByTimeKey(Long qualityRuleId, String dayKey);
    @Query(value =
            "SELECT time_key , 'queueYarnCapacityLimit' AS seriesName, jt.queueYarnCapacityLimit AS value " +
                    "FROM dw_quality_realtime_result, JSON_TABLE(result, '$[0]' COLUMNS (queueYarnCapacityLimit FLOAT PATH '$.queueYarnCapacityLimit')) AS jt " +
                    "WHERE day_key BETWEEN :startDate AND :endDate AND quality_name = :qualityName " +
                    "UNION ALL " +
                    "SELECT time_key , 'queueYarnUsagePercentage' AS seriesName, jt.queueYarnUsagePercentage AS value " +
                    "FROM dw_quality_realtime_result, JSON_TABLE(result, '$[0]' COLUMNS (queueYarnUsagePercentage FLOAT PATH '$.queueYarnUsagePercentage')) AS jt " +
                    "WHERE day_key BETWEEN :startDate AND :endDate AND quality_name = :qualityName " +
                    "UNION ALL " +
                    "SELECT time_key , 'taskQueueUsagePercentage' AS seriesName, jt.taskQueueUsagePercentage AS value " +
                    "FROM dw_quality_realtime_result, JSON_TABLE(result, '$[0]' COLUMNS (taskQueueUsagePercentage FLOAT PATH '$.taskQueueUsagePercentage')) AS jt " +
                    "WHERE day_key BETWEEN :startDate AND :endDate AND quality_name = :qualityName",
            nativeQuery = true)
    List<Object[]> findQueueDetailByQueueName(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("qualityName") String qualityName);
}
