package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.DataClearRule;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.FileRenameRule;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpSubCustomerRule;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.List;

@Repository("BpmsSftpSubCustomerRuleRepository")
public interface SftpSubCustomerRuleRepository extends CrudRepository<SftpSubCustomerRule, Long> {
    @Query(value = "select *\n" +
            "from dw_sftp_sub_customer_rule \n" +
            "where (customer = :customer) \n" +
            " and (coalesce(:subCustomer,null) IS NULL OR (('(null)' in (:subCustomer) AND sub_customer IS NULL) OR sub_customer in (:subCustomer))) \n" +
            " and (coalesce(:factory,null) IS NULL OR (('(null)' in (:factory) AND factory IS NULL) OR factory in (:factory))) \n" +
            " and (coalesce(:testArea,null) IS NULL OR (('(null)' in (:testArea) AND test_area IS NULL) OR test_area in (:testArea))) \n" +
            " order by create_time",
            nativeQuery = true)
    Page<SftpSubCustomerRule> findAllByCustomerAndSubCustomerInAndFactoryInAndTestAreaInOrderByUpdateTimeDesc(
            @Param("customer") String customer,
            @Param("subCustomer") List<String> subCustomer,
            @Param("factory") List<String> factory,
            @Param("testArea") List<String> testArea,
            Pageable pageable);

    @Transactional
    @Modifying
    @Query("delete from SftpSubCustomerRule a where a.id in (:ids) ")
    void deleteAllByIds(@Param("ids") Collection<Long> ids);

    List<SftpSubCustomerRule> findAllByCustomer(String customer);
    @Query(value = "select id\n" +
            "from dw_sftp_sub_customer_rule \n" +
            "where customer = :customer \n" +
            " and sub_customer = :subCustomer \n" +
            " and factory = :factory \n" +
            " and test_area = :testArea",
            nativeQuery = true)
    List<Long> findAllIdsByCustomerAndSubCustomerAndFactoryAndTestArea(
            @Param("customer") String customer,
            @Param("subCustomer") String subCustomer,
            @Param("factory") String factory,
            @Param("testArea") String testArea);

    @Transactional
    @Modifying
    @Query(value = "delete from SftpSubCustomerRule a where a.customer = :customer")
    void deleteAllByCustomer(@Param("customer") String customer);
}
