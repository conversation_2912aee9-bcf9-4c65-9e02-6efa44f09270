package com.guwave.datahub.bpms.dao.domain.metadata;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Data
@Entity
@Table(name = "bz_metadata_table_link")
public class MetadataTableLink implements Serializable {

    private static final long serialVersionUID = 7816961989974377924L;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @Column(name = "datasource")
    private String datasource;

    @Column(name = "database_name")
    private String databaseName;

    @Column(name = "table_name")
    private String tableName;

    @Column(name = "link_name")
    private String linkName;

    @Column(name = "link")
    private String link;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;
}
