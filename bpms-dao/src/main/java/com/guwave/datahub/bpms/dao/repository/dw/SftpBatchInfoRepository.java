package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.common.contant.BatchInfoStatus;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpBatchInfo;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputePool;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository("BpmsSftpBatchInfoRepository")
public interface SftpBatchInfoRepository extends CrudRepository<SftpBatchInfo, Long> {
    List<SftpBatchInfo> findAllByBatchStatusOrderByIdDesc(BatchInfoStatus batchStatus);

    @Transactional
    @Modifying
    @Query(value = "update dw_sftp_batch_info\n" +
            "set batch_status = 'FAIL'\n" +
            "where id in (select * from (select id from dw_sftp_batch_info where batch_status = 'PROCESSING') x)", nativeQuery = true)
    void updateProcessingToFail();

    @Query(value = "select collect_rule_id from dw_sftp_batch_info group by collect_rule_id having count(1) > 2000 order by collect_rule_id desc", nativeQuery = true)
    List<Long> findAllCollectRuleId();

    @Transactional
    @Modifying
    @Query(value = "delete from dw_sftp_batch_info where collect_rule_id = :collectRuleId and id < (select min(id) from (select id from dw_sftp_batch_info where collect_rule_id = :collectRuleId order by id desc limit 2000) x)", nativeQuery = true)
    void cleanForCollectRuleId(@Param("collectRuleId") Long collectRuleId);

    @Query(value = "SELECT *\n" +
            "FROM dw_sftp_batch_info\n" +
            "WHERE batch_status = 'SUCCESS'\n" +
            "  AND TIMESTAMPDIFF(SECOND, create_time, update_time) > 600\n" +
            "  and create_time >= :startTime\n" +
            "  and create_time <= :endTime\n" +
            "union all\n" +
            "SELECT *\n" +
            "FROM dw_sftp_batch_info\n" +
            "WHERE batch_status = 'PROCESSING'\n" +
            "  AND TIMESTAMPDIFF(SECOND, create_time, CURDATE()) > 600\n" +
            "  and create_time >= :startTime\n" +
            "  and create_time <= :endTime",
            nativeQuery = true)
    List<SftpBatchInfo> findSlowlyTask(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
