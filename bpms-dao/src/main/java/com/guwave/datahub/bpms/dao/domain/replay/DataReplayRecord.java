package com.guwave.datahub.bpms.dao.domain.replay;

import com.guwave.datahub.bpms.dao.converter.ListToJsonArrayIntegerConverter;
import com.guwave.datahub.bpms.dao.converter.ListToJsonArrayStringConverter;
import com.guwave.onedata.dataware.common.contant.CleanUpType;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.contant.StepType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.dao.mysql.converter.CleanUpTypeConverter;
import com.guwave.onedata.dataware.dao.mysql.converter.FileCategoryConverter;
import com.guwave.onedata.dataware.dao.mysql.converter.LotTypeConverter;
import com.guwave.onedata.dataware.dao.mysql.converter.ProcessStatusConverter;
import com.guwave.onedata.dataware.dao.mysql.converter.StepTypeConverter;
import com.guwave.onedata.dataware.dao.mysql.converter.TestAreaConverter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * 2024/3/12 15:40
 * DataReplayRecord
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "bz_data_replay_record")
public class DataReplayRecord implements Serializable {

    private static final long serialVersionUID = -463842765430431198L;

    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    /**
     * 客户名
     */
    @Column(name = "customer")
    private String customer;

    /**
     * CP/FT/WAT等 表示测试阶段大类型
     */
    @Column(name = "test_area")
    @Convert(converter = TestAreaConverter.class)
    private TestArea testArea;

    /**
     * 测试工厂
     */
    @Column(name = "factory")
    @Convert(converter = ListToJsonArrayStringConverter.class)
    private List<String> factory;

    /**
     * device id
     */
    @Column(name = "device_id")
    @Convert(converter = ListToJsonArrayStringConverter.class)
    private List<String> deviceId;

    /**
     * 文件类型，比如STDF、RAW_DATA等
     */
    @Column(name = "file_category")
    @Convert(converter = FileCategoryConverter.class)
    private FileCategory fileCategory;

    /**
     * lot id
     */
    @Column(name = "lot_id_list")
    @Convert(converter = ListToJsonArrayStringConverter.class)
    private List<String> lotIdList;

    /**
     * wafer no
     */
    @Column(name = "wafer_no_list")
    @Convert(converter = ListToJsonArrayStringConverter.class)
    private List<String> waferNoList;

    /**
     * sblot id，可能有多个
     */
    @Column(name="sblot_id_list")
    @Convert(converter = ListToJsonArrayStringConverter.class)
    private List<String> sblotIdList;

    /**
     * Lot类型，Engineering/Production等
     */
    @Column(name = "lot_type")
    @Convert(converter = LotTypeConverter.class)
    private LotType lotType;

    /**
     * test_stage值
     */
    @Column(name = "test_stage_list")
    @Convert(converter = ListToJsonArrayStringConverter.class)
    private List<String> testStageList;

    /**
     * test_program值
     */
    @Column(name = "test_program_list")
    @Convert(converter = ListToJsonArrayStringConverter.class)
    private List<String> testProgramList;

    /**
     * 文件名
     */
    @Column(name = "file_name")
    private String fileName;

    /**
     * 远程服务器的原始文件路径
     */
    @Column(name = "remote_file_path")
    private String remoteFilePath;

    /**
     * 删除/重跑，DELETE_FILE/DELETE_LOT/REPROCESS_FILE/REPROCESS_LOT
     */
    @Column(name = "replay_type")
    @Convert(converter = CleanUpTypeConverter.class)
    private CleanUpType replayType;

    /**
     * 删除步骤（1000），重跑步骤（2200->需要转换的文件文件合并；3100->文件预解析；4100->文件解析到ods；5100->dwd层计算）
     */
    @Column(name = "step")
    @Convert(converter = StepTypeConverter.class)
    private StepType step;

    /**
     * process_status，可能有多个
     */
    @Column(name="filter_process_status_list")
    @Convert(converter = ListToJsonArrayStringConverter.class)
    private List<String> filterProcessStatusList;

    /**
     * toppedTime，任务最后一次置顶时间
     */
    @Column(name="topped_time")
    private Date toppedTime;

    /**
     * exception_message
     */
    @Lob
    @Column(name="filter_exception_message")
    private String filterExceptionMessage;


    /**
     * exception_message
     */
    @Lob
    @Column(name="filter_file_warehousing_record_id")
    private String filterFileWarehousingRecordId;

    /**
     * exception_message
     */
    @Column(name="filter_step_list")
    @Convert(converter = ListToJsonArrayIntegerConverter.class)
    private List<Integer> filterStepList;

    /**
     * 重播的文件create_time开始时间
     */
    @Column(name="start_create_time")
    private String startCreateTime;

    /**
     * 重播的文件create_time结束时间
     */
    @Column(name="end_create_time")
    private String endCreateTime;

    /**
     * 重播的文件update_time开始时间
     */
    @Column(name="start_update_time")
    private String startUpdateTime;

    /**
     * 重播的文件update_time结束时间
     */
    @Column(name="end_update_time")
    private String endUpdateTime;

    /**
     * dw_file_warehousing_record id
     */
    @Lob
    @Column(name="file_warehousing_record_id")
    private String fileWarehousingRecordId;

    /**
     * 重播备注
     */
    @Column(name = "replay_comments")
    private String replayComments;

    /**
     * 删除/重跑文件个数
     */
    @Column(name = "replay_file_cnt")
    private Integer replayFileCnt;

    /**
     * 删除/重跑任务总数
     */
    @Column(name = "total_task_cnt")
    private Integer totalTaskCnt;

    /**
     * 删除/重跑任务执行成功数
     */
    @Column(name = "success_task_cnt")
    private Integer successTaskCnt;

    /**
     * 删除/重跑任务执行失败数
     */
    @Column(name = "fail_task_cnt")
    private Integer failTaskCnt;

    /**
     * 处理状态
     */
    @Column(name = "process_status")
    @Convert(converter = ProcessStatusConverter.class)
    private ProcessStatus processStatus;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;
}
