package com.guwave.datahub.bpms.dao.vo.bpms.dailyReport;

import javax.persistence.Convert;

public class ManualTaskResult {

    public Long successUploadTaskCnt;
    public Long successMergeTaskCnt;
    public Long successMoveProductionTaskCnt;

    public Long failUploadTaskCnt;
    public Long failMergeTaskCnt;
    public Long failMoveProductionTaskCnt;

    public Long getSuccessUploadTaskCnt() {
        return successUploadTaskCnt;
    }

    public void setSuccessUploadTaskCnt(Long successUploadTaskCnt) {
        this.successUploadTaskCnt = successUploadTaskCnt;
    }

    public Long getSuccessMergeTaskCnt() {
        return successMergeTaskCnt;
    }

    public void setSuccessMergeTaskCnt(Long successMergeTaskCnt) {
        this.successMergeTaskCnt = successMergeTaskCnt;
    }

    public Long getSuccessMoveProductionTaskCnt() {
        return successMoveProductionTaskCnt;
    }

    public void setSuccessMoveProductionTaskCnt(Long successMoveProductionTaskCnt) {
        this.successMoveProductionTaskCnt = successMoveProductionTaskCnt;
    }

    public Long getFailUploadTaskCnt() {
        return failUploadTaskCnt;
    }

    public void setFailUploadTaskCnt(Long failUploadTaskCnt) {
        this.failUploadTaskCnt = failUploadTaskCnt;
    }

    public Long getFailMergeTaskCnt() {
        return failMergeTaskCnt;
    }

    public void setFailMergeTaskCnt(Long failMergeTaskCnt) {
        this.failMergeTaskCnt = failMergeTaskCnt;
    }

    public Long getFailMoveProductionTaskCnt() {
        return failMoveProductionTaskCnt;
    }

    public void setFailMoveProductionTaskCnt(Long failMoveProductionTaskCnt) {
        this.failMoveProductionTaskCnt = failMoveProductionTaskCnt;
    }
}
