package com.guwave.datahub.bpms.dao.domain.repair;

import org.hibernate.annotations.Comment;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 2025/5/6 17:43
 * DataRepairBatchInfo
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "bz_data_repair_batch_info")
public class DataRepairBatchInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "customer")
    @Comment("客户")
    private String customer;

    @Column(name = "sub_customer")
    @Comment("子客户")
    private String subCustomer;

    @Column(name = "factory")
    @Comment("factory")
    private String factory;

    @Column(name = "factory_site")
    @Comment("factory_site")
    private String factorySite;

    @Column(name = "device_id")
    @Comment("device_id")
    private String deviceId;

    @Column(name = "lot_id")
    @Comment("lot_id")
    private String lotId;

    @Column(name = "test_stage")
    @Comment("test stage")
    private String testStage;

    @Column(name = "test_area")
    @Comment("测试阶段，CP/FT")
    private String testArea;

    @Column(name = "sblot_id")
    @Comment("sblot_id")
    private String sblotId;

    @Column(name = "wafer_id")
    @Comment("wafer_id")
    private String waferId;

    @Column(name = "delete_flag", nullable = false)
    @Comment("删除标志,0:未删除，1:已删除")
    private Boolean deleteFlag = false;

    @Column(name = "create_time")
    @Comment("创建时间")
    private Date createTime;

    @Column(name = "update_time")
    @Comment("更新时间")
    private Date updateTime;

    @Column(name = "create_user")
    @Comment("创建用户")
    private String createUser = "System";

    @Column(name = "update_user")
    @Comment("更新用户")
    private String updateUser = "System";

    public Long getId() {
        return id;
    }

    public DataRepairBatchInfo setId(Long id) {
        this.id = id;
        return this;
    }

    public String getCustomer() {
        return customer;
    }

    public DataRepairBatchInfo setCustomer(String customer) {
        this.customer = customer;
        return this;
    }

    public String getSubCustomer() {
        return subCustomer;
    }

    public DataRepairBatchInfo setSubCustomer(String subCustomer) {
        this.subCustomer = subCustomer;
        return this;
    }

    public String getFactory() {
        return factory;
    }

    public DataRepairBatchInfo setFactory(String factory) {
        this.factory = factory;
        return this;
    }

    public String getFactorySite() {
        return factorySite;
    }

    public DataRepairBatchInfo setFactorySite(String factorySite) {
        this.factorySite = factorySite;
        return this;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public DataRepairBatchInfo setDeviceId(String deviceId) {
        this.deviceId = deviceId;
        return this;
    }

    public String getLotId() {
        return lotId;
    }

    public DataRepairBatchInfo setLotId(String lotId) {
        this.lotId = lotId;
        return this;
    }

    public String getTestStage() {
        return testStage;
    }

    public DataRepairBatchInfo setTestStage(String testStage) {
        this.testStage = testStage;
        return this;
    }

    public String getTestArea() {
        return testArea;
    }

    public DataRepairBatchInfo setTestArea(String testArea) {
        this.testArea = testArea;
        return this;
    }

    public String getSblotId() {
        return sblotId;
    }

    public DataRepairBatchInfo setSblotId(String sblotId) {
        this.sblotId = sblotId;
        return this;
    }

    public String getWaferId() {
        return waferId;
    }

    public DataRepairBatchInfo setWaferId(String waferId) {
        this.waferId = waferId;
        return this;
    }

    public Boolean getDeleteFlag() {
        return deleteFlag;
    }

    public DataRepairBatchInfo setDeleteFlag(Boolean deleteFlag) {
        this.deleteFlag = deleteFlag;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public DataRepairBatchInfo setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public DataRepairBatchInfo setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public DataRepairBatchInfo setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public DataRepairBatchInfo setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }
}
