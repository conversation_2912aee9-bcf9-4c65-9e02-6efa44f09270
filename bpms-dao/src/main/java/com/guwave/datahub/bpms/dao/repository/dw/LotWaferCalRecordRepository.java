package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.TimelinessResult;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotWaferCalRecord;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository("BpmsLotWaferCalRecordRepository")
public interface LotWaferCalRecordRepository extends CrudRepository<LotWaferCalRecord, Long> {

    Long countByLatestFlagAndTestItemStatusAndCreateTimeBetween(Integer latestFlag, ProcessStatus processStatus, Date startTime, Date endTime);

    @Query(value = "SELECT a.create_time                                                                   AS createTime,\n" +
            "       a.test_area                                                                            AS testArea,\n" +
            "       a.factory                                                                              AS factory,\n" +
            "       a.device_id                                                                            AS deviceId,\n" +
            "       a.lot_id                                                                               AS lotId,\n" +
            "       a.wafer_no                                                                             AS waferNo,\n" +
            "       a.test_stage                                                                           AS testStage,\n" +
            "       a.lot_type                                                                             AS lotType,\n" +
            "       a.cal_cnt                                                                              as calCnt,\n" +
            "       a.repair_cnt                                                                           as repairCnt,\n" +
            "       a.replay_cnt                                                                           as replayCnt," +
            "       a.die_total_cnt                                                                        AS dieCount,\n" +
            "       a.test_item_total_cnt                                                                  AS testItemCount,\n" +
            "       b.end_time                                                                             AS maxFinishTime,\n" +
            "       min_ftp_mtime                                                                          AS minFtpMtime,\n" +
            "       max_ftp_mtime                                                                          AS maxFtpMtime,\n" +
            "       TIMESTAMPDIFF(SECOND, max_ftp_mtime,\n" +
            "                     SUBSTRING_INDEX(SUBSTRING_INDEX(file_download_times, ',', -1), '~', 1))  AS scanWaitDuration,\n" +
            "       SUBSTRING_INDEX(file_download_times, ',', -1)                                          AS lastFileDownloadStart,\n" +
            "       TIMESTAMPDIFF(SECOND, max_ftp_mtime,\n" +
            "                     SUBSTRING_INDEX(SUBSTRING_INDEX(file_download_times, ',', -1), '~',\n" +
            "                                     -1))                                                     AS uploadToDownloadDuration,\n" +
            "       file_convert_total_consume                                                             AS totalFileParseDuration,\n" +
            "       TIMESTAMPDIFF(SECOND,\n" +
            "                     SUBSTRING_INDEX(SUBSTRING_INDEX(file_download_times, ',', -1), '~', -1),\n" +
            "                     SUBSTRING_INDEX(SUBSTRING_INDEX(file_pre_parse_times, ',', -1), '~', 1)) AS downloadToPreparseGap,\n" +
            "       SUBSTRING_INDEX(file_pre_parse_times, ',', -1)                                         AS lastFilePreparseStart,\n" +
            "       file_pre_parse_total_consume                                                           AS totalPreparseDuration,\n" +
            "       TIMESTAMPDIFF(SECOND,\n" +
            "                     SUBSTRING_INDEX(SUBSTRING_INDEX(file_pre_parse_times, ',', -1), '~', -1),\n" +
            "                     SUBSTRING_INDEX(SUBSTRING_INDEX(file_parse_times, ',', -1), '~', 1))     AS preparseToParseGap,\n" +
            "       SUBSTRING_INDEX(file_parse_times, ',', -1)                                             AS lastFileParseStart,\n" +
            "       file_parse_total_consume                                                               AS totalParseDuration,\n" +
            "       TIMESTAMPDIFF(SECOND,\n" +
            "                     SUBSTRING_INDEX(SUBSTRING_INDEX(file_parse_times, ',', -1), '~', -1),\n" +
            "                     SUBSTRING_INDEX(die_cal_time, '~', 1))                                   AS parseToDieGap,\n" +
            "       die_cal_time                                                                           AS dieCalculationStart,\n" +
            "       die_cal_consume                                                                        AS dieCalculationDuration,\n" +
            "       TIMESTAMPDIFF(SECOND,\n" +
            "                     SUBSTRING_INDEX(die_cal_time, '~', -1),\n" +
            "                     SUBSTRING_INDEX(test_item_cal_time, '~', 1))                             AS dieToTestitemGap,\n" +
            "       test_item_cal_time                                                                     AS testitemCalculationStart,\n" +
            "       test_item_cal_consume                                                                  AS testitemCalculationDuration,\n" +
            "       TIMESTAMPDIFF(SECOND, max_ftp_mtime,\n" +
            "                     SUBSTRING_INDEX(die_cal_time, '~', -1))                                  AS dieTotalDuration,\n" +
            "       TIMESTAMPDIFF(SECOND,\n" +
            "                     SUBSTRING_INDEX(SUBSTRING_INDEX(file_download_times, ',', -1), '~', -1),\n" +
            "                     SUBSTRING_INDEX(SUBSTRING_INDEX(die_cal_time, ',', -1), '~', -1))        AS dieDurationWithoutDownload,\n" +
            "       TIMESTAMPDIFF(SECOND, max_ftp_mtime, final_end_time)                                   AS totalDuration,\n" +
            "       TIMESTAMPDIFF(SECOND,\n" +
            "                     SUBSTRING_INDEX(SUBSTRING_INDEX(file_download_times, ',', -1), '~', -1),\n" +
            "                     final_end_time)                                                          AS totalDurationWithoutDownload\n" +
            "FROM onedata.dw_lot_wafer_cal_record a\n" +
            "         JOIN (SELECT device_id,\n" +
            "                      lot_id,\n" +
            "                      wafer_no,\n" +
            "                      test_area,\n" +
            "                      test_stage,\n" +
            "                      lot_type,\n" +
            "                      FROM_UNIXTIME(MAX(CAST(finish_t AS SIGNED))) AS end_time\n" +
            "               FROM onedata.dw_lot_stocking_detail\n" +
            "               GROUP BY device_id, lot_id, wafer_no, test_area, test_stage, lot_type) b ON\n" +
            "            a.test_area = b.test_area AND\n" +
            "            a.device_id = b.device_id AND\n" +
            "            a.lot_id = b.lot_id AND\n" +
            "            a.wafer_no = b.wafer_no AND\n" +
            "            a.test_stage = b.test_stage AND\n" +
            "            a.lot_type = b.lot_type AND\n" +
            "            a.test_item_status = 'SUCCESS' AND\n" +
            "            a.latest_flag = 1 AND\n" +
            "            a.create_time >= :startTime AND\n" +
            "            a.create_time <= :endTime\n" +
            "ORDER BY totalDuration DESC;",
    nativeQuery = true)
    List<Object[]> calculateTimeliness(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
