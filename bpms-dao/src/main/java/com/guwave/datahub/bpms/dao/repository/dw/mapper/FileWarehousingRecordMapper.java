package com.guwave.datahub.bpms.dao.repository.dw.mapper;

import com.guwave.datahub.bpms.dao.vo.dw.WaferVo;
import com.guwave.datahub.bpms.dao.vo.bpms.dataIntegrity.DataIntegrityBase;
import com.guwave.datahub.bpms.dao.vo.bpms.dataIntegrity.DataIntegrityDropDown;
import com.guwave.datahub.bpms.dao.vo.bpms.dataIntegrity.DataIntegrityRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface FileWarehousingRecordMapper {

    List<WaferVo> find7100SuccessFileWarehousingByFilters(
            @Param("customer") String customer,
            @Param("subCustomerList") List<String> subCustomerList,
            @Param("factoryList") List<String> factoryList,
            @Param("testAreaList") List<String> testAreaList,
            @Param("deviceIdList") List<String> deviceIdList,
            @Param("testStageList") List<String> testStageList,
            @Param("lotIdList") List<String> lotIdList,
            @Param("waferNoList") List<String> waferNoList,
            @Param("sblotIdList") List<String> sblotIdList,
            @Param("lotTypeList") List<String> lotTypeList,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime,
            @Param("allSupportFileCategory") List<String> allSupportFileCategory
    );

    List<DataIntegrityDropDown> queryDataIntegrityDropDown(
            @Param("customer") String customer,
            @Param("testAreas") List<String> testAreas,
            @Param("fileCategorys") List<String> fileCategorys,
            @Param("remoteFileMtimeStart") String remoteFileMtimeStart,
            @Param("remoteFileMtimeEnd") String remoteFileMtimeEnd,
            @Param("subCustomers") List<String> subCustomers,
            @Param("factorys") List<String> factorys,
            @Param("deviceIds") List<String> deviceIds,
            @Param("testStages") List<String> testStages,
            @Param("lotTypes") List<String> lotTypes,
            @Param("lotIds") List<String> lotIds,
            @Param("sblotIds") List<String> sblotIds,
            @Param("waferIds") List<String> waferIds,
            @Param("dataLoadeds") List<String> dataLoadeds,
            @Param("failedTypes") List<String> failedTypes,
            @Param("testPrograms") List<String> testPrograms,
            @Param("ftpPath") String ftpPath,
            @Param("fileName") String fileName,
            @Param("filterField") String filterField,
            @Param("fileNames") List<String> fileNames);

    DataIntegrityBase queryDataIntegrityBase(
            @Param("customer") String customer,
            @Param("testAreas") List<String> testAreas,
            @Param("fileCategorys") List<String> fileCategorys,
            @Param("remoteFileMtimeStart") String remoteFileMtimeStart,
            @Param("remoteFileMtimeEnd") String remoteFileMtimeEnd,
            @Param("subCustomers") List<String> subCustomers,
            @Param("factorys") List<String> factorys,
            @Param("deviceIds") List<String> deviceIds,
            @Param("testStages") List<String> testStages,
            @Param("lotTypes") List<String> lotTypes,
            @Param("lotIds") List<String> lotIds,
            @Param("sblotIds") List<String> sblotIds,
            @Param("waferIds") List<String> waferIds,
            @Param("dataLoadeds") List<String> dataLoadeds,
            @Param("failedTypes") List<String> failedTypes,
            @Param("testPrograms") List<String> testPrograms,
            @Param("ftpPath") String ftpPath,
            @Param("fileName") String fileName,
            @Param("fileNames") List<String> fileNames
    );

    List<DataIntegrityRecord> queryDataIntegrityRecord(
            @Param("customer") String customer,
            @Param("testAreas") List<String> testAreas,
            @Param("fileCategorys") List<String> fileCategorys,
            @Param("remoteFileMtimeStart") String remoteFileMtimeStart,
            @Param("remoteFileMtimeEnd") String remoteFileMtimeEnd,
            @Param("subCustomers") List<String> subCustomers,
            @Param("factorys") List<String> factorys,
            @Param("deviceIds") List<String> deviceIds,
            @Param("testStages") List<String> testStages,
            @Param("lotTypes") List<String> lotTypes,
            @Param("lotIds") List<String> lotIds,
            @Param("sblotIds") List<String> sblotIds,
            @Param("waferIds") List<String> waferIds,
            @Param("dataLoadeds") List<String> dataLoadeds,
            @Param("failedTypes") List<String> failedTypes,
            @Param("testPrograms") List<String> testPrograms,
            @Param("ftpPath") String ftpPath,
            @Param("fileName") String fileName,
            @Param("orderByField") String orderByField,
            @Param("orderByType") String orderByType,
            @Param("offset") Long offset,
            @Param("size") Long size,
            @Param("fileNames") List<String> fileNames
    );

    Long countDataIntegrityRecord(
            @Param("customer") String customer,
            @Param("testAreas") List<String> testAreas,
            @Param("fileCategorys") List<String> fileCategorys,
            @Param("remoteFileMtimeStart") String remoteFileMtimeStart,
            @Param("remoteFileMtimeEnd") String remoteFileMtimeEnd,
            @Param("subCustomers") List<String> subCustomers,
            @Param("factorys") List<String> factorys,
            @Param("deviceIds") List<String> deviceIds,
            @Param("testStages") List<String> testStages,
            @Param("lotTypes") List<String> lotTypes,
            @Param("lotIds") List<String> lotIds,
            @Param("sblotIds") List<String> sblotIds,
            @Param("waferIds") List<String> waferIds,
            @Param("dataLoadeds") List<String> dataLoadeds,
            @Param("failedTypes") List<String> failedTypes,
            @Param("testPrograms") List<String> testPrograms,
            @Param("ftpPath") String ftpPath,
            @Param("fileName") String fileName,
            @Param("fileNames") List<String> fileNames
    );
}
