package com.guwave.datahub.bpms.dao.converter;

import com.guwave.datahub.bpms.common.constant.ExecuteEngine;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class ExecuteEngineConverter implements AttributeConverter<com.guwave.datahub.bpms.common.constant.ExecuteEngine, String> {

    @Override
    public String convertToDatabaseColumn(com.guwave.datahub.bpms.common.constant.ExecuteEngine engine) {
        return null == engine ? null : engine.name();
    }

    @Override
    public com.guwave.datahub.bpms.common.constant.ExecuteEngine convertToEntityAttribute(String engine) {
        return ExecuteEngine.of(engine);
    }
}