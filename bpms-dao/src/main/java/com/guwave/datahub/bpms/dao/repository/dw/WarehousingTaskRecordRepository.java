package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.datahub.bpms.dao.vo.bpms.dataFreeze.DataFreezeRecord;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.WarehousingTaskRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/10
 * @description WarehousingTaskRecordRepository
 */
@Repository("BpmsWarehousingTaskRecordRepository")
public interface WarehousingTaskRecordRepository extends JpaRepository<WarehousingTaskRecord, Long> {

    /**
     * dw下拉框查询
     *
     * @param filterField   查询字段
     * @param testArea      testArea
     * @param factory       factory
     * @param deviceId      deviceId
     * @param testStage     testStage
     * @param lotId         lotId
     * @param waferNo       waferNo
     * @param lotType       lotType
     * @param dwLayer       dwLayer
     * @param processStatus processStatus
     * @return 查询字段的所有记录
     */
    @Query(value = "select case " +
            "           when LOWER(?1) = LOWER('test_area') then test_area " +
            "           when LOWER(?1) = LOWER('factory') then factory " +
            "           when LOWER(?1) = LOWER('device_id') then device_id " +
            "           when LOWER(?1) = LOWER('test_stage') then test_stage " +
            "           when LOWER(?1) = LOWER('lot_id') then lot_id " +
            "           when LOWER(?1) = LOWER('wafer_no') then wafer_no " +
            "           when LOWER(?1) = LOWER('lot_type') then lot_type " +
            "           end as result " +
            "from dw_warehousing_task_record " +
            "      where (coalesce(?2,null) IS NULL OR test_area in (?2))  " +
            "        and (coalesce(?3,null) IS NULL OR factory in (?3)) " +
            "        and (coalesce(?4,null) IS NULL OR device_id in (?4)) " +
            "        and (coalesce(?5,null) IS NULL OR test_stage in (?5)) " +
            "        and (coalesce(?6,null) IS NULL OR lot_id in (?6)) " +
            "        and (coalesce(?7,null) IS NULL OR wafer_no in (?7)) " +
            "        and (coalesce(?8,null) IS NULL OR lot_type in (?8)) " +
            "        and (coalesce(?9,null) IS NULL OR dw_layer in (?9)) " +
            "        and (coalesce(?10,null) IS NULL OR process_status in (?10)) " +
            "        and customer=?11 " +
            "group by result " +
            "order by result ",
            nativeQuery = true)
    List<String> findDynamicField(
            @Param("filterField") String filterField,
            @Param("testArea") List<String> testArea,
            @Param("factory") List<String> factory,
            @Param("deviceId") List<String> deviceId,
            @Param("testStage") List<String> testStage,
            @Param("lotId") List<String> lotId,
            @Param("waferNo") List<String> waferNo,
            @Param("lotType") List<String> lotType,
            @Param("dwLayer") List<String> dwLayer,
            @Param("processStatus") List<String> processStatus,
            @Param("customer") String customer
    );


    @Query(value = "select *" +
            "      from dw_warehousing_task_record " +
            "      where (coalesce(?1, null) is null or test_area in (?1)) " +
            "        and (coalesce(?2, null) is null or factory in (?2)) " +
            "        and (coalesce(?3, null) is null or device_id in (?3)) " +
            "        and (coalesce(?4, null) is null or test_stage in (?4)) " +
            "        and (coalesce(?5, null) is null or lot_id in (?5)) " +
            "        and (coalesce(?6, null) is null or wafer_no in (?6)) " +
            "        and (coalesce(?7, null) is null or lot_type in (?7)) " +
            "        and (coalesce(?8, null) is null or dw_layer in (?8)) " +
            "        and (coalesce(?9, null) is null or process_status in (?9)) " +
            "        and (coalesce(?10, null) is null or file_category in (?10)) " +
            "        and customer=?11 " +
            "        order by field(process_status, 'FAIL', 'RELOADING', 'RELOAD_FAIL', 'PROCESSING', 'CREATE', 'SUCCESS', 'RELOAD_SUCCESS', 'FREEZE'), update_time desc ",
            nativeQuery = true)
    Page<WarehousingTaskRecord> findDwTasksByFilters(
            @Param("testArea") List<String> testArea,
            @Param("factory") List<String> factory,
            @Param("deviceId") List<String> deviceId,
            @Param("testStage") List<String> testStage,
            @Param("lotId") List<String> lotId,
            @Param("waferNo") List<String> waferNo,
            @Param("lotType") List<String> lotType,
            @Param("dwLayer") List<String> dwLayer,
            @Param("processStatus") List<String> processStatus,
            @Param("fileCategory") List<String> fileCategory,
            @Param("customer") String customer,
            Pageable pageable
    );

    @Transactional
    @Modifying
    @Query("update WarehousingTaskRecord a set a.processStatus = 'FREEZE',a.updateTime = now() where a.id in :ids")
    void freezeByids(@Param("ids") List ids);

    @Transactional
    @Modifying
    @Query("update WarehousingTaskRecord a set a.processStatus = :newProcessStatus,a.updateTime = now(),a.exceptionType = :exceptionType,a.errorMessage = :errorMessage  where a.id in :ids and a.processStatus = :oldProcessStatus")
    int updateProcessStatus(@Param("ids") List<Long> ids, @Param("exceptionType") ExceptionType exceptionType, @Param("errorMessage") String errorMessage, @Param("newProcessStatus") ProcessStatus newProcessStatus, @Param("oldProcessStatus") ProcessStatus oldProcessStatus);

    List<WarehousingTaskRecord> findAllByCustomerAndSubCustomerAndFactoryAndFactorySiteAndTestAreaAndLotTypeAndDeviceIdAndLotIdAndWaferNoAndTestStageAndFileCategoryAndProcessStatus(String customer, String subCustomer, String factory, String factorySite, TestArea testArea, LotType lotType, String deviceId, String lotId, String waferNo, String testStage, FileCategory fileCategory, ProcessStatus processStatus);
}
