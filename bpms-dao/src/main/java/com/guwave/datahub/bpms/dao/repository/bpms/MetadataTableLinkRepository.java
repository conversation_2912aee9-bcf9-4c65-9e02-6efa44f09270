package com.guwave.datahub.bpms.dao.repository.bpms;

import com.guwave.datahub.bpms.dao.domain.metadata.MetadataSchema;
import com.guwave.datahub.bpms.dao.domain.metadata.MetadataTableDescription;
import com.guwave.datahub.bpms.dao.domain.metadata.MetadataTableLink;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * MetadataTableLinkRepository
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-03-13 17:02:05
 */
@Repository
public interface MetadataTableLinkRepository extends JpaRepository<MetadataTableLink, Long> {

    @Query("select t from MetadataTableLink t where t.datasource=:source and t.databaseName=:database and t.tableName=:table")
    List<MetadataTableLink> findByTable(@Param("source") String source, @Param("database") String database, @Param("table") String table);

}
