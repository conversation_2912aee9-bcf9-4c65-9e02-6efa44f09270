package com.guwave.datahub.bpms.dao.repository.bpms;

import com.guwave.datahub.bpms.dao.domain.repair.DataIntegrityDeleteLog;
import com.guwave.datahub.bpms.dao.vo.bpms.dataIntegrity.DataIntegrityDeleteLogDropDown;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository("BpmsDataIntegrityDeleteLogRepository")
public interface DataIntegrityDeleteLogRepository extends CrudRepository<DataIntegrityDeleteLog, Long> {



    @Query(value = "select res showName,\n" +
            "       res name\n" +
            "from (select case :filterField\n" +
            "                 when 'DELETE_BY' then create_user\n" +
            "                 else ''\n" +
            "                 end res\n" +
            "      from bz_data_integrity_delete_log\n" +
            "      where customer = :customer\n" +
            "        and delete_flag = 0\n" +
            "        and (coalesce(:subCustomers) is null or sub_customer in (:subCustomers))\n" +
            "        and (:fileName is null or file_name like concat('%', :fileName, '%'))\n" +
            "        and (coalesce(:createUsers) is null or create_user in (:createUsers))\n" +
            "      group by res) x\n" +
            "where res is not null\n" +
            "  and res != ''\n" +
            "order by showName",
            nativeQuery = true)
    List<DataIntegrityDeleteLogDropDown> queryDropDown(
            @Param("customer") String customer,
            @Param("subCustomers") List<String> subCustomers,
            @Param("fileName") String fileName,
            @Param("createUsers") List<String> createUsers,
            @Param("filterField") String filterField
    );

    @Query(value = "select *\n" +
            "from bz_data_integrity_delete_log\n" +
            "where customer = :customer\n" +
            "  and delete_flag = 0\n" +
            "  and (coalesce(:subCustomers) is null or sub_customer in (:subCustomers))\n" +
            "  and (:fileName is null or file_name like concat('%', :fileName, '%'))\n" +
            "  and (coalesce(:createUsers) is null or create_user in (:createUsers))\n" +
            "order by update_time desc",
            countQuery = "select count(1)\n" +
                    "from bz_data_integrity_delete_log\n" +
                    "where customer = :customer\n" +
                    "  and delete_flag = 0\n" +
                    "  and (coalesce(:subCustomers) is null or sub_customer in (:subCustomers))\n" +
                    "  and (:fileName is null or file_name like concat('%', :fileName, '%'))\n" +
                    "  and (coalesce(:createUsers) is null or create_user in (:createUsers))",
            nativeQuery = true)
    Page<DataIntegrityDeleteLog> queryDataIntegrityDeleteLog(
            @Param("customer") String customer,
            @Param("subCustomers") List<String> subCustomers,
            @Param("fileName") String fileName,
            @Param("createUsers") List<String> createUsers,
            Pageable pageable
    );
}
