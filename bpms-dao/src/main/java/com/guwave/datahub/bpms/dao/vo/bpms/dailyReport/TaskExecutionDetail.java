package com.guwave.datahub.bpms.dao.vo.bpms.dailyReport;

import java.util.Date;

public class TaskExecutionDetail {
    private String name;
    private Long executeTime;

    public TaskExecutionDetail() {
    }

    public TaskExecutionDetail(String name, Long executeTime) {
        this.name = name;
        this.executeTime = executeTime;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(Long executeTime) {
        this.executeTime = executeTime;
    }

}
