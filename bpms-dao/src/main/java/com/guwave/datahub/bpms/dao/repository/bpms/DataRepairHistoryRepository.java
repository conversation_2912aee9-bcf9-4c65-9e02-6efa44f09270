package com.guwave.datahub.bpms.dao.repository.bpms;

import com.guwave.datahub.bpms.dao.domain.repair.DataRepairHistory;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * 2025/3/5 14:50
 * DataRepairHistoryRepository
 *
 * <AUTHOR>
 */
@Repository("DataRepairHistoryRepository")
public interface DataRepairHistoryRepository extends CrudRepository<DataRepairHistory, Long> {

    List<DataRepairHistory> findAllByFileIdInAndDeleteFlag(Collection<Long> fileId, Boolean deleteFlag);

    List<DataRepairHistory> findAllByRepairBatchIdAndDeleteFlag(Long repairBatchId, Boolean deleteFlag);

    List<DataRepairHistory> findAllByIdIn(List<Long> ids);

    List<DataRepairHistory> findAllByFileIdOrderByUpdateTimeDesc(Long fileId);
}
