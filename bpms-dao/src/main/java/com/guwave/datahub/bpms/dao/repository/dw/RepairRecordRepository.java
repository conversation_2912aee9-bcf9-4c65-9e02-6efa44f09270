package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.RepairDetail;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.RepairRecord;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository("BpmsRepairRecordRepository")
public interface RepairRecordRepository extends CrudRepository<RepairRecord, Long> {


    List<RepairRecord> findAllByCreateTimeBetween(Date startTime, Date endTime);

    @Query(value = "SELECT\n" +
            "    DATE_FORMAT(update_time, '%Y%m%d') AS day,\n" +
            "    COUNT(*) AS totalCnt,\n" +
            "    SUM(process_status = 'SUCCESS') AS successCnt\n" +
            "FROM dw_repair_record\n" +
            "WHERE update_time >= :startTime\n" +
            "  AND update_time <= :endTime\n" +
            "GROUP BY day\n" +
            "ORDER BY day;",
    nativeQuery = true)
    List<Object[]> findFileDetailByDay(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
