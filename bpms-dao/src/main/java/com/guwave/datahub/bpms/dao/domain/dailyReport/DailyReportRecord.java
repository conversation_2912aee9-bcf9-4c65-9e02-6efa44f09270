package com.guwave.datahub.bpms.dao.domain.dailyReport;

import com.guwave.datahub.bpms.common.constant.ExceptionType;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.dao.mysql.converter.ProcessStatusConverter;

import javax.persistence.*;

import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "bz_daily_report_record")
public class DailyReportRecord {

    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = IDENTITY)
    private Long id;

    @Column(name = "day")
    private String day;

    @Column(name = "process_status")
    @Convert(converter = ProcessStatusConverter.class)
    private ProcessStatus processStatus;

    @Column(name = "start_time")
    private Date startTime;

    @Column(name = "end_time")
    private Date endTime;

    @Column(name = "error_message", columnDefinition = "LONGTEXT")
    private String errorMessage;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "update_user")
    private String updateUser;

    public Long getId() {
        return id;
    }

    public DailyReportRecord setId(Long id) {
        this.id = id;
        return this;
    }

    public String getDay() {
        return day;
    }

    public DailyReportRecord setDay(String day) {
        this.day = day;
        return this;
    }

    public ProcessStatus getProcessStatus() {
        return processStatus;
    }

    public DailyReportRecord setProcessStatus(ProcessStatus processStatus) {
        this.processStatus = processStatus;
        return this;
    }

    public Date getStartTime() {
        return startTime;
    }

    public DailyReportRecord setStartTime(Date startTime) {
        this.startTime = startTime;
        return this;
    }

    public Date getEndTime() {
        return endTime;
    }

    public DailyReportRecord setEndTime(Date endTime) {
        this.endTime = endTime;
        return this;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public DailyReportRecord setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public DailyReportRecord setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public DailyReportRecord setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public DailyReportRecord setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public DailyReportRecord setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }
}
