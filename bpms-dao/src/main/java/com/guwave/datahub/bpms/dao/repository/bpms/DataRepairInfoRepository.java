package com.guwave.datahub.bpms.dao.repository.bpms;

import com.guwave.datahub.bpms.dao.domain.repair.DataRepairInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 2025/3/5 14:50
 * CustomRepository
 *
 * <AUTHOR>
 */
@Repository("DataRepairInfoRepository")
public interface DataRepairInfoRepository extends CrudRepository<DataRepairInfo, Long> {

    List<DataRepairInfo> findAllByFileIdInAndDeleteFlag(Collection<Long> fileId, Boolean deleteFlag);

    boolean existsByFileIdInAndDeleteFlag(Collection<Long> fileId, Boolean deleteFlag);

    Optional<DataRepairInfo> findByFileIdAndDeleteFlag(Long fileId, Boolean deleteFlag);

    List<DataRepairInfo> findAllByRepairBatchIdAndDeleteFlag(Long repairBatchId, Boolean deleteFlag);

    @Query(nativeQuery = true,
            value = "SELECT DISTINCT result " +
                    "FROM ( " +
                    "    SELECT DISTINCT CASE :filterField " +
                    "        WHEN 'subCustomer' THEN b.sub_customer " +
                    "        WHEN 'deviceId' THEN b.modify_device_id " +
                    "        WHEN 'testStage' THEN b.modify_test_stage " +
                    "        WHEN 'lotId' THEN b.modify_lot_id " +
                    "        WHEN 'fileName' THEN b.file_name " +
                    "        WHEN 'repairStatus' THEN b.repair_status " +
                    "        WHEN 'repairCategory' THEN b.repair_category " +
                    "        WHEN 'lastRepairType' THEN b.last_repair_type " +
                    "        WHEN 'repairTimes' THEN CAST(b.repair_times AS CHAR) " +
                    "    END AS result " +
                    "    FROM bz_data_repair_info b " +
                    "    WHERE b.delete_flag = 0 " +
                    "      AND b.customer = :customer " +
                    "      AND (coalesce(:subCustomerList, null) IS NULL OR sub_customer IN (:subCustomerList)) " +
                    "      AND (coalesce(:deviceIdList, null) IS NULL OR modify_device_id IN (:deviceIdList)) " +
                    "      AND (coalesce(:testStageList, null) IS NULL OR modify_test_stage IN (:testStageList)) " +
                    "      AND (coalesce(:lotIdList, null) IS NULL OR modify_lot_id IN (:lotIdList)) " +
                    "      AND (coalesce(:fileNameList, null) IS NULL OR file_name IN (:fileNameList)) " +
                    "      AND (coalesce(:repairStatusList, null) IS NULL OR repair_status IN (:repairStatusList)) " +
                    "      AND (coalesce(:repairCategoryList, null) IS NULL OR repair_category IN (:repairCategoryList)) " +
                    "      AND (coalesce(:lastRepairTypeList, null) IS NULL OR last_repair_type IN (:lastRepairTypeList)) " +
                    "      AND (coalesce(:repairTimesList, null) IS NULL OR repair_times IN (:repairTimesList)) " +
                    ") a " +
                    "ORDER BY result")
    List<String> getFileListDropdown(
            @Param("customer") String customer,
            @Param("filterField") String filterField,
            @Param("subCustomerList") List<String> subCustomerList,
            @Param("deviceIdList") List<String> deviceIdList,
            @Param("testStageList") List<String> testStageList,
            @Param("lotIdList") List<String> lotIdList,
            @Param("fileNameList") List<String> fileNameList,
            @Param("repairStatusList") List<String> repairStatusList,
            @Param("repairCategoryList") List<String> repairCategoryList,
            @Param("lastRepairTypeList") List<String> lastRepairTypeList,
            @Param("repairTimesList") List<Integer> repairTimesList);

    @Query(nativeQuery = true,
            value = "SELECT * FROM bz_data_repair_info b " +
                    "WHERE b.delete_flag = 0 " +
                    "  AND b.customer = :customer " +
                    "  AND (coalesce(:subCustomerList, null) IS NULL OR sub_customer IN (:subCustomerList)) " +
                    "  AND (coalesce(:deviceIdList, null) IS NULL OR modify_device_id IN (:deviceIdList)) " +
                    "  AND (coalesce(:testStageList, null) IS NULL OR modify_test_stage IN (:testStageList)) " +
                    "  AND (coalesce(:lotIdList, null) IS NULL OR modify_lot_id IN (:lotIdList)) " +
                    "  AND (coalesce(:fileNameList, null) IS NULL OR file_name IN (:fileNameList)) " +
                    "  AND (coalesce(:repairStatusList, null) IS NULL OR repair_status IN (:repairStatusList)) " +
                    "  AND (coalesce(:repairCategoryList, null) IS NULL OR repair_category IN (:repairCategoryList)) " +
                    "  AND (coalesce(:lastRepairTypeList, null) IS NULL OR last_repair_type IN (:lastRepairTypeList)) " +
                    "  AND (coalesce(:repairTimesList, null) IS NULL OR repair_times IN (:repairTimesList)) " +
                    "ORDER BY b.update_time DESC",
            countQuery = "SELECT COUNT(*) FROM bz_data_repair_info b " +
                         "WHERE b.delete_flag = 0 " +
                         "  AND b.customer = :customer " +
                         "  AND (coalesce(:subCustomerList, null) IS NULL OR sub_customer IN (:subCustomerList)) " +
                         "  AND (coalesce(:deviceIdList, null) IS NULL OR modify_device_id IN (:deviceIdList)) " +
                         "  AND (coalesce(:testStageList, null) IS NULL OR modify_test_stage IN (:testStageList)) " +
                         "  AND (coalesce(:lotIdList, null) IS NULL OR modify_lot_id IN (:lotIdList)) " +
                         "  AND (coalesce(:fileNameList, null) IS NULL OR file_name IN (:fileNameList)) " +
                         "  AND (coalesce(:repairStatusList, null) IS NULL OR repair_status IN (:repairStatusList)) " +
                         "  AND (coalesce(:repairCategoryList, null) IS NULL OR repair_category IN (:repairCategoryList)) " +
                         "  AND (coalesce(:lastRepairTypeList, null) IS NULL OR last_repair_type IN (:lastRepairTypeList)) " +
                         "  AND (coalesce(:repairTimesList, null) IS NULL OR repair_times IN (:repairTimesList))")
    Page<DataRepairInfo> findPagedList(
            @Param("customer") String customer,
            @Param("subCustomerList") List<String> subCustomerList,
            @Param("deviceIdList") List<String> deviceIdList,
            @Param("testStageList") List<String> testStageList,
            @Param("lotIdList") List<String> lotIdList,
            @Param("fileNameList") List<String> fileNameList,
            @Param("repairStatusList") List<String> repairStatusList,
            @Param("repairCategoryList") List<String> repairCategoryList,
            @Param("lastRepairTypeList") List<String> lastRepairTypeList,
            @Param("repairTimesList") List<Integer> repairTimesList,
            Pageable pageable);
}
