package com.guwave.datahub.bpms.dao.aspect;


import com.guwave.datahub.bpms.dao.domain.ModuleScheduledControlSwitch;
import com.guwave.datahub.bpms.dao.repository.bpms.BpmsModuleScheduledControlSwitchRepository;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Aspect
@Component("BpmsScheduleSwitchAspect")
@Order(1)
public class ScheduleSwitchAspect {
    private static final Logger LOGGER = LoggerFactory.getLogger(ScheduleSwitchAspect.class);

    @Value("${spring.module.name}")
    private String moduleName;

    @Autowired
    private BpmsModuleScheduledControlSwitchRepository bpmsModuleScheduledControlSwitchRepository;

    @Pointcut("@annotation(com.guwave.onedata.dataware.common.annotation.ScheduleSwitch)")
    public void scheduleSwitch() {

    }

    @Around("scheduleSwitch()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        List<ModuleScheduledControlSwitch> allByModule = bpmsModuleScheduledControlSwitchRepository.findAllByModuleOrderByModule(moduleName);
        if (allByModule.stream().anyMatch(t -> Objects.equals(1, t.getStopFlag()))) {
            LOGGER.info("{} 未开启定时任务！", moduleName);
            long sleepTime = BigDecimal.valueOf(30 + Math.random() * 30).longValue();
            try {
                Thread.sleep(sleepTime * 1000L);
            } catch (InterruptedException e) {
                LOGGER.info("线程停止");
            }
            return null;
        } else {
            return joinPoint.proceed();
        }
    }

}
