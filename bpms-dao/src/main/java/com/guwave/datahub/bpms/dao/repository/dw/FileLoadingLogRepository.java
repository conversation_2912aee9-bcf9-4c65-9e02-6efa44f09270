package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.dao.mysql.domain.dw.FileLoadingLog;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("BpmsFileLoadingLogRepository")
public interface FileLoadingLogRepository extends CrudRepository<FileLoadingLog, Long> {

    @Query(value = "select exception_type, file_category\n" +
            "from dw_file_loading_log\n" +
            "where step = 7100\n" +
            "  and process_status = :processStatus\n" +
            "  and step_end_time >= :startTime\n" +
            "  and step_end_time <= :endTime",
            nativeQuery = true)
    List<Object[]> findFailFileDetail(@Param("processStatus") String processStatus, @Param("startTime") String startTime, @Param("endTime") String endTime);

    @Query(value = "select COUNT(1)                                                                    as successFileCnt,\n" +
            "       CAST(COALESCE(SUM(origin_file_size), 0)                                                AS SIGNED)/1024/1024/1024 as successFileSize,\n" +
            "       CAST(COALESCE(SUM(file_category = 'BIT_MEM'), 0)                                AS SIGNED) as successBitMemFileCnt,\n" +
            "       CAST(COALESCE(SUM(IF(file_category = 'BIT_MEM', COALESCE(origin_file_size, 0), 0)), 0) AS SIGNED)/1024/1024/1024 as successBitMemFileSize,\n" +
            "       CAST(COALESCE(SUM(file_category = 'SUMMARY'), 0)                                AS SIGNED) as successSummaryFileCnt,\n" +
            "       CAST(COALESCE(SUM(IF(file_category = 'SUMMARY', COALESCE(origin_file_size, 0), 0)), 0) AS SIGNED)/1024/1024/1024 as successSummaryFileSize,\n" +
            "       CAST(COALESCE(SUM(file_category = 'RAW_DATA'), 0)                               AS SIGNED) as successRawDataFileCnt,\n" +
            "       CAST(COALESCE(SUM(IF(file_category = 'RAW_DATA', COALESCE(origin_file_size, 0), 0)), 0)AS SIGNED)/1024/1024/1024 as successRawDataFileSize,\n" +
            "       CAST(COALESCE(SUM(file_category = 'STDF'), 0)                                   AS SIGNED) as successStdfFileCnt,\n" +
            "       CAST(COALESCE(SUM(IF(file_category = 'STDF', COALESCE(origin_file_size, 0), 0)), 0)    AS SIGNED)/1024/1024/1024 as successStdfFileSize\n" +
            "from onedata.dw_file_loading_log\n" +
            "where step = 7100\n" +
            "  and process_status = :processStatus\n" +
            "  and step_end_time >= :startTime\n" +
            "  and step_end_time <= :endTime",
            nativeQuery = true)
    List<Object[]> findSuccessFileResult(@Param("processStatus") String processStatus, @Param("startTime") String startTime, @Param("endTime") String endTime);


    @Query(value = "select * from dw_file_loading_log " +
            "      where file_name in (" +
            "      select file_name from dw_file_loading_log " +
            "        where (customer = :customer)  " +
            "          and (coalesce(:subCustomer,null) IS NULL OR (('(null)' in (:subCustomer) AND sub_customer IS NULL) OR sub_customer in (:subCustomer)))" +
            "          and (coalesce(:factory,null) IS NULL OR (('(null)' in (:factory) AND factory IS NULL) OR factory in (:factory))) " +
            "          and (coalesce(:deviceId,null) IS NULL OR (('(null)' in (:deviceId) AND device_id IS NULL) OR device_id in (:deviceId))) " +
            "          and (coalesce(:testArea,null) IS NULL OR (('(null)' in (:testArea) AND test_area IS NULL) OR test_area in (:testArea))) " +
            "          and (coalesce(:testProgram,null) IS NULL OR (('(null)' in (:testProgram) AND test_program IS NULL) OR test_program in (:testProgram))) " +
            "          and (coalesce(:testStage,null) IS NULL OR (('(null)' in (:testStage) AND test_stage IS NULL) OR test_stage in (:testStage))) " +
            "          and (coalesce(:lotId,null) IS NULL OR (('(null)' in (:lotId) AND lot_id IS NULL) OR lot_id in (:lotId))) " +
            "          and (coalesce(:sblotId,null) IS NULL OR (('(null)' in (:sblotId) AND sblot_id IS NULL) OR sblot_id in (:sblotId))) " +
            "          and (coalesce(:waferId,null) IS NULL OR (('(null)' in (:waferId) AND wafer_id IS NULL) OR wafer_id in (:waferId))) " +
            "          and (ifnull(:startTime,'bpms_mock_null') = 'bpms_mock_null' OR step_end_time >= :startTime) " +
            "          and (ifnull(:endTime,'bpms_mock_null') = 'bpms_mock_null' OR step_end_time <= :endTime)" +
            "          and (coalesce(:allSupportTestArea,null) IS NULL OR (('(null)' in (:allSupportTestArea) AND test_area IS NULL) OR test_area in (:allSupportTestArea))) " +
            "          and convert_flag = 0 " +
            "          and step = 7100 " +
            "          and process_status = 'SUCCESS'" +
            "          and file_category in ('STDF', 'RAW_DATA') " +
            ")",
            countQuery = "select count(1) from dw_file_loading_log " +
                    "      where file_name in (" +
                    "      select file_name from dw_file_loading_log " +
                    "        where (customer = :customer)  " +
                    "          and (coalesce(:subCustomer,null) IS NULL OR (('(null)' in (:subCustomer) AND sub_customer IS NULL) OR sub_customer in (:subCustomer)))" +
                    "          and (coalesce(:factory,null) IS NULL OR (('(null)' in (:factory) AND factory IS NULL) OR factory in (:factory))) " +
                    "          and (coalesce(:deviceId,null) IS NULL OR (('(null)' in (:deviceId) AND device_id IS NULL) OR device_id in (:deviceId))) " +
                    "          and (coalesce(:testArea,null) IS NULL OR (('(null)' in (:testArea) AND test_area IS NULL) OR test_area in (:testArea))) " +
                    "          and (coalesce(:testProgram,null) IS NULL OR (('(null)' in (:testProgram) AND test_program IS NULL) OR test_program in (:testProgram))) " +
                    "          and (coalesce(:testStage,null) IS NULL OR (('(null)' in (:testStage) AND test_stage IS NULL) OR test_stage in (:testStage))) " +
                    "          and (coalesce(:lotId,null) IS NULL OR (('(null)' in (:lotId) AND lot_id IS NULL) OR lot_id in (:lotId))) " +
                    "          and (coalesce(:sblotId,null) IS NULL OR (('(null)' in (:sblotId) AND sblot_id IS NULL) OR sblot_id in (:sblotId))) " +
                    "          and (coalesce(:waferId,null) IS NULL OR (('(null)' in (:waferId) AND wafer_id IS NULL) OR wafer_id in (:waferId))) " +
                    "          and (ifnull(:startTime,'bpms_mock_null') = 'bpms_mock_null' OR step_end_time >= :startTime) " +
                    "          and (ifnull(:endTime,'bpms_mock_null') = 'bpms_mock_null' OR step_end_time <= :endTime)" +
                    "          and (coalesce(:allSupportTestArea,null) IS NULL OR (('(null)' in (:allSupportTestArea) AND test_area IS NULL) OR test_area in (:allSupportTestArea))) " +
                    "          and convert_flag = 0 " +
                    "          and step = 7100 " +
                    "          and process_status = 'SUCCESS'" +
                    "          and file_category in ('STDF', 'RAW_DATA') " +
                    ")",
            nativeQuery = true)
    List<FileLoadingLog> querySuccessFileLogByCond(
            @Param("customer") String customer,
            @Param("subCustomer") List<String> subCustomer,
            @Param("factory") List<String> factory,
            @Param("deviceId") List<String> deviceId,
            @Param("testArea") List<String> testArea,
            @Param("testProgram") List<String> testProgram,
            @Param("testStage") List<String> testStage,
            @Param("lotId") List<String> lotId,
            @Param("sblotId") List<String> sblotId,
            @Param("waferId") List<String> waferId,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime,
            @Param("allSupportTestArea") List<String> allSupportTestArea
    );


    @Query(value = "select case " +
            "           when upper(:filterField) = upper('SUB_CUSTOMER') then sub_customer " +
            "           when upper(:filterField) = upper('FACTORY') then factory " +
            "           when upper(:filterField) = upper('DEVICE_ID') then device_id " +
            "           when upper(:filterField) = upper('TEST_PROGRAM') then test_program " +
            "           when upper(:filterField) = upper('TEST_STAGE') then test_stage " +
            "           when upper(:filterField) = upper('LOT_ID') then lot_id " +
            "           when upper(:filterField) = upper('WAFER_NO') then wafer_no " +
            "           end as result " +
            "from ( " +
            "select distinct customer " +
            "              , sub_customer " +
            "              , factory " +
            "              , test_area " +
            "              , device_id " +
            "              , test_program " +
            "              , test_stage " +
            "              , lot_id " +
            "              , wafer_no " +
            "from dw_file_loading_log " +
            "                  where step = 7100 and process_status = 'SUCCESS' and convert_flag = 0 and file_category in ('STDF', 'RAW_DATA') " +
            "                    and (customer = :customer) " +
            "                    and (coalesce(:subCustomerList,null) IS NULL OR sub_customer in (:subCustomerList)) " +
            "                    and (coalesce(:factoryList,null) IS NULL OR factory in (:factoryList)) " +
            "                    and (test_area = :testArea) " +
            "                    and (coalesce(:deviceIdList,null) IS NULL OR device_id in (:deviceIdList)) " +
            "                    and (coalesce(:testProgramList,null) IS NULL OR test_program in (:testProgramList)) " +
            "                    and (coalesce(:testStageList,null) IS NULL OR test_stage in (:testStageList)) " +
            "                    and (coalesce(:lotIdList,null) IS NULL OR lot_id in (:lotIdList)) " +
            "                    and (coalesce(:waferNoList,null) IS NULL OR wafer_no in (:waferNoList)) " +
            "                    and (ifNull(:startUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR create_time >= :startUpdateTime) " +
            "                    and (ifNull(:endUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR create_time <= :endUpdateTime) " +
            ") t;",
            nativeQuery = true)
    List<String> findCpDynamicField(
            @Param("customer") String customer,
            @Param("filterField") String filterField,
            @Param("subCustomerList") List<String> subCustomerList,
            @Param("factoryList") List<String> factoryList,
            @Param("testArea") String testArea,
            @Param("deviceIdList") List<String> deviceIdList,
            @Param("testProgramList") List<String> testProgramList,
            @Param("testStageList") List<String> testStageList,
            @Param("lotIdList") List<String> lotIdList,
            @Param("waferNoList") List<String> waferNoList,
            @Param("startUpdateTime") String startUpdateTime,
            @Param("endUpdateTime") String endUpdateTime
    );

    @Query(value = "select case " +
            "           when upper(:filterField) = upper('SUB_CUSTOMER') then sub_customer " +
            "           when upper(:filterField) = upper('FACTORY') then factory " +
            "           when upper(:filterField) = upper('DEVICE_ID') then device_id " +
            "           when upper(:filterField) = upper('TEST_PROGRAM') then test_program " +
            "           when upper(:filterField) = upper('TEST_STAGE') then test_stage " +
            "           when upper(:filterField) = upper('LOT_ID') then lot_id " +
            "           when upper(:filterField) = upper('SBLOT_ID') then sblot_id " +
            "           end as result " +
            "from ( " +
            "select distinct customer " +
            "              , sub_customer " +
            "              , factory " +
            "              , test_area " +
            "              , device_id " +
            "              , test_program " +
            "              , test_stage " +
            "              , lot_id " +
            "              , sblot_id " +
            "from dw_file_loading_log " +
            "                  where step = 7100 and process_status = 'SUCCESS' and convert_flag = 0 and file_category in ('STDF', 'RAW_DATA') " +
            "                    and (customer = :customer) " +
            "                    and (coalesce(:subCustomerList,null) IS NULL OR sub_customer in (:subCustomerList)) " +
            "                    and (coalesce(:factoryList,null) IS NULL OR factory in (:factoryList)) " +
            "                    and (test_area = :testArea) " +
            "                    and (coalesce(:deviceIdList,null) IS NULL OR device_id in (:deviceIdList)) " +
            "                    and (coalesce(:testProgramList,null) IS NULL OR test_program in (:testProgramList)) " +
            "                    and (coalesce(:testStageList,null) IS NULL OR test_stage in (:testStageList)) " +
            "                    and (coalesce(:lotIdList,null) IS NULL OR lot_id in (:lotIdList)) " +
            "                    and (coalesce(:sblotIdList,null) IS NULL OR sblot_id in (:sblotIdList)) " +
            "                    and (ifNull(:startUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR create_time >= :startUpdateTime) " +
            "                    and (ifNull(:endUpdateTime,'bpms_mock_null') = 'bpms_mock_null' OR create_time <= :endUpdateTime) " +
            ") t;",
            nativeQuery = true)
    List<String> findFtDynamicField(
            @Param("customer") String customer,
            @Param("filterField") String filterField,
            @Param("subCustomerList") List<String> subCustomerList,
            @Param("factoryList") List<String> factoryList,
            @Param("testArea") String testArea,
            @Param("deviceIdList") List<String> deviceIdList,
            @Param("testProgramList") List<String> testProgramList,
            @Param("testStageList") List<String> testStageList,
            @Param("lotIdList") List<String> lotIdList,
            @Param("sblotIdList") List<String> sblotIdList,
            @Param("startUpdateTime") String startUpdateTime,
            @Param("endUpdateTime") String endUpdateTime
    );

}
