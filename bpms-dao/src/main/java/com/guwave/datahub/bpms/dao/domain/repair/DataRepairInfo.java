package com.guwave.datahub.bpms.dao.domain.repair;

import org.hibernate.annotations.Comment;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * 2025/5/6 17:00
 * DataRepairInfo
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "bz_data_repair_info")
public class DataRepairInfo {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @Column(name = "customer")
    @Comment("客户")
    private String customer;

    @Column(name = "sub_customer")
    @Comment("子客户")
    private String subCustomer;

    @Column(name = "factory")
    @Comment("factory")
    private String factory;

    @Column(name = "factory_site")
    @Comment("factory_site")
    private String factorySite;

    @Column(name = "repair_batch_id")
    @Comment("处理批次")
    private Long repairBatchId;

    @Column(name = "file_id")
    @Comment("fileId")
    private Long fileId;

    @Column(name = "file_name", nullable = false)
    @Comment("文件名称")
    private String fileName;

    @Column(name = "wafer_id")
    @Comment("wafer_id")
    private String waferId;

    @Column(name = "test_area")
    @Comment("测试阶段，CP/FT")
    private String testArea;

    @Column(name = "repair_json", columnDefinition = "text")
    @Comment("修改内容json")
    private String repairJson;

    @Column(name = "original_device_id")
    @Comment("原始的device_id")
    private String originalDeviceId;

    @Column(name = "original_test_stage")
    @Comment("原始的test stage")
    private String originalTestStage;

    @Column(name = "original_lot_id")
    @Comment("原始的lot_id")
    private String originalLotId;

    @Column(name = "original_wafer_id")
    @Comment("原始的wafer_id")
    private String originalWaferId;

    @Column(name = "original_wafer_no")
    @Comment("原始的wafer_no")
    private String originalWaferNo;

    @Column(name = "original_sblot_id")
    @Comment("原始的sblot_id")
    private String originalSblotId;

    @Column(name = "original_test_program")
    @Comment("原始的test_program")
    private String originalTestProgram;

    @Column(name = "original_lot_type")
    @Comment("原始的lot_type")
    private String originalLotType;

    @Column(name = "original_retest_hbin_number")
    @Comment("原始的retest_hbin_number")
    private String originalRetestHbinNumber;

    @Column(name = "original_flow_id")
    @Comment("原始的flow_id")
    private String originalFlowId;

    @Column(name = "modify_device_id")
    @Comment("修改的device_id")
    private String modifyDeviceId;

    @Column(name = "modify_test_stage")
    @Comment("修改的test stage")
    private String modifyTestStage;

    @Column(name = "modify_lot_id")
    @Comment("修改的lot_id")
    private String modifyLotId;

    @Column(name = "modify_wafer_no")
    @Comment("修改的wafer_no")
    private String modifyWaferNo;

    @Column(name = "modify_sblot_id")
    @Comment("修改的sblot_id")
    private String modifySblotId;

    @Column(name = "modify_test_program")
    @Comment("修改的test_program")
    private String modifyTestProgram;

    @Column(name = "modify_lot_type")
    @Comment("修改的lot_type")
    private String modifyLotType;

    @Column(name = "modify_retest_hbin_number")
    @Comment("修改的retest_hbin_number")
    private String modifyRetestHbinNumber;

    @Column(name = "modify_flow_id")
    @Comment("修改的flow_id")
    private String modifyFlowId;

    @Column(name = "repair_category")
    @Comment("修改类别 Data Accuracy,Data Completion")
    private String repairCategory;

    @Column(name = "last_repair_type")
    @Comment("修改类型 准确性 默认为关键字段调整")
    private String lastRepairType;

    @Column(name = "repair_status")
    @Comment("修改状态 Success,Fail,Process")
    private String repairStatus;

    @Column(name = "repair_error_info", columnDefinition = "text")
    @Comment("修改异常信息")
    private String repairErrorInfo;

    @Column(name = "repair_times", nullable = false)
    @Comment("修改次数")
    private Integer repairTimes = 0;

    @Column(name = "start_time")
    @Comment("开始时间")
    private Date startTime;

    @Column(name = "end_time")
    @Comment("结束时间")
    private Date endTime;

    @Column(name = "remark")
    @Comment("remark")
    private String remark;

    @Column(name = "delete_flag", nullable = false)
    @Comment("删除标志,0:未删除，1:已删除")
    private Boolean deleteFlag = false;

    @Column(name = "create_time")
    @Comment("创建时间")
    private Date createTime;

    @Column(name = "update_time")
    @Comment("更新时间")
    private Date updateTime;

    @Column(name = "create_user")
    @Comment("创建用户")
    private String createUser = "System";

    @Column(name = "update_user")
    @Comment("更新用户")
    private String updateUser = "System";

    public Long getId() {
        return id;
    }

    public DataRepairInfo setId(Long id) {
        this.id = id;
        return this;
    }

    public String getCustomer() {
        return customer;
    }

    public DataRepairInfo setCustomer(String customer) {
        this.customer = customer;
        return this;
    }

    public String getSubCustomer() {
        return subCustomer;
    }

    public DataRepairInfo setSubCustomer(String subCustomer) {
        this.subCustomer = subCustomer;
        return this;
    }

    public String getFactory() {
        return factory;
    }

    public DataRepairInfo setFactory(String factory) {
        this.factory = factory;
        return this;
    }

    public String getFactorySite() {
        return factorySite;
    }

    public DataRepairInfo setFactorySite(String factorySite) {
        this.factorySite = factorySite;
        return this;
    }

    public Long getRepairBatchId() {
        return repairBatchId;
    }

    public DataRepairInfo setRepairBatchId(Long repairBatchId) {
        this.repairBatchId = repairBatchId;
        return this;
    }

    public Long getFileId() {
        return fileId;
    }

    public DataRepairInfo setFileId(Long fileId) {
        this.fileId = fileId;
        return this;
    }

    public String getFileName() {
        return fileName;
    }

    public DataRepairInfo setFileName(String fileName) {
        this.fileName = fileName;
        return this;
    }

    public String getWaferId() {
        return waferId;
    }

    public DataRepairInfo setWaferId(String waferId) {
        this.waferId = waferId;
        return this;
    }

    public String getTestArea() {
        return testArea;
    }

    public DataRepairInfo setTestArea(String testArea) {
        this.testArea = testArea;
        return this;
    }

    public String getRepairJson() {
        return repairJson;
    }

    public DataRepairInfo setRepairJson(String repairJson) {
        this.repairJson = repairJson;
        return this;
    }

    public String getOriginalDeviceId() {
        return originalDeviceId;
    }

    public DataRepairInfo setOriginalDeviceId(String originalDeviceId) {
        this.originalDeviceId = originalDeviceId;
        return this;
    }

    public String getOriginalTestStage() {
        return originalTestStage;
    }

    public DataRepairInfo setOriginalTestStage(String originalTestStage) {
        this.originalTestStage = originalTestStage;
        return this;
    }

    public String getOriginalLotId() {
        return originalLotId;
    }

    public DataRepairInfo setOriginalLotId(String originalLotId) {
        this.originalLotId = originalLotId;
        return this;
    }

    public String getOriginalWaferId() {
        return originalWaferId;
    }

    public DataRepairInfo setOriginalWaferId(String originalWaferId) {
        this.originalWaferId = originalWaferId;
        return this;
    }

    public String getOriginalWaferNo() {
        return originalWaferNo;
    }

    public DataRepairInfo setOriginalWaferNo(String originalWaferNo) {
        this.originalWaferNo = originalWaferNo;
        return this;
    }

    public String getOriginalSblotId() {
        return originalSblotId;
    }

    public DataRepairInfo setOriginalSblotId(String originalSblotId) {
        this.originalSblotId = originalSblotId;
        return this;
    }

    public String getOriginalTestProgram() {
        return originalTestProgram;
    }

    public DataRepairInfo setOriginalTestProgram(String originalTestProgram) {
        this.originalTestProgram = originalTestProgram;
        return this;
    }

    public String getOriginalLotType() {
        return originalLotType;
    }

    public DataRepairInfo setOriginalLotType(String originalLotType) {
        this.originalLotType = originalLotType;
        return this;
    }

    public String getOriginalRetestHbinNumber() {
        return originalRetestHbinNumber;
    }

    public DataRepairInfo setOriginalRetestHbinNumber(String originalRetestHbinNumber) {
        this.originalRetestHbinNumber = originalRetestHbinNumber;
        return this;
    }

    public String getOriginalFlowId() {
        return originalFlowId;
    }

    public DataRepairInfo setOriginalFlowId(String originalFlowId) {
        this.originalFlowId = originalFlowId;
        return this;
    }

    public String getModifyDeviceId() {
        return modifyDeviceId;
    }

    public DataRepairInfo setModifyDeviceId(String modifyDeviceId) {
        this.modifyDeviceId = modifyDeviceId;
        return this;
    }

    public String getModifyTestStage() {
        return modifyTestStage;
    }

    public DataRepairInfo setModifyTestStage(String modifyTestStage) {
        this.modifyTestStage = modifyTestStage;
        return this;
    }

    public String getModifyLotId() {
        return modifyLotId;
    }

    public DataRepairInfo setModifyLotId(String modifyLotId) {
        this.modifyLotId = modifyLotId;
        return this;
    }

    public String getModifyWaferNo() {
        return modifyWaferNo;
    }

    public DataRepairInfo setModifyWaferNo(String modifyWaferNo) {
        this.modifyWaferNo = modifyWaferNo;
        return this;
    }

    public String getModifySblotId() {
        return modifySblotId;
    }

    public DataRepairInfo setModifySblotId(String modifySblotId) {
        this.modifySblotId = modifySblotId;
        return this;
    }

    public String getModifyTestProgram() {
        return modifyTestProgram;
    }

    public DataRepairInfo setModifyTestProgram(String modifyTestProgram) {
        this.modifyTestProgram = modifyTestProgram;
        return this;
    }

    public String getModifyLotType() {
        return modifyLotType;
    }

    public DataRepairInfo setModifyLotType(String modifyLotType) {
        this.modifyLotType = modifyLotType;
        return this;
    }

    public String getModifyRetestHbinNumber() {
        return modifyRetestHbinNumber;
    }

    public DataRepairInfo setModifyRetestHbinNumber(String modifyRetestHbinNumber) {
        this.modifyRetestHbinNumber = modifyRetestHbinNumber;
        return this;
    }

    public String getModifyFlowId() {
        return modifyFlowId;
    }

    public DataRepairInfo setModifyFlowId(String modifyFlowId) {
        this.modifyFlowId = modifyFlowId;
        return this;
    }

    public String getRepairCategory() {
        return repairCategory;
    }

    public DataRepairInfo setRepairCategory(String repairCategory) {
        this.repairCategory = repairCategory;
        return this;
    }

    public String getLastRepairType() {
        return lastRepairType;
    }

    public DataRepairInfo setLastRepairType(String lastRepairType) {
        this.lastRepairType = lastRepairType;
        return this;
    }

    public String getRepairStatus() {
        return repairStatus;
    }

    public DataRepairInfo setRepairStatus(String repairStatus) {
        this.repairStatus = repairStatus;
        return this;
    }

    public String getRepairErrorInfo() {
        return repairErrorInfo;
    }

    public DataRepairInfo setRepairErrorInfo(String repairErrorInfo) {
        this.repairErrorInfo = repairErrorInfo;
        return this;
    }

    public Integer getRepairTimes() {
        return repairTimes;
    }

    public DataRepairInfo setRepairTimes(Integer repairTimes) {
        this.repairTimes = repairTimes;
        return this;
    }

    public Date getStartTime() {
        return startTime;
    }

    public DataRepairInfo setStartTime(Date startTime) {
        this.startTime = startTime;
        return this;
    }

    public Date getEndTime() {
        return endTime;
    }

    public DataRepairInfo setEndTime(Date endTime) {
        this.endTime = endTime;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public DataRepairInfo setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public Boolean getDeleteFlag() {
        return deleteFlag;
    }

    public DataRepairInfo setDeleteFlag(Boolean deleteFlag) {
        this.deleteFlag = deleteFlag;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public DataRepairInfo setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public DataRepairInfo setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public DataRepairInfo setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public DataRepairInfo setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }
}
