package com.guwave.datahub.bpms.dao.repository.dw;

import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LayerCalculatePool;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * LayerCalculatePoolRepository
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-25 17:44:01
 */
@Repository("BpmsLayerCalculatePoolRepository")
public interface LayerCalculatePoolRepository extends JpaRepository<LayerCalculatePool, Long> {

    LayerCalculatePool findFirstByCustomerAndSubCustomerAndFactoryAndTestAreaAndDeviceIdAndTestStageAndLotIdAndWaferNoAndLotTypeAndFileCategoryAndDwLayerOrderByUpdateTimeDesc(String customer, String subCustomer, String factory, TestArea testArea, String deviceId, String testStage, String lotId, String waferNo, LotType lotType, FileCategory fileCategory, DwLayer dwLayer);

    List<LayerCalculatePool> findByProcessStatusInOrderByIdDesc(List<ProcessStatus> processStatus);

    @Transactional
    @Modifying
    @Query(value = "update dw_layer_calculate_pool\n" +
            "set process_status = 'FAIL'\n" +
            "where id in (select * from (select id from dw_layer_calculate_pool where process_status in ('PROCESSING', 'CREATE')) x)", nativeQuery = true)
    void updateNotCalFinishToFail();

    @Transactional
    @Modifying
    @Query(value = "update dw_layer_calculate_pool\n" +
            "set process_status = 'CREATE'\n" +
            "where id in (select * from (select id from dw_layer_calculate_pool where process_status in ('PROCESSING')) x)", nativeQuery = true)
    void updateProcessingToCreate();
}
