package com.guwave.datahub.bpms.dao.clickhouse.dto;

import com.guwave.onedata.dataware.common.contant.TestArea;
import lombok.Data;

@Data
public class AccuracyLotWaferDetailDTO {
    private String deviceName;
    private String testArea;
    private String testStage;
    private String testProgram;
    private String lotId;
    private String waferId;
    private String waferNo;
    private String sbLotId;

    private String lotType;
    private String accuracyCheck;
    private String accuracyCheckType;
    private String firstYield;
    private String finalYield;
    private Long inputCount;
    private Long passCount;
    private String summaryYield;
    private String summaryCount;
    private Long summaryPassCount;
    private String matchFlag;
    private Long stdFileCount;
    private Long lossCount;
    private Long defectCount;
    private String stdStartTime;

    public String getQueryKey() {
        StringBuffer buffer = new StringBuffer();
        buffer.append(deviceName).append("-");
        buffer.append(testArea).append("-");
        buffer.append(testStage).append("-");
        buffer.append(testProgram).append("-");
        buffer.append(lotId).append("-");
        if (TestArea.getCPList().contains(TestArea.of(testArea))) {
            buffer.append(waferId).append("-");
        } else {
            buffer.append(sbLotId).append("-");
        }
        return buffer.toString();
    }

}
