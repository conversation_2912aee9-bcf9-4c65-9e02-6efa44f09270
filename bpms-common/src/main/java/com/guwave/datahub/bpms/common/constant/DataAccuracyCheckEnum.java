package com.guwave.datahub.bpms.common.constant;

import org.apache.commons.lang3.StringUtils;

public enum DataAccuracyCheckEnum {
    NORMAL("NORMAL","正常",1),
    DUP_RETEST_NORMAL("DUP_RETEST_NORMAL","重测-正常",2),
    MORE_THAN_ONE_TPS("MORE_THAN_ONE_TPS","多程序测试",3),
    DUP_RETEST_MORE_THAN_ONE_TPS("DUP_RETEST_MORE_THAN_ONE_TPS","重测-多程序测试",4),
    PASS_PARTS_RETESTED("PASS_PARTS_RETESTED","好品复测",5),
    DUP_RETEST_PASS_PARTS_RETESTED("DUP_RETEST_PASS_PARTS_RETESTED","重测-好品复测",6),
    YIELD_GREATER_THAN_100("YIELD_GREATER_THAN_100","良率超高",7),
    DUP_RETEST_YIELD_GREATER_THAN_100("DUP_RETEST_YIELD_GREATER_THAN_100", "重测-良率超高", 8),
    P_FILE_NOT_EXISTS("P_FILE_NOT_EXISTS", "初测数据缺失", 9),
    DUP_RETEST_P_FILE_NOT_EXISTS("DUP_RETEST_P_FILE_NOT_EXISTS", "重测-初测数据缺失", 10),
    // {}_INPUT_MORE 展示为{}芯片过多
    INPUT_MORE("INPUT_MORE", "%s芯片过多", 11),
    // {}_INPUT_MORE 展示为{}芯片过多
    DUP_RETEST_INPUT_MORE("DUP_RETEST_INPUT_MORE", "重测-%s芯片过多", 12),
    // {}_INPUT_LESS 展示为{}芯片过少
    INPUT_LESS("INPUT_LESS", "%s芯片过少", 13),
    // {}_INPUT_LESS 展示为{}芯片过少
    DUP_RETEST_INPUT_LESS("DUP_RETEST_INPUT_LESS", "重测-%s芯片过少", 14),

    UNCHECKED("", "未检测", 15);

    private final String fieldValue;
    private final String showName;

    public Integer getOrder() {
        return order;
    }

    private final Integer order;

    DataAccuracyCheckEnum(String fieldValue, String showName, Integer order) {
        this.fieldValue = fieldValue;
        this.showName = showName;
        this.order = order;
    }

    public static Boolean isNormal(String type) {
        DataAccuracyCheckEnum accuracyCheck = DataAccuracyCheckEnum.of(type);
        return NORMAL.equals(accuracyCheck);
    }

    public String getShowName() {
        return showName;
    }

    public String getFieldValue() {
        return fieldValue;
    }

    public static DataAccuracyCheckEnum of(String type) {
        if (StringUtils.isBlank(type)) {
            return UNCHECKED;
        }
        for (DataAccuracyCheckEnum accuracyCheck : DataAccuracyCheckEnum.values()) {
            if (accuracyCheck.getFieldValue().equals(type)) {
                return accuracyCheck;
            }
        }
        if (type.endsWith(INPUT_MORE.getFieldValue())) {
            if (type.startsWith("DUP_RETEST_")) {
                return DUP_RETEST_INPUT_MORE;
            } else {
                return INPUT_MORE;
            }
        }
        if (type.endsWith(INPUT_LESS.getFieldValue())) {
            if (type.startsWith("DUP_RETEST_")) {
                return DUP_RETEST_INPUT_LESS;
            } else {
                return INPUT_LESS;
            }
        }
        return UNCHECKED;
    }
}
