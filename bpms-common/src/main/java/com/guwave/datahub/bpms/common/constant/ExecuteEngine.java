package com.guwave.datahub.bpms.common.constant;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum ExecuteEngine {

    /**
     * CLICKHOUSE
     */
    CLICKHOUSE("CLICKHOUSE"),

    /**
     * SPARK
     */
    SPARK("SPARK"),
    /**
     * PYTHON
     */
    PYTHON("PYTHON");

    private final String engine;

    ExecuteEngine(String engine) {
        this.engine = engine;
    }

    private static final Map<String, ExecuteEngine> ENGINE_MAP = Stream.of(ExecuteEngine.values()).collect(Collectors.toMap(ExecuteEngine::name, Function.identity()));

    public static ExecuteEngine of(String state) {
        return ENGINE_MAP.get(state);
    }

    public String getEngine() {
        return engine;
    }
}
