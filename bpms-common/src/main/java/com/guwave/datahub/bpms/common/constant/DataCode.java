package com.guwave.datahub.bpms.common.constant;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum DataCode {

    /**
     * 入库成功文件统计数据
     */
    SUCCESS_WAREHOUSING_LOT_WAFER("SUCCESS_WAREHOUSING_LOT_WAFER"),
    /**
     * 入库失败文件统计数据
     */
    FAIL_WAREHOUSING_FILE("FAIL_WAREHOUSING_FILE"),
    /**
     * 入库成功文件统计数据
     */
    SUCCESS_WAREHOUSING_FILE("SUCCESS_WAREHOUSING_FILE"),
    /**
     * 入库失败任务统计数据
     */
    FAIL_WAREHOUSING_TASK("FAIL_WAREHOUSING_TASK"),
    /**
     * 入库成功文件统计数据
     */
    FAIL_SPARK_TASK("FAIL_SPARK_TASK"),
    /**
     * 异常文件统计数据
     */
    ABNORMAL_FILE("ABNORMAL_FILE"),
    /**
     * YMS_MODIFY任务统计数据
     */
    YMS_MODIFY("YMS_MODIFY"),
    /**
     * GPD集群资源使用
     */
    GDP_CLUSTER_STATUS("GDP_CLUSTER_STATUS"),
    /**
     * Collectx拉取任务统计
     */
    COLLECTX_PULLING("COLLECTX_PULLING"),
    /**
     * 手动上传数据统计
     */
    MANUAL_TASK("MANUAL_TASK"),
    /**
     * 数据重播统计
     */
    REPLAY_TASK("REPLAY_TASK"),

    /**
     * 数据修复统计
     */
    REPAIR_TASK("REPAIR_TASK"),
    ;

    private final String code;

    DataCode(String code) {
        this.code = code;
    }

    private static final Map<String, DataCode> STATUS_MAP = Stream.of(DataCode.values()).collect(Collectors.toMap(DataCode::name, Function.identity()));

    public static DataCode of(String state) {
        return STATUS_MAP.get(state);
    }

    public String getCode() {
        return code;
    }
}
