package com.guwave.datahub.bpms.common.constant;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 常量
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-26 15:31:38
 */
public class Constant {

    public static final String UNKNOWN = "unknown";
    public static final String EMPTY = "";
    public static final String COMMA = ",";
    public static final String SEMICOLON = ";";
    public static final String HYPHEN = "-";
    public static final String EQUAL = "=";
    public static final String SLASH = "/";
    public static final String Pipe = "|";
    public static final String ENTER = "\n";
    public static final String SPACE = " ";
    public static final String COLON = ":";
    public static final String DOUBLE_AMPERSAND = "&&";

    public static final String TASK_SUFFIX = "cluster/app/";

    public static final String AUTH_HEADER_FIELD_NAME = "Authorization";

    public static final String WRAPPER_NULL = "(null)";
    // 文件后缀
    public static final String FILE_SUFFIX_ZIP = ".zip";
    public static final String FILE_SUFFIX_PY = ".py";
    public static final String TEST_NUM_STR = "testNum";
    public static final String TEST_TXT_STR = "testTxt";
    public static final String RESULT_STR = "result";
    // 复制后缀
    public static final String DUPLICATE_SUFFIX = "_副本";

    public static final String NA = "NA";

    public static final String REPAIRINFO_STR = "repairInfo";
    public static final String FLOW_ID_STR = "Flow Id";
    public static final String RETEST_HBIN_NUMBER_STR = "Retest Hbin Number";
    public static final String REMARK_STR = "remark";


    public static final String ETL_GATEWAY_DIR = "etl_gateway/";
    public static final String ENTRY_NAME = "entry";

    public static final String REGEXP = "regexp";
    public static final String KEYWORD = "keyword";
    public static final String LOG = "log";


}
