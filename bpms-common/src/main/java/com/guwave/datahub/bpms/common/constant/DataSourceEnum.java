package com.guwave.datahub.bpms.common.constant;

import com.guwave.onedata.dataware.common.contant.TestArea;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

public enum DataSourceEnum {
    TEST_RAW_DATA("Test Raw Data"),
    MAP("CP Map"),
    INK_LESS_MAP("Inkless Map");

    private String value;

    DataSourceEnum(String value) {
        this.value = value;
    }

    /**
     * 将TestArea映射为DataSourceEnum
     * @param testArea
     * @return
     */
    public static DataSourceEnum ofDwTestArea(TestArea testArea) {
        if (TestArea.getCpMapDataSourceList().contains(testArea)) {
            return MAP;
        }
        if (TestArea.getCpInklessMapDataDourceList().contains(testArea)) {
            return INK_LESS_MAP;
        }

        return TEST_RAW_DATA;
    }

    public static DataSourceEnum of(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        for (DataSourceEnum dataSourceEnum : DataSourceEnum.values()) {
            if (dataSourceEnum.getValue().equals(value)) {
                return dataSourceEnum;
            }
        }
        return null;
    }

    public String getValue() {
        return value;
    }


}
