package com.guwave.datahub.bpms.common.constant;

public class ChartData {
    private String category;
    private Double value;       // 数值
    private String seriesName;  // 数据系列名称（用于折线图的多系列）

    public ChartData(String category, Double value) {
        this.category = category;
        this.value = value;
    }
    public ChartData(String category, Long value) {
        this.category = category;
        this.value = Double.valueOf(value);
    }


    public ChartData(String category, Double value, String seriesName) {
        this.category = category;
        this.value = value;
        this.seriesName = seriesName;
    }
    public ChartData(String category, Integer value, String seriesName) {
        this.category = category;
        this.value = Double.valueOf(value);
        this.seriesName = seriesName;
    }
    public ChartData(String category, Long value, String seriesName) {
        this.category = category;
        this.value = Double.valueOf(value);
        this.seriesName = seriesName;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Double getValue() {
        return value;
    }

    public void setValue(Double value) {
        this.value = value;
    }

    public String getSeriesName() {
        return seriesName;
    }

    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName;
    }
}