package com.guwave.datahub.bpms.common.constant;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum StatisticIndexEnum {

    /**
     * 统计指标类型
     */
    PRODUCT_COUNT("productCnt", "产品数量"),
    BATCH_COUNT("batchCnt", "批次数量"),
    CHIP_COUNT("chipCnt", "芯片数量"),
    IN_DB_TIME("costTime", "入库耗时"),
    YIELD_ACCURACY("yieldAccuracy","批次良率准确率"),
    MATCH_RATE("matchRate", "匹配率"),
    NORMAL_RATE("DataAccuracyCheckRate","DataAccuracyCheck normal率");

    private final String indexType;
    private final String indexName;

    StatisticIndexEnum(String indexType, String indexName) {
        this.indexType = indexType;
        this.indexName = indexName;
    }

    public static List<StatisticIndexEnum> getStatisticIndexEnumList() {
        return Arrays.asList(CHIP_COUNT, MATCH_RATE, NORMAL_RATE, PRODUCT_COUNT, BATCH_COUNT, YIELD_ACCURACY);
    }

    private static final Map<String, StatisticIndexEnum> INDEX_MAP = Arrays.stream(values()).collect(Collectors.toMap( StatisticIndexEnum::getIndexType, v -> v));

    public static StatisticIndexEnum of(String indexType) {
        return INDEX_MAP.get(indexType);
    }

}
