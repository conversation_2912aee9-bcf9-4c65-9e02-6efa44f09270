package com.guwave.datahub.bpms.common.constant;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum ReportModule {

    /**
     * 入库
     */
    WAREHOUSING("WAREHOUSING"),
    /**
     * spark任务
     */
    SPARK_TASK("SPARK_TASK"),
    /**
     * 时效性
     */
    TIMELINESS("TIMELINESS"),
    /**
     * collectx拉取
     */
    COLLECTX_PULLING("COLLECTX_PULLING"),
    /**
     * 数据修复
     */
    REPAIR("REPAIR"),
    /**
     * 数据重播
     */
    REPLAY("REPLAY"),
    /**
     * YMS的modify任务
     */
    YMS_MODIFY("YMS_MODIFY"),
    /**
     * 手动上传
     */
    MANUAL("MANUAL"),
    /**
     * GDP集群资源使用情况
     */
    GDP_CLUSTER_STATUS("GDP_CLUSTER_STATUS"),
    /**
     * 异常数据
     */
    ABNORMAL_DATA("ABNORMAL");
    private final String module;

    ReportModule(String module) {
        this.module = module;
    }

    private static final Map<String, ReportModule> STATUS_MAP = Stream.of(ReportModule.values()).collect(Collectors.toMap(ReportModule::name, Function.identity()));

    public static ReportModule of(String module) {
        return STATUS_MAP.get(module);
    }

    public String getModule() {
        return module;
    }
}
