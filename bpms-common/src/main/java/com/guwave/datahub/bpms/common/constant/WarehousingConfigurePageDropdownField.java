package com.guwave.datahub.bpms.common.constant;

/**
 * 2025/3/4 15:29
 * WarehousingConfigurePageDropdownField
 *
 * <AUTHOR>
 */
public enum WarehousingConfigurePageDropdownField {
    NAME("NAME"),
    SUB_CUSTOMER("SUB_CUSTOMER"),
    FACTORY("FACTORY"),
    TEST_AREA("TEST_AREA"),
    FILE_CATEGORY("FILE_CATEGORY"),
    CREATE_USER("CREATE_USER"),
    UPDATE_USER("UPDATE_USER"),
    ;

    private final String field;

    WarehousingConfigurePageDropdownField(String field) {
        this.field = field;
    }

    public String getField(){
        return field;
    }
}
