package com.guwave.datahub.bpms.common.constant;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Bin P/F枚举
 * 
 * <AUTHOR>
 * @version 1.0
 */
public enum BinPF {

    /**
     * P - Pass 通过
     */
    P("P"),

    /**
     * F - Fail 失败
     */
    F("F");

    private final String pf;

    BinPF(String pf) {
        this.pf = pf;
    }

    private static final Map<String, BinPF> PF_MAP = Stream.of(BinPF.values())
            .collect(Collectors.toMap(BinPF::name, Function.identity()));

    public static BinPF of(String pf) {
        return PF_MAP.get(pf);
    }

    public String getPf() {
        return pf;
    }
}