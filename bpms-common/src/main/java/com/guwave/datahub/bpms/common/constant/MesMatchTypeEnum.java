package com.guwave.datahub.bpms.common.constant;

import java.util.Comparator;

public enum MesMatchTypeEnum {
    MATCH("Match","Match",1),
    MES_DATA_LOSS("MESDataLoss","MESDataLoss",2),
    FILE_LOSS("FileLoss","FileLoss",3),
    FILE_MORE("FileMore","FileMore",4),
    FILE_UNEQUAL("FileUnequal","FileUnequal",5),
    INPUT_LESS("InputLess","InputLess",6),
    INPUT_MORE("InputMore","InputMore",7),
    JAM_OK("<PERSON><PERSON>","<PERSON><PERSON>",8),
    OUTPUT_MORE("OutputMore","OutputMore",9),
    OUTPUT_LESS("OutputLess","OutputLess",10),
    DL_ZERO("DL_ZERO","DL_ZERO",11),
    DL_<PERSON>("DL_<PERSON>","DL_<PERSON>",12),
    DL_MORE("DL_MORE","DL_MORE",13),
    FILE_MATCH("File Match","File Match",14),
    LOSS("File Loss","File Loss",15),
    MORE("File More","File More",16),
    UNEQUAL("File Unequal","File Unequal",17),
    B2G("B2G","B2G",19),
    G2B("G2B","G2B",20),
    G2L("G2L","G2L",21),
    B2L("B2L","B2L",22),

    UNCHECKED("", "unChecked",23);

    private final String type;
    private final String showName;
    private final Integer order;

    public static class MesMatchTypeComparator implements Comparator<MesMatchTypeEnum> {

        @Override
        public int compare(MesMatchTypeEnum o1, MesMatchTypeEnum o2) {
            return o1.getOrder().compareTo(o2.getOrder());
        }
    }


    public Integer getOrder() {
        return order;
    }

    private MesMatchTypeEnum(String type, String showName, Integer order) {
        this.type = type;
        this.showName = showName;
        this.order = order;
    }
    public static Boolean isMatch(String type) {
        MesMatchTypeEnum matchType = MesMatchTypeEnum.of(type);
        return MATCH.equals(matchType) || MES_DATA_LOSS.equals(matchType) || UNCHECKED.equals(matchType);
    }

    public static MesMatchTypeEnum of (String type) {
        for (MesMatchTypeEnum matchType : MesMatchTypeEnum.values()) {
            if (matchType.getType().equals(type)) {
                return matchType;
            }
        }
        return UNCHECKED;
    }

    public String getShowName() {
        return showName;
    }

    public String getType() {
        return this.type;
    }
}