package com.guwave.datahub.bpms.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum FileStepEnum {
    STEP_1000(1000, "文件从ftp下载"),
    STEP_2100(2100, "需要转换的文件文件读取"),
    STEP_2200(2200, "需要转换的文件文件合并"),
    STEP_3100(3100, "文件预解析"),
    STEP_4100(4100, "文件解析到ods"),
    STEP_5100(5100, "dwd层计算"),
    STEP_6100(6100, "dws层计算"),
    STEP_7100(7100, "ads层计算");
    private final int step;
    private final String desc;


    public static List<FileStepEnum> valueList() {
        return Arrays.stream(FileStepEnum.values()).collect(Collectors.toList());
    }

}
