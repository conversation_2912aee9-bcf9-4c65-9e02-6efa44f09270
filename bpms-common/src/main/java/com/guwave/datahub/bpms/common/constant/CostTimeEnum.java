package com.guwave.datahub.bpms.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CostTimeEnum {


    FILE_TRANSFER("FILE_TRANSFER", "文件传输耗时\nAvg(Ftp 加载时间 - End Time)"),
    FILE_RECEIVE("FILE_RECEIVE", "数据接收耗时\nAvg(数据接收时间 - Ftp 加载时间)"),
    FILE_PARSE("FILE_PARSE", "数据解析耗时\nAvg(Create Time - 数据接收时间)"),
    ;

    private final String type;
    private final String desc;
}
