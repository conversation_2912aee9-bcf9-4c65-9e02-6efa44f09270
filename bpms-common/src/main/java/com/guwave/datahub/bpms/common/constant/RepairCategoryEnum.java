package com.guwave.datahub.bpms.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RepairCategoryEnum {
    DATA_ACCURACY("DATA_ACCURACY", "关键字段调整"),
    DATA_COMPLETION("DATA_COMPLETION", "关键字段补充"),
    ;

    private final String category;
    private final String type;

    public String getCategory() {
        return category;
    }

    public String getType() {
        return type;
    }
}
