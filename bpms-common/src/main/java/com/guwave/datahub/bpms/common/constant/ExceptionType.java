package com.guwave.datahub.bpms.common.constant;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum ExceptionType {


    EXECUTE_CLICKHOUSE_SQL_EXCEPTION("EXECUTE_CLICKHOUSE_SQL_EXCEPTION"),
    EXECUTE_SPARK_TASK_EXCEPTION("EXECUTE_SPARK_TASK_EXCEPTION"),
    EXECUTE_PYTHON_SCRIPT_EXCEPTION("EXECUTE_PYTHON_SCRIPT_EXCEPTION"),

    MISSING_RESOURCES_EXCEPTION("MISSING_RESOURCES_EXCEPTION"),
    /**
     * OTHER_EXCEPTION
     */
    OTHER_EXCEPTION("OTHER_EXCEPTION");

    private final String type;

    ExceptionType(String type) {
        this.type = type;
    }

    private static final Map<String, ExceptionType> TYPE_MAP = Stream.of(ExceptionType.values()).collect(Collectors.toMap(ExceptionType::getType, Function.identity()));

    public static ExceptionType of(String type) {
        return TYPE_MAP.get(type);
    }

    public String getType() {
        return type;
    }
}
