package com.guwave.datahub.bpms.common.constant;

import com.guwave.onedata.dataware.common.contant.TestArea;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum RepairFieldEnum {

    DEVICE_ID("Device Name","deviceId","modifyDeviceId","originalDeviceId",null,1),
    TEST_STAGE("Test Stage","testStage","modifyTestStage","originalTestStage",null,2),
    LOT_ID("Lot ID","lotId","modifyLotId","originalLotId",null,4),
    WAFER_NO("Wafer NO","waferNo","modifyWaferNo","originalWaferNo",TestArea.CP.getTestScope(),6),
    SBLOT_ID("Sblot ID","sblotId","modifySblotId","originalSblotId",TestArea.FT.getTestScope(),5),
    TEST_PROGRAM("Test Program","testProgram","modifyTestProgram","originalTestProgram",null,3),
    LOT_TYPE("Lot Type","lotType","modifyLotType","originalLotType",null,7);


    private final String mappingField;

    private final String mappingModifyField;

    private final String mappingOriginalField;

    private final String showName;

    private final String testScope;

    private final Integer order;

    public Integer getOrder() {
        return order;
    }

    RepairFieldEnum(String showName, String mappingField, String mappingModifyField, String mappingOriginalField, String testScope, Integer order) {
        this.mappingField = mappingField;
        this.mappingModifyField = mappingModifyField;
        this.mappingOriginalField = mappingOriginalField;
        this.showName = showName;
        this.testScope = testScope;
        this.order = order;
    }

    private static final Map<String, RepairFieldEnum> FIELD_MAP = Stream.of(RepairFieldEnum.values()).collect(Collectors.toMap(RepairFieldEnum::name, Function.identity()));

    private static final List<RepairFieldEnum> SHOW_LIST = Arrays.stream(RepairFieldEnum.values()).collect(Collectors.toList());

    public static RepairFieldEnum of(String result) { return FIELD_MAP.get(result); }

    public String getMappingField() { return mappingField; }

    public String getMappingModifyField() { return mappingModifyField; }

    public String getMappingOriginalField() { return mappingOriginalField; }


    public String getShowName() { return showName; }

    public String getTestScope() {
        return testScope;
    }

    public static List<RepairFieldEnum> getCpRepairFields() {
        return Arrays.stream(RepairFieldEnum.values())
                .filter(t -> t.getTestScope() == null || t.getTestScope().equals(TestArea.CP.getTestScope()))
                .sorted(Comparator.comparing(RepairFieldEnum::getOrder)).collect(Collectors.toList());
    }

    public static List<RepairFieldEnum> getFtRepairFields() {
        return Arrays.stream(RepairFieldEnum.values())
                .filter(t -> t.getTestScope() == null || t.getTestScope().equals(TestArea.FT.getTestScope()))
                .sorted(Comparator.comparing(RepairFieldEnum::getOrder)).collect(Collectors.toList());
    }



}
