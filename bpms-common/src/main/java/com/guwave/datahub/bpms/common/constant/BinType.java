package com.guwave.datahub.bpms.common.constant;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Bin类型枚举
 * 
 * <AUTHOR>
 * @version 1.0
 */
public enum BinType {

    /**
     * HBIN - 硬件Bin
     */
    HBIN("HBIN"),

    /**
     * SBIN - 软件Bin
     */
    SBIN("SBIN");

    private final String type;

    BinType(String type) {
        this.type = type;
    }

    private static final Map<String, BinType> TYPE_MAP = Stream.of(BinType.values())
            .collect(Collectors.toMap(BinType::name, Function.identity()));

    public static BinType of(String type) {
        return TYPE_MAP.get(type);
    }

    public String getType() {
        return type;
    }
}