package com.guwave.datahub.bpms.common.constant;


public enum ChartType {
    /**
     * 表格
     */
    TABLE("TABLE"),

    /**
     * 折线图
     */
    LINE("LINE"),

    /**
     * 饼图
     */
    PIE("PIE"),

    /**
     * 柱状图
     */
    BAR("BAR"),

    /**
     * 堆积柱状图
     */
    STACKED_BAR("STACKED_BAR"),
    ;

    private final String type;

    ChartType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }
}
