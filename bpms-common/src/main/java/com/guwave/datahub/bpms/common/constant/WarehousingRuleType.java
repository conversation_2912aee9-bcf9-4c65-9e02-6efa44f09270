package com.guwave.datahub.bpms.common.constant;

/**
 * 2025/3/4 11:41
 * WarehousingRuleType
 *
 * <AUTHOR>
 */
public enum WarehousingRuleType {
    DwSftpSubCustomerRule("DwSftpSubCustomerRule", "dw_sftp_sub_customer_rule"),
    DwSftpFileRenameRule("DwSftpFileRenameRule", "dw_sftp_file_rename_rule"),
    DwDataClearRule("DwDataClearRule", "dw_data_clear_rule"),
    DwSftpFileConvertScript("DwSftpFileConvertScript", "dw_sftp_file_convert_script"),
    DwSourceStandardDeviceIdRule("DwSourceStandardDeviceIdRule", "dw_source_standard_device_id_rule"),
    DwSourceStandardFieldRule("DwSourceStandardFieldRule", "dw_source_standard_field_rule"),
    BzWarehousingRuleFlow("BzWarehousingRuleFlow", "bz_warehousing_rule_flow")
    ;

    private final String ruleType;
    private final String tableName;

    WarehousingRuleType(String ruleType, String tableName) {
        this.ruleType = ruleType;
        this.tableName = tableName;
    }

    public String getRuleType(){
        return ruleType;
    }

    public String getTableName(){
        return tableName;
    }
}
