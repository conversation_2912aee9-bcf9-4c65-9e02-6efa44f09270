package com.guwave.datahub.bpms.common.constant;

public class ExecuteCommandException extends RuntimeException {
    private ExceptionType exceptionType;

    private String errorMessage;

    public ExecuteCommandException(ExceptionType exceptionType, String errorMessage) {
        super(errorMessage);
        this.exceptionType = exceptionType;
        this.errorMessage = errorMessage;
    }

    public ExceptionType getExceptionType() {
        return exceptionType;
    }

    public void setExceptionType(ExceptionType exceptionType) {
        this.exceptionType = exceptionType;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
