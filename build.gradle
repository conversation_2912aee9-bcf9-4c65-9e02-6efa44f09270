allprojects {
  apply plugin: 'java-library'
  apply plugin: 'idea'
  apply plugin: 'maven-publish'
  apply from: "$rootDir/gradle/tasks.gradle"

  group = 'com.guwave.datahub'
  version = '1.7.0'
  description = 'bpms'
  defaultTasks "clean"

  dependencies {
    implementation group: 'org.slf4j', name: 'slf4j-api', version: slf4jVersion

    compileOnly group: 'org.projectlombok', name: 'lombok', version: lombokVersion
    annotationProcessor group: 'org.projectlombok', name: 'lombok', version: lombokVersion
  }
  tasks.withType(GenerateModuleMetadata).configureEach {
    suppressedValidationErrors.add('enforced-platform')
  }
}
