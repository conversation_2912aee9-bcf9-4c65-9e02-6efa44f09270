#!/bin/bash

version=$1

pwd=`pwd`

rm -rf $pwd/deploy/$version
mkdir -p $pwd/deploy/$version
mkdir -p $pwd/deploy/${version}_thin

./gradlew clean build -x test --parallel
if [ $? -ne 0 ]; then
    echo "Gradle build failed"
    exit 1
fi

cd $pwd/bpms-app/image
sudo bash build.sh $version
cd $pwd/deploy/$version
sudo docker save -o bpms-app-$version.tar bpms/bpms-app:$version

cd $pwd
cp $pwd/bpms-app/target/distributions/*.zip $pwd/deploy/$version

cp $pwd/deploy.properties $pwd/deploy/$version
cp $pwd/deploy.sh $pwd/deploy/$version
cp $pwd/upgrade.properties $pwd/deploy/$version
cp $pwd/upgrade.sh $pwd/deploy/$version

cp $pwd/deploy.properties $pwd/deploy/${version}_thin
cp $pwd/deploy.sh $pwd/deploy/${version}_thin
cp $pwd/upgrade.properties $pwd/deploy/${version}_thin
cp $pwd/upgrade.sh $pwd/deploy/${version}_thin

mkdir -p $pwd/deploy/$version/resources/sql/mysql
cp -r $pwd/resources/sql/mysql/v$version/* $pwd/deploy/$version/resources/sql/mysql

mkdir -p $pwd/deploy/${version}_thin/resources/sql/mysql
cp -r $pwd/resources/sql/mysql/v$version/* $pwd/deploy/${version}_thin/resources/sql/mysql

mkdir -p $pwd/deploy/$version/resources/shell
cp -r $pwd/resources/shell/* $pwd/deploy/$version/resources/shell

mkdir -p $pwd/deploy/${version}_thin/resources/shell
cp -r $pwd/resources/shell/* $pwd/deploy/${version}_thin/resources/shell