plugins {
  id 'application'
}

description = 'bpms app'

dependencies {
  implementation project(':bpms-common')
  implementation project(':bpms-dao')
  implementation enforcedPlatform("org.springframework.boot:spring-boot-dependencies:$springBootVersion")
  implementation group: 'org.springframework.boot', name: 'spring-boot-starter'
  implementation group: 'org.springframework.boot', name: 'spring-boot-starter-web'
  implementation group: 'org.springframework.boot', name: 'spring-boot-starter-aop'
  implementation group: 'org.springframework.boot', name: 'spring-boot-starter-validation'
  implementation group: 'org.springframework.boot', name: 'spring-boot-starter-data-redis', version: springBootVersion
  implementation group: 'org.springframework.kafka', name: 'spring-kafka'
  implementation group: 'com.fasterxml.jackson.core', name: 'jackson-databind'

  implementation group: 'org.apache.dubbo', name: 'dubbo-spring-boot-starter', version: dubboVersion
  implementation group: 'org.apache.curator', name: 'curator-framework', version: curatorVersion
  implementation group: 'org.apache.curator', name: 'curator-x-discovery-server', version: curatorVersion
  implementation group: 'org.apache.curator', name: 'curator-client', version: curatorVersion

  implementation group: 'com.guwave.onedata', name: 'dataware-bridge-api', version: datawareVersion
  implementation group: 'com.guwave.onedata', name: 'dataware-repair-api', version: datawareVersion
  implementation group: 'com.guwave.onedata', name: 'linkx-bridge-api', version: linkxVersion
  implementation("com.guwave.gdp:common:$gdpCommonVersion") {
    transitive = false
  }
  implementation group: 'com.alibaba', name: 'fastjson', version: fastJsonVersion
  implementation group: 'mysql', name: 'mysql-connector-java', version: mysqlVersion
  implementation group: 'com.alibaba', name: 'transmittable-thread-local', version: transmittableVersion
  implementation group: 'org.apache.commons', name: 'commons-lang3', version: commonsLang3Version
  implementation group: 'commons-collections', name: 'commons-collections', version: commonsCollectionsVersion
  implementation group: 'cn.hutool', name: 'hutool-all', version: hutoolVersion

  implementation group: 'io.github.openfeign.form', name: 'feign-form', version: feignFormVersion
  implementation group: 'io.github.openfeign', name: 'feign-core', version: feignVersion
  implementation group: 'io.github.openfeign', name: 'feign-httpclient', version: feignVersion
  implementation group: 'io.github.openfeign', name: 'feign-jackson', version: feignVersion

  implementation group: 'com.github.housepower', name: 'clickhouse-native-jdbc', version: clickhouseNativeJdbcVersion

  implementation group: 'org.springdoc', name: 'springdoc-openapi-ui', version: springdocOpenapiVersion

  testImplementation group: 'org.springframework.boot', name: 'spring-boot-starter-test'

  implementation group: 'com.alibaba', name: 'easyexcel', version: easyExcelVersion
  implementation group: 'org.redisson', name: 'redisson', version: redissonVersion

  implementation group: 'org.springframework.boot', name: 'spring-boot-starter-thymeleaf', version: thymeleafVersion

  api (group: 'org.apache.hadoop', name: 'hadoop-common', version: hadoopVersion) {
    exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    exclude group: 'tomcat'
    exclude group: 'javax.servlet.jsp', module: 'jsp-api'
  }
  api (group: 'org.apache.hadoop', name: 'hadoop-hdfs', version: hadoopVersion) {
    exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    exclude group: 'tomcat'
    exclude group: 'javax.servlet.jsp', module: 'jsp-api'
  }
  api (group: 'org.apache.hadoop', name: 'hadoop-client', version: hadoopVersion) {
    exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    exclude group: 'tomcat'
    exclude group: 'javax.servlet.jsp', module: 'jsp-api'
  }
  implementation group: 'org.apache.commons', name: 'commons-compress', version: commonsCompressVersion
  implementation group: 'org.tukaani', name: 'xz', version: tukaaniXzVersion
  implementation group: 'net.sf.sevenzipjbinding', name: 'sevenzipjbinding', version: sevenzipjbindingVersion
  implementation group: 'net.sf.sevenzipjbinding', name: 'sevenzipjbinding-all-platforms', version: sevenzipjbindingVersion
  implementation group: 'org.apache.tika', name: 'tika-core', version: tikaVersion
  implementation group: 'com.guwave.onedata', name: 'dataware-parser', version: datawareVersion
  implementation group: 'com.guwave.onedata', name: 'dataware-sdk', version: datawareVersion
  api group: 'com.guwave.onedata', name: 'next-compute-api', version: nextComputeVersion
  implementation group: 'org.jfree', name: 'jfreechart', version: jfreechartVersion
  implementation group: 'org.apache.pdfbox', name: 'pdfbox', version: pdfboxVersion

  implementation group: 'com.guwave.bigbrother', name: 'skyeye-driver-logback', version: skyeyeVersion
}

configurations {
  compile.exclude group: 'log4j', module: 'log4j'
  compile.exclude group: 'org.apache.tomcat.embed', module: 'tomcat-embed-websocket'
  compile.exclude group: 'org.slf4j', module: 'slf4j-log4j12'
}

compileJava {
  options.compilerArgs = ["-parameters"]
}

jar {
  enabled true
  manifest.attributes 'Main-Class': 'com.guwave.datahub.bpms.app.BpmsAppApplication'
}

application {
  mainClassName = 'com.guwave.datahub.bpms.app.BpmsAppApplication'
  applicationDistribution.from('src/main/resources/properties').into('properties')
}

startScripts {
  doLast {
    unixScript.text = unixScript.text.replaceAll(":\\\$APP_HOME/lib/(.*)\n", ":\\\$APP_HOME/lib/\\*\n")
  }
}
