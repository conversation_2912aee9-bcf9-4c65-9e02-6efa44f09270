FROM common/openjdk-8u262-python3.8.13-centos7.8

<NAME_EMAIL>

ARG version

ENV VERSION $version

RUN mkdir -p /home/<USER>/deploy/datahub/bpms/bpms-app
COPY bpms-app-$version.tar /home/<USER>/deploy/datahub/bpms/bpms-app

RUN mkdir -p /home/<USER>/deploy/datahub/bpms/cron
COPY wip_snapshot.py /home/<USER>/deploy/datahub/bpms/cron
COPY dim_refresh.py /home/<USER>/deploy/datahub/bpms/cron
COPY dim_optimize.py /home/<USER>/deploy/datahub/bpms/cron
COPY ck_disk_rebalance.py /home/<USER>/deploy/datahub/bpms/cron
COPY lot_history.py /home/<USER>/deploy/datahub/bpms/cron
COPY yarn_calculator_update.py /home/<USER>/deploy/datahub/bpms/cron

COPY run.sh /run.sh
RUN chmod +x /run.sh

WORKDIR /home/<USER>/deploy/datahub/bpms/bpms-app

CMD ["/run.sh"]
