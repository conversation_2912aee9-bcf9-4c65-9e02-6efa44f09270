#!/bin/bash

version=$1
cp ../target/distributions/*.tar .
cp -r ../../resources/script/wip_snapshot.py .
cp -r ../../resources/script/dim_refresh.py .
cp -r ../../resources/script/dim_optimize.py .
cp -r ../../resources/script/ck_disk_rebalance.py .
cp -r ../../resources/script/lot_history.py .
cp -r ../../resources/script/yarn_calculator_update.py .

sudo docker build --build-arg version=$version -t bpms/bpms-app:$version .

rm -rf *.tar
