package com.guwave.datahub.bpms.app.util;

import com.alibaba.fastjson.JSON;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 2024/8/5 17:34
 * JsonUtil
 *
 * <AUTHOR>
 */
public class JsonUtil {

    public static boolean isJSONArray(String jsonString) {
        String jsonArrayRegex = "^\\[.*\\]$"; // Regex pattern for JSON array

        Pattern pattern = Pattern.compile(jsonArrayRegex);
        Matcher matcher = pattern.matcher(jsonString);

        return matcher.matches();
    }

    public static String toJsonArrayString(List<String> elements) {
        return elements == null ? null : JSON.toJSONString(elements);
    }

}
