package com.guwave.datahub.bpms.app.vo.web;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.guwave.datahub.bpms.app.vo.auth.DataPermissionInfoV2;
import com.guwave.datahub.bpms.app.vo.auth.UserInfo;
import com.guwave.datahub.bpms.common.constant.Constant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 请求上下文信息
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-27 10:57:25
 */
public class RequestContext {

    private static final TransmittableThreadLocal<UserInfo> userInfoThreadLocal = new TransmittableThreadLocal<>();
    private static final TransmittableThreadLocal<DataPermissionInfoV2> dataPermissionV2ThreadLocal = new TransmittableThreadLocal<>();

    public static void setUserInfo(UserInfo user) {
        userInfoThreadLocal.set(user);
    }

    public static UserInfo getUserInfo() {
        return userInfoThreadLocal.get();
    }

    /**
     * - 是否有某个角色
     */
    public static boolean hasRole(String role) {
        UserInfo userInfo = getUserInfo();
        return null != userInfo && CollectionUtils.isNotEmpty(userInfo.getRoles()) && userInfo.getRoles().contains(role);
    }

    /**
     * - 是否有某个权限
     */
    public static boolean hasPermission(String permission) {
        UserInfo userInfo = getUserInfo();
        return null != userInfo && CollectionUtils.isNotEmpty(userInfo.getPermissions()) && userInfo.getPermissions().contains(permission);
    }

    /**
     * - 是否有subCustomer数据权限
     * @return
     */
    public static boolean hasSubCustomerDataPermission() {
        return needProcDataPermission() && !noDataPermission() && getRoleDataPermissions().stream().anyMatch(roleDataPermission -> org.apache.commons.collections4.CollectionUtils.isNotEmpty(roleDataPermission.getSubCustomers()));
    }

    /**
     * - 是否有device数据权限
     * @return
     */
    public static boolean hasDeviceDataPermission() {
        return needProcDataPermission() && !noDataPermission() && getRoleDataPermissions().stream().anyMatch(roleDataPermission -> org.apache.commons.collections4.CollectionUtils.isNotEmpty(roleDataPermission.getDeviceIds()) || org.apache.commons.collections4.CollectionUtils.isNotEmpty(roleDataPermission.getProductDeviceIds()));
    }


    /**
     * - 是否有factory数据权限
     * @return
     */
    public static boolean hasFactoryDataPermission() {
        return needProcDataPermission() && !noDataPermission() && getRoleDataPermissions().stream().anyMatch(roleDataPermission -> org.apache.commons.collections4.CollectionUtils.isNotEmpty(roleDataPermission.getFactorys()));
    }

    /**
     * - 设置数据权限信息到上下文
     * added 20240911 by wt for 数据权限V2
     */
    public static void setDataPermission(DataPermissionInfoV2 dataPermissionInfoV2) {
        dataPermissionV2ThreadLocal.set(dataPermissionInfoV2);
    }

    /**
     * - 从上下文获取数据权限信息
     * added 20240911 by wt for 数据权限V2
     */
    public static DataPermissionInfoV2 getDataPermission() {
        return dataPermissionV2ThreadLocal.get();
    }

    /**
     * - 是否开启了数据权限控制
     * added 20240912 by wt for 数据权限V2
     */
    public static boolean openDataPermission() {
        DataPermissionInfoV2 dataPermissionInfoV2 = getDataPermission();
        return null != dataPermissionInfoV2 && dataPermissionInfoV2.getAccessSwitch();
    }

    /**
     * - 是否需要处理数据权限
     * added 20240911 by wt for 数据权限V2
     */
    public static boolean needProcDataPermission() {
        DataPermissionInfoV2 dataPermissionInfoV2 = getDataPermission();

        // 如果上下文没有数据权限信息，不需要处理数据权限
        if (null == dataPermissionInfoV2) {
            return false;
        }

        // 如果没开数据权限，不需要处理数据权限
        Boolean open = dataPermissionInfoV2.getAccessSwitch();
        if (null == open || !open) {
            return false;
        }

        // 如果是管理员，不需要处理数据权限
        Boolean admin = dataPermissionInfoV2.getAdmin();
        if (null != admin && admin) {
            return false;
        }

        return true;
    }

    /**
     * - 是否完全没有数据权限
     * - 当开启了数据权限，不是管理员，并且没有任何数据角色的时候，就完全没有数据全
     * added 20240911 by wt for 数据权限V2
     */
    public static boolean noDataPermission() {
        return needProcDataPermission() && CollectionUtils.isEmpty(getDataPermission().getRoleDataPermissionInfos());
    }

    /**
     * - 获取角色数据权限列表
     * added 20240911 by wt for 数据权限V2
     */
    public static List<DataPermissionInfoV2.RoleDataPermissionInfo> getRoleDataPermissions() {
        return needProcDataPermission() ? getDataPermission().getRoleDataPermissionInfos() : null;
    }

    public static void clear() {
        userInfoThreadLocal.remove();
        dataPermissionV2ThreadLocal.remove();
    }

    public static Boolean isSystem() {
        UserInfo userInfo = getUserInfo();
        return null != userInfo && userInfo.isSystem();
    }

    public static Boolean isSystemWithUserInfo() {
        UserInfo userInfo = getUserInfo();
        return null != userInfo && userInfo.isSystemWithUserInfo();
    }

    public static String getAppId() {
        UserInfo userInfo = getUserInfo();
        return null == userInfo ? Constant.EMPTY : userInfo.getAppId();
    }

    public static Boolean isAdmin() {
        UserInfo userInfo = getUserInfo();
        return null != userInfo && userInfo.isAdmin();
    }

    public static Long getUserId() {
        UserInfo userInfo = getUserInfo();
        return null == userInfo ? -1 : userInfo.getUserId();
    }

    public static String getUserName() {
        UserInfo userInfo = getUserInfo();
        return null == userInfo ? Constant.UNKNOWN : userInfo.getUserName();
    }

    public static String getCustomer() {
        UserInfo userInfo = getUserInfo();
        return null == userInfo ? Constant.UNKNOWN : StringUtils.defaultIfBlank(userInfo.getCustomer(), Constant.EMPTY);
    }

    public static String getSubCustomer() {
        UserInfo userInfo = getUserInfo();
        return null == userInfo ? Constant.UNKNOWN : StringUtils.defaultIfBlank(userInfo.getSubCustomer(), Constant.EMPTY);
    }

    public static String getDepartment() {
        UserInfo userInfo = getUserInfo();
        return null == userInfo ? Constant.UNKNOWN : userInfo.getDepartment();
    }

    public static String getToken() {
        UserInfo userInfo = getUserInfo();
        return null == userInfo ? Constant.EMPTY : StringUtils.defaultIfBlank(userInfo.getToken(), Constant.EMPTY);
    }

    public static List<String> getSubCustomers() {
        List<DataPermissionInfoV2.RoleDataPermissionInfo> roleDataPermissions = getRoleDataPermissions();
        if (CollectionUtils.isEmpty(roleDataPermissions)) {
            return null;
        }
        List<String> subCustomers = roleDataPermissions.stream()
                .filter(t -> CollectionUtils.isNotEmpty(t.getSubCustomers()))
                .flatMap(t -> t.getSubCustomers().stream())
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(subCustomers)) {
            return null;
        }
        return subCustomers;
    }
}
