package com.guwave.datahub.bpms.app.service.bz.handler.clearRulePreview.preHandler;

import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model.ConfResult;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model.MetaData;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model.ParseField;
import com.guwave.datahub.bpms.dao.domain.clearRulePreview.ClearRulePreviewRecord;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.parser.stdf.visitor.impl.StdfMetaDataVisitor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Map;

@Component
public class FtStdfPreHandler extends FtPreHandler {

    @Override
    public boolean isSuppport(ClearRulePreviewRecord record) {
        return record.getGivenTestArea() == TestArea.FT && record.getFileCategory() == FileCategory.STDF;
    }

    @Override
    public void fillMetaData(ClearRulePreviewRecord record, File file, MetaData metaData) throws Exception {
        StdfMetaDataVisitor stdfMetaDataVisitor = new StdfMetaDataVisitor(file.getAbsolutePath());

        record.setDeviceId(convertNullStr(stdfMetaDataVisitor.getPartTyp()));
        record.setOriginStartT(convertNullStr(stdfMetaDataVisitor.getStartT()));
        record.setOriginFinishT(convertNullStr(stdfMetaDataVisitor.getFinishT()));
        // startT 相比 originStartT 默认减8小时
        record.setStartT(StringUtils.isBlank(stdfMetaDataVisitor.getStartT()) ? Constant.EMPTY : Long.parseLong(stdfMetaDataVisitor.getStartT()) - 8 * 3600L + "");
        record.setFinishT(StringUtils.isBlank(stdfMetaDataVisitor.getFinishT()) ? Constant.EMPTY : Long.parseLong(stdfMetaDataVisitor.getFinishT()) - 8 * 3600L + "");
        record.setTestCod(convertNullStr(stdfMetaDataVisitor.getTestCod()));
        record.setFloorId(convertNullStr(stdfMetaDataVisitor.getFloorId()));
        record.setTestTemperature(convertNullStr(stdfMetaDataVisitor.getTstTemp()));
        record.setTesterName(convertNullStr(stdfMetaDataVisitor.getNodeNam()));
        record.setTesterType(convertNullStr(stdfMetaDataVisitor.getTstrTyp()));
        record.setProbecardLoadboardId(convertNullStr(stdfMetaDataVisitor.getLoadId()));
        record.setTestProgram(convertNullStr(stdfMetaDataVisitor.getJobNam()));
        record.setTestProgramVersion(convertNullStr(stdfMetaDataVisitor.getJobRev()));
        record.setPkgTyp(convertNullStr(stdfMetaDataVisitor.getPkgTyp()));
        record.setBatchNum(0);


        metaData.setStartT(record.getOriginStartT());
        metaData.setFinishT(record.getOriginFinishT());
        metaData.setPartTyp(convertNullStr(stdfMetaDataVisitor.getPartTyp()));
        metaData.setLotId(convertNullStr(stdfMetaDataVisitor.getLotId()));
        metaData.setSblotId(convertNullStr(stdfMetaDataVisitor.getSblotId()));
        metaData.setWaferId(convertNullStr(stdfMetaDataVisitor.getWaferId()));
        metaData.setFlowId(convertNullStr(stdfMetaDataVisitor.getFlowId()));
        metaData.setRtstCod(convertNullStr(stdfMetaDataVisitor.getRtstCod()));
        metaData.setTestCod(convertNullStr(stdfMetaDataVisitor.getTestCod()));
        metaData.setSetupId(convertNullStr(stdfMetaDataVisitor.getSetupId()));
        metaData.setFloorId(convertNullStr(stdfMetaDataVisitor.getFloorId()));
        metaData.setTstTemp(convertNullStr(stdfMetaDataVisitor.getTstTemp()));
        metaData.setNodeNam(convertNullStr(stdfMetaDataVisitor.getNodeNam()));
        metaData.setTstrTyp(convertNullStr(stdfMetaDataVisitor.getTstrTyp()));
        metaData.setLoadId(convertNullStr(stdfMetaDataVisitor.getLoadId()));
        metaData.setCardId(convertNullStr(stdfMetaDataVisitor.getCardId()));
        metaData.setJobNam(convertNullStr(stdfMetaDataVisitor.getJobNam()));
        metaData.setJobRev(convertNullStr(stdfMetaDataVisitor.getJobRev()));
        metaData.setPkgTyp(convertNullStr(stdfMetaDataVisitor.getPkgTyp()));
        metaData.setUserTxt(convertNullStr(stdfMetaDataVisitor.getUserTxt()));

    }
}
