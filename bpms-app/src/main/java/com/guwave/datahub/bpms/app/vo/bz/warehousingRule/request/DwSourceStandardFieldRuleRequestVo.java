package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(title = "关键字段取值规则")
public class DwSourceStandardFieldRuleRequestVo {
    @Schema(title = "id")
    private Long id;

    @Schema(description = "子客户", example = "GUWAVE")
    private String subCustomer;

    @Schema(description = "deviceId", example = "DDR5-001")
    private String deviceId;

    @Schema(description = "测试阶段类型: CP/FT/WAT/SLT/EQC 等", example = "CP")
    private String testArea;

    @Schema(description = "测试工厂名称", example = "TSMC")
    private String factory;

    @Schema(description = "测试子工厂", example = "TSMC")
    private String factorySite;

    @Schema(description = "metaData中的字段名称", example = "waferNo")
    private String field;

    @Schema(description = "js执行代码", example = "result = 25")
    private String ruleExpression;

}
