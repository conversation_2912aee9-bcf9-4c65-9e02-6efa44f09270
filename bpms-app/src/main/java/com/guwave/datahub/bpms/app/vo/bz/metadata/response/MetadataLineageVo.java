package com.guwave.datahub.bpms.app.vo.bz.metadata.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class MetadataLineageVo {

    @Schema(title = "ID", example = "22")
    private Long id;

    @Schema(title = "起点数据源", example = "mysql")
    private String from_source;

    @Schema(title = "起点库名", example = "onedata")
    private String fromDatabase;

    @Schema(title = "起点表名", example = "dc_device_info")
    private String fromTable;

    @Schema(title = "起点字段名", example = "ecid_cp_stage_id")
    private String fromField;

    @Schema(title = "终点数据源", example = "clickhouse")
    private String toSource;

    @Schema(title = "终点库名", example = "ods")
    private String toDatabase;

    @Schema(title = "终点表名", example = "ods_dc_device_info_cluster")
    private String toTable;

    @Schema(title = "终点字段名", example = "ECID_CP_STAGE_ID")
    private Integer toField;
}
