package com.guwave.datahub.bpms.app.vo.bz.qualityRepair.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.guwave.datahub.bpms.dao.domain.repair.DataRepairHistory;
import com.guwave.datahub.bpms.dao.domain.repair.DataRepairInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class DataQualityBatchRepairInfoVo {

    @Schema(title = "id", example = "123")
    private Long id;

    @Schema(title = "customer", example = "guwave")
    private String customer;

    @Schema(title = "subCustomer", example = "guwave")
    private String subCustomer;

    @Schema(title = "处理批次",  example = "123")
    private Long repairBatchId;

    @Schema(title = "fileId", example = "1001")
    private Long fileId;

    @Schema(title = "文件名称")
    private String fileName;

    @Schema(title = "waferId")
    private String waferId;

    @Schema(title = "testArea")
    private String testArea;

    @Schema(title = "修改类别")
    private String repairCategory;

    @Schema(title = "修改类型")
    private String lastRepairType;

    @Schema(title = "修改状态")
    private String repairStatus;

    @Schema(title = "修改异常信息")
    private String repairErrorInfo;

    @Schema(title = "修改次数")
    private Integer repairTimes;

    @Schema(title = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @Schema(title = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @Schema(title = "remark")
    private String remark;

    @Schema(title = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(title = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Schema(title = "创建用户")
    private String createUser;

    @Schema(title = "修改用户")
    private String updateUser;

    @Schema(title = "DeviceId")
    private String modifyDeviceId;

    @Schema(title = "TestStage")
    private String modifyTestStage;

    @Schema(title = "LotId")
    private String modifyLotId;

    @Schema(title = "SblotId")
    private String modifySblotId;

    @Schema(title = "TestProgram")
    private String modifyTestProgram;

    @Schema(title = "LotType")
    private String modifyLotType;

    @Schema(title = "RetestHbinNumber")
    private String modifyRetestHbinNumber;

    @Schema(title = "FlowId")
    private String modifyFlowId;

    public static DataQualityBatchRepairInfoVo of(DataRepairInfo info) {
        DataQualityBatchRepairInfoVo vo = new DataQualityBatchRepairInfoVo();
        vo.setId(info.getId());
        vo.setCustomer(info.getCustomer());
        vo.setSubCustomer(info.getSubCustomer());
        vo.setRepairBatchId(info.getRepairBatchId());
        vo.setFileId(info.getFileId());
        vo.setFileName(info.getFileName());
        vo.setWaferId(info.getWaferId());
        vo.setTestArea(info.getTestArea());
        vo.setRepairCategory(info.getRepairCategory());
        vo.setLastRepairType(info.getLastRepairType());
        vo.setRepairStatus(info.getRepairStatus());
        vo.setRepairErrorInfo(info.getRepairErrorInfo());
        vo.setRepairTimes(info.getRepairTimes());
        vo.setStartTime(info.getStartTime());
        vo.setEndTime(info.getEndTime());
        vo.setRemark(info.getRemark());
        vo.setCreateTime(info.getCreateTime());
        vo.setUpdateTime(info.getUpdateTime());
        vo.setCreateUser(info.getCreateUser());
        vo.setUpdateUser(info.getUpdateUser());
        vo.setModifyDeviceId(info.getModifyDeviceId());
        vo.setModifyTestStage(info.getModifyTestStage());
        vo.setModifyLotId(info.getModifyLotId());
        vo.setModifySblotId(info.getModifySblotId());
        vo.setModifyTestProgram(info.getModifyTestProgram());
        vo.setModifyLotType(info.getModifyLotType());
        vo.setModifyRetestHbinNumber(info.getModifyRetestHbinNumber());
        vo.setModifyFlowId(info.getModifyFlowId());
        return vo;
    }

    public static DataQualityBatchRepairInfoVo of(DataRepairHistory history){
        DataQualityBatchRepairInfoVo vo = new DataQualityBatchRepairInfoVo();
        vo.setId(history.getId());
        vo.setCustomer(history.getCustomer());
        vo.setSubCustomer(history.getSubCustomer());
        vo.setRepairBatchId(history.getRepairBatchId());
        vo.setFileId(history.getFileId());
        vo.setFileName(history.getFileName());
        vo.setWaferId(history.getWaferId());
        vo.setTestArea(history.getTestArea());
        vo.setRepairCategory(history.getRepairCategory());
        vo.setLastRepairType(history.getLastRepairType());
        vo.setRepairStatus(history.getRepairStatus());
        vo.setRepairErrorInfo(history.getRepairErrorInfo());
        vo.setRepairTimes(history.getRepairTimes());
        vo.setCreateTime(history.getCreateTime());
        vo.setUpdateTime(history.getUpdateTime());
        vo.setCreateUser(history.getCreateUser());
        vo.setUpdateUser(history.getUpdateUser());
        vo.setModifyDeviceId(history.getModifyDeviceId());
        vo.setModifyTestStage(history.getModifyTestStage());
        vo.setModifyLotId(history.getModifyLotId());
        vo.setModifySblotId(history.getModifySblotId());
        vo.setModifyTestProgram(history.getModifyTestProgram());
        vo.setModifyLotType(history.getModifyLotType());
        vo.setModifyRetestHbinNumber(history.getModifyRetestHbinNumber());
        vo.setModifyFlowId(history.getModifyFlowId());
        return vo;
    }
}