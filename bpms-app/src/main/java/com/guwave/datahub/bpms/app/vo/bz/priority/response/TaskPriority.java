package com.guwave.datahub.bpms.app.vo.bz.priority.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(description = "任务基础信息")
public class TaskPriority {
    @Schema(description = "任务ID")
    private Long id;

    @Schema(description = "排名")
    private Long Rank;

    @Schema(description = "优先级")
    private String priority;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
}
