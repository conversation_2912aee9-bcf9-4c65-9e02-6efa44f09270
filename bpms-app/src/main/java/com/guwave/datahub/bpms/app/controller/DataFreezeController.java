package com.guwave.datahub.bpms.app.controller;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.service.bz.DataFreezeService;
import com.guwave.datahub.bpms.app.vo.bz.stateManage.response.StateManageDetailVo;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Log
@Auth
//@License
@Validated
@Slf4j
@RestController
@AllArgsConstructor
@Tag(name = "DataFreezeControllerAPI", description = "数据冻结接口",
        externalDocs = @ExternalDocumentation(description = "需求文档",
                url = "")
)
@RequestMapping("dataFreeze")
public class DataFreezeController {

    private DataFreezeService dataFreezeService;

    @Auth
    @Operation(summary = "冻结testItem数据")
    @PostMapping("freezeTestItem")
    public ResponseVo<StateManageDetailVo> freezeTestItem() {
        dataFreezeService.deleteCk();
        return ResponseVo.ok();
    }

    @Auth
    @Operation(summary = "从磁盘删除ck数据")
    @PostMapping("deleteCkDataFromDisk")
    public ResponseVo<Void> deleteCkDataFromDisk() {
        dataFreezeService.deleteCkDataFromDisk();
        return ResponseVo.ok();
    }

}
