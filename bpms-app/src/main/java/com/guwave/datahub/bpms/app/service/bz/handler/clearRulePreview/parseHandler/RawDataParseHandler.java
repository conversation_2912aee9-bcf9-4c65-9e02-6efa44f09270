package com.guwave.datahub.bpms.app.service.bz.handler.clearRulePreview.parseHandler;

import com.guwave.datahub.bpms.app.service.bz.handler.clearRulePreview.visitor.MultiThreadVisitor;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model.ClearRulePreviewResult;
import com.guwave.datahub.bpms.dao.domain.clearRulePreview.ClearRulePreviewRecord;
import com.guwave.onedata.dataware.parser.stdf.ecidParse.EcidRuleSetting;
import com.guwave.onedata.dataware.parser.stdf.serialization.RawDataReader;
import com.guwave.onedata.dataware.parser.stdf.uidParse.UidRuleSetting;
import com.guwave.onedata.dataware.parser.stdf.visitor.Visitor;
import com.guwave.onedata.dataware.sdk.model.ecid.CpLotIdInfo;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

@Component
public abstract class RawDataParseHandler extends ParseHandler {
    @Override
    protected List<Visitor> parseData(ClearRulePreviewRecord record, File file, ClearRulePreviewResult clearRulePreviewResult, EcidRuleSetting ecidRuleSetting, String configCpLotId, UidRuleSetting uidRuleSetting, CpLotIdInfo cpLotIdInfo) {
        List<Visitor> visitors = new ArrayList<>();
        int allThreadCnt = 1;
        for (int i = 1; i <= allThreadCnt; i++) {
            visitors.add(new MultiThreadVisitor(i, generateFillFileMainDataConsumer(record), record, ecidRuleSetting, configCpLotId, uidRuleSetting, cpLotIdInfo));
        }

        try {
            Visitor visitor = visitors.get(0);
            new RawDataReader(file.getAbsolutePath(), visitor, false);
        } finally {
            visitors.forEach(t -> {
                MultiThreadVisitor visitor = (MultiThreadVisitor) t;
                visitor.close();
            });
        }
        return visitors;
    }
}
