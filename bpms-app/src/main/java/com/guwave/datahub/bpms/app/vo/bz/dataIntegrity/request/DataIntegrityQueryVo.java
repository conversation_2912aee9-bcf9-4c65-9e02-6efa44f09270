package com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.request;


import com.google.common.collect.Lists;
import com.guwave.datahub.bpms.app.service.bz.DataIntegrityService;
import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import com.guwave.onedata.dataware.common.contant.Constant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
@EqualsAndHashCode(callSuper = false)
@Schema(title = "数据完整性查询条件")
public class DataIntegrityQueryVo extends PageableRequestVo {


    @Schema(description = " testArea列表", example = "[\"CP\",\"FT\"]")
    private List<String> testAreaList;

    @Schema(description = "fileCategory列表", example = "[\"STDF\",\"RAW_DATA\"]")
    private List<String> fileCategoryList;

    @Schema(description = "remoteFileMtime起始时间", example = "2024-04-03 00:00:00")
    private String startTime;

    @Schema(description = "remoteFileMtime结束时间", example = "2024-04-11 23:59:59")
    private String endTime;

    @Schema(description = "subCustomer列表", example = "[\"SUBCUS1\",\"SUBCUS2\"]")
    private List<String> subCustomerList;

    @Schema(description = "factory列表", example = "[\"FAC1\",\"FAC2\"]")
    private List<String> factoryList;

    @Schema(description = "deviceId列表", example = "[\"DEV1\",\"DEV2\"]")
    private List<String> deviceIdList;

    @Schema(description = "testStage列表", example = "[\"STAGE1\",\"STAGE2\"]")
    private List<String> testStageList;

    @Schema(description = "lotType列表", example = "[\"TYP1\",\"TYP2\"]")
    private List<String> lotTypeList;

    @Schema(description = "lotId列表", example = "[\"LOT1\",\"LOT2\"]")
    private List<String> lotIdList;

    @Schema(description = "sblotId列表", example = "[\"SBLOT1\",\"SBLOT2\"]")
    private List<String> sblotIdList;

    @Schema(description = "waferId列表", example = "[\"WF1\",\"WF2\"]")
    private List<String> waferIdList;

    @Schema(description = "fileLoaded列表", example = "[\"N\",\"Y\"]")
    private List<String> dataLoadedList;

    @Schema(description = "fileLoadingFailedType列表", example = "[\"Other\",\"Exc1\"]")
    private List<String> loadingFailedTypeList;

    @Schema(description = "testProgram列表", example = "[\"Prog1\",\"Prog2\"]")
    private List<String> testProgramList;

    @Schema(description = "fileLocation", example = "/data1/upload/a.stdf")
    private String fileLocation;

    @Schema(description = "fileName", example = "a.stdf.zip")
    private String fileName;

    @Schema(description = "排序字段", example = "fileName")
    private String orderByField;

    @Schema(description = "排序方式", example = "ASC")
    private String orderByType;

    @Schema(description = "dc file info id列表", example = "[\"1\",\"2\"]")
    private List<Long> fileIdList;

    @Schema(description = "fileName列表", example = "[\"a.stdf.zip\",\"b.stdf.zip\"]")
    private List<String> fileNameList;


    public void validCondition(boolean exportFlag) {
        startTime = convertEmpty(startTime);
        endTime = convertEmpty(endTime);
        subCustomerList = convertEmpty(subCustomerList);
        factoryList = convertEmpty(factoryList);
        testAreaList = convertEmpty(testAreaList);
        deviceIdList = convertEmpty(deviceIdList);
        testStageList = convertEmpty(testStageList);
        lotTypeList = convertEmpty(lotTypeList);
        lotIdList = convertEmpty(lotIdList);
        sblotIdList = convertEmpty(sblotIdList);
        waferIdList = convertEmpty(waferIdList);
        dataLoadedList = convertEmpty(dataLoadedList);
        loadingFailedTypeList = convertEmpty(loadingFailedTypeList);
        testProgramList = convertEmpty(testProgramList);
        fileLocation = convertEmpty(fileLocation);
        fileName = convertEmpty(fileName);
        orderByField = convertEmpty(orderByField);
        orderByType = convertEmpty(orderByType);
        fileNameList = convertEmpty(fileNameList);

        if (CollectionUtils.isEmpty(testAreaList)) {
            testAreaList = Lists.newArrayList(DataIntegrityService.ALL_TEST_AREAS);
        }
        if (CollectionUtils.isEmpty(fileCategoryList)) {
            fileCategoryList = Lists.newArrayList(DataIntegrityService.ALL_FILE_CATEGORYS);
        }
        if (CollectionUtils.isNotEmpty(loadingFailedTypeList)) {
            loadingFailedTypeList = loadingFailedTypeList.stream()
                    .flatMap(t -> Arrays.stream(t.split(Constant.COMMA)))
                    .collect(Collectors.toList());
        }
        if (StringUtils.isEmpty(orderByField)) {
            orderByField = "updateTime";
        }
        if (StringUtils.isEmpty(orderByType)) {
            orderByType = "DESC";
        }
        orderByType = orderByType.toLowerCase();
        if (exportFlag) {
            orderByField = "id";
        }
    }

    private String convertEmpty(String str) {
        if (StringUtils.isEmpty(str)) {
            return null;
        } else {
            return str;
        }
    }

    private List<String> convertEmpty(List<String> strs) {
        if (CollectionUtils.isEmpty(strs)) {
            return null;
        } else {
            return strs;
        }
    }
}
