package com.guwave.datahub.bpms.app.service.bz;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.fastjson.JSONObject;
import com.guwave.datahub.bpms.app.decompress.impl.ZipUncompressHandler;
import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.util.DateUtil;
import com.guwave.datahub.bpms.app.util.ExcelUtil;
import com.guwave.datahub.bpms.app.util.FileUtil;
import com.guwave.datahub.bpms.app.util.HdfsUtil;
import com.guwave.datahub.bpms.app.vo.bz.warehousingRule.response.DropDownVo;
import com.guwave.datahub.bpms.app.vo.bz.warehousingRule.model.*;
import com.guwave.datahub.bpms.app.vo.bz.warehousingRule.request.*;
import com.guwave.datahub.bpms.app.vo.bz.warehousingRule.response.*;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.common.constant.OperationType;
import com.guwave.datahub.bpms.common.constant.WarehousingConfigurePageDropdownField;
import com.guwave.datahub.bpms.common.constant.WarehousingRuleType;
import com.guwave.datahub.bpms.dao.domain.warehousingRule.ModifyWarehousingRuleRecord;
import com.guwave.datahub.bpms.dao.domain.warehousingRule.WarehousingRuleFlow;
import com.guwave.datahub.bpms.dao.repository.bpms.ModifyWarehousingRuleRecordRepository;
import com.guwave.datahub.bpms.dao.repository.bpms.WarehousingRuleFlowRepository;
import com.guwave.datahub.bpms.dao.repository.dw.*;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.ArchiveInputStream;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;
import java.util.stream.IntStream;

import static com.guwave.datahub.bpms.common.constant.Constant.*;
import static com.guwave.datahub.bpms.common.constant.Constant.COMMA;
import static com.guwave.datahub.bpms.common.constant.WarehousingRuleType.BzWarehousingRuleFlow;
import static com.guwave.onedata.dataware.common.contant.Constant.EMPTY;
import static com.guwave.onedata.dataware.common.contant.Constant.SLASH;
import static com.guwave.onedata.dataware.common.contant.Constant.*;

/**
 * 2025/3/4 14:26
 * WarehousingRuleService
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class WarehousingRuleService {

    @Autowired
    private CustomRepository customRepository;
    @Autowired
    private SftpSubCustomerRuleRepository sftpSubCustomerRuleRepository;
    @Autowired
    private FileRenameRuleRepository fileRenameRuleRepository;
    @Autowired
    private DataClearRuleRepository dataClearRuleRepository;
    @Autowired
    private SftpFileConvertScriptRepository sftpFileConvertScriptRepository;
    @Autowired
    private SourceStandardDeviceIdRuleRepository sourceStandardDeviceIdRuleRepository;
    @Autowired
    private SourceStandardFieldRuleRepository sourceStandardFieldRuleRepository;
    @Autowired
    private ModifyWarehousingRuleRecordRepository modifyWarehousingRuleRecordRepository;
    @Autowired
    private WarehousingRuleFlowRepository warehousingRuleFlowRepository;

    @Autowired
    private WarehousingRuleService warehousingRuleService;

    @Autowired
    private HdfsUtil hdfsUtil;

    @Value("${spring.handler.file.confReadPath}")
    private String confReadPath;

    @Value("${spring.hdfs.adapterDir}")
    private String adapterDir;

    private final String EXCEL_FILE_SUF = ".xlsx";

    private final String ZIP_FILE_ALREADY_EXISTS = "exist";

    /**
     * 根据入库规则下拉筛选条件获取对应字段的下拉选项数据
     *
     * @param warehouseRuleDropDownFilterVo 入库规则下拉框过滤条件参数对象，包含需要过滤的字段信息
     * @return List<DropDownVo> 返回对应字段的下拉选项列表，当字段不存在时返回空列表
     */
    public List<DropDownVo> filterWarehousingRuleDropDown(WarehousingRuleDropDownFilterVo warehouseRuleDropDownFilterVo) {
        // 获取当前请求的过滤字段枚举
        WarehousingConfigurePageDropdownField filterField = warehouseRuleDropDownFilterVo.getFilterField();
        List<String> dropDownResult;

        if (filterField == null) {
            // 抛出异常处理未支持的过滤字段类型
            log.info("不支持的过滤字段：{}", filterField);
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, filterField);
        } else {
            String field = filterField.getField();
            log.info("获取{}列表", field);
            dropDownResult = warehousingRuleFlowRepository.findDynamicField(RequestContext.getCustomer(), field)
                    .stream()
                    .distinct()
                    .collect(Collectors.toList());
            log.info("获取{}列表，dropDownResult:{}", field, dropDownResult);
        }
        log.info("下拉框结果 field:{}, value:{}", filterField, String.join(Constant.COMMA, dropDownResult));
        return DropDownVo.ofList(dropDownResult);
    }


    /**
     * 查询子客户目录规则
     *
     * @param warehousingRuleQueryVo 包含查询条件和分页参数的值对象
     * @return 分页数据视图对象，包含转换后的规则VO列表
     */
    public PageableDataVo<DwSftpSubCustomerRuleVo> getDwSftpSubCustomerRule(WarehousingRuleQueryVo warehousingRuleQueryVo) {
        Pageable pageable = PageRequest.of(warehousingRuleQueryVo.getJpaPageIndex(), warehousingRuleQueryVo.getPageSize());

        List<String> testAreaList = warehousingRuleQueryVo.getTestAreaList();
        List<String> testScopes = testAreaList == null ? null : testAreaList.stream().map(TestArea::of).filter(Objects::nonNull).map(TestArea::getTestScope).collect(Collectors.toList());
        testScopes = CollectionUtils.isEmpty(testScopes) ? testAreaList : testScopes;

        Page<SftpSubCustomerRule> sftpSubCustomerRulePage = sftpSubCustomerRuleRepository.findAllByCustomerAndSubCustomerInAndFactoryInAndTestAreaInOrderByUpdateTimeDesc(
                RequestContext.getCustomer(),
                warehousingRuleQueryVo.getSubCustomerList(),
                warehousingRuleQueryVo.getFactoryList(),
                testScopes,
                pageable
        );

        return PageableDataVo.of(sftpSubCustomerRulePage.map(DwSftpSubCustomerRuleVo::of));
    }

    /**
     * 查询文件重命名规则
     *
     * @param warehousingRuleQueryVo 包含查询条件和分页参数的值对象
     * @return 分页数据视图对象，包含转换后的重命名规则VO列表
     */
    public PageableDataVo<DwSftpFileRenameRuleVo> getDwSftpFileRenameRule(WarehousingRuleQueryVo warehousingRuleQueryVo) {
        Pageable pageable = PageRequest.of(warehousingRuleQueryVo.getJpaPageIndex(), warehousingRuleQueryVo.getPageSize());

        Page<FileRenameRule> fileRenameRulePage = fileRenameRuleRepository.findAllByCustomerAndSubCustomerInOrderByUpdateTimeDesc(
                RequestContext.getCustomer(),
                warehousingRuleQueryVo.getSubCustomerList(),
                pageable
        );

        return PageableDataVo.of(fileRenameRulePage.map(DwSftpFileRenameRuleVo::of));
    }

    /**
     * 查询特殊逻辑配置规则
     *
     * @param warehousingRuleQueryVo 包含查询条件和分页参数的值对象
     * @return 分页数据视图对象，包含转换后的数据清除规则VO列表
     */
    public PageableDataVo<DwDataClearRuleVo> getDwDataClearRule(WarehousingRuleQueryVo warehousingRuleQueryVo) {
        Pageable pageable = PageRequest.of(warehousingRuleQueryVo.getJpaPageIndex(), warehousingRuleQueryVo.getPageSize());

        Page<DataClearRule> dataClearRulePage = dataClearRuleRepository.findAllByCustomerAndSubCustomerInAndFactoryInAndTestAreaInOrderByUpdateTimeDesc(
                RequestContext.getCustomer(),
                warehousingRuleQueryVo.getSubCustomerList(),
                warehousingRuleQueryVo.getFactoryList(),
                warehousingRuleQueryVo.getTestAreaList(),
                pageable
        );

        return PageableDataVo.of(dataClearRulePage.map(DwDataClearRuleVo::of));
    }

    /**
     * 查询解析脚本配置规则
     *
     * @param warehousingRuleQueryVo 包含查询条件和分页参数的值对象
     * @return 分页数据包装对象，包含转换后的VO列表和分页信息
     */
    public PageableDataVo<DwSftpFileConvertScriptVo> getDwSftpFileConvertScript(WarehousingRuleQueryVo warehousingRuleQueryVo) {
        Pageable pageable = PageRequest.of(warehousingRuleQueryVo.getJpaPageIndex(), warehousingRuleQueryVo.getPageSize());

        List<String> testAreaList = warehousingRuleQueryVo.getTestAreaList();
        List<String> testScopes = testAreaList == null ? null : testAreaList.stream().map(TestArea::of).filter(Objects::nonNull).map(TestArea::getTestScope).collect(Collectors.toList());
        testScopes = CollectionUtils.isEmpty(testScopes) ? testAreaList : testScopes;

        Page<SftpFileConvertScript> sftpFileConvertScriptPage = sftpFileConvertScriptRepository.findAllByCustomerAndSubCustomerInAndFactoryInAndTestAreaInAndFileCategoryInOrderByUpdateTimeDesc(
                RequestContext.getCustomer(),
                warehousingRuleQueryVo.getSubCustomerList(),
                warehousingRuleQueryVo.getFactoryList(),
                testScopes,
                warehousingRuleQueryVo.getFileCategoryList(),
                pageable
        );

        return PageableDataVo.of(sftpFileConvertScriptPage.map(DwSftpFileConvertScriptVo::of));
    }

    /**
     * 查询DEVICE_ID取值规则
     *
     * @param warehousingRuleQueryVo 包含查询条件和分页参数的值对象
     * @return 分页数据包装对象，包含转换后的VO列表和分页信息
     */
    public PageableDataVo<DwSourceStandardDeviceIdRuleVo> getDwSourceStandardDeviceIdRule(WarehousingRuleQueryVo warehousingRuleQueryVo) {
        Pageable pageable = PageRequest.of(warehousingRuleQueryVo.getJpaPageIndex(), warehousingRuleQueryVo.getPageSize());

        List<String> testAreaList = warehousingRuleQueryVo.getTestAreaList();
        List<String> testScopes = testAreaList == null ? null : testAreaList.stream().map(TestArea::of).filter(Objects::nonNull).map(TestArea::getTestScope).collect(Collectors.toList());
        testScopes = CollectionUtils.isEmpty(testScopes) ? testAreaList : testScopes;

        Page<SourceStandardDeviceIdRule> sourceStandardDeviceIdRulePage = sourceStandardDeviceIdRuleRepository.findAllByCustomerAndSubCustomerInAndFactoryInAndTestAreaInOrderByUpdateTimeDesc(
                RequestContext.getCustomer(),
                warehousingRuleQueryVo.getSubCustomerList(),
                warehousingRuleQueryVo.getFactoryList(),
                testScopes,
                pageable
        );
        if (sourceStandardDeviceIdRulePage.getTotalElements() > 1) {
            log.error("查询到多条DEVICE_ID取值规则");
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "查询到多条DEVICE_ID取值规则");
        }

        return PageableDataVo.of(sourceStandardDeviceIdRulePage.map(DwSourceStandardDeviceIdRuleVo::of));
    }

    /**
     * 查询关键字段取值规则
     *
     * @param warehousingRuleQueryVo 包含查询条件和分页参数的值对象
     * @return 分页数据包装对象，包含转换后的VO列表和分页信息
     */
    public PageableDataVo<DwSourceStandardFieldRuleVo> getDwSourceStandardFieldRule(WarehousingRuleQueryVo warehousingRuleQueryVo) {
        Pageable pageable = PageRequest.of(warehousingRuleQueryVo.getJpaPageIndex(), warehousingRuleQueryVo.getPageSize());

        List<String> testAreaList = warehousingRuleQueryVo.getTestAreaList();
        List<String> testScopes = testAreaList == null ? null : testAreaList.stream().map(TestArea::of).filter(Objects::nonNull).map(TestArea::getTestScope).collect(Collectors.toList());
        testScopes = CollectionUtils.isEmpty(testScopes) ? testAreaList : testScopes;

        Page<SourceStandardFieldRule> sourceStandardFieldRulePage = sourceStandardFieldRuleRepository.findAllByCustomerAndSubCustomerInAndFactoryInAndTestAreaInOrderByUpdateTimeDesc(
                RequestContext.getCustomer(),
                warehousingRuleQueryVo.getSubCustomerList(),
                warehousingRuleQueryVo.getFactoryList(),
                testScopes,
                pageable
        );
        if (sourceStandardFieldRulePage.stream().anyMatch(sourceStandardFieldRule -> StringUtils.isNotBlank(sourceStandardFieldRule.getDeviceId()))) {
            log.error("关键字段取值规则DEVICE_ID维度不为空");
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "关键字段取值规则DEVICE_ID维度不为空");
        }

        return PageableDataVo.of(sourceStandardFieldRulePage.map(DwSourceStandardFieldRuleVo::of));
    }

    // 保存子客户目录规则
    public void saveDwSftpSubCustomerRule(List<DwSftpSubCustomerRuleRequestVo> dwSftpSubCustomerRuleRequestVos, WarehousingRuleFlowRequestVo warehouseRuleFlowRequestVo) {
        if (warehouseRuleFlowRequestVo != null) {
            log.info("删除DwSftpSubCustomerRule");
            deleteDwSftpSubCustomerRule(dwSftpSubCustomerRuleRequestVos, warehouseRuleFlowRequestVo);
        }
        String customer = RequestContext.getCustomer();

        for (DwSftpSubCustomerRuleRequestVo requestVo : dwSftpSubCustomerRuleRequestVos) {
            TestArea testAreaEnum = TestArea.of(requestVo.getTestArea());
            String testArea = testAreaEnum == null ? EMPTY : testAreaEnum.getTestScope();

            SftpSubCustomerRule rule = new SftpSubCustomerRule();
            // 检查 testArea 是否为空并转换
            if (testAreaEnum == null) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "testArea 不能为空或无效: " + testArea);
            }

            // 检查 ftp_ip、directory、sub_customer不为空
            if (StringUtils.isBlank(requestVo.getFtpIp())) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "ftpIp 不能为空");
            }
            if (StringUtils.isBlank(requestVo.getDirectory())) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "directory 不能为空");
            }
            if (StringUtils.isBlank(requestVo.getSubCustomer())) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "subCustomer 不能为空");
            }
            if (StringUtils.isBlank(requestVo.getFactorySite())) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "factorySite 不能为空");
            }

            rule.setCustomer(customer)
                    .setSubCustomer(requestVo.getSubCustomer())
                    .setFactory(requestVo.getFactory())
                    .setFactorySite(requestVo.getFactorySite())
                    .setTestArea(TestArea.of(testArea))
                    .setFtpIp(requestVo.getFtpIp())
                    .setDirectory(requestVo.getDirectory());

            // 判断更新或新增, 并记录到 modify_warehousing_rule_record 表里
            Date now = new Date();
            Long ruleId = requestVo.getId();
            if (ruleId == null) {
                rule.setCreateUser(RequestContext.getUserName())
                        .setUpdateUser(RequestContext.getUserName())
                        .setCreateTime(now)
                        .setUpdateTime(now);
                ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.INSERT, WarehousingRuleType.DwSftpSubCustomerRule, EMPTY);
                modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
            } else {
                Optional<SftpSubCustomerRule> optionalOriginalRule = sftpSubCustomerRuleRepository.findById(ruleId);
                if (optionalOriginalRule.isPresent()) {
                    SftpSubCustomerRule originalRule = optionalOriginalRule.get();
                    String originalDataSnapshot = JSONObject.toJSONString(originalRule);
                    ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.UPDATE, WarehousingRuleType.DwSftpSubCustomerRule, originalDataSnapshot);
                    modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
                    rule.setId(ruleId)
                            .setUpdateUser(RequestContext.getUserName())
                            .setUpdateTime(now)
                            .setCreateUser(originalRule.getCreateUser())
                            .setCreateTime(originalRule.getCreateTime());
                } else {
                    rule.setCreateUser(RequestContext.getUserName())
                            .setUpdateUser(RequestContext.getUserName())
                            .setCreateTime(now)
                            .setUpdateTime(now);
                    ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.INSERT, WarehousingRuleType.DwSftpSubCustomerRule, EMPTY);
                    modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
                }
            }
            sftpSubCustomerRuleRepository.save(rule);
        }
    }

    // 保存文件重命名规则
    public void saveDwSftpFileRenameRule(List<DwSftpFileRenameRuleRequestVo> dwSftpFileRenameRuleRequestVos, WarehousingRuleFlowRequestVo warehouseRuleFlowRequestVo) {
        if (warehouseRuleFlowRequestVo != null) {
            log.info("删除DwSftpFileRenameRule");
            deleteDwSftpFileRenameRule(dwSftpFileRenameRuleRequestVos, warehouseRuleFlowRequestVo);
        }
        for (DwSftpFileRenameRuleRequestVo dwSftpFileRenameRuleRequestVo : dwSftpFileRenameRuleRequestVos) {
            FileRenameRule rule = new FileRenameRule();

            // 检查 ftpIp 是否为空
            if (StringUtils.isBlank(dwSftpFileRenameRuleRequestVo.getFtpIp())) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "ftpIp 不能为空");
            }

            // 检查 use_timestamp= 1 或 directoryNum 有值，有且只有一个满足条件
            Integer useTimestamp = dwSftpFileRenameRuleRequestVo.getUseTimestamp();
            Integer directoryNum = dwSftpFileRenameRuleRequestVo.getDirectoryNum();
            if ((Objects.equals(useTimestamp, 1) && directoryNum != null) ||
                    (!Objects.equals(useTimestamp, 1) && directoryNum == null)) {
                log.info("useTimestamp = 1 和 directoryNum != null 必须有且只有一个成立!useTimestamp:{},directoryNum:{}", useTimestamp, directoryNum);
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "useTimestamp = 1 和 directoryNum != null 必须有且只有一个成立");
            }

            // 检查 ruleExpression 是否为合法的正则表达式
            String ruleExpression = dwSftpFileRenameRuleRequestVo.getRuleExpression();
            if (StringUtils.isBlank(ruleExpression)) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "ruleExpression 不能为空");
            }
            try {
                java.util.regex.Pattern.compile(ruleExpression);
            } catch (java.util.regex.PatternSyntaxException e) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "ruleExpression 不是合法的正则表达式");
            }

            rule.setCustomer(RequestContext.getCustomer());
            rule.setSubCustomer(dwSftpFileRenameRuleRequestVo.getSubCustomer());
            rule.setFtpIp(dwSftpFileRenameRuleRequestVo.getFtpIp());
            rule.setRuleExpression(dwSftpFileRenameRuleRequestVo.getRuleExpression());
            rule.setUseTimestamp(dwSftpFileRenameRuleRequestVo.getUseTimestamp());
            rule.setDirectoryNum(dwSftpFileRenameRuleRequestVo.getDirectoryNum());

            // 判断更新或新增, 并记录到 modify_warehousing_rule_record 表里
            Date now = new Date();
            Long ruleId = dwSftpFileRenameRuleRequestVo.getId();
            if (ruleId == null) {
                rule.setCreateUser(RequestContext.getUserName());
                rule.setUpdateUser(RequestContext.getUserName());
                rule.setCreateTime(now);
                rule.setUpdateTime(now);
                ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.INSERT, WarehousingRuleType.DwSftpFileRenameRule, EMPTY);
                modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
            } else {
                Optional<FileRenameRule> optionalOriginalRule = fileRenameRuleRepository.findById(ruleId);
                if (optionalOriginalRule.isPresent()) {
                    FileRenameRule originalRule = optionalOriginalRule.get();
                    String originalDataSnapshot = JSONObject.toJSONString(originalRule);
                    ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.UPDATE, WarehousingRuleType.DwSftpFileRenameRule, originalDataSnapshot);
                    modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
                    rule.setId(ruleId);
                    rule.setUpdateUser(RequestContext.getUserName());
                    rule.setUpdateTime(now);
                    rule.setCreateUser(originalRule.getCreateUser());
                    rule.setCreateTime(originalRule.getCreateTime());
                } else {
                    rule.setCreateUser(RequestContext.getUserName());
                    rule.setUpdateUser(RequestContext.getUserName());
                    rule.setCreateTime(now);
                    rule.setUpdateTime(now);
                    ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.INSERT, WarehousingRuleType.DwSftpFileRenameRule, EMPTY);
                    modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
                }
            }
            fileRenameRuleRepository.save(rule);
        }
    }

    public void deleteDwSftpFileRenameRule(List<DwSftpFileRenameRuleRequestVo> dwSftpFileRenameRuleRequestVos, WarehousingRuleFlowRequestVo warehouseRuleFlowRequestVo) {
        String customer = RequestContext.getCustomer();
        String subCustomer = warehouseRuleFlowRequestVo.getSubCustomer();

        // 如果传入的 dwSftpSubCustomerRuleRequestVos 中的 customer, sub_customer 唯一且不为空
        // 找出全部的id 并删除其中不在传入id里的数据
        List<Long> ids = dwSftpFileRenameRuleRequestVos.stream().map(DwSftpFileRenameRuleRequestVo::getId).collect(Collectors.toList());
        List<Long> allIds = fileRenameRuleRepository.findAllIdsByCustomerAndSubCustomer(customer, subCustomer);
        if (!CollectionUtils.isEmpty(allIds)) {
            List<Long> deleteIds = allIds.stream().filter(id -> !ids.contains(id)).collect(Collectors.toList());
            // 找出要删除的id后,遍历 deleteIds获取数据并将删除记录到  modifyWarehousingRuleRecord
            for (Long id : deleteIds) {
                Optional<FileRenameRule> optionalOriginalRule = fileRenameRuleRepository.findById(id);
                FileRenameRule originalRule = optionalOriginalRule.get();
                String originalDataSnapshot = JSONObject.toJSONString(originalRule);
                ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.DELETE, WarehousingRuleType.DwSftpFileRenameRule, originalDataSnapshot);
                modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
            }
            if (!CollectionUtils.isEmpty(deleteIds)) {
                log.info("删除 dwSftpSubCustomerRule: {}", deleteIds);
                fileRenameRuleRepository.deleteAllByIds(deleteIds);
            }
        }
    }

    // 保存特殊逻辑配置规则
    public void saveDwDataClearRule(List<DwDataClearRuleRequestVo> dwDataClearRuleRequestVos, WarehousingRuleFlowRequestVo warehouseRuleFlowRequestVo) {
        if (warehouseRuleFlowRequestVo != null) {
            log.info("删除DwDataClearRule");
            deleteDwDataClearRule(dwDataClearRuleRequestVos, warehouseRuleFlowRequestVo);
        }

        for (DwDataClearRuleRequestVo dwDataClearRuleRequestVo : dwDataClearRuleRequestVos) {
            DataClearRule rule = new DataClearRule();

            // 检查 testArea 是否为空
            if (StringUtils.isBlank(dwDataClearRuleRequestVo.getTestArea())) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "testArea 不能为空");
            }

            // 检查 factory 是否为空
            if (StringUtils.isBlank(dwDataClearRuleRequestVo.getFactory())) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "factory 不能为空");
            }

            // 检查 StdfFieldType 是否为空
            if (StringUtils.isBlank(dwDataClearRuleRequestVo.getFieldType())) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "Field Type 不能为空");
            }
            StdfFieldType stdfFieldType = StdfFieldType.of(dwDataClearRuleRequestVo.getFieldType());
            String ruleStr = dwDataClearRuleRequestVo.getRule();
            String ruleTypeStr = dwDataClearRuleRequestVo.getRuleType();
            DataClearRuleType ruleType;

            if (stdfFieldType != StdfFieldType.CTDR) {
                // StdfFieldType 不为 CTDR时,检查 ruleType 和 rule 的合法性
                if (StringUtils.isBlank(ruleTypeStr)) {
                    throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "非CTDR的 Rule Type 不能为空");
                }
                ruleType = DataClearRuleType.of(ruleTypeStr);
                if (StringUtils.isBlank(ruleStr)) {
                    throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "非CTDR的 Rule 不能为空");
                }
                switch (ruleType) {
                    case FIRST_TXT_REGEX:
                    case CLEAR_TXT_REGEX:
                    case CLEAR_VECT_NAM_REGEX:
                        // 检查 rule 是否为合法的正则表达式
                        try {
                            java.util.regex.Pattern.compile(ruleStr);
                        } catch (java.util.regex.PatternSyntaxException e) {
                            log.error("rule 不是合法的正则表达式: {}", ruleStr);
                            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "Rule 不是合法的正则表达式");
                        }
                        break;
                    case TEST_ITEM_FROM_FIELDS:
                        // 检查 rule 是否至少包含 testNum 或 testTxt 中的一个
                        if (!(ruleStr.contains(TEST_NUM_STR) || ruleStr.contains(TEST_TXT_STR))) {
                            log.error("rule 必须包含 testNum 或 testTxt 中的一个: {}", ruleStr);
                            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "Rule 必须包含 testNum 或 testTxt 中的一个");
                        }
                        break;
                    default:
                        break;
                }
                rule.setRuleType(ruleType)
                        .setRule(ruleStr);
            } else {
                if (!StringUtils.isBlank(ruleTypeStr)) {
                    rule.setRuleType(DataClearRuleType.of(ruleTypeStr));
                }
                if (!StringUtils.isBlank(ruleStr)) {
                    rule.setRule(ruleStr);
                }
            }

            rule.setCustomer(RequestContext.getCustomer())
                    .setSubCustomer(dwDataClearRuleRequestVo.getSubCustomer())
                    .setTestArea(TestArea.of(dwDataClearRuleRequestVo.getTestArea()))
                    .setFactory(dwDataClearRuleRequestVo.getFactory())
                    .setFactorySite(dwDataClearRuleRequestVo.getFactorySite())
                    .setDeviceId(dwDataClearRuleRequestVo.getDeviceId())
                    .setFieldType(stdfFieldType);

            // 判断更新或新增, 并记录到 modify_warehousing_rule_record 表里
            Date now = new Date();
            Long ruleId = dwDataClearRuleRequestVo.getId();
            if (ruleId == null) {
                rule.setCreateUser(RequestContext.getUserName())
                        .setUpdateUser(RequestContext.getUserName())
                        .setCreateTime(now)
                        .setUpdateTime(now);
                ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.INSERT, WarehousingRuleType.DwDataClearRule, EMPTY);
                modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
            } else {
                Optional<DataClearRule> optionalOriginalRule = dataClearRuleRepository.findById(ruleId);
                if (optionalOriginalRule.isPresent()) {
                    DataClearRule originalRule = optionalOriginalRule.get();
                    String originalDataSnapshot = JSONObject.toJSONString(originalRule);
                    ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.UPDATE, WarehousingRuleType.DwDataClearRule, originalDataSnapshot);
                    modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
                    rule.setId(ruleId)
                            .setUpdateUser(RequestContext.getUserName())
                            .setUpdateTime(now)
                            .setCreateUser(originalRule.getCreateUser())
                            .setCreateTime(originalRule.getCreateTime());
                } else {
                    rule.setCreateUser(RequestContext.getUserName())
                            .setUpdateUser(RequestContext.getUserName())
                            .setCreateTime(now)
                            .setUpdateTime(now);
                    ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.INSERT, WarehousingRuleType.DwDataClearRule, EMPTY);
                    modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
                }
            }
            dataClearRuleRepository.save(rule);
        }
    }

    public void deleteDwDataClearRule(List<DwDataClearRuleRequestVo> dwDataClearRuleRequestVos, WarehousingRuleFlowRequestVo warehouseRuleFlowRequestVo) {
        // 检查传入的 dwSftpSubCustomerRuleRequestVos 获取其中的 customer, sub_customer, factory, test_area
        Set<String> uniqueKeys = new HashSet<>();
        String customer = RequestContext.getCustomer();
        String subCustomer = warehouseRuleFlowRequestVo.getSubCustomer();
        String factory = warehouseRuleFlowRequestVo.getFactory();
        String testArea = warehouseRuleFlowRequestVo.getTestArea();

        // 如果传入的 dwSftpSubCustomerRuleRequestVos 中的 customer, sub_customer, factory, test_area 唯一且不为空
        // 找出全部的id 并删除其中不在传入id里的数据
        List<Long> ids = dwDataClearRuleRequestVos.stream().map(DwDataClearRuleRequestVo::getId).collect(Collectors.toList());
        List<Long> allIds = dataClearRuleRepository.findAllIdsByCustomerAndSubCustomerAndFactoryAndTestArea(customer, subCustomer, factory, testArea);
        if (!CollectionUtils.isEmpty(allIds)) {
            List<Long> deleteIds = allIds.stream().filter(id -> !ids.contains(id)).collect(Collectors.toList());
            // 找出要删除的id后,遍历 deleteIds获取数据并将删除记录到  modifyWarehousingRuleRecord
            for (Long id : deleteIds) {
                Optional<DataClearRule> optionalOriginalRule = dataClearRuleRepository.findById(id);
                DataClearRule originalRule = optionalOriginalRule.get();
                String originalDataSnapshot = JSONObject.toJSONString(originalRule);
                ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.DELETE, WarehousingRuleType.DwSftpSubCustomerRule, originalDataSnapshot);
                modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
            }
            if (!CollectionUtils.isEmpty(deleteIds)) {
                log.info("删除 dwSftpSubCustomerRule: {}", deleteIds);
                dataClearRuleRepository.deleteAllByIds(deleteIds);
            }
        }
    }


    // 保存解析脚本配置规则
    public void saveDwSftpFileConvertScript(List<DwSftpFileConvertScriptRequestVo> dwSftpFileConvertScriptRequestVos, MultipartFile[] files) {
        // 检查传入的 dwSftpSubCustomerRuleRequestVos 获取其中的 customer, sub_customer, factory, test_area, file_category
        String customer = RequestContext.getCustomer();
        String subCustomer = null;
        String factory = null;
        String testArea = null;
        String fileCategory = null;
        Set<String> keySet = new HashSet<>();

        for (DwSftpFileConvertScriptRequestVo requestVo : dwSftpFileConvertScriptRequestVos) {
            subCustomer = requestVo.getSubCustomer();
            factory = requestVo.getFactory();
            TestArea testAreaEnum = TestArea.of(requestVo.getTestArea());
            testArea = testAreaEnum == null ? EMPTY : testAreaEnum.getTestScope();
            fileCategory = requestVo.getFileCategory();

            if (StringUtils.isBlank(customer) || StringUtils.isBlank(subCustomer) || StringUtils.isBlank(factory) || StringUtils.isBlank(testArea)) {
                StringBuilder missingFields = new StringBuilder("以下字段不能为空: ");
                if (StringUtils.isBlank(customer)) {
                    missingFields.append("customer, ");
                }
                if (StringUtils.isBlank(subCustomer)) {
                    missingFields.append("subCustomer, ");
                }
                if (StringUtils.isBlank(factory)) {
                    missingFields.append("factory, ");
                }
                if (StringUtils.isBlank(testArea)) {
                    missingFields.append("testArea, ");
                }

                String logMessage = missingFields.toString().trim();
                if (logMessage.endsWith(COMMA)) {
                    logMessage = logMessage.substring(0, logMessage.length() - 1);
                }
                log.error(logMessage);
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "customer, subCustomer, factory, testArea 不能为空");
            }

            String key = customer + MIDDLE_LINE + subCustomer + MIDDLE_LINE + factory + MIDDLE_LINE + testArea + MIDDLE_LINE + fileCategory;
            keySet.add(key);
            // 检查是否有多个不同的key
            if (keySet.size() > 1) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "customer, subCustomer, factory, testArea, fileCategory 必须唯一");
            }
        }

        // 如果传入的 dwSftpSubCustomerRuleRequestVos 中的 customer, sub_customer, factory, test_area, file_category 唯一且不为空
        // 找出全部的id 并删除其中不在传入id里的数据和文件
        List<Long> ids = dwSftpFileConvertScriptRequestVos.stream().map(DwSftpFileConvertScriptRequestVo::getId).collect(Collectors.toList());
        List<Long> allIds = sftpFileConvertScriptRepository.findAllIdsByCustomerAndSubCustomerAndFactoryAndFileCategoryAndTestArea(customer, subCustomer, factory, fileCategory, testArea);
        if (!CollectionUtils.isEmpty(allIds)) {
            List<Long> deleteIds = allIds.stream().filter(id -> !ids.contains(id)).collect(Collectors.toList());
            // 找出要删除的id后,遍历 deleteIds获取数据并将删除记录到  modifyWarehousingRuleRecord
            for (Long id : deleteIds) {
                Optional<SftpFileConvertScript> optionalOriginalRule = sftpFileConvertScriptRepository.findById(id);
                SftpFileConvertScript originalRule = optionalOriginalRule.get();
                String originalDataSnapshot = JSONObject.toJSONString(originalRule);
                ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.DELETE, WarehousingRuleType.DwSftpFileConvertScript, originalDataSnapshot);
                modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
                // 获取文件全路径, 到hdfs 删除
                String fullPathFileName = originalRule.getHdfsFilePath();
                log.info("开始删除文件: {}", fullPathFileName);
                try {
                    hdfsUtil.deleteFile(fullPathFileName);
                } catch (Exception e) {
                    log.error("删除文件失败: {}", fullPathFileName, e);
                }
            }
            if (!CollectionUtils.isEmpty(deleteIds)) {
                log.info("删除 dwSftpFileConvertScript: {}", deleteIds);
                sftpFileConvertScriptRepository.deleteAllByIds(deleteIds);
            }
        }

        if (CollectionUtils.isEmpty(Arrays.asList(files)) && dwSftpFileConvertScriptRequestVos.stream().map(DwSftpFileConvertScriptRequestVo::getId).allMatch(Objects::isNull)) {
            log.info("清空解析脚本配置");
            return;
        }

        IntStream.range(0, dwSftpFileConvertScriptRequestVos.size()).forEach(i -> {
            DwSftpFileConvertScriptRequestVo dwSftpFileConvertScriptRequestVo = dwSftpFileConvertScriptRequestVos.get(i);
            // log.info("dwSftpFileConvertScriptRequestVo是:{}",dwSftpFileConvertScriptRequestVo);
            MultipartFile file = i < files.length ? files[i] : null;
            String localFileName = file != null ? file.getOriginalFilename() : null;
            // log.info("localFileName是:{},索引是,{}", localFileName,i);
            if ((file == null || StringUtils.isBlank(localFileName))) {
                log.info("文件索引是 {} 为空，跳过保存文件, 只更新regRule、executeFileName、scriptList", i);
                if (dwSftpFileConvertScriptRequestVo.getId() == null) {
                    log.error("文件和ID不能同时为空！");
                    throw new BpmsException(ResponseCode.Business.CONFIGURE_SAVE_SCRIPT_RULE);
                } else {
                    saveDwSftpFileConvertScript(dwSftpFileConvertScriptRequestVo);
                }
            } else {
                File localDir = new File(FileUtil.getFileFullName(confReadPath, UUID.randomUUID().toString()));
                localDir.mkdirs();
                File localFile = new File(FileUtil.getFileFullName(localDir.getAbsolutePath(), localFileName));
                try {
                    file.transferTo(localFile);
                    saveDwSftpFileConvertScript(dwSftpFileConvertScriptRequestVo, localFile);
                } catch (Exception e) {
                    if (e instanceof BpmsException) {
                        throw (BpmsException) e;
                    } else {
                        throw new BpmsException(ResponseCode.Business.CONFIGURE_SAVE_SCRIPT_RULE);
                    }
                } finally {
                    FileUtil.deleteDir(localDir);
                }
            }
        });
    }

    public void saveDwSftpFileConvertScript(DwSftpFileConvertScriptRequestVo dwSftpFileConvertScriptRequestVo, File localFile) {

        SftpFileConvertScript rule = new SftpFileConvertScript();
        String customer = RequestContext.getCustomer();
        String subCustomer = dwSftpFileConvertScriptRequestVo.getSubCustomer();
        String testArea = dwSftpFileConvertScriptRequestVo.getTestArea();
        String factory = dwSftpFileConvertScriptRequestVo.getFactory();
        String factorySite = dwSftpFileConvertScriptRequestVo.getFactorySite();
        String fileCategoryStr = dwSftpFileConvertScriptRequestVo.getFileCategory();

        // 检查 subCustomer 是否为空
        if (StringUtils.isBlank(subCustomer)) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "subCustomer 不能为空");
        }

        // 检查 testArea 是否为空并转换
        TestArea testAreaEnum = TestArea.of(testArea);
        TestArea testScope = testAreaEnum != null ? TestArea.of(testAreaEnum.getTestScope()) : null;
        if (testAreaEnum == null || testScope == null) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "testArea 不能为空或无效: " + testArea);
        }

        // 检查 factory 是否为空
        if (StringUtils.isBlank(factory)) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "factory 不能为空");
        }

        // 检查 factorySite 是否为空
        if (StringUtils.isBlank(factorySite)) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "factorySite 不能为空");
        }

        // 检查 fileCategory 是否为空
        if (StringUtils.isBlank(fileCategoryStr)) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "fileCategory 不能为空");
        }

        // 检查 fileCategory 是否为非 STDF
        FileCategory fileCategory = FileCategory.of(fileCategoryStr);
        if (fileCategory == FileCategory.STDF) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "fileCategory 不能为 STDF");
        }

        // 检查 regRule 是否为合法的正则表达式
        String regRule = dwSftpFileConvertScriptRequestVo.getRegRule();
        if (StringUtils.isBlank(regRule)) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "regRule 不能为空");
        }
        try {
            java.util.regex.Pattern.compile(regRule);
        } catch (java.util.regex.PatternSyntaxException e) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "regRule 不是合法的正则表达式");
        }


        // 检查executeFileName是否为空
        if (StringUtils.isBlank(dwSftpFileConvertScriptRequestVo.getExecuteFileName())) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "executeFileName 不能为空");
        }

        // 检查scriptList是否为空
        if (CollectionUtils.isEmpty(dwSftpFileConvertScriptRequestVo.getScriptList())) {
            log.info("历史数据scriptList为空,默认scriptList为executeFileName");
            dwSftpFileConvertScriptRequestVo.setScriptList(Collections.singletonList(dwSftpFileConvertScriptRequestVo.getExecuteFileName()));
        }

        String fileName = localFile.getName();
        // 检查文件大小是否大于100M
        if (localFile.length() > 100 * 1024 * 1024) {
            log.error("上传脚本文件大小超过 100M，文件名: {}，文件大小: {}", fileName, localFile.length());
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "上传脚本文件大小不能超过 100M");
        }
        // 检查上传脚本的后缀
        if (StringUtils.isBlank(fileName) || !fileName.endsWith(FILE_SUFFIX_ZIP)) {
            log.error("上传文件格式不正确，仅支持.zip 文件，文件名为：{}", fileName);
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "上传文件格式不正确，仅.zip 文件");
        }
        // 拼接上传脚本的 hdfs 路径
        String fullHdfsFilePath = adapterDir + customer + SLASH + subCustomer + SLASH + testArea + SLASH + factory + SLASH + factorySite + SLASH + fileCategoryStr;
        String fullPathFileName = fullHdfsFilePath + SLASH + fileName;
        // log.info("修正后的文件名包括：fileName:{}, pyFileName:{} ,zipFileName:{} ,baseFileName:{} ,fullHdfsFilePath:{} ,fullPathFileName:{}", fileName, pyFileName, zipFileName, baseFileName, fullHdfsFilePath, fullPathFileName);
        // .zip 直接上传
        try {
            hdfsUtil.uploadToHDFSByFileSystem(localFile.getAbsolutePath(), fullHdfsFilePath, 1);
            log.info("上传文件 {} 成功", fullHdfsFilePath);
        } catch (Exception e) {
            throw new BpmsException(ResponseCode.Common.UPLOAD_HDFS_FAIL, "文件上传hdfs失败");
        }

        Date now = new Date();
        rule.setCustomer(customer)
                .setSubCustomer(subCustomer)
                .setTestArea(testScope)
                .setFactory(factory)
                .setFactorySite(factorySite)
                .setFileCategory(fileCategory)
                .setRegRule(regRule)
                .setFileName(fileName)
                .setScriptList(dwSftpFileConvertScriptRequestVo.getScriptList())
                .setExecuteFileName(dwSftpFileConvertScriptRequestVo.getExecuteFileName())
                .setHdfsFilePath(fullPathFileName)
                .setUpdateUser(RequestContext.getUserName())
                .setUpdateTime(now);

        /// 判断更新或新增, 并记录到 modify_warehousing_rule_record 表里
        Long ruleId = dwSftpFileConvertScriptRequestVo.getId();
        if (ruleId == null) {
            log.info("新增解析脚本规则: regRule:{}, fileName:{}, scriptList:{}, executeFileName:{}", regRule, fileName, dwSftpFileConvertScriptRequestVo.getScriptList(), dwSftpFileConvertScriptRequestVo.getExecuteFileName());
            rule.setCreateUser(RequestContext.getUserName()).setCreateTime(now);

            ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.INSERT, WarehousingRuleType.DwSftpFileConvertScript, EMPTY);
            modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
        } else {
            Optional<SftpFileConvertScript> optionalOriginalRule = sftpFileConvertScriptRepository.findById(ruleId);
            if (optionalOriginalRule.isPresent()) {
                SftpFileConvertScript originalRule = optionalOriginalRule.get();
                String originalDataSnapshot = JSONObject.toJSONString(originalRule);
                ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.UPDATE, WarehousingRuleType.DwSftpFileConvertScript, originalDataSnapshot);
                modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);

                log.info("更新解析脚本规则: regRule:{}, fileName:{}, scriptList:{}, executeFileName:{}", regRule, fileName, dwSftpFileConvertScriptRequestVo.getScriptList(), dwSftpFileConvertScriptRequestVo.getExecuteFileName());
                rule.setId(ruleId).setCreateUser(originalRule.getCreateUser()).setCreateTime(originalRule.getCreateTime());
            } else {
                log.info("新增解析脚本规则: regRule:{}, fileName:{}, scriptList:{}, executeFileName:{}", regRule, fileName, dwSftpFileConvertScriptRequestVo.getScriptList(), dwSftpFileConvertScriptRequestVo.getExecuteFileName());
                rule.setCreateUser(RequestContext.getUserName()).setCreateTime(now);

                ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.INSERT, WarehousingRuleType.DwSftpFileConvertScript, EMPTY);
                modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
            }
        }
        sftpFileConvertScriptRepository.save(rule);
    }

    public void saveDwSftpFileConvertScript(DwSftpFileConvertScriptRequestVo dwSftpFileConvertScriptRequestVo) {
        // log.info("dwSftpFileConvertScriptRequestVo是:{}",dwSftpFileConvertScriptRequestVo);
        SftpFileConvertScript rule = new SftpFileConvertScript();
        // 检查 regRule 是否为合法的正则表达式
        String regRule = dwSftpFileConvertScriptRequestVo.getRegRule();
        List<String> scriptList = dwSftpFileConvertScriptRequestVo.getScriptList();
        String executeFileName = dwSftpFileConvertScriptRequestVo.getExecuteFileName();
        if (StringUtils.isBlank(regRule)) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "regRule 不能为空");
        }
        try {
            java.util.regex.Pattern.compile(regRule);
        } catch (java.util.regex.PatternSyntaxException e) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "regRule 不是合法的正则表达式");
        }
        if (StringUtils.isBlank(executeFileName)) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "executeFileName 不能为空");
        }
        // 此时必定是更新
        Date now = new Date();
        Long ruleId = dwSftpFileConvertScriptRequestVo.getId();
        Optional<SftpFileConvertScript> optionalOriginalRule = sftpFileConvertScriptRepository.findById(ruleId);
        if (optionalOriginalRule.isPresent()) {
            SftpFileConvertScript originalRule = optionalOriginalRule.get();
            String originalDataSnapshot = JSONObject.toJSONString(originalRule);
            ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.UPDATE, WarehousingRuleType.DwSftpFileConvertScript, originalDataSnapshot);
            modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
            TestArea testScope = originalRule.getTestArea() != null ? TestArea.of(originalRule.getTestArea().getTestScope()) : null;
            rule.setId(ruleId)
                    .setCustomer(originalRule.getCustomer())
                    .setFactory(originalRule.getFactory())
                    .setFactorySite(originalRule.getFactorySite())
                    .setRegRule(regRule)
                    .setFileName(originalRule.getFileName())
                    .setScriptList(scriptList)
                    .setExecuteFileName(executeFileName)
                    .setHdfsFilePath(originalRule.getHdfsFilePath())
                    .setUpdateTime(now)
                    .setUpdateUser(RequestContext.getUserName())
                    .setTestArea(testScope)
                    .setSubCustomer(originalRule.getSubCustomer())
                    .setFileCategory(originalRule.getFileCategory())
                    .setCreateUser(originalRule.getCreateUser())
                    .setCreateTime(originalRule.getCreateTime());
        } else {
            log.error("更新失败, dw_sftp_file_convert_script 原始数据不存在,id:{}", ruleId);
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "更新失败, dw_sftp_file_convert_script 原始数据不存在,id:" + ruleId);
        }
        sftpFileConvertScriptRepository.save(rule);
    }

    public void checkDwSourceStandardDeviceIdRule(List<DwSourceStandardDeviceIdRuleRequestVo> ruleRequestVos, WarehousingRuleFlowRequestVo warehouseRuleFlowRequestVo) {
        // 检查传入的 ruleRequestVos 获取其中的 customer, sub_customer, factory, test_area
        Set<String> uniqueKeys = new HashSet<>();
        String customer = RequestContext.getCustomer();
        String subCustomer = null;
        String factory = null;
        String testArea = null;
        Map<String, Integer> keyCountMap = new HashMap<>();

        String ruleFlowsubCustomer = warehouseRuleFlowRequestVo.getSubCustomer();
        String ruleFlowfactory = warehouseRuleFlowRequestVo.getFactory();
        String ruleFlowtestArea = warehouseRuleFlowRequestVo.getTestArea();
        String ruleFlowtestAreaScop = TestArea.of(ruleFlowtestArea) == null ? EMPTY : TestArea.of(ruleFlowtestArea).getTestScope();

        for (DwSourceStandardDeviceIdRuleRequestVo requestVo : ruleRequestVos) {
            subCustomer = requestVo.getSubCustomer();
            factory = requestVo.getFactory();
            TestArea testAreaEnum = TestArea.of(requestVo.getTestArea());
            testArea = testAreaEnum == null ? EMPTY : testAreaEnum.getTestScope();

            if (StringUtils.isBlank(customer) || StringUtils.isBlank(subCustomer) || StringUtils.isBlank(factory) || StringUtils.isBlank(testArea) || !ruleFlowsubCustomer.equals(subCustomer) || !ruleFlowfactory.equals(factory) || !ruleFlowtestAreaScop.equals(testArea)) {
                StringBuilder missingFields = new StringBuilder("以下字段不合法: ");
                if (StringUtils.isBlank(customer)) {
                    missingFields.append("customer, ");
                }
                if (StringUtils.isBlank(subCustomer) || !ruleFlowsubCustomer.equals(subCustomer)) {
                    missingFields.append("subCustomer, ");
                }
                if (StringUtils.isBlank(factory) || !ruleFlowfactory.equals(factory)) {
                    missingFields.append("factory, ");
                }
                if (StringUtils.isBlank(testArea) || !ruleFlowtestAreaScop.equals(testArea)) {
                    missingFields.append("testArea, ");
                    log.info("ruleFlowtestAreaScop: {}, testArea: {},isBlank: {},equals: {}", ruleFlowtestAreaScop, testArea, StringUtils.isBlank(testArea), !ruleFlowtestAreaScop.equals(testArea));
                }

                String logMessage = missingFields.toString().trim();
                if (logMessage.endsWith(COMMA)) {
                    logMessage = logMessage.substring(0, logMessage.length() - 1);
                }
                log.error(logMessage);
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "customer, subCustomer, factory, testArea 不能为空, 且必须和RuleFlow一致！");
            }

        }

        // 如果传入的 ruleRequestVos 中的 customer, sub_customer, factory, test_area 唯一且不为空
        // 找出全部的id 并删除其中不在传入id里的数据
        List<Long> ids = ruleRequestVos.stream().map(DwSourceStandardDeviceIdRuleRequestVo::getId).collect(Collectors.toList());
        List<Long> allIds = sourceStandardDeviceIdRuleRepository.findAllIdsByCustomerAndSubCustomerAndFactoryAndTestArea(customer, ruleFlowsubCustomer, ruleFlowfactory, ruleFlowtestAreaScop);
        if (!CollectionUtils.isEmpty(allIds)) {
            List<Long> deleteIds = allIds.stream().filter(id -> !ids.contains(id)).collect(Collectors.toList());
            // 找出要删除的id后,遍历 deleteIds获取数据并将删除记录到  modifyWarehousingRuleRecord
            for (Long id : deleteIds) {
                Optional<SourceStandardDeviceIdRule> optionalOriginalRule = sourceStandardDeviceIdRuleRepository.findById(id);
                SourceStandardDeviceIdRule originalRule = optionalOriginalRule.get();
                String originalDataSnapshot = JSONObject.toJSONString(originalRule);
                ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.DELETE, WarehousingRuleType.DwSourceStandardDeviceIdRule, originalDataSnapshot);
                modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
            }
            if (!CollectionUtils.isEmpty(deleteIds)) {
                log.info("删除 sourceStandardDeviceIdRule: {}", deleteIds);
                sourceStandardDeviceIdRuleRepository.deleteAllByIds(deleteIds);
            }
        }
    }

    // 保存DEVICE_ID取值规则
    public void saveDwSourceStandardDeviceIdRule(List<DwSourceStandardDeviceIdRuleRequestVo> ruleRequestVos) {

        for (DwSourceStandardDeviceIdRuleRequestVo dwSourceStandardDeviceIdRuleRequestVo : ruleRequestVos) {
            SourceStandardDeviceIdRule rule = new SourceStandardDeviceIdRule();

            // 检查 subCustomer 是否为空
            if (StringUtils.isBlank(dwSourceStandardDeviceIdRuleRequestVo.getSubCustomer())) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "subCustomer 不能为空");
            }

            // 检查 testArea 是否为空并转换
            TestArea testAreaEnum = TestArea.of(dwSourceStandardDeviceIdRuleRequestVo.getTestArea());
            TestArea testScope = testAreaEnum != null ? TestArea.of(testAreaEnum.getTestScope()) : null;
            if (testAreaEnum == null || testScope == null) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "testArea 不能为空或无效: " + dwSourceStandardDeviceIdRuleRequestVo.getTestArea());
            }
            // 检查 factory 是否为空
            if (StringUtils.isBlank(dwSourceStandardDeviceIdRuleRequestVo.getFactory())) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "factory 不能为空");
            }

            // 检查 factorySite 是否为空
            if (StringUtils.isBlank(dwSourceStandardDeviceIdRuleRequestVo.getFactorySite())) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "factorySite 不能为空");
            }

            // 检查 ruleExpression 是否包含 "result"
            String ruleExpression = dwSourceStandardDeviceIdRuleRequestVo.getRuleExpression();
            if (StringUtils.isBlank(ruleExpression)) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "ruleExpression 不能为空");
            }
            if (!ruleExpression.contains(RESULT_STR)) {
                log.error("ruleExpression 必须包含 result: {}", ruleExpression);
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "ruleExpression 必须包含 'result'");
            }

            rule.setCustomer(RequestContext.getCustomer())
                    .setSubCustomer(dwSourceStandardDeviceIdRuleRequestVo.getSubCustomer())
                    .setTestArea(testScope)
                    .setFactory(dwSourceStandardDeviceIdRuleRequestVo.getFactory())
                    .setFactorySite(dwSourceStandardDeviceIdRuleRequestVo.getFactorySite())
                    .setRuleExpression(ruleExpression);

            // 判断更新或新增, 并记录到 modify_warehousing_rule_record 表里
            Date now = new Date();
            Long ruleId = dwSourceStandardDeviceIdRuleRequestVo.getId();
            if (ruleId == null) {
                rule.setCreateUser(RequestContext.getUserName())
                        .setUpdateUser(RequestContext.getUserName())
                        .setCreateTime(now)
                        .setUpdateTime(now);
                ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.INSERT, WarehousingRuleType.DwSourceStandardDeviceIdRule, EMPTY);
                modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
            } else {
                Optional<SourceStandardDeviceIdRule> optionalOriginalRule = sourceStandardDeviceIdRuleRepository.findById(ruleId);
                if (optionalOriginalRule.isPresent()) {
                    SourceStandardDeviceIdRule originalRule = optionalOriginalRule.get();
                    String originalDataSnapshot = JSONObject.toJSONString(originalRule);
                    ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.UPDATE, WarehousingRuleType.DwSourceStandardDeviceIdRule, originalDataSnapshot);
                    modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
                    rule.setId(ruleId)
                            .setUpdateUser(RequestContext.getUserName())
                            .setUpdateTime(now);
                } else {
                    rule.setCreateUser(RequestContext.getUserName())
                            .setUpdateUser(RequestContext.getUserName())
                            .setCreateTime(now)
                            .setUpdateTime(now);
                    ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.INSERT, WarehousingRuleType.DwSourceStandardDeviceIdRule, EMPTY);
                    modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
                }
            }
            sourceStandardDeviceIdRuleRepository.save(rule);
        }
    }

    // 保存关键字段取值规则
    public void saveDwSourceStandardFieldRule(List<DwSourceStandardFieldRuleRequestVo> dwSourceStandardFieldRuleRequestVos, WarehousingRuleFlowRequestVo warehouseRuleFlowRequestVo) {
        if (warehouseRuleFlowRequestVo != null) {
            log.info("删除DwSftpSubCustomerRule");
            deleteDwSourceStandardFieldRule(dwSourceStandardFieldRuleRequestVos, warehouseRuleFlowRequestVo);
        }

        for (DwSourceStandardFieldRuleRequestVo dwSourceStandardFieldRuleRequestVo : dwSourceStandardFieldRuleRequestVos) {
            SourceStandardFieldRule rule = new SourceStandardFieldRule();

            // 检查 testArea 是否为空并转换
            TestArea testAreaEnum = TestArea.of(dwSourceStandardFieldRuleRequestVo.getTestArea());
            TestArea testScope = testAreaEnum != null ? TestArea.of(testAreaEnum.getTestScope()) : null;
            if (testAreaEnum == null || testScope == null) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "testArea 不能为空或无效: " + dwSourceStandardFieldRuleRequestVo.getTestArea());
            }
            // 检查 ruleExpression 是否包含 "result"
            String ruleExpression = dwSourceStandardFieldRuleRequestVo.getRuleExpression();
            if (StringUtils.isBlank(ruleExpression)) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "ruleExpression 不能为空");
            }
            if (!ruleExpression.contains(RESULT_STR)) {
                log.error("ruleExpression 必须包含 result: {}", ruleExpression);
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "ruleExpression 必须包含 'result'");
            }

            rule.setId(dwSourceStandardFieldRuleRequestVo.getId())
                    .setCustomer(RequestContext.getCustomer())
                    .setSubCustomer(dwSourceStandardFieldRuleRequestVo.getSubCustomer())
                    .setDeviceId(EMPTY)
                    .setTestArea(testScope)
                    .setFactory(dwSourceStandardFieldRuleRequestVo.getFactory())
                    .setFactorySite(dwSourceStandardFieldRuleRequestVo.getFactorySite())
                    .setField(dwSourceStandardFieldRuleRequestVo.getField())
                    .setRuleExpression(ruleExpression);

            // 判断更新或新增, 并记录到 modify_warehousing_rule_record 表里
            Date now = new Date();
            Long ruleId = dwSourceStandardFieldRuleRequestVo.getId();
            if (ruleId == null) {
                rule.setCreateUser(RequestContext.getUserName())
                        .setUpdateUser(RequestContext.getUserName())
                        .setCreateTime(now)
                        .setUpdateTime(now);
                ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.INSERT, WarehousingRuleType.DwSourceStandardFieldRule, EMPTY);
                modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
            } else {
                Optional<SourceStandardFieldRule> optionalOriginalRule = sourceStandardFieldRuleRepository.findById(ruleId);
                if (optionalOriginalRule.isPresent()) {
                    SourceStandardFieldRule originalRule = optionalOriginalRule.get();
                    String originalDataSnapshot = JSONObject.toJSONString(originalRule);
                    ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.UPDATE, WarehousingRuleType.DwSourceStandardFieldRule, originalDataSnapshot);
                    modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
                    rule.setId(ruleId)
                            .setUpdateUser(RequestContext.getUserName())
                            .setUpdateTime(now)
                            .setCreateUser(originalRule.getCreateUser())
                            .setCreateTime(originalRule.getCreateTime());
                } else {
                    rule.setCreateUser(RequestContext.getUserName())
                            .setUpdateUser(RequestContext.getUserName())
                            .setCreateTime(now)
                            .setUpdateTime(now);
                    ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.INSERT, WarehousingRuleType.DwSourceStandardFieldRule, EMPTY);
                    modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
                }
            }
            sourceStandardFieldRuleRepository.save(rule);
        }
    }

    public void deleteDwSourceStandardFieldRule(List<DwSourceStandardFieldRuleRequestVo> dwSourceStandardFieldRuleRequestVos, WarehousingRuleFlowRequestVo warehouseRuleFlowRequestVo) {
        // 检查传入的 dwSourceStandardFieldRuleRequestVos 获取其中的 customer, sub_customer, factory, test_area
        Set<String> uniqueKeys = new HashSet<>();
        String customer = RequestContext.getCustomer();
        String subCustomer = warehouseRuleFlowRequestVo.getSubCustomer();
        String factory = warehouseRuleFlowRequestVo.getFactory();
        TestArea testAreaEnum = TestArea.of(warehouseRuleFlowRequestVo.getTestArea());
        String testArea = testAreaEnum == null ? EMPTY : testAreaEnum.getTestScope();

        // 找出全部的id 并删除其中不在传入id里的数据
        List<Long> ids = dwSourceStandardFieldRuleRequestVos.stream().map(DwSourceStandardFieldRuleRequestVo::getId).collect(Collectors.toList());
        List<Long> allIds = sourceStandardFieldRuleRepository.findAllIdsByCustomerAndSubCustomerAndFactoryAndTestArea(customer, subCustomer, factory, testArea);
        if (!CollectionUtils.isEmpty(allIds)) {
            List<Long> deleteIds = allIds.stream().filter(id -> !ids.contains(id)).collect(Collectors.toList());
            // 找出要删除的id后,遍历 deleteIds获取数据并将删除记录到  modifyWarehousingRuleRecord
            for (Long id : deleteIds) {
                Optional<SourceStandardFieldRule> optionalOriginalRule = sourceStandardFieldRuleRepository.findById(id);
                SourceStandardFieldRule originalRule = optionalOriginalRule.get();
                String originalDataSnapshot = JSONObject.toJSONString(originalRule);
                ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.DELETE, WarehousingRuleType.DwSourceStandardFieldRule, originalDataSnapshot);
                modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
            }
            if (!CollectionUtils.isEmpty(deleteIds)) {
                log.info("删除 dwSourceStandardFieldRule: {}", deleteIds);
                sourceStandardFieldRuleRepository.deleteAllByIds(deleteIds);
            }
        }
    }

    private ModifyWarehousingRuleRecord buildModifyWarehousingRuleRecord(OperationType operationType, WarehousingRuleType warehouseRuleType, String dataSnapshot) {
        return ModifyWarehousingRuleRecord.builder()
                .customer(RequestContext.getCustomer())
                .ruleTable(warehouseRuleType == null ? null : warehouseRuleType.getTableName())
                .operationType(operationType.getType())
                .dataSnapshot(dataSnapshot)
                .createTime(new Date())
                .updateTime(new Date())
                .createUser(RequestContext.getUserName())
                .updateUser(RequestContext.getUserName())
                .build();
    }

    public boolean checkConfExists() {
        log.info("checkConfExists");
        return customRepository.getDataCnt(RequestContext.getCustomer()) > 0 &&
                warehousingRuleFlowRepository.getDataCnt(RequestContext.getCustomer()) > 0;
    }

    public void importWarehousingRule(MultipartFile file) throws IOException {
        log.info("导入conf配置");
        if (file.getSize() > 1024L * 1024L * 1024L) {
            throw new BpmsException(ResponseCode.Common.FILE_SIZE_EXCEED_LIMIT, "1GB");
        }
        String localPath = FileUtil.getFileFullName(confReadPath, UUID.randomUUID().toString());
        new File(localPath).mkdirs();

        // 备份脚本文件用于异常回滚
        List<Pair<String, File>> scriptFiles = null;
        boolean bollBackFlag = false;
        try {
            scriptFiles = backupFile(localPath);
            File zipFile = new File(FileUtil.getFileFullName(localPath, file.getOriginalFilename()));
            file.transferTo(zipFile);
            // 解压
            File localDir = new File(FileUtil.removeFileSuffix(zipFile.getAbsolutePath(), FileType.ZIP));
            try {
                new ZipUncompressHandler().unCompress(zipFile, localDir);
            } catch (Exception e) {
                log.error("导入的文件解压失败");
                bollBackFlag = true;
                throw new BpmsException(ResponseCode.Common.FILE_DECOMPRESS_FAIL);
            }
            try {
                Map<String, File> fileMap = listFile(localDir);

                File excelFile = fileMap.get("excel");
                if (excelFile == null) {
                    throw new BpmsException(ResponseCode.Business.CONFIGURE_EXCEL_FILE_NOT_EXIST);
                }
                Map<String, List<?>> resultMap = new HashMap<>();

                ExcelReader excelReader = EasyExcel.read(excelFile).build();
                // 获取所有Sheet信息
                List<ReadSheet> sheets = excelReader.excelExecutor().sheetList();
                sheets.forEach(sheet -> {
                    Class<?> clazz = null;
                    String sheetName = sheet.getSheetName();
                    switch (WarehousingRuleType.valueOf(sheetName)) {
                        case DwSftpSubCustomerRule:
                            clazz = DwSftpSubCustomerRuleVo.class;
                            break;
                        case DwSourceStandardDeviceIdRule:
                            clazz = DwSourceStandardDeviceIdRuleVo.class;
                            break;
                        case DwSourceStandardFieldRule:
                            clazz = DwSourceStandardFieldRuleVo.class;
                            break;
                        case DwDataClearRule:
                            clazz = DwDataClearRuleVo.class;
                            break;
                        case DwSftpFileConvertScript:
                            clazz = DwSftpFileConvertScriptVo.class;
                            break;
                        case DwSftpFileRenameRule:
                            clazz = DwSftpFileRenameRuleVo.class;
                            break;
                        case BzWarehousingRuleFlow:
                            clazz = WarehousingRuleFlowVo.class;
                            break;
                        default:
                            throw new BpmsException(ResponseCode.Business.CONFIGURE_EXCEL_SHEET_NAME_NOT_SUPPORT, WarehousingRuleType.valueOf(sheetName));
                    }

                    // 为每个Sheet创建独立监听器
                    ExcelUtil.SheetDataListener<Object> listener = new ExcelUtil.SheetDataListener<>(sheetName);

                    // 处理每个Sheet
                    ReadSheet readSheet = EasyExcel.readSheet(sheet.getSheetNo())
                            .head(clazz)
                            .registerReadListener(listener)
                            .build();
                    excelReader.read(readSheet);
                    try {
                        resultMap.put(sheetName, listener.getDataList());
                    } catch (Exception e) {
                        throw new BpmsException(e, ResponseCode.Business.CONFIGURE_DUPLICATE, sheet.getSheetName());
                    }
                });

                // 校验脚本是否和脚本逻辑能匹配上
                List<String> notExistsScript = checkScriptFile(resultMap, fileMap);
                if (notExistsScript.size() > 0) {
                    throw new BpmsException(ResponseCode.Business.CONFIGURE_SCRIPT_FILE_NOT_EXIST, String.join(COMMA, notExistsScript));
                }

                // 处理数据
                warehousingRuleService.processConfSheetData(resultMap, fileMap);
                warehousingRuleService.processFlowSheetData(resultMap);

                modifyWarehousingRuleRecordRepository.save(buildModifyWarehousingRuleRecord(OperationType.OVERWRITE, null, Constant.EMPTY));
            } catch (Exception e) {
                log.info("conf文件解析失败", e);
                bollBackFlag = true;

                if (e instanceof BpmsException) {
                    throw e;
                } else {
                    throw new BpmsException(ResponseCode.Common.FILE_PARSE_FAIL, file.getOriginalFilename());
                }
            }
        } finally {
            try {
                if (bollBackFlag && scriptFiles != null && scriptFiles.size() > 0) {
                    log.info("导入规则失败，进行script回滚");
                    scriptFiles.stream().filter(Objects::nonNull).forEach(t -> hdfsUtil.uploadToHDFSByFileSystem(t.getValue().getAbsolutePath(), t.getKey(), 1));
                }
            } catch (Exception exception) {
                log.error("脚本回滚失败");
            }

            FileUtil.deleteDir(new File(localPath));
        }
    }

    /**
     * @param resultMap excel中的数据
     * @param fileMap   zip包中所有文件
     * @return excel中存在的脚本记录没有实际文件
     */
    private List<String> checkScriptFile(Map<String, List<?>> resultMap, Map<String, File> fileMap) {
        return resultMap.get(WarehousingRuleType.DwSftpFileConvertScript.name()).stream()
                .map(t -> convertToDto(t, DwSftpFileConvertScriptRequestVo.class))
                .map(DwSftpFileConvertScriptRequestVo::getFileName)
                .filter(script -> !fileMap.containsKey(script))
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManagerSecondary")
    public void processConfSheetData(Map<String, List<?>> resultMap, Map<String, File> fileMap) {
        String customer = RequestContext.getCustomer();
        // 删除
        sftpSubCustomerRuleRepository.deleteAllByCustomer(customer);
        fileRenameRuleRepository.deleteAllByCustomer(customer);
        dataClearRuleRepository.deleteAllByCustomer(customer);
        sftpFileConvertScriptRepository.deleteAllByCustomer(customer);
        sourceStandardDeviceIdRuleRepository.deleteAllByCustomer(customer);
        sourceStandardFieldRuleRepository.deleteAllByCustomer(customer);
        // 校验
        resultMap.forEach((key, value) -> {
            switch (WarehousingRuleType.valueOf(key)) {
                case DwSftpFileConvertScript:
                    value.stream().map(t -> convertToDto(t, DwSftpFileConvertScriptRequestVo.class))
                            .forEach(t -> {
                                saveDwSftpFileConvertScript(t, fileMap.get(t.getFileName()));
                            });
                    break;
                case DwSftpFileRenameRule:
                    saveDwSftpFileRenameRule(value.stream().map(t -> convertToDto(t, DwSftpFileRenameRuleRequestVo.class)).collect(Collectors.toList()), null);
                    break;
                case DwDataClearRule:
                    saveDwDataClearRule(value.stream().map(t -> convertToDto(t, DwDataClearRuleRequestVo.class)).collect(Collectors.toList()), null);
                    break;
                case DwSftpSubCustomerRule:
                    saveDwSftpSubCustomerRule(value.stream().map(t -> convertToDto(t, DwSftpSubCustomerRuleRequestVo.class)).collect(Collectors.toList()), null);
                    break;
                case DwSourceStandardDeviceIdRule:
                    saveDwSourceStandardDeviceIdRule(value.stream().map(t -> convertToDto(t, DwSourceStandardDeviceIdRuleRequestVo.class)).collect(Collectors.toList()));
                    break;
                case DwSourceStandardFieldRule:
                    saveDwSourceStandardFieldRule(value.stream().map(t -> convertToDto(t, DwSourceStandardFieldRuleRequestVo.class)).collect(Collectors.toList()), null);
                    break;
            }
        });
    }

    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManagerPrimary")
    public void processFlowSheetData(Map<String, List<?>> resultMap) {
        warehousingRuleFlowRepository.deleteAllByCustomer(RequestContext.getCustomer());
        if (resultMap.containsKey(BzWarehousingRuleFlow.getRuleType())) {
            resultMap.get(BzWarehousingRuleFlow.getRuleType()).stream().map(t -> convertToDto(t, WarehousingRuleFlowRequestVo.class))
                    .forEach(this::saveWarehousingRuleFlow);
        }
    }

    /**
     * 备份文件
     *
     * @param localPath 本地路径
     * @return 所有备份文件
     */
    private List<Pair<String, File>> backupFile(String localPath) {

        String backupPath = FileUtil.getFileFullName(localPath, "backup");
        new File(backupPath).mkdirs();
        return sftpFileConvertScriptRepository.findAllByCustomer(RequestContext.getCustomer()).stream().map(t -> {

            try {
                if (!hdfsUtil.checkFileExist(t.getHdfsFilePath())) {
                    log.info("{} 脚本在hdfs中不存在，不进行备份", t.getHdfsFilePath());
                    return null;
                }
                hdfsUtil.downloadToLocal(true, t.getHdfsFilePath(), FileUtil.getFileFullName(backupPath, t.getFileName()));
            } catch (IOException e) {
                throw new BpmsException(ResponseCode.Business.CONFIGURE_BACKUP_SCRIPT, t.getFileName());
            }
            return new Pair<>(t.getHdfsFilePath().substring(0, t.getHdfsFilePath().lastIndexOf(SLASH)), new File(backupPath, t.getFileName()));
        }).collect(Collectors.toList());

    }

    private Map<String, File> listFile(File localDir) {
        // 解压出来不是文件夹
        if (!localDir.isDirectory()) {
            throw new RuntimeException("文件解压出来不是文件夹");
        }

        // 遍历文件夹中每个文件
        Map<String, File> fileMap = new HashMap<>();
        for (File subFile : Objects.requireNonNull(localDir.listFiles())) {
            if (subFile.getName().toLowerCase().endsWith(EXCEL_FILE_SUF)) {
                // 判断excel文件大小
                if (subFile.length() > 100L * 1024L * 1024L) {
                    throw new BpmsException(ResponseCode.Common.FILE_SIZE_EXCEED_LIMIT, "10MB");
                }
                fileMap.compute("excel", (v1, v2) -> {
                    if (v2 == null) {
                        return subFile;
                    } else {
                        throw new BpmsException(ResponseCode.Business.CONFIGURE_MORE_THAN_EXCEL_FILE);
                    }
                });
            } else if (subFile.getName().toLowerCase().endsWith(FILE_SUFFIX_ZIP) || subFile.getName().toLowerCase().endsWith(FILE_SUFFIX_PY)) {
                // 判断py脚本所在zip文件大小
                if (subFile.length() > 50L * 1024L * 1024L) {
                    throw new BpmsException(ResponseCode.Common.FILE_SIZE_EXCEED_LIMIT, "50MB", subFile.getName());
                }
                fileMap.put(subFile.getName(), subFile);
            } else {
                throw new BpmsException(ResponseCode.Common.FILE_TYPE_NOT_SUPPORT, subFile.getName());
            }
        }

        return fileMap;
    }

    /**
     * 导出所有入库规则配置
     *
     * @param response 请求
     * @throws IOException
     */
    public void exportWarehousingRule(HttpServletResponse response) throws IOException {
        log.info("开始导出所有conf");
        String localPath = FileUtil.getFileFullName(confReadPath, UUID.randomUUID().toString());
        File localDir = new File(localPath);
        localDir.mkdirs();
        String fileName = FileUtil.getFileFullName(localPath, RequestContext.getCustomer() + UNDER_LINE + "自动入库规则配置" + UNDER_LINE + DateUtil.getDayBefortDays(0));
        response.setContentType("application/zip");
        response.setCharacterEncoding("UTF-8");
        String encodedFileName = URLEncoder.encode(fileName + ".zip", StandardCharsets.UTF_8.name());
        response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
        try {
            // 生成excel文件
            String excelFile = fileName + EXCEL_FILE_SUF;
            ExcelUtil.exportMultipleSheets(Files.newOutputStream(Paths.get(excelFile)), getConfigsData(false));

            // 下载py脚本
            sftpFileConvertScriptRepository.findAllByCustomer(RequestContext.getCustomer()).stream()
                    .map(t -> new Pair<>(t.getHdfsFilePath(), t.getFileName()))
                    .forEach(path -> {
                        try {
                            String localScriptFile = localPath + SLASH + path.getValue();
                            hdfsUtil.downloadToLocal(false, path.getKey(), localScriptFile);
                            // 清理crc文件
                            FileUtil.deleteFile(new File(localPath + SLASH + POINT + path.getValue() + POINT + CRC_SUFFIX));
                        } catch (IOException e) {
                            // do nothing
                        }
                    });

            // xlsx和script放入zip文件
            sendZipResponse(response, localDir);
            log.info("导出所有conf完成");
        } catch (BpmsException e) {
            throw e;
        } catch (Exception e) {
            throw new BpmsException(e, ResponseCode.Business.CONFIGURE_EXPORT_ERROR);
        } finally {
            FileUtil.deleteDir(localDir);
        }
    }

    public String exportWarehousingRuleMessage() {
        ArrayList<String> notExistFiles = new ArrayList<>();
        sftpFileConvertScriptRepository.findAllByCustomer(RequestContext.getCustomer()).stream()
                .map(t -> new Pair<>(t.getHdfsFilePath(), t.getFileName()))
                .forEach(path -> {
                    try {
                        if (!hdfsUtil.checkFileExist(path.getKey())) {
                            notExistFiles.add(path.getValue());
                        }
                    } catch (IOException e) {
                        // do nothing
                    }
                });
        StringBuilder sb = new StringBuilder();
        if (notExistFiles.size() > 0) {
            sb.append("导出文件缺少：");
            for (String fileName : notExistFiles) {
                sb.append(fileName).append(";");
            }
        }
        return sb.toString();
    }

    /**
     * 把文件夹内所有文件放入zip流并写入响应
     *
     * @param response 请求响应
     * @param filePath 文件夹
     * @throws IOException
     */
    private void sendZipResponse(HttpServletResponse response, File filePath) throws IOException {
        // 打包ZIP
        Path dir = Paths.get(filePath.getAbsolutePath());

        try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream())) {
            Files.walk(dir)
                    .filter(path -> !Files.isDirectory(path))
                    .forEach(path -> {
                        try {
                            String entryName = dir.relativize(path).toString().replace('\\', '/');
                            zos.putNextEntry(new ZipEntry(entryName));
                            Files.copy(path, zos);
                            zos.closeEntry();
                        } catch (IOException e) {
                            throw new UncheckedIOException(e);
                        }
                    });
        }
    }

    /**
     * 获取配置数据
     *
     * @param isEmpty 导出的是否为空数据
     * @return 配置数据
     */
    private List<ExcelUtil.SheetData<?>> getConfigsData(boolean isEmpty) {
        List<ExcelUtil.SheetData<?>> sheetDatas = new ArrayList<>();
        String customer = RequestContext.getCustomer();


        ExcelUtil.addSheetData(sheetDatas, isEmpty,
                () -> warehousingRuleFlowRepository.findAllByCustomerAndDeleteFlag(customer, 0),
                BzWarehousingRuleFlow.class,
                WarehousingRuleType.BzWarehousingRuleFlow);

        ExcelUtil.addSheetData(sheetDatas, isEmpty,
                () -> fileRenameRuleRepository.findAllByCustomer(customer),
                DwSftpFileRenameRule.class,
                WarehousingRuleType.DwSftpFileRenameRule);

        ExcelUtil.addSheetData(sheetDatas, isEmpty,
                () -> sftpSubCustomerRuleRepository.findAllByCustomer(customer),
                DwSftpSubCustomerRule.class,
                WarehousingRuleType.DwSftpSubCustomerRule);

        ExcelUtil.addSheetData(sheetDatas, isEmpty,
                () -> dataClearRuleRepository.findAllByCustomer(customer),
                DwDataClearRule.class,
                WarehousingRuleType.DwDataClearRule);

        ExcelUtil.addSheetData(sheetDatas, isEmpty,
                () -> sourceStandardDeviceIdRuleRepository.findAllByCustomer(customer),
                DwSourceStandardDeviceIdRule.class,
                WarehousingRuleType.DwSourceStandardDeviceIdRule);

        ExcelUtil.addSheetData(sheetDatas, isEmpty,
                () -> sourceStandardFieldRuleRepository.findAllByCustomer(customer),
                DwSourceStandardFieldRule.class,
                WarehousingRuleType.DwSourceStandardFieldRule);

        ExcelUtil.addSheetData(sheetDatas, isEmpty,
                () -> sftpFileConvertScriptRepository.findAllByCustomer(customer),
                DwSftpFileConvertScript.class,
                WarehousingRuleType.DwSftpFileConvertScript);
        return sheetDatas;
    }


    /**
     * 实体转换DTO工具方法
     */
    private <E, D> D convertToDto(E entity, Class<D> dtoClass) {
        try {
            D dto = dtoClass.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        } catch (Exception e) {
            throw new RuntimeException("DTO转换失败: " + dtoClass.getSimpleName(), e);
        }
    }

    public boolean checkScriptFileExist(DwSftpFileConvertScriptRequestVo dwSftpFileConvertScriptRequestVo) {
        String expectedHdfsPath = String.format("%s/%s/%s/%s/%s/%s/%s",
                adapterDir,
                RequestContext.getCustomer(),
                dwSftpFileConvertScriptRequestVo.getSubCustomer(),
                TestArea.of(dwSftpFileConvertScriptRequestVo.getTestArea()) == null ? EMPTY : TestArea.of(dwSftpFileConvertScriptRequestVo.getTestArea()),
                dwSftpFileConvertScriptRequestVo.getFactory(),
                dwSftpFileConvertScriptRequestVo.getFactorySite(),
                dwSftpFileConvertScriptRequestVo.getFileCategory());

        try {
            return hdfsUtil.checkFileExist(FileUtil.getFileFullName(expectedHdfsPath, dwSftpFileConvertScriptRequestVo.getFileName()));
        } catch (Exception e) {
            throw new BpmsException(e, ResponseCode.Common.CHECK_HDFS_FILE_FAIL);
        }
    }


    public Map<String, Object> getConstantMap() {
        Map<String, Object> map = new HashMap<>();

        // 填入可选字段conf可解析的字段
        List<String> analysisFields = Arrays.asList("testArea", "lotType", "offlineRetest", "offlineRetestIgnoreTp", "interrupt", "interruptIgnoreTp", "lotId", "sblotId", "testCod", "originWaferId", "waferNo", "testStage", "dupRetest", "dupRetestIgnoreTp", "batchNum", "batchNumIgnoreTp", "floorId", "testTemperature", "testerName", "testerType", "probecardLoadboardId", "testProgram", "testProgramVersion", "pkgTyp", "retestBinNum", "conditionSet", "startT", "finishT", "realWaferId");
        map.put("parseableField", analysisFields);

        // 填入testArea
        map.put("testArea", Arrays.stream(TestArea.values()).collect(Collectors.toMap(TestArea::getArea, TestArea::getTestScope)));

        // 填入fileCategory
        map.put("fileCategory", Arrays.asList(FileCategory.STDF.getCategory(), FileCategory.SUMMARY.getCategory(), FileCategory.LOT_RELATION.getCategory(), FileCategory.BIT_MEM.getCategory(), FileCategory.RAW_DATA.getCategory(), FileCategory.SHMOO.getCategory(), FileCategory.LOG.getCategory(), FileCategory.MAP.getCategory(), FileCategory.ZIP_SPLIT.getCategory(), FileCategory.WIP.getCategory(), FileCategory.WAT.getCategory()));

        // 填入可使用的metaData
        map.put("metadata", Arrays.asList("fileName", "partTyp", "lotId", "sblotId", "waferId", "flowId", "rtstCod", "testCod", "setupId", "floorId", "tstTemp", "nodeNam", "tstrTyp", "cardId", "loadId", "jobNam", "jobRev", "pkgTyp", "userTxt", "remoteOriginFilePath", "remoteOriginFileName", "startT", "finishT", "deviceInfos.product", "deviceInfos.testArea", "deviceInfos.deviceId", "deviceInfos.testStage", "deviceInfos.dataSource"));

        // 填入StdfFieldType
        map.put("stdfFieldType", Arrays.stream(StdfFieldType.values()).map(StdfFieldType::getType).collect(Collectors.toList()));

        // 填入DataClearRuleType
        map.put("dataClearRuleType", Arrays.stream(DataClearRuleType.values()).map(DataClearRuleType::getType).collect(Collectors.toList()));

        return map;
    }

    /**
     * 根据ID获取解析脚本配置规则并从HDFS下载文件
     *
     * @param id 解析脚本配置规则ID
     * @return SftpFileConvertScript对象
     */
    public SftpFileConvertScript getScriptFileById(Long id) {
        Optional<SftpFileConvertScript> optionalScript = sftpFileConvertScriptRepository.findById(id);
        if (!optionalScript.isPresent()) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "未找到ID为" + id + "的解析脚本配置规则");
        }
        return optionalScript.get();
    }

    /**
     * 从HDFS下载文件
     *
     * @param hdfsFilePath  HDFS文件路径
     * @param localFilePath 本地文件路径
     * @throws IOException IO异常
     */
    public void downloadFileFromHdfs(String hdfsFilePath, String localFilePath) throws IOException {
        hdfsUtil.downloadToLocal(false, hdfsFilePath, localFilePath);
    }

    public PageableDataVo<WarehousingRuleFlowVo> getWarehousingRuleFlow(WarehousingRuleFlowQueryVo warehousingRuleFlowQueryVo) {
        Pageable pageable = PageRequest.of(warehousingRuleFlowQueryVo.getJpaPageIndex(), warehousingRuleFlowQueryVo.getPageSize(), warehousingRuleFlowQueryVo.toSort());
        Page<WarehousingRuleFlow> warehousingRulePage = warehousingRuleFlowRepository.findPageByFilters(
                warehousingRuleFlowQueryVo.getNameList(),
                RequestContext.getCustomer(),
                warehousingRuleFlowQueryVo.getSubCustomerList(),
                warehousingRuleFlowQueryVo.getFactoryList(),
                warehousingRuleFlowQueryVo.getTestAreaList(),
                warehousingRuleFlowQueryVo.getFileCategoryList(),
                warehousingRuleFlowQueryVo.getCreateUserList(),
                warehousingRuleFlowQueryVo.getUpdateUserList(),
                warehousingRuleFlowQueryVo.getCreateTimeStart(),
                warehousingRuleFlowQueryVo.getCreateTimeEnd(),
                warehousingRuleFlowQueryVo.getUpdateTimeStart(),
                warehousingRuleFlowQueryVo.getUpdateTimeEnd(),
                pageable);
        log.info("查询入库规则流程结果, dataSize: {}, 当前页: {}", warehousingRulePage.getTotalElements(), warehousingRuleFlowQueryVo.getJpaPageIndex() + 1);
        return PageableDataVo.of(warehousingRulePage.map(WarehousingRuleFlowVo::of));
    }

    public void saveWarehousingRuleFlow(WarehousingRuleFlowRequestVo warehouseRuleFlowRequestVo) {
        Long id = warehouseRuleFlowRequestVo.getId();
        WarehousingRuleFlow warehousingRuleFlow;
        if (id != null) {
            warehousingRuleFlow = warehousingRuleFlowRepository.findById(warehouseRuleFlowRequestVo.getId()).orElseThrow(() -> new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT));
        } else {
            List<WarehousingRuleFlow> warehousingRuleFlowList = warehousingRuleFlowRepository.findAllByCustomerAndNameAndDeleteFlag(RequestContext.getCustomer(), warehouseRuleFlowRequestVo.getName(), 0);
            if (CollectionUtils.isEmpty(warehousingRuleFlowList)) {
                warehousingRuleFlow = new WarehousingRuleFlow();
            } else {
                warehousingRuleFlow = warehousingRuleFlowList.get(0);
            }
        }

        warehousingRuleFlow.setName(warehouseRuleFlowRequestVo.getName());
        warehousingRuleFlow.setCustomer(RequestContext.getCustomer());
        warehousingRuleFlow.setSubCustomer(warehouseRuleFlowRequestVo.getSubCustomer());
        warehousingRuleFlow.setFactory(warehouseRuleFlowRequestVo.getFactory());
        warehousingRuleFlow.setTestArea(TestArea.of(warehouseRuleFlowRequestVo.getTestArea()));
        warehousingRuleFlow.setFileCategory(FileCategory.of(warehouseRuleFlowRequestVo.getFileCategory()));
        warehousingRuleFlow.setRemarks(warehouseRuleFlowRequestVo.getRemarks());
        warehousingRuleFlow.setDeleteFlag(0);
        warehousingRuleFlow.setCreateTime(id == null ? new Date() : warehousingRuleFlow.getCreateTime());
        warehousingRuleFlow.setUpdateTime(new Date());
        warehousingRuleFlow.setUpdateUser(RequestContext.getUserName());
        warehousingRuleFlow.setCreateUser(id == null ? RequestContext.getUserName() : warehousingRuleFlow.getCreateUser());

        warehousingRuleFlowRepository.save(warehousingRuleFlow);
        log.info("入库规则流程保存成功, id: {}, name: {}", warehousingRuleFlow.getId(), warehousingRuleFlow.getName());
    }

    public boolean validateWarehousingRuleFlow(WarehousingRuleFlowRequestVo warehouseRuleFlowRequestVo) {
        List<WarehousingRuleFlow> warehousingRuleFlowList = warehousingRuleFlowRepository.findAllByCustomerAndNameAndDeleteFlag(RequestContext.getCustomer(), warehouseRuleFlowRequestVo.getName(), 0);
        if (CollectionUtils.isNotEmpty(warehousingRuleFlowList)) {
            for (WarehousingRuleFlow ruleFlow : warehousingRuleFlowList) {
                if (!Objects.equals(warehouseRuleFlowRequestVo.getId(), ruleFlow.getId())) {
                    log.info("入库规则流程名称已存在，name:{}", warehouseRuleFlowRequestVo.getName());
                    return false;
                }
            }
        } else {
            log.info("入库规则流程名称不存在，name:{}", warehouseRuleFlowRequestVo.getName());
        }
        return true;
    }

    public void duplicateWarehousingRuleFlow(Long id) {
        WarehousingRuleFlow warehousingRuleFlow = warehousingRuleFlowRepository.findFirstByIdAndDeleteFlag(id, 0).orElseThrow(() -> new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT));
        String name = warehousingRuleFlow.getName();
        log.info("复制入库规则流程, id:{}, name:{}", id, name);

        Set<String> duplicateNames = warehousingRuleFlowRepository.findAllByPrefixName(RequestContext.getCustomer(), name + DUPLICATE_SUFFIX)
                .stream().map(WarehousingRuleFlow::getName).collect(Collectors.toSet());
        log.info("相同前缀的入库规则流程：{}", duplicateNames);
        int index = 1;
        while (duplicateNames.contains(warehousingRuleFlow.getName() + DUPLICATE_SUFFIX + index)) {
            index++;
        }
        WarehousingRuleFlow duplicateWarehousingRuleFlow = WarehousingRuleFlow.builder()
                .name(warehousingRuleFlow.getName() + DUPLICATE_SUFFIX + index)
                .customer(warehousingRuleFlow.getCustomer())
                .subCustomer(warehousingRuleFlow.getSubCustomer())
                .factory(warehousingRuleFlow.getFactory())
                .testArea(warehousingRuleFlow.getTestArea())
                .fileCategory(warehousingRuleFlow.getFileCategory())
                .remarks(warehousingRuleFlow.getRemarks())
                .deleteFlag(0)
                .createTime(new Date())
                .updateTime(new Date())
                .createUser(RequestContext.getUserName())
                .updateUser(RequestContext.getUserName())
                .build();
        log.info("保存复制后的入库规则流程, name:{}", duplicateWarehousingRuleFlow.getName());
        warehousingRuleFlowRepository.save(duplicateWarehousingRuleFlow);
    }

    public void deleteWarehousingRuleFlow(Long id) {
        log.info("删除入库规则流程, id:{}", id);
        warehousingRuleFlowRepository.deleteAllByIds(Collections.singletonList(id));
        log.info("删除入库规则流程成功");
    }

    private void deleteDwSftpSubCustomerRule(List<DwSftpSubCustomerRuleRequestVo> dwSftpSubCustomerRuleRequestVos, WarehousingRuleFlowRequestVo warehouseRuleFlowRequestVo) {
        // 检查传入的 warehouseRuleFlowRequestVo 获取其中的 customer, sub_customer, factory, test_area
        String customer = RequestContext.getCustomer();
        String subCustomer = warehouseRuleFlowRequestVo.getSubCustomer();
        String factory = warehouseRuleFlowRequestVo.getFactory();
        TestArea testAreaEnum = TestArea.of(warehouseRuleFlowRequestVo.getTestArea());
        String testArea = testAreaEnum == null ? EMPTY : testAreaEnum.getTestScope();

        // 找出全部的id 并删除其中不在传入id里的数据
        List<Long> ids = dwSftpSubCustomerRuleRequestVos.stream().map(DwSftpSubCustomerRuleRequestVo::getId).collect(Collectors.toList());
        List<Long> allIds = sftpSubCustomerRuleRepository.findAllIdsByCustomerAndSubCustomerAndFactoryAndTestArea(customer, subCustomer, factory, testArea);
        if (!CollectionUtils.isEmpty(allIds)) {
            List<Long> deleteIds = allIds.stream().filter(id -> !ids.contains(id)).collect(Collectors.toList());
            // 找出要删除的id后,遍历 deleteIds获取数据并将删除记录到  modifyWarehousingRuleRecord
            for (Long id : deleteIds) {
                Optional<SftpSubCustomerRule> optionalOriginalRule = sftpSubCustomerRuleRepository.findById(id);
                SftpSubCustomerRule originalRule = optionalOriginalRule.get();
                String originalDataSnapshot = JSONObject.toJSONString(originalRule);
                ModifyWarehousingRuleRecord modifyWarehousingRuleRecord = buildModifyWarehousingRuleRecord(OperationType.DELETE, WarehousingRuleType.DwSftpSubCustomerRule, originalDataSnapshot);
                modifyWarehousingRuleRecordRepository.save(modifyWarehousingRuleRecord);
            }
            if (!CollectionUtils.isEmpty(deleteIds)) {
                log.info("删除 dwSftpSubCustomerRule: {}", deleteIds);
                sftpSubCustomerRuleRepository.deleteAllByIds(deleteIds);
            }
        }
    }

    public List<String> extractConvertScriptFiles(MultipartFile zipFile) {
        List<String> allFiles = new ArrayList<>();

        try (InputStream is = zipFile.getInputStream();
             ArchiveInputStream ais = new ZipArchiveInputStream(is)) {

            ArchiveEntry entry;
            while ((entry = ais.getNextEntry()) != null) {
                log.info("正在处理文件：{}", entry.getName());
                if (!entry.isDirectory() && entry.getName().endsWith(FILE_SUFFIX_PY)) {
                    allFiles.add(entry.getName());
                }
            }
        } catch (IOException e) {
            // 记录日志或向上抛
            log.error("Failed to read zip file", e);
            return Collections.emptyList();
        }

        // 只有一个文件 → 直接返回
        if (allFiles.size() == 1) {
            return allFiles;
        }

        // 多个文件 → 按规则过滤
        return allFiles.stream()
                .filter(path -> path.contains(ETL_GATEWAY_DIR))
                .filter(path -> getFileName(path).contains(ENTRY_NAME))
                .collect(Collectors.toList());
    }

    /**
     * 从完整路径截取文件名（不含目录）
     */
    private String getFileName(String fullPath) {
        int slash = fullPath.lastIndexOf(File.separator);
        if (slash >= 0) {
            return fullPath.substring(slash + 1);
        }
        return fullPath;
    }

    public void downloadScriptFileById(Long id, HttpServletResponse response) throws IOException {
        // 1. 校验并取出脚本配置
        SftpFileConvertScript script = sftpFileConvertScriptRepository.findById(id)
                .orElseThrow(() -> new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID,
                        "未找到ID为 " + id + " 的解析脚本配置规则"));

        if (!hdfsUtil.checkFileExist(script.getHdfsFilePath())) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID,
                    "未找到ID为 " + id + " 的解析脚本配置规则对应的文件");
        }

        // 2. 计算指定目录下的临时文件路径,创建临时文件
        Path tempDir = Paths.get(confReadPath, UUID.randomUUID().toString());
        if (!Files.exists(tempDir)) {
            Files.createDirectories(tempDir);
        }
        Path tempFile = tempDir.resolve(script.getFileName());
        try {
            // 3. 把 HDFS 文件拉到本地临时文件
            hdfsUtil.downloadToLocal(false, script.getHdfsFilePath(), tempFile.toString());

            // 4. 设置响应头
            String encoded = URLEncoder.encode(script.getFileName(), StandardCharsets.UTF_8.name())
                    .replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition",
                    "attachment; filename=\"" + encoded + "\"; filename*=utf-8''" + encoded);
            // 5. 把文件内容写给客户端
            Files.copy(tempFile, response.getOutputStream());
        } catch (IOException | RuntimeException e) {
            throw new BpmsException(ResponseCode.Business.CONFIGURE_DOWNLOAD_SCRIPT, script.getExecuteFileName());
        } finally {
            // 6. 无论如何都删临时文件（已放在 try-with-resources，保险起见再次确认）
            Files.deleteIfExists(tempFile);
        }
    }
}
