package com.guwave.datahub.bpms.app.vo.bz.priority.request;

import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@EqualsAndHashCode(callSuper = false)
@Data
@Schema(description = "添加优先级配置请求")
public class PriorityConfigAddVo {

    @NotBlank(message = "customer不能为空")
    @Schema(description = "customer")
    private String customer;

    @NotBlank(message = "subCustomer不能为空")
    @Schema(description = "subCustomer")
    private String subCustomer;

    @NotBlank(message = "factory不能为空")
    @Schema(description = "factory")
    private String factory;

    @NotBlank(message = "factorySite不能为空")
    @Schema(description = "factorySite")
    private String factorySite;

    @Schema(description = "deviceId")
    private String deviceId;

    @Schema(description = "testStage")
    private String testStage;

    @NotBlank(message = "lotType不能为空")
    @Schema(description = "lotType")
    private String lotType;

    @Schema(description = "lotId")
    private String lotId;

    @Schema(description = "waferNo")
    private String waferNo;

    @NotBlank(message = "testArea不能为空")
    @Schema(description = "testArea")
    private String testArea;

    @NotBlank(message = "fileCategory不能为空")
    @Schema(description = "fileCategory")
    private String fileCategory;

    @NotBlank(message = "优先级不能为空")
    @Schema(description = "优先级")
    private String priority;

    @Schema(description = "最小的startT")
    private Date minStartT;

    @Schema(description = "最大的startT")
    private Date maxStartT;

    @Schema(description = "最小FTP修改时间")
    private Date minFtpMtime;

    @Schema(description = "最大FTP修改时间")
    private Date maxFtpMtime;

    @Schema(description = "FTP路径")
    private String ftpPath;
}
