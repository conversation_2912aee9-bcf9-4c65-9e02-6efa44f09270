package com.guwave.datahub.bpms.app.util;

import com.guwave.datahub.bpms.common.constant.ChartData;
import com.guwave.datahub.bpms.common.constant.ChartType;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.jfree.chart.plot.PlotOrientation;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Stream;

import static com.guwave.onedata.dataware.common.contant.Constant.UNDER_LINE;

public class PdfUtil {
    private final PDDocument document;
    private PDPage currentPage;
    private PDPageContentStream contentStream;
    private final float margin = 40;
    private float yPosition;
    private final PDFont font;
    private final String readPath;
    private static final Pattern SIM_FONT_PATTERN = Pattern.compile("(?i)^sim.*\\.ttf$");


    // 初始化文档
    public PdfUtil(String fontPath, String readPath) throws IOException {
        this.document = new PDDocument();
        this.readPath = readPath;

        // 使用sim系列的字体
        try (Stream<Path> pathStream = Files.list(Paths.get(fontPath))) {
            File file = pathStream
                    .filter(Files::isRegularFile)
                    .filter(p -> SIM_FONT_PATTERN.matcher(p.getFileName().toString()).matches())
                    .findFirst()
                    .map(Path::toFile)
                    .orElseThrow(() -> new RuntimeException("未找到匹配的Sim系列字体文件"));
            this.font = PDType0Font.load(document, file);
        } catch (IOException e) {
            throw new RuntimeException("字体加载异常", e);
        }
        addNewPage();
    }

    /**
     * 添加新页面
     *
     * @throws IOException IOException
     */
    private void addNewPage() throws IOException {
        if (contentStream != null) contentStream.close();
        currentPage = new PDPage();
        document.addPage(currentPage);
        contentStream = new PDPageContentStream(document, currentPage);
        yPosition = currentPage.getMediaBox().getHeight() - margin;
    }

    /**
     * 添加一级标题
     *
     * @param text text
     * @throws IOException IOException
     */
    public void addPrimaryHeading(String text) throws IOException {
        addHeading(text, 24, 20);
    }

    /**
     * 添加二级标题
     *
     * @param text text
     * @throws IOException IOException
     */
    public void addSecondaryHeading(String text) throws IOException {
        addNewPage();
        addHeading(text, 18, 15);
    }

    /**
     * 添加正文内容
     *
     * @param text text
     * @throws IOException IOException
     */
    public void addBodyText(String text) throws IOException {
        addText(text, 12, 12);
    }

    /**
     * 添加标题
     *
     * @param text     text
     * @param fontSize fontSize
     * @param spacing  spacing
     * @throws IOException IOException
     */
    private void addHeading(String text, int fontSize, float spacing) throws IOException {
        checkPageSpace(fontSize + spacing, fontSize);
        contentStream.setFont(font, fontSize);
        contentStream.beginText();
        contentStream.newLineAtOffset(margin, yPosition);
        contentStream.showText(text);
        contentStream.endText();
        yPosition -= (fontSize + spacing);
    }

    /**
     * 添加文本
     *
     * @param text        text
     * @param fontSize    fontSize
     * @param lineSpacing lineSpacing
     * @throws IOException IOException
     */
    private void addText(String text, int fontSize, float lineSpacing) throws IOException {
        contentStream.setFont(font, fontSize);
        float maxWidth = currentPage.getMediaBox().getWidth() - 2 * margin;

        for (String line : wrapText(text, fontSize, maxWidth)) {
            checkPageSpace(fontSize + lineSpacing, fontSize);
            contentStream.beginText();
            contentStream.newLineAtOffset(margin, yPosition);
            contentStream.showText(line);
            contentStream.endText();
            yPosition -= (fontSize + lineSpacing);
        }
    }

    /**
     * 根据数据生成图表并放入pdf中
     *
     * @param data      data
     * @param chartType chartType
     * @param title     title
     * @throws IOException IOException
     */
    public void addChart(List<ChartData> data, ChartType chartType, String title) throws IOException {
        File imageFile = new File(FileUtil.getFileFullName(readPath, java.util.UUID.randomUUID() + UNDER_LINE + "image.png"));
        ChartUtil.generateChart(data, chartType, title, imageFile);
        addImage(imageFile);
    }

    /**
     * 根据数据生成表格并放入pdf中
     *
     * @param data      data
     * @param chartType chartType
     * @param title     title
     * @throws IOException IOException
     */
    public void addChart(List<ChartData> data, ChartType chartType, String title, String XAxisName, String YAxisName) throws IOException {
        addChart(data, chartType, title, XAxisName, YAxisName, PlotOrientation.HORIZONTAL, false);
    }

    /**
     *
     * @param data 数据
     * @param chartType 表格类型
     * @param title 标题
     * @param XAxisName X轴名称
     * @param YAxisName Y轴名称
     * @param orientation 方向
     * @param displayLable 表格中是否显示值
     * @throws IOException
     */
    public void addChart(List<ChartData> data, ChartType chartType, String title, String XAxisName, String YAxisName, PlotOrientation orientation, boolean displayLable) throws IOException {
        File imageFile = new File(FileUtil.getFileFullName(readPath, java.util.UUID.randomUUID() + UNDER_LINE + "image.png"));
        ChartUtil.generateChart(data, chartType, title, imageFile, XAxisName, YAxisName, orientation, displayLable);
        addImage(imageFile);
    }

    /**
     * 根据数据生成表格并放入pdf中
     *
     * @param data 数据
     * @param chartType 表格类型
     * @param title 标题
     * @param XAxisName X轴名称
     * @param YAxisName Y轴名称
     * @param orientation 方向
     * @throws IOException IOException
     */
    public void addChart(List<ChartData> data, ChartType chartType, String title, String XAxisName, String YAxisName, PlotOrientation orientation) throws IOException {
        addChart(data, chartType, title, XAxisName, YAxisName, orientation, false);
    }

    /**
     * 根据数据生成表格并放入pdf中
     *
     * @param data 数据
     * @param chartType 表格类型
     * @param title 标题
     * @param XAxisName X轴名称
     * @param YAxisName Y轴名称
     * @param displayLable 表格中是否显示值
     * @throws IOException IOException
     */
    public void addChart(List<ChartData> data, ChartType chartType, String title, String XAxisName, String YAxisName, boolean displayLable) throws IOException {
        addChart(data, chartType, title, XAxisName, YAxisName, PlotOrientation.VERTICAL, displayLable);
    }

    /**
     * 添加图片
     *
     * @param imageFile imageFile
     * @throws IOException IOException
     */
    private void addImage(File imageFile) throws IOException {
        // 获取图片原始尺寸
        PDImageXObject pdImage = PDImageXObject.createFromFileByContent(imageFile, document);
        float imageWidth = pdImage.getWidth();
        float imageHeight = pdImage.getHeight();

        // 计算可用宽度（页面宽度 - 左右边距）
        float maxWidth = currentPage.getMediaBox().getWidth() - 2 * margin;

        // 自动缩放计算（保持宽高比）
        float scaledWidth = Math.min(imageWidth, maxWidth);
        float scaledHeight = (scaledWidth / imageWidth) * imageHeight;

        // 检查页面剩余空间
        if (yPosition - scaledHeight < margin) {
            addNewPage();
        }

        // 自动居中计算
        float x = (currentPage.getMediaBox().getWidth() - scaledWidth) / 2;
        float y = yPosition - scaledHeight;

        // 绘制图片
        contentStream.drawImage(pdImage, x, y, scaledWidth, scaledHeight);

        // 更新Y坐标（留25像素间距）
        yPosition = y - 25;

        // 删除文件
        FileUtil.deleteFile(imageFile);
    }

    /**
     * 页面空间检查
     *
     * @param requiredSpace requiredSpace
     * @throws IOException IOException
     */
    private void checkPageSpace(float requiredSpace, int fontSize) throws IOException {
        if (yPosition < (margin + requiredSpace)) {
            addNewPage();
            contentStream.setFont(font, fontSize);
        }
    }

    /**
     * 处理文本，按照页面大小自动换行
     *
     * @param text     text
     * @param fontSize fontSize
     * @param maxWidth maxWidth
     * @return List<String> 每个元素是一行
     * @throws IOException IOException
     */
    private List<String> wrapText(String text, int fontSize, float maxWidth)
            throws IOException {
        List<String> lines = new ArrayList<>();
        StringBuilder currentLine = new StringBuilder();
        float currentWidth = 0;

        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            float charWidth = font.getStringWidth(String.valueOf(c)) / 1000 * fontSize;

            // 换行条件：当前行宽超过限制 或 遇到换行符
            if (currentWidth + charWidth > maxWidth || c == '\n') {
                lines.add(currentLine.toString());
                currentLine = new StringBuilder();
                currentWidth = 0;
            }

            // 非换行符时添加字符
            if (c != '\n') {
                currentLine.append(c);
                currentWidth += charWidth;
            }
        }
        lines.add(currentLine.toString());
        return lines;
    }

    /**
     * 保存pdf文件
     *
     * @param filename filename
     * @throws IOException IOException
     */
    public void saveDocument(String filename) throws IOException {
        contentStream.close();
        document.save(filename);
        document.close();
    }
}