package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.request;

import com.guwave.datahub.bpms.app.vo.web.SortPageableRequestVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper=false)
@Schema(title = "入库规则流程查询")
public class WarehousingRuleFlowQueryVo extends SortPageableRequestVo {

   @Schema(description = "流程名称")
    private List<String> nameList;

   @Schema(description = "子客户")
    private List<String> subCustomerList;

   @Schema(description = "工厂")
    private List<String> factoryList;

   @Schema(description = "测试区域")
    private List<String> testAreaList;

   @Schema(description = "文件类别")
    private List<String> fileCategoryList;

   @Schema(description = "创建用户")
    private List<String> createUserList;

   @Schema(description = "创建时间开始", example = "2023-09-20 08:14:13")
    private String createTimeStart;

   @Schema(description = "创建时间结束", example = "2023-09-21 08:14:13")
    private String createTimeEnd;

   @Schema(description = "更新用户")
    private List<String> updateUserList;

    @Schema(description = "更新时间开始", example = "2023-09-20 08:14:13")
    private String updateTimeStart;

    @Schema(description = "更新时间结束", example = "2023-09-21 08:14:13")
    private String updateTimeEnd;

}
