package com.guwave.datahub.bpms.app.executor;

import com.guwave.datahub.bpms.common.constant.ExecuteEngine;
import com.guwave.datahub.bpms.dao.domain.schedule.ScheduleTaskRecord;
import com.guwave.onedata.next.compute.api.vo.response.ComputeResponse;

/**
 * Copyright (C), 2023, guwave
 * <p>
 * 执行器
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2023-07-28 11:01:19
 */
public interface Executor {
    Boolean support(ExecuteEngine executeEngine);

    default ComputeResponse doExecute(ScheduleTaskRecord taskRecord) throws Exception {
        return null;
    }

    default void doExecute(String command) throws Exception {
    }
}
