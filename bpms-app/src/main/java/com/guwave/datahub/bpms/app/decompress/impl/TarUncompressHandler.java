package com.guwave.datahub.bpms.app.decompress.impl;

import com.guwave.datahub.bpms.app.decompress.UnCompressHandler;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;

public class TarUncompressHandler implements UnCompressHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(TarUncompressHandler.class);

    @Override
    public void unCompress(File file, File targetDir) throws Exception {
        try (InputStream inputStream = new FileInputStream(file);
             TarArchiveInputStream tarArchiveInputStream = new TarArchiveInputStream(inputStream)) {
            byte[] buffer = new byte[1024 * 5];
            ArchiveEntry archiveEntry;
            while (null != (archiveEntry = tarArchiveInputStream.getNextEntry())) {
                String archiveEntryFileName = archiveEntry.getName();
                File entryFile = new File(targetDir, archiveEntryFileName);
                if (!archiveEntry.isDirectory()) {
                    if (!entryFile.getParentFile().exists()) {
                        entryFile.getParentFile().mkdirs();
                    }
                    try (FileOutputStream outputStream = new FileOutputStream(entryFile)) {
                        int length;
                        while ((length = tarArchiveInputStream.read(buffer)) != -1) {
                            outputStream.write(buffer, 0, length);
                        }
                        outputStream.flush();
                    } catch (Exception e) {
                        LOGGER.info("解压失败：{} --> {}", file.getAbsolutePath(), targetDir, e);
                        throw e;
                    }
                } else {
                    if (!entryFile.exists()) {
                        entryFile.mkdirs();
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.info("解压失败：{} --> {}", file.getAbsolutePath(), targetDir, e);
            throw e;
        }
    }
}
