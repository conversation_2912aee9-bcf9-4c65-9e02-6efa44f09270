package com.guwave.datahub.bpms.app.service.bz;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.exception.BpmsRpcInvokeException;
import com.guwave.datahub.bpms.app.util.ExcelUtil;
import com.guwave.datahub.bpms.app.util.HdfsUtil;
import com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.model.DataIntegrityExportDTO;
import com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.model.DataIntegrityRepairDimension;
import com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.model.FtpStepMappingEnum;
import com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.request.*;
import com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.response.DataIntegrityBaseVo;
import com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.response.DataIntegrityDeleteRecordVo;
import com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.response.DataIntegrityQueryDropDownVo;
import com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.response.DataIntegrityRecordVo;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.dao.domain.repair.DataIntegrityDeleteLog;
import com.guwave.datahub.bpms.dao.domain.repair.DataRepairBatchInfo;
import com.guwave.datahub.bpms.dao.domain.repair.DataRepairEffectBatch;
import com.guwave.datahub.bpms.dao.domain.repair.DataRepairHistory;
import com.guwave.datahub.bpms.dao.domain.repair.DataRepairInfo;
import com.guwave.datahub.bpms.dao.repository.bpms.DataRepairBatchInfoRepository;
import com.guwave.datahub.bpms.dao.repository.bpms.DataRepairEffectBatchRepository;
import com.guwave.datahub.bpms.dao.repository.bpms.DataRepairHistoryRepository;
import com.guwave.datahub.bpms.dao.repository.bpms.DataRepairInfoRepository;
import com.guwave.datahub.bpms.dao.repository.dw.*;
import com.guwave.datahub.bpms.dao.repository.bpms.DataIntegrityDeleteLogRepository;
import com.guwave.datahub.bpms.dao.repository.dw.FileWarehousingRecordRepository;
import com.guwave.datahub.bpms.dao.repository.dw.mapper.FileWarehousingRecordMapper;
import com.guwave.datahub.bpms.dao.vo.bpms.dataIntegrity.DataIntegrityBase;
import com.guwave.datahub.bpms.dao.vo.bpms.dataIntegrity.DataIntegrityDeleteLogDropDown;
import com.guwave.datahub.bpms.dao.vo.bpms.dataIntegrity.DataIntegrityDropDown;
import com.guwave.datahub.bpms.dao.vo.bpms.dataIntegrity.DataIntegrityRecord;
import com.guwave.onedata.dataware.bridge.api.iface.IDataRepairRpcService;
import com.guwave.onedata.dataware.bridge.api.vo.request.RepairVo;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.model.repair.DataRepairField;
import com.guwave.onedata.dataware.common.util.WaferUtil;
import com.guwave.onedata.dataware.dao.mysql.domain.dc.FileInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.hadoop.io.IOUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;


@Slf4j
@Service
public class DataIntegrityService {


    public final static List<String> ALL_TEST_AREAS = new ArrayList<>();
    public final static List<String> ALL_FILE_CATEGORYS = new ArrayList<>();

    static {
        ALL_TEST_AREAS.addAll(TestArea.getCPList().stream().map(TestArea::getArea).collect(Collectors.toList()));
        ALL_TEST_AREAS.addAll(TestArea.getFTList().stream().map(TestArea::getArea).collect(Collectors.toList()));

        ALL_FILE_CATEGORYS.add(FileCategory.STDF.getCategory());
        ALL_FILE_CATEGORYS.add(FileCategory.RAW_DATA.getCategory());
    }

    private final static Map<RepairField, BiConsumer<DataRepairInfo, String>> MODIFY_FIELD_CONSUMER_MAP = new HashMap<RepairField, BiConsumer<DataRepairInfo, String>>() {{
        put(RepairField.DEVICE_ID, DataRepairInfo::setModifyDeviceId);
        put(RepairField.LOT_ID, DataRepairInfo::setModifyLotId);
        put(RepairField.WAFER_NO, DataRepairInfo::setModifyWaferNo);
        put(RepairField.TEST_PROGRAM, DataRepairInfo::setModifyTestProgram);
        put(RepairField.TEST_STAGE, DataRepairInfo::setModifyTestStage);
        put(RepairField.LOT_TYPE, DataRepairInfo::setModifyLotType);
        put(RepairField.FLOW_ID, DataRepairInfo::setModifyFlowId);
        put(RepairField.SBLOT_ID, DataRepairInfo::setModifySblotId);
        put(RepairField.RETEST_BIN_NUM, DataRepairInfo::setModifyRetestHbinNumber);
    }};

    @Autowired
    private FileWarehousingRecordRepository fileWarehousingRecordRepository;
    @Autowired
    private DataIntegrityDeleteLogRepository dataIntegrityDeleteLogRepository;
    @Autowired
    private DataRepairBatchInfoRepository dataRepairBatchInfoRepository;
    @Autowired
    private DataRepairInfoRepository dataRepairInfoRepository;
    @Autowired
    private DataRepairHistoryRepository dataRepairHistoryRepository;
    @Autowired
    private DataRepairEffectBatchRepository dataRepairEffectBatchRepository;
    @Autowired
    private DcFileInfoRepository dcFileInfoRepository;
    @Autowired
    private HdfsUtil hdfsUtil;
    @DubboReference
    private IDataRepairRpcService dataRepairRpcService;
    @Autowired
    private FileWarehousingRecordMapper fileWarehousingRecordMapper;


    public List<DataIntegrityQueryDropDownVo> queryDropDown(DataIntegrityQueryDropDownFilterVo condition) {
        condition.validCondition();
        if ("TEST_AREA".equalsIgnoreCase(condition.getFilterField())) {
            return condition.getTestAreaList().stream().map(DataIntegrityQueryDropDownVo::of).collect(Collectors.toList());
        }
        condition.setFileNameList(queryFileNames(condition.getFileIdList()));
        List<DataIntegrityDropDown> dropDowns = fileWarehousingRecordMapper.queryDataIntegrityDropDown(
                RequestContext.getCustomer(),
                condition.getTestAreaList(),
                condition.getFileCategoryList(),
                condition.getStartTime(),
                condition.getEndTime(),
                condition.getSubCustomerList(),
                condition.getFactoryList(),
                condition.getDeviceIdList(),
                condition.getTestStageList(),
                condition.getLotTypeList(),
                condition.getLotIdList(),
                condition.getSblotIdList(),
                condition.getWaferIdList(),
                condition.getDataLoadedList(),
                condition.getLoadingFailedTypeList(),
                condition.getTestProgramList(),
                condition.getFileLocation(),
                condition.getFileName(),
                condition.getFilterField(),
                condition.getFileNameList()
        );
        return dropDowns.stream().map(DataIntegrityQueryDropDownVo::of).collect(Collectors.toList());
    }

    private List<String> queryFileNames(List<Long> fileIds) {
        if (CollectionUtils.isEmpty(fileIds)) {
            return null;
        }
        List<String> fileNames = dcFileInfoRepository.findAllByIdInAndUploadTypeAndDeleteFlag(fileIds, UploadType.AUTO, false)
                .stream().map(FileInfo::getFileName).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fileNames)) {
            return null;
        }
        return fileNames;
    }

    public DataIntegrityBaseVo queryIntegrityBase(DataIntegrityQueryVo condition) {
        condition.validCondition(false);
        condition.setFileNameList(queryFileNames(condition.getFileIdList()));
        DataIntegrityBase dataIntegrityBase = fileWarehousingRecordMapper.queryDataIntegrityBase(
                RequestContext.getCustomer(),
                condition.getTestAreaList(),
                condition.getFileCategoryList(),
                condition.getStartTime(),
                condition.getEndTime(),
                condition.getSubCustomerList(),
                condition.getFactoryList(),
                condition.getDeviceIdList(),
                condition.getTestStageList(),
                condition.getLotTypeList(),
                condition.getLotIdList(),
                condition.getSblotIdList(),
                condition.getWaferIdList(),
                condition.getDataLoadedList(),
                condition.getLoadingFailedTypeList(),
                condition.getTestProgramList(),
                condition.getFileLocation(),
                condition.getFileName(),
                condition.getFileNameList()
        );
        log.info("dataIntegrityBase: {}", JSON.toJSONString(dataIntegrityBase));
        DataIntegrityBaseVo dataIntegrityBaseVo = new DataIntegrityBaseVo();
        dataIntegrityBaseVo.setTotalCnt(dataIntegrityBase.getTotalCnt());
        dataIntegrityBaseVo.setSuccessCnt(dataIntegrityBase.getSuccessCnt());
        dataIntegrityBaseVo.setFailNumCnt(dataIntegrityBase.getFailCnt());
        dataIntegrityBaseVo.setProcessCnt(dataIntegrityBase.getTotalCnt() - dataIntegrityBase.getSuccessCnt() - dataIntegrityBase.getFailCnt());
        dataIntegrityBaseVo.setBaseIndexValue(dataIntegrityBaseVo.calRatio(dataIntegrityBase.getSuccessCnt()));

        new DataIntegrityBaseVo.StatisticsDetailDTO(FtpStepMappingEnum.FTP_FILE_COUNT, dataIntegrityBaseVo, dataIntegrityBase.getTotalCnt());
        long calculatePlatformSuccessCnt = dataIntegrityBase.getTotalCnt() - dataIntegrityBase.getTransferToPlatformFailCnt();
        new DataIntegrityBaseVo.StatisticsDetailDTO(FtpStepMappingEnum.CALCULATE_PLATFORM, dataIntegrityBaseVo, calculatePlatformSuccessCnt);
        long triggerParseSuccessCnt = dataIntegrityBase.getSuccessCnt() + dataIntegrityBase.getKeyFieldSuccessCnt() + dataIntegrityBase.getKeyFieldFailCnt();
        new DataIntegrityBaseVo.StatisticsDetailDTO(FtpStepMappingEnum.TRIGGER_PARSE, dataIntegrityBaseVo, triggerParseSuccessCnt);
        long keyFieldCheckSuccessCnt = dataIntegrityBase.getSuccessCnt() + dataIntegrityBase.getKeyFieldSuccessCnt();
        new DataIntegrityBaseVo.StatisticsDetailDTO(FtpStepMappingEnum.KEY_FIELD_CHECK, dataIntegrityBaseVo, keyFieldCheckSuccessCnt);
        new DataIntegrityBaseVo.StatisticsDetailDTO(FtpStepMappingEnum.PARSE_AND_IN_DB, dataIntegrityBaseVo, dataIntegrityBase.getSuccessCnt());
        return dataIntegrityBaseVo;
    }

    public PageableDataVo<DataIntegrityRecordVo> queryRecords(DataIntegrityQueryVo condition) {
        condition.validCondition(false);
        condition.setFileNameList(queryFileNames(condition.getFileIdList()));
        List<DataIntegrityRecord> dataIntegritys = fileWarehousingRecordMapper.queryDataIntegrityRecord(
                RequestContext.getCustomer(),
                condition.getTestAreaList(),
                condition.getFileCategoryList(),
                condition.getStartTime(),
                condition.getEndTime(),
                condition.getSubCustomerList(),
                condition.getFactoryList(),
                condition.getDeviceIdList(),
                condition.getTestStageList(),
                condition.getLotTypeList(),
                condition.getLotIdList(),
                condition.getSblotIdList(),
                condition.getWaferIdList(),
                condition.getDataLoadedList(),
                condition.getLoadingFailedTypeList(),
                condition.getTestProgramList(),
                condition.getFileLocation(),
                condition.getFileName(),
                condition.getOrderByField(),
                condition.getOrderByType(),
                condition.getJpaPageIndex() * condition.getPageSize().longValue(),
                condition.getPageSize().longValue(),
                condition.getFileNameList()
        );
        List<DataIntegrityRecordVo> dataIntegrityRecordVos = dataIntegritys.stream().map(DataIntegrityRecordVo::of).collect(Collectors.toList());
        Long count = fileWarehousingRecordMapper.countDataIntegrityRecord(
                RequestContext.getCustomer(),
                condition.getTestAreaList(),
                condition.getFileCategoryList(),
                condition.getStartTime(),
                condition.getEndTime(),
                condition.getSubCustomerList(),
                condition.getFactoryList(),
                condition.getDeviceIdList(),
                condition.getTestStageList(),
                condition.getLotTypeList(),
                condition.getLotIdList(),
                condition.getSblotIdList(),
                condition.getWaferIdList(),
                condition.getDataLoadedList(),
                condition.getLoadingFailedTypeList(),
                condition.getTestProgramList(),
                condition.getFileLocation(),
                condition.getFileName(),
                condition.getFileNameList()
        );

        return PageableDataVo.of(new PageImpl<>(dataIntegrityRecordVos, PageRequest.of(condition.getJpaPageIndex(), condition.getPageSize()), count));
    }


    public void downloadFile(DataIntegrityDownloadFileVo downloadFileVo, HttpServletResponse response) throws IOException {
        log.info("下载 {}", downloadFileVo.getFileName());
        List<FileWarehousingRecord> files = fileWarehousingRecordRepository.findAllByFileNameIn(Lists.newArrayList(downloadFileVo.getFileName()));
        if (CollectionUtils.isEmpty(files)) {
            throw new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT);
        }
        String hdfsPath = files.get(0).getHdfsPath();
        if (StringUtils.isEmpty(hdfsPath) || !hdfsUtil.checkFileExist(hdfsPath)) {
            throw new BpmsException(ResponseCode.Business.DATA_INTEGRITY_FILE_NOT_EXISTS, downloadFileVo.getFileName());
        }
        try (InputStream stream = hdfsUtil.openFile(hdfsPath); ServletOutputStream outputStream = response.getOutputStream()) {
            response.addHeader("Content-Disposition", "attachment;filename=" + downloadFileVo.getFileName());
            response.setContentType("application/octet-stream");
            IOUtils.copyBytes(stream, outputStream, 4096, true);
            log.info("文件下载完成:{}", hdfsPath);
        } catch (Exception e) {
            log.info("下载文件 {} 异常：", hdfsPath, e);
            throw new BpmsException(ResponseCode.Common.FILE_READ_ERROR, "IO异常");
        }
    }

    public void exportTable(DataIntegrityQueryVo condition, HttpServletResponse response) throws IOException {
        log.info("导出表格内容");
        condition.validCondition(true);
        condition.setFileNameList(queryFileNames(condition.getFileIdList()));
        response.setCharacterEncoding("gb2312");
        response.setContentType("text/csv;charset=gb2312");
        String fileName = "数据完整性指标.xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        List<DataIntegrityRecord> dataIntegritys = fileWarehousingRecordMapper.queryDataIntegrityRecord(
                RequestContext.getCustomer(),
                condition.getTestAreaList(),
                condition.getFileCategoryList(),
                condition.getStartTime(),
                condition.getEndTime(),
                condition.getSubCustomerList(),
                condition.getFactoryList(),
                condition.getDeviceIdList(),
                condition.getTestStageList(),
                condition.getLotTypeList(),
                condition.getLotIdList(),
                condition.getSblotIdList(),
                condition.getWaferIdList(),
                condition.getDataLoadedList(),
                condition.getLoadingFailedTypeList(),
                condition.getTestProgramList(),
                condition.getFileLocation(),
                condition.getFileName(),
                condition.getOrderByField(),
                condition.getOrderByType(),
                0L,
                300000L,
                condition.getFileNameList()
        );
        List<DataIntegrityExportDTO> exportData = dataIntegritys.stream().map(DataIntegrityExportDTO::of).collect(Collectors.toList());
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            ExcelUtil.exportDataWithDefaultStyle(outputStream, exportData, DataIntegrityExportDTO.class, null);
        }
        response.setHeader("Content-Disposition", String.format("attachment;filename:%s", new String(fileName.getBytes("gb2312"))));
        log.info("导出表格内容完成 {} 条", exportData.size());
    }

    public Boolean remark(DataIntegrityRemarkVo remarkVo) {
        return dataRepairRpcService.updateDataIntegrityFileComment(remarkVo.getFileNames(), remarkVo.getRemark());
    }

    public Boolean fileLabel(DataIntegrityFileLabelVo fileLabelVo) {
        return dataRepairRpcService.updateDataIntegrityFileLabel(fileLabelVo.getFileNames(), DataIntegrityFileLabel.of(fileLabelVo.getFileLabel()), fileLabelVo.getRemark());
    }

    public Boolean delete(DataIntegrityDeleteVo deleteVo) {
        List<DataIntegrityDeleteLog> dataIntegrityDeleteLogs = deleteVo.getFileNames().stream().map(t -> new DataIntegrityDeleteLog()
                .setCustomer(RequestContext.getCustomer())
                .setSubCustomer(RequestContext.getSubCustomer())
                .setFileName(t)
                .setRemark(deleteVo.getRemark())
                .setCreateTime(new Date())
                .setUpdateTime(new Date())
                .setCreateUser(RequestContext.getUserName())
                .setUpdateUser(RequestContext.getUserName())
                .setDeleteFlag(0)).collect(Collectors.toList());
        dataIntegrityDeleteLogRepository.saveAll(dataIntegrityDeleteLogs);
        boolean flag = false;
        try {
            flag = dataRepairRpcService.triggerDataIntegrityDelete(deleteVo.getFileNames());
        } catch (Exception e) {
            log.info("调用triggerDataIntegrityDelete异常：", e);
            dataIntegrityDeleteLogRepository.deleteAllById(dataIntegrityDeleteLogs.stream().map(DataIntegrityDeleteLog::getId).collect(Collectors.toList()));
            throw new BpmsRpcInvokeException(e);
        }
        return flag;
    }


    public List<DataIntegrityQueryDropDownVo> queryDeleteDropDown(DataIntegrityQueryDeleteDropDownFilterVo condition) {
        condition.validCondition();
        List<DataIntegrityDeleteLogDropDown> dropDowns = dataIntegrityDeleteLogRepository.queryDropDown(
                RequestContext.getCustomer(),
                condition.getSubCustomerList(),
                condition.getFileName(),
                condition.getDeleteByList(),
                condition.getFilterField()
        );
        return dropDowns.stream().map(DataIntegrityQueryDropDownVo::of).collect(Collectors.toList());
    }

    public PageableDataVo<DataIntegrityDeleteRecordVo> deleteQuery(DataIntegrityDeleteQueryVo condition) {
        condition.validCondition();
        Page<DataIntegrityDeleteLog> dataIntegrityDeleteLogs = dataIntegrityDeleteLogRepository.queryDataIntegrityDeleteLog(
                RequestContext.getCustomer(),
                condition.getSubCustomerList(),
                condition.getFileName(),
                condition.getDeleteByList(),
                PageRequest.of(condition.getJpaPageIndex(), condition.getPageSize())
        );
        Page<DataIntegrityDeleteRecordVo> deleteRecordVos = dataIntegrityDeleteLogs.map(DataIntegrityDeleteRecordVo::of);
        return PageableDataVo.of(deleteRecordVos);
    }

    public Boolean reRun(DataIntegrityReRunVo reRunVo) {
        return dataRepairRpcService.triggerDataIntegrityReprocess(reRunVo.getFileNames(), reRunVo.getRemark());
    }

    public void repair(DataIntegrityRepairVo repairVo) {
        Map<String, DataIntegrityRepairVo.IntegrityRepairInfoDTO> fileRepairMap = repairVo.getRepairInfos().stream().collect(Collectors.toMap(t -> t.getIntegrityInfo().getFileName(), t -> t, (v1, v2) -> v1));
        ArrayList<String> fileNames = Lists.newArrayList(fileRepairMap.keySet());
        Map<String, FileWarehousingRecord> fileWarehousingRecordMap = fileWarehousingRecordRepository.findAllByFileNameIn(fileNames).stream().collect(Collectors.toMap(FileWarehousingRecord::getFileName, t -> t, (v1, v2) -> v1));
        Map<String, Long> fileIdMap = dcFileInfoRepository.findAllByFileNameInAndUploadTypeAndDeleteFlag(fileNames, UploadType.AUTO, false).stream().collect(Collectors.toMap(FileInfo::getFileName, FileInfo::getId, (v1, v2) -> v1));
        HashMap<String, DataRepairBatchInfo> dataRepairBatchInfoMap = new HashMap<>();
        HashMap<String, DataRepairInfo> dataRepairInfoMap = new HashMap<>();
        HashMap<String, DataRepairHistory> dataRepairHistoryMap = new HashMap<>();
        HashMap<String, DataRepairEffectBatch> dataRepairEffectBatchMap = new HashMap<>();
        HashMap<String, RepairVo> repairVoMap = new HashMap<>();

        fileRepairMap.forEach((fileName, integrityRepairInfoDTO) -> {
            FileWarehousingRecord fileWarehousingRecord = fileWarehousingRecordMap.get(fileName);
            Long fileId = fileIdMap.get(fileName);
            if (fileWarehousingRecord == null || fileId == null) {
                throw new BpmsException(ResponseCode.Business.DATA_INTEGRITY_FILE_NOT_EXISTS, fileName);
            }
            dataRepairBatchInfoMap.put(fileName, buildDataRepairBatchInfo(fileWarehousingRecord));
            dataRepairInfoMap.put(fileName, buildDataRepairInfo(fileId, fileWarehousingRecord, integrityRepairInfoDTO, repairVo.getRemark()));

            repairVoMap.put(fileName, buildRepairVo(fileId, fileWarehousingRecord, integrityRepairInfoDTO.getRepairFields()));
        });

        Map<String, DataRepairInfo> existsDataRepairInfoMap = dataRepairInfoRepository.findAllByFileIdInAndDeleteFlag(dataRepairInfoMap.values().stream().map(DataRepairInfo::getFileId).collect(Collectors.toList()), false)
                .stream().collect(Collectors.toMap(DataRepairInfo::getFileName, t -> t, (v1, v2) -> v1));
        dataRepairBatchInfoRepository.saveAll(dataRepairBatchInfoMap.values());

        dataRepairInfoMap.forEach((k, v) -> {
            v.setRepairBatchId(dataRepairBatchInfoMap.get(k).getId());
            DataRepairInfo existsDataRepairInfo = existsDataRepairInfoMap.get(k);
            if (existsDataRepairInfo != null) {
                v
                        .setId(existsDataRepairInfo.getId())
                        .setRepairTimes(existsDataRepairInfo.getRepairTimes() + 1)
                        .setCreateUser(existsDataRepairInfo.getCreateUser())
                        .setCreateTime(existsDataRepairInfo.getCreateTime());
            }
        });
        dataRepairInfoRepository.saveAll(dataRepairInfoMap.values());

        dataRepairInfoMap.forEach((k, v) -> {
            DataRepairHistory dataRepairHistory = new DataRepairHistory();
            BeanUtils.copyProperties(v, dataRepairHistory);
            dataRepairHistory
                    .setId(null)
                    .setDataRepairId(v.getId())
                    .setCreateTime(new Date())
                    .setCreateUser(RequestContext.getUserName())
            ;
            dataRepairHistoryMap.put(k, dataRepairHistory);
        });
        dataRepairHistoryRepository.saveAll(dataRepairHistoryMap.values());

        dataRepairHistoryMap.forEach((k, v) -> {
            dataRepairEffectBatchMap.put(k, buildDataRepairEffectBatch(v));
        });
        dataRepairEffectBatchRepository.saveAll(dataRepairEffectBatchMap.values());

        try {
            log.info("start triggerDataIntegrityRepair param:{}", JSON.toJSONString(repairVoMap.values()));
            dataRepairRpcService.triggerDataIntegrityRepair(new ArrayList<>(repairVoMap.values()), repairVo.getRemark());
        } catch (Exception e) {
            log.info("调用triggerDataIntegrityRepair异常：", e);
            dataRepairBatchInfoRepository.deleteAllById(dataRepairBatchInfoMap.values().stream().map(DataRepairBatchInfo::getId).collect(Collectors.toList()));
            dataRepairInfoRepository.deleteAllById(dataRepairInfoMap.values().stream().map(DataRepairInfo::getId).collect(Collectors.toList()));
            dataRepairInfoRepository.saveAll(existsDataRepairInfoMap.values());
            dataRepairHistoryRepository.deleteAllById(dataRepairHistoryMap.values().stream().map(DataRepairHistory::getId).collect(Collectors.toList()));
            dataRepairEffectBatchRepository.deleteAllById(dataRepairEffectBatchMap.values().stream().map(DataRepairEffectBatch::getId).collect(Collectors.toList()));
            throw new BpmsRpcInvokeException(e);
        }
    }

    private DataRepairBatchInfo buildDataRepairBatchInfo(FileWarehousingRecord fileWarehousingRecord) {
        return new DataRepairBatchInfo()
                .setCustomer(fileWarehousingRecord.getCustomer())
                .setSubCustomer(fileWarehousingRecord.getSubCustomer())
                .setFactory(fileWarehousingRecord.getFactory())
                .setFactorySite(fileWarehousingRecord.getFactorySite())
                .setDeviceId(fileWarehousingRecord.getDeviceId())
                .setLotId(fileWarehousingRecord.getLotId())
                .setTestStage(fileWarehousingRecord.getTestStage())
                .setTestArea(fileWarehousingRecord.getTestArea() == null ? null : fileWarehousingRecord.getTestArea().getArea())
                .setSblotId(fileWarehousingRecord.getSblotId())
                .setWaferId(fileWarehousingRecord.getWaferId())
                .setDeleteFlag(false)
                .setCreateTime(new Date())
                .setUpdateTime(new Date())
                .setCreateUser(RequestContext.getUserName())
                .setUpdateUser(RequestContext.getUserName());
    }

    private DataRepairInfo buildDataRepairInfo(Long fileId, FileWarehousingRecord fileWarehousingRecord, DataIntegrityRepairVo.IntegrityRepairInfoDTO integrityRepairInfoDTO, String remark) {
        DataRepairInfo dataRepairInfo = new DataRepairInfo()
                .setCustomer(fileWarehousingRecord.getCustomer())
                .setSubCustomer(fileWarehousingRecord.getSubCustomer())
                .setFactory(fileWarehousingRecord.getFactory())
                .setFactorySite(fileWarehousingRecord.getFactorySite())
                .setRepairBatchId(null)
                .setFileId(fileId)
                .setFileName(fileWarehousingRecord.getFileName())
                .setWaferId(Constant.EMPTY)
                .setTestArea(fileWarehousingRecord.getTestArea() == null ? null : fileWarehousingRecord.getTestArea().getArea())
                .setRepairJson(null)
                .setOriginalDeviceId(fileWarehousingRecord.getDeviceId())
                .setOriginalTestStage(fileWarehousingRecord.getTestStage())
                .setOriginalLotId(fileWarehousingRecord.getLotId())
                .setOriginalWaferId(fileWarehousingRecord.getWaferId())
                .setOriginalWaferNo(fileWarehousingRecord.getWaferNo())
                .setOriginalSblotId(fileWarehousingRecord.getSblotId())
                .setOriginalTestProgram(fileWarehousingRecord.getTestProgram())
                .setOriginalLotType(fileWarehousingRecord.getLotType() == null ? null : fileWarehousingRecord.getLotType().getType())
                .setOriginalRetestHbinNumber(null)
                .setOriginalFlowId(null)
                .setModifyDeviceId(fileWarehousingRecord.getDeviceId())
                .setModifyTestStage(fileWarehousingRecord.getTestStage())
                .setModifyLotId(fileWarehousingRecord.getLotId())
                .setModifyWaferNo(fileWarehousingRecord.getWaferNo())
                .setModifySblotId(fileWarehousingRecord.getSblotId())
                .setModifyTestProgram(fileWarehousingRecord.getTestProgram())
                .setModifyLotType(fileWarehousingRecord.getLotType() == null ? null : fileWarehousingRecord.getLotType().getType())
                .setModifyRetestHbinNumber(null)
                .setModifyFlowId(null)
                .setRepairCategory("DATA_COMPLETION")
                .setLastRepairType("关键字段补充")
                .setRepairStatus("INIT")
                .setRepairTimes(1)
                .setStartTime(fileWarehousingRecord.getStartT())
                .setEndTime(fileWarehousingRecord.getFinishT())
                .setRemark(remark)
                .setDeleteFlag(false)
                .setCreateTime(new Date())
                .setUpdateTime(new Date())
                .setCreateUser(RequestContext.getUserName())
                .setUpdateUser(RequestContext.getUserName());
        integrityRepairInfoDTO.getRepairFields().forEach(repairField -> {
            BiConsumer<DataRepairInfo, String> consumer = MODIFY_FIELD_CONSUMER_MAP.get(RepairField.of(repairField.getFieldName()));
            if (consumer != null) {
                consumer.accept(dataRepairInfo, repairField.getModifyValue());
            }
        });
        if (TestArea.getCPList().contains(TestArea.of(dataRepairInfo.getTestArea()))) {
            dataRepairInfo.setWaferId(WaferUtil.formatStandardWaferId(dataRepairInfo.getModifyLotId(), dataRepairInfo.getModifyWaferNo()));
        }
        HashMap<String, Object> repairJson = new HashMap<>();
        repairJson.put("fileInfo", fileWarehousingRecord);
        repairJson.put("repairInfo", integrityRepairInfoDTO);
        repairJson.put("remark", remark);
        dataRepairInfo.setRepairJson(JSON.toJSONString(repairJson));
        return dataRepairInfo;
    }

    private RepairVo buildRepairVo(Long fileId, FileWarehousingRecord fileWarehousingRecord, List<DataIntegrityRepairVo.IntegrityRepairFieldDTO> repairFields) {
        RepairVo repairVo = new RepairVo()
                .setCustomer(fileWarehousingRecord.getCustomer())
                .setSubCustomer(fileWarehousingRecord.getSubCustomer())
                .setFactory(fileWarehousingRecord.getFactory())
                .setFactorySite(fileWarehousingRecord.getFactorySite())
                .setLotType(fileWarehousingRecord.getLotType())
                .setTestArea(fileWarehousingRecord.getTestArea())
                .setDeviceId(fileWarehousingRecord.getDeviceId())
                .setLotId(fileWarehousingRecord.getLotId())
                .setWaferNo(fileWarehousingRecord.getWaferNo())
                .setTestStage(fileWarehousingRecord.getTestStage())
                .setFileCategory(fileWarehousingRecord.getFileCategory())
                .setFileName(fileWarehousingRecord.getFileName())
                .setFileId(fileId)
                .setFlowId(null)
                .setTestProgram(fileWarehousingRecord.getTestProgram())
                .setSblotId(fileWarehousingRecord.getSblotId())
                .setStartT(secondTime(fileWarehousingRecord.getStartT()))
                .setFinishT(secondTime(fileWarehousingRecord.getFinishT()));

        List<DataRepairField> dataRepairFields = repairFields.stream().map(repairField -> new DataRepairField(
                RepairField.of(repairField.getFieldName()),
                repairField.getOriginalValue(),
                repairField.getModifyValue()
        )).collect(Collectors.toList());
        repairVo.setDataRepairFields(dataRepairFields);
        return repairVo;
    }

    private DataIntegrityRepairDimension buildDataIntegrityDimension(DataRepairInfo item) {
        return new DataIntegrityRepairDimension(
                item.getCustomer(),
                item.getFactory(),
                item.getTestArea(),
                item.getModifyDeviceId(),
                item.getModifyLotId(),
                item.getModifyWaferNo(),
                item.getModifyTestStage()
        );
    }


    private DataRepairEffectBatch buildDataRepairEffectBatch(DataRepairHistory repairHistory) {
        return new DataRepairEffectBatch()
                .setCustomer(repairHistory.getCustomer())
                .setSubCustomer(repairHistory.getSubCustomer())
                .setFactory(repairHistory.getFactory())
                .setFactorySite(repairHistory.getFactorySite())
                .setRepairBatchId(repairHistory.getRepairBatchId())
                .setRepairStatus("PROCESS")
                .setDeviceId(repairHistory.getModifyDeviceId())
                .setLotId(repairHistory.getModifyLotId())
                .setTestStage(repairHistory.getModifyTestStage())
                .setTestProgram(repairHistory.getModifyTestProgram())
                .setTestArea(repairHistory.getTestArea())
                .setSblotId(repairHistory.getModifySblotId())
                .setWaferId(repairHistory.getWaferId())
                .setWaferNo(repairHistory.getModifyWaferNo())
                .setRepairNo(1)
                .setFileCountBefore(0L)
                .setFileListBefore(Constant.EMPTY)
                .setFileCountAfter(1L)
                .setFileListAfter(repairHistory.getFileName())
                .setRepairFiles(repairHistory.getFileName())
                .setRepairInfoIds(repairHistory.getId().toString())
                .setDeleteFlag(false)
                .setCreateTime(new Date())
                .setUpdateTime(new Date())
                .setCreateUser(RequestContext.getUserName())
                .setUpdateUser(RequestContext.getUserName())
                ;
    }

    private Long stringToLong(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        return Long.valueOf(str);
    }

    private Long secondTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.getTime() / 1000;
    }

    private Date stringToTime(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        return new Date(Long.parseLong(str) * 1000);
    }
}
