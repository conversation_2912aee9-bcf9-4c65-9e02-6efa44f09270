package com.guwave.datahub.bpms.app.vo.bz.dataTimeliness.model;

import com.guwave.datahub.bpms.app.vo.bz.quality.enums.StatisticIndexEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/10 15:10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Statistics {

    private StatisticIndexEnum calculateIndexType;
    private BigDecimal baseIndexValue;
    private List<StatisticsDetail> detail;

    @Data
    @Builder
    public static class StatisticsDetail {
        private String indexKey;
        private BigDecimal indexValue;
        private String colorByKey;
        private BigDecimal percent;


        /**
         * 计算结果详情
         *
         * @param indexKey   统计类型
         * @param indexValue 统计值
         * @return 计算结果详情
         */
        public static StatisticsDetail of(String indexKey, BigDecimal indexValue, String colorByKey) {
            return StatisticsDetail.builder()
                    .indexKey(indexKey)
                    .indexValue(indexValue)
                    .colorByKey(colorByKey)
                    .build();
        }


        /**
         * 计算结果详情
         *
         * @param indexKey   统计类型
         * @param indexValue 统计值
         * @return 计算结果详情
         */
        public static StatisticsDetail of(String indexKey, BigDecimal indexValue, String colorByKey, BigDecimal percent) {
            return StatisticsDetail.builder()
                    .indexKey(indexKey)
                    .indexValue(indexValue)
                    .colorByKey(colorByKey)
                    .percent(percent)
                    .build();
        }

        /**
         * 计算结果详情
         *
         * @param indexKey   统计类型
         * @param indexValue 统计值
         * @return 计算结果详情
         */
        public static StatisticsDetail withPercent(String indexKey, BigDecimal indexValue, BigDecimal percent) {
            return StatisticsDetail.builder()
                    .indexKey(indexKey)
                    .indexValue(indexValue)
                    .percent(percent)
                    .build();
        }
    }

    /**
     * 数据指标统计数据结果
     *
     * @param calculateIndexType {@link StatisticIndexEnum}
     * @param totalCnt           {@link BigDecimal}
     * @param detail             {@link List}
     * @return StatisticsDTO
     */
    public static Statistics of(StatisticIndexEnum calculateIndexType, BigDecimal totalCnt, List<StatisticsDetail> detail) {
        return Statistics.builder()
                .calculateIndexType(calculateIndexType)
                .baseIndexValue(totalCnt)
                .detail(detail)
                .build();
    }


    /**
     * 默认空数据
     *
     * @param indexEnum {@link StatisticIndexEnum}
     * @return StatisticsDTO
     */
    public static Statistics empty(StatisticIndexEnum indexEnum) {
        return Statistics.builder().calculateIndexType(StatisticIndexEnum.IN_DB_RATE).baseIndexValue(BigDecimal.ZERO).detail(Collections.emptyList()).build();
    }
}
