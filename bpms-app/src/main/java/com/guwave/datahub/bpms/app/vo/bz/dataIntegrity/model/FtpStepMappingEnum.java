package com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.model;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum FtpStepMappingEnum {
    FTP_FILE_COUNT("FTP_FILE", "Ftp文件", ""),
    CALCULATE_PLATFORM("CALCULATE_PLATFORM", "Step1:传输至大数据存储计算平台", ""),
    TRIGGER_PARSE("TRIGGER_PARSE", "Step2:触发解析", "正在解析"),
    KEY_FIELD_CHECK("KEY_FIELD_CHECK", "Step3:关键字段验证", ""),
    PARSE_AND_IN_DB("PARSE_AND_IN_DB", "Step4:解析入库", ""),
    PARSE_SUCCESS("PARSE_SUCCESS", "Not Applicable", "Not Applicable");

    private final String code;
    private final String desc;
    private final String failedType;

}
