package com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.request;

import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model.MetaData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Schema(title = "清洗规则预览调试")
@Data
public class ClearRulePreviewTestVo {
    @Schema(title = "metaData", example = "{\"fileName\":\"gubo_demo_2024-04-18_13_32_54.stdf\",\"lotId\":\"GUBO_LOT_ID_00001\",}")
    @NotNull(message = "metaData")
    private MetaData metaData;

    @Schema(title = "ruleExpression", example = "result = metaData.lotId")
    @NotBlank(message = "ruleExpression")
    private String ruleExpression;

    @Schema(title = "field", example = "lotId")
    @NotBlank(message = "field")
    private String field;

    @Schema(title = "id", example = "111")
    @NotNull(message = "id")
    private Long id;
}
