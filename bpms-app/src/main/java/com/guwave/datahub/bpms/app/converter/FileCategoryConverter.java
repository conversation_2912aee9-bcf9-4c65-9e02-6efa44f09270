package com.guwave.datahub.bpms.app.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.guwave.onedata.dataware.common.contant.FileCategory;

public class FileCategoryConverter implements Converter<FileCategory> {
    @Override
    public Class<?> supportJavaTypeKey() {
        return FileCategory.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(FileCategory value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new WriteCellData<>(value.getCategory());
    }

    // 从Excel读取时：String -> FileCategory
    @Override
    public FileCategory convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String fileCategory = cellData.getStringValue();
        return FileCategory.valueOf(fileCategory);
    }
}
