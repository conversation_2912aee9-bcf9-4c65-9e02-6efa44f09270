package com.guwave.datahub.bpms.app.configuration;

import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.app.web.response.ResponseCodeInterface;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import org.springdoc.core.customizers.OpenApiCustomiser;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.context.annotation.Bean;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.stream.Collectors;

@SpringBootConfiguration
@SecurityScheme(
        name = "JWT",                   // 认证方案名称
        type = SecuritySchemeType.HTTP,      // 认证类型，当前为http认证
        description = "请填写token",  // 描述信息
        in = SecuritySchemeIn.HEADER,        // 代表在http请求头部
        scheme = "bearer",                   // 认证方案，如：Authorization: bearer token信息
        bearerFormat = "JWT")                // 表示使用 JWT 格式作为 Bearer Token 的格式
public class SwaggerOpenApiConfiguration {

    /***
     * 构建Swagger3.0文档说明
     * @return 返回 OpenAPI
     */
    @Bean
    public OpenAPI customOpenAPI() {

        // 联系人信息(contact)，构建API的联系人信息，用于描述API开发者的联系信息，包括名称、URL、邮箱等
        // name：文档的发布者名称 url：文档发布者的网站地址，一般为企业网站 email：文档发布者的电子邮箱
        Contact contact = new Contact()
                .name("bpms") // 作者名称
                .email("") // 作者邮箱
                .url("") // 介绍作者的URL地址
                .extensions(new HashMap<>()); // 使用Map配置信息（如key为"name","email","url"）

        //创建Api帮助文档的描述信息、联系人信息(contact)、授权许可信息(license)
        Info info = new Info()
                .title("BPMS 接口文档") // Api接口文档标题（必填）
                .description("bpms(大数据平台管理系统)") // Api接口文档描述
                .version("0.1.0") // Api接口版本
                .termsOfService("") // Api接口的服务条款地址
                .contact(contact); // 设置联系人信息
        // 返回信息
        return new OpenAPI().addSecurityItem(new SecurityRequirement().addList("JWT"))
                .openapi("3.0.1") // Open API 3.0.1(默认)
                .info(info); // 配置Swagger3.0描述信息

    }

    @Bean
    public OpenApiCustomiser openApiCustomiser() {
        return openApi -> {
            String des =
                    "<div style=\"max-width: 800px;margin: 20px auto;padding: 20px;background-color: #fff;border-radius: 10px;box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);\">" +
                    "<h3>统一的返回值对象</h3>" +
                    "<table style=\"width: 100%;border-collapse: collapse;margin-top: 20px;\">" +
                    "    <thead>" +
                    "    <tr>" +
                    "        <th style=\"background-color: #4CAF50;color: white;padding: 10px;text-align: left;border-bottom: 1px solid #ddd;\">code</th>" +
                    "        <th style=\"background-color: #4CAF50;color: white;padding: 10px;text-align: left;border-bottom: 1px solid #ddd;\">msg</th>" +
                    "    </tr>" +
                    "    </thead>" +
                    "    <tbody>" +
                    new ArrayList<ResponseCodeInterface[]>() {{
                        add(ResponseCode.values());
                        add(ResponseCode.Internal.values());
                        add(ResponseCode.Common.values());
                        add(ResponseCode.Business.values());
                    }}.stream().flatMap(Arrays::stream).sorted(Comparator.comparingLong(l -> Long.parseLong(l.code())))
                            .map(t -> "<tr><td style=\"padding: 10px;text-align: left;border-bottom: 1px solid #ddd;\">" + t.code() + "</td><td style=\"padding: 10px;text-align: left;border-bottom: 1px solid #ddd;\">" + t.msg() + "</td></tr>").collect(Collectors.joining())
                    + "</tbody>" +
                    "</table>" +
                    "</div>";
            openApi.getComponents().getSchemas().forEach((key, schema) -> {
                if ("ResponseVo".equals(schema.getDescription())) {
                    schema.setDescription(des);
                }
            });
        };
    }
}
