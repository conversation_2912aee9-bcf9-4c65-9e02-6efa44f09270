package com.guwave.datahub.bpms.app.controller;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.DataPermission;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.service.bz.DataQualityOverviewService;
import com.guwave.datahub.bpms.app.vo.bz.dataQualityOverview.request.DataQualityOverviewDropDownFilterVo;
import com.guwave.datahub.bpms.app.vo.bz.dataQualityOverview.request.DataQualityOverviewQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.dataQualityOverview.response.DataQualityOverviewDropDownVo;
import com.guwave.datahub.bpms.app.vo.bz.dataQualityOverview.response.DataQualityOverviewStatisticsVo;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Log
@Auth
//@License
@Validated
@Slf4j
@RestController
@AllArgsConstructor
@Tag(name = "DataQualityOverviewControllerAPI",
     description = "数据质量概览接口",
     externalDocs = @ExternalDocumentation(description = "需求文档",
     url = "https://loop.cloud.microsoft/p/eyJ3Ijp7InUiOiJodHRwczovL2d1d2F2ZS5zaGFyZXBvaW50LmNvbS8%2FbmF2PWN6MGxNa1ltWkQxaUlUQnRhalpRWDBSUGF6Qkhia1J3TjE5U04yMTRaREJOVlROaVozZEljbWhIY0VoWVZFaFVabFUwWkRWQmRWQmpWVmg1YkVGUlNteFZSRUkyZUVwaGJEVW1aajB3TVVoRFRFbERURUpYVWtSVlZ6WkhTbEpIUmtaTVMwVkpURWhQVVVkSVRFdFBKbU05Sm1ac2RXbGtQVEUlM0QiLCJyIjpmYWxzZX0sInAiOnsidSI6Imh0dHBzOi8vZ3V3YXZlLnNoYXJlcG9pbnQuY29tLzpmbDovci9jb250ZW50c3RvcmFnZS9DU1BfM2ZmYTY4ZDItY2VmMC00MTkzLWE3MGUtOWVmZjQ3YjliMTc3L0RvY3VtZW50JTIwTGlicmFyeS9Mb29wQXBwRGF0YS8lRTYlOTclQTAlRTYlQTAlODclRTklQTIlOTglMjAzLmxvb3A%2FZD13MjZlMjY4NDg2M2UxNDU5MmIxZmU3MTY4YTFhNTQ1OGYmY3NmPTEmd2ViPTEmbmF2PWN6MGxNa1pqYjI1MFpXNTBjM1J2Y21GblpTVXlSa05UVUY4elptWmhOamhrTWkxalpXWXdMVFF4T1RNdFlUY3daUzA1WldabU5EZGlPV0l4TnpjbVpEMWlJVEJ0YWpaUVgwUlBhekJIYmtSd04xOVNOMjE0WkRCTlZUTmlaM2RJY21oSGNFaFlWRWhVWmxVMFpEVkJkVkJqVlZoNWJFRlJTbXhWUkVJMmVFcGhiRFVtWmowd01VaERURWxEVEVOSlRrUlNRMDVaVEVSVFNrTXpSRGRVVWs1RFVUSkxVazFRSm1NOUpUSkdKbVpzZFdsa1BURW1ZVDFNYjI5d1FYQndKbkE5SlRRd1pteDFhV1I0SlRKR2JHOXZjQzF3WVdkbExXTnZiblJoYVc1bGNpWjRQU1UzUWlVeU1uY2xNaklsTTBFbE1qSlVNRkpVVlVoNGJtUllaR2hrYlZWMVl6Sm9hR050Vm5kaU1teDFaRU0xYW1JeU1UaFphVVYzWWxkdk1sVkdPVVZVTW5OM1VqSTFSV05FWkdaVmFtUjBaVWRSZDFSV1ZYcFpiV1F6VTBoS2IxSXpRa2xYUmxKSlZrZGFWazVIVVRGUldGWlJXVEZXV1dWWGVFSlZWWEJ6VmxWU1EwNXVhRXRaVjNjeFprUkJlRk5GVGsxVFZVNU5VV3hrVTFKR1ZsaE9hMlJMVld0a1IxSnJlRXhTVld4TlUwVTVVbEl3YUUxVE1EZ2xNMFFsTWpJbE1rTWxNakpwSlRJeUpUTkJKVEl5WlRBMll6RTFaVGN0TTJWallTMDBNekF3TFRoalpqQXRORFkyWVRGaFpUQXpPR013SlRJeUpUZEUiLCJyIjpmYWxzZX0sImkiOnsiaSI6ImUwNmMxNWU3LTNlY2EtNDMwMC04Y2YwLTQ2NmExYWUwMzhjMCJ9fQ%3D%3D")
)
@RequestMapping("dataQualityOverview")
public class DataQualityOverviewController {

    @Autowired
    private DataQualityOverviewService dataQualityOverviewService;

    @DataPermission
    @Operation(summary = "数据指标概览下拉框")
    @PostMapping("/filter")
    public ResponseVo<List<DataQualityOverviewDropDownVo>> dataQualityOverviewFilter(@RequestBody @Validated DataQualityOverviewDropDownFilterVo dataQualityOverviewDropDownFilterVo) {
        return ResponseVo.ok(dataQualityOverviewService.dataQualityOverviewFilter(dataQualityOverviewDropDownFilterVo));
    }

    @DataPermission
    @Operation(summary = "数据指标概览接口")
    @PostMapping("/overview")
    public ResponseVo<List<DataQualityOverviewStatisticsVo>> dataQualityOverview(@RequestBody @Valid DataQualityOverviewQueryVo dataQualityOverviewQueryVO) {
        return ResponseVo.ok(dataQualityOverviewService.statisticOverview(dataQualityOverviewQueryVO));
    }

    @Operation(summary = "获取常量Map")
    @PostMapping("getConstantMap")
    public ResponseVo<Map<String, Object>> getConstantMap() {
        return ResponseVo.ok(dataQualityOverviewService.getConstantMap());
    }

}