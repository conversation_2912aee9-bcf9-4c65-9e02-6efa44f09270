package com.guwave.datahub.bpms.app.configuration;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import com.guwave.datahub.bpms.app.web.interceptor.AuthInterceptor;
import com.guwave.datahub.bpms.app.web.interceptor.LicenseInterceptor;
import lombok.AllArgsConstructor;
import org.hibernate.validator.HibernateValidator;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.io.IOException;
import java.math.BigDecimal;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * BpmsAppConfiguration
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2024-02-26 11:22:12
 */
@Configuration
@AllArgsConstructor
public class BpmsAppConfiguration implements WebMvcConfigurer {

    private final AuthInterceptor authInterceptor;

    private final LicenseInterceptor licenseInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        InterceptorRegistration authRegistration = registry.addInterceptor(authInterceptor);
        authRegistration.addPathPatterns("/**");
        authRegistration.excludePathPatterns(
                "/status",
                "/**/*.html",
                "/**/*.htm",
                "/**/*.js",
                "/**/*.css",
                "/**/*.png",
                "/**/*.jpg",
                "/swagger-ui.html",
                "/swagger-ui/**",
                "/swagger-resources/**",
                "/v2/api-docs",
                "/v3/api-docs",
                "/webjars/**",
                "/actuator/**"
        );

        InterceptorRegistration licenseRegistration = registry.addInterceptor(licenseInterceptor);
        licenseRegistration.addPathPatterns("/**");
        licenseRegistration.excludePathPatterns(
                "/status",
                "/**/*.html",
                "/**/*.htm",
                "/**/*.js",
                "/**/*.css",
                "/**/*.png",
                "/**/*.jpg",
                "/swagger-ui.html",
                "/swagger-ui/**",
                "/swagger-resources/**",
                "/v2/api-docs",
                "/v3/api-docs",
                "/webjars/**",
                "/actuator/**"
        );
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {

        registry
                // 项目中的所有接口都支持跨域
                .addMapping("/**")
                // 所有地址都可以访问，也可以配置具体地址
                .allowedOriginPatterns("*")
                .allowCredentials(true)
                // 所有方法都支持
                .allowedMethods("*")
                .maxAge(1200);// 跨域允许时间
    }

    @Bean
    public Validator validator() {
        ValidatorFactory validatorFactory = Validation.byProvider(HibernateValidator.class)
                .configure()
                .addProperty("hibernate.validator.fail_fast", "true")
                .buildValidatorFactory();
        return validatorFactory.getValidator();
    }

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {
        return jacksonObjectMapperBuilder -> jacksonObjectMapperBuilder
                .serializerByType(BigDecimal.class, new StdSerializer<BigDecimal>(BigDecimal.class) {

                    private static final long serialVersionUID = -487360869655123104L;

                    @Override
                    public void serialize(BigDecimal value, JsonGenerator gen, SerializerProvider provider) throws IOException {
                        if (null != value) {
                            gen.writeNumber(value.stripTrailingZeros().toPlainString());
                        }
                    }
                });
    }
}
