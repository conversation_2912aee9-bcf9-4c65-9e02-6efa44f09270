package com.guwave.datahub.bpms.app.vo.bz.metadata.response;

import com.guwave.datahub.bpms.app.vo.bz.metadata.request.TableVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.LinkedHashMap;
import java.util.List;

@Data
@NoArgsConstructor
public class MetadataTableLineageVo {

    @Schema(title = "所有来源表", description = "多张不同的表")
    private List<TableVo> fromTables;

    @Schema(title = "所有结果表", description = "多张不同的表")
    private List<TableVo> toTables;

    @Schema(title = "字段", description = "当前表的所有字段及其id", example = "{\"FACTORY_SITE\":22, \"ECID_CP_STAGE_ID\": 24}")
    private LinkedHashMap<String, Long> fields;
}
