package com.guwave.datahub.bpms.app.service.license;

import com.guwave.datahub.bpms.app.vo.license.LicenseRequestVo;
import com.guwave.datahub.bpms.app.vo.license.LicenseResponseVo;
import com.guwave.datahub.bpms.app.annotation.FeignClient;
import feign.Headers;
import feign.RequestLine;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * LicenseApi
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-27 14:49:36
 */
@FeignClient(name = "licenseApi", url = "${license.baseUrl}")
public interface LicenseApi {

    @RequestLine("POST /license/checkTotalModule")
    @Headers("Content-Type: application/json")
    LicenseResponseVo<String> check(LicenseRequestVo licenseRequestVo);
}
