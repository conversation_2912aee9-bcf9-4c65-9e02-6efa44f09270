package com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.guwave.datahub.bpms.dao.vo.dw.WaferVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


@Schema(title = "批次匹配详情请求体")
@Data
@EqualsAndHashCode(callSuper = false)
public class DataAccuracyBatchMatchQueryVo {

    @Schema(description = "子客户", example = "[\"AMD\"]")
    private List<String> subCustomerList;

    @Schema(description = "工厂", example = "[\"AMD\"]")
    private List<String> factoryList;

    @Schema(description = "测试区域", example = "CP")
    @NotNull(message = "TestArea不能为空")
    private String testArea;

    @Schema(description = "deviceId", example = "[\"deviceId-1\"]")
    private List<String> deviceIdList;

    @Schema(description = "testStage", example = "[\"CP2\"]")
    private List<String> testStageList;

    @Schema(description = "lotId", example = "[\"lotId-1\"]")
    private List<String> lotIdList;

    @Schema(description = "waferNo", example = "[\"14\"]")
    private List<String> waferNoList;

    @Schema(description = "sblotId", example = "[\"sblotId-1\"]")
    private List<String> sblotIdList;

    @Schema(description = "lotType", example = "[\"PRODUCTION\"]")
    private List<String> lotTypeList;

    @Schema(description = "mesMatchFlag", example = "[\"deviceId-1\"]")
    private List<String> mesMatchFlagList;

    @Schema(description = "dataAccuracyCheck", example = "[\"deviceId-1\"]")
    private List<String> dataAccuracyCheckList;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "startTime", example = "开始时间, 格式 yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "endTime", example = "结束时间，格式 yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

   public static void fillWafer(DataAccuracyBatchMatchQueryVo dataAccuracyBatchMatchQueryVo, boolean isCp, List<WaferVo> waferVoList) {
       dataAccuracyBatchMatchQueryVo.setSubCustomerList(waferVoList.stream().map(WaferVo::getSubCustomer).distinct().collect(Collectors.toList()));
       dataAccuracyBatchMatchQueryVo.setFactoryList(waferVoList.stream().map(WaferVo::getFactory).distinct().collect(Collectors.toList()));
       dataAccuracyBatchMatchQueryVo.setDeviceIdList(waferVoList.stream().map(WaferVo::getDeviceId).distinct().collect(Collectors.toList()));
       dataAccuracyBatchMatchQueryVo.setTestStageList(waferVoList.stream().map(WaferVo::getTestStage).distinct().collect(Collectors.toList()));
       dataAccuracyBatchMatchQueryVo.setLotIdList(waferVoList.stream().map(WaferVo::getLotId).distinct().collect(Collectors.toList()));
       dataAccuracyBatchMatchQueryVo.setLotTypeList(waferVoList.stream().map(WaferVo::getLotType).distinct().collect(Collectors.toList()));
       if (isCp) {
           dataAccuracyBatchMatchQueryVo.setWaferNoList(waferVoList.stream().map(WaferVo::getWaferNo).distinct().collect(Collectors.toList()));
           dataAccuracyBatchMatchQueryVo.setSblotIdList(Collections.emptyList());
       } else {
           dataAccuracyBatchMatchQueryVo.setWaferNoList(Collections.emptyList());
           dataAccuracyBatchMatchQueryVo.setSblotIdList(waferVoList.stream().map(WaferVo::getSblotId).distinct().collect(Collectors.toList()));
       }
   }

}
