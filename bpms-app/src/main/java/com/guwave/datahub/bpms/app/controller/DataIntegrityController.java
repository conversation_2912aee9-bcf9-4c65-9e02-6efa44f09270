package com.guwave.datahub.bpms.app.controller;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.DataPermission;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.service.bz.DataIntegrityService;
import com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.request.*;
import com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.response.DataIntegrityBaseVo;
import com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.response.DataIntegrityDeleteRecordVo;
import com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.response.DataIntegrityQueryDropDownVo;
import com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.response.DataIntegrityRecordVo;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Log
@Auth
//@License
@Validated
@Slf4j
@RestController
@AllArgsConstructor
@Tag(name = "DataQualityControllerAPI", description = "数据完整性接口",
        externalDocs = @ExternalDocumentation(description = "需求文档",
                url = "https://loop.cloud.microsoft/p/eyJ3Ijp7InUiOiJodHRwczovL2d1d2F2ZS5zaGFyZXBvaW50LmNvbS8%2FbmF2PWN6MGxNa1ltWkQxaUlUQnRhalpRWDBSUGF6Qkhia1J3TjE5U04yMTRaREJOVlROaVozZEljbWhIY0VoWVZFaFVabFUwWkRWQmRWQmpWVmg1YkVGUlNteFZSRUkyZUVwaGJEVW1aajB3TVVoRFRFbERURUpYVWtSVlZ6WkhTbEpIUmtaTVMwVkpURWhQVVVkSVRFdFBKbU05Sm1ac2RXbGtQVEUlM0QiLCJyIjpmYWxzZX0sInAiOnsidSI6Imh0dHBzOi8vZ3V3YXZlLnNoYXJlcG9pbnQuY29tLzpmbDovci9jb250ZW50c3RvcmFnZS9DU1BfM2ZmYTY4ZDItY2VmMC00MTkzLWE3MGUtOWVmZjQ3YjliMTc3L0RvY3VtZW50JTIwTGlicmFyeS9Mb29wQXBwRGF0YS8lRTYlOTclQTAlRTYlQTAlODclRTklQTIlOTglMjAzLmxvb3A%2FZD13MjZlMjY4NDg2M2UxNDU5MmIxZmU3MTY4YTFhNTQ1OGYmY3NmPTEmd2ViPTEmbmF2PWN6MGxNa1pqYjI1MFpXNTBjM1J2Y21GblpTVXlSa05UVUY4elptWmhOamhrTWkxalpXWXdMVFF4T1RNdFlUY3daUzA1WldabU5EZGlPV0l4TnpjbVpEMWlJVEJ0YWpaUVgwUlBhekJIYmtSd04xOVNOMjE0WkRCTlZUTmlaM2RJY21oSGNFaFlWRWhVWmxVMFpEVkJkVkJqVlZoNWJFRlJTbXhWUkVJMmVFcGhiRFVtWmowd01VaERURWxEVEVOSlRrUlNRMDVaVEVSVFNrTXpSRGRVVWs1RFVUSkxVazFRSm1NOUpUSkdKbVpzZFdsa1BURW1ZVDFNYjI5d1FYQndKbkE5SlRRd1pteDFhV1I0SlRKR2JHOXZjQzF3WVdkbExXTnZiblJoYVc1bGNpWjRQU1UzUWlVeU1uY2xNaklsTTBFbE1qSlVNRkpVVlVoNGJtUllaR2hrYlZWMVl6Sm9hR050Vm5kaU1teDFaRU0xYW1JeU1UaFphVVYzWWxkdk1sVkdPVVZVTW5OM1VqSTFSV05FWkdaVmFtUjBaVWRSZDFSV1ZYcFpiV1F6VTBoS2IxSXpRa2xYUmxKSlZrZGFWazVIVVRGUldGWlJXVEZXV1dWWGVFSlZWWEJ6VmxWU1EwNXVhRXRaVjNjeFprUkJlRk5GVGsxVFZVNU5VV3hrVTFKR1ZsaE9hMlJMVld0a1IxSnJlRXhTVld4TlUwVTVVbEl3YUUxVE1EZ2xNMFFsTWpJbE1rTWxNakpwSlRJeUpUTkJKVEl5WlRBMll6RTFaVGN0TTJWallTMDBNekF3TFRoalpqQXRORFkyWVRGaFpUQXpPR013SlRJeUpUZEUiLCJyIjpmYWxzZX0sImkiOnsiaSI6ImUwNmMxNWU3LTNlY2EtNDMwMC04Y2YwLTQ2NmExYWUwMzhjMCJ9fQ%3D%3D")
)
@RequestMapping("dataIntegrity")
public class DataIntegrityController {

    private DataIntegrityService dataIntegrityService;


    @Operation(summary = "下拉框查询")
    @PostMapping("filter")
    @DataPermission
    public ResponseVo<List<DataIntegrityQueryDropDownVo>> queryDropDown(@RequestBody DataIntegrityQueryDropDownFilterVo condition) {
        return ResponseVo.ok(dataIntegrityService.queryDropDown(condition));
    }

    @Operation(summary = "指标查询")
    @PostMapping("integrityBase")
    @DataPermission
    public ResponseVo<DataIntegrityBaseVo> queryBase(@RequestBody DataIntegrityQueryVo condition) {
        return ResponseVo.ok(dataIntegrityService.queryIntegrityBase(condition));
    }

    @Operation(summary = "表格数据查询")
    @PostMapping("dataIntegrity")
    @DataPermission
    public ResponseVo<PageableDataVo<DataIntegrityRecordVo>> queryRecords(@RequestBody DataIntegrityQueryVo condition) {
        return ResponseVo.ok(dataIntegrityService.queryRecords(condition));
    }

    @Operation(summary = "文件下载")
    @PostMapping("download")
    public void downloadFile(@RequestBody DataIntegrityDownloadFileVo downloadFileVo, HttpServletResponse response) throws IOException {
        dataIntegrityService.downloadFile(downloadFileVo, response);
    }

    @Operation(summary = "表格下载")
    @PostMapping("export")
    @DataPermission
    public void exportTable(@RequestBody DataIntegrityQueryVo condition, HttpServletResponse response) throws IOException {
        dataIntegrityService.exportTable(condition, response);
    }

    @Operation(summary = "备注")
    @PostMapping("remark")
    public ResponseVo<Boolean> remark(@RequestBody DataIntegrityRemarkVo remarkVo) {
        return ResponseVo.ok(dataIntegrityService.remark(remarkVo));
    }

    @Operation(summary = "修改")
    @PostMapping("fileLabel")
    public ResponseVo<Boolean> fileLabel(@RequestBody DataIntegrityFileLabelVo fileLabelVo) {
        return ResponseVo.ok(dataIntegrityService.fileLabel(fileLabelVo));
    }

    @Operation(summary = "删除")
    @PostMapping("delete")
    public ResponseVo<Boolean> delete(@RequestBody DataIntegrityDeleteVo deleteVo) {
        return ResponseVo.ok(dataIntegrityService.delete(deleteVo));
    }

    @Operation(summary = "下拉框查询")
    @PostMapping("delete/dropDown")
    public ResponseVo<List<DataIntegrityQueryDropDownVo>> queryDeleteDropDown(@RequestBody DataIntegrityQueryDeleteDropDownFilterVo condition) {
        return ResponseVo.ok(dataIntegrityService.queryDeleteDropDown(condition));
    }

    @Operation(summary = "删除记录查询")
    @PostMapping("delete/list")
    public ResponseVo<PageableDataVo<DataIntegrityDeleteRecordVo>> deleteQuery(@RequestBody DataIntegrityDeleteQueryVo condition) {
        return ResponseVo.ok(dataIntegrityService.deleteQuery(condition));
    }

    @Operation(summary = "重跑")
    @PostMapping("reRun")
    public ResponseVo<Boolean> reRun(@RequestBody DataIntegrityReRunVo reRunVo) {
        return ResponseVo.ok(dataIntegrityService.reRun(reRunVo));
    }

    @Operation(summary = "修复")
    @PostMapping("repair")
    public ResponseVo<Void> repair(@RequestBody DataIntegrityRepairVo repairVo) {
        dataIntegrityService.repair(repairVo);
        return ResponseVo.ok();
    }
}
