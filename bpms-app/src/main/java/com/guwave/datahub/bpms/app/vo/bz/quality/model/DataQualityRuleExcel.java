package com.guwave.datahub.bpms.app.vo.bz.quality.model;


import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class DataQualityRuleExcel {

    @ExcelProperty("sqlType")
    private Integer sqlType;

    @ExcelProperty("executeTimeType")
    private Integer executeTimeType;

    @ExcelProperty("qualityName")
    private String qualityName;

    @ExcelProperty("comment")
    private String comment;

    @ExcelProperty("mysqlQuerySql")
    private String mysqlQuerySql;

    @ExcelProperty("ckTempTableSql")
    private String ckTempTableSql;

    @ExcelProperty("ckQuerySql")
    private String ckQuerySql;

    @ExcelProperty("status")
    private Integer status;
}
