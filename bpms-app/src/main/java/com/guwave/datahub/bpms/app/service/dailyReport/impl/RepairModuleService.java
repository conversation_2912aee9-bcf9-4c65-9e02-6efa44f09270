package com.guwave.datahub.bpms.app.service.dailyReport.impl;

import com.alibaba.fastjson2.JSON;
import com.guwave.datahub.bpms.app.service.dailyReport.CommonService;
import com.guwave.datahub.bpms.app.util.DateUtil;
import com.guwave.datahub.bpms.app.util.PdfUtil;
import com.guwave.datahub.bpms.common.constant.ChartData;
import com.guwave.datahub.bpms.common.constant.ChartType;
import com.guwave.datahub.bpms.common.constant.DataCode;
import com.guwave.datahub.bpms.common.constant.ReportModule;
import com.guwave.datahub.bpms.dao.repository.bpms.DailyReportResultRepository;
import com.guwave.datahub.bpms.dao.repository.dw.RepairLotWaferRepository;
import com.guwave.datahub.bpms.dao.repository.dw.RepairRecordRepository;
import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.RepairDetail;
import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.RepairTaskResult;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.RepairLotWafer;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.RepairRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.EMPTY;
import static com.guwave.onedata.dataware.common.contant.Constant.PERCENT;

@Service
public class RepairModuleService implements CommonService {
    @Autowired
    private DailyReportResultRepository dailyReportResultRepository;

    @Autowired
    private RepairLotWaferRepository repairLotWaferRepository;

    @Autowired
    private RepairRecordRepository repairRecordRepository;

    private boolean initFlag = false;

    private final RepairTaskResult result = new RepairTaskResult();

    private final List<ChartData> repairChartData = new ArrayList<>();

    private void init() {
        // 当天数据
        List<RepairLotWafer> repairLotWafers = repairLotWaferRepository.findAllByCreateTimeBetween(DateUtil.getDateFormattedStartTimeBeforeDays(1), DateUtil.getDateFormattedStartTimeBeforeDays(0));
        result.setTotalLotWaferCnt((long) repairLotWafers.size());
        result.setSuccessLotWaferCnt(repairLotWafers.stream().filter(t -> t.getProcessStatus() == ProcessStatus.SUCCESS).count());

        List<RepairRecord> repairRecords = repairRecordRepository.findAllByCreateTimeBetween(DateUtil.getDateFormattedStartTimeBeforeDays(1), DateUtil.getDateFormattedStartTimeBeforeDays(0));
        result.setTotalFileCnt((long) repairRecords.size());
        result.setSuccessFileCnt(repairRecords.stream().filter(t -> t.getProcessStatus() == ProcessStatus.SUCCESS).count());

        // 前7天数据
        List<RepairDetail> lotWaferDetailByDay = repairLotWaferRepository.findLotWaferDetailByDay(DateUtil.getStringFormattedStartTimeBeforeDays(7), DateUtil.getStringFormattedStartTimeBeforeDays(1))
                .stream().map(this::buildRepairDetail).collect(Collectors.toList());
        repairChartData.addAll(lotWaferDetailByDay.stream().map(t -> new ChartData(t.getDay(), t.getSuccessCnt(), "修复成功lot/wafer数")).collect(Collectors.toList()));
        repairChartData.addAll(lotWaferDetailByDay.stream().map(t -> new ChartData(t.getDay(), t.getTotalCnt(), "修复lot/wafer数")).collect(Collectors.toList()));
        List<RepairDetail> fileDetailByDay = repairRecordRepository.findFileDetailByDay(DateUtil.getStringFormattedStartTimeBeforeDays(7), DateUtil.getStringFormattedStartTimeBeforeDays(1))
                .stream().map(this::buildRepairDetail).collect(Collectors.toList());
        repairChartData.addAll(fileDetailByDay.stream().map(t -> new ChartData(t.getDay(), t.getSuccessCnt(), "修复成功文件数")).collect(Collectors.toList()));
        repairChartData.addAll(fileDetailByDay.stream().map(t -> new ChartData(t.getDay(), t.getTotalCnt(), "修复文件数")).collect(Collectors.toList()));

        repairChartData.add(new ChartData(DateUtil.getDayBefortDays(1), result.getSuccessLotWaferCnt(), "修复成功lot/wafer数"));
        repairChartData.add(new ChartData(DateUtil.getDayBefortDays(1), result.getTotalLotWaferCnt(), "修复lot/wafer数"));
        repairChartData.add(new ChartData(DateUtil.getDayBefortDays(1), result.getSuccessFileCnt(), "修复成功文件数"));
        repairChartData.add(new ChartData(DateUtil.getDayBefortDays(1), result.getTotalFileCnt(), "修复文件数"));

    }

    public void generateRepairDetail(PdfUtil pdfUtil) throws IOException {
        if (!initFlag) {
            init();
            initFlag = true;
        }

        saveDailyReportResult(JSON.toJSONString(result), ReportModule.REPLAY, DataCode.REPAIR_TASK);

        pdfUtil.addBodyText("修复成功的lot/wafer数：" + result.getSuccessLotWaferCnt() + " 成功率：" + (result.getTotalLotWaferCnt() != 0 ? result.getSuccessLotWaferCnt() * 1.0 / result.getTotalLotWaferCnt() * 100 + PERCENT : "NA"));
        pdfUtil.addBodyText("修复成功的文件数：" + result.getSuccessFileCnt() + " 成功率：" + (result.getTotalFileCnt() != 0 ? result.getSuccessFileCnt() * 1.0 / result.getTotalFileCnt() * 100 + PERCENT : "NA"));
        pdfUtil.addBodyText(EMPTY);
    }

    public void generateRepairTaskChart(PdfUtil pdfUtil) throws IOException {
        pdfUtil.addChart(repairChartData, ChartType.LINE, "近7天数据修复统计", "时间", "大小", true);
    }

    private RepairDetail buildRepairDetail(Object[] objects) {
        return new RepairDetail(
                (String) objects[0],
                ((Number) objects[1]).longValue(),
                ((Number) objects[2]).longValue()

        );
    }

    @Override
    public DailyReportResultRepository getDailyReportResultRepository() {
        return dailyReportResultRepository;
    }

    public void resetInitFlag() {
        initFlag = false;
    }
}
