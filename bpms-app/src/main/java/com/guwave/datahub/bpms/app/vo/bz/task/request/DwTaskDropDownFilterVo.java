package com.guwave.datahub.bpms.app.vo.bz.task.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/3/12
 * @description DwTaskFieldFilterVo
 */

@Data
@NoArgsConstructor
@ToString
@Schema(title = "task DW级联查询")
public class DwTaskDropDownFilterVo {

    @NotBlank(message = "filterField")
    @Schema(title = "filterField", description = "过滤字段，即当前下拉框需要筛选的字段")
    private String filterField;

    @Schema(title = "testArea列表", example = "[]")
    private List<String> testAreaList;

    @Schema(title = "factory列表", example = "[]")
    private List<String> factoryList;

    @Schema(title = "deviceId列表", example = "[]")
    private List<String> deviceIdList;

    @Schema(title = "testStage列表", example = "[]")
    private List<String> testStageList;

    @Schema(title = "lotId列表", example = "[]")
    private List<String> lotIdList;

    @Schema(title = "waferNo列表", example = "[]")
    private List<String> waferNoList;

    @Schema(title = "lotType列表", example = "[]")
    private List<String> lotTypeList;

    @Schema(title = "dwLayer列表", example = "[]")
    private List<String> dwLayerList;

    @Schema(title = "processStatus列表", example = "[\"PRODUCTION\", \"ENGINEERING\"]")
    private List<String> processStatusList;
}
