package com.guwave.datahub.bpms.app.util;

import com.guwave.datahub.bpms.common.constant.ChartData;
import com.guwave.datahub.bpms.common.constant.ChartType;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.ChartUtils;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.labels.StandardCategoryItemLabelGenerator;
import org.jfree.chart.labels.StandardPieSectionLabelGenerator;
import org.jfree.chart.plot.PiePlot;
import org.jfree.chart.plot.PlotOrientation;
import org.jfree.chart.renderer.category.CategoryItemRenderer;
import org.jfree.chart.renderer.category.StackedBarRenderer;
import org.jfree.data.category.DefaultCategoryDataset;
import org.jfree.data.general.DefaultPieDataset;

import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.util.List;

public class ChartUtil {
    private static final int WIDTH = 700;   // 图表宽度
    private static final int HEIGHT = 450;  // 图表高度

    public static void generateChart(List<ChartData> data, ChartType chartType, String title, File outputFile) throws IOException {
        generateChart(data, chartType, title, outputFile, null, null, null, false);
    }

    public static void generateChart(List<ChartData> data, ChartType chartType, String title, File outputFile, String XAxisName, String YAxisName) throws IOException {
        generateChart(data, chartType, title, outputFile, XAxisName, YAxisName, null, false);
    }

    public static void generateChart(List<ChartData> data, ChartType chartType, String title, File outputFile, String XAxisName, String YAxisName, boolean displayLable) throws IOException {
        generateChart(data, chartType, title, outputFile, XAxisName, YAxisName, null, displayLable);
    }

    public static void generateChart(List<ChartData> data, ChartType chartType, String title, File outputFile, String XAxisName, String YAxisName, PlotOrientation orientation, boolean displayLable) throws IOException {
        JFreeChart chart;
        // 设置中文字体
        Font font = new Font("黑体", Font.PLAIN, 14);

        switch (chartType) {
            case LINE:
                chart = createLineChart(font, data, title, XAxisName, YAxisName, displayLable);
                break;
            case PIE:
                chart = createPieChart(font, data, title);
                break;
            case BAR:
                chart = createBarChart(font, data, title, XAxisName, YAxisName, orientation);
                break;
            case STACKED_BAR:
                chart = createStackedBarChart(font, data, title, XAxisName, YAxisName, orientation);
                break;
            default:
                throw new IllegalArgumentException("Unsupported chart type");
        }


        chart.getTitle().setFont(font);
        chart.getLegend().setItemFont(font);
        chart.setBackgroundPaint(Color.white);
        chart.getPlot().setBackgroundPaint(Color.white);

        // 保存图表
        ChartUtils.saveChartAsPNG(outputFile, chart, WIDTH, HEIGHT);
    }

    private static JFreeChart createLineChart(Font font, List<ChartData> data, String title, String XAxisName, String YAxisName, boolean displayLable) {
        DefaultCategoryDataset dataset = new DefaultCategoryDataset();

        // 将数据添加到数据集
        data.forEach(d -> dataset.addValue(
                d.getValue(),
                d.getSeriesName() != null ? d.getSeriesName() : "Series",
                d.getCategory()
        ));

        // 设置样式


        JFreeChart chart = ChartFactory.createLineChart(
                title,                    // 图表标题
                XAxisName,                   // X轴标签
                YAxisName,                   // Y轴标签
                dataset,                  // 数据集
                PlotOrientation.VERTICAL, // 图表方向
                true,                    // 显示图例
                true,                    // 显示工具提示
                false                    // 显示URL
        );

        chart.getCategoryPlot().getDomainAxis().setLabelFont(font);
        chart.getCategoryPlot().getDomainAxis().setTickLabelFont(font);
        chart.getCategoryPlot().getRangeAxis().setLabelFont(font);
        chart.getCategoryPlot().getRangeAxis().setTickLabelFont(font);
        if (displayLable) {
            CategoryItemRenderer renderer = chart.getCategoryPlot().getRenderer();
            renderer.setDefaultItemLabelGenerator(new StandardCategoryItemLabelGenerator());
            renderer.setDefaultItemLabelsVisible(true);
        }


        return chart;
    }

    private static JFreeChart createPieChart(Font font, List<ChartData> data, String title) {
        DefaultPieDataset<String> dataset = new DefaultPieDataset<>();

        // 将数据添加到数据集
        data.forEach(d -> dataset.setValue(d.getCategory(), d.getValue()));

        JFreeChart chart = ChartFactory.createPieChart(
                title,     // 图表标题
                dataset,   // 数据集
                true,      // 显示图例
                true,      // 显示工具提示
                false      // 显示URL
        );

        ((PiePlot<?>) chart.getPlot()).setLabelFont(font);
        ((PiePlot<?>) chart.getPlot()).setLabelGenerator(new StandardPieSectionLabelGenerator("{0} = {1} ({2})"));
        ;

        return chart;
    }

    private static JFreeChart createBarChart(Font font, List<ChartData> data, String title, String XAxisName, String YAxisName, PlotOrientation orientation) {
        DefaultCategoryDataset dataset = new DefaultCategoryDataset();

        // 将数据添加到数据集
        data.forEach(d -> dataset.addValue(
                d.getValue(),
                d.getSeriesName() != null ? d.getSeriesName() : "Series",
                d.getCategory()
        ));

        JFreeChart chart = ChartFactory.createBarChart(
                title,                    // 图表标题
                XAxisName,                   // X轴标签
                YAxisName,                   // Y轴标签
                dataset,                  // 数据集
                orientation != null ? orientation : PlotOrientation.VERTICAL, // 图表方向
                true,                    // 显示图例
                true,                    // 显示工具提示
                false                    // 显示URL
        );

        chart.getCategoryPlot().getDomainAxis().setLabelFont(font);
        chart.getCategoryPlot().getDomainAxis().setTickLabelFont(font);
        chart.getCategoryPlot().getRangeAxis().setLabelFont(font);
        chart.getCategoryPlot().getRangeAxis().setTickLabelFont(font);

        return chart;
    }

    private static JFreeChart createStackedBarChart(Font font, List<ChartData> data, String title, String XAxisName, String YAxisName, PlotOrientation orientation) {
        DefaultCategoryDataset dataset = new DefaultCategoryDataset();

        // 将数据添加到数据集
        data.forEach(d -> dataset.addValue(
                d.getValue(),
                d.getSeriesName() != null ? d.getSeriesName() : "Series",
                d.getCategory()
        ));

        JFreeChart chart = ChartFactory.createStackedBarChart(
                title,                    // 图表标题
                XAxisName,                   // X轴标签
                YAxisName,                   // Y轴标签
                dataset,                  // 数据集
                orientation != null ? orientation : PlotOrientation.VERTICAL, // 图表方向
                true,                    // 显示图例
                true,                    // 显示工具提示
                false                    // 显示URL
        );

        chart.getCategoryPlot().getDomainAxis().setLabelFont(font);
        chart.getCategoryPlot().getDomainAxis().setTickLabelFont(font);
        chart.getCategoryPlot().getRangeAxis().setLabelFont(font);
        chart.getCategoryPlot().getRangeAxis().setTickLabelFont(font);
        StackedBarRenderer renderer = new StackedBarRenderer();
        renderer.setDrawBarOutline(false); // 不显示柱状图的边框
        renderer.setDefaultItemLabelGenerator(new StandardCategoryItemLabelGenerator()); // 设置标签生成器
        renderer.setDefaultItemLabelsVisible(true); // 显示每个柱状图元素的数值
        chart.getCategoryPlot().setRenderer(renderer);
        return chart;
    }
}
