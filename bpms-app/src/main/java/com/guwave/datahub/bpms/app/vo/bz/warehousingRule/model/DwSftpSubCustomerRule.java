package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.guwave.datahub.bpms.app.converter.TestAreaConverter;
import com.guwave.onedata.dataware.common.contant.TestArea;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DwSftpSubCustomerRule {
    private String factory;
    private String factorySite;
    @ExcelProperty(converter = TestAreaConverter.class)
    private TestArea testArea;
    private String ftpIp;
    private String directory;
    private String subCustomer;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DwSftpSubCustomerRule that = (DwSftpSubCustomerRule) o;
        return Objects.equals(factory, that.factory) && Objects.equals(factorySite, that.factorySite) && testArea == that.testArea && Objects.equals(ftpIp, that.ftpIp) && Objects.equals(directory, that.directory) && Objects.equals(subCustomer, that.subCustomer);
    }

    @Override
    public int hashCode() {
        return Objects.hash(factory, factorySite, testArea, ftpIp, directory, subCustomer);
    }
}
