package com.guwave.datahub.bpms.app.web.interceptor;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.DataPermission;
import com.guwave.datahub.bpms.app.exception.BpmsAuthException;
import com.guwave.datahub.bpms.app.service.auth.AuthService;
import com.guwave.datahub.bpms.app.service.auth.DataPermissionService;
import com.guwave.datahub.bpms.app.vo.auth.DataPermissionInfoV2;
import com.guwave.datahub.bpms.app.vo.auth.UserInfo;
import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.common.constant.Constant;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 认证拦截器
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2024-02-27 09:00:21
 */
@Component
@AllArgsConstructor
public class AuthInterceptor implements HandlerInterceptor {

    private static final Logger LOGGER = LoggerFactory.getLogger(AuthInterceptor.class);

    private final AuthService authService;
    private final DataPermissionService dataPermissionService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Method method = handlerMethod.getMethod();
            Class<?> clazz = method.getDeclaringClass();
            if (method.isAnnotationPresent(Auth.class) || clazz.isAnnotationPresent(Auth.class)) {
                boolean procDataPermissionFlag = false;
                if (method.isAnnotationPresent(DataPermission.class) || clazz.isAnnotationPresent(DataPermission.class)) {
                    procDataPermissionFlag = true;
                }
                LOGGER.info("开始鉴权");
                StopWatch sw = new StopWatch();
                sw.start();
                // 从请求头获取token
                String token = request.getHeader(Constant.AUTH_HEADER_FIELD_NAME);
                if (!this.auth(token, request, procDataPermissionFlag)) {
                    // 鉴权失败
                    throw new BpmsAuthException(ResponseCode.Common.AUTH_FAIL);
                }
                sw.stop();
                LOGGER.info("结束鉴权, 耗时: {}ms", sw.getTotalTimeMillis());
            }
        }
        return HandlerInterceptor.super.preHandle(request, response, handler);
    }

    /**
     * 根据token鉴权
     *
     * @param token Token
     * @return 是否认证通过
     */
    private boolean auth(String token, HttpServletRequest request, boolean procDataPermissionFlag) {
        UserInfo userInfo = this.authService.auth(token);

        if (null == userInfo) {
            String uri = request.getRequestURI();
            LOGGER.info("请求: {}时鉴权失败", uri);
            return false;
        }
        // 保存用户信息
        RequestContext.setUserInfo(userInfo);
        // 数据权限
        if (procDataPermissionFlag) {
            DataPermissionInfoV2 dataPermissionInfo = dataPermissionService.getDataPermissionV2();
            RequestContext.setDataPermission(dataPermissionInfo);
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清理用户信息
        RequestContext.clear();
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
    }
}
