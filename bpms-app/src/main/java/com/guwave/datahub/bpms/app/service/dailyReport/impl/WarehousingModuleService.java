package com.guwave.datahub.bpms.app.service.dailyReport.impl;

import com.alibaba.fastjson2.JSON;
import com.guwave.datahub.bpms.app.service.dailyReport.CommonService;
import com.guwave.datahub.bpms.app.util.DateUtil;
import com.guwave.datahub.bpms.app.util.PdfUtil;
import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.WarehousingFailFileResult;
import com.guwave.datahub.bpms.common.constant.ChartData;
import com.guwave.datahub.bpms.common.constant.ChartType;
import com.guwave.datahub.bpms.common.constant.DataCode;
import com.guwave.datahub.bpms.common.constant.ReportModule;
import com.guwave.datahub.bpms.dao.domain.dailyReport.DailyReportResult;
import com.guwave.datahub.bpms.dao.repository.bpms.DailyReportResultRepository;
import com.guwave.datahub.bpms.dao.repository.dw.FileLoadingLogRepository;
import com.guwave.datahub.bpms.dao.repository.dw.LotWaferCalRecordRepository;
import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.WarehousingFailFileDetail;
import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.WarehousingLotWaferResult;
import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.WarehousingSuccessFileResult;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class WarehousingModuleService implements CommonService {

    @Autowired
    private DailyReportResultRepository dailyReportResultRepository;

    @Autowired
    private FileLoadingLogRepository fileLoadingLogRepository;

    @Autowired
    private LotWaferCalRecordRepository lotWaferCalRecordRepository;

    private boolean initFlag = false;

    private WarehousingLotWaferResult warehousingLotWaferResult;

    private List<ChartData> successFileCntChartData;

    private List<ChartData> successFileSizeChartData;

    private List<WarehousingFailFileDetail> failFileDetail;

    private WarehousingSuccessFileResult successFileResult;


    public void init() {
        Long successLotWaferCnt = lotWaferCalRecordRepository.countByLatestFlagAndTestItemStatusAndCreateTimeBetween(1, ProcessStatus.SUCCESS, DateUtil.getDateFormattedStartTimeBeforeDays(1), DateUtil.getDateFormattedStartTimeBeforeDays(0));
        String day = DateUtil.getDayBefortDays(1);
        warehousingLotWaferResult = new WarehousingLotWaferResult().setSuccessLotWaferCnt(successLotWaferCnt);

        failFileDetail = fileLoadingLogRepository.findFailFileDetail(ProcessStatus.FAIL.name(), DateUtil.getStringFormattedStartTimeBeforeDays(1), DateUtil.getStringFormattedStartTimeBeforeDays(0))
                .stream().map(this::buildWarehousingFailFileDetail).collect(Collectors.toList());
        successFileResult = getSuccessFileResult(ProcessStatus.SUCCESS.name(), DateUtil.getStringFormattedStartTimeBeforeDays(1), DateUtil.getStringFormattedStartTimeBeforeDays(0));

        // 找前6天的数据
        List<DailyReportResult> dailyReportResults = dailyReportResultRepository.findAllByReportModuleAndDataCodeAndLatestFlagAndDayBetween(ReportModule.WAREHOUSING, DataCode.SUCCESS_WAREHOUSING_FILE, 1, DateUtil.getDayBefortDays(7), DateUtil.getDayBefortDays(1));

        successFileCntChartData = dailyReportResults.stream().flatMap(t -> {
            WarehousingSuccessFileResult result = JSON.parseObject(t.getData(), WarehousingSuccessFileResult.class);
            return Stream.of(
                    new ChartData(t.getDay(), result.getSuccessFileCnt(), "总文件数量"),
                    new ChartData(t.getDay(), result.getSuccessBitMemFileCnt(), FileCategory.BIT_MEM.getCategory()),
                    new ChartData(t.getDay(), result.getSuccessSummaryFileCnt(), FileCategory.SUMMARY.getCategory()),
                    new ChartData(t.getDay(), result.getSuccessRawDataFileCnt(), FileCategory.RAW_DATA.getCategory()),
                    new ChartData(t.getDay(), result.getSuccessStdfFileCnt(), FileCategory.STDF.getCategory())
            );
        }).collect(Collectors.toList());
        // 将当天数据填入;
        successFileCntChartData.add(new ChartData(day, successFileResult.getSuccessFileCnt(), "总文件数量"));
        successFileCntChartData.add(new ChartData(day, successFileResult.getSuccessBitMemFileCnt(), FileCategory.BIT_MEM.getCategory()));
        successFileCntChartData.add(new ChartData(day, successFileResult.getSuccessSummaryFileCnt(), FileCategory.SUMMARY.getCategory()));
        successFileCntChartData.add(new ChartData(day, successFileResult.getSuccessRawDataFileCnt(), FileCategory.RAW_DATA.getCategory()));
        successFileCntChartData.add(new ChartData(day, successFileResult.getSuccessStdfFileCnt(), FileCategory.STDF.getCategory()));

        successFileSizeChartData = dailyReportResults.stream().flatMap(t -> {
            WarehousingSuccessFileResult result = JSON.parseObject(t.getData(), WarehousingSuccessFileResult.class);
            return Stream.of(
                    new ChartData(t.getDay(), result.getSuccessFileSize(), "总文件大小"),
                    new ChartData(t.getDay(), result.getSuccessBitMemFileSize(), FileCategory.BIT_MEM.getCategory()),
                    new ChartData(t.getDay(), result.getSuccessSummaryFileSize(), FileCategory.SUMMARY.getCategory()),
                    new ChartData(t.getDay(), result.getSuccessRawDataFileSize(), FileCategory.RAW_DATA.getCategory()),
                    new ChartData(t.getDay(), result.getSuccessStdfFileSize(), FileCategory.STDF.getCategory())
            );
        }).collect(Collectors.toList());
        // 将当天数据填入;
        successFileSizeChartData.add(new ChartData(day, successFileResult.getSuccessFileSize(), "总文件大小"));
        successFileSizeChartData.add(new ChartData(day, successFileResult.getSuccessBitMemFileSize(), FileCategory.BIT_MEM.getCategory()));
        successFileSizeChartData.add(new ChartData(day, successFileResult.getSuccessSummaryFileSize(), FileCategory.SUMMARY.getCategory()));
        successFileSizeChartData.add(new ChartData(day, successFileResult.getSuccessRawDataFileSize(), FileCategory.RAW_DATA.getCategory()));
        successFileSizeChartData.add(new ChartData(day, successFileResult.getSuccessStdfFileSize(), FileCategory.STDF.getCategory()));
    }

    public void generateWarehousingLotWaferDetail(PdfUtil pdfUtil) throws IOException {
        if (!initFlag) {
            init();
            initFlag = true;
        }

        //  将结果转为json之后写入表中
        saveDailyReportResult(JSON.toJSONString(warehousingLotWaferResult), ReportModule.WAREHOUSING, DataCode.SUCCESS_WAREHOUSING_LOT_WAFER);

        // 写入pdf
        pdfUtil.addBodyText("成功入库的lot/wafer数:" + warehousingLotWaferResult.getSuccessLotWaferCnt());
    }

    public void generateWarehousingSuccessFileDetail(PdfUtil pdfUtil) throws IOException {
        if (!initFlag) {
            init();
            initFlag = true;
        }

        saveDailyReportResult(JSON.toJSONString(successFileResult), ReportModule.WAREHOUSING, DataCode.SUCCESS_WAREHOUSING_FILE);

        pdfUtil.addBodyText("成功入库的文件数:" + successFileResult.getSuccessFileCnt());
        pdfUtil.addBodyText("成功入库的文件大小:" + successFileResult.getSuccessFileSize() + "GB");
        pdfUtil.addBodyText(Constant.EMPTY);
    }


    public void generateWarehousingSuccessFileChart(PdfUtil pdfUtil) throws IOException {
        if (!initFlag) {
            init();
            initFlag = true;
        }

        pdfUtil.addChart(successFileCntChartData, ChartType.LINE, "近7天成功入库文件数", "时间", "大小", true);
        pdfUtil.addChart(successFileSizeChartData, ChartType.LINE, "近7天成功入库文件大小", "时间", "大小", true);
    }

    /**
     * 再pdf中生成入库失败文件的任务
     *
     * @param pdfUtil pdfUtil
     * @throws IOException IOException
     */
    public void generateWarehousingFailFileDetail(PdfUtil pdfUtil) throws IOException {
        if (!initFlag) {
            init();
            initFlag = true;
        }
        WarehousingFailFileResult result = new WarehousingFailFileResult();
        result.setFailFileCnt((long) failFileDetail.size());
        pdfUtil.addBodyText("入库失败文件数：" + failFileDetail.size());
        if (failFileDetail.size() > 0) {
            Map<String, Long> exceptionTypeMap = failFileDetail.stream().collect(Collectors.groupingBy(WarehousingFailFileDetail::getExceptionType, Collectors.counting()));
            List<ChartData> exceptionMapChartData = new ArrayList<>();
            exceptionTypeMap.forEach((key, value) -> exceptionMapChartData.add(new ChartData(key, value)));
            result.setExceptionTypeMap(exceptionTypeMap);
            pdfUtil.addChart(exceptionMapChartData, ChartType.PIE, "入库失败类型分布");

            Map<String, Long> fileCategoryMap = failFileDetail.stream().collect(Collectors.groupingBy(WarehousingFailFileDetail::getFileCategory, Collectors.counting()));
            List<ChartData> fileCategoryMapChartData = new ArrayList<>();
            fileCategoryMap.forEach((key, value) -> fileCategoryMapChartData.add(new ChartData(key, value)));
            result.setFileCategoryMap(fileCategoryMap);
            pdfUtil.addChart(fileCategoryMapChartData, ChartType.PIE, "入库失败文件类型分布");
        }

        saveDailyReportResult(JSON.toJSONString(result), ReportModule.WAREHOUSING, DataCode.FAIL_WAREHOUSING_FILE);
    }

    @Override
    public DailyReportResultRepository getDailyReportResultRepository() {
        return dailyReportResultRepository;
    }

    public WarehousingSuccessFileResult getSuccessFileResult(String status, String start, String end) {
        Object[] result = fileLoadingLogRepository.findSuccessFileResult(status, start, end).get(0);
        if (result == null || result.length == 0) return new WarehousingSuccessFileResult();

        return new WarehousingSuccessFileResult(
                ((Number) result[0]).longValue(),
                ((Number) result[1]).doubleValue(),
                ((Number) result[2]).longValue(),
                ((Number) result[3]).doubleValue(),
                ((Number) result[4]).longValue(),
                ((Number) result[5]).doubleValue(),
                ((Number) result[6]).longValue(),
                ((Number) result[7]).doubleValue(),
                ((Number) result[8]).longValue(),
                ((Number) result[9]).doubleValue()
        );
    }

    public WarehousingFailFileDetail buildWarehousingFailFileDetail(Object[] objects) {
        return new WarehousingFailFileDetail(
                (String) objects[0],
                (String) objects[1]
        );
    }

    public void resetInitFlag() {
        initFlag = false;
    }
}
