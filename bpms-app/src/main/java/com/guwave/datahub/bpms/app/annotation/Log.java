package com.guwave.datahub.bpms.app.annotation;

import java.lang.annotation.*;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * Log注解
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-26 14:18:19
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Log {

    /**
     * 是否打印请求日志
     *
     * @return boolean
     */
    boolean request() default true;

    /**
     * 是否打印返回日志
     *
     * @return boolean
     */
    boolean response() default true;

    /**
     * 是否打印请求耗时
     *
     * @return boolean
     */
    boolean cost() default true;
}
