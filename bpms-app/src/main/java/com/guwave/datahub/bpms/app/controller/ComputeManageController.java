package com.guwave.datahub.bpms.app.controller;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.service.bz.ComputeManageService;
import com.guwave.datahub.bpms.app.vo.bz.computeManage.model.ComputeManageConfig;
import com.guwave.datahub.bpms.app.vo.bz.computeManage.request.ComputeManageUpdateVo;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Min;

@Log
@Slf4j
@RestController
@AllArgsConstructor
@Auth
@Tag(name = "ComputeManageControllerAPI", description = "业务计算管理相关接口",
        externalDocs = @ExternalDocumentation(description = "需求文档",
                url = "https://guwave.sharepoint.com/:fl:/g/contentstorage/CSP_3ffa68d2-cef0-4193-a70e-9eff47b9b177/EfQOiIf-i8VIkMVSGeeGhuwB1Z7g0NgB2cUSX3u7fbdYvw?e=Xh9chn&nav=cz0lMkZjb250ZW50c3RvcmFnZSUyRkNTUF8zZmZhNjhkMi1jZWYwLTQxOTMtYTcwZS05ZWZmNDdiOWIxNzcmZD1iJTIxMG1qNlBfRE9rMEduRHA3X1I3bXhkME1VM2Jnd0hyaEdwSFhUSFRmVTRkNUF1UGNVWHlsQVFKbFVEQjZ4SmFsNSZmPTAxSENMSUNMSFVCMkVJUDdVTFlWRUpCUktTREhUWU5CWE0mYz0lMkYmYT1Mb29wQXBwJnA9JTQwZmx1aWR4JTJGbG9vcC1wYWdlLWNvbnRhaW5lciZ4PSU3QiUyMnclMjIlM0ElMjJUMFJUVUh4bmRYZGhkbVV1YzJoaGNtVndiMmx1ZEM1amIyMThZaUV3YldvMlVGOUVUMnN3UjI1RWNEZGZVamR0ZUdRd1RWVXpZbWQzU0hKb1IzQklXRlJJVkdaVk5HUTFRWFZRWTFWWWVXeEJVVXBzVlVSQ05uaEtZV3cxZkRBeFNFTk1TVU5NUWxkU1JGVlhOa2RLVWtkR1JreExSVWxNU0U5UlIwaE1TMDglM0QlMjIlMkMlMjJpJTIyJTNBJTIyYjMxNjliN2EtM2M1YS00YmVlLTg1NGYtOWFhMTIxMjFmMTUxJTIyJTdE")
)
@RequestMapping("computeManage")
public class ComputeManageController {

    private final ComputeManageService computeManageService;

    @Operation(summary = "获取业务列表")
    @PostMapping("list")
    public ResponseVo<PageableDataVo<ComputeManageConfig>> list(@Validated @RequestBody PageableRequestVo pageableRequestVo) {
        return ResponseVo.ok(computeManageService.list(pageableRequestVo));
    }

    @Operation(summary = "创建计算管理配置")
    @PostMapping("addConfig")
    public ResponseVo<ComputeManageConfig> addConfig(@Validated @RequestBody ComputeManageConfig item) {
        return ResponseVo.ok(computeManageService.addConfig(item));
    }

    @Operation(summary = "更新计算管理配置")
    @PostMapping("updateConfig")
    public ResponseVo<ComputeManageConfig> updateConfig(@Validated @RequestBody ComputeManageUpdateVo updateVo) {
        return ResponseVo.ok(computeManageService.updateConfig(updateVo));
    }

    @Operation(summary = "删除计算管理配置")
    @DeleteMapping("deleteConfig")
    public ResponseVo<Void> deleteConfig(@RequestParam("id") @Min(1) Long id) {
        computeManageService.deleteConfig(id);
        return ResponseVo.ok();
    }
}
