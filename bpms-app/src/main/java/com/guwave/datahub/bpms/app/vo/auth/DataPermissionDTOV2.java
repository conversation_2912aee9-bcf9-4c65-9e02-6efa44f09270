package com.guwave.datahub.bpms.app.vo.auth;

import lombok.Data;

import java.util.List;

/**
 * - 新版的数据权限返回结果
 * Copyright (C), 2021, guwave
 * @Title: DataPermissionDTOV2.java
 * @summary 
 * <AUTHOR> <PERSON>
 * @version 0.0.1
 * @date 2024-9-11
 */
@Data
public class DataPermissionDTOV2 {
	
    /**
     * - 是否开启数据权限
     * true的时候开启数据权限
     */
    private Boolean accessSwitch;
    
    /**
     * - 是否管理员权限
     * - 管理员拥有所有的数据权限
     */
	private Boolean admin;
	
	/**
	 * - 数据权限信息，每个角色一条
	 * - 角色之间是or的关系
	 */
	private List<RoleDataPermissionDTO> data;
	

	/**
	 * - 单个角色拥有的数据权限信息
	 * - 角色内部的数据权限是and的关系，为空的除外
	 */
	@Data
	public static class RoleDataPermissionDTO {
	    
	    private List<String> factory;
	    
	    private List<String> product;
	    
	    /**
	     * added 20250318 by wt for 支持数据权限增加productFamily
	     */
	    private List<String> productFamily;
	    
	    /**
         * added 20250318 by wt for 支持数据权限增加productLine
         */
	    private List<String> productLine;
	    
	    private List<String> testArea;
	    
	    private List<String> subCustomer;
	    
	    private List<String> deviceId;
	    
	    private List<String> lotType;
	    
	}
}
