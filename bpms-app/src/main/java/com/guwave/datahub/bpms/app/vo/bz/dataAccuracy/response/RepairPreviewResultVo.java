package com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response;

import cn.hutool.core.util.StrUtil;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.DataAccuracyCheckHelper;
import com.guwave.onedata.dataware.repair.api.vo.DataAccuracyResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * 2025/4/27 18:18
 * RepairPreviewResultVo
 *
 * <AUTHOR>
 */
@Schema(title = "数据修复预览结果")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper=false)
public class RepairPreviewResultVo extends DataAccuracyResult implements Serializable {

    private static final long serialVersionUID = 505440488505649168L;

    private String firstYieldShowName;
    private String finalYieldShowName;
    private String dataAccuracyCheckShowName;

    public static RepairPreviewResultVo of(DataAccuracyResult data) {
        RepairPreviewResultVo repairPreviewResultVo = RepairPreviewResultVo.builder()
                .firstYieldShowName(bigDecimalToPercentStr(data.getFirstYield(), true))
                .finalYieldShowName(bigDecimalToPercentStr(data.getFinalYield(), true))
                .dataAccuracyCheckShowName(DataAccuracyCheckHelper.getShowName(data.getDataAccuracyCheck()))
                .build();

        repairPreviewResultVo
                .setTestArea(data.getTestArea())
                .setTestStage(data.getTestStage())
                .setLotType(data.getLotType())
                .setFileNames(data.getFileNames())
                .setDeviceId(data.getDeviceId())
                .setLotId(data.getLotId())
                .setWaferId(data.getWaferId())
                .setWaferNo(data.getWaferNo())
                .setSblotId(data.getSblotId())
                .setTestProgram(data.getTestProgram())
                .setInputCnt(data.getInputCnt())
                .setFirstGoodCnt(data.getFirstGoodCnt())
                .setFinalGoodCnt(data.getFinalGoodCnt())
                .setFirstFailCnt(data.getFirstFailCnt())
                .setFinalFailCnt(data.getFinalFailCnt())
                .setFirstYield(data.getFirstYield())
                .setFinalYield(data.getFinalYield())
                .setDataAccuracyCheck(data.getDataAccuracyCheck());

        return repairPreviewResultVo;
    }

    public static String bigDecimalToPercentStr(BigDecimal val, boolean multiply) {
        if (Objects.isNull(val)) {
            return StrUtil.EMPTY;
        }

        BigDecimal res = val;
        if (multiply) {
            res = val.multiply(BigDecimal.valueOf(100));
        }
        return res.setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + "%";
    }

}
