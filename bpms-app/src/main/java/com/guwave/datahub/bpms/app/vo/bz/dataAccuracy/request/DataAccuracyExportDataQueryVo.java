package com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;


@Schema(title = "数据导出请求体")
@Data
@EqualsAndHashCode(callSuper = false)
public class DataAccuracyExportDataQueryVo {

    @Schema(description = "子客户", example = "[\"AMD\"]")
    private List<String> subCustomerList;

    @Schema(description = "工厂", example = "[\"AMD\"]")
    private List<String> factoryList;

    @Schema(description = "测试区域", example = "CP")
    @NotNull(message = "TestArea不能为空")
    private String testArea;

    @Schema(description = "deviceId", example = "[\"deviceId-1\"]")
    private List<String> deviceIdList;

    @Schema(description = "testStage", example = "[\"CP2\"]")
    private List<String> testStageList;

    @Schema(description = "lotId", example = "[\"lotId-1\"]")
    private List<String> lotIdList;

    @Schema(description = "waferNo", example = "[\"14\"]")
    private List<String> waferNoList;

    @Schema(description = "sblotId", example = "[\"sblotId-1\"]")
    private List<String> sblotIdList;

    @Schema(description = "lotType", example = "[\"PRODUCTION\"]")
    private List<String> lotTypeList;

    @Schema(description = "mesMatchFlag", example = "[\"deviceId-1\"]")
    private List<String> mesMatchFlagList;

    @Schema(description = "dataAccuracyCheck", example = "[\"deviceId-1\"]")
    private List<String> dataAccuracyCheckList;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "startTime", example = "开始时间, 格式 yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "endTime", example = "结束时间，格式 yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

}
