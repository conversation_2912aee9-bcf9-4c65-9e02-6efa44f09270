package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(title = "入库规则流程保存")
public class WarehousingRuleFlowRequestVo {

    @Schema(title = "id")
    private Long id;

    @Schema(title = "name", example = "入库规则1")
    @NotBlank(message = "name不能为空")
    private String name;

    @Schema(title = "sub_customer", example = "AMD")
    @NotBlank(message = "sub_customer不能为空")
    private String subCustomer;

    @Schema(title = "factory", example = "TSMC")
    @NotBlank(message = "factory不能为空")
    private String factory;

    @Schema(title = "test_area", example = "CP")
    @NotBlank(message = "test_area不能为空")
    private String testArea;

    @Schema(title = "file_category", example = "STDF")
    @NotBlank(message = "file_category不能为空")
    private String fileCategory;

    @Schema(title = "remarks", example = "备注1")
    private String remarks;

}
