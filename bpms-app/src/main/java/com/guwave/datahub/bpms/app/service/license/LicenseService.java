package com.guwave.datahub.bpms.app.service.license;

import com.alibaba.fastjson.JSON;
import com.guwave.datahub.bpms.app.exception.BpmsLicenseException;
import com.guwave.datahub.bpms.app.vo.license.LicenseRequestVo;
import com.guwave.datahub.bpms.app.vo.license.LicenseResponseVo;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * LicenseService
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-27 11:43:38
 */
@Service
@AllArgsConstructor
public class LicenseService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LicenseService.class);

    public static final String LICENSE_CACHE = "bpms-license:cache:15m";
    public static final String TRUE = "true";

    private final LicenseApi licenseApi;

    @Cacheable(LICENSE_CACHE)
    public void check(String companyCode, String moduleName, String userName) {
        try {
            StopWatch sw = new StopWatch();
            sw.start();
            LicenseRequestVo licenseRequestVo = new LicenseRequestVo();
            licenseRequestVo.setCompanyCode(companyCode);
            licenseRequestVo.setModuleName(moduleName);
            licenseRequestVo.setUserName(userName);
            LOGGER.info("开始请求LicenseApi, 参数: {}", JSON.toJSONString(licenseRequestVo));
            LicenseResponseVo<String> result = this.licenseApi.check(licenseRequestVo);
            sw.stop();
            LOGGER.info("结束请求LicenseApi, 结果: {}, 耗时: {}ms", JSON.toJSONString(result), sw.getTotalTimeMillis());
            if (!result.success()) {
                LOGGER.info("校验License失败: {} ", result.getMsg());
                throw new BpmsLicenseException(ResponseCode.Common.LICENSE_CHECK_FAIL);
            }
            if (!TRUE.equalsIgnoreCase(result.getData())) {
                LOGGER.info("当前模块超出License授权数");
                throw new BpmsLicenseException(ResponseCode.Common.LICENSE_EXCEED);
            }
        } catch (BpmsLicenseException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.info("请求License接口失败: {}", e.getMessage());
            throw new BpmsLicenseException(e, ResponseCode.Common.LICENSE_REQUEST_FAIL);
        }
    }
}
