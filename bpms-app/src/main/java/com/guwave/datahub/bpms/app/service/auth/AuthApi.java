package com.guwave.datahub.bpms.app.service.auth;

import com.guwave.datahub.bpms.app.vo.auth.DataPermissionDTOV2;
import com.guwave.datahub.bpms.app.vo.auth.UserInfoUcVo;
import com.guwave.datahub.bpms.app.annotation.FeignClient;
import com.guwave.datahub.bpms.app.vo.auth.UserResponseDTO;
import feign.Headers;
import feign.RequestLine;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * AuthApi
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-27 14:51:23
 */
@FeignClient(value = "authApi", url = "${auth.baseUrl}", interceptors = {"ucAuthInterceptor"})
public interface AuthApi {

    @RequestLine("GET /getInfo")
    UserInfoUcVo getInfo();

    /**
     * - 第二版数据权限接口
     * added 20240911 by wt
     */
    @RequestLine("GET /dataPermissionV2")
    @Headers("Content-Type: application/json")
    UserResponseDTO<DataPermissionDTOV2> getDataPermissionV2();
}
