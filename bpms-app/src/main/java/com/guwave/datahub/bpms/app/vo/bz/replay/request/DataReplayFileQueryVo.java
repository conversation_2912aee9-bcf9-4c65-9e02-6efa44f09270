package com.guwave.datahub.bpms.app.vo.bz.replay.request;

import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@Schema(title = "数据入库记录查询请求体")
@Data
@EqualsAndHashCode(callSuper = false)
public class DataReplayFileQueryVo extends PageableRequestVo {

    @Schema(title = "fileName", description = "文件名", example = "CP1_RT_2022-10-10.stdf")
    private String fileName;

    @Schema(title = "remoteFilePath", description = "远程服务器的原始文件路径", example = "/onedata-saas/ETA/FT/YT_TESTING/raw_data/ETA7085S2F_21442980_A0172211T01022_FT.csv")
    private String remoteFilePath;

    @Schema(title = "testArea", description = "CP/FT/WAT等 表示测试阶段大类型", example = "CP")
    private String testArea;

    @Schema(title = "工厂列表", description = "测试工厂", example = "[\"VC\"]")
    private List<String> factoryList;

    @Schema(title = "fileCategory", description = "只能单选，取值为：STDF,RAW_DATA,WAT", example = "STDF")
    private FileCategory fileCategory;

    @Schema(title = "stepList", example = "[\"5100\"]")
    private List<Integer> stepList;

    @Schema(title = "statusList", description = "文件入库状态", example = "[\"SUCCESS\"]")
    private List<String> processStatusList;

    @Schema(title = "异常信息", example = "RAW_DATA文件转换异常")
    private String exceptionMessage;

    @Schema(title = "createTime", description = "createTime", example = "[\"2023-09-20 08:14:13\", \"2023-09-21 08:14:13\"]")
    private List<String> createTime;

    @Schema(title = "updateTime", description = "updateTime", example = "[\"2023-09-20 08:14:13\", \"2023-09-21 08:14:13\"]")
    private List<String> updateTime;
}
