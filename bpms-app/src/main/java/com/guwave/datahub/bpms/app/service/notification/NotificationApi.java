package com.guwave.datahub.bpms.app.service.notification;

import com.guwave.datahub.bpms.app.annotation.FeignClient;
import com.guwave.datahub.bpms.app.vo.notification.NotificationRequestVo;
import com.guwave.datahub.bpms.app.vo.notification.NotificationResponseVo;
import feign.Headers;
import feign.RequestLine;


@FeignClient(name = "notificationApi", url = "${license.baseUrl}")
public interface NotificationApi {

    @RequestLine("POST /notification/send")
    @Headers("Content-Type: application/json")
    NotificationResponseVo<String> sendNotification(NotificationRequestVo notificationRequestVo);
}
