package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.response;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.guwave.datahub.bpms.app.converter.StringToListConverter;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpFileConvertScript;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_FORMAT;

/**
 * 2025/3/4 16:49
 * DwSftpFileConvertScriptVo
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(title = "解析脚本配置规则")
public class DwSftpFileConvertScriptVo {
    @Schema(title = "id")
    private Long id;

    @Schema(description = "客户名称", example = "AMD")
    private String customer;

    @Schema(description = "子客户", example = "AMD")
    @ExcelProperty("subCustomer")
    private String subCustomer;

    @Schema(description = "测试阶段类型: CP/FT/WAT/SLT/EQC 等", example = "CP")
    @ExcelProperty("testArea")
    private String testArea;

    @Schema(description = "测试工厂名称", example = "TSMC")
    @ExcelProperty("factory")
    private String factory;

    @Schema(description = "测试子工厂", example = "TSMC")
    @ExcelProperty("factorySite")
    private String factorySite;

    @Schema(description = "测试子工厂", example = "RAW_DATA")
    @ExcelProperty("fileCategory")
    private String fileCategory;

    @Schema(description = "此脚本匹配文件名的规则", example = "(\\.xls$)|(\\.xlsx$)")
    @ExcelProperty("regRule")
    private String regRule;

    @Schema(description = "脚本文件名称(压缩包)", example = "HISENSE_FT_HN_summary_xlsx_auto.zip")
    @ExcelProperty("fileName")
    private String fileName;

    @Schema(description = "压缩包内符合条件脚本文件列表", example = "[\"HISENSE_FT_HN_summary_xlsx_auto-1.py\",\"HISENSE_FT_HN_summary_xlsx_auto-2.py\"]")
    @ExcelProperty(value = "scriptList", converter = StringToListConverter.class)
    private List<String> scriptList;

    @Schema(description = "执行的入口脚本名称", example = "HISENSE_FT_HN_summary_xlsx_auto.py")
    @ExcelProperty("executeFileName")
    private String executeFileName;

    @Schema(title = "createTime", description = "创建时间", example = "2024-11-15 11:21:00")
    private String createTime;

    @Schema(title = "updateTime", description = "更新时间", example = "2024-11-18 11:21:00")
    private String updateTime;

    @Schema(description = "创建用户", example = "System")
    private String createUser;

    @Schema(description = "更新用户", example = "System")
    private String updateUser;

    public static DwSftpFileConvertScriptVo of(SftpFileConvertScript data) {
        return DwSftpFileConvertScriptVo.builder()
                .id(data.getId())
                .customer(data.getCustomer())
                .subCustomer(data.getSubCustomer())
                .testArea(TestArea.of(data.getTestArea()))
                .factory(data.getFactory())
                .factorySite(data.getFactorySite())
                .fileCategory(FileCategory.of(data.getFileCategory()))
                .regRule(data.getRegRule())
                .fileName(data.getFileName())
                .scriptList(CollectionUtils.isNotEmpty(data.getScriptList()) ? data.getScriptList() : Collections.singletonList(data.getExecuteFileName()))
                .executeFileName(data.getExecuteFileName())
                .createTime(DateUtil.format(data.getCreateTime(), NORM_DATETIME_FORMAT))
                .updateTime(DateUtil.format(data.getUpdateTime(), NORM_DATETIME_FORMAT))
                .createUser(data.getCreateUser())
                .updateUser(data.getUpdateUser())
                .build();
    }
}
