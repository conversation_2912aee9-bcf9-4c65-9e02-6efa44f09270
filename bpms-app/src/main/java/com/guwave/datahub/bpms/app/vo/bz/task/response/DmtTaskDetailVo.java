package com.guwave.datahub.bpms.app.vo.bz.task.response;

import cn.hutool.core.date.DateUtil;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.linkx.dao.mysql.domain.bz.BzTaskInstance;
import com.guwave.onedata.next.compute.common.constant.SubmitMode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.stream.Stream;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_FORMAT;

/**
 * 2024/10/25 14:10
 * DmtTaskDetailVo
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
@Schema(title = "单条任务详情，包括spark运行参数")
public class DmtTaskDetailVo {
    @Schema(title = "id")
    private Long id;

    @Schema(title = "preTaskInstanceId", example = "null")
    private Long preTaskInstanceId;

    @Schema(title = "name", description = "task的名字", example = "NEW_DATA_001")
    private String name;

    @Schema(title = "executeEngine", description = "执行引擎: CLICKHOUSE/SPARK/HIVE", example = "SPARK")
    private String executeEngine;

    @Schema(title = "numExecutors", description = "container个数", example = "2")
    private String numExecutors;

    @Schema(title = "executorCores", description = "每个container的cpu数", example = "4")
    private String executorCores;

    @Schema(title = "executorMemory", description = "executor内存(GB)", example = "8")
    private String executorMemory;

    @Schema(title = "driverMemory", description = "driver内存(GB)(GB)", example = "5")
    private String driverMemory;

    @Schema(title = "parallelism", description = "最大并发数", example = "20")
    private String parallelism;

    @Schema(title = "extraConf", description = "额外spark配置", example = "")
    private String extraConf;

    @Schema(title = "processStatus", description = "计算状态", example = "FAIL")
    private String processStatus;

    @Schema(title = "createTime", description = "任务开始时间", example = "2024-11-15 11:21:00")
    private String createTime;

    @Schema(title = "updateTime", description = "任务更新时间", example = "2024-11-18 11:21:00")
    private String updateTime;

    @Schema(title = "modifyParamFlag", description = "是否允许修改任务参数", example = "falser")
    private boolean modifyParamFlag;

    public static DmtTaskDetailVo of(BzTaskInstance item) {
        String status = item.getProcessStatus() == null ? null : item.getProcessStatus().name();
        String numExecutors = item.getNumExecutors() == null ? null : item.getNumExecutors().toString();
        String executorCores = item.getExecutorCores() == null ? null : item.getExecutorCores().toString();
        String executorMemory = item.getExecutorMemory() == null ? null : item.getExecutorMemory().toString();
        String driverMemory = item.getDriverMemory() == null ? null : item.getDriverMemory().toString();
        String parallelism = item.getParallelism() == null ? null : item.getParallelism().toString();

        boolean modifyParamFlag = false;
        if (item.getProcessStatus() == ProcessStatus.FAIL &&
                item.getSubmitMode() == SubmitMode.DIRECT &&
                Stream.of(numExecutors, executorCores, executorMemory, driverMemory, parallelism, item.getExtraConf()).allMatch(StringUtils::isNotBlank)) {
            // 当任务失败，且为直接提交模式，且参数均不为空时，可以修改资源参数重跑
            log.info("当任务失败，且为直接提交模式，且参数均不为空时，可以修改资源参数重跑");
            modifyParamFlag = true;
        }

        return DmtTaskDetailVo.builder()
                .id(item.getId())
                .preTaskInstanceId(item.getPreTaskInstanceId())
                .name(item.getName())
                .executeEngine(item.getExecuteEngine().getEngine())
                .numExecutors(numExecutors)
                .executorCores(executorCores)
                .executorMemory(executorMemory)
                .driverMemory(driverMemory)
                .parallelism(parallelism)
                .extraConf(item.getExtraConf())
                .processStatus(status)
                .createTime(DateUtil.format(item.getCreateTime(), NORM_DATETIME_FORMAT))
                .updateTime(DateUtil.format(item.getUpdateTime(), NORM_DATETIME_FORMAT))
                .modifyParamFlag(modifyParamFlag)
                .build();

    }

}
