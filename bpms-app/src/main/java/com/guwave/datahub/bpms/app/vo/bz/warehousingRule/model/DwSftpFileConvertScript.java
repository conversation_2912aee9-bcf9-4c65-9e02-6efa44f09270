package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.guwave.datahub.bpms.app.converter.FileCategoryConverter;
import com.guwave.datahub.bpms.app.converter.ListStringConverter;
import com.guwave.datahub.bpms.app.converter.TestAreaConverter;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.TestArea;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DwSftpFileConvertScript {
    private String subCustomer;
    @ExcelProperty(converter = TestAreaConverter.class)
    private TestArea testArea;
    private String factory;
    private String factorySite;
    @ExcelProperty(converter = FileCategoryConverter.class)
    private FileCategory fileCategory;
    private String regRule;
    private String fileName;
    @ExcelProperty(converter = ListStringConverter.class)
    private List<String> scriptList;
    private String executeFileName;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DwSftpFileConvertScript that = (DwSftpFileConvertScript) o;
        return Objects.equals(subCustomer, that.subCustomer) && Objects.equals(testArea, that.testArea) && Objects.equals(factory, that.factory) && Objects.equals(factorySite, that.factorySite) && Objects.equals(fileCategory, that.fileCategory) && Objects.equals(regRule, that.regRule) && Objects.equals(fileName, that.fileName) && Objects.equals(executeFileName, that.executeFileName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(subCustomer, testArea, factory, factorySite, fileCategory, regRule, fileName, executeFileName);
    }
}
