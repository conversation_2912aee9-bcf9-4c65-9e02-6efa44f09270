package com.guwave.datahub.bpms.app.vo.bz.dataTimeliness.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.guwave.datahub.bpms.app.vo.bz.dataTimeliness.model.Statistics;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class DataTimelinessVo {

    @Schema(title = "入库耗时 By Factory")
    private List<Statistics.StatisticsDetail> groupByFactoryData;
    @Schema(title = "入库耗时 By Factory&Tester")
    private List<Statistics.StatisticsDetail> groupByFactoryAndTester;
    @Schema(title = "数据入库耗时趋势")
    private List<Statistics.StatisticsDetail> groupByWeek;
    @Schema(title = "Top 10 耗时最高文件列表")
    private List<FileDetail> top10Data;
    @Schema(title = "数据入库时间流")
    private Statistics inDbStream;

    @Data
    public static class FileDetail {
        @Schema(title = "Factory")
        private String factory;
        @Schema(title = "Tester")
        private String tester;
        @Schema(title = "File Name")
        private String fileName;
        @Schema(title = "File Location")
        private String fileLocation;
        @Schema(title = "整体耗时/h")
        private BigDecimal costTime;

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        @Schema(title = "FTP 加载时间", example = "格式 yyyy-MM-dd HH:mm:ss")
        private LocalDateTime ftpLoadTime;
        /**
         * 测试结束时间
         */
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        @Schema(title = "End Time", example = "格式 yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endTime;
        private String fileSize;
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        @Schema(title = "Update Time", example = "格式 yyyy-MM-dd HH:mm:ss")
        private LocalDateTime updateTime;
    }
}
