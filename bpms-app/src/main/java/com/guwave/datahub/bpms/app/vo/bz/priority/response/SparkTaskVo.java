package com.guwave.datahub.bpms.app.vo.bz.priority.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "Spark任务")
public class SparkTaskVo extends TaskPriority {
    @Schema(description = "应用名称")
    private String appName;

    @Schema(description = "计算类型")
    private String computeType;

    @Schema(description = "Die数量")
    private Long dieCnt;

    @Schema(description = "测试项数量")
    private Long testItemCnt;

    @Schema(description = "队列")
    private String queue;

    @Schema(description = "处理状态")
    private String processStatus;
}
