package com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.response;


import com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.model.FtpStepMappingEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

@Data
@Schema(title = "数据完整性指标")
public class DataIntegrityBaseVo {

    @Schema(description = "文件入库成功率", example = "0.5951")
    private BigDecimal baseIndexValue = BigDecimal.ZERO;
    @Schema(description = "总文件数", example = "100")
    private Long totalCnt = 0L;
    @Schema(description = "已成功入库文件数", example = "50")
    private Long successCnt = 0L;
    @Schema(description = "正在入库文件数", example = "10")
    private Long processCnt = 0L;
    @Schema(description = "入库失败文件数", example = "40")
    private Long failNumCnt = 0L;
    @Schema(description = "量产数据入库过程", example = "40")
    private List<StatisticsDetailDTO> detail = new ArrayList<>();

    public BigDecimal calRatio(Long cnt) {
        if (cnt == null || totalCnt == null || totalCnt == 0L) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(cnt).divide(BigDecimal.valueOf(totalCnt), 4, RoundingMode.HALF_UP);
    }

    @Data
    @NoArgsConstructor
    public static class StatisticsDetailDTO {
        @Schema(description = "描述", example = "FTP_FILE")
        private String indexKey;
        @Schema(description = "文件数", example = "40")
        private Long indexValue = 0L;
        @Schema(description = "比例", example = "0.1234")
        private BigDecimal percent = BigDecimal.ZERO;

        public StatisticsDetailDTO(FtpStepMappingEnum type, DataIntegrityBaseVo dataIntegrityBaseVo, Long indexValue) {
            this.indexKey = type.getCode();
            dataIntegrityBaseVo.detail.add(this);
            if (indexValue == null) {
                return;
            }
            this.indexValue = indexValue;
            this.percent = dataIntegrityBaseVo.calRatio(indexValue);
        }
    }
}
