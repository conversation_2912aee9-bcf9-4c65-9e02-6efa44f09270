package com.guwave.datahub.bpms.app.vo.bz.task.request;

import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * LayerCalculatePoolQueryVo
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-26 15:37:27
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper=false)
@Schema(title = "task DW使用task详细信息查询")
public class DwTaskQueryVo  extends PageableRequestVo {

    @Schema(title = "testAreaList", description = "CP、FT、SLT等")
    private List<String> testAreaList;

    @Schema(title = "factoryList", description = "工厂")
    private List<String> factoryList;

    @Schema(title = "deviceIdList", description = "deviceId")
    private List<String> deviceIdList;

    @Schema(title = "testStageList", description = "testStage")
    private List<String> testStageList;

    @Schema(title = "lotIdList", description = "lotId")
    private List<String> lotIdList;

    @Schema(title = "waferNoList", description = "waferNo")
    private List<String> waferNoList;

    @Schema(title = "lotTypeList", description = "lotType")
    private List<String> lotTypeList;

    @Schema(title = "dwLayerList", description = "dwLayer")
    private List<String> dwLayerList;

    @Schema(title = "processStatusList", description = "计算状态")
    private List<String> processStatusList;

    @Schema(title = "fileCategoryList", description = "文件类型")
    private List<String> fileCategoryList;
}
