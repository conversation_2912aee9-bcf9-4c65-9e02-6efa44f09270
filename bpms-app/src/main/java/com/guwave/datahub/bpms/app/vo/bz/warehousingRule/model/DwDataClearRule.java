package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.guwave.datahub.bpms.app.converter.RuleTypeConverter;
import com.guwave.datahub.bpms.app.converter.StdfFieldTypeConverter;
import com.guwave.datahub.bpms.app.converter.TestAreaConverter;
import com.guwave.onedata.dataware.common.contant.DataClearRuleType;
import com.guwave.onedata.dataware.common.contant.StdfFieldType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DwDataClearRule {
    private String subCustomer;
    @ExcelProperty(converter = TestAreaConverter.class)
    private TestArea testArea;
    private String factory;
    private String factorySite;
    private String deviceId;
    @ExcelProperty(converter = StdfFieldTypeConverter.class)
    private StdfFieldType fieldType;
    @ExcelProperty(converter = RuleTypeConverter.class)
    private DataClearRuleType ruleType;
    private String rule;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DwDataClearRule that = (DwDataClearRule) o;
        return Objects.equals(subCustomer, that.subCustomer) && Objects.equals(testArea, that.testArea) && Objects.equals(factory, that.factory) && Objects.equals(factorySite, that.factorySite) && Objects.equals(deviceId, that.deviceId) && Objects.equals(fieldType, that.fieldType) && Objects.equals(ruleType, that.ruleType) && Objects.equals(rule, that.rule);
    }

    @Override
    public int hashCode() {
        return Objects.hash(subCustomer, testArea, factory, factorySite, deviceId, fieldType, ruleType, rule);
    }
}
