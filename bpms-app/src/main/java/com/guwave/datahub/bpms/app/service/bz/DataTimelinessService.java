package com.guwave.datahub.bpms.app.service.bz;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.util.BigDecimalUtil;
import com.guwave.datahub.bpms.app.vo.bz.dataTimeliness.model.Statistics;
import com.guwave.datahub.bpms.app.vo.bz.dataTimeliness.request.DataTimelinessDropDownFilterVo;
import com.guwave.datahub.bpms.app.vo.bz.dataTimeliness.request.DataTimelinessQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.dataTimeliness.response.DataTimelinessDropDownVo;
import com.guwave.datahub.bpms.app.vo.bz.dataTimeliness.response.DataTimelinessVo;
import com.guwave.datahub.bpms.app.vo.bz.quality.enums.CostTimeEnum;
import com.guwave.datahub.bpms.app.vo.bz.quality.enums.StatisticIndexEnum;
import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.common.constant.DataTimelinessPageDropdownField;
import com.guwave.datahub.bpms.dao.clickhouse.dto.DataTimelinessDTO;
import com.guwave.datahub.bpms.dao.repository.dw.mapper.FileLoadingLogMapper;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.contant.StepType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_FORMAT;

@Slf4j
@Service
public class DataTimelinessService {

    private static final long HOUR_MS_NUM = 3600 * 1000L;
    private final List<TestArea> SUPPORT_TEST_AREA = Stream.concat(TestArea.getCPList().stream(), TestArea.getFTList().stream())
            .collect(Collectors.toList());

    @Autowired
    private DataTimelinessService dataTimelinessService;

    @Autowired
    FileLoadingLogMapper fileLoadingLogMapper;

    public List<DataTimelinessDropDownVo> dataTimelinessFiter(DataTimelinessDropDownFilterVo dataTimelinessDropDownFilterVo) {
        DataTimelinessPageDropdownField filterField = dataTimelinessDropDownFilterVo.getFilterField();

        log.info("获取{}列表", filterField);

        List<String> dropDownResult;

        if (filterField == null) {
            // 抛出异常处理未支持的过滤字段类型
            log.info("不支持的过滤字段：{}", filterField);
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, filterField);
        } else {
            if (DataTimelinessPageDropdownField.TEST_AREA == filterField) {
                // testArea 常量
                dropDownResult = SUPPORT_TEST_AREA.stream().map(TestArea::getArea).collect(Collectors.toList());
            } else {
                boolean isCp = TestArea.getCPList().contains(TestArea.of(dataTimelinessDropDownFilterVo.getTestArea()));
                log.info("查询时效性下拉框结果, isCp:{}", isCp);

                if (isCp) {
                    dropDownResult = fileLoadingLogMapper.findCpDynamicField(
                            RequestContext.getCustomer(),
                            filterField.getField(),
                            dataTimelinessDropDownFilterVo.getSubCustomerList(),
                            dataTimelinessDropDownFilterVo.getFactoryList(),
                            dataTimelinessDropDownFilterVo.getTestArea(),
                            dataTimelinessDropDownFilterVo.getDeviceIdList(),
                            dataTimelinessDropDownFilterVo.getTestProgramList(),
                            dataTimelinessDropDownFilterVo.getTestStageList(),
                            dataTimelinessDropDownFilterVo.getLotIdList(),
                            dataTimelinessDropDownFilterVo.getWaferNoList(),
                            LocalDateTimeUtil.format(dataTimelinessDropDownFilterVo.getStartTime(), NORM_DATETIME_FORMAT.getDateTimeFormatter()),
                            LocalDateTimeUtil.format(dataTimelinessDropDownFilterVo.getEndTime(), NORM_DATETIME_FORMAT.getDateTimeFormatter())
                    );
                } else {
                    dropDownResult = fileLoadingLogMapper.findFtDynamicField(
                            RequestContext.getCustomer(),
                            filterField.getField(),
                            dataTimelinessDropDownFilterVo.getSubCustomerList(),
                            dataTimelinessDropDownFilterVo.getFactoryList(),
                            dataTimelinessDropDownFilterVo.getTestArea(),
                            dataTimelinessDropDownFilterVo.getDeviceIdList(),
                            dataTimelinessDropDownFilterVo.getTestProgramList(),
                            dataTimelinessDropDownFilterVo.getTestStageList(),
                            dataTimelinessDropDownFilterVo.getLotIdList(),
                            dataTimelinessDropDownFilterVo.getSblotIdList(),
                            LocalDateTimeUtil.format(dataTimelinessDropDownFilterVo.getStartTime(), NORM_DATETIME_FORMAT.getDateTimeFormatter()),
                            LocalDateTimeUtil.format(dataTimelinessDropDownFilterVo.getEndTime(), NORM_DATETIME_FORMAT.getDateTimeFormatter())
                    );
                }
            }
        }

        dropDownResult = dropDownResult.stream()
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        log.info("下拉框结果 field:{}, value:{}", filterField, String.join(Constant.COMMA, dropDownResult));
        return DataTimelinessDropDownVo.ofList(dropDownResult, DataTimelinessPageDropdownField.TEST_AREA != filterField);
    }

    /**
     * 数据入库时间验证
     *
     * @param baseDTO {@link DataTimelinessQueryVo}
     * @return InDbTimeLimitDTO
     */
    public DataTimelinessVo dataTimeliness(DataTimelinessQueryVo baseDTO) {
        log.info("数据入库时间验证开始, 参数={}", baseDTO);

        DataTimelinessVo inDbTimeLimitDTO = new DataTimelinessVo();

        // 获取筛选数据
        List<DataTimelinessDTO> fileList = fileLoadingLogMapper.querySuccessFileLogByCond(
                RequestContext.getCustomer(),
                baseDTO.getSubCustomerList(),
                baseDTO.getFactoryList(),
                baseDTO.getDeviceIdList(),
                baseDTO.getTestArea(),
                baseDTO.getTestProgramList(),
                baseDTO.getTestStageList(),
                baseDTO.getLotIdList(),
                baseDTO.getSblotIdList(),
                baseDTO.getWaferNoList(),
                baseDTO.getStartTime(),
                baseDTO.getEndTime(),
                Stream.concat(TestArea.getCPList().stream(), TestArea.getFTList().stream()).map(TestArea::getArea).collect(Collectors.toList())
        );

        // 入库耗时 By Factory
        List<Statistics.StatisticsDetail> factoryData = dataTimelinessService.calculateInDbTimeGroupByFactory(fileList);
        // 入库耗时 By Factory&Tester
        List<Statistics.StatisticsDetail> factoryAndTesterData = dataTimelinessService.calculateInDbTimeGroupByFactoryAndTester(fileList);
        // 数据入库耗时趋势图
        List<Statistics.StatisticsDetail> weekData = dataTimelinessService.calculateInDbTime(fileList).getDetail();
        // 数据入库时间流
        Statistics inDbStream = dataTimelinessService.calculateInDbTimeStream(fileList, StatisticIndexEnum.IN_DB_STREAM.getIndexType(), true);

        postHandleInDbStream(inDbStream);
        // Top 10 耗时最高文件列表
        List<DataTimelinessVo.FileDetail> top10List = dataTimelinessService.calculateInDbTimeTop10(fileList);

        inDbTimeLimitDTO.setGroupByFactoryData(factoryData);
        inDbTimeLimitDTO.setGroupByFactoryAndTester(factoryAndTesterData);
        inDbTimeLimitDTO.setGroupByWeek(weekData);
        inDbTimeLimitDTO.setInDbStream(inDbStream);
        inDbTimeLimitDTO.setTop10Data(top10List);

        log.info("数据入库时间验证结束");
        return inDbTimeLimitDTO;
    }

    /**
     * 入库时间流的赋值处理
     */
    private void postHandleInDbStream(Statistics inDbStream) {
        inDbStream.setCalculateIndexType(StatisticIndexEnum.IN_DB_STREAM);
        if (CollectionUtils.isEmpty(inDbStream.getDetail())) {
            return;
        }
        for (Statistics.StatisticsDetail detailDTO : inDbStream.getDetail()) {
            detailDTO.setIndexKey(detailDTO.getColorByKey());
        }
    }


    /**
     * 计算inDbTime
     *
     * @param list 数据
     * @return 计算结果
     */
    public Statistics calculateInDbTime(List<DataTimelinessDTO> list) {
        log.info("开始计算入库时间");

        Map<String, List<DataTimelinessDTO>> fileNameMap = list.stream().collect(Collectors.groupingBy(DataTimelinessDTO::getFileName));

        // 1 数据按周分组 计算组内的文件数

        // 1.1 按周分组 7100
        Map<String, List<DataTimelinessDTO>> weekMap = fileDataGroupBy(list);
        // 1.2 按文件名分组 1000
        Map<String, DataTimelinessDTO> step1000fileMap = list.stream()
                .filter(v -> StepType.STEP_TYPE_1000.getStep() == v.getStep() && ProcessStatus.SUCCESS.equals(v.getProcessStatus()))
                .collect(Collectors.toMap(DataTimelinessDTO::getFileName, Function.identity()));

        // 2 计算每周的文件入库耗时平均值
        List<Statistics> dtoList = weekMap.entrySet()
                .stream()
                .map(weekEntry -> {
                    // 2.1 按文件名分组 每个文件只有一个step=7100的数据
                    return getStatisticsDTO(step1000fileMap, weekEntry.getKey(), weekEntry.getValue(), fileNameMap);
                })
                .collect(Collectors.toList());

        //计算整体平均值
        int total = dtoList.size();
        BigDecimal avgTime = BigDecimal.ZERO;
        if (total != 0) {
            avgTime = dtoList.stream().map(Statistics::getBaseIndexValue).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(total), 2, RoundingMode.HALF_UP);
        }

        //重新排序数据
        List<Statistics.StatisticsDetail> inDbValues = dtoList.stream()
                .flatMap(v -> v.getDetail().stream())
                .sorted((v1, v2) -> {
                    int i = v1.getIndexKey().compareTo(v2.getIndexKey());
                    if (i == 0) {
                        return v1.getColorByKey().compareTo(v2.getColorByKey());
                    }
                    return i;
                })
                .collect(Collectors.toList());

        //组装返回数据
        Statistics statistics = Statistics.of(StatisticIndexEnum.IN_DB_TIME, avgTime, inDbValues);

        log.info("计算入库耗时平均值完成:{}", statistics);

        return statistics;
    }

    /**
     * 得到统计dto
     *
     * @param step1000fileMap step1000file数据map
     * @param key             关键
     * @param value           价值
     * @return {@link Statistics}
     */
    private static Statistics getStatisticsDTO(Map<String, DataTimelinessDTO> step1000fileMap, String key, List<DataTimelinessDTO> value, Map<String, List<DataTimelinessDTO>> fileNameMap) {
        Map<String, DataTimelinessDTO> fileMap = value.stream().collect(Collectors.toMap(DataTimelinessDTO::getFileName, Function.identity(), (v1, v2) -> v1));


        // 2.2 计算每个文件的入库时间
        long fileTransferMs = 0;
        long fileReceiveMs = 0;
        long fileInDbMs = 0;

        for (Map.Entry<String, DataTimelinessDTO> entry : fileMap.entrySet()) {
            DataTimelinessDTO step7100Data = entry.getValue();

            DataTimelinessDTO step1000Data = step1000fileMap.get(entry.getKey());
            if (step1000Data == null) {
                log.error("文件名:{} 没有step=1000的数据", entry.getKey());
            } else {
                fileTransferMs += DateUtil.betweenMs(step1000Data.getRemoteFileMtime(), step7100Data.getFinishT());
                fileReceiveMs += DateUtil.betweenMs(step1000Data.getStepEndTime(), step1000Data.getRemoteFileMtime());
                // fileInDbMs += DateUtil.betweenMs(step7100Data.getStepEndTime(), step7100Data.getMaxFirstStepEndTime());
                fileInDbMs += accumulateStepTime(fileNameMap.get(entry.getKey()));
            }
        }


        // 2.3 计算每周的入库avg时间
        List<Statistics.StatisticsDetail> details = new ArrayList<>();
        long fileCount = fileMap.size();
        double weekTransferHour = fileCount == 0 ? 0 : NumberUtil.div(fileTransferMs, fileCount * HOUR_MS_NUM, 2);
        Statistics.StatisticsDetail transferDetail = Statistics.StatisticsDetail.of(key, BigDecimal.valueOf(weekTransferHour), CostTimeEnum.FILE_TRANSFER.getType());
        details.add(transferDetail);

        double weekReceiveHour = fileCount == 0 ? 0 : NumberUtil.div(fileReceiveMs, fileCount * HOUR_MS_NUM, 2);
        Statistics.StatisticsDetail receiveDetail = Statistics.StatisticsDetail.of(key, BigDecimal.valueOf(weekReceiveHour), CostTimeEnum.FILE_RECEIVE.getType());
        details.add(receiveDetail);

        double weekInDbHour = fileCount == 0 ? 0 : NumberUtil.div(fileInDbMs, fileCount * HOUR_MS_NUM, 2);
        Statistics.StatisticsDetail inDbDetail = Statistics.StatisticsDetail.of(key, BigDecimal.valueOf(weekInDbHour), CostTimeEnum.FILE_PARSE.getType());
        details.add(inDbDetail);

        double totalHour = weekTransferHour + weekReceiveHour + weekInDbHour;

        return Statistics.of(StatisticIndexEnum.IN_DB_TIME, BigDecimal.valueOf(totalHour), details);
    }

    /**
     * 计算耗时根据factory分组
     *
     * @param list 列表
     * @return {@link List}<{@link Statistics.StatisticsDetail}>
     */
    public List<Statistics.StatisticsDetail> calculateInDbTimeGroupByFactory(List<DataTimelinessDTO> list) {
        return list.stream().collect(Collectors.groupingBy(DataTimelinessDTO::getFactory))
                .entrySet()
                .stream()
                .map(v -> calculateInDbTimeStream(v.getValue(), v.getKey(), false))
                .flatMap(v -> v.getDetail().stream())
                .collect(Collectors.toList());
    }

    /**
     * 计算耗时根据factory哈tester分组处理
     *
     * @param list 列表
     * @return {@link List}<{@link Statistics.StatisticsDetail}>
     */
    public List<Statistics.StatisticsDetail> calculateInDbTimeGroupByFactoryAndTester(List<DataTimelinessDTO> list) {
        return list.stream().collect(Collectors.groupingBy(v -> v.getFactory() + "_" + transferTesterName4Null(v.getTesterName())))
                .entrySet()
                .stream()
                .map(v -> calculateInDbTimeStream(v.getValue(), v.getKey(), false))
                .flatMap(v -> v.getDetail().stream())
                .collect(Collectors.toList());
    }

    private String transferTesterName(String testerName) {
        if (Objects.isNull(testerName)) {
            return "Null";
        }
        return testerName;
    }

    private String transferTesterName4Null(String testerName) {
        if (Objects.isNull(testerName)) {
            return "";
        }
        return testerName;
    }

    /**
     * 计算top10耗时的文件数据
     *
     * @param list 列表
     * @return {@link List}<{@link DataTimelinessVo.FileDetail}>
     */
    public List<DataTimelinessVo.FileDetail> calculateInDbTimeTop10(List<DataTimelinessDTO> list) {
        return list.stream().collect(Collectors.groupingBy(DataTimelinessDTO::getFileName))
                .entrySet()
                .stream()
                .sorted((v1, v2) -> {
                    DataTimelinessDTO step7100Data1 = v1.getValue().stream().filter(v -> v.getStep() == StepType.STEP_TYPE_7100.getStep()).findFirst().orElse(null);
                    DataTimelinessDTO step7100Data2 = v2.getValue().stream().filter(v -> v.getStep() == StepType.STEP_TYPE_7100.getStep()).findFirst().orElse(null);
                    if (step7100Data1 == null || step7100Data2 == null) {
                        return 0;
                    }
                    return getTop10SortTime(step7100Data2).compareTo(getTop10SortTime(step7100Data1));
                }).limit(10)
                .map(v -> {
                    DataTimelinessDTO step7100Data = v.getValue().stream().filter(v1 -> v1.getStep() == StepType.STEP_TYPE_7100.getStep()).findFirst().orElse(null);
                    assert step7100Data != null : "当前完成数据不能为空";

                    DataTimelinessVo.FileDetail detailDTO = new DataTimelinessVo.FileDetail();
                    detailDTO.setFactory(step7100Data.getFactory());
                    detailDTO.setTester(transferTesterName(step7100Data.getTesterName()));
                    detailDTO.setFileName(step7100Data.getFileName());
                    detailDTO.setFileLocation(step7100Data.getHdfsPath());
                    detailDTO.setFtpLoadTime(DateUtil.toLocalDateTime(step7100Data.getRemoteFileMtime()));
                    detailDTO.setCostTime(BigDecimalUtil.toBigDecimal(NumberUtil.div((float) getTop10SortTime(step7100Data), HOUR_MS_NUM, 2)));
                    detailDTO.setEndTime(DateUtil.toLocalDateTime(step7100Data.getFinishT()));
                    detailDTO.setFileSize(String.format("%s MB", BigDecimal.valueOf(NumberUtil.div((float) step7100Data.getOriginFileSize(), (1024 * 1024), 2))));
                    detailDTO.setUpdateTime(DateUtil.toLocalDateTime(step7100Data.getUpdateTime()));

                    return detailDTO;
                }).collect(Collectors.toList());

    }

    private Long getTop10SortTime(DataTimelinessDTO fileLoadingLog) {
        // 毫秒
        return fileLoadingLog.getStepEndTime().getTime() - fileLoadingLog.getFinishT().getTime();
    }


    /**
     * 计算数据入库时间流
     *
     * @param list 列表
     * @return {@link Statistics}
     */
    public Statistics calculateInDbTimeStream(List<DataTimelinessDTO> list, String indexKey, boolean isNeedCalculatePercent) {
        Map<String, List<DataTimelinessDTO>> map = list.stream().collect(Collectors.groupingBy(DataTimelinessDTO::getFileName));

        long fileTransferMs = 0;
        long fileReceiveMs = 0;
        long fileInDbMs = 0;
        for (Map.Entry<String, List<DataTimelinessDTO>> entry : map.entrySet()) {

            List<DataTimelinessDTO> value = entry.getValue();
            DataTimelinessDTO step1000Data = value.stream().filter(o -> o.getStep() == StepType.STEP_TYPE_1000.getStep()).findFirst().orElse(null);
            DataTimelinessDTO step7100Data = value.stream().filter(o -> o.getStep() == StepType.STEP_TYPE_7100.getStep()).findFirst().orElse(null);
            if (step1000Data == null || step7100Data == null) {
                log.error("文件名:{} 没有step=1000和step=7100的数据", entry.getKey());
            } else {
                fileTransferMs += DateUtil.betweenMs(step1000Data.getRemoteFileMtime(), step7100Data.getFinishT());
                fileReceiveMs += DateUtil.betweenMs(step1000Data.getStepEndTime(), step1000Data.getRemoteFileMtime());
                // 解析入库时间：逻辑 1000step-endTime – 1000step–startTime + 3100.. +..7100step-endTime – 7100step–startTime
                fileInDbMs += accumulateStepTime(value);
            }
        }

        List<Statistics.StatisticsDetail> details = new ArrayList<>();
        long fileCount = map.size();
        log.info("文件总数:{}", fileCount);

        double transferHour = 0;
        double receiveHour = 0;
        double inDbHour = 0;
        if (fileCount != 0) {
            transferHour = NumberUtil.div(fileTransferMs, fileCount * HOUR_MS_NUM, 2);
            receiveHour = NumberUtil.div(fileReceiveMs, fileCount * HOUR_MS_NUM, 2);
            inDbHour = NumberUtil.div(fileInDbMs, fileCount * HOUR_MS_NUM, 2);
        }
        log.info("文件传输时间:{} 小时, 文件接收时间:{} 小时, 文件入库时间:{} 小时", transferHour, receiveHour, inDbHour);

        double totalHour = transferHour + receiveHour + inDbHour;

        // 计算百分比
        BigDecimal transferHourPercent = null;
        BigDecimal receiveHourPercent = null;
        BigDecimal inDbHourPercent = null;
        if (isNeedCalculatePercent) {
            if (totalHour == 0) {
                transferHourPercent = BigDecimal.ZERO;
                receiveHourPercent = BigDecimal.ZERO;
                inDbHourPercent = BigDecimal.ZERO;
            } else {
                transferHourPercent = BigDecimalUtil.toBigDecimal(NumberUtil.div(transferHour, totalHour, 4));
                receiveHourPercent = BigDecimalUtil.toBigDecimal(NumberUtil.div(receiveHour, totalHour, 4));
                inDbHourPercent = BigDecimalUtil.toBigDecimal(NumberUtil.div(inDbHour, totalHour, 4));
            }
        }

        Statistics.StatisticsDetail transferDetail = Statistics.StatisticsDetail.of(indexKey, BigDecimal.valueOf(transferHour), CostTimeEnum.FILE_TRANSFER.getType(), transferHourPercent);
        details.add(transferDetail);

        Statistics.StatisticsDetail receiveDetail = Statistics.StatisticsDetail.of(indexKey, BigDecimal.valueOf(receiveHour), CostTimeEnum.FILE_RECEIVE.getType(), receiveHourPercent);
        details.add(receiveDetail);

        Statistics.StatisticsDetail inDbDetail = Statistics.StatisticsDetail.of(indexKey, BigDecimal.valueOf(inDbHour), CostTimeEnum.FILE_PARSE.getType(), inDbHourPercent);
        details.add(inDbDetail);

        return Statistics.of(StatisticIndexEnum.IN_DB_TIME, BigDecimalUtil.toBigDecimal(NumberUtil.div(totalHour, 1.0, 2)), details);
    }

    /**
     * 将step 1000至7100 endTime - startTime 累加
     */
    private static long accumulateStepTime(List<DataTimelinessDTO> value) {
        if (CollectionUtils.isEmpty(value)) {
            return 0;
        }

        String fileName = value.get(0).getFileName();
        long totalStepTimes = 0;
        List<StepType> stepEnums = Arrays.stream(StepType.values()).collect(Collectors.toList());
        for (StepType step : stepEnums) {
            DataTimelinessDTO stepData = value.stream().filter(s -> s.getStep() == step.getStep()).findFirst().orElse(null);
            if (Objects.isNull(stepData)) {
                log.debug("文件{} step不存在:{}", fileName, step);
                continue;
            }
            Date stepStartTime = stepData.getStepStartTime();
            Date stepEndTime = stepData.getStepEndTime();
            if (Objects.isNull(stepStartTime) || Objects.isNull(stepEndTime)) {
                log.debug("文件{} 时间为空, stepStartTime:{}, stepEndTime:{}", fileName, stepStartTime, stepEndTime);
                continue;
            }
            totalStepTimes += DateUtil.betweenMs(stepEndTime, stepStartTime);
        }

        return totalStepTimes;
    }

    /**
     * 数据按周分组
     *
     * @param fileLoadingLogList 统计数据
     * @return Map
     */
    public Map<String, List<DataTimelinessDTO>> fileDataGroupBy(List<DataTimelinessDTO> fileLoadingLogList) {
        if (CollectionUtils.isEmpty(fileLoadingLogList)) {
            return Collections.emptyMap();
        }
        SimpleDateFormat weekFormat = new SimpleDateFormat("yyyy_ww");
        // 按周分组
        return fileLoadingLogList.stream()
                .filter(v -> StepType.STEP_TYPE_7100.getStep() == v.getStep() && ProcessStatus.SUCCESS.equals(v.getProcessStatus()))
                .collect(Collectors.groupingBy(fileLoadingLog ->
                        weekFormat.format(fileLoadingLog.getStepEndTime())
                ));
    }
}
