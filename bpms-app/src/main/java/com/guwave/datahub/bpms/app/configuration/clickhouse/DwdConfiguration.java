package com.guwave.datahub.bpms.app.configuration.clickhouse;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManager;
import javax.sql.DataSource;
import java.util.Objects;

@Slf4j
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "entityManagerFactoryDwd",
        transactionManagerRef = "transactionManagerDwd",
        basePackages = {"com.guwave.datahub.bpms.dao.repository.clickhouse.dwd"})
@MapperScan(
        basePackages = "com.guwave.datahub.bpms.dao.repository.clickhouse.dwd.mapper",
        sqlSessionFactoryRef = "dwdSqlSessionFactory"
)
public class DwdConfiguration {

    @Autowired
    JpaClickhouseConfiguration jpaClickhouseConfiguration;

    @Value("${spring.ck.dwd.database}")
    private String database;

    @Bean(name = "entityManagerDwd")
    public EntityManager entityManager() {
        return Objects.requireNonNull(entityManagerFactoryDwd().getObject()).createEntityManager();
    }

    @Bean(name = "entityManagerFactoryDwd")
    public LocalContainerEntityManagerFactoryBean entityManagerFactoryDwd() {
        return jpaClickhouseConfiguration.getEntityManagerFactory(
                database,
                "dwdPersistenceUnit",
                "com.guwave.datahub.bpms.dao.clickhouse.domain.dwd");
    }

    @Bean(name = "transactionManagerDwd")
    public PlatformTransactionManager transactionManagerDwd() {
        return new JpaTransactionManager(Objects.requireNonNull(entityManagerFactoryDwd().getObject()));
    }

    @Bean
    public DataSource dwdDataSource() {
        return entityManagerFactoryDwd().getDataSource();
    }


    // 配置MyBatis
    @Bean(name = "dwdSqlSessionFactory")
    public SqlSessionFactory DwdSqlSessionFactory() throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dwdDataSource());
        sessionFactory.setMapperLocations(
                ArrayUtils.addAll(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/clickhouse/dwd/*.xml"),
                        new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml"))
        );
        return sessionFactory.getObject();

    }

    @Bean(name = "dwdMybatisTransactionManager")
    public PlatformTransactionManager DwdMybatisTransactionManager() {
        return new DataSourceTransactionManager(dwdDataSource());
    }
}
