//package com.guwave.datahub.bpms.app.configuration.clickhouse;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.ArrayUtils;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.SqlSessionFactoryBean;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//import org.springframework.orm.jpa.JpaTransactionManager;
//import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
//import org.springframework.transaction.PlatformTransactionManager;
//import org.springframework.transaction.annotation.EnableTransactionManagement;
//
//import javax.persistence.EntityManager;
//import javax.sql.DataSource;
//import java.util.Objects;
//
//
//@Slf4j
//@Configuration
//@EnableTransactionManagement
//@EnableJpaRepositories(entityManagerFactoryRef = "entityManagerFactoryOds",
//        transactionManagerRef = "transactionManagerOds",
//        basePackages = {"com.guwave.datahub.bpms.dao.repository.clickhouse.ods"})
//@MapperScan(
//        basePackages = "com.guwave.datahub.bpms.dao.repository.clickhouse.ods.mapper",
//        sqlSessionFactoryRef = "odsSqlSessionFactory"
//)
//public class OdsConfiguration {
//
//    @Autowired
//    JpaClickhouseConfiguration jpaClickhouseConfiguration;
//
//    @Value("${spring.ck.ods.database}")
//    private String database;
//
//    @Bean(name = "entityManagerOds")
//    public EntityManager entityManager() {
//        return Objects.requireNonNull(entityManagerFactoryOds().getObject()).createEntityManager();
//    }
//
//    @Bean(name = "entityManagerFactoryOds")
//    public LocalContainerEntityManagerFactoryBean entityManagerFactoryOds() {
//        return jpaClickhouseConfiguration.getEntityManagerFactory(
//                database,
//                "odsPersistenceUnit",
//                "com.guwave.datahub.bpms.dao.clickhouse.domain.ods");
//    }
//
//    @Bean(name = "transactionManagerOds")
//    public PlatformTransactionManager transactionManagerOds() {
//        return new JpaTransactionManager(Objects.requireNonNull(entityManagerFactoryOds().getObject()));
//    }
//
//
//    @Bean
//    public DataSource odsDataSource() {
//        return entityManagerFactoryOds().getDataSource();
//    }
//
//
//    // 配置MyBatis
//    @Bean(name = "odsSqlSessionFactory")
//    public SqlSessionFactory OdsSqlSessionFactory() throws Exception {
//        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
//        sessionFactory.setDataSource(odsDataSource());
//        sessionFactory.setMapperLocations(
//                ArrayUtils.addAll(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/clickhouse/ods/*.xml"),
//                        new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml"))
//        );
//        return sessionFactory.getObject();
//
//    }
//
//    @Bean(name = "odsMybatisTransactionManager")
//    public PlatformTransactionManager OdsMybatisTransactionManager() {
//        return new DataSourceTransactionManager(odsDataSource());
//    }
//}
