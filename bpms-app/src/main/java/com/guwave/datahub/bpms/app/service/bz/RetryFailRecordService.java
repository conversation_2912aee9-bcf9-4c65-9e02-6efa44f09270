package com.guwave.datahub.bpms.app.service.bz;

import com.guwave.datahub.bpms.app.util.MultipleDataSourceUtil;
import com.guwave.datahub.bpms.app.vo.bz.retryFailRecord.BusinessRecord;
import com.guwave.datahub.bpms.dao.domain.retryFailRecord.BusinessFailRecord;
import com.guwave.datahub.bpms.dao.domain.retryFailRecord.BusinessFailRecordQueryCondition;
import com.guwave.datahub.bpms.dao.repository.bpms.BusinessFailRecordQueryConditionRepository;
import com.guwave.datahub.bpms.dao.repository.bpms.BusinessFailRecordRepository;
import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Service
public class RetryFailRecordService {

    public static final String QUERY_CONDITION = "{QUERY_CONDITION}";
    public static final String IDS = "{IDS}";
    public static final String MYSQL_QUERY_IDS_SQL_TEMPLATE = "select id from {TABLE} where process_status = 'FAIL' and update_time between DATE_SUB(NOW(), INTERVAL 1 DAY) and NOW() and {QUERY_CONDITION}";
    public static final String MYSQL_QUERY_STATUS_SQL_TEMPLATE = "select id,process_status from {TABLE} where id in ({IDS})";
    public static final String MYSQL_RETRY_UPDATE_SQL_TEMPLATE = "update {TABLE} set process_status = 'CREATE' where process_status = 'FAIL' and id in ({IDS})";

    private static final Integer FAST_RETRY_MAX_CNT = 10;
    private static final Integer SLOW_RETRY_MAX_CNT = 15;

    @Autowired
    private BusinessFailRecordQueryConditionRepository businessFailRecordQueryConditionRepository;
    @Autowired
    private BusinessFailRecordRepository businessFailRecordRepository;
    @Autowired
    private MultipleDataSourceUtil multipleDataSourceUtil;

    synchronized public void saveFailRecords() {
        ArrayList<BusinessFailRecord> businessFailRecords = new ArrayList<>();
        businessFailRecordQueryConditionRepository.findAll().forEach(t -> {
            try {
                businessFailRecords.addAll(queryTableFailRecords(t));
            } catch (Exception e) {
                log.info("执行异常：", e);
            }
        });
        businessFailRecordRepository.saveAll(businessFailRecords);
    }

    private List<BusinessFailRecord> queryTableFailRecords(BusinessFailRecordQueryCondition tableCondition) {
        if (StringUtils.isBlank(tableCondition.getQueryCondition())) {
            return new ArrayList<>();
        }
        String querySql = MYSQL_QUERY_IDS_SQL_TEMPLATE.replace(Constant.TABLE, tableCondition.getTable()).replace(QUERY_CONDITION, tableCondition.getQueryCondition());
        log.info("开始执行查询：{}", querySql);
        EntityManager entityManager = multipleDataSourceUtil.getEntityManager(tableCondition.getDb());
        List<?> resultList = entityManager.createNativeQuery(querySql).getResultList();
        log.info("查询到 {} 条记录", resultList.size());
        if (CollectionUtils.isEmpty(resultList)) {
            return new ArrayList<>();
        }
        List<Long> recordIds = resultList.stream().map(res -> ((BigInteger) res).longValue()).collect(Collectors.toList());
        List<BusinessFailRecord> existsFailRecords = businessFailRecordRepository.findAllByRecordIdInAndTableAndDb(recordIds, tableCondition.getTable(), tableCondition.getDb());
        Map<Long, BusinessFailRecord> existsFailRecordMap = existsFailRecords.stream().collect(Collectors.toMap(BusinessFailRecord::getRecordId, t -> t, (v1, v2) -> v1));
        Date updateTime = new Date();

        return recordIds.stream().map(t -> {
            BusinessFailRecord existsFailRecord = existsFailRecordMap.get(t);
            if (existsFailRecord != null) {
                existsFailRecord.setRecordProcessStatus(ProcessStatus.FAIL);
                existsFailRecord.setUpdateTime(updateTime);
                return existsFailRecord;
            } else {
                return new BusinessFailRecord(null, tableCondition.getDb(), tableCondition.getTable(), t, 0, ProcessStatus.FAIL, updateTime, updateTime, Constant.SYSTEM, Constant.SYSTEM);
            }
        }).collect(Collectors.toList());
    }

    synchronized public void allRetryFailRecords() {
        List<BusinessFailRecord> businessFailRecords = businessFailRecordRepository.findAllByRecordProcessStatusAndRecordRetryCntBetween(ProcessStatus.FAIL, 0, Integer.MAX_VALUE);
        retryFailRecords(businessFailRecords);
    }


    @Scheduled(cron = "${spring.scheduler.timer.fastRetryFailRecordTask}")
    @ScheduleSwitch
    public void fastRetryJob() {
        log.info("fastRetryJob 开始");
        try {
            saveFailRecords();
            fastRetryFailRecords();
        } catch (Exception e) {
            log.info("fastRetryJob 异常：", e);
        }
    }

    @Scheduled(cron = "${spring.scheduler.timer.slowRetryFailRecordTask}")
    @ScheduleSwitch
    public void slowRetryJob() {
        log.info("slowRetryJob 开始");
        try {
            slowRetryFailRecords();
        } catch (Exception e) {
            log.info("slowRetryJob 异常：", e);
        }
    }

    synchronized public void fastRetryFailRecords() {
        List<BusinessFailRecord> businessFailRecords = businessFailRecordRepository.findAllByRecordProcessStatusAndRecordRetryCntBetween(ProcessStatus.FAIL, 0, FAST_RETRY_MAX_CNT - 1);
        retryFailRecords(businessFailRecords);
    }

    synchronized public void slowRetryFailRecords() {
        List<BusinessFailRecord> businessFailRecords = businessFailRecordRepository.findAllByRecordProcessStatusAndRecordRetryCntBetween(ProcessStatus.FAIL, FAST_RETRY_MAX_CNT, SLOW_RETRY_MAX_CNT - 1);
        retryFailRecords(businessFailRecords);
    }

    private void retryFailRecords(List<BusinessFailRecord> businessFailRecords) {
        businessFailRecords.stream().collect(Collectors.groupingBy(t -> Pair.create(t.getDb(), t.getTable())))
                .forEach((dbAndTable, records) -> {
                    try {
                        retryTableRecords(dbAndTable, records);
                    } catch (Exception e) {
                        log.info("执行异常：", e);
                    }
                });
    }

    private void retryTableRecords(Pair<String, String> dbAndTable, List<BusinessFailRecord> records) {
        EntityManager entityManager = multipleDataSourceUtil.getEntityManager(dbAndTable.getFirst());
        String recordIds = records.stream().map(t -> t.getRecordId() + "").collect(Collectors.joining(Constant.COMMA));
        String queryProcessStatusSql = MYSQL_QUERY_STATUS_SQL_TEMPLATE.replace(Constant.TABLE, dbAndTable.getSecond()).replace(IDS, recordIds);
        log.info("开始执行查询：{}", queryProcessStatusSql);
        List<Object[]> resultList = entityManager.createNativeQuery(queryProcessStatusSql).getResultList();
        log.info("查询到 {} 条记录", resultList.size());
        Map<Long, BusinessRecord> currentBusinessRecordMap = resultList.stream().map(t -> new BusinessRecord(((BigInteger) t[0]).longValue(), ProcessStatus.of((String) t[1])))
                .collect(Collectors.toMap(BusinessRecord::getId, t -> t, (v1, v2) -> v1));
        Date date = new Date();

        ArrayList<BusinessFailRecord> deleteRecords = new ArrayList<>();
        ArrayList<BusinessFailRecord> updateRecords = new ArrayList<>();
        ArrayList<Long> retryIds = new ArrayList<>();
        records.forEach(record -> {
            record.setUpdateTime(date);
            BusinessRecord businessRecord = currentBusinessRecordMap.get(record.getRecordId());
            if (businessRecord == null) {
                deleteRecords.add(record);
            } else if (businessRecord.getProcessStatus() != ProcessStatus.FAIL) {
                record.setRecordProcessStatus(businessRecord.getProcessStatus());
                updateRecords.add(record);
            } else {
                record.setRecordProcessStatus(ProcessStatus.CREATE);
                record.setRecordRetryCnt(record.getRecordRetryCnt() + 1);
                updateRecords.add(record);
                retryIds.add(record.getRecordId());
            }
        });

        if (CollectionUtils.isNotEmpty(retryIds)) {
            String retrySql = MYSQL_RETRY_UPDATE_SQL_TEMPLATE.replace(Constant.TABLE, dbAndTable.getSecond()).replace(IDS, retryIds.stream().map(t -> t + "").collect(Collectors.joining(Constant.COMMA)));
            log.info("开始执行更新：{}", retrySql);

            multipleDataSourceUtil.executeUpdate(dbAndTable.getFirst(), manager -> {
                int updateCnt = manager.createNativeQuery(retrySql).executeUpdate();
                log.info("更新 {} 条记录", updateCnt);
            });
        }

        businessFailRecordRepository.saveAll(updateRecords);
        businessFailRecordRepository.deleteAll(deleteRecords);
    }
}
