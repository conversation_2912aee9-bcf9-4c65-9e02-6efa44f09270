package com.guwave.datahub.bpms.app.controller;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.service.bz.DailySummaryService;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Log
@Auth
//@License
@Validated
@Slf4j
@RestController
@AllArgsConstructor
@Tag(name = "DailySummaryControllerAPI", description = "日报接口",
        externalDocs = @ExternalDocumentation(description = "需求文档",
                url = "")
)
@RequestMapping("dailySummary")
public class DailySummaryController {

    private DailySummaryService dailySummaryService;

    @Auth
    @Operation(summary = "生成日报数据")
    @PostMapping("generateDailySummary")
    public ResponseVo<Void> generateDailySummary() {
        dailySummaryService.generateDailySummary();
        return ResponseVo.ok();
    }

    @Auth
    @Operation(summary = "发送日报数据")
    @PostMapping("sendDailySummary")
    public ResponseVo<Void> sendDailySummary() {
        dailySummaryService.sendDailySummary();
        return ResponseVo.ok();
    }

}
