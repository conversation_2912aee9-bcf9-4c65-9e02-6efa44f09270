package com.guwave.datahub.bpms.app.job;

import com.guwave.datahub.bpms.app.service.bz.PullFailMessageService;
import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PullFailMessageJob {

    @Autowired
    private PullFailMessageService pullFailMessageService;

    @ScheduleSwitch
    @Scheduled(fixedDelayString = "${spring.scheduler.message.retry.millisecond}")
    public void pullFailMessage() {
        try {
            pullFailMessageService.pullDatawareFailMessage();
            pullFailMessageService.pullLinkxFailMessage();
            pullFailMessageService.pullNextComputeFailMessage();
        } catch (Exception e) {
            log.error(e.toString(), e);
        }
    }
}
