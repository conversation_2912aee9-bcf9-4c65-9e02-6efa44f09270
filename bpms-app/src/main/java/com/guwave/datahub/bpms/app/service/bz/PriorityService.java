package com.guwave.datahub.bpms.app.service.bz;

import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.vo.bz.priority.request.*;
import com.guwave.datahub.bpms.app.vo.bz.priority.response.AutoTaskResultVo;
import com.guwave.datahub.bpms.app.vo.bz.priority.response.ManualUploadTaskVo;
import com.guwave.datahub.bpms.app.vo.bz.priority.response.PriorityConfigVo;
import com.guwave.datahub.bpms.app.vo.bz.priority.response.SparkTaskVo;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.dao.projection.RankedComputePool;
import com.guwave.datahub.bpms.dao.projection.RankedLotMetaDataDetail;
import com.guwave.datahub.bpms.dao.projection.RankedManualCalculateTask;
import com.guwave.datahub.bpms.dao.repository.compute.ComputePoolRepository;
import com.guwave.datahub.bpms.dao.repository.dw.LotMetaDataDetailRepository;
import com.guwave.datahub.bpms.dao.repository.dw.ManualCalculateTaskRepository;
import com.guwave.datahub.bpms.dao.repository.dw.TaskPriorityConfigRepository;
import com.guwave.datahub.bpms.dao.repository.linkx.BzAppInstanceRepository;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.common.model.meta.CalculateDimensionKey;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.DwTaskPriorityConfig;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotMetaDataDetail;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualCalculateTask;
import com.guwave.onedata.dataware.dao.mysql.manager.TaskPriorityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.guwave.datahub.bpms.app.web.response.ResponseCode.Business.PRIORITY_CONFIG_ALREADY_EXISTS;
import static com.guwave.datahub.bpms.app.web.response.ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT;

@Slf4j
@Service
@RequiredArgsConstructor
public class PriorityService {

    @Autowired
    private TaskPriorityConfigRepository taskPriorityConfigRepository;

    @Autowired
    private LotMetaDataDetailRepository lotMetaDataDetailRepository;

    @Autowired
    private ManualCalculateTaskRepository manualCalculateTaskRepository;

    @Autowired
    private ComputePoolRepository computePoolRepository;

    @Autowired
    private BzAppInstanceRepository bzAppInstanceRepository;

    @Autowired
    private TaskPriorityManager taskPriorityManager;

    static final String DEFAULT = "default";
    static final String DEFERRED = "deferred";

    public PageableDataVo<AutoTaskResultVo> getAutoTasks(AutoTaskQueryVo request) {
        Page<RankedLotMetaDataDetail> taskPage = lotMetaDataDetailRepository.findAutoTasksByConditions(
                request.getTestArea(),
                request.getFactory(),
                request.getDeviceId(),
                request.getFileCategory(),
                request.getLotId(),
                request.getWaferNo(),
                request.getLotType(),
                request.toPageable()
        );

        return PageableDataVo.of(taskPage.map(this::convertToAutoTaskResultVo));
    }

    public PageableDataVo<ManualUploadTaskVo> getManualUploadTasks(ManualUploadQueryVo request) {
        log.info("查询手动上传任务列表");

        Page<RankedManualCalculateTask> taskPage = manualCalculateTaskRepository.findByFileName(
                request.getFileName(),
                request.toPageable()
        );

        return PageableDataVo.of(taskPage.map(this::convertToManualUploadTaskVo));
    }

    public PageableDataVo<SparkTaskVo> getSparkTasks(SparkTaskQueryVo request) {
        log.info("查询Spark任务列表");

        Page<RankedComputePool> taskPage = computePoolRepository.findByConditions(
                request.getAppName(),
                request.getQueue(),
                request.getComputeType(),
                request.getProcessStatus(),
                request.toPageable()
        );

        return PageableDataVo.of(taskPage.map(this::convertToSparkTaskVo));
    }

    @Transactional
    public void updatePriority(TaskPriorityUpdateVo request) {
        log.info("更新任务优先级");
        Integer priority = parsePriority(request.getPriority());
        switch (request.getTaskType()) {
            case "AUTO":
                updateAutoTasksPriority(request.getTaskIds(), priority);
                break;
            case "MANUAL":
                updateManualUploadTasksPriority(request.getTaskIds(), priority);
                break;
            case "SPARK":
                updateSparkTasksPriority(request.getTaskIds(), priority);
                break;
            default:
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, request.getTaskType());
        }
    }

    private void updateAutoTasksPriority(List<Long> taskIds, Integer priority) {
        // 通过最小id查询对应维度的所有数据
        List<LotMetaDataDetail> tasks = lotMetaDataDetailRepository.findAllRelatedByIds(taskIds);

        lotMetaDataDetailRepository.updatePriorityByIds(
                tasks.stream().map(LotMetaDataDetail::getId).collect(Collectors.toList()),
                priority,
                RequestContext.getUserName()
        );

        log.info("更新自动导入任务优先级成功, taskIds: {}, priority: {}", taskIds, priority);
    }

    private void updateManualUploadTasksPriority(List<Long> taskIds, Integer priority) {
        // 获取所有相关 file_id 的任务记录
        List<Long> allTaskIds = manualCalculateTaskRepository.findAllByFileIds(taskIds)
                .stream().map(ManualCalculateTask::getId).collect(Collectors.toList());

        manualCalculateTaskRepository.updatePriorityByIds(
                allTaskIds,
                priority,
                RequestContext.getUserName()
        );

        log.info("更新手动上传任务优先级成功, taskIds: {}, 实际更新记录数: {}, priority: {}",
                taskIds, allTaskIds.size(), priority);
    }

    private void updateSparkTasksPriority(List<Long> taskIds, Integer priority) {

        computePoolRepository.updatePriorityByIds(
                taskIds,
                // priority_group = -priority
                -priority,
                RequestContext.getUserName()
        );

        log.info("更新Spark任务优先级成功, taskIds: {}, priority: {}", taskIds, priority);
    }

    public PageableDataVo<PriorityConfigVo> getConfigs(PriorityConfigQueryVo request) {

        Page<DwTaskPriorityConfig> configPage = taskPriorityConfigRepository.findByConditions(
                request.getCustomer(),
                request.getSubCustomer(),
                request.getFactory(),
                request.getFactorySite(),
                request.getDeviceId(),
                request.getTestStage(),
                request.getLotType(),
                request.getLotId(),
                request.getWaferNo(),
                request.getTestArea(),
                request.getFileCategory(),
                request.toPageable()
        );

        return PageableDataVo.of(configPage.map(this::convertToPriorityConfigVo));
    }

    @Transactional
    public Long addConfig(PriorityConfigAddVo request) {
        // 检查是否已存在相同配置
        if (taskPriorityConfigRepository.existsByCustomerAndSubCustomerAndFactoryAndFactorySiteAndDeviceIdAndTestStageAndLotTypeAndTestAreaAndFileCategoryAndLotIdAndWaferNoAndDeleteFlag(
                request.getCustomer(),
                request.getSubCustomer(),
                request.getFactory(),
                request.getFactorySite(),
                request.getDeviceId(),
                request.getTestStage(),
                LotType.of(request.getLotType()),
                TestArea.of(request.getTestArea()),
                FileCategory.of(request.getFileCategory()),
                request.getLotId(),
                request.getWaferNo(),
                0
        )) {
            throw new BpmsException(PRIORITY_CONFIG_ALREADY_EXISTS);
        }

        Date now = new Date();
        DwTaskPriorityConfig config = new DwTaskPriorityConfig()
                .setCustomer(request.getCustomer())
                .setSubCustomer(request.getSubCustomer())
                .setFactory(request.getFactory())
                .setFactorySite(request.getFactorySite())
                .setDeviceId(request.getDeviceId())
                .setTestStage(request.getTestStage())
                .setLotType(LotType.of(request.getLotType()))
                .setTestArea(TestArea.of(request.getTestArea()))
                .setFileCategory(FileCategory.of(request.getFileCategory()))
                .setLotId(request.getLotId())
                .setWaferNo(request.getWaferNo())
                .setPriority(parsePriority(request.getPriority()))
                .setMaxStartT(request.getMaxStartT())
                .setMinStartT(request.getMinStartT())
                .setMaxFtpMtime(request.getMaxFtpMtime())
                .setMinFtpMtime(request.getMinFtpMtime())
                .setFtpPath(request.getFtpPath())
                .setCreateTime(now)
                .setUpdateTime(now)
                .setCreateUser(RequestContext.getUserName())
                .setUpdateUser(RequestContext.getUserName())
                .setDeleteFlag(0);

        DwTaskPriorityConfig saved = taskPriorityConfigRepository.save(config);
        updateCreatedTasks(config);
        return saved.getId();
    }

    // 更新正在自动入库任务的优先级
    private void updateCreatedTasks(DwTaskPriorityConfig config) {
        List<LotMetaDataDetail> lotMetaDataDetails =
                config.getDeviceId() == null ?
                        lotMetaDataDetailRepository.findByProcessStatusAndFactoryAndFactorySiteAndLotTypeAndTestAreaAndFileCategory(
                                ProcessStatus.CREATE,
                                config.getFactory(),
                                config.getFactorySite(),
                                config.getLotType(),
                                config.getTestArea(),
                                config.getFileCategory()
                        ) :
                        lotMetaDataDetailRepository.findByProcessStatusAndFactoryAndFactorySiteAndLotTypeAndTestAreaAndFileCategoryAndDeviceId(
                                ProcessStatus.CREATE,
                                config.getFactory(),
                                config.getFactorySite(),
                                config.getLotType(),
                                config.getTestArea(),
                                config.getFileCategory(),
                                config.getDeviceId());
        if (config.getTestStage() != null) {
            lotMetaDataDetails = lotMetaDataDetails.stream()
                    .filter(detail -> Objects.equals(config.getTestStage(), detail.getTestStage()))
                    .collect(Collectors.toList());
        }
        if (config.getLotId() != null) {
            lotMetaDataDetails = lotMetaDataDetails.stream()
                    .filter(detail -> Objects.equals(config.getLotId(), detail.getLotId()))
                    .collect(Collectors.toList());
        }
        if (config.getWaferNo() != null) {
            lotMetaDataDetails = lotMetaDataDetails.stream()
                    .filter(detail -> Objects.equals(config.getWaferNo(), detail.getWaferNo()))
                    .collect(Collectors.toList());
        }
        List<LotMetaDataDetail> updated = taskPriorityManager.updateLotMetaDataPriority(lotMetaDataDetails);
        updated.stream().map(lotMetaDataDetail -> new CalculateDimensionKey(
                        lotMetaDataDetail.getCustomer(),
                        lotMetaDataDetail.getSubCustomer(),
                        lotMetaDataDetail.getFileCategory(),
                        lotMetaDataDetail.getFactory(),
                        lotMetaDataDetail.getFactorySite(),
                        lotMetaDataDetail.getDeviceId(),
                        lotMetaDataDetail.getLotType(),
                        lotMetaDataDetail.getTestArea(),
                        lotMetaDataDetail.getTestStage(),
                        lotMetaDataDetail.getLotId(),
                        lotMetaDataDetail.getWaferNo()))
                .distinct()
                .forEach(calculateDimensionKey ->
                        lotMetaDataDetailRepository.updatePriorityByCalculateDimension(
                                config.getCustomer(),
                                config.getSubCustomer(),
                                calculateDimensionKey.getFactory(),
                                calculateDimensionKey.getFactorySite(),
                                calculateDimensionKey.getDeviceId(),
                                calculateDimensionKey.getTestArea().getArea(),
                                calculateDimensionKey.getLotType().getType(),
                                calculateDimensionKey.getFileCategory().getCategory(),
                                calculateDimensionKey.getTestStage(),
                                calculateDimensionKey.getLotId(),
                                calculateDimensionKey.getWaferNo(),
                                config.getPriority(),
                                config.getUpdateUser(),
                                config.getUpdateTime()));
        // 更新linkx的优先级
        bzAppInstanceRepository.updatePriority(
                config.getCustomer(),
                config.getSubCustomer(),
                config.getFactory(),
                config.getFactorySite(),
                config.getDeviceId(),
                config.getTestArea().getArea(),
                config.getLotType().getType(),
                config.getFileCategory().getCategory(),
                config.getTestStage(),
                config.getLotId(),
                config.getWaferNo(),
                config.getPriority(),
                config.getUpdateUser(),
                config.getUpdateTime());
    }

    @Transactional
    public void updateConfig(PriorityConfigUpdateVo request) {
        DwTaskPriorityConfig config = taskPriorityConfigRepository.findByIdAndDeleteFlag(
                        request.getId(), 0)
                .orElseThrow(() -> new BpmsException(COMMON_OPERATE_NOT_PRESENT));

        taskPriorityConfigRepository.updateConfig(
                request.getId(),
                parsePriority(request.getPriority()),
                request.getMaxStartT(),
                request.getMinStartT(),
                request.getMaxFtpMtime(),
                request.getMinFtpMtime(),
                request.getFtpPath(),
                RequestContext.getUserName()
        );

        config.setPriority(parsePriority(request.getPriority()))
                .setMaxStartT(request.getMaxStartT())
                .setMinStartT(request.getMinStartT())
                .setMaxFtpMtime(request.getMaxFtpMtime())
                .setMinFtpMtime(request.getMinFtpMtime())
                .setFtpPath(request.getFtpPath())
                .setUpdateTime(new Date());

        updateCreatedTasks(config);
    }

    @Transactional
    public void deleteConfig(Long id) {
        taskPriorityConfigRepository.findByIdAndDeleteFlag(id, 0)
                .orElseThrow(() -> new BpmsException(COMMON_OPERATE_NOT_PRESENT));

        taskPriorityConfigRepository.deleteConfig(id, RequestContext.getUserName());
    }

    private PriorityConfigVo convertToPriorityConfigVo(DwTaskPriorityConfig config) {
        PriorityConfigVo vo = new PriorityConfigVo();
        vo.setId(config.getId());
        vo.setCustomer(config.getCustomer());
        vo.setSubCustomer(config.getSubCustomer());
        vo.setFactory(config.getFactory());
        vo.setFactorySite(config.getFactorySite());
        vo.setDeviceId(config.getDeviceId());
        vo.setTestStage(config.getTestStage());
        vo.setLotType(config.getLotType().getType());
        vo.setLotId(config.getLotId());
        vo.setWaferNo(config.getWaferNo());
        vo.setTestArea(config.getTestArea().getArea());
        vo.setFileCategory(config.getFileCategory().getCategory());
        vo.setPriority(formatPriority(config.getPriority()));
        vo.setMinStartT(config.getMinStartT());
        vo.setMaxStartT(config.getMaxStartT());
        vo.setMinFtpMtime(config.getMinFtpMtime());
        vo.setMaxFtpMtime(config.getMaxFtpMtime());
        vo.setFtpPath(config.getFtpPath());
        vo.setCreateTime(config.getCreateTime());
        vo.setUpdateTime(config.getUpdateTime());
        return vo;
    }

    private AutoTaskResultVo convertToAutoTaskResultVo(RankedLotMetaDataDetail detail) {
        AutoTaskResultVo vo = new AutoTaskResultVo();
        vo.setRank(detail.getRankNum());
        vo.setId(detail.getId());
        vo.setTestArea(detail.getTestArea());
        vo.setFactory(detail.getFactory());
        vo.setDeviceId(detail.getDeviceId());
        vo.setFileCategory(detail.getFileCategory());
        vo.setLotId(detail.getLotId());
        vo.setWaferNo(detail.getWaferNo());
        vo.setLotType(detail.getLotType());
        vo.setTestStage(detail.getTestStage());
        vo.setPriority(formatPriority(detail.getPriority()));
        vo.setCreateTime(detail.getCreateTime());
        vo.setUpdateTime(detail.getUpdateTime());
        return vo;
    }

    private ManualUploadTaskVo convertToManualUploadTaskVo(RankedManualCalculateTask task) {
        ManualUploadTaskVo vo = new ManualUploadTaskVo();
        vo.setRank(task.getRankNum());
        vo.setId(task.getId());
        vo.setUser(task.getFileOwner());
        vo.setPriority(formatPriority(task.getPriority()));
        vo.setFileName(task.getFileName());
        vo.setFileSize(task.getFileSize());
        vo.setCreateTime(task.getCreateTime());
        vo.setUpdateTime(task.getUpdateTime());
        return vo;
    }

    private SparkTaskVo convertToSparkTaskVo(RankedComputePool task) {
        SparkTaskVo vo = new SparkTaskVo();
        vo.setRank(task.getRankNum());
        vo.setId(task.getId());
        vo.setAppName(task.getAppName());
        vo.setComputeType(task.getComputeType());
        vo.setDieCnt(task.getDieCnt());
        vo.setTestItemCnt(task.getTestItemCnt());
        vo.setQueue(task.getQueue());
        vo.setPriority(fromPriorityGroup(task.getPriorityGroup().intValue()));
        vo.setProcessStatus(task.getProcessStatus());
        vo.setCreateTime(task.getCreateTime());
        vo.setUpdateTime(task.getUpdateTime());
        return vo;
    }

    static String formatPriority(Integer priority) {
        if (priority > 0) {
            return priority.toString();
        } else if (priority == 0) {
            return DEFAULT;
        } else if (priority == -100) {
            return DEFERRED;
        } else {
            throw new BpmsException(ResponseCode.Business.COMMON_PARAMETER_ERROR);
        }
    }


    /**
     * 从compute.bz_compute_pool中的priorityGroup得到priority
     *
     * @param priorityGroup compute.bz_compute_pool中的priorityGroup是越小优先级越高
     *                      关系是 priority = priorityGroup * -1
     * @return 任务优先级管理页面中的priority
     */
    static String fromPriorityGroup(Integer priorityGroup) {
        if (priorityGroup < 0) {
            return String.valueOf((priorityGroup * -1));
        } else if (priorityGroup == 100) {
            return DEFERRED;
        } else {
            return DEFAULT;
        }
    }

    static Integer parsePriority(String priority) {
        if (priority.equals(DEFAULT)) {
            return 0;
        } else if (priority.equals(DEFERRED)) {
            return -100;
        } else {
            try {
                return Integer.parseInt(priority);
            } catch (NumberFormatException e) {
                throw new BpmsException(ResponseCode.Business.COMMON_PARAMETER_ERROR);
            }
        }
    }
}
