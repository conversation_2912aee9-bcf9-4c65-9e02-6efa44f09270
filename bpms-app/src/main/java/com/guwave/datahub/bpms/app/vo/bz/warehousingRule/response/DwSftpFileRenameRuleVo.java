package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.response;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.FileRenameRule;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_FORMAT;

/**
 * 2025/3/4 16:49
 * DwSftpFileRenameRuleVo
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(title = "文件重命名规则")
public class DwSftpFileRenameRuleVo {
    @Schema(title = "id")
    private Long id;

    @Schema(description = "客户名称", example = "AMD")
    private String customer;

    @Schema(description = "子客户", example = "AMD")
    @ExcelProperty("subCustomer")
    private String subCustomer;

    @Schema(description = "ftp服务ip", example = "************")
    @ExcelProperty("ftpIp")
    private String ftpIp;

    @Schema(description = "使用时间戳，与使用目录互斥", example = "1")
    @ExcelProperty("useTimestamp")
    private Integer useTimestamp;

    @Schema(description = "需要执行的正则", example = "/upload/onedata_saas/NOVOSNS/NOVOSNS/.*/WIP/.*\\.xlsx$")
    @ExcelProperty("ruleExpression")
    private String ruleExpression;

    @Schema(description = "需要拼接的目录个数")
    @ExcelProperty("directoryNum")
    private Integer directoryNum;

    @Schema(title = "createTime", description = "创建时间", example = "2024-11-15 11:21:00")
    private String createTime;

    @Schema(title = "updateTime", description = "更新时间", example = "2024-11-18 11:21:00")
    private String updateTime;

    @Schema(description = "创建用户", example = "System")
    private String createUser;

    @Schema(description = "更新用户", example = "System")
    private String updateUser;

    public static DwSftpFileRenameRuleVo of(FileRenameRule data) {
        return DwSftpFileRenameRuleVo.builder()
                .id(data.getId())
                .customer(data.getCustomer())
                .subCustomer(data.getSubCustomer())
                .ftpIp(data.getFtpIp())
                .useTimestamp(data.getUseTimestamp())
                .ruleExpression(data.getRuleExpression())
                .directoryNum(data.getDirectoryNum())
                .createTime(DateUtil.format(data.getCreateTime(), NORM_DATETIME_FORMAT))
                .updateTime(DateUtil.format(data.getUpdateTime(), NORM_DATETIME_FORMAT))
                .createUser(data.getCreateUser())
                .updateUser(data.getUpdateUser())
                .build();
    }
}
