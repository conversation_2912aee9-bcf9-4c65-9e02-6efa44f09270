package com.guwave.datahub.bpms.app.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.guwave.onedata.dataware.common.contant.StdfFieldType;

public class StdfFieldTypeConverter implements Converter<StdfFieldType> {
    @Override
    public Class<?> supportJavaTypeKey() {
        return StdfFieldType.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(StdfFieldType value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new WriteCellData<>(value.getType());
    }

    // 从Excel读取时：String -> StdfFieldType
    @Override
    public StdfFieldType convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String fileCategory = cellData.getStringValue();
        return StdfFieldType.valueOf(fileCategory);
    }
}
