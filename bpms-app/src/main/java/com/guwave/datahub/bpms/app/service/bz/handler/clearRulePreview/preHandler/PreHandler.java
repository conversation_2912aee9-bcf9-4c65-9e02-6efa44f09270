package com.guwave.datahub.bpms.app.service.bz.handler.clearRulePreview.preHandler;

import com.alibaba.fastjson.JSON;
import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model.*;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.dao.domain.clearRulePreview.ClearRulePreviewRecord;
import com.guwave.datahub.bpms.dao.repository.dw.*;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.FileLoadExceptionInfo;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.vo.DeviceInfoDto;
import com.guwave.onedata.dataware.common.model.vo.ProductDeviceInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SourceStandardDeviceIdRule;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SourceStandardFieldRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.io.File;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

@Component
@Slf4j
public abstract class PreHandler {

    private static final String jsFun = "function getData(metaData) {\n" +
            "        var result = null\n" +
            "        if (metaData) {\n" +
            "            metaData = JSON.parse(metaData)\n" +
            "            {RULE_EXPRESSION}\n" +
            "        }\n" +
            "        if (result || result === 0) {\n" +
            "            return result\n" +
            "        } else {\n" +
            "            return null\n" +
            "        }\n" +
            "    }";

    private static final String RULE_EXPRESSION = "{RULE_EXPRESSION}";
    private static final String JS_ENGINE_NAME = "javascript";

    private static final Map<ParseField, Pair<BiConsumer<ClearRulePreviewRecord, Object>, Function<ClearRulePreviewRecord, Object>>> SOURCE_STANDARD_FIELD_CONSUMER_MAP = new HashMap<ParseField, Pair<BiConsumer<ClearRulePreviewRecord, Object>, Function<ClearRulePreviewRecord, Object>>>() {{
        put(ParseField.TEST_AREA, Pair.of((record, result) -> record.setTestArea(TestArea.of(result.toString())), t -> t.getTestArea() != null ? t.getTestArea().getArea() : null));
        put(ParseField.LOT_TYPE, Pair.of((record, result) -> record.setLotType(LotType.of(result.toString())), ClearRulePreviewRecord::getLotType));
        put(ParseField.OFFLINE_RETEST, Pair.of((record, result) -> record.setOfflineRetest(Double.valueOf(result.toString()).intValue()), ClearRulePreviewRecord::getOfflineRetest));
        put(ParseField.INTERRUPT, Pair.of((record, result) -> record.setInterrupt(Double.valueOf(result.toString()).intValue()), ClearRulePreviewRecord::getInterrupt));
        put(ParseField.LOT_ID, Pair.of((record, result) -> record.setLotId(result.toString()), ClearRulePreviewRecord::getLotId));
        put(ParseField.SBLOT_ID, Pair.of((record, result) -> record.setSblotId(result.toString()), ClearRulePreviewRecord::getSblotId));
        put(ParseField.TEST_COD, Pair.of((record, result) -> record.setTestCod(result.toString()), ClearRulePreviewRecord::getTestCod));
        put(ParseField.ORIGIN_WAFER_ID, Pair.of((record, result) -> record.setOriginWaferId(result.toString()), ClearRulePreviewRecord::getOriginWaferId));
        put(ParseField.WAFER_NO, Pair.of((record, result) -> record.setWaferNo(String.valueOf(Double.valueOf(result.toString()).intValue())), ClearRulePreviewRecord::getWaferNo));
        put(ParseField.TEST_STAGE, Pair.of((record, result) -> record.setTestStage(result.toString()), ClearRulePreviewRecord::getTestStage));
        put(ParseField.DUP_RETEST, Pair.of((record, result) -> record.setDupRetest(Double.valueOf(result.toString()).intValue()), ClearRulePreviewRecord::getDupRetest));
        put(ParseField.BATCH_NUM, Pair.of((record, result) -> record.setBatchNum(Double.valueOf(result.toString()).intValue()), ClearRulePreviewRecord::getBatchNum));
        put(ParseField.FLOOR_ID, Pair.of((record, result) -> record.setFloorId(result.toString()), ClearRulePreviewRecord::getFloorId));
        put(ParseField.TEST_TEMPERATURE, Pair.of((record, result) -> record.setTestTemperature(result.toString()), ClearRulePreviewRecord::getTestTemperature));
        put(ParseField.TESTER_NAME, Pair.of((record, result) -> record.setTesterName(result.toString()), ClearRulePreviewRecord::getTesterName));
        put(ParseField.TESTER_TYPE, Pair.of((record, result) -> record.setTesterType(result.toString()), ClearRulePreviewRecord::getTesterType));
        put(ParseField.PROBECARD_LOADBOARD_ID, Pair.of((record, result) -> record.setProbecardLoadboardId(result.toString()), ClearRulePreviewRecord::getProbecardLoadboardId));
        put(ParseField.TEST_PROGRAM, Pair.of((record, result) -> record.setTestProgram(result.toString()), ClearRulePreviewRecord::getTestProgram));
        put(ParseField.TEST_PROGRAM_VERSION, Pair.of((record, result) -> record.setTestProgramVersion(result.toString()), ClearRulePreviewRecord::getTestProgramVersion));
        put(ParseField.PKG_TYP, Pair.of((record, result) -> record.setPkgTyp(result.toString()), ClearRulePreviewRecord::getPkgTyp));
        put(ParseField.RETEST_BIN_NUM, Pair.of((record, result) -> record.setRetestBinNum(result.toString()), ClearRulePreviewRecord::getRetestBinNum));
        put(ParseField.CONDITION_SET, Pair.of((record, result) -> record.setConditionSet(getMap(result.toString())), ClearRulePreviewRecord::getConditionSet));
        put(ParseField.START_T, Pair.of((record, result) -> record.setStartT(result.toString()), ClearRulePreviewRecord::getStartT));
        put(ParseField.FINISH_T, Pair.of((record, result) -> record.setFinishT(result.toString()), ClearRulePreviewRecord::getFinishT));
        put(ParseField.REAL_WAFER_ID, Pair.of((record, result) -> record.setRealWaferId(result.toString()), ClearRulePreviewRecord::getRealWaferId));
    }};

    private static final Map<ParseField, Predicate<ClearRulePreviewRecord>> KEY_FIELD_CHECK_MAP = new LinkedHashMap<ParseField, Predicate<ClearRulePreviewRecord>>() {{
        put(ParseField.DEVICE_ID, record -> StringUtils.isNotEmpty(record.getDeviceId()));
        put(ParseField.TEST_AREA, record -> record.getTestArea() != null);
        put(ParseField.LOT_TYPE, record -> record.getLotType() != null);
        put(ParseField.TEST_STAGE, record -> record.getTestArea() != null
                        && StringUtils.isNotEmpty(record.getTestStage())
//                && Pattern.compile(String.format("^%s[1-9]\\d*$", record.getTestArea().getArea())).matcher(record.getTestStage()).matches()
        );
        put(ParseField.LOT_ID, record -> StringUtils.isNotEmpty(record.getLotId()));
        put(ParseField.SBLOT_ID, record -> StringUtils.isNotEmpty(record.getSblotId()));
        put(ParseField.WAFER_ID, record -> StringUtils.isNotEmpty(record.getWaferId()) && record.getWaferId().contains(UNDER_LINE));
        put(ParseField.WAFER_NO, record -> StringUtils.isNotEmpty(record.getWaferNo()));
        put(ParseField.START_T, record -> NumberUtils.isDigits(record.getStartT()));
        put(ParseField.FINISH_T, record -> NumberUtils.isDigits(record.getFinishT()));
        put(ParseField.OFFLINE_RETEST, record -> record.getOfflineRetest() != null);
        put(ParseField.INTERRUPT, record -> record.getInterrupt() != null);
        put(ParseField.DUP_RETEST, record -> record.getDupRetest() != null);
        put(ParseField.BATCH_NUM, record -> record.getBatchNum() != null);
    }};


    @Autowired
    private SourceStandardDeviceIdRuleRepository sourceStandardDeviceIdRuleRepository;
    @Autowired
    private SourceStandardFieldRuleRepository sourceStandardFieldRuleRepository;
    @Autowired
    private DataClearRuleRepository dataClearRuleRepository;
    @Autowired
    private EcidRuleRepository ecidRuleRepository;
    @Autowired
    private DeviceInfoRepository deviceInfoRepository;


    public abstract boolean isSuppport(ClearRulePreviewRecord record);

    public void preDealFile(ClearRulePreviewRecord record, File file, ClearRulePreviewResult clearRulePreviewResult) throws Exception {
        // 预解析文件 填充 record 和 MetaData
        fillMetaData(record, file, clearRulePreviewResult.getMetaData());

        // 根据字段配置 修改record字段
        fillPreviewResult(record, file, clearRulePreviewResult);
    }

    public abstract void fillMetaData(ClearRulePreviewRecord record, File file, MetaData metaData) throws Exception;

    public void fillPreviewResult(ClearRulePreviewRecord record, File file, ClearRulePreviewResult clearRulePreviewResult) {
        Map<ParseField, ConfResult> confResultMap = clearRulePreviewResult.getConfResults().stream().collect(Collectors.toMap(t -> ParseField.of(t.getField()), t -> t, (v1, v2) -> v1));

        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName(JS_ENGINE_NAME);

        // 放置一些额外信息
        List<DeviceInfoDto> deviceInfos = deviceInfoRepository.findDeviceInfo(record.getCustomer(), record.getFactory(), record.getFactorySite(), clearRulePreviewResult.getMetaData().getPartTyp());
        clearRulePreviewResult.getMetaData().setDeviceInfos(deviceInfos.stream().map(ProductDeviceInfo::of).collect(Collectors.toList()));

        String metaData = JSON.toJSONString(clearRulePreviewResult.getMetaData());

        // 修改deviceId
        SourceStandardDeviceIdRule sourceStandardDeviceIdRule = querySourceStandardDeviceIdRule(record);
        if (sourceStandardDeviceIdRule != null) {
            clearRulePreviewResult.getSettingSnapshot().getDwSourceStandardDeviceIdRules().add(SettingSnapshot.SourceStandardDeviceIdRuleSnapshot.of(sourceStandardDeviceIdRule));
            confResultMap.get(ParseField.DEVICE_ID).setRuleExpression(sourceStandardDeviceIdRule.getRuleExpression());
            Object deviceId = getResultFromJs(engine, metaData, ParseField.DEVICE_ID.getField(), jsFun.replace(RULE_EXPRESSION, sourceStandardDeviceIdRule.getRuleExpression()));
            if (deviceId != null) {
                record.setDeviceId(deviceId.toString());
            }
        }

        MetaData metaDataCopy = clearRulePreviewResult.getMetaData().copy();
        metaDataCopy.setPartTyp(convertNullStr(record.getDeviceId()));
        metaData = JSON.toJSONString(metaDataCopy);

        // 修改其他字段
        List<SourceStandardFieldRule> sourceStandardFieldRuleList = querySourceStandardFieldRules(record);
        // 修改前
        beforeModifyField(record, confResultMap);
        if (CollectionUtils.isNotEmpty(sourceStandardFieldRuleList)) {
            clearRulePreviewResult.getSettingSnapshot().getDwSourceStandardFieldRules().addAll(sourceStandardFieldRuleList.stream().map(SettingSnapshot.SourceStandardFieldRuleSnapshot::of).collect(Collectors.toList()));
            for (SourceStandardFieldRule sourceStandardFieldRule : sourceStandardFieldRuleList) {
                String field = sourceStandardFieldRule.getField();
                ParseField parseField = ParseField.of(field);
                Pair<BiConsumer<ClearRulePreviewRecord, Object>, Function<ClearRulePreviewRecord, Object>> consumer = SOURCE_STANDARD_FIELD_CONSUMER_MAP.get(parseField);
                String completeJsFun = jsFun.replace(RULE_EXPRESSION, sourceStandardFieldRule.getRuleExpression());

                if (consumer != null) {
                    confResultMap.get(parseField).setRuleExpression(sourceStandardFieldRule.getRuleExpression());
                }

                Object result = getResultFromJs(engine, metaData, field, completeJsFun);

                if (result != null && consumer != null) {
                    try {
                        consumer.getLeft().accept(record, result);
                    } catch (Exception e) {
                        log.info("{} 从JS解析出来的值非法！", field, e);
                        String fieldExceptionMessage = FileLoadExceptionInfo.KEY_FIELD_JS_SCRIPT_EXECUTE_FAIL_EXCEPTION.getMessage().replace(KEY_FIELD, field);
                        throw new FileLoadException(FileLoadExceptionInfo.KEY_FIELD_JS_SCRIPT_EXECUTE_FAIL_EXCEPTION, ExceptionUtils.getStackTrace(e), Collections.singletonList(field)).updateExceptionMessage(fieldExceptionMessage);
                    }
                }
            }
        }
        // 修改后
        afterModifyField(record, confResultMap);

        // 设置startT和finishT
        fillStartAndFinishT(record);

        // 校验关键字段
        checkKeyField(record, metaData);

    }

    private void checkKeyField(ClearRulePreviewRecord record, String metaData) {
        Pair<Boolean, List<String>> checkKeyFiledPair = checkRecordKeyField(record);
        if (!checkKeyFiledPair.getKey()) {
            List<String> keyFieldNullList = checkKeyFiledPair.getValue();
            String exceptionMessage = keyFieldNullList.stream().map(field -> FileLoadExceptionInfo.KEY_FIELD_NULL_EXCEPTION.getMessage().replace(KEY_FIELD, field)).collect(Collectors.joining(COMMA));
            String errorMessage = new StringBuilder("必要字段为空：")
                    .append(Constant.ENTER)
                    .append("基础信息 : ")
                    .append(metaData).toString();
            throw new FileLoadException(FileLoadExceptionInfo.KEY_FIELD_NULL_EXCEPTION, errorMessage, keyFieldNullList)
                    .updateExceptionMessage(exceptionMessage);
        }
    }

    private Pair<Boolean, List<String>> checkRecordKeyField(ClearRulePreviewRecord record) {
        boolean checkFlag = true;
        List<String> keyFieldNullList = new ArrayList<>();

        Set<ParseField> keyFileds = Arrays.stream(getKeyFields().split(COMMA)).map(ParseField::of).collect(Collectors.toSet());
        for (Map.Entry<ParseField, Predicate<ClearRulePreviewRecord>> entry : KEY_FIELD_CHECK_MAP.entrySet()) {
            ParseField field = entry.getKey();
            Predicate<ClearRulePreviewRecord> predicate = entry.getValue();
            if (keyFileds.contains(field)) {
                if (!predicate.test(record)) {
                    checkFlag = false;
                    keyFieldNullList.add(field.getField());
                }
            }
        }
        return Pair.of(checkFlag, keyFieldNullList);
    }

    protected abstract String getKeyFields();

    private static void fillStartAndFinishT(ClearRulePreviewRecord record) {
        if (StringUtils.isEmpty(record.getStartT())) {
            record.setStartT(record.getOriginStartT());
        }

        if (StringUtils.isEmpty(record.getFinishT())) {
            record.setFinishT(record.getOriginFinishT());
        }

        if (StringUtils.isEmpty(record.getStartT())) {
            record.setStartT(record.getFinishT());
        }
        if (StringUtils.isEmpty(record.getFinishT())) {
            record.setFinishT(record.getStartT());
        }
        if (StringUtils.isEmpty(record.getStartT())) {
            record.setStartT(0 + Constant.EMPTY);
            record.setFinishT(0 + Constant.EMPTY);
        }
    }

    public abstract void beforeModifyField(ClearRulePreviewRecord record, Map<ParseField, ConfResult> confResultMap);

    public abstract void afterModifyField(ClearRulePreviewRecord record, Map<ParseField, ConfResult> confResultMap);


    public static Object getResultFromJs(ScriptEngine engine, String metaData, String field, String completeJsFun) {
        Object result = null;
        try {
            // 注册js方法
            engine.eval(completeJsFun);
            Invocable invo = (Invocable) engine;

            //执行脚本中方法
            result = invo.invokeFunction("getData", metaData);
            log.info("{} 执行js脚本； completeJsFun : {} \n metaData: {} \n result: {}", field, completeJsFun, metaData, result);
        } catch (Throwable e) {
            String errorMessage = String.format("%s 执行js脚本异常； completeJsFun : %s \n metaData: %s \n %s", field, completeJsFun, metaData, e.getMessage());
            String fieldExceptionMessage = FileLoadExceptionInfo.KEY_FIELD_JS_SCRIPT_EXECUTE_FAIL_EXCEPTION.getMessage().replace(KEY_FIELD, field);
            log.info(errorMessage);
            throw new FileLoadException(FileLoadExceptionInfo.KEY_FIELD_JS_SCRIPT_EXECUTE_FAIL_EXCEPTION, errorMessage, Collections.singletonList(field)).updateExceptionMessage(fieldExceptionMessage);
        }
        return result;
    }

    private SourceStandardDeviceIdRule querySourceStandardDeviceIdRule(ClearRulePreviewRecord record) {
        List<SourceStandardDeviceIdRule> sourceStandardDeviceIdRules = sourceStandardDeviceIdRuleRepository.findAllByCustomerAndSubCustomerAndTestAreaAndFactoryAndFactorySite(
                record.getCustomer(), record.getSubCustomer(), record.getGivenTestArea(), record.getFactory(), record.getFactorySite()
        );
        if (sourceStandardDeviceIdRules.size() > 1) {
            throw new RuntimeException("deviceId获取规则存在多条");
        } else if (sourceStandardDeviceIdRules.size() == 1) {
            return sourceStandardDeviceIdRules.get(0);
        } else {
            return null;
        }
    }

    private List<SourceStandardFieldRule> querySourceStandardFieldRules(ClearRulePreviewRecord record) {
        List<SourceStandardFieldRule> sourceStandardFieldRuleList;
        if (record.getCustomer().equals(record.getSubCustomer())) {
            // sub_customer 和 customer 相同
            sourceStandardFieldRuleList = sourceStandardFieldRuleRepository.findAllByCustomerAndTestAreaAndFactoryAndFactorySiteAndDeviceId(record.getCustomer(), record.getGivenTestArea(), record.getFactory(), record.getFactorySite(), record.getDeviceId());
            if (CollectionUtils.isEmpty(sourceStandardFieldRuleList)) {
                sourceStandardFieldRuleList = sourceStandardFieldRuleRepository.findAllByCustomerAndTestAreaAndFactoryAndFactorySiteAndDeviceId(record.getCustomer(), record.getGivenTestArea(), record.getFactory(), record.getFactorySite(), Constant.EMPTY);
            }
            if (CollectionUtils.isEmpty(sourceStandardFieldRuleList)) {
                sourceStandardFieldRuleList = sourceStandardFieldRuleRepository.findAllByCustomerAndTestAreaAndFactoryAndFactorySiteAndDeviceId(record.getCustomer(), record.getGivenTestArea(), Constant.EMPTY, Constant.EMPTY, Constant.EMPTY);
            }
        } else if (record.getCustomer().equals(record.getFactory())) {
            // factory 和 customer 相同
            sourceStandardFieldRuleList = sourceStandardFieldRuleRepository.findAllByCustomerAndTestAreaAndSubCustomerAndDeviceId(record.getCustomer(), record.getGivenTestArea(), record.getSubCustomer(), record.getDeviceId());
            if (CollectionUtils.isEmpty(sourceStandardFieldRuleList)) {
                sourceStandardFieldRuleList = sourceStandardFieldRuleRepository.findAllByCustomerAndTestAreaAndSubCustomerAndDeviceId(record.getCustomer(), record.getGivenTestArea(), record.getSubCustomer(), Constant.EMPTY);
            }
            if (CollectionUtils.isEmpty(sourceStandardFieldRuleList)) {
                sourceStandardFieldRuleList = sourceStandardFieldRuleRepository.findAllByCustomerAndTestAreaAndSubCustomerAndDeviceId(record.getCustomer(), record.getGivenTestArea(), Constant.EMPTY, Constant.EMPTY);
            }
        } else {
            // sub_customer 和 customer 不同   且   factory 也和 customer 不同
            sourceStandardFieldRuleList = sourceStandardFieldRuleRepository.findAllByCustomerAndTestAreaAndSubCustomerAndFactoryAndDeviceId(record.getCustomer(), record.getGivenTestArea(), record.getSubCustomer(), record.getFactory(), record.getDeviceId());
            if (CollectionUtils.isEmpty(sourceStandardFieldRuleList)) {
                sourceStandardFieldRuleList = sourceStandardFieldRuleRepository.findAllByCustomerAndTestAreaAndSubCustomerAndFactoryAndDeviceId(record.getCustomer(), record.getGivenTestArea(), record.getSubCustomer(), record.getFactory(), Constant.EMPTY);
            }
            if (CollectionUtils.isEmpty(sourceStandardFieldRuleList)) {
                sourceStandardFieldRuleList = sourceStandardFieldRuleRepository.findAllByCustomerAndTestAreaAndSubCustomerAndFactoryAndDeviceId(record.getCustomer(), record.getGivenTestArea(), record.getSubCustomer(), Constant.EMPTY, Constant.EMPTY);
            }
            if (CollectionUtils.isEmpty(sourceStandardFieldRuleList)) {
                sourceStandardFieldRuleList = sourceStandardFieldRuleRepository.findAllByCustomerAndTestAreaAndSubCustomerAndFactoryAndDeviceId(record.getCustomer(), record.getGivenTestArea(), Constant.EMPTY, Constant.EMPTY, Constant.EMPTY);
            }
        }
        return sourceStandardFieldRuleList;

    }

    public static String convertNullStr(String str) {
        if (str == null) {
            return Constant.EMPTY;
        }
        return str;
    }

    public static Map<String, String> getMap(String str) {
        Map<String, String> result = new HashMap<>();
        if (str.isEmpty()) {
            return result;
        }
        Map map = JSON.parseObject(str, Map.class);
        for (Object key : map.keySet()) {
            result.put(key.toString(), map.get(key).toString());
        }
        return result;
    }

    public Object test(MetaData metaData, String ruleExpression, String field) {
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName(JS_ENGINE_NAME);

        ParseField parseField = ParseField.of(field);
        Pair<BiConsumer<ClearRulePreviewRecord, Object>, Function<ClearRulePreviewRecord, Object>> consumer = SOURCE_STANDARD_FIELD_CONSUMER_MAP.get(parseField);
        String completeJsFun = jsFun.replace(RULE_EXPRESSION, ruleExpression);
        log.info("执行JS：{}", completeJsFun);

        Object result = getResultFromJs(engine, JSON.toJSONString(metaData), field, completeJsFun);
        log.info("执行JS结果：{}", result);
        if (result != null && consumer != null) {
            try {
                ClearRulePreviewRecord record = new ClearRulePreviewRecord();
                consumer.getLeft().accept(record, result);
                afterModifyField(record, new HashMap<ParseField, ConfResult>() {{
                    put(parseField, ConfResult.of(parseField));
                }});
                fillStartAndFinishT(record);
                return consumer.getRight().apply(record);
            } catch (Exception e) {
                log.info("{} 从JS解析出来的值非法！", field, e);
                throw new BpmsException(ResponseCode.Business.CLEAR_RULE_PREVIEW_CAN_NOT_CONVERT_RESULT, e.getMessage());
            }
        } else {
            throw new BpmsException(ResponseCode.Business.CLEAR_RULE_PREVIEW_NOT_SUPPORT_FIELD);
        }
    }
}
