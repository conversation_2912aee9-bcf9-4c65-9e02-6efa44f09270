package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.guwave.datahub.bpms.app.converter.TestAreaConverter;
import com.guwave.onedata.dataware.common.contant.TestArea;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DwSourceStandardDeviceIdRule {
    private String subCustomer;
    @ExcelProperty(converter = TestAreaConverter.class)
    private TestArea testArea;
    private String factory;
    private String factorySite;
    private String ruleExpression;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DwSourceStandardDeviceIdRule that = (DwSourceStandardDeviceIdRule) o;
        return Objects.equals(subCustomer, that.subCustomer) && Objects.equals(testArea, that.testArea) && Objects.equals(factory, that.factory) && Objects.equals(factorySite, that.factorySite) && Objects.equals(ruleExpression, that.ruleExpression);
    }

    @Override
    public int hashCode() {
        return Objects.hash(subCustomer, testArea, factory, factorySite, ruleExpression);
    }
}
