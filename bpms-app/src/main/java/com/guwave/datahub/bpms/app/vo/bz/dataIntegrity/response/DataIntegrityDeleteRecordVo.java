package com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.response;


import com.guwave.datahub.bpms.dao.domain.repair.DataIntegrityDeleteLog;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.time.DateFormatUtils;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(title = "数据完整性文件删除记录")
public class DataIntegrityDeleteRecordVo {

    private Long id;
    private String fileName;
    private String deleteTime;
    private String deleteBy;
    private String remark;

    public static DataIntegrityDeleteRecordVo of(DataIntegrityDeleteLog record) {
        return new DataIntegrityDeleteRecordVo(
                record.getId(),
                record.getFileName(),
                DateFormatUtils.format(record.getCreateTime(), "yyyy-MM-dd HH:mm:ss"),
                record.getCreateUser(),
                record.getRemark()
        );
    }
}
