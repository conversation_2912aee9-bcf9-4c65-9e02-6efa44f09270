package com.guwave.datahub.bpms.app.controller;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.service.bz.PriorityService;
import com.guwave.datahub.bpms.app.vo.bz.priority.request.*;
import com.guwave.datahub.bpms.app.vo.bz.priority.response.AutoTaskResultVo;
import com.guwave.datahub.bpms.app.vo.bz.priority.response.ManualUploadTaskVo;
import com.guwave.datahub.bpms.app.vo.bz.priority.response.PriorityConfigVo;
import com.guwave.datahub.bpms.app.vo.bz.priority.response.SparkTaskVo;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Min;

@Log
@Slf4j
@RestController
@AllArgsConstructor
@Auth
@Tag(name = "PriorityControllerAPI", description = "任务优先级管理相关接口",
        externalDocs = @ExternalDocumentation(description = "需求文档",
                url = "https://guwave.sharepoint.com/:fl:/g/contentstorage/CSP_3ffa68d2-cef0-4193-a70e-9eff47b9b177/EWry8XRhRu5GktaaJM-ZTicBKirIw1E6ZiQs-9Vm9cZn1Q?e=P4Iewq&nav=cz0lMkZjb250ZW50c3RvcmFnZSUyRkNTUF8zZmZhNjhkMi1jZWYwLTQxOTMtYTcwZS05ZWZmNDdiOWIxNzcmZD1iJTIxMG1qNlBfRE9rMEduRHA3X1I3bXhkME1VM2Jnd0hyaEdwSFhUSFRmVTRkNUF1UGNVWHlsQVFKbFVEQjZ4SmFsNSZmPTAxSENMSUNMREs2TFlYSVlLRzVaREpGVlUyRVRIWlNUUkgmYz0lMkYmYT1Mb29wQXBwJnA9JTQwZmx1aWR4JTJGbG9vcC1wYWdlLWNvbnRhaW5lciZ4PSU3QiUyMnclMjIlM0ElMjJUMFJUVUh4bmRYZGhkbVV1YzJoaGNtVndiMmx1ZEM1amIyMThZaUV3YldvMlVGOUVUMnN3UjI1RWNEZGZVamR0ZUdRd1RWVXpZbWQzU0hKb1IzQklXRlJJVkdaVk5HUTFRWFZRWTFWWWVXeEJVVXBzVlVSQ05uaEtZV3cxZkRBeFNFTk1TVU5NUWxkU1JGVlhOa2RLVWtkR1JreExSVWxNU0U5UlIwaE1TMDglM0QlMjIlMkMlMjJpJTIyJTNBJTIyNThjYjcxZGUtMmNhNy00OGZmLWFiNmQtN2U1MzVlZTcwZjkwJTIyJTdE")
)
@RequestMapping("priority")
public class PriorityController {

    private final PriorityService priorityService;

    @Operation(summary = "获取自动入库任务列表")
    @PostMapping("autoTasks")
    public ResponseVo<PageableDataVo<AutoTaskResultVo>> getAutoImportTasks(@Validated @RequestBody AutoTaskQueryVo request) {
        return ResponseVo.ok(priorityService.getAutoTasks(request));
    }

    @Operation(summary = "获取手动上传任务列表")
    @PostMapping("manualUploadTasks")
    public ResponseVo<PageableDataVo<ManualUploadTaskVo>> getManualUploadTasks(@Validated @RequestBody ManualUploadQueryVo request) {
        return ResponseVo.ok(priorityService.getManualUploadTasks(request));
    }

    @Operation(summary = "获取Spark任务列表")
    @PostMapping("sparkTasks")
    public ResponseVo<PageableDataVo<SparkTaskVo>> getSparkTasks(@Validated @RequestBody SparkTaskQueryVo request) {
        return ResponseVo.ok(priorityService.getSparkTasks(request));
    }

    @Operation(summary = "批量调整任务优先级")
    @PostMapping("updatePriority")
    public ResponseVo<Void> updatePriority(@Validated @RequestBody TaskPriorityUpdateVo request) {
        priorityService.updatePriority(request);
        return ResponseVo.ok();
    }

    @Operation(summary = "获取优先级配置列表")
    @PostMapping("getConfigs")
    public ResponseVo<PageableDataVo<PriorityConfigVo>> getConfigs(@Validated @RequestBody PriorityConfigQueryVo request) {
        return ResponseVo.ok(priorityService.getConfigs(request));
    }

    @Operation(summary = "新增优先级配置")
    @PostMapping("addConfig")
    public ResponseVo<Long> addConfig(@Validated @RequestBody PriorityConfigAddVo request) {
        return ResponseVo.ok(priorityService.addConfig(request));
    }

    @Operation(summary = "编辑优先级配置")
    @PostMapping("updateConfig")
    public ResponseVo<Void> updateConfig(@Validated @RequestBody PriorityConfigUpdateVo request) {
        priorityService.updateConfig(request);
        return ResponseVo.ok();
    }

    @Operation(summary = "删除优先级配置")
    @DeleteMapping("deleteConfig")
    public ResponseVo<Void> deleteConfig(@RequestParam("id") @Min(1) Long id) {
        priorityService.deleteConfig(id);
        return ResponseVo.ok();
    }
}
