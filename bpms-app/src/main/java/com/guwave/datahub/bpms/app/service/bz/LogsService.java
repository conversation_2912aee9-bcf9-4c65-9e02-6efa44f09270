package com.guwave.datahub.bpms.app.service.bz;

import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.vo.web.LogsQueryRequestVo;
import com.guwave.datahub.bpms.app.vo.web.LogsQueryResponseVo;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.common.constant.Constant;
import com.guwave.datahub.bpms.dao.repository.logs.OdsAppLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.guwave.datahub.bpms.app.vo.web.ResponseVo.ok;
import static com.guwave.onedata.dataware.common.contant.Constant.*;


@Slf4j
@Service
public class LogsService {

    private final OdsAppLogMapper odsAppLogMapper;

    @Autowired
    public LogsService(OdsAppLogMapper odsAppLogMapper) {
        this.odsAppLogMapper = odsAppLogMapper;
    }

    public ResponseVo<LogsQueryResponseVo> queryLogs(LogsQueryRequestVo requestVo) {
        validateLogQueryRequest(requestVo);

        String startTime = requestVo.getStartTime();
        String endTime = requestVo.getEndTime();

        LogQueryResult result = executeLogQuery(requestVo, startTime, endTime, 10000);

        LogsQueryResponseVo responseVo = new LogsQueryResponseVo();
        responseVo.setLogs(result.logs);
        responseVo.setLogCount(result.totalCount);
        return ok(responseVo);
    }

    public void downloadLogs(LogsQueryRequestVo requestVo, HttpServletResponse response) {
        validateLogQueryRequest(requestVo);

        String startTime = requestVo.getStartTime();
        String endTime = requestVo.getEndTime();

        // 先查询总数量，如果超过10万条则抛异常
        LogQueryResult countResult = executeLogQuery(requestVo, startTime, endTime, 0);
        if (countResult.totalCount > 100000) {
            throw new BpmsException(ResponseCode.Common.FILE_SIZE_EXCEED_LIMIT, "日志数量超过10万条，请缩小查询范围");
        }

        // 查询日志内容，限制10万条
        LogQueryResult result = executeLogQuery(requestVo, startTime, endTime, 100000);

        String host = requestVo.getHost() != null && !requestVo.getHost().isEmpty() ? requestVo.getHost() : ALL;
        String instance = requestVo.getInstance() != null && !requestVo.getInstance().isEmpty()
                ? requestVo.getInstance()
                : ALL;
        String start = startTime.replaceAll(COLON, UNDER_LINE);
        String end = endTime.replaceAll(COLON, UNDER_LINE);
        String fileName = Constant.LOG + UNDER_LINE
                + requestVo.getApp() + UNDER_LINE
                + host + UNDER_LINE
                + instance + UNDER_LINE
                + start + UNDER_LINE
                + end + POINT
                + Constant.LOG;
        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

        try (PrintWriter writer = response.getWriter()) {
            for (String log : result.logs) {
                writer.println(log);
            }
            writer.flush();
        } catch (IOException e) {
            log.error("下载日志失败", e);
            throw new BpmsException(ResponseCode.Common.COMMON_GENERATE_CONTENT_FAIL, "下载日志失败");
        }
    }

    private void validateLogQueryRequest(LogsQueryRequestVo requestVo) {
        // 校验app是必填项
        if (requestVo.getApp() == null || requestVo.getApp().isEmpty()) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "应用名是必填项");
        }

        // 校验时间范围不超过24小时
        if (requestVo.getStartTime() == null || requestVo.getEndTime() == null) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "开始时间和结束时间不能为空");
        }
        // 去掉时间范围校验
        // long hours = java.time.Duration.between(requestVo.getStartTime(), requestVo.getEndTime()).toHours();
        // if (hours > 24) {
        //     throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "日志的时间范围不能超过24小时");
        // }

        // 校验 queryType 是否为 "regexp" 或 "keyword"
        String queryType = requestVo.getQueryType();
        if (queryType == null || queryType.isEmpty()) {
            log.warn("Query type is null or empty");
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "查询类型是必填项");
        }

        if (!Constant.REGEXP.equals(queryType) && !Constant.KEYWORD.equals(queryType)) {
            log.warn("Invalid query type: {}", queryType);
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "查询类型只能是 'regexp' 或 'keyword'");
        }

        // 如果是正则查询，校验正则表达式合法性
        if (Constant.REGEXP.equals(requestVo.getQueryType())) {
            if (requestVo.getQueryValue() == null || requestVo.getQueryValue().isEmpty()) {
                log.error("正则表达式不能为空");
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "正则表达式不能为空");
            }
            try {
                java.util.regex.Pattern.compile(requestVo.getQueryValue());
            } catch (java.util.regex.PatternSyntaxException e) {
                log.error("正则表达式不合法:");
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "正则表达式不合法:");
            }
        }
    }

    private LogQueryResult executeLogQuery(LogsQueryRequestVo requestVo, String startTime, String endTime, int limit) {
        // 如果没有选择日志级别，则查询所有级别
        List<String> logLevels = requestVo.getLogLevel() != null ? requestVo.getLogLevel() : Collections.emptyList();

        LogQueryResult result = new LogQueryResult();

        // 根据查询类型调用不同方法,regexp
        if (Constant.REGEXP.equals(requestVo.getQueryType())) {
            Map<String, Object> countResult = odsAppLogMapper.countLogsByRegexp(requestVo.getApp(), requestVo.getHost(), requestVo.getInstance(), logLevels, startTime, endTime, requestVo.getQueryValue());
            result.totalCount = ((Number) countResult.get("count")).intValue();
            Integer host_instance_max_length = ((Number) countResult.get("host_instance_max_length")).intValue();
            if (limit > 0) {
                result.logs = odsAppLogMapper.selectLogsByRegexp(requestVo.getApp(), requestVo.getHost(), requestVo.getInstance(), logLevels, startTime, endTime, requestVo.getQueryValue(), limit, host_instance_max_length);
            }
        } else {
            List<String> keywords = parseKeywords(requestVo.getQueryValue());
            Map<String, Object> countResult = odsAppLogMapper.countLogsByKeywords(requestVo.getApp(), requestVo.getHost(), requestVo.getInstance(), logLevels, startTime, endTime, keywords);
            result.totalCount = ((Number) countResult.get("count")).intValue();
            Integer host_instance_max_length = ((Number) countResult.get("host_instance_max_length")).intValue();
            if (limit > 0) {
                result.logs  = odsAppLogMapper.selectLogsByKeywords(requestVo.getApp(), requestVo.getHost(), requestVo.getInstance(), logLevels, startTime, endTime, keywords, limit, host_instance_max_length);
            }
        }

        return result;
    }

    /**
     * 日志查询结果封装类
     */
    private static class LogQueryResult {
        List<String> logs = Collections.emptyList();
        int totalCount = 0;
    }

    /**
     * 获取应用列表
     *
     * @return ResponseVo<List < String>>
     */
    public ResponseVo<List<String>> getAppList() {
        List<String> apps = odsAppLogMapper.selectApps();
        return ok(apps);
    }

    /**
     * 获取主机列表
     *
     * @param app 应用名
     * @return ResponseVo<List < String>>
     */
    public ResponseVo<List<String>> getHostList(String app) {
        List<String> hosts = odsAppLogMapper.selectHosts(app);
        return ok(hosts);
    }

    /**
     * 获取实例列表
     *
     * @param app  应用名
     * @param host 主机名
     * @return ResponseVo<List < String>>
     */
    public ResponseVo<List<String>> getInstanceList(String app, String host) {
        List<String> instances = odsAppLogMapper.selectInstances(app, host);
        return ok(instances);
    }


    public List<String> parseKeywords(String keywordString) {
        if (keywordString == null || keywordString.isEmpty()) {
            return Collections.emptyList();
        }

        return Arrays.stream(keywordString.split(Constant.DOUBLE_AMPERSAND))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }

}