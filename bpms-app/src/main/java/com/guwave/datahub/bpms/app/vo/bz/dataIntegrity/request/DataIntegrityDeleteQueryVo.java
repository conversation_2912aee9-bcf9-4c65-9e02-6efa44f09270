package com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.request;


import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@EqualsAndHashCode(callSuper = false)
@Data
@Schema(title = "数据完整性删除记录查询条件")
public class DataIntegrityDeleteQueryVo extends PageableRequestVo {


    @Schema(description = "subCustomer列表", example = "[\"A\",\"B\"]")
    private List<String> subCustomerList;

    @Schema(description = "文件名", example = "a.stsf.zip")
    private String fileName;

    @Schema(description = "删除人列表", example = "[\"user_1\",\"user_2\"]")
    private List<String> deleteByList;

    public void validCondition() {
        subCustomerList = convertEmpty(subCustomerList);
        fileName = convertEmpty(fileName);
        deleteByList = convertEmpty(deleteByList);
    }


    private String convertEmpty(String str) {
        if (StringUtils.isEmpty(str)) {
            return null;
        } else {
            return str;
        }
    }

    private List<String> convertEmpty(List<String> strs) {
        if (CollectionUtils.isEmpty(strs)) {
            return null;
        } else {
            return strs;
        }
    }
}
