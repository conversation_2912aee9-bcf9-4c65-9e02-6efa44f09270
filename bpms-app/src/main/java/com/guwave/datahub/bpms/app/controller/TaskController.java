package com.guwave.datahub.bpms.app.controller;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.License;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.service.bz.TaskService;
import com.guwave.datahub.bpms.app.vo.bz.task.request.*;
import com.guwave.datahub.bpms.app.vo.bz.task.response.DmtAppDetailVo;
import com.guwave.datahub.bpms.app.vo.bz.task.response.DwTaskDetailVo;
import com.guwave.datahub.bpms.app.vo.bz.task.response.DwTaskVo;
import com.guwave.datahub.bpms.app.vo.bz.task.response.TaskDropDownVo;
import com.guwave.datahub.bpms.app.vo.bz.task.response.YmsTaskVo;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Min;
import java.util.List;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * TaskController
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2024-02-26 11:37:45
 */
@Log
@Auth
//@License
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("task")
@Tag(name = "TaskControllerAPI", description = "任务管理接口",
        externalDocs = @ExternalDocumentation(description = "需求文档",
                url = "https://ohjj5az3be.feishu.cn/wiki/LZtXwDP09iPwcok0oQgc4PdMnFe?fromTtSwitch=1")
)
public class TaskController {

    private final TaskService taskService;

    @Operation(
            summary = "DW任务管理 过滤条件"
    )
    @PostMapping("dwTaskDropDownFilter")
    public ResponseVo<List<TaskDropDownVo>> dwTaskDropDownFilter(@RequestBody @Validated DwTaskDropDownFilterVo dwTaskDropDownFilterVo) {
        List<TaskDropDownVo> taskDropDownVos = this.taskService.dwTaskFilter(dwTaskDropDownFilterVo);
        return ResponseVo.ok(taskDropDownVos);
    }

    @Operation(
            summary = "DW任务管理 使用条件查询DW的task"
    )
    @PostMapping("queryDwTasks")
    public ResponseVo<PageableDataVo<DwTaskVo>> queryDwTasks(@RequestBody @Validated DwTaskQueryVo dwTaskQueryVo) {
        PageableDataVo<DwTaskVo> dwTaskVoPageableDataVo = this.taskService.queryDwTasks(dwTaskQueryVo);
        return ResponseVo.ok(dwTaskVoPageableDataVo);
    }

    @Operation(
            summary = "DW任务管理 根据id查询单个task详情"
    )
    @GetMapping("querySingDwTaskDetail")
    public ResponseVo<DwTaskDetailVo> querySingDwTaskDetail(@RequestParam("id") @Schema(example = "1") @Min(value = 1) Long id) {
        return ResponseVo.ok(this.taskService.querySingDwTaskDetail(id));
    }

    @Operation(
            summary = "DW任务管理 DW中重跑task"
    )
    @PostMapping("rerunDwTask")
    public ResponseVo<Void> rerunDwTask(@RequestBody @Validated DwTaskRerunVo dwTaskRerunVo) {
        this.taskService.rerunDwTask(dwTaskRerunVo);
        return ResponseVo.ok();
    }

    @Operation(
            summary = "DW任务管理 DW中reload task"
    )
    @GetMapping("reloadDwTask")
    public ResponseVo<Void> reloadDwTask(@RequestParam("id") @Schema(example = "1") @Min(value = 1) Long id) {
        this.taskService.reloadDwTask(id);
        return ResponseVo.ok();
    }

    @Operation(
            summary = "YMS任务管理 过滤条件"
    )
    @PostMapping("ymsTaskDropDownFilter")
    public ResponseVo<List<TaskDropDownVo>> ymsTaskDropDownFilter(@RequestBody @Validated YmsTaskDropDownFilterVo ymsTaskDropDownFilterVo) {
        return ResponseVo.ok(this.taskService.ymsTaskDropDownFilter(ymsTaskDropDownFilterVo));
    }

    @Operation(
            summary = "YMS任务管理 使用条件查询YMS的task"
    )
    @PostMapping("queryYmsTasks")
    public ResponseVo<PageableDataVo<YmsTaskVo>> queryYmsTasks(@RequestBody @Validated YmsTaskQueryVo ymsTaskQueryVo) {
        return ResponseVo.ok(this.taskService.queryYmsTasks(ymsTaskQueryVo));
    }


    @Operation(
            summary = "YMS任务管理 YMS重跑task"
    )
    @PostMapping("rerunYmsTask")
    public ResponseVo<Void> rerunYmsBatchTask(@RequestBody @Validated YmsTaskRerunVo ymsTaskRerunVo) {
        this.taskService.rerunYmsTask(ymsTaskRerunVo);
        return ResponseVo.ok();
    }

    @Operation(
            summary = "DMT任务管理 根据id查询单个task详情"
    )
    @GetMapping("querySingleDmtAppDetail")
    public ResponseVo<DmtAppDetailVo> querySingDmtAppDetail(@RequestParam("id") @Schema(example = "1") @Min(value = 1) Long id) {
        return ResponseVo.ok(this.taskService.querySingleDmtAppDetail(id));
    }

    @Operation(
            summary = "DMT任务管理 DMT重跑单个task"
    )
    @PostMapping("rerunDmtSingleTask")
    public ResponseVo<Void> rerunDmtSingleTask(@RequestBody @Validated DmtTaskRerunSingleVo dmtTaskRerunSingleVo) {
        this.taskService.rerunSingleDmtTask(dmtTaskRerunSingleVo);
        return ResponseVo.ok();
    }
}
