package com.guwave.datahub.bpms.app.service.dailyReport.impl;

import com.alibaba.fastjson2.JSON;
import com.guwave.datahub.bpms.app.service.dailyReport.CommonService;
import com.guwave.datahub.bpms.app.util.DateUtil;
import com.guwave.datahub.bpms.app.util.PdfUtil;
import com.guwave.datahub.bpms.common.constant.ChartData;
import com.guwave.datahub.bpms.common.constant.ChartType;
import com.guwave.datahub.bpms.common.constant.DataCode;
import com.guwave.datahub.bpms.common.constant.ReportModule;
import com.guwave.datahub.bpms.dao.domain.replay.DataReplayRecord;
import com.guwave.datahub.bpms.dao.repository.bpms.DailyReportResultRepository;
import com.guwave.datahub.bpms.dao.repository.bpms.DataReplayRecordRepository;
import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.ReplayDetail;
import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.ReplayTaskResult;
import org.apache.commons.math3.util.MathUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.PERCENT;

@Service
public class ReplayModuleService implements CommonService {
    @Autowired
    private DailyReportResultRepository dailyReportResultRepository;

    @Autowired
    private DataReplayRecordRepository dataReplayRecordRepository;

    private final ReplayTaskResult result = new ReplayTaskResult();

    private boolean initFlag = false;

    private final List<ChartData> replayTaskChartData = new ArrayList<>();

    private void init() {
        String day = DateUtil.getDayBefortDays(1);
        List<DataReplayRecord> replayRecords = dataReplayRecordRepository.findAllByCreateTimeBetween(DateUtil.getDateFormattedStartTimeBeforeDays(1), DateUtil.getDateFormattedStartTimeBeforeDays(0));
        result.setTotalFileCnt(replayRecords.stream().mapToLong(DataReplayRecord::getReplayFileCnt).sum());
        result.setTotalTaskCnt(replayRecords.stream().mapToLong(DataReplayRecord::getTotalTaskCnt).sum());
        result.setSuccessTaskCnt(replayRecords.stream().mapToLong(DataReplayRecord::getSuccessTaskCnt).sum());

        List<ReplayDetail> replayDetailByDay = dataReplayRecordRepository.findReplayDetailByDay(DateUtil.getStringFormattedStartTimeBeforeDays(7), DateUtil.getStringFormattedStartTimeBeforeDays(1))
                .stream().map(this::buildReplayDetail).collect(Collectors.toList());
        replayTaskChartData.addAll(replayDetailByDay.stream().map(t -> new ChartData(t.getDay(), t.getSuccessTaskCnt(), "重播成功的任务数")).collect(Collectors.toList()));
        replayTaskChartData.addAll(replayDetailByDay.stream().map(t -> new ChartData(t.getDay(), t.getTotalTaskCnt(), "重播的总任务数")).collect(Collectors.toList()));
        replayTaskChartData.addAll(replayDetailByDay.stream().map(t -> new ChartData(t.getDay(), t.getTotalFileCnt(), "重播的总文件数")).collect(Collectors.toList()));
        replayTaskChartData.add(new ChartData(day, result.getSuccessTaskCnt(), "重播成功的任务数"));
        replayTaskChartData.add(new ChartData(day, result.getTotalTaskCnt(), "重播的总任务数"));
        replayTaskChartData.add(new ChartData(day, result.getTotalFileCnt(), "重播的总文件数"));
    }

    public void generateReplayDetail(PdfUtil pdfUtil) throws IOException {
        if (!initFlag) {
            init();
            initFlag = true;
        }
        saveDailyReportResult(JSON.toJSONString(result), ReportModule.REPLAY, DataCode.REPLAY_TASK);
        pdfUtil.addBodyText("总共重播的任务数：" + result.getTotalTaskCnt());
        pdfUtil.addBodyText("重播成功的任务数：" + result.getSuccessTaskCnt() + " 成功率：" + (result.getTotalTaskCnt() != 0 ? result.getSuccessTaskCnt() * 1.0 / result.getTotalTaskCnt() * 100 + PERCENT : "NA"));
        pdfUtil.addBodyText("总共重播的文件数：" + result.getTotalFileCnt());
    }

    public void generateReplayDetailChart(PdfUtil pdfUtil) throws IOException {
        if (!initFlag) {
            init();
            initFlag = true;
        }
        pdfUtil.addChart(replayTaskChartData, ChartType.LINE, "近7天数据重播统计", "时间", "大小", true);

    }

    private ReplayDetail buildReplayDetail(Object[] objects) {
        return new ReplayDetail(
                ((String) objects[0]),
                ((Number) objects[1]).longValue(),
                ((Number) objects[2]).longValue(),
                ((Number) objects[3]).longValue()
        );
    }

    @Override
    public DailyReportResultRepository getDailyReportResultRepository() {
        return dailyReportResultRepository;
    }

    public void resetInitFlag() {
        initFlag = false;
    }
}
