package com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(title = "批次匹配详情,数据质量页面上方饼图")
public class DataAccuracyBatchMatchDetailVo {
    @Schema(title = "批次良率准确率")
    private BigDecimal yieldAccuracy;

    @Schema(title = "正常数据比例")
    private BigDecimal normalRate;

    @Schema(title = "批次良率匹配比例")
    private BigDecimal matchRate;

    @Schema(title = "mes数据入库比例")
    private BigDecimal mesInputRate;

    @Schema(title = "批次异常良率分布图")
    private List<PieItem> dataAccuracyCheckPieItems;

    @Schema(title = "测试文件数据流与MES数据流数据一致性")
    private List<PieItem> mesMatchTypePieItems;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class PieItem {
        @Schema(title = "类别")
        private String category;
        private BigDecimal rate;
    }

}
