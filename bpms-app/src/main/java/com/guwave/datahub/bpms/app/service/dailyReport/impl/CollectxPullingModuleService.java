package com.guwave.datahub.bpms.app.service.dailyReport.impl;

import com.alibaba.fastjson2.JSON;
import com.guwave.datahub.bpms.app.service.dailyReport.CommonService;
import com.guwave.datahub.bpms.app.util.*;
import com.guwave.datahub.bpms.app.vo.dailyReport.model.CollectSlowlyTask;
import com.guwave.datahub.bpms.common.constant.*;
import com.guwave.datahub.bpms.dao.repository.bpms.DailyReportResultRepository;
import com.guwave.datahub.bpms.dao.repository.dw.SftpBatchInfoRepository;
import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.CollectxTimeoutPullingResult;
import com.guwave.onedata.dataware.common.contant.BatchInfoStatus;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpBatchInfo;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputePool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

@Service
public class CollectxPullingModuleService implements CommonService {

    @Autowired
    private SftpBatchInfoRepository sftpBatchInfoRepository;

    @Autowired
    private HdfsUtil hdfsUtil;

    @Autowired
    private DailyReportResultRepository dailyReportResultRepository;

    /**
     * @param pdfUtil  pdfUtil
     * @param localDir localDir
     * @param hdfsDir  hdfsDir
     * @throws IOException IOException
     */
    public void generateCollectxPullingDetail(PdfUtil pdfUtil, String localDir, String hdfsDir) throws IOException {
        CollectxTimeoutPullingResult result = new CollectxTimeoutPullingResult();
        List<CollectSlowlyTask> collectSlowlyTasks = sftpBatchInfoRepository.findSlowlyTask(DateUtil.getStringFormattedStartTimeBeforeDays(1), DateUtil.getStringFormattedStartTimeBeforeDays(0))
                .stream().map(this::buildCollectSlowlyTask).collect(Collectors.toList());
        Map<Long, Long> timeoutRuleMap = collectSlowlyTasks
                .stream().collect(Collectors.groupingBy(CollectSlowlyTask::getCollectRuleId, Collectors.counting()))
                .entrySet().stream()
                .sorted(Collections.reverseOrder(Map.Entry.comparingByValue()))
                .limit(10)
                .collect(Collectors.toMap(Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldVal, newVal) -> oldVal,
                        HashMap::new
                ));

        result.setTimeoutBatchCnt((long) collectSlowlyTasks.size());
        result.setTimeoutRuleMap(timeoutRuleMap);
        saveDailyReportResult(JSON.toJSONString(result), ReportModule.COLLECTX_PULLING, DataCode.COLLECTX_PULLING);
        pdfUtil.addBodyText("collectx慢拉取任务数：" + result.getTimeoutBatchCnt());

        if (result.getTimeoutBatchCnt() > 0) {
            List<ChartData> timeoutRuleChartData = timeoutRuleMap.entrySet().stream().map(entry -> new ChartData(entry.getKey().toString(), entry.getValue(), "")).collect(Collectors.toList());
            pdfUtil.addChart(timeoutRuleChartData, ChartType.BAR, "collectx慢拉取任务数", "规则ID", "任务数");
        }

        // 生产csv文件并上传
        collectSlowlyTasks.sort(Comparator.comparingLong(CollectSlowlyTask::getCollectRuleId));
        String excelFile = FileUtil.getFileFullName(localDir, "collectx慢拉取明细" + UNDER_LINE + DateUtil.getDayBefortDays(1) + ".xlsx");
        ExcelUtil.exportDataWithDefaultStyle(Files.newOutputStream(Paths.get(excelFile)), collectSlowlyTasks, CollectSlowlyTask.class, null);
        hdfsUtil.uploadToHDFSByFileSystem(excelFile, hdfsDir, 1);
    }

    private CollectSlowlyTask buildCollectSlowlyTask(SftpBatchInfo sftpBatchInfo) {
        return new CollectSlowlyTask(
                sftpBatchInfo.getCollectRuleId(),
                sftpBatchInfo.getCustomer(),
                sftpBatchInfo.getFactory(),
                sftpBatchInfo.getFactorySite(),
                sftpBatchInfo.getFab(),
                sftpBatchInfo.getFabSite(),
                sftpBatchInfo.getTestArea() == null ? EMPTY : sftpBatchInfo.getTestArea().getArea(),
                sftpBatchInfo.getFtpIp(),
                sftpBatchInfo.getSftpFileLastMtime(),
                sftpBatchInfo.getBatchTotalLoadFiles(),
                sftpBatchInfo.getBatchSuccessLoadFiles(),
                sftpBatchInfo.getBatchStatus() == null ? EMPTY : sftpBatchInfo.getBatchStatus().getStatus(),
                sftpBatchInfo.getCollectRuleSnapshot(),
                sftpBatchInfo.getExceptionType() == null ? EMPTY : sftpBatchInfo.getExceptionType().getType(),
                sftpBatchInfo.getExceptionMessage(),
                sftpBatchInfo.getErrorMessage()
        );
    }

    @Override
    public DailyReportResultRepository getDailyReportResultRepository() {
        return dailyReportResultRepository;
    }
}
