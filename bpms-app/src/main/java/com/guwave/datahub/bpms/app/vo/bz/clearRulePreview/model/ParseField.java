package com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@AllArgsConstructor
@Getter
public enum ParseField {

    DEVICE_ID("deviceId", 1),
    TEST_AREA("testArea", 2),
    LOT_ID("lotId", 3),
    WAFER_ID("waferId", 4),
    ORIGIN_WAFER_ID("originWaferId", 5),
    WAFER_NO("waferNo", 6),
    LOT_TYPE("lotType", 7),
    OFFLINE_RETEST("offlineRetest", 8),
    INTERRUPT("interrupt", 9),
    DUP_RETEST("dupRetest", 10),
    BATCH_NUM("batchNum", 11),
    SBLOT_ID("sblotId", 12),
    TEST_COD("testCod", 13),
    TEST_STAGE("testStage", 14),
    START_T("startT", 15),
    FINISH_T("finishT", 16),
    POS_X("posX", 17),
    POS_Y("posY", 18),
    NOTCH("notch", 19),
    FLOOR_ID("floorId", 20),
    TEST_TEMPERATURE("testTemperature", 21),
    TESTER_NAME("testerName", 22),
    TESTER_TYPE("testerType", 23),
    PROBECARD_LOADBOARD_ID("probecardLoadboardId", 24),
    TEST_PROGRAM("testProgram", 25),
    TEST_PROGRAM_VERSION("testProgramVersion", 26),
    PKG_TYP("pkgTyp", 27),
    RETEST_BIN_NUM("retestBinNum", 28),
    PROCESS("process", 29),
    CONDITION_SET("conditionSet", 30),
    REAL_WAFER_ID("realWaferId", 31),
    ;

    private final String field;
    private final Integer order;

    private static final Map<String, ParseField> FIELD_MAP = Stream.of(ParseField.values()).collect(Collectors.toMap(ParseField::getField, Function.identity()));

    public static ParseField of(String field) {
        return FIELD_MAP.get(field);
    }

}
