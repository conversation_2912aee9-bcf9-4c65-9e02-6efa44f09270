package com.guwave.datahub.bpms.app.callback;

import com.alibaba.fastjson.JSON;
import com.guwave.datahub.bpms.app.scheduler.TaskSchedulerService;
import com.guwave.datahub.bpms.app.util.ComputeUtil;
import com.guwave.datahub.bpms.common.constant.ExceptionType;
import com.guwave.datahub.bpms.dao.domain.schedule.ScheduleTaskRecord;
import com.guwave.datahub.bpms.dao.repository.bpms.ScheduleTaskRecordRepository;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.next.compute.api.callback.ComputeCallback;
import com.guwave.onedata.next.compute.common.message.ComputeResultMessage;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

import static com.guwave.onedata.linkx.common.constant.Constant.ENTER;

@Component
public class SparkTaskCallback implements ComputeCallback {

    private static final Logger LOGGER = LoggerFactory.getLogger(SparkTaskCallback.class);
    @Autowired
    private ScheduleTaskRecordRepository scheduleTaskRecordRepository;

    @Autowired
    ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public void doCallback(ComputeResultMessage computeResultMessage) {
        ProcessStatus processStatus = ComputeUtil.convertProcessStatus(computeResultMessage.getProcessStatus());
        if (processStatus == ProcessStatus.PROCESSING) {
            LOGGER.info("computeResultMessage {} 忽略", JSON.toJSONString(computeResultMessage));
            return;
        }
        List<ScheduleTaskRecord> scheduleTaskRecords = scheduleTaskRecordRepository.findByProcessStatusAndUniqueId(ProcessStatus.PROCESSING, computeResultMessage.getUniqueId());
        // 应该只找到一条 ScheduleTaskRecord
        if (CollectionUtils.isEmpty(scheduleTaskRecords)) {
            LOGGER.info("computeResultMessage {} 没有对应的记录", JSON.toJSONString(computeResultMessage));
            return;
        } else if (scheduleTaskRecords.size() > 1) {
            LOGGER.info("computeResultMessage {} 找到多条记录", JSON.toJSONString(computeResultMessage));
            return;
        }

        ScheduleTaskRecord firstScheduleTask = scheduleTaskRecords.get(0);

        LOGGER.info("spark task 结束 开始处理：{}", JSON.toJSONString(computeResultMessage));
        Date now = new Date();
        firstScheduleTask.setEndTime(now)
                .setExecuteTime(now.getTime() - firstScheduleTask.getStartTime().getTime())
                .setProcessStatus(processStatus)
                .setExceptionType(processStatus == ProcessStatus.FAIL ? ExceptionType.EXECUTE_SPARK_TASK_EXCEPTION : null)
                .setErrorMessage(processStatus == ProcessStatus.FAIL ? computeResultMessage.getExceptionMessage() + ENTER + computeResultMessage.getErrorMessage() : null)
                .setUpdateUser(Constant.SYSTEM)
                .setUpdateTime(new Date());

        scheduleTaskRecordRepository.save(firstScheduleTask);

    }

    @Override
    public boolean isSupport(String computeCode) {
        return TaskSchedulerService.COMPUTE_CODE_SET.contains(computeCode);
    }
}
