package com.guwave.datahub.bpms.app.service.notification;

import com.guwave.datahub.bpms.app.annotation.FeignClient;
import com.guwave.datahub.bpms.app.vo.notification.NotificationResponseVo;
import feign.Headers;
import feign.Param;
import feign.RequestLine;

import java.io.File;
import java.util.List;


@FeignClient(name = "notificationWithAttachmentApi", url = "${license.baseUrl}", formEncode = true)
public interface NotificationWithAttachmentApi {

    @RequestLine("POST /notification/sendWithAttachment")
    @Headers("Content-Type: multipart/form-data")
    NotificationResponseVo<String> sendNotificationWithAttachment(
            @Param("form") String form,
            @Param("files") List<File> files
    );
}
