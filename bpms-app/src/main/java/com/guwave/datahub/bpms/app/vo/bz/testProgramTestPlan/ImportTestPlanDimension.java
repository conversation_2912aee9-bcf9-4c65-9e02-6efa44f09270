package com.guwave.datahub.bpms.app.vo.bz.testProgramTestPlan;

import java.util.Objects;

public class ImportTestPlanDimension {
    private String uploadType;
    private String testArea;
    private String factory;
    private String factorySite;
    private String deviceId;
    private String testStage;
    private String lotType;
    private String testProgram;

    public ImportTestPlanDimension() {
    }

    public ImportTestPlanDimension(String uploadType, String testArea, String factory, String factorySite, String deviceId, String testStage, String lotType, String testProgram) {
        this.uploadType = uploadType;
        this.testArea = testArea;
        this.factory = factory;
        this.factorySite = factorySite;
        this.deviceId = deviceId;
        this.testStage = testStage;
        this.lotType = lotType;
        this.testProgram = testProgram;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ImportTestPlanDimension that = (ImportTestPlanDimension) o;
        return Objects.equals(uploadType, that.uploadType) && Objects.equals(testArea, that.testArea) && Objects.equals(factory, that.factory) && Objects.equals(factorySite, that.factorySite) && Objects.equals(deviceId, that.deviceId) && Objects.equals(testStage, that.testStage) && Objects.equals(lotType, that.lotType) && Objects.equals(testProgram, that.testProgram);
    }

    @Override
    public int hashCode() {
        return Objects.hash(uploadType, testArea, factory, factorySite, deviceId, testStage, lotType, testProgram);
    }

    public String getUploadType() {
        return uploadType;
    }

    public ImportTestPlanDimension setUploadType(String uploadType) {
        this.uploadType = uploadType;
        return this;
    }

    public String getTestArea() {
        return testArea;
    }

    public ImportTestPlanDimension setTestArea(String testArea) {
        this.testArea = testArea;
        return this;
    }

    public String getFactory() {
        return factory;
    }

    public ImportTestPlanDimension setFactory(String factory) {
        this.factory = factory;
        return this;
    }

    public String getFactorySite() {
        return factorySite;
    }

    public ImportTestPlanDimension setFactorySite(String factorySite) {
        this.factorySite = factorySite;
        return this;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public ImportTestPlanDimension setDeviceId(String deviceId) {
        this.deviceId = deviceId;
        return this;
    }

    public String getTestStage() {
        return testStage;
    }

    public ImportTestPlanDimension setTestStage(String testStage) {
        this.testStage = testStage;
        return this;
    }

    public String getLotType() {
        return lotType;
    }

    public ImportTestPlanDimension setLotType(String lotType) {
        this.lotType = lotType;
        return this;
    }

    public String getTestProgram() {
        return testProgram;
    }

    public ImportTestPlanDimension setTestProgram(String testProgram) {
        this.testProgram = testProgram;
        return this;
    }

    @Override
    public String toString() {
        return "{" +
                "UPLOAD_TYPE='" + uploadType + '\'' +
                ", TEST_AREA='" + testArea + '\'' +
                ", FACTORY='" + factory + '\'' +
                ", FACTORY_SITE='" + factorySite + '\'' +
                ", DEVICE_ID='" + deviceId + '\'' +
                ", TEST_STAGE='" + testStage + '\'' +
                ", LOT_TYPE='" + lotType + '\'' +
                ", TEST_PROGRAM='" + testProgram + '\'' +
                '}';
    }
}
