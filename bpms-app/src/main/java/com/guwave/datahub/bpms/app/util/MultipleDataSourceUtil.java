package com.guwave.datahub.bpms.app.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.util.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

@Slf4j
@Component
public class MultipleDataSourceUtil {

    private final Map<String, Pair<EntityManager, Consumer<Runnable>>> entityManagerMap = new HashMap<>();

    @Value("${database.name.bpms}")
    private String bpmsDb;
    @Value("${database.name.dw}")
    private String onedataDb;
    @Value("${database.name.linkx}")
    private String linkxDb;
    @Value("${database.name.compute}")
    private String computeDb;

    @PersistenceContext(unitName = "primaryPersistenceUnit")
    private EntityManager entityManagerPrimary;
    @PersistenceContext(unitName = "secondaryPersistenceUnit")
    private EntityManager entityManagerSecondary;
    @PersistenceContext(unitName = "tertiaryPersistenceUnit")
    private EntityManager entityManagerTertiary;
    @PersistenceContext(unitName = "quaternaryPersistenceUnit")
    private EntityManager entityManagerQuaternary;
    @Autowired
    private MultipleDataSourceUtil multipleDataSourceUtil;


    @PostConstruct
    public void init() {
        entityManagerMap.put(bpmsDb, Pair.create(entityManagerPrimary, runable -> multipleDataSourceUtil.primaryExecute(runable)));
        entityManagerMap.put(onedataDb, Pair.create(entityManagerSecondary, runable -> multipleDataSourceUtil.secondaryExecute(runable)));
        entityManagerMap.put(linkxDb, Pair.create(entityManagerTertiary, runable -> multipleDataSourceUtil.tertiaryExecute(runable)));
        entityManagerMap.put(computeDb, Pair.create(entityManagerQuaternary, runable -> multipleDataSourceUtil.quaternaryExecute(runable)));
    }

    public EntityManager getEntityManager(String db) {
        return entityManagerMap.get(db).getFirst();
    }

    public void executeUpdate(String db, Consumer<EntityManager> updateConsumer) {
        Pair<EntityManager, Consumer<Runnable>> entityManagerConsumerPair = entityManagerMap.get(db);
        entityManagerConsumerPair.getSecond().accept(() -> updateConsumer.accept(entityManagerConsumerPair.getFirst()));
    }

    @Transactional("transactionManagerPrimary")
    public void primaryExecute(Runnable runnable) {
        runnable.run();
    }

    @Transactional("transactionManagerSecondary")
    public void secondaryExecute(Runnable runnable) {
        runnable.run();
    }

    @Transactional("transactionManagerTertiary")
    public void tertiaryExecute(Runnable runnable) {
        runnable.run();
    }

    @Transactional("transactionManagerQuaternary")
    public void quaternaryExecute(Runnable runnable) {
        runnable.run();
    }

}
