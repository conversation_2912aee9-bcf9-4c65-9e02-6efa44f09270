package com.guwave.datahub.bpms.app.controller;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.service.bz.RetryFailRecordService;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Log
@Auth
//@License
@Validated
@Slf4j
@RestController
@AllArgsConstructor
@Tag(name = "RetryFailRecordControllerAPI", description = "失败数据重跑接口",
        externalDocs = @ExternalDocumentation(description = "需求文档",
                url = "")
)
@RequestMapping("retryFailRecord")
public class RetryFailRecordController {

    private RetryFailRecordService retryFailRecordService;

    @Auth
    @Operation(summary = "重跑失败的任务")
    @PostMapping("allRetryFailRecords")
    public ResponseVo<Void> allRetryFailRecords() {
        retryFailRecordService.saveFailRecords();
        retryFailRecordService.allRetryFailRecords();
        return ResponseVo.ok();
    }

}
