package com.guwave.datahub.bpms.app.service.bz;

import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.manage.TestProgramBinDefinitionManage;
import com.guwave.datahub.bpms.app.vo.bz.binDefinition.model.BinDefinitionDetailVo;
import com.guwave.datahub.bpms.app.vo.bz.binDefinition.request.*;
import com.guwave.datahub.bpms.app.vo.bz.binDefinition.response.BinDefinitionImportResultVo;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.common.constant.BinType;
import com.guwave.datahub.bpms.dao.repository.dw.mapper.BinDefinitionMapper;
import com.guwave.datahub.bpms.dao.vo.bpms.binDefinition.BinDefinitionDetail;
import com.guwave.datahub.bpms.dao.vo.bpms.binDefinition.BinDefinitionDropDown;
import com.guwave.datahub.bpms.dao.vo.bpms.binDefinition.ConfiguredTestProgramVo;
import com.guwave.datahub.bpms.dao.vo.bpms.binDefinition.UnconfiguredTestProgramVo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.TestProgramBinDefinition;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Test Program Bin Definition Service
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class BinDefinitionService {

    @Autowired
    private BinDefinitionMapper binDefinitionMapper;
    @Autowired
    private TestProgramBinDefinitionManage testProgramBinDefinitionManage;


    /**
     * 搜索测试程序
     */
    public List<BinDefinitionDropDown> searchTestPrograms(BinDefinitionDropDownQueryVo queryVo) {
        String customer = RequestContext.getCustomer();
        String subCustomer = RequestContext.getSubCustomer();

        // 调用mapper搜索测试程序
        return binDefinitionMapper.searchTestPrograms(customer, subCustomer, queryVo.getFilterField())
                .stream().map(t -> new BinDefinitionDropDown(t, t)).collect(Collectors.toList());
    }

    /**
     * 获取已配置的测试程序列表
     */
    public PageableDataVo<ConfiguredTestProgramVo> getConfiguredTestPrograms(BinDefinitionQueryVo queryVo) {
        queryVo.validConfiguredCondition();

        // 查询已配置的测试程序统计信息
        List<ConfiguredTestProgramVo> dataList = binDefinitionMapper.findConfiguredTestPrograms(
                RequestContext.getCustomer(), RequestContext.getSubCustomer(), queryVo.getTestPrograms(),
                queryVo.getSortField(), queryVo.getSortDirection(),
                queryVo.getJpaPageIndex() * queryVo.getPageSize().longValue(), queryVo.getPageSize().longValue()
        );

        // 查询总数
        Long totalCount = binDefinitionMapper.countConfiguredTestPrograms(
                RequestContext.getCustomer(), RequestContext.getSubCustomer(), queryVo.getTestPrograms()
        );

        return PageableDataVo.of(new PageImpl<>(dataList, PageRequest.of(queryVo.getJpaPageIndex(), queryVo.getPageSize()), totalCount));
    }

    /**
     * 获取未配置的测试程序列表
     */
    public PageableDataVo<UnconfiguredTestProgramVo> getUnconfiguredTestPrograms(BinDefinitionQueryVo queryVo) {
        queryVo.validUnconfiguredCondition();

        // 查询未配置的测试程序
        List<UnconfiguredTestProgramVo> dataList = binDefinitionMapper.findUnconfiguredTestPrograms(
                RequestContext.getCustomer(), RequestContext.getSubCustomer(), queryVo.getTestPrograms(),
                queryVo.getSortField(), queryVo.getSortDirection(),
                queryVo.getJpaPageIndex() * queryVo.getPageSize().longValue(), queryVo.getPageSize().longValue()
        );

        // 查询总数
        Long totalCount = binDefinitionMapper.countUnconfiguredTestPrograms(
                RequestContext.getCustomer(), RequestContext.getSubCustomer(), queryVo.getTestPrograms()
        );

        return PageableDataVo.of(new PageImpl<>(dataList, PageRequest.of(queryVo.getJpaPageIndex(), queryVo.getPageSize()), totalCount));
    }

    /**
     * 获取测试程序的bin定义详情
     */
    public List<BinDefinitionDetail> getBinDefinitions(String testProgram) {
        if (testProgram == null) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "测试程序名称");
        }

        List<BinDefinitionDetail> definitions = binDefinitionMapper
                .findByTestProgramAndPermissions(
                        RequestContext.getCustomer(), RequestContext.getSubCustomer(),
                        Collections.singletonList(testProgram), "testProgram", "asc");

        return definitions;
    }

    /**
     * 保存bin定义
     */
    public void saveBinDefinitions(BinDefinitionSaveVo saveVo) {
        if (saveVo == null || saveVo.getTestProgram() == null) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "测试程序名称");
        }

        // 验证bin定义
        validateBinDefinitions(saveVo);

        // 获取用户权限
        String customer = RequestContext.getCustomer();
        String subCustomer = RequestContext.getSubCustomer();

        // 保存新的bin定义
        List<TestProgramBinDefinition> entities = new ArrayList<>();

        // 添加HBIN定义
        for (BinDefinitionDetailVo hbin : saveVo.getHbinList()) {
            TestProgramBinDefinition entity = convertToEntity(hbin, saveVo.getTestProgram(),
                    BinType.HBIN, customer, subCustomer);
            entities.add(entity);
        }

        // 添加SBIN定义
        for (BinDefinitionDetailVo sbin : saveVo.getSbinList()) {
            TestProgramBinDefinition entity = convertToEntity(sbin, saveVo.getTestProgram(),
                    BinType.SBIN, customer, subCustomer);
            entities.add(entity);
        }

        testProgramBinDefinitionManage.deleteAndInsert(customer, subCustomer, saveVo.getTestProgram(), false, entities);
        log.info("成功保存测试程序 {} 的bin定义，共 {} 条记录", saveVo.getTestProgram(), entities.size());
    }

    /**
     * 删除测试程序的bin定义
     */
    public void deleteTestProgramBinDefinitions(BinDefinitionDeleteVo deleteVo) {
        if (deleteVo == null || deleteVo.getTestProgram() == null) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "测试程序名称");
        }

        // 获取用户权限
        String customer = RequestContext.getCustomer();
        String subCustomer = RequestContext.getSubCustomer();

        // 删除bin定义
        testProgramBinDefinitionManage.deleteAndInsert(customer, subCustomer, deleteVo.getTestProgram(), false, null);
        log.info("成功删除测试程序 {} 的bin定义", deleteVo.getTestProgram());
    }

    /**
     * 验证bin定义
     */
    private void validateBinDefinitions(BinDefinitionSaveVo saveVo) {
        // 验证HBIN和SBIN都不为空
        if (CollectionUtils.isEmpty(saveVo.getHbinList())) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "HBIN定义");
        }
        if (CollectionUtils.isEmpty(saveVo.getSbinList())) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "SBIN定义");
        }

        // 验证HBIN编号唯一性
        Set<Long> hbinNums = new HashSet<>();
        for (BinDefinitionDetailVo hbin : saveVo.getHbinList()) {
            if (hbin.getBinNum() == null || hbin.getBinNum() <= 0) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "HBIN编号必须为正数");
            }
            if (!hbinNums.add(hbin.getBinNum())) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "HBIN编号重复: " + hbin.getBinNum());
            }
            if (StringUtils.isBlank(hbin.getBinName())) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "HBIN名称");
            }
            if (hbin.getBinPf() == null) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "HBIN P/F");
            }
        }

        // 验证SBIN编号唯一性
        Set<Long> sbinNums = new HashSet<>();
        for (BinDefinitionDetailVo sbin : saveVo.getSbinList()) {
            if (sbin.getBinNum() == null || sbin.getBinNum() <= 0) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "SBIN编号必须为正数");
            }
            if (!sbinNums.add(sbin.getBinNum())) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "SBIN编号重复: " + sbin.getBinNum());
            }
            if (StringUtils.isBlank(sbin.getBinName())) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "SBIN名称");
            }
            if (sbin.getBinPf() == null) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "SBIN P/F");
            }
        }
    }

    /**
     * 转换VO为实体
     */
    private TestProgramBinDefinition convertToEntity(BinDefinitionDetailVo vo, String testProgram,
                                                     BinType binType, String customer, String subCustomer) {
        TestProgramBinDefinition entity = new TestProgramBinDefinition();
        entity.setTestProgram(testProgram);
        entity.setBinType(binType.getType());
        entity.setBinNum(vo.getBinNum());
        entity.setBinName(vo.getBinName());
        entity.setBinPf(vo.getBinPf().getPf());
        entity.setCustomer(customer);
        entity.setSubCustomer(subCustomer);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        entity.setCreateUser(RequestContext.getUserName());
        entity.setUpdateUser(RequestContext.getUserName());
        return entity;
    }

    /**
     * 导出bin定义到CSV
     */
    public void exportBinDefinitions(HttpServletResponse response, BinDefinitionQueryVo queryVo) {
        try {
            // 获取用户权限
            String customer = RequestContext.getCustomer();
            String subCustomer = RequestContext.getSubCustomer();

            // 查询所有bin定义
            List<BinDefinitionDetail> definitions;
            if (queryVo.getExportConfiguredFlag() == 1) {
                queryVo.validConfiguredCondition();
                definitions = binDefinitionMapper
                        .findByTestProgramAndPermissions(customer, subCustomer, queryVo.getTestPrograms(), queryVo.getSortField(), queryVo.getSortDirection());
            } else {
                queryVo.validUnconfiguredCondition();
                definitions = binDefinitionMapper.findExportUnconfigured(customer, subCustomer, queryVo.getTestPrograms(), queryVo.getSortField(), queryVo.getSortDirection());
            }


            // 设置响应头
            String fileName = "bin_definition_export_" + System.currentTimeMillis() + ".csv";
            response.setContentType("text/csv;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            response.setCharacterEncoding("UTF-8");

            // 写入CSV内容
            StringBuilder csvContent = new StringBuilder();
            csvContent.append("\uFEFF"); // UTF-8 BOM
            csvContent.append("Test Program,Bin Type,Bin Num,Bin Name,Bin PF\n");

            for (BinDefinitionDetail vo : definitions) {
                csvContent.append(escapeCSV(vo.getTestProgram())).append(",")
                        .append(escapeCSV(vo.getBinType())).append(",")
                        .append(vo.getBinNum() == null ? "" : vo.getBinNum()).append(",")
                        .append(escapeCSV(vo.getBinName())).append(",")
                        .append(escapeCSV(vo.getBinPf())).append("\n");
            }

            response.getWriter().write(csvContent.toString());
            response.getWriter().flush();

            log.info("成功导出bin定义，共 {} 条记录", definitions.size());

        } catch (IOException e) {
            log.error("导出bin定义失败", e);
            throw new BpmsException(e, ResponseCode.Business.BIN_DEFINITION_EXPORT_ERROR, e.getMessage());
        }
    }

    /**
     * CSV字段转义
     */
    private String escapeCSV(String value) {
        if (value == null) {
            return "";
        }
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        return value;
    }


    /**
     * 导入bin定义
     */
    public BinDefinitionImportResultVo importBinDefinitions(MultipartFile file, BinDefinitionImportVo importVo) {
        if (file == null || file.isEmpty()) {
            throw new BpmsException(ResponseCode.Business.BIN_DEFINITION_IMPORT_FILE_EMPTY);
        }

        if (importVo == null || importVo.getImportMode() == null) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "导入模式");
        }

        try {
            // 获取用户权限
            String customer = RequestContext.getCustomer();
            String subCustomer = RequestContext.getSubCustomer();

            // 解析文件内容
            List<BinDefinitionImportData> importDataList = parseImportFile(file);

            // 验证导入数据
            validateImportData(importDataList);

            // 转换并保存数据
            List<TestProgramBinDefinition> entities = importDataList.stream()
                    .map(data -> convertImportDataToEntity(data, customer, subCustomer))
                    .collect(Collectors.toList());

            if (importVo.getImportMode() == BinDefinitionImportVo.ImportMode.ADD_OR_OVERWRITE) {
                // 新增或覆盖模式：按测试程序分组处理
                Map<String, List<TestProgramBinDefinition>> groupedByProgram = entities.stream()
                        .collect(Collectors.groupingBy(TestProgramBinDefinition::getTestProgram));

                for (Map.Entry<String, List<TestProgramBinDefinition>> entry : groupedByProgram.entrySet()) {
                    String testProgram = entry.getKey();
                    // 保存新定义
                    if (!entry.getValue().isEmpty()) {
                        testProgramBinDefinitionManage.deleteAndInsert(customer, subCustomer, testProgram, false, entry.getValue());
                    }
                }
            } else {
                // 清除并全量导入模式：直接保存所有数据
                if (!entities.isEmpty()) {
                    testProgramBinDefinitionManage.deleteAndInsert(customer, subCustomer, null, true, entities);
                }
            }

            log.info("成功导入bin定义，共 {} 条记录，模式：{}", entities.size(), importVo.getImportMode());

            return new BinDefinitionImportResultVo(
                    importDataList.size(),
                    entities.size(),
                    0,
                    importVo.getImportMode().name(),
                    "导入成功"
            );

        } catch (Exception e) {
            log.error("导入bin定义失败", e);
            throw new BpmsException(e, ResponseCode.Business.BIN_DEFINITION_IMPORT_FILE_FORMAT_ERROR, e.getMessage());
        }
    }

    /**
     * 解析导入文件
     */
    private List<BinDefinitionImportData> parseImportFile(MultipartFile file) throws IOException {
        List<BinDefinitionImportData> result = new ArrayList<>();
        String fileName = file.getOriginalFilename();

        if (fileName == null) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "文件名");
        }

        String fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();

        if ("csv".equals(fileExtension)) {
            result = parseCSVFile(file);
        } else if ("xlsx".equals(fileExtension) || "xls".equals(fileExtension)) {
            result = parseExcelFile(file);
        } else {
            throw new BpmsException(ResponseCode.Common.FILE_TYPE_NOT_SUPPORT, fileExtension);
        }

        return result;
    }

    /**
     * 解析CSV文件
     */
    private List<BinDefinitionImportData> parseCSVFile(MultipartFile file) throws IOException {
        List<BinDefinitionImportData> result = new ArrayList<>();
        String content = new String(file.getBytes(), "UTF-8");

        // 移除BOM
        if (content.startsWith("\uFEFF")) {
            content = content.substring(1);
        }

        String[] lines = content.split("\n");
        if (lines.length < 2) {
            throw new BpmsException(ResponseCode.Common.FILE_PARSE_FAIL, "文件内容为空或格式错误");
        }

        // 验证表头
        String header = lines[0].trim();
        if (!"Test Program,Bin Type,Bin Num,Bin Name,Bin PF".equals(header)) {
            throw new BpmsException(ResponseCode.Common.FILE_PARSE_FAIL, "CSV文件表头格式错误");
        }

        // 解析数据行
        for (int i = 1; i < lines.length; i++) {
            String line = lines[i].trim();
            if (StringUtils.isBlank(line)) {
                continue;
            }

            try {
                BinDefinitionImportData data = parseCSVLine(line, i + 1);
                result.add(data);
            } catch (Exception e) {
                throw new BpmsException(e, ResponseCode.Common.FILE_PARSE_FAIL, "第 " + (i + 1) + " 行数据解析失败");
            }
        }

        return result;
    }

    /**
     * 解析CSV行
     */
    private BinDefinitionImportData parseCSVLine(String line, int lineNumber) {
        String[] fields = line.split(",", -1);
        if (fields.length != 5) {
            throw new RuntimeException("列数不正确，应为5列");
        }

        BinDefinitionImportData data = new BinDefinitionImportData();
        data.setLineNumber(lineNumber);
        data.setTestProgram(fields[0].trim());
        data.setBinType(fields[1].trim());

        try {
            data.setBinNum(Long.parseLong(fields[2].trim()));
        } catch (NumberFormatException e) {
            throw new RuntimeException("Bin Num格式错误：" + fields[2]);
        }

        data.setBinName(fields[3].trim());
        data.setBinPf(fields[4].trim());

        return data;
    }

    /**
     * 解析Excel文件
     */
    private List<BinDefinitionImportData> parseExcelFile(MultipartFile file) throws IOException {
        // 这里可以使用Apache POI来解析Excel文件
        // 为了简化，暂时抛出异常提示用户使用CSV格式
        throw new BpmsException(ResponseCode.Common.FILE_TYPE_NOT_SUPPORT, "Excel");
    }

    /**
     * 验证导入数据
     */
    private void validateImportData(List<BinDefinitionImportData> importDataList) {
        if (CollectionUtils.isEmpty(importDataList)) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "导入数据");
        }

        List<String> errors = new ArrayList<>();

        for (BinDefinitionImportData data : importDataList) {
            // 验证必填字段
            if (data.getTestProgram() == null) {
                errors.add("第 " + data.getLineNumber() + " 行：Test Program不能为null");
            }
            if (StringUtils.isBlank(data.getBinType())) {
                errors.add("第 " + data.getLineNumber() + " 行：Bin Type不能为空");
            }
            if (data.getBinNum() == null || data.getBinNum() <= 0) {
                errors.add("第 " + data.getLineNumber() + " 行：Bin Num必须为正数");
            }
            if (StringUtils.isBlank(data.getBinName())) {
                errors.add("第 " + data.getLineNumber() + " 行：Bin Name不能为空");
            }
            if (StringUtils.isBlank(data.getBinPf())) {
                errors.add("第 " + data.getLineNumber() + " 行：Bin PF不能为空");
            }

            // 验证枚举值
            if (StringUtils.isNotBlank(data.getBinType()) &&
                    !"HBIN".equals(data.getBinType()) && !"SBIN".equals(data.getBinType())) {
                errors.add("第 " + data.getLineNumber() + " 行：Bin Type必须为HBIN或SBIN");
            }
            if (StringUtils.isNotBlank(data.getBinPf()) &&
                    !"P".equals(data.getBinPf()) && !"F".equals(data.getBinPf())) {
                errors.add("第 " + data.getLineNumber() + " 行：Bin PF必须为P或F");
            }
        }

        if (!errors.isEmpty()) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, String.join("; ", errors));
        }

        // 验证同一测试程序下同一bin类型的bin编号唯一性
        Map<String, Set<Long>> hbinNums = new HashMap<>();
        Map<String, Set<Long>> sbinNums = new HashMap<>();

        for (BinDefinitionImportData data : importDataList) {
            String key = data.getTestProgram();
            if ("HBIN".equals(data.getBinType())) {
                Set<Long> nums = hbinNums.computeIfAbsent(key, k -> new HashSet<>());
                if (!nums.add(data.getBinNum())) {
                    errors.add("测试程序 " + key + " 的HBIN编号 " + data.getBinNum() + " 重复");
                }
            } else if ("SBIN".equals(data.getBinType())) {
                Set<Long> nums = sbinNums.computeIfAbsent(key, k -> new HashSet<>());
                if (!nums.add(data.getBinNum())) {
                    errors.add("测试程序 " + key + " 的SBIN编号 " + data.getBinNum() + " 重复");
                }
            }
        }

        if (!errors.isEmpty()) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, String.join("; ", errors));
        }
    }

    /**
     * 转换导入数据为实体
     */
    private TestProgramBinDefinition convertImportDataToEntity(BinDefinitionImportData data,
                                                               String customer, String subCustomer) {
        TestProgramBinDefinition entity = new TestProgramBinDefinition();
        entity.setTestProgram(data.getTestProgram());
        entity.setBinType(data.getBinType());
        entity.setBinNum(data.getBinNum());
        entity.setBinName(data.getBinName());
        entity.setBinPf(data.getBinPf());
        entity.setCustomer(customer);
        entity.setSubCustomer(subCustomer);
        return entity;
    }

    /**
     * 导入数据内部类
     */
    private static class BinDefinitionImportData {
        private int lineNumber;
        private String testProgram;
        private String binType;
        private Long binNum;
        private String binName;
        private String binPf;

        // Getters and Setters
        public int getLineNumber() {
            return lineNumber;
        }

        public void setLineNumber(int lineNumber) {
            this.lineNumber = lineNumber;
        }

        public String getTestProgram() {
            return testProgram;
        }

        public void setTestProgram(String testProgram) {
            this.testProgram = testProgram;
        }

        public String getBinType() {
            return binType;
        }

        public void setBinType(String binType) {
            this.binType = binType;
        }

        public Long getBinNum() {
            return binNum;
        }

        public void setBinNum(Long binNum) {
            this.binNum = binNum;
        }

        public String getBinName() {
            return binName;
        }

        public void setBinName(String binName) {
            this.binName = binName;
        }

        public String getBinPf() {
            return binPf;
        }

        public void setBinPf(String binPf) {
            this.binPf = binPf;
        }
    }

    /**
     * 转换实体为VO
     */
    private BinDefinitionDetailVo convertToDetailVo(TestProgramBinDefinition entity) {
        BinDefinitionDetailVo vo = new BinDefinitionDetailVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    /**
     * 导出模板
     */
    public void exportTemplate(HttpServletResponse response) {
        try {
            // 设置响应头
            response.setContentType("text/csv;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment; filename=bin_definition_template.csv");

            // 创建CSV内容
            StringBuilder csvContent = new StringBuilder();
            csvContent.append("Test Program,Bin Type,Bin Num,Bin Name,Bin PF\n");
            csvContent.append("EXAMPLE_PROGRAM,HBIN,1,Pass Bin,P\n");
            csvContent.append("EXAMPLE_PROGRAM,HBIN,2,Fail Bin,F\n");
            csvContent.append("EXAMPLE_PROGRAM,SBIN,1,Good Die,P\n");
            csvContent.append("EXAMPLE_PROGRAM,SBIN,2,Bad Die,F\n");

            // 写入响应
            response.getWriter().write(csvContent.toString());
            response.getWriter().flush();

        } catch (IOException e) {
            log.error("导出模板失败", e);
            throw new BpmsException(e, ResponseCode.Business.BIN_DEFINITION_EXPORT_ERROR, e.getMessage());
        }
    }
}