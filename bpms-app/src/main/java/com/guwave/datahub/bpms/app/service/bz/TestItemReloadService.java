package com.guwave.datahub.bpms.app.service.bz;

import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.vo.bz.testItemReload.CalTestItemWaferRequestVo;
import com.guwave.onedata.dataware.bridge.api.iface.ICalculateTestItemRpcService;
import com.guwave.onedata.dataware.bridge.api.vo.WaferVo;
import com.guwave.onedata.dataware.bridge.api.vo.request.CalTestItemWaferVo;
import com.guwave.onedata.dataware.common.contant.TestArea;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static com.guwave.datahub.bpms.app.web.response.ResponseCode.Common.COMMON_PARAM_INVALID;
import static com.guwave.datahub.bpms.app.web.response.ResponseCode.Internal.INTERNAL_RPC_INVOKE_ERROR;

/**
 * 2025/7/7 17:03
 * TestItemReloadService
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TestItemReloadService {

    @DubboReference
    private ICalculateTestItemRpcService iCalculateTestItemRpcService;

    public void triggerCalTestItem(CalTestItemWaferRequestVo calTestItemWaferRequestVo) {
        log.info("触发跑测项表, calculateDwTestItem:{}, calculateDwTestItem:{}", calTestItemWaferRequestVo.isCalculateDwTestItem(), calTestItemWaferRequestVo.isCalculateDwTestItem());
        if (calTestItemWaferRequestVo.getWaferVos().stream().anyMatch(waferVo -> TestArea.getCpMapDataSourceList().contains(waferVo.getTestArea()) || TestArea.getCpInklessMapDataDourceList().contains(waferVo.getTestArea()))) {
            log.info("dataSource为CP Map、Inkless Map的批次不支持触发跑测项");
            throw new BpmsException(COMMON_PARAM_INVALID, "dataSource为CP Map、Inkless Map的批次不支持触发跑测项");
        }
        if (CollectionUtils.isEmpty(calTestItemWaferRequestVo.getWaferVos())) {
            log.info("参数校验失败, 参数为空");
            throw new BpmsException(COMMON_PARAM_INVALID, "waferVos参数为空");
        } else if (Objects.equals(false,calTestItemWaferRequestVo.isCalculateDwTestItem())) {
            log.info("参数校验失败, calculateDwTestItem必须为true");
            throw new BpmsException(COMMON_PARAM_INVALID, "参数校验失败, calculateDwTestItem必须为true");
        } else if (calTestItemWaferRequestVo.getWaferVos().stream().anyMatch(waferVo -> !WaferVo.paramsCheck(waferVo))) {
            log.info("参数校验失败, waferVo计算维度值不能为空");
            throw new BpmsException(COMMON_PARAM_INVALID, "参数校验失败, waferVo计算维度值不能为空");
        }
        try {
            CalTestItemWaferVo calTestItemWaferVo = new CalTestItemWaferVo()
                    .setWaferVos(calTestItemWaferRequestVo.getWaferVos())
                    .setCalculateDwTestItem(calTestItemWaferRequestVo.isCalculateDwTestItem())
                    .setCalculateYmsTestItem(calTestItemWaferRequestVo.isCalculateYmsTestItem());
            iCalculateTestItemRpcService.triggerCalTestItem(calTestItemWaferVo);
            log.info("触发跑测项表结束");
        } catch (Exception e) {
            log.error("触发跑测项表异常", e);
            throw new BpmsException(INTERNAL_RPC_INVOKE_ERROR);
        }
    }

}
