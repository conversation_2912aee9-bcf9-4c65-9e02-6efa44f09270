package com.guwave.datahub.bpms.app.controller;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.service.bz.StateManageService;
import com.guwave.datahub.bpms.app.vo.bz.stateManage.model.ModuleSwitch;
import com.guwave.datahub.bpms.app.vo.bz.stateManage.model.ModuleTask;
import com.guwave.datahub.bpms.app.vo.bz.stateManage.response.StateManageDetailVo;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Log
@Auth
//@License
@Validated
@Slf4j
@RestController
@AllArgsConstructor
@Tag(name = "StateManageControllerAPI", description = "状态管理接口",
        externalDocs = @ExternalDocumentation(description = "需求文档",
                url = "")
)
@RequestMapping("stateManage")
public class StateManageController {

    private StateManageService stateManageService;

    @Auth
    @Operation(summary = "开关页面数据")
    @PostMapping("queryStateManageData")
    public ResponseVo<StateManageDetailVo> queryStateManageData() {
        return ResponseVo.ok(stateManageService.queryStateManageData());
    }

    @Auth
    @Operation(summary = "一键关闭、开启开关")
    @GetMapping("switchAllModule")
    public ResponseVo<Void> switchAllModule(@RequestParam("stopFlag") Integer stopFlag) {
        stateManageService.switchAllModule(stopFlag);
        return ResponseVo.ok();
    }

    @Auth
    @Operation(summary = "关闭某个开关")
    @PostMapping("switchModuleStopFlag")
    public ResponseVo<Void> switchModuleStopFlag(@RequestBody List<ModuleSwitch> modules) {
        stateManageService.switchModuleStopFlag(modules);
        return ResponseVo.ok();
    }

    @Auth
    @Operation(summary = "onlyRepairCollectx")
    @PostMapping("onlyRepairCollectx")
    public ResponseVo<Void> onlyRepairCollectx() {
        stateManageService.onlyRepairCollectx();
        return ResponseVo.ok();
    }

    @Auth
    @Operation(summary = "onlyRepairProcessing")
    @PostMapping("onlyRepairProcessing")
    public ResponseVo<Void> onlyRepairProcessing() {
        stateManageService.onlyRepairProcessing();
        return ResponseVo.ok();
    }

    @Auth
    @Operation(summary = "forceOneClickRepair")
    @PostMapping("forceOneClickRepair")
    public ResponseVo<Void> forceOneClickRepair() {
        stateManageService.forceOneClickRepair();
        return ResponseVo.ok();
    }
}
