package com.guwave.datahub.bpms.app.web.response;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 返回码
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-26 13:34:45
 */
public enum ResponseCode implements ResponseCodeInterface {

    /**
     * 返回码设计
     * 00000001：操作成功(为了适配之前的后端!!!!!!!!!!)
     * 99999：操作失败，系统无法捕捉的异常都为此
     * 00001~00099：内部错误码(框架本身的错误，如：db异常、缓存异常等)
     * 10000~99999：业务逻辑返回码(无异常，代码正常跑，并返回提示给用户)
     * --1XX00~1XX99：通用业务逻辑返回码(XX表示模块, 比如00为鉴权、01为License相关、02为通用异常相关)
     * ----10000~10099：鉴权相关
     * ----10100~10199：License相关
     * ----10200~10299：通用异常相关
     * ----10300~10399：Common Business
     * ----剩下的可以拓展
     * --2XX00~2XX99：业务逻辑返回码(XX表示模块, 比如00为任务管理模块、01为数据删除重跑相关、02为元数据管理模块、03为数据质量模块)
     * --剩下的可以拓展
     * ----------------------------------------------------------------------------------------------------------------
     * 总体设计就是值越小，错误严重性越高，ENUM的KEY命名规范：以模块英文大写加下划线开头，比如鉴权相关必须AUTH_开头
     */
    SUCCESS("00000001", "操作成功"),
    FAIL("99999", "操作失败"),

    ;

    private final String code;
    private final String msg;

    ResponseCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public String code() {
        return this.code;
    }

    @Override
    public String msg() {
        return this.msg;
    }

    /**
     * 内部异常返回码，框架内部问题之类的
     */
    public enum Internal implements ResponseCodeInterface {

        INTERNAL_ERROR("00001", "系统内部错误"),

        INTERNAL_DB_ERROR("00002", "数据库异常"),

        INTERNAL_RPC_INVOKE_ERROR("00003", "系统内部RPC调用异常"),

        ;

        private final String code;
        private final String msg;

        Internal(String code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        @Override
        public String code() {
            return this.code;
        }

        @Override
        public String msg() {
            return this.msg;
        }
    }

    /**
     * 通用返回码
     */
    public enum Common implements ResponseCodeInterface {

        // Auth, 10000~10099
        AUTH_FAIL("10000", "鉴权失败"),
        AUTH_UC_FAIL("10001", "UC鉴权失败"),
        AUTH_REQUEST_UC_FAIL("10002", "请求UC鉴权接口失败"),
        AUTH_DATA_PERMISSION_FAIL("10003", "获取数据权限失败"),
        AUTH_REQUEST_DATA_PERMISSION_FAIL("10004", "请求数据权限接口失败"),

        // License, 10100~10199
        LICENSE_EXCEED("10100", "当前模块超出License授权数"),
        LICENSE_CHECK_FAIL("10101", "校验License失败"),
        LICENSE_REQUEST_FAIL("10102", "请求License接口失败"),

        // 通用异常
        COMMON_REQUEST_METHOD_INVALID("10200", "请求方式: {} 不支持"),
        COMMON_REQUEST_METHOD_NO_FOUND("10201", "请求的url不存在: {}"),
        COMMON_REQUEST_NOTIFICATION_FAIL("10202", "通知中心发送失败: {}"),
        COMMON_GENERATE_CONTENT_FAIL("10203", "内容生成失败"),

        // Common Business, 10300~10399
        COMMON_PARAM_INVALID("10300", "参数为空或者不合法: {}"),
        COMMON_PARAM_NOT_READABLE("10301", "参数不可读或未传"),
        COMMON_OPERATE_NOT_PRESENT("10302", "操作的对象库中不存在"),
        FILE_NOT_READ_NEED_FIELD("10303", "文件没有读取到需要的字段"),
        FILE_SIZE_EXCEED_LIMIT("10304", "文件大小超出限制: {}"),
        FILE_READ_ERROR("10305", "文件读取异常: {}"),
        FILE_DECOMPRESS_FAIL("10306", "文件解压失败"),
        VIOLATION_INTEGRITY_CONSTRAINTS("10307", "数据违反完整性约束"),
        DATA_HAS_BEEN_DEAL("10308", "数据已经被处理"),
        FILE_COMPRESS_FAIL("10309", "文件压缩失败"),
        UPLOAD_HDFS_FAIL("10310", "文件上传hdfs失败"),
        CHECK_HDFS_FILE_FAIL("10311", "校验hdfs文件失败"),
        COMMON_OPERATE_ALREADY_EXIST("103012", "保存操作的对象库中已存在"),
        FILE_PARSE_FAIL("10313", "文件解析失败 {}"),
        FILE_TYPE_NOT_SUPPORT("10314", "不支持的文件类型 {}");
        private final String code;
        private final String msg;

        Common(String code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        @Override
        public String code() {
            return this.code;
        }

        @Override
        public String msg() {
            return this.msg;
        }
    }

    /**
     * 业务返回码
     */
    public enum Business implements ResponseCodeInterface {

        // Task, 20000~20099
        TASK_NOT_COMPLETED_OR_SUCCESS("20000", "禁止提交任务,存在成功或正在运行的任务"),
        TASK_PRE_JOB_NOT_COMPLETED("20001", "禁止提交任务,前置DWD任务未完成"),
        TASK_NOT_NEED_RELOAD("20002", "未计算测项数据,不需要恢复"),
        TASK_PLEASE_TRY_REPLAY("20003", "数据异常,请尝试数据重播"),

        // DataReplay, 20100~20199
        DATA_REPLAY_PROHIBIT_SUBMIT("20100", "禁止提交任务,当前有正在Replay的任务"),
        DATA_REPLAY_TOO_MANY_FILE("20101", "禁止提交任务,重播文件数量超过最大限制"),
        DATA_REPLAY_FILE_NOT_EXISTS("20102", "禁止提交任务,重播文件数量为0"),
        DATA_REPLAY_STEP_NOT_SUPPORT("20103", "禁止提交任务,重播步骤小于等于当前文件的的Step"),
        DATA_REPLAY_TASK_NOT_FAIL("20104", "禁止提交重试,没有失败的重播任务"),
        DATA_REPLAY_UNSUPPORTED_TYPE_STEP("20105", "禁止提交任务,不支持的重播类型和步骤"),
        DATA_REPLAY_TOO_MANY_TASK("20106", "禁止提交任务,重播任务数量超过最大限制"),
        DATA_REPLAY_CONVERT_FINISHED_FILE("20107", "禁止提交重试,存在2200文件已转换成功，请重播转换后的文件"),
        DATA_REPLAY_DELETE_SUMMARY_TOO_MANY("20108", "禁止提交任务,删除任务存在多个summary文件"),
        DATA_REPLAY_PROHIBIT_TOP("20109", "置顶任务失败,任务状态不是CREATE"),

        // DataQuality, 20200~20299
        DATA_QUALITY_TRY_RUN_ERROR("20200", "试运行异常：{}"),

        // ClearRulePreview, 20300~20399
        CLEAR_RULE_PREVIEW_NOT_FIND_DETAIL_FILE("20300", "不存在明细信息文件"),
        CLEAR_RULE_PREVIEW_CAN_NOT_CONVERT_RESULT("20301", "结果转换失败：{}"),
        CLEAR_RULE_PREVIEW_NOT_SUPPORT_FIELD("20302", "不支持配置此字段"),
        CLEAR_RULE_PREVIEW_NOT_SUPPORT_FILE("20303", "不支持此文件"),

        // StateManage, 20400~20499
        STATE_MANAGE_HAS_PROCESSING_TASK("20400", "存在运行中的任务，若检查无误请点击强制一键修复"),

        // ComputeManage, 20500~20599
        CONFIG_ALREADY_EXISTS("20501", "已存在相同配置"),

        // Priority, 20600-20699
        PRIORITY_CONFIG_ALREADY_EXISTS("20601", "优先级配置已存在"),
        COMMON_PARAMETER_ERROR("20602", "优先级格式错误"),

        // configure, 20700-20799
        CONFIGURE_EXCEL_SHEET_NAME_NOT_SUPPORT("20701", "不支持的sheet名称 {}"),
        CONFIGURE_EXCEL_FILE_NOT_EXIST("20702", "excel文件不存在"),
        CONFIGURE_DUPLICATE("20703", "Excel中有重复的配置 {}"),
        CONFIGURE_MORE_THAN_EXCEL_FILE("20704", "有多个Excel文件"),
        CONFIGURE_EXPORT_ERROR("20705", "导出文件异常"),
        CONFIGURE_DOWNLOAD_SCRIPT("20706", "下载脚本文件失败 {}"),
        CONFIGURE_SCRIPT_FILE_NOT_EXIST("20707", "脚本文件不存在 {}"),
        CONFIGURE_BACKUP_SCRIPT("20708", "备份脚本失败 {}"),
        CONFIGURE_SAVE_SCRIPT_RULE("20709", "保存脚本规则失败"),

        // dataIntegrity, 20800-20899
        DATA_INTEGRITY_FILE_NOT_EXISTS("20801", "{} 文件不存在"),

        // dataAccuracy, 20900-20999
        DATA_ACCURACY_FILE_NOT_CHANGE("20901", "存在未变更数据的文件"),
        DATA_ACCURACY_FIRST_TEST_FILE_NOT_EXIST("20902", "变更后的批次没有初测文件，请核对修改信息"),
        Data_ACCURACY_REPAIRING_FILE_EXIST("20903", "存在正在修复的批次，请修复完成再发起新修复！"),
        DATA_ACCURACY_WAREHOUSING_FILE_EXIST("20904", "存在正在入库的批次，请入库完成再发起新修复！"),
        DATA_ACCURACY_TRIGGER_REPAIR_FAIL("20905", "触发修复失败"),

        // collectx, 21000-21099
        COLLECTX_IMMEDIATELY_RUN_FAIL("21001", "立即运行失败：{}"),

        // BinDefinition, 21100-21199
        BIN_DEFINITION_TEST_PROGRAM_NOT_FOUND("21100", "测试程序 {} 不存在"),
        BIN_DEFINITION_TEST_PROGRAM_ALREADY_CONFIGURED("21101", "测试程序 {} 已配置bin定义"),
        BIN_DEFINITION_BIN_NUM_ALREADY_EXISTS("21102", "测试程序 {} 的 {} bin编号 {} 已存在"),
        BIN_DEFINITION_IMPORT_FILE_EMPTY("21103", "导入文件为空"),
        BIN_DEFINITION_IMPORT_FILE_FORMAT_ERROR("21104", "导入文件格式错误：{}"),
        BIN_DEFINITION_EXPORT_ERROR("21105", "导出bin定义失败：{}"),
        BIN_DEFINITION_DELETE_ERROR("21106", "删除bin定义失败：{}"),
        BIN_DEFINITION_SAVE_ERROR("21107", "保存bin定义失败：{}"),
        BIN_DEFINITION_PARAM_INVALID("21108", "bin定义参数无效：{}"),
        ;

        private final String code;
        private final String msg;

        Business(String code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        @Override
        public String code() {
            return this.code;
        }

        @Override
        public String msg() {
            return this.msg;
        }
    }

}
