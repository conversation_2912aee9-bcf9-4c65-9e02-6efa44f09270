package com.guwave.datahub.bpms.app.vo.bz.task.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/12
 * @description SparkConfigModifyVo
 */

@Data
@Schema(title = "task 修改spark运行参数")
public class DwTaskRerunVo {
    @Schema(title = "ids")
    @NotEmpty(message = "ids")
    private List<Long> ids;

}

