package com.guwave.datahub.bpms.app.vo.bz.qualityRepair.request;

import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Schema(title = "下拉框过滤条件请求体")
@Data
@EqualsAndHashCode(callSuper = false)
public class DataQualityBatchRepairVo extends PageableRequestVo {

    @Schema(title = "subCustomerList", description = "sub_customer列表", example = "[\"AMD\"]")
    private List<String> subCustomerList;

    @Schema(title = "deviceIdList", description = "device_id列表", example = "[\"SS521BB-DEFAULT\"]")
    private List<String> deviceIdList;

    @Schema(title = "testStageList", description =  "test_stage列表", example = "[\"CP1\"]")
    private List<String> testStageList;

    @Schema(title = "lotIdList", description =  "lot_id列表", example = "[\"NT41A\"]")
    private List<String> lotIdList;
}