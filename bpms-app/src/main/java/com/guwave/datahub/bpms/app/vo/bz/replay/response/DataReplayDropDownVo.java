package com.guwave.datahub.bpms.app.vo.bz.replay.response;

import com.guwave.datahub.bpms.common.constant.Constant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Schema(title = "下拉框结果")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataReplayDropDownVo {
    private static final Pattern NUMBER_PATTERN = Pattern.compile("-?\\d+");

    @Schema(title = "下拉框结果值", example = "NT41A")
    private String name;
    @Schema(title = "页面显示下拉框结果值", example = "NT41A")
    private String showName;
    @Schema(title = "是否可以被选", example = "true")
    private Boolean selectable;

    public static DataReplayDropDownVo of(String name) {
        return DataReplayDropDownVo.of(name, name);
    }

    public static DataReplayDropDownVo of(String name, String showName) {
        DataReplayDropDownVo vo = new DataReplayDropDownVo(name, showName, true);
        return vo;
    }

    public static DataReplayDropDownVo of(String name, String showName, Boolean selectable) {
        DataReplayDropDownVo vo = new DataReplayDropDownVo(name, showName, selectable);
        return vo;
    }

    public static List<DataReplayDropDownVo> ofList(List<String> names) {
        if (CollectionUtils.isEmpty(names)) {
            return Collections.emptyList();
        }
        List<String> topPriorityList = Arrays.asList(Constant.WRAPPER_NULL, Constant.EMPTY);

        Map<Boolean, List<String>> sortedMap = names.stream()
                .collect(Collectors.groupingBy(topPriorityList::contains, Collectors.toList()));
        sortedMap.computeIfAbsent(true, key -> new ArrayList<>()).sort(Comparator.comparing(String::toString).reversed());

        boolean isNumer =sortedMap.getOrDefault(false, new ArrayList<>()).stream().allMatch(t -> NUMBER_PATTERN.matcher(t).matches());
        sortedMap.computeIfAbsent(false, key -> new ArrayList<>()).sort(isNumer ? Comparator.comparingInt(Integer::parseInt) : Comparator.comparing(String::toString));

        List<String> sortedList = new ArrayList<>();
        sortedList.addAll(sortedMap.get(true));
        sortedList.addAll(sortedMap.get(false));

        return sortedList.stream()
                .map(DataReplayDropDownVo::of)
                .collect(Collectors.toList());
    }
}
