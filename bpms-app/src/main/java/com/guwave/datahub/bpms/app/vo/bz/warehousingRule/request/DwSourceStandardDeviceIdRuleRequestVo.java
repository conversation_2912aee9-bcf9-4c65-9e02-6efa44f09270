package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(title = "DEVICE_ID取值规则")
public class DwSourceStandardDeviceIdRuleRequestVo {
    @Schema(title = "id")
    private Long id;

    @Schema(description = "子客户", example = "GUWAVE")
    private String subCustomer;

    @Schema(description = "测试阶段类型: CP/FT/WAT/SLT/EQC 等", example = "CP")
    private String testArea;

    @Schema(description = "测试工厂名称", example = "TSMC")
    private String factory;

    @Schema(description = "测试子工厂", example = "TSMC")
    private String factorySite;

    @Schema(description = "js执行代码", example = "result = 25")
    private String ruleExpression;
}
