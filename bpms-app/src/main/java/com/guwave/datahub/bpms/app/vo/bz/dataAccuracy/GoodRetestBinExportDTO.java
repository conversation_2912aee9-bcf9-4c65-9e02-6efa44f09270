package com.guwave.datahub.bpms.app.vo.bz.dataAccuracy;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.guwave.datahub.bpms.dao.clickhouse.dto.GoodRetestBinDTO;
import lombok.Builder;
import lombok.Data;

/**
 * 好品复测下载实体
 *
 * <AUTHOR>
 * 2023/10/27
 */
@Data
@Builder
public class GoodRetestBinExportDTO {
    @ExcelProperty("Device Name")
    @ColumnWidth(20)
    private String deviceId;
    @ExcelProperty("Test Stage")
    @ColumnWidth(20)
    private String testStage;
    @ExcelProperty("Test Program")
    @ColumnWidth(40)
    private String testProgram;
    @ExcelProperty("Lot ID")
    @ColumnWidth(20)
    private String lotId;
    @ExcelProperty("sbLot ID")
    @ColumnWidth(20)
    private String sblotId;
    @ExcelProperty("Wafer ID")
    @ColumnWidth(20)
    private String waferId;
    @ExcelProperty("Lot Type")
    @ColumnWidth(20)
    private String lotType;
    @ExcelProperty("Data Accuracy Check")
    @ColumnWidth(30)
    private String accuracyCheck;
    @ExcelProperty("ECID")
    @ColumnWidth(20)
    private String ecid;
    @ExcelProperty("Test Order")
    @ColumnWidth(20)
    private Integer retestNo;
    @ExcelProperty("HBin Number")
    @ColumnWidth(20)
    private Integer hbinNum;
    @ExcelProperty("SBin Number")
    @ColumnWidth(20)
    private Integer sbinNum;
    @ExcelProperty("HBin Name")
    @ColumnWidth(20)
    private String hbinName;
    @ExcelProperty("SBin Name")
    @ColumnWidth(20)
    private String sbinName;
    @ExcelProperty("HBin PF")
    @ColumnWidth(20)
    private String hbinPf;
    @ExcelProperty("File Name")
    @ColumnWidth(100)
    private String fileName;

    public static GoodRetestBinExportDTO of(GoodRetestBinDTO goodRetestBinDTO) {
        return GoodRetestBinExportDTO.builder()
                .deviceId(goodRetestBinDTO.getDeviceId())
                .testStage(goodRetestBinDTO.getTestStage())
                .testProgram(goodRetestBinDTO.getTestProgram())
                .lotId(goodRetestBinDTO.getLotId())
                .sblotId(goodRetestBinDTO.getSblotId())
                .waferId(goodRetestBinDTO.getWaferId())
                .lotType(goodRetestBinDTO.getLotType())
                .accuracyCheck(goodRetestBinDTO.getAccuracyCheck())
                .ecid(goodRetestBinDTO.getEcid())
                .retestNo(goodRetestBinDTO.getRetestNo())
                .hbinNum(goodRetestBinDTO.getHbinNum())
                .sbinNum(goodRetestBinDTO.getSbinNum())
                .hbinName(goodRetestBinDTO.getHbinName())
                .sbinName(goodRetestBinDTO.getSbinName())
                .hbinPf(goodRetestBinDTO.getHbinPf())
                .fileName(goodRetestBinDTO.getFileName())
                .build();
    }

}
