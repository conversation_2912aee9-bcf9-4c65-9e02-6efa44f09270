package com.guwave.datahub.bpms.app.service.dailyReport.impl;

import com.alibaba.fastjson2.JSON;
import com.guwave.datahub.bpms.app.service.dailyReport.CommonService;
import com.guwave.datahub.bpms.app.util.DateUtil;
import com.guwave.datahub.bpms.app.util.PdfUtil;
import com.guwave.datahub.bpms.common.constant.*;
import com.guwave.datahub.bpms.dao.repository.bpms.DailyReportResultRepository;
import com.guwave.datahub.bpms.dao.repository.linkx.BzAppInstanceRepository;
import com.guwave.datahub.bpms.dao.repository.linkx.BzTaskInstanceRepository;
import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.TaskExecutionDetail;
import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.TriggerTypeCount;
import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.YmsModifyDetail;
import com.guwave.onedata.linkx.common.constant.OptType;
import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.guwave.datahub.bpms.common.constant.Constant.COMMA;

@Service
public class YmsModifyModuleService implements CommonService {

    @Autowired
    private DailyReportResultRepository dailyReportResultRepository;

    @Autowired
    private BzAppInstanceRepository bzAppInstanceRepository;

    @Autowired
    private BzTaskInstanceRepository bzTaskInstanceRepository;

    private static final Map<String, String> TRIGGER_TYPE_MAP = new HashMap<String, String>() {{
        put("insert_new_product_info","新增product");
        put("insert_new_device_info","新增device");
        put("insert_new_device_product","新增device-product关系");
        put("insert_new_product_config","新增product_config");
        put("system_modify_product_config","系统推算product_wafermap_config");
        put("user_init_product_config","首次配置product_wafermap_config");
        put("insert_new_device_config","新增device_wafermap_config");
        put("user_init_device_config","首次配置device_wafermap_config");
    }};

    private static final Map<String, String> MODIFY_TASK_MAP = new HashMap<String, String>() {{
        put("modify-wafermap-config-run-ods-python", "ods使用py同步其他表");
        put("modify-wafermap-config-run-modify-python", "检查需要modify的device");
        put("modify_wafermap_config_rerun_success_leatest_dt", "记录本次成功的时间");
        put("modify-wafermap-config-update-gross-die-cnt", "更新gross_die");
        put("modify-wafermap-config-update-mysql-info", "更新主数据表和批次信息表");
        put("modify-wafermap-config-run-ods-spark", "ods使用spark同步mapping表");
    }};

    List<String> MODIFY_TASK_OPT_TYPES = Arrays.asList(
            OptType.UPDATE_WAFERMAP_CONFIG.toString(),
            OptType.UPDATE_PRODUCT.toString(),
            OptType.DELETE_WAFERMAP_CONFIG.toString()
    );

    public void generateYmsModifyDetail(PdfUtil pdfUtil) throws IOException {
        YmsModifyDetail ymsModifyDetail = new YmsModifyDetail();

        // 获取昨日Mofigy任务成功数
        int successModifyCnt = bzAppInstanceRepository.findCountByOptTypeAndProcessStatusAndTimeRange(OptType.MODIFY_WAFERMAP_CONFIG.getType(), DateUtil.getDateFormattedStartTimeBeforeDays(1), DateUtil.getDateFormattedStartTimeBeforeDays(0));
        ProcessStatus processStatus = (successModifyCnt > 0) ? ProcessStatus.SUCCESS : ProcessStatus.FAIL;
        ymsModifyDetail.setProcessStatus(processStatus);
        pdfUtil.addBodyText("昨日Modify任务状态:" + processStatus);

        // 获取执行时长分布
        List<TaskExecutionDetail> taskExecutionDetails = bzTaskInstanceRepository.findTaskExecutionDetails(DateUtil.getDateFormattedStartTimeBeforeDays(1), DateUtil.getDateFormattedStartTimeBeforeDays(0), OptType.MODIFY_WAFERMAP_CONFIG.getType())
                        .stream().map(this::buildTaskExecutionDetail).collect(Collectors.toList());
        ymsModifyDetail.setModifyDuration(JSON.toJSONString(taskExecutionDetails));
        List<ChartData> executionChartData = taskExecutionDetails.stream().map(t -> new ChartData(t.getName(), t.getExecuteTime())).collect(Collectors.toList());
        pdfUtil.addChart(executionChartData, ChartType.PIE, "modify任务运行时间分布");

        // 获取拉起任务数量
        Long modifyTaskCnt = bzAppInstanceRepository.findCountByOptTypeAndTimeRange(DateUtil.getDateFormattedStartTimeBeforeDays(1), DateUtil.getDateFormattedStartTimeBeforeDays(0), MODIFY_TASK_OPT_TYPES);
        ymsModifyDetail.setModifyTaskCnt(modifyTaskCnt);
        pdfUtil.addBodyText("昨日Modify任务生成app_instance数量:" + modifyTaskCnt);
        if (modifyTaskCnt > 0) {
            // 获取拉起原因分布
            List<TriggerTypeCount> triggerTypeCounts = bzAppInstanceRepository.findCountByOptTypeAndTimeRangeGroupByTriggerType(DateUtil.getDateFormattedStartTimeBeforeDays(1), DateUtil.getDateFormattedStartTimeBeforeDays(0), MODIFY_TASK_OPT_TYPES)
                            .stream().map(this::buildTriggerTypeCount).collect(Collectors.toList());
            ymsModifyDetail.setGenerateReportTime(JSON.toJSONString(triggerTypeCounts));

            List<ChartData> triggerChartData = triggerTypeCounts.stream().map(t -> new ChartData(t.getTriggerType(), t.getNums())).collect(Collectors.toList());

            pdfUtil.addChart(triggerChartData, ChartType.PIE, "modify任务生成原因分布");
        }
        saveDailyReportResult(JSON.toJSONString(ymsModifyDetail), ReportModule.YMS_MODIFY, DataCode.YMS_MODIFY);
    }

    private TaskExecutionDetail buildTaskExecutionDetail(Object[] objects) {
        return new TaskExecutionDetail(
                MODIFY_TASK_MAP.getOrDefault(objects[0] == null ? Constant.EMPTY : objects[0], "其他任务"),
                objects[1] == null ? 0 : ((Number) objects[1]).longValue()
        );
    }

    private TriggerTypeCount buildTriggerTypeCount(Object[] objects) {
        return new TriggerTypeCount(
                objects[0] == null ? 0 : ((Number) objects[0]).longValue(),
                TRIGGER_TYPE_MAP.getOrDefault((objects[1] == null ? Constant.EMPTY :(String) objects[1]).split(COMMA)[0], "其他原因")
        );
    }

    @Override
    public DailyReportResultRepository getDailyReportResultRepository() {
        return dailyReportResultRepository;
    }
}
