package com.guwave.datahub.bpms.app.controller;

import cn.hutool.core.io.FileUtil;
import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.service.bz.TestProgramTestPlanService;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.util.List;

@Log
@Auth
//@License
@Validated
@Slf4j
@RestController
@AllArgsConstructor
@Tag(name = "TestProgramTestPlanControllerAPI", description = "test_program_test_plan接口",
        externalDocs = @ExternalDocumentation(description = "需求文档",
                url = "https://ohjj5az3be.feishu.cn/wiki/F8riw52HSibvlAkbBwfcTRHYnRg")
)
@RequestMapping("testProgramTestPlan")
public class TestProgramTestPlanController {

    private TestProgramTestPlanService testProgramTestPlanService;


    @Operation(summary = "导入")
    @PostMapping("importPlan")
    public ResponseVo<Void> importPlan(MultipartFile file, Boolean overriteFullFlag) throws IOException {
        this.testProgramTestPlanService.importPlan(file, overriteFullFlag == null || overriteFullFlag);
        return ResponseVo.ok();
    }
}
