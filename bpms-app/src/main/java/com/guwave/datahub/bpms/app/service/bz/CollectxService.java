package com.guwave.datahub.bpms.app.service.bz;

import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.onedata.dataware.bridge.api.iface.ICollectxImmediatelyRunRpcService;
import com.guwave.onedata.dataware.bridge.api.vo.response.CollectxImmediatelyRunRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class CollectxService {

    @DubboReference
    private ICollectxImmediatelyRunRpcService collectxImmediatelyRunRpcService;

    public String immediatelyRun(Long id) {
        log.info("Collectx立即运行 {}", id);
        CollectxImmediatelyRunRes res = collectxImmediatelyRunRpcService.immediatelyRun(id);
        if (res.isHasErr()) {
            throw new BpmsException(ResponseCode.Business.COLLECTX_IMMEDIATELY_RUN_FAIL, res.getErrMsg());
        }
        return res.getResult();
    }
}
