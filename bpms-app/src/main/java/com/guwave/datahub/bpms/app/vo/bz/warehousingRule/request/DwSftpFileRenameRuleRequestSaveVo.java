package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DwSftpFileRenameRuleRequestSaveVo {
    @Schema(title = "文件重命名规则")
    List<DwSftpFileRenameRuleRequestVo> ruleRequestVos;

    @Schema(title = "入库规则流程")
    WarehousingRuleFlowRequestVo warehouseRuleFlowRequestVo;
}
