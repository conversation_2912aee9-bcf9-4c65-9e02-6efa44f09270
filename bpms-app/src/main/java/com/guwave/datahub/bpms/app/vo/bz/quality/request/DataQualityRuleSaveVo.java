package com.guwave.datahub.bpms.app.vo.bz.quality.request;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(title = "数据质量保存信息")
public class DataQualityRuleSaveVo {

    @Schema(title = "id", example = "1")
    private Long id;

    @Schema(title = "指标类型", description = "0->初始化，1->指标结果", example = "1")
    @NotNull(message = "指标类型")
    private Integer sqlType;

    @Schema(title = "执行间隔", description = "1->每天一次，2->每半小时一次", example = "1")
    @NotNull(message = "执行间隔")
    private Integer executeTimeType;

    @Schema(title = "指标名称", example = "TEST")
    @NotBlank(message = "指标名称")
    private String qualityName;

    @Schema(title = "备注", example = "备注")
    @NotBlank(message = "备注")
    private String comment;

    @Schema(title = "mysql查询sql", example = "select 1")
    private String mysqlQuerySql;

    @Schema(title = "创建ck临时表的sql", description = "如果填写表示需要创建临时表，且会根据mysql_query_sql的查询结果插入此临时表（表结构和mysql查询的结果字段顺序要一致），且ck_query_sql不会再执行", example = "CREATE temporary table if not exists dw_lot_stocking_detail")
    private String ckTempTableSql;

    @Schema(title = "ck查询sql", example = "select 1")
    private String ckQuerySql;

    @Schema(title = "状态", description = "0->关闭，1->开启", example = "1")
    @NotNull(message = "状态")
    private Integer status;
}
