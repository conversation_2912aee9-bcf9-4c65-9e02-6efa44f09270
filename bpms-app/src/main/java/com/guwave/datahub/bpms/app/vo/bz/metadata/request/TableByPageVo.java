package com.guwave.datahub.bpms.app.vo.bz.metadata.request;

import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * TableByPageVo
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-03-11 15:31:40
 */
@Schema(title = "metadata 根据数据库、库名、表名分页查询")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class TableByPageVo extends PageableRequestVo {

    @Schema(title = "数据库", description = "clickhouse/mysql", example = "clickhouse")
    @NotNull(message = "数据库")
    private String datasource;

    @Schema(title = "库名", example = "ods")
    @NotNull(message = "库名")
    private String databaseName;

    @Schema(title = "表名", example = "ods_dc_device_info_cluster")
    @NotNull(message = "表名")
    private String tableName;
}
