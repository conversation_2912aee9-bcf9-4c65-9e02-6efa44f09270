package com.guwave.datahub.bpms.app.controller;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.service.bz.CollectxService;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.Min;

@Log
@Auth
//@License
@Validated
@Slf4j
@RestController
@AllArgsConstructor
@Tag(name = "CollectxControllerAPI", description = "Collectx接口",
        externalDocs = @ExternalDocumentation(description = "需求文档",
                url = "")
)
@RequestMapping("collectx")
public class CollectxController {

    private CollectxService collectxService;

    @Auth
    @Operation(summary = "立即运行")
    @GetMapping("immediatelyRun")
    public ResponseVo<String> immediatelyRun(@RequestParam("id") @Schema(example = "1") @Min(value = 1) Long id) {
        return ResponseVo.ok(collectxService.immediatelyRun(id));
    }

}
