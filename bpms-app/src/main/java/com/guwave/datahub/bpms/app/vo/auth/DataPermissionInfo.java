package com.guwave.datahub.bpms.app.vo.auth;

import lombok.Data;

import java.util.List;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * DataPermissionInfo
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-27 11:06:20
 */
@Data
public class DataPermissionInfo {

    /**
     * - 是否开启subCustomer数据权限控制
     */
    private boolean openSubCustomer;

    /**
     * - 是否拥有当前customer下所有subCustomer数据权限
     */
    private boolean allSubCustomerDataPermission;

    /**
     * - 当allSubCustomerPermission=false时，拥有的subCustomer数据权限列表
     */
    private List<String> subCustomerDataPermissions;

    /**
     * - 是否开始deviceId数据权限控制
     */
    private boolean openDevice;

    /**
     * - 是否拥有当前customer下所有device数据权限
     */
    private boolean allDeviceDataPermission;

    /**
     * - 当allDevicePermission为false是，用户的device数据权限列表
     */
    private List<String> deviceDataPermissions;
}
