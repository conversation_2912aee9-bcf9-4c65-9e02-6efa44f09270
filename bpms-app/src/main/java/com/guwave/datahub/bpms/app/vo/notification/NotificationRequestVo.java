package com.guwave.datahub.bpms.app.vo.notification;

import lombok.Data;

import java.util.List;
import java.util.Map;


@Data
public class NotificationRequestVo {

    /**
     * 来源系统
     */
    private String source;

    /**
     * 通知模板code
     */
    private String code;

    /**
     * 公司编码
     */
    private String companyCode;

    /**
     * 通知参数
     */
    private Map<String, Object> parameters;

    /**
     * 接收人账号
     */
    private List<String> toUsers;

    /**
     * 接收人用户组
     */
    private List<String> toGroups;

    /**
     * 接收人邮箱（邮箱使用）
     */
    private List<String> toMails;

    /**
     * 抄送人账号（邮箱使用）
     */
    private List<String> ccUsers;

    /**
     * 抄送邮箱（邮箱使用）
     */
    private List<String> ccMails;

    /**
     * 链接（站内信）
     */
    private String link;

}
