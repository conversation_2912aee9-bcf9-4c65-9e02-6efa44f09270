package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.request;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(title = "解析脚本配置规则")
public class DwSftpFileConvertScriptRequestVo {
    @Schema(title = "id")
    private Long id;

    @Schema(description = "子客户", example = "GUWAVE")
    private String subCustomer;

    @Schema(description = "测试阶段类型: CP/FT/WAT/SLT/EQC 等", example = "CP")
    private String testArea;

    @Schema(description = "测试工厂名称", example = "TSMC")
    private String factory;

    @Schema(description = "测试子工厂", example = "TSMC")
    private String factorySite;

    @Schema(description = "测试子工厂", example = "RAW_DATA")
    private String fileCategory;

    @Schema(description = "此脚本匹配文件名的规则", example = "(\\.xls$)|(\\.xlsx$)")
    private String regRule;

    @Schema(description = "脚本文件名称(压缩包)", example = "HISENSE_FT_HN_summary_xlsx_auto.zip")
    private String fileName;

    @Schema(description = "压缩包内符合条件脚本文件列表", example = "[\"HISENSE_FT_HN_summary_xlsx_auto-1.py\",\"HISENSE_FT_HN_summary_xlsx_auto-2.py\"]")
    @ExcelProperty("script_list")
    private List<String> scriptList;

    @Schema(description = "执行的入口脚本名称", example = "HISENSE_FT_HN_summary_xlsx_auto.py")
    private String executeFileName;
}