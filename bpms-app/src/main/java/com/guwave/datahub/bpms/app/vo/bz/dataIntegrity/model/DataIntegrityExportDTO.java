package com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.guwave.datahub.bpms.dao.vo.bpms.dataIntegrity.DataIntegrityRecord;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataIntegrityExportDTO {
    @ExcelProperty("File Name")
    private String fileName;
    @ExcelProperty("File Location")
    private String fileLocation;
    @ExcelProperty("Data Loaded")
    private String dataLoaded;
    @ExcelProperty("Data Loading Failed Step")
    private String dataLoadingFailedStep;
    @ExcelProperty("Data Loading Failed Type")
    private String failedType;
    @ExcelProperty("Update Time")
    private String updateTime;

    @ExcelProperty("Device Name")
    private String deviceName;
    // private String testArea;
    @ExcelProperty("Test Stage")
    private String testStage;
    @ExcelProperty("Lot ID")
    private String lotId;
    @ExcelProperty("sbLot ID")
    private String sblotId;
    @ExcelProperty("Wafer ID")
    private String waferId;
    @ExcelProperty("Lot Type")
    private String lotType;
    @ExcelProperty("Start Time")
    private String startTime;
    @ExcelProperty("End Time")
    private String endTime;

    public static DataIntegrityExportDTO of(DataIntegrityRecord record) {
        return new DataIntegrityExportDTO(
                record.getFileName(), record.getFileLocation(), record.getDataLoaded(), record.getDataLoadingFailedStep(),
                record.getLoadingFailedType(), record.getUpdateTime(), record.getDeviceName(), record.getTestStage(),
                record.getLotId(), record.getSblotId(), record.getWaferId(), record.getLotType(), record.getStartTime(),
                record.getEndTime()
        );
    }

}
