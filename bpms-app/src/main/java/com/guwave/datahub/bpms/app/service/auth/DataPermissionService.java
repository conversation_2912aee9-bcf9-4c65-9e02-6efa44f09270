package com.guwave.datahub.bpms.app.service.auth;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.guwave.datahub.bpms.app.exception.BpmsAuthException;
import com.guwave.datahub.bpms.app.vo.auth.DataPermissionDTOV2;
import com.guwave.datahub.bpms.app.vo.auth.DataPermissionInfoV2;
import com.guwave.datahub.bpms.app.vo.auth.UserResponseDTO;
import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.dao.repository.dw.DeviceInfoRepository;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Service
@AllArgsConstructor
public class DataPermissionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DataPermissionService.class);

    private final AuthApi authApi;
    private final DeviceInfoRepository deviceInfoRepository;

    public DataPermissionInfoV2 getDataPermissionV2() {
        try {
            StopWatch sw = new StopWatch();
            sw.start();
            LOGGER.info("开始请求DataPermissionV2 Api");
            UserResponseDTO<DataPermissionDTOV2> userResponseDTO = this.authApi.getDataPermissionV2();
            sw.stop();
            LOGGER.info("结束请求DataPermissionV2 Api, 结果: {}, 耗时: {}ms", JSON.toJSONString(userResponseDTO), sw.getTotalTimeMillis());
            if (null == userResponseDTO || !userResponseDTO.success()) {
                LOGGER.info("获取DataPermissionV2失败");
                throw new BpmsAuthException(ResponseCode.Common.AUTH_DATA_PERMISSION_FAIL);
            }
            DataPermissionDTOV2 dataPermissionDTOV2 = userResponseDTO.getData();
            DataPermissionInfoV2 info = new DataPermissionInfoV2();
            info.setAccessSwitch(dataPermissionDTOV2.getAccessSwitch());
            info.setAdmin(dataPermissionDTOV2.getAdmin());
            info.setRoleDataPermissionInfos(CollectionUtils.isNotEmpty(dataPermissionDTOV2.getData()) ?
                    dataPermissionDTOV2.getData().stream().map(this::buildRoleDataPermissionInfo).filter(role -> null != role).collect(Collectors.toList())
                    : Collections.emptyList());

            return info;
        } catch (BpmsAuthException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.info("请求DataPermissionV2接口失败: {}", e.getMessage());
            throw new BpmsAuthException(e, ResponseCode.Common.AUTH_REQUEST_DATA_PERMISSION_FAIL);
        }
    }


    private DataPermissionInfoV2.RoleDataPermissionInfo buildRoleDataPermissionInfo(DataPermissionDTOV2.RoleDataPermissionDTO dto) {
        DataPermissionInfoV2.RoleDataPermissionInfo info = new DataPermissionInfoV2.RoleDataPermissionInfo();
        info.setFactorys(dto.getFactory());
        info.setSubCustomers(dto.getSubCustomer());
        info.setDeviceIds(dto.getDeviceId());
        info.setLotTypes(dto.getLotType());
        info.setProducts(dto.getProduct());

        // added 20250318 by wt for 新增productLine、productFamily数据权限。把product、productLine、productFamily都转换成device数据权限之后，取交集
        Set<String> productDeviceIds = null;

        // product权限需要转换成device权限
        if (CollectionUtils.isNotEmpty(dto.getProduct())) {
            productDeviceIds = combine(productDeviceIds, deviceInfoRepository.listDeviceIdByProductInfo(RequestContext.getCustomer(), dto.getProduct(), null, null));
        }
        if (CollectionUtils.isNotEmpty(dto.getProductLine())) {
            productDeviceIds = combine(productDeviceIds, deviceInfoRepository.listDeviceIdByProductInfo(RequestContext.getCustomer(), null, dto.getProductLine(), null));
        }
        if (CollectionUtils.isNotEmpty(dto.getProductFamily())) {
            productDeviceIds = combine(productDeviceIds, deviceInfoRepository.listDeviceIdByProductInfo(RequestContext.getCustomer(), null, null, dto.getProductFamily()));
        }

        // 取交集
        // null 说明没设置权限，忽略；空列表，说明权限冲突，替换成一个一定失败的device null
        if (null == productDeviceIds) {
            // to nothing
        } else if (CollectionUtils.isEmpty(productDeviceIds)) {
            info.setProductDeviceIds(Arrays.asList("null"));
        } else {
            info.setProductDeviceIds(Lists.newArrayList(productDeviceIds));
        }

        // 什么权限都没有的角色，直接不要了。避免使用的时候还得做判断
        if (CollectionUtils.isEmpty(info.getFactorys())
                && CollectionUtils.isEmpty(info.getSubCustomers())
                && CollectionUtils.isEmpty(info.getDeviceIds())
                && CollectionUtils.isEmpty(info.getLotTypes())
                && CollectionUtils.isEmpty(info.getProducts())
                && CollectionUtils.isEmpty(info.getProductDeviceIds())) {
            return null;
        }

        return info;
    }

    /**
     * - 取交集
     */
    private Set<String> combine(Set<String> set, List<String> deviceIds) {
        if (CollectionUtils.isEmpty(deviceIds)) {
            return set;
        }
        return null == set ? Sets.newHashSet(deviceIds) : deviceIds.stream().filter(set::contains).collect(Collectors.toSet());
    }
}
