package com.guwave.datahub.bpms.app.util;

import com.guwave.onedata.dataware.common.contant.Constant;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

public class ThreadPoolUtils {


    public static ThreadPoolExecutor getNewThreadPoolExecutor(String namePrefix, int poolCoreSize, int poolMaxSize, int poolQueueSize) {
        final ThreadFactory THREAD_FACTORY = new ThreadFactory() {

            private final ThreadFactory defaultFactory = Executors.defaultThreadFactory();
            private final AtomicInteger threadNumber = new AtomicInteger(1);

            public Thread newThread(Runnable r) {
                Thread thread = defaultFactory.newThread(r);
                thread.setName(namePrefix + Constant.MIDDLE_LINE + threadNumber.getAndIncrement());
                return thread;
            }
        };

        return new ThreadPoolExecutor(
                poolCoreSize,
                poolMaxSize,
                60,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(poolQueueSize),
                THREAD_FACTORY,
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }
}
