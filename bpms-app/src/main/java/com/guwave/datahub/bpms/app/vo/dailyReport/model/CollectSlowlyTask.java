package com.guwave.datahub.bpms.app.vo.dailyReport.model;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
public class CollectSlowlyTask {
    private Long collectRuleId = 0L;
    private String customer;
    private String factory;
    private String factorySite;
    private String fab;
    private String fabSite;
    private String testArea;
    private String ftpIp;
    private Date sftpFileLastMtime;
    private Integer batchTotalLoadFiles;
    private Integer batchSuccessLoadFiles;
    private String batchStatus;
    private String collectRuleSnapshot;
    private String exceptionType;
    private String exceptionMessage;
    private String errorMessage;

    public Long getCollectRuleId() {
        return collectRuleId;
    }

    public void setCollectRuleId(Long collectRuleId) {
        this.collectRuleId = collectRuleId;
    }

    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public String getFactory() {
        return factory;
    }

    public void setFactory(String factory) {
        this.factory = factory;
    }

    public String getFactorySite() {
        return factorySite;
    }

    public void setFactorySite(String factorySite) {
        this.factorySite = factorySite;
    }

    public String getFab() {
        return fab;
    }

    public void setFab(String fab) {
        this.fab = fab;
    }

    public String getFabSite() {
        return fabSite;
    }

    public void setFabSite(String fabSite) {
        this.fabSite = fabSite;
    }

    public String getTestArea() {
        return testArea;
    }

    public void setTestArea(String testArea) {
        this.testArea = testArea;
    }

    public String getFtpIp() {
        return ftpIp;
    }

    public void setFtpIp(String ftpIp) {
        this.ftpIp = ftpIp;
    }

    public Date getSftpFileLastMtime() {
        return sftpFileLastMtime;
    }

    public void setSftpFileLastMtime(Date sftpFileLastMtime) {
        this.sftpFileLastMtime = sftpFileLastMtime;
    }

    public Integer getBatchTotalLoadFiles() {
        return batchTotalLoadFiles;
    }

    public void setBatchTotalLoadFiles(Integer batchTotalLoadFiles) {
        this.batchTotalLoadFiles = batchTotalLoadFiles;
    }

    public Integer getBatchSuccessLoadFiles() {
        return batchSuccessLoadFiles;
    }

    public void setBatchSuccessLoadFiles(Integer batchSuccessLoadFiles) {
        this.batchSuccessLoadFiles = batchSuccessLoadFiles;
    }

    public String getBatchStatus() {
        return batchStatus;
    }

    public void setBatchStatus(String batchStatus) {
        this.batchStatus = batchStatus;
    }

    public String getCollectRuleSnapshot() {
        return collectRuleSnapshot;
    }

    public void setCollectRuleSnapshot(String collectRuleSnapshot) {
        this.collectRuleSnapshot = collectRuleSnapshot;
    }

    public String getExceptionType() {
        return exceptionType;
    }

    public void setExceptionType(String exceptionType) {
        this.exceptionType = exceptionType;
    }

    public String getExceptionMessage() {
        return exceptionMessage;
    }

    public void setExceptionMessage(String exceptionMessage) {
        this.exceptionMessage = exceptionMessage;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
