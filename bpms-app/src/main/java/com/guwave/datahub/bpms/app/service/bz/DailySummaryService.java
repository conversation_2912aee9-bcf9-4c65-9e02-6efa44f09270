package com.guwave.datahub.bpms.app.service.bz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.guwave.datahub.bpms.app.ck.CkProvider;
import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.service.notification.NotificationService;
import com.guwave.datahub.bpms.app.util.MultipleDataSourceUtil;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.dao.domain.dailySummary.DailySummary;
import com.guwave.datahub.bpms.dao.domain.dailySummary.DailySummaryQueryConfig;
import com.guwave.datahub.bpms.dao.repository.bpms.DailySummaryQueryConfigRepository;
import com.guwave.datahub.bpms.dao.repository.bpms.DailySummaryRepository;
import com.guwave.onedata.dataware.common.contant.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.hibernate.query.NativeQuery;
import org.hibernate.transform.Transformers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;


@Slf4j
@Service
public class DailySummaryService {

    private static final String MYSQL = "MYSQL";
    private static final String CLICKHOUSE = "CLICKHOUSE";

    @Value("${spring.notification.dailySummaryCode}")
    private String notificationDailySummaryCode;
    @Value("${spring.handler.file.dailySummaryPath}")
    private String dailySummaryPath;

    @Autowired
    private DailySummaryQueryConfigRepository dailySummaryQueryConfigRepository;
    @Autowired
    private DailySummaryRepository dailySummaryRepository;
    @Autowired
    private MultipleDataSourceUtil multipleDataSourceUtil;
    @Autowired
    private CkProvider ckProvider;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private TheamleafService theamleafService;

    /**
     * 生成日报数据
     */
    @Scheduled(cron = "${spring.scheduler.timer.generateDailySummaryTask}")
    public void generateDailySummaryJob() {
        try {
            generateDailySummary();
        } catch (Exception e) {
            log.info("generateDailySummary 异常：", e);
        }
    }

    /**
     * 发送日报数据
     */
    @Scheduled(cron = "${spring.scheduler.timer.sendDailySummaryTask}")
    public void sendDailySummaryJob() {
        try {
            sendDailySummary();
        } catch (Exception e) {
            log.info("sendDailySummaryJob 异常：", e);
        }
    }

    public void generateDailySummary() {
        String day = DateFormatUtils.format(DateUtils.addDays(new Date(), -1), "yyyy-MM-dd");
        JSONObject data = new JSONObject();
        StreamSupport.stream(dailySummaryQueryConfigRepository.findAll().spliterator(), false)
                .filter(t -> StringUtils.isNotBlank(t.getType()) && StringUtils.isNotBlank(t.getDb()) && StringUtils.isNotBlank(t.getCode()) && StringUtils.isNotBlank(t.getFieldsQuery()))
                .collect(Collectors.groupingBy(DailySummaryQueryConfig::getCode))
                .forEach((code, querys) -> {
                    JSONObject codeForJsonObject = new JSONObject();
                    JSONArray datas = new JSONArray();
                    querys.forEach(query -> {
                        try {
                            executeQuery(datas, query);
                        } catch (Exception e) {
                            log.info("{} 执行异常：{}", query.getCode(), query.getQueryName(), e);
                        }
                    });
                    codeForJsonObject.put("date", day);
                    codeForJsonObject.put("datas", datas);
                    data.put(code, codeForJsonObject);
                });
        Date date = new Date();
        DailySummary dailySummary = new DailySummary(null, day, data.toJSONString(), date, Constant.SYSTEM, date, Constant.SYSTEM);
        List<DailySummary> existsRecords = dailySummaryRepository.findAllByDay(dailySummary.getDay());
        if (CollectionUtils.isNotEmpty(existsRecords)) {
            dailySummaryRepository.deleteAllById(existsRecords.stream().map(DailySummary::getId).collect(Collectors.toList()));
        }
        dailySummaryRepository.save(dailySummary);
    }

    private void executeQuery(JSONArray datas, DailySummaryQueryConfig query) {
        log.info("执行：{} {}", query.getCode(), query.getQueryName());
        List<Map> resultList = new ArrayList<>();
        if (MYSQL.equalsIgnoreCase(query.getType())) {
            resultList = multipleDataSourceUtil.getEntityManager(query.getDb())
                    .createNativeQuery(query.getFieldsQuery())
                    .unwrap(NativeQuery.class)
                    .setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP)
                    .getResultList();
        } else if (CLICKHOUSE.equalsIgnoreCase(query.getType())) {
            resultList = ckProvider.readCk(query.getFieldsQuery(), Map.class);
        } else {
            log.info("不支持的类型：{}", query.getType());
        }

        if (resultList.size() == 1) {
            if (datas.size() == 0) {
                JSONObject jsonObject = new JSONObject();
                datas.add(jsonObject);
            }
            JSONObject jsonObject = (JSONObject) datas.get(0);
            jsonObject.putAll(resultList.get(0));
        } else if (resultList.size() > 1) {
            resultList.forEach(res -> {
                JSONObject jsonObject = new JSONObject();
                jsonObject.putAll(res);
                datas.add(jsonObject);
            });
        }
    }

    public void sendDailySummary() {
        File tempDir = new File(dailySummaryPath, UUID.randomUUID().toString());
        try {
            String date = DateFormatUtils.format(DateUtils.addDays(new Date(), -1), "yyyy-MM-dd");
            List<DailySummary> dailySummaries = dailySummaryRepository.findAllByOrderByDayDesc(Pageable.ofSize(10));
            Set<String> codes = dailySummaries.stream().flatMap(t -> {
                JSONObject data = JSON.parseObject(t.getData());
                return data.keySet().stream();
            }).collect(Collectors.toSet());
            Map<String, Object> paramMap = new HashMap<>();
            dailySummaries.forEach(t -> {
                JSONObject data = JSON.parseObject(t.getData());
                codes.forEach(code -> {
                    paramMap.compute(code, (_key, oldValue) -> {
                        if (oldValue == null) {
                            oldValue = new ArrayList<JSONObject>();
                        }
                        JSONObject codeData = (JSONObject) data.getOrDefault(code, new JSONObject());
                        ((ArrayList<JSONObject>) oldValue).add(codeData);
                        return oldValue;
                    });
                });
            });
            paramMap.put("date", date);

            String overviewHtmlContent;
            try {
                overviewHtmlContent = theamleafService.generateHtmlFile("dailySummary/dailySummaryOverview", paramMap, null);
                log.info("概览日报HTML生成成功");
            } catch (Exception e) {
                log.info("概览日报HTML生成失败", e);
                throw new BpmsException(ResponseCode.Common.COMMON_GENERATE_CONTENT_FAIL);
            }

            String errorItemHtmlFileName = "error_item_" + date + ".html";
            File htmlFile = new File(tempDir, errorItemHtmlFileName);
            try {
                theamleafService.generateHtmlFile("dailySummary/dailySummaryErrorItem", paramMap, htmlFile);
                log.info("错误明细日报HTML文件生成成功: {}", htmlFile.getAbsolutePath());
            } catch (Exception e) {
                log.info("错误明细日报HTML文件生成失败: {}", htmlFile.getAbsolutePath(), e);
                throw new BpmsException(ResponseCode.Common.COMMON_GENERATE_CONTENT_FAIL);
            }

            Map<String, Object> requestParamMap = new HashMap<String, Object>() {{
                put("date", date);
                put("content", overviewHtmlContent);
            }};
            ArrayList<File> files = Lists.newArrayList(htmlFile);
            notificationService.sendEmail(notificationDailySummaryCode, requestParamMap, files);
        } finally {
            FileUtils.deleteQuietly(tempDir);
        }
    }

}
