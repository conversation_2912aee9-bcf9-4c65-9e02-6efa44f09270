package com.guwave.datahub.bpms.app.vo.bz.metadata.response;

import com.guwave.datahub.bpms.dao.domain.metadata.MetadataTableDescription;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class MetadataTableDescriptionVo {

    @Schema(title = "ID", example = "2")
    private Long id;

    @Schema(title = "数据库", description = "clickhouse/mysql", example = "clickhouse")
    private String datasource;

    @Schema(title = "库名", example = "ods")
    private String databaseName;

    @Schema(title = "表名", example = "'ods_dc_device_info_cluster'")
    private String tableName;

    @Schema(title = "描述", example = "后端的表")
    private String description;

    public static MetadataTableDescriptionVo of(MetadataTableDescription metadataTableDescription) {
        MetadataTableDescriptionVo metadataTableDescriptionVo = new MetadataTableDescriptionVo();
        metadataTableDescriptionVo.setDatasource(metadataTableDescription.getDatasource());
        metadataTableDescriptionVo.setDatabaseName(metadataTableDescription.getDatabaseName());
        metadataTableDescriptionVo.setTableName(metadataTableDescription.getTableName());
        metadataTableDescriptionVo.setDescription(metadataTableDescription.getDescription());
        return metadataTableDescriptionVo;
    }
}
