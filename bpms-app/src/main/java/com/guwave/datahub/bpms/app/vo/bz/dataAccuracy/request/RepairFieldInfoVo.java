package com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request;

import com.guwave.datahub.bpms.common.constant.RepairFieldEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 2025/4/27 18:00
 * RepairFieldInfoVo
 *
 * <AUTHOR>
 */
@Data
public class RepairFieldInfoVo {

    @Schema(description = "文件Id")
    private Long fileId;

    @Schema(description = "字段名称")
    private String fieldName;


    @Schema(description = "字段属性值")
    private RepairFieldEnum repairField;


    @Schema(description = "原始值")
    private String originalValue;


    @Schema(description = "修改值")
    private String modifyValue;


    @Schema(description = "修改原因")
    private String modifyRemark;
    
}
