package com.guwave.datahub.bpms.app.vo.bz.metadata.request;

import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import com.guwave.datahub.bpms.dao.domain.metadata.MetadataTableLink;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * LinkAddVo
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-03-11 15:31:40
 */
@Schema(title = "metadata 添加链接信息")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
public class LinkAddVo extends TableVo {

    @Schema(title = "链接名", example = "元数据管理-MetaData")
    @NotNull(message = "链接名")
    private String name;

    @Schema(title = "链接", example = "https://ohjj5az3be.feishu.cn/wiki/Qwpnwk9zZi4wNikYpZzc9nlGnlf")
    @NotNull(message = "链接")
    private String link;

    public MetadataTableLink toMetadataTableLink() {
        MetadataTableLink metadataTableLink = new MetadataTableLink();
        metadataTableLink.setDatasource(getDatasource());
        metadataTableLink.setDatabaseName(getDatabaseName());
        metadataTableLink.setTableName(getTableName());
        metadataTableLink.setLinkName(getName());
        metadataTableLink.setLink(getLink());
        metadataTableLink.setCreateUser(RequestContext.getUserName());
        metadataTableLink.setUpdateUser(RequestContext.getUserName());
        metadataTableLink.setLink(getLink());
        Date date = new Date();
        metadataTableLink.setCreateTime(date);
        metadataTableLink.setUpdateTime(date);
        return metadataTableLink;
    }
}
