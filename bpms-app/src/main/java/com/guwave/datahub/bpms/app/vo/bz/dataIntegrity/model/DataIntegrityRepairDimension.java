package com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;



@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
public class DataIntegrityRepairDimension {
    private String customer;
    private String factory;
    private String testArea;
    private String deviceId;
    private String lotId;
    private String waferNo;
    private String testStage;

}
