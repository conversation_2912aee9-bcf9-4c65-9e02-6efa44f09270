package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper=false)
@Schema(title = "删除入库规则")
public class WarehousingRuleDeleteVo {

    @Schema(title = "dwSftpSubCustomerRuleIds", description = "根据子客户目录规则表的id删除数据", example = "[1,2,3,4]")
    private List<Long> dwSftpSubCustomerRuleIds;

    @Schema(title = "dwSftpFileRenameRuleIds", description = "根据文件重命名规则表的id删除数据", example = "[1,2,3,4]")
    private List<Long> dwSftpFileRenameRuleIds;

    @Schema(title = "dwDataClearRuleIds", description = "根据特殊逻辑配置表的id删除数据", example = "[1,2,3,4]")
    private List<Long> dwDataClearRuleIds;

    @Schema(title = "dwSftpFileConvertScriptIds", description = "根据解析脚本配置规则表的id删除数据", example = "[1,2,3,4]")
    private List<Long> dwSftpFileConvertScriptIds;

    @Schema(title = "dwSourceStandardDeviceIdRuleIds", description = "根据DEVICE_ID取值规则表的id删除数据", example = "[1,2,3,4]")
    private List<Long> dwSourceStandardDeviceIdRuleIds;

    @Schema(title = "dwSourceStandardFieldRuleIds", description = "根据关键字段取值规则表的id删除数据", example = "[1,2,3,4]")
    private List<Long> dwSourceStandardFieldRuleIds;

}
