package com.guwave.datahub.bpms.app.vo.bz.quality.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/10 15:16
 */
@Getter
public enum StatisticIndexEnum {

    /**
     * 统计指标类型
     */
    PRODUCT_COUNT("productCnt", "产品数量"),
    CHIP_COUNT("chipCnt", "芯片数量"),
    BATCH_COUNT("batchCnt", "批次数量"),
    IN_DB_TIME("costTime", "入库耗时"),
    IN_DB_STREAM("inDbStream", "入库时间流"),
    YIELD_ACCURACY("yieldAccuracy","批次良率准确率"),
    MATCH_RATE("matchRate", "匹配率"),
    NORMAL_RATE("DataAccuracyCheckRate","DataAccuracyCheck normal率"),
    EXCEPTION_YIELD("exceptionYield", "异常良率"),
    IN_DB_RATE("inDbRate", "入库率");

    private final String indexType;
    private final String indexName;


    StatisticIndexEnum(String indexType, String indexName) {
        this.indexType = indexType;
        this.indexName = indexName;
    }

    public static List<StatisticIndexEnum> getStatisticIndexEnumList() {
        return Arrays.asList(CHIP_COUNT, MATCH_RATE, NORMAL_RATE,YIELD_ACCURACY, PRODUCT_COUNT, BATCH_COUNT);
    }

    public static List<StatisticIndexEnum> getFileStatisticIndexEnumList() {
        return Arrays.asList(IN_DB_TIME);
    }

    private static final Map<String, StatisticIndexEnum> INDEX_MAP = Arrays.stream(values()).collect(Collectors.toMap( StatisticIndexEnum::getIndexType, v -> v));


    public static StatisticIndexEnum of(String indexType) {
        return INDEX_MAP.get(indexType);
    }


    @Getter
    @AllArgsConstructor
    public enum ExceptionYieldType {
        GT_100("GT_1", "Yield > 100%"),
        EQ_0("EQ_0", "Yield = 0"),
        NORMAL("NORMAL", "Yield in (0,100%]");


        private final String type;
        private final String desc;


    }
}
