package com.guwave.datahub.bpms.app.executor.impl;

import com.guwave.datahub.bpms.dao.ck.ClickHouseConnection;
import com.guwave.datahub.bpms.app.executor.Executor;
import com.guwave.datahub.bpms.common.constant.ExceptionType;
import com.guwave.datahub.bpms.common.constant.ExecuteCommandException;
import com.guwave.datahub.bpms.common.constant.ExecuteEngine;
import com.guwave.datahub.bpms.dao.domain.schedule.ScheduleTaskRecord;
import com.guwave.onedata.next.compute.api.vo.response.ComputeResponse;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;

import static com.guwave.datahub.bpms.common.constant.Constant.SEMICOLON;

/**
 * Copyright (C), 2023, guwave
 * <p>
 * CK执行器
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2023-07-28 11:03:02
 */
@Component
public class CkExecutor implements Executor {

    private static final Logger LOGGER = LoggerFactory.getLogger(CkExecutor.class);

    private static final String DELETE_SQL_TEMPLATE = "ALTER TABLE %s ON cluster cluster_3shards_1replicas DELETE WHERE IS_DELETE = 1;";

    @Override
    public Boolean support(ExecuteEngine executeEngine) {
        return executeEngine == ExecuteEngine.CLICKHOUSE;
    }

    @Override
    public void doExecute(String command) throws Exception {
        String[] tableNames = command.split(SEMICOLON);
        for (String tableName : tableNames) {
            tableName = tableName.trim();
            if (!tableName.isEmpty()) {
                String sql = String.format(DELETE_SQL_TEMPLATE, tableName);
                executeSql(sql);
            }
        }
    }

    public ComputeResponse doExecute(ScheduleTaskRecord taskRecord) throws Exception {
        return null;
    }

    public void executeSql(String sql) throws Exception {
        Connection connection = null;
        try {
            connection = ClickHouseConnection.getConnection();
            assert connection != null;
            execute(connection, sql);
        } catch (Exception e) {
            LOGGER.error("出现异常", e);
            throw new ExecuteCommandException(ExceptionType.EXECUTE_CLICKHOUSE_SQL_EXCEPTION, ExceptionUtils.getStackTrace(e));
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    LOGGER.error("关闭连接失败", e);
                }
            }
        }
    }

    /**
     * @param connection {@link Connection}
     * @param sql        具体sql语句
     * @throws Exception {@link Exception}
     */
    private void execute(Connection connection, String sql) throws Exception {
        StopWatch sw = new StopWatch();
        sw.start();
        PreparedStatement statement = null;
        try {
            assert connection != null;
            connection.setAutoCommit(false);
            LOGGER.info("开始执行: {}", sql);
            statement = connection.prepareStatement(sql);
            statement.execute();
            connection.commit();
            sw.stop();
            LOGGER.info("执行结束: {}, 耗时: {}ms", sql, sw.getTotalTimeMillis());
        } catch (SQLException e) {
            LOGGER.error("sql解析失败", e);
            throw new Exception(e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    LOGGER.error("关闭 statement 失败", e);
                }
            }
        }
    }
}
