package com.guwave.datahub.bpms.app.vo.bz.binDefinition.request;

import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * Bin Definition查询条件VO
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(title = "Bin Definition查询条件")
public class BinDefinitionQueryVo extends PageableRequestVo {

    @Schema(description = "是否导出已定义：1->是，0->否", example = "testProgram")
    private Integer exportConfiguredFlag ;

    @Schema(description = "测试程序名称列表（支持多选）")
    private List<String> testPrograms;

    @Schema(description = "排序字段", example = "testProgram")
    private String sortField;

    @Schema(description = "排序方式", example = "ASC")
    private String sortDirection;

    public void validConfiguredCondition() {
        sortField = convertEmpty(sortField);
        sortDirection = convertEmpty(sortDirection);

        if (StringUtils.isEmpty(sortField)) {
            sortField = "testProgram";
        }
        if (StringUtils.isEmpty(sortDirection)) {
            sortDirection = "ASC";
        }
        sortDirection = sortDirection.toLowerCase();
        exportConfiguredFlag = 1;
    }

    public void validUnconfiguredCondition() {
        sortField = convertEmpty(sortField);
        sortDirection = convertEmpty(sortDirection);

        if (StringUtils.isEmpty(sortField)) {
            sortField = "createTime";
        }
        if (StringUtils.isEmpty(sortDirection)) {
            sortDirection = "DESC";
        }
        sortDirection = sortDirection.toLowerCase();
        exportConfiguredFlag = 0;
    }

    private String convertEmpty(String str) {
        if (StringUtils.isEmpty(str)) {
            return null;
        } else {
            return str;
        }
    }
}