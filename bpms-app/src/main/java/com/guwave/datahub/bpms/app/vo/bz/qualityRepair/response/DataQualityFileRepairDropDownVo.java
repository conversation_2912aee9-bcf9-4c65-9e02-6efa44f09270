package com.guwave.datahub.bpms.app.vo.bz.qualityRepair.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Schema(title = "文件修复记录下拉框结果")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataQualityFileRepairDropDownVo {
    @Schema(title = "label")
    private String label;
    @Schema(title = "value")
    private String value;
    public static DataQualityFileRepairDropDownVo of(String value) {
        DataQualityFileRepairDropDownVo vo = new DataQualityFileRepairDropDownVo();
        vo.setLabel(value);
        vo.setValue(value);
        return vo;
    }
}
