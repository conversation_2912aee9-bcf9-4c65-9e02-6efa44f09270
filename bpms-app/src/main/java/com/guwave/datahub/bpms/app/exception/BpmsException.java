package com.guwave.datahub.bpms.app.exception;

import cn.hutool.core.util.StrUtil;
import com.guwave.datahub.bpms.app.web.response.ResponseCodeInterface;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * BpmsException
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-27 14:00:23
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@Data
public class BpmsException extends RuntimeException {

    protected ResponseCodeInterface responseCode;

    protected String msg;


    public BpmsException(ResponseCodeInterface responseCode, Object... args) {
        super(StrUtil.format(responseCode.msg(), args));
        this.fillResponseCode(responseCode, args);
    }

    public BpmsException(Throwable e, ResponseCodeInterface responseCode, Object... args) {
        super(StrUtil.format(responseCode.msg(), args), e);
        fillResponseCode(responseCode, args);
    }

    private void fillResponseCode(ResponseCodeInterface responseCode, Object... args) {
        this.responseCode = responseCode;
        this.msg = StrUtil.format(responseCode.msg(), args);
    }
}
