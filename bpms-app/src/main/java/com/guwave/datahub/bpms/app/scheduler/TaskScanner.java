package com.guwave.datahub.bpms.app.scheduler;

import com.guwave.datahub.bpms.common.constant.ExecuteEngine;
import com.guwave.datahub.bpms.dao.domain.schedule.ScheduleTaskRecord;
import com.guwave.datahub.bpms.dao.repository.bpms.ScheduleTaskRecordRepository;
import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Component
public class TaskScanner {

    private static final Logger LOGGER = LoggerFactory.getLogger(TaskScanner.class);
    private final ScheduleTaskRecordRepository scheduleTaskRecordRepository;

    private final TaskSchedulerService taskSchedulerService;

    @Autowired
    public TaskScanner(ScheduleTaskRecordRepository scheduleTaskRecordRepository,
                       TaskSchedulerService taskSchedulerService) {
        this.scheduleTaskRecordRepository = scheduleTaskRecordRepository;
        this.taskSchedulerService = taskSchedulerService;
    }

    private final List<ExecuteEngine> supportedEngines = Collections.unmodifiableList(Arrays.asList(ExecuteEngine.PYTHON, ExecuteEngine.CLICKHOUSE));
    @ScheduleSwitch
    @Scheduled(fixedDelayString = "${scheduler.polling.milliseconds}")
    public void scanAndExecutePendingTasks() {
        try {
            ScheduleTaskRecord pendingTask = scheduleTaskRecordRepository.findFirstByProcessStatusAndExecuteEngineInOrderByIdAsc(ProcessStatus.CREATE, supportedEngines);
            if (pendingTask == null) {
                LOGGER.info("当前没有待处理的任务");
                return;
            }
            LOGGER.info("开始调度任务: {}", pendingTask.getTaskName());
            pendingTask.setProcessStatus(ProcessStatus.PROCESSING);
            scheduleTaskRecordRepository.save(pendingTask);
            taskSchedulerService.executeSyncTask(pendingTask);
        } catch (Exception e) {
            LOGGER.error("任务扫描异常", e);
        }
    }
}
