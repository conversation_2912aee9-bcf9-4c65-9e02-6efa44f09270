package com.guwave.datahub.bpms.app.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.onedata.dataware.common.contant.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class ExcelLineListener extends AnalysisEventListener<Map<Integer, String>> {

    // Map<showName,fieldName>
    private final Map<String, String> fieldMapping;

    // Map<fieldName,[<index,showName>,...]>
    private final Map<String, List<Pair<Integer, String>>> fieldIndexMapping = new HashMap<>();
    // Map<fieldName,[<showName,Value>,...]>
    private final ExcelConsumer<Map<String, List<Pair<String, String>>>> fieldWithContentListConsumer;
    private long num = 1L;

    public ExcelLineListener(Map<String, String> fieldMapping, ExcelConsumer<Map<String, List<Pair<String, String>>>> fieldWithContentListConsumer) {
        this.fieldMapping = fieldMapping;
        this.fieldWithContentListConsumer = fieldWithContentListConsumer;
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        headMap.forEach((index, showName) -> {
            String field = fieldMapping.get(specialUnicode(showName));
            if (field != null) {
                fieldIndexMapping.compute(field, (k, oldValue) -> {
                    if (CollectionUtils.isEmpty(oldValue)) {
                        oldValue = new ArrayList<>();
                    }
                    oldValue.add(Pair.of(index, showName));
                    return oldValue;
                });
            }
        });
        if (fieldIndexMapping.isEmpty()) {
            throw new BpmsException(ResponseCode.Common.FILE_NOT_READ_NEED_FIELD);
        }
    }

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        num++;
        if (null == data || data.size() == 0) {
            return;
        }

        if (fieldWithContentListConsumer != null) {
            // Map<fieldName,[<showName,Value>,...]>
            HashMap<String, List<Pair<String, String>>> lineMap = new HashMap<>();
            fieldIndexMapping.forEach((field, indexWithShowNameList) -> {
                indexWithShowNameList.forEach(indexWithShowName -> {
                    lineMap.compute(field, (k, oldValue) -> {
                        if (CollectionUtils.isEmpty(oldValue)) {
                            oldValue = new ArrayList<>();
                        }
                        String str = data.get(indexWithShowName.getKey());
                        if (StringUtils.isBlank(str)) {
                            str = Constant.EMPTY;
                        }
                        oldValue.add(Pair.of(indexWithShowName.getValue(), str));
                        return oldValue;
                    });
                });
            });
            if (!lineMap.isEmpty()) {
                fieldWithContentListConsumer.dealLine(num, lineMap);
            }
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (fieldWithContentListConsumer != null) {
            fieldWithContentListConsumer.after();
        }
    }

    private static String specialUnicode(String str) {
        if (str.startsWith("\uFEFF")) {
            str = str.replace("\uFEFF", "");
        } else if (str.endsWith("\uFEFF")) {
            str = str.replace("\uFEFF", "");
        }
        return str;
    }
}
