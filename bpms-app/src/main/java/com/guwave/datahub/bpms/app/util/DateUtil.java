package com.guwave.datahub.bpms.app.util;

import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

public class DateUtil {

    /**
     * 获取前n天的开始时间字符串，格式为yyyy-MM-dd HH:mm:ss.SSS
     *
     * @param n 前n天
     * @return 开始时间
     */
    public static String getStringFormattedStartTimeBeforeDays(int n) {
        //
        LocalDateTime dayStart = LocalDateTime.now().minusDays(n).withHour(0)
                .withMinute(0)
                .withSecond(0)
                .withNano(0);

        // 定义格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
        return dayStart.format(formatter);
    }

    /**
     * 获取前n天的开始时间字符串，Date类型
     *
     * @param n 前n天
     * @return 开始时间
     */
    public static Date getDateFormattedStartTimeBeforeDays(int n) {
        //
        LocalDateTime dayStart = LocalDateTime.now().minusDays(n).withHour(0)
                .withMinute(0)
                .withSecond(0)
                .withNano(0);

        // 定义格式化器
        return Date.from(dayStart.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取前n天的day字符串，格式为：yyyyMMdd
     *
     * @param n 前n天
     * @return 前n天的day
     */
    public static String getDayBefortDays(int n) {
        return LocalDate.now().minusDays(n).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

}
