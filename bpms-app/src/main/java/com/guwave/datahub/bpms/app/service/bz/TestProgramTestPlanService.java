package com.guwave.datahub.bpms.app.service.bz;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.exception.BpmsParamException;
import com.guwave.datahub.bpms.app.listener.ExcelConsumer;
import com.guwave.datahub.bpms.app.listener.ExcelLineListener;
import com.guwave.datahub.bpms.app.manage.TestProgramTestPlanManage;
import com.guwave.datahub.bpms.app.vo.bz.testProgramTestPlan.BinRelation;
import com.guwave.datahub.bpms.app.vo.bz.testProgramTestPlan.ImportTestPlanDimension;
import com.guwave.datahub.bpms.app.vo.bz.testProgramTestPlan.ImportTestPlanVo;
import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.common.constant.Constant;
import com.guwave.datahub.bpms.dao.repository.dw.TestProgramTestPlanRepository;
import com.guwave.onedata.dataware.common.util.RedisKeyGenerateUtil;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.TestProgramTestPlan;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.EmptyFileException;
import org.apache.poi.UnsupportedFileFormatException;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;


@Slf4j
@Service
public class TestProgramTestPlanService {


    @Autowired
    private TestProgramTestPlanRepository testProgramTestPlanRepository;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private TestProgramTestPlanManage testProgramTestPlanManage;

    // <showName,field>
    private static final Map<String, String> TEST_PLAN_FIELD_MAPPING = new HashMap<String, String>() {{
        put("UPLOAD_TYPE", "uploadType");
        put("TEST_AREA", "testArea");
        put("FACTORY", "factory");
        put("FACTORY_SITE", "factorySite");
        put("DEVICE_ID", "deviceId");
        put("TEST_STAGE", "testStage");
        put("LOT_TYPE", "lotType");
        put("TEST_PROGRAM", "testProgram");
        put("TEST_ITEM", "testItem");
        put("TEST_ORDER", "testOrder");
        put("SBIN_NUM", "sbinNum");
        put("SBIN_NAME", "sbinName");
        put("SBIN_PF", "sbinPf");
        put("HBIN_NUM", "hbinNum");
        put("HBIN_NAME", "hbinName");
        put("HBIN_PF", "hbinPf");
        put("UNIT_SCALE", "unitScale");
        put("CUSTOM_UNIT", "customUnit");
    }};
    // <field,consumer>
    private static final Map<String, BiConsumer<String, ImportTestPlanDimension>> IMPORT_MATCH_FIELD_CONSUMER_MAP = new HashMap<String, BiConsumer<String, ImportTestPlanDimension>>() {{
        put("uploadType", (str, item) -> item.setUploadType(str));
        put("testArea", (str, item) -> item.setTestArea(str));
        put("factory", (str, item) -> item.setFactory(str));
        put("factorySite", (str, item) -> item.setFactorySite(str));
        put("deviceId", (str, item) -> item.setDeviceId(str));
        put("testStage", (str, item) -> item.setTestStage(str));
        put("lotType", (str, item) -> item.setLotType(str));
        put("testProgram", (str, item) -> item.setTestProgram(str));
    }};

    // <field,consumer>
    private static final Map<String, BiConsumer<String, ImportTestPlanVo>> IMPORT_SETTING_FIELD_CONSUMER_MAP = new HashMap<String, BiConsumer<String, ImportTestPlanVo>>() {{
        put("testItem", (str, item) -> item.setTestItem(str));
        put("testOrder", (str, item) -> item.setTestOrder(getLongValue(str)));
        put("sbinNum", (str, item) -> item.setSbinNum(getLongValue(str)));
        put("sbinName", (str, item) -> item.setSbinNam(str));
        put("sbinPf", (str, item) -> item.setSbinPf(str));
        put("hbinNum", (str, item) -> item.setHbinNum(getLongValue(str)));
        put("hbinName", (str, item) -> item.setHbinNam(str));
        put("hbinPf", (str, item) -> item.setHbinPf(str));
        put("unitScale", (str, item) -> item.setUnitScale(getDecimalValue(str)));
        put("customUnit", (str, item) -> item.setCustomUnit(str));
    }};

    public void importPlan(MultipartFile file, boolean overriteFullFlag) throws IOException {
        log.info("导入 test plan");
        if (file.getSize() > 10L * 1024L * 1024L) {
            throw new BpmsException(ResponseCode.Common.FILE_SIZE_EXCEED_LIMIT, "10MB");
        }
        String fileSuffix = ("." + FileUtil.getSuffix(file.getOriginalFilename())).toLowerCase();
        ExcelTypeEnum excelTypeEnum = ExcelTypeEnum.CSV;
        if (ExcelTypeEnum.XLSX.getValue().equals(fileSuffix)) {
            excelTypeEnum = ExcelTypeEnum.XLSX;
        } else if (ExcelTypeEnum.XLS.getValue().equals(fileSuffix)) {
            excelTypeEnum = ExcelTypeEnum.XLS;
        }
        ExcelReader excel = null;
        try {
            excel = EasyExcel.read(file.getInputStream(), new ExcelLineListener(TEST_PLAN_FIELD_MAPPING, new ExcelConsumer<Map<String, List<Pair<String, String>>>>() {
                        private final Map<ImportTestPlanDimension, List<ImportTestPlanVo>> importTestPlanVoMap = new HashMap<>();
                        private final StringBuilder parseError = new StringBuilder();
                        private final Set<String> testOrderKeyColumns = Sets.newHashSet("testOrder");
                        private final Set<String> binRelationKeyColumns = Sets.newHashSet("sbinNum", "sbinName", "sbinPf");
                        private final Set<String> hbinColumns = Sets.newHashSet("hbinNum", "hbinName", "hbinPf");
                        private boolean testOrderManualImportFlag = false;
                        private boolean binRelationManualImportFlag = false;
                        private boolean hbinExistsFlag = false;
                        private final Map<String, Boolean> fieldExistsMap = TEST_PLAN_FIELD_MAPPING.values().stream().collect(Collectors.toMap(t -> t, t -> false, (v1, v2) -> v1));
                        private final BigDecimal zero = BigDecimal.ZERO;
                        private final Set<String> vaildBinPfSet = Sets.newHashSet(PF_PASS, PF_FAIL, U);
                        private final boolean validTestOrderContinuityFlag = false;

                        @Override
                        public void dealLine(Long num, Map<String, List<Pair<String, String>>> fieldWithContentListMap) {
                            fieldWithContentListMap.put(TEST_PLAN_FIELD_MAPPING.get("UPLOAD_TYPE"), Lists.newArrayList(Pair.of("UPLOAD_TYPE", "AUTO")));
                            ImportTestPlanDimension importTestPlanDimension = new ImportTestPlanDimension();
                            IMPORT_MATCH_FIELD_CONSUMER_MAP.forEach((field, consumer) -> {
                                if (fieldWithContentListMap.containsKey(field)) {
                                    fieldExistsMap.computeIfPresent(field, (fieldKey, fieldExistsOldValue) -> true);
                                    consumer.accept(fieldWithContentListMap.get(field).get(0).getValue(), importTestPlanDimension);
                                }
                            });
                            importTestPlanVoMap.compute(importTestPlanDimension, (key, oldValue) -> {
                                if (oldValue == null) {
                                    oldValue = new ArrayList<>();
                                }
                                ImportTestPlanVo importTestPlanVo = new ImportTestPlanVo();
                                importTestPlanVo.setNum(num);
                                IMPORT_SETTING_FIELD_CONSUMER_MAP.forEach((field, consumer) -> {
                                    if (fieldWithContentListMap.containsKey(field)) {
                                        fieldExistsMap.computeIfPresent(field, (fieldKey, fieldExistsOldValue) -> true);
                                        try {
                                            consumer.accept(fieldWithContentListMap.get(field).get(0).getValue(), importTestPlanVo);
                                        } catch (Exception e) {
                                            parseError.append("第").append(num).append("行,").append(fieldWithContentListMap.get(field).get(0).getKey()).append(":").append(e.getMessage()).append("\n");
                                        }
                                    }
                                });
                                oldValue.add(importTestPlanVo);
                                return oldValue;
                            });
                        }

                        @Override
                        public void after() {
                            if (testOrderKeyColumns.stream().anyMatch(field -> fieldExistsMap.getOrDefault(field, false))) {
                                testOrderManualImportFlag = true;
                            }
                            if (binRelationKeyColumns.stream().anyMatch(field -> fieldExistsMap.getOrDefault(field, false))) {
                                binRelationManualImportFlag = true;
                            }
                            if (hbinColumns.stream().anyMatch(field -> fieldExistsMap.getOrDefault(field, false))) {
                                hbinExistsFlag = true;
                            }
                            String validateError = validateImportData();
                            String err = parseError.append(validateError).toString();
                            if (StringUtils.isNotBlank(err)) {
                                throw new BpmsParamException(err);
                            }
                            saveData();
                        }

                        private void saveData() {
                            if (importTestPlanVoMap.isEmpty()) {
                                return;
                            }
                            StringBuilder saveError = new StringBuilder();
                            importTestPlanVoMap.forEach((importDimension, items) -> {
                                List<ImportTestPlanDimension> existsDimension = testProgramTestPlanRepository.findWithDimension(
                                                RequestContext.getCustomer(), RequestContext.getSubCustomer(), importDimension.getDeviceId(),
                                                importDimension.getTestStage(), importDimension.getTestProgram(), importDimension.getTestArea(), importDimension.getLotType(),
                                                importDimension.getFactory(), importDimension.getFactorySite(), importDimension.getUploadType()
                                        ).stream().map(t -> new ImportTestPlanDimension(t.getUploadType(), t.getTestArea(), t.getFactory(), t.getFactorySite(), t.getDeviceId(), t.getTestStage(), t.getLotType(), t.getTestProgram()))
                                        .collect(Collectors.toList());
                                if (existsDimension.isEmpty()) {
                                    existsDimension.add(new ImportTestPlanDimension(
                                            fieldExistsMap.getOrDefault("uploadType", false) ? importDimension.getUploadType() : EMPTY,
                                            fieldExistsMap.getOrDefault("testArea", false) ? importDimension.getTestArea() : EMPTY,
                                            fieldExistsMap.getOrDefault("factory", false) ? importDimension.getFactory() : EMPTY,
                                            fieldExistsMap.getOrDefault("factorySite", false) ? importDimension.getFactorySite() : EMPTY,
                                            importDimension.getDeviceId(),
                                            importDimension.getTestStage(),
                                            fieldExistsMap.getOrDefault("lotType", false) ? importDimension.getLotType() : EMPTY,
                                            importDimension.getTestProgram()
                                    ));
                                }
                                Map<String, List<ImportTestPlanVo>> newTestItemMap = items.stream().collect(Collectors.groupingBy(ImportTestPlanVo::getTestItem));
                                existsDimension.forEach(saveDimension -> {
                                    RLock lock = redissonClient.getLock(
                                            RedisKeyGenerateUtil.generateRedisKeyTestProgramTestPlan(RequestContext.getCustomer(), RequestContext.getSubCustomer(), saveDimension.getUploadType(),
                                                    saveDimension.getTestArea(), saveDimension.getFactory(), saveDimension.getFactorySite(), saveDimension.getDeviceId(), saveDimension.getTestStage(), saveDimension.getLotType(), saveDimension.getTestProgram())
                                    );
                                    lock.lock();
                                    try {
                                        log.info("开始写入 {}", lock.getName());
                                        Map<String, TestProgramTestPlan> existsTestItemMap = testProgramTestPlanRepository.findByCustomerAndSubCustomerAndDeviceIdAndTestStageAndTestProgramAndTestAreaAndLotTypeAndFactoryAndFactorySiteAndUploadType(
                                                RequestContext.getCustomer(), RequestContext.getSubCustomer(), saveDimension.getDeviceId(), saveDimension.getTestStage(), saveDimension.getTestProgram(), saveDimension.getTestArea(), saveDimension.getLotType(), saveDimension.getFactory(), saveDimension.getFactorySite(), saveDimension.getUploadType()
                                        ).stream().collect(Collectors.toMap(TestProgramTestPlan::getTestItem, t -> t, (v1, v2) -> v1));
                                        Date date = new Date();
                                        HashMap<Long, String> testOrderWithItemMap = new HashMap<>();
                                        if (testOrderManualImportFlag && (overriteFullFlag || existsTestItemMap.values().stream().allMatch(t -> t.getTestOrderManualImportFlag() == 0))) {
                                            existsTestItemMap.values().forEach(t -> t.setTestOrderManualImportFlag(1).setTestOrder(null).setUpdateUser(RequestContext.getUserName()).setUpdateTime(date));
                                        }
                                        if (binRelationManualImportFlag && (overriteFullFlag || existsTestItemMap.values().stream().allMatch(t -> t.getBinRelationManualImportFlag() == 0))) {
                                            existsTestItemMap.values().forEach(t -> t.setBinRelationManualImportFlag(1).setBinRelation(EMPTY_JSON_ARRAY).setSbins(Constant.EMPTY).setHbins(Constant.EMPTY).setUpdateUser(RequestContext.getUserName()).setUpdateTime(date));
                                        }
                                        existsTestItemMap.forEach((testItemKey, existsTestPlan) -> {
                                            List<ImportTestPlanVo> newPlans = newTestItemMap.get(testItemKey);
                                            if (newPlans != null) {
                                                existsTestPlan
                                                        .setUpdateUser(RequestContext.getUserName())
                                                        .setUpdateTime(date);
                                                if (fieldExistsMap.getOrDefault("unitScale", false)) {
                                                    existsTestPlan.setUnitScale(newPlans.get(0).getUnitScale());
                                                }
                                                if (fieldExistsMap.getOrDefault("customUnit", false)) {
                                                    existsTestPlan.setCustomUnit(newPlans.get(0).getCustomUnit());
                                                }
                                                if (testOrderManualImportFlag) {
                                                    existsTestPlan
                                                            .setTestOrderManualImportFlag(1)
                                                            .setTestOrder(newPlans.get(0).getTestOrder());
                                                }
                                                if (binRelationManualImportFlag) {
                                                    Map<BinKey, BinRelation> existsBinRelationMap = parseExistsBinRelation(existsTestPlan.getBinRelation());
                                                    Map<BinKey, BinRelation> newBinRelationMap = convertToBinRelationMapForImport(newPlans);
                                                    existsBinRelationMap.putAll(newBinRelationMap);
                                                    existsTestPlan
                                                            .setBinRelationManualImportFlag(1)
                                                            .setBinRelation(JSON.toJSONString(existsBinRelationMap.values()))
                                                            .setHbins(sortHbinAndMerge(existsBinRelationMap))
                                                            .setSbins(sortSbinAndMerge(existsBinRelationMap));
                                                }
                                            }
                                        });
                                        boolean existsManualImportTestOrder = existsTestItemMap.values().stream().anyMatch(t -> t.getTestOrderManualImportFlag() == 1);
                                        boolean existsManualImportBinRelation = existsTestItemMap.values().stream().anyMatch(t -> t.getBinRelationManualImportFlag() == 1);
                                        List<Long> deleteIds = existsTestItemMap.values().stream()
                                                .filter(t -> existsManualImportTestOrder && t.getTestOrder() == null && existsManualImportBinRelation && (StringUtils.isBlank(t.getBinRelation()) || EMPTY_JSON_ARRAY.equals(t.getBinRelation())))
                                                .map(TestProgramTestPlan::getId)
                                                .collect(Collectors.toList());

                                        List<TestProgramTestPlan> insertPlans = newTestItemMap.values().stream()
                                                .filter(newPlans -> !existsTestItemMap.containsKey(newPlans.get(0).getTestItem()))
                                                .map(newPlans -> buildTestProgramTestPlan(newPlans, saveDimension, date, testOrderManualImportFlag, binRelationManualImportFlag))
                                                .collect(Collectors.toList());

                                        StringBuilder error = new StringBuilder();
                                        existsTestItemMap.values().stream()
                                                .filter(t -> !deleteIds.contains(t.getId()) && t.getTestOrder() != null)
                                                .forEach(plan -> savaValidateDuplicateTestOrder(testOrderWithItemMap, plan, saveDimension, error));
                                        insertPlans.forEach(plan -> savaValidateDuplicateTestOrder(testOrderWithItemMap, plan, saveDimension, error));

                                        List<TestProgramTestPlan> updatePlans = existsTestItemMap.values().stream()
                                                .filter(t -> !deleteIds.contains(t.getId()) && t.getUpdateTime() == date)
                                                .collect(Collectors.toList());

                                        if (error.length() > 0) {
                                            error.insert(0, "维度：" + saveDimension + "\n\n");
                                            error.insert(0, "----------------------------------------------------------------------------------------------\n");
                                            error.append("----------------------------------------------------------------------------------------------\n");
                                            saveError.append(error);
                                        } else {
                                            testProgramTestPlanManage.modifyPlan(saveDimension, deleteIds, updatePlans, insertPlans);
                                        }
                                        log.info("结束写入 {}", lock.getName());
                                    } finally {
                                        lock.unlock();
                                    }
                                });
                            });
                            if (saveError.length() > 0) {
                                saveError.insert(0, "以下数据维度导入失败:\n");
                                throw new BpmsParamException(saveError.toString());
                            }
                        }

                        private void savaValidateDuplicateTestOrder(HashMap<Long, String> testOrderWithItemMap, TestProgramTestPlan plan, ImportTestPlanDimension dimension, StringBuilder saveError) {
                            if (testOrderManualImportFlag && !overriteFullFlag) {
                                testOrderWithItemMap.compute(plan.getTestOrder(), (key, oldValue) -> {
                                    if (oldValue == null) {
                                        oldValue = plan.getTestItem();
                                    } else if (!Objects.equals(oldValue, plan.getTestItem())) {
                                        saveError.append("TEST_ITEM: ").append(plan.getTestItem()).append("和").append(oldValue).append(" 的TEST_ORDER相同!\n");
                                    }
                                    return oldValue;
                                });
                            }
                        }

                        private String validateImportData() {
                            StringBuilder validateError = new StringBuilder();
                            if (!fieldExistsMap.getOrDefault("deviceId", false)) {
                                validateError.append("DEVICE_ID列必须存在!\n");
                            }
                            if (!fieldExistsMap.getOrDefault("testStage", false)) {
                                validateError.append("TEST_STAGE列必须存在!\n");
                            }
                            if (!fieldExistsMap.getOrDefault("testProgram", false)) {
                                validateError.append("TEST_PROGRAM列必须存在!\n");
                            }
                            if (!fieldExistsMap.getOrDefault("testItem", false)) {
                                validateError.append("TEST_ITEM列必须存在!\n");
                            }
                            if (testOrderManualImportFlag && !testOrderKeyColumns.stream().allMatch(field -> fieldExistsMap.getOrDefault(field, false))) {
                                validateError.append("TEST_ORDER 必须存在 TEST_ORDER列!\n");
                            }
                            if (binRelationManualImportFlag && !binRelationKeyColumns.stream().allMatch(field -> fieldExistsMap.getOrDefault(field, false))) {
                                validateError.append("BIN relation 必须存在 SBIN_NUM、SBIN_NAME、SBIN_PF列!\n");
                            }
                            if (hbinExistsFlag && !binRelationManualImportFlag) {
                                validateError.append("BIN relation 必须存在 SBIN_NUM、SBIN_NAME、SBIN_PF列!\n");
                            }
                            if (hbinExistsFlag && !hbinColumns.stream().allMatch(field -> fieldExistsMap.getOrDefault(field, false))) {
                                validateError.append("HBIN 必须存在 HBIN_NUM、HBIN_NAME、HBIN_PF列!\n");
                            }
                            if (!testOrderManualImportFlag && !binRelationManualImportFlag) {
                                validateError.append("TEST_ORDER 和 BIN relation 至少导入一种!\n");
                            }
                            if (validateError.length() > 0) {
                                return validateError.toString();
                            }
                            for (List<ImportTestPlanVo> items : importTestPlanVoMap.values()) {
                                HashMap<String, Pair<BigDecimal, Long>> testItemWithUnitScaleMap = new HashMap<>();
                                HashMap<String, Pair<String, Long>> testItemWithCustomUnitMap = new HashMap<>();
                                HashMap<String, Pair<Long, Long>> testItemWithOrderMap = new HashMap<>();
                                HashMap<Long, Pair<String, Long>> testOrderWithItemMap = new HashMap<>();
                                HashMap<String, List<Pair<BinKey, Long>>> testItemWithBinMap = new HashMap<>();
                                for (ImportTestPlanVo item : items) {
                                    if (item.getUnitScale() != null && zero.compareTo(item.getUnitScale()) >= 0) {
                                        validateError.append("第").append(item.getNum()).append("行UNIT_SCALE不合法!\n");
                                    }
                                    testItemWithUnitScaleMap.compute(item.getTestItem(), (key, oldValue) -> {
                                        if (oldValue == null) {
                                            oldValue = Pair.of(item.getUnitScale(), item.getNum());
                                        } else if (!Objects.equals(oldValue.getKey(), item.getUnitScale())) {
                                            validateError.append("第").append(item.getNum()).append("行与第").append(oldValue.getValue()).append("行UNIT_SCALE不同!\n");
                                        }
                                        return oldValue;
                                    });
                                    testItemWithCustomUnitMap.compute(item.getTestItem(), (key, oldValue) -> {
                                        if (oldValue == null) {
                                            oldValue = Pair.of(item.getCustomUnit(), item.getNum());
                                        } else if (!Objects.equals(oldValue.getKey(), item.getCustomUnit())) {
                                            validateError.append("第").append(item.getNum()).append("行与第").append(oldValue.getValue()).append("行CUSTOM_UNIT不同!\n");
                                        }
                                        return oldValue;
                                    });
                                    if (testOrderManualImportFlag) {
                                        if (item.getTestOrder() == null || item.getTestOrder() < 1) {
                                            validateError.append("第").append(item.getNum()).append("行TEST_ORDER不合法!\n");
                                        }
                                        testItemWithOrderMap.compute(item.getTestItem(), (key, oldValue) -> {
                                            if (oldValue == null) {
                                                oldValue = Pair.of(item.getTestOrder(), item.getNum());
                                            } else if (!Objects.equals(oldValue.getKey(), item.getTestOrder())) {
                                                validateError.append("第").append(item.getNum()).append("行与第").append(oldValue.getValue()).append("行TEST_ORDER不同!\n");
                                            }
                                            return oldValue;
                                        });
                                        testOrderWithItemMap.compute(item.getTestOrder(), (key, oldValue) -> {
                                            if (oldValue == null) {
                                                oldValue = Pair.of(item.getTestItem(), item.getNum());
                                            } else if (!Objects.equals(oldValue.getKey(), item.getTestItem())) {
                                                validateError.append("第").append(item.getNum()).append("行与第").append(oldValue.getValue()).append("行TEST_ORDER相同!\n");
                                            }
                                            return oldValue;
                                        });
                                    }
                                    if (binRelationManualImportFlag) {
                                        if (item.getSbinNum() == null || item.getSbinNum() < 0) {
                                            validateError.append("第").append(item.getNum()).append("行SBIN_NUM不合法!\n");
                                        }
                                        if (!vaildBinPfSet.contains(item.getSbinPf())) {
                                            validateError.append("第").append(item.getNum()).append("行SBIN_PF不合法!\n");
                                        }
                                        if (StringUtils.isBlank(item.getSbinNam())) {
                                            validateError.append("第").append(item.getNum()).append("行SBIN_NAME不合法!\n");
                                        }
                                        if (hbinExistsFlag) {
                                            if (item.getHbinNum() == null || item.getHbinNum() < 0) {
                                                validateError.append("第").append(item.getNum()).append("行HBIN_NUM不合法!\n");
                                            }
                                            if (!vaildBinPfSet.contains(item.getHbinPf())) {
                                                validateError.append("第").append(item.getNum()).append("行HBIN_PF不合法!\n");
                                            }
                                            if (StringUtils.isBlank(item.getHbinNam())) {
                                                validateError.append("第").append(item.getNum()).append("行HBIN_NAME不合法!\n");
                                            }
                                        }
                                        testItemWithBinMap.compute(item.getTestItem(), (key, oldValue) -> {
                                            if (oldValue == null) {
                                                oldValue = new ArrayList<>();
                                            }
                                            BinKey binKey = new BinKey(item.getSbinNum(), item.getSbinNam(), item.getHbinNum(), item.getHbinNam());
                                            Optional<Pair<BinKey, Long>> existsBin = oldValue.stream().filter(t -> t.getKey().equals(binKey)).findFirst();
                                            if (existsBin.isPresent()) {
                                                validateError.append("第").append(item.getNum()).append("行与第").append(existsBin.get().getValue()).append("行BIN relation相同!\n");
                                            } else {
                                                oldValue.add(Pair.of(binKey, item.getNum()));
                                            }
                                            return oldValue;
                                        });
                                    }
                                }
                                if (testOrderManualImportFlag && validTestOrderContinuityFlag && overriteFullFlag) {
                                    List<Map.Entry<Long, Pair<String, Long>>> sortOrders = testOrderWithItemMap.entrySet().stream()
                                            .filter(t -> t.getKey() != null)
                                            .sorted(Comparator.comparingLong(Map.Entry::getKey))
                                            .collect(Collectors.toList());
                                    Map.Entry<Long, Pair<String, Long>> preOrder = null;
                                    for (Map.Entry<Long, Pair<String, Long>> sortOrder : sortOrders) {
                                        if (preOrder == null) {
                                            if (!Objects.equals(1L, sortOrder.getKey())) {
                                                validateError.append("第").append(sortOrder.getValue().getValue()).append("行TEST_ORDER不是从1开始!\n");
                                            }
                                        } else if (!Objects.equals(sortOrder.getKey(), preOrder.getKey() + 1L)) {
                                            validateError.append("第").append(sortOrder.getValue().getValue()).append("行与第").append(preOrder.getValue().getValue()).append("行TEST_ORDER不连续!\n");
                                        }
                                        preOrder = sortOrder;
                                    }
                                }
                            }
                            return validateError.toString();
                        }

                    }))
                    .headRowNumber(1)
                    .excelType(excelTypeEnum)
                    .build();
            excel.read(new ReadSheet(0));
        } catch (Exception e) {
            log.info("导入规则异常：", e);
            if (e instanceof BpmsException) {
                throw e;
            } else if (e instanceof EmptyFileException || e instanceof UnsupportedFileFormatException || e instanceof ExcelAnalysisException) {
                throw new BpmsException(ResponseCode.Common.FILE_NOT_READ_NEED_FIELD);
            }
            throw new BpmsException(ResponseCode.Common.FILE_READ_ERROR, "未知异常");
        } finally {
            if (excel != null) {
                excel.finish();
            }
        }
        log.info("导入 test plan 完成");
    }

    private String sortHbinAndMerge(Map<BinKey, BinRelation> binRelationMap) {
        List<String> hbinList = binRelationMap.values().stream()
                .filter(t -> t.getHbinNum() != null)
                .sorted(Comparator.comparingLong(BinRelation::getHbinNum))
                .map(BinRelation::getHbin)
                .collect(Collectors.toList());
        hbinList
                .addAll(binRelationMap.values().stream()
                        .filter(t -> t.getHbinNum() == null)
                        .map(BinRelation::getHbin)
                        .sorted()
                        .collect(Collectors.toList()));
        return hbinList.stream().distinct().collect(Collectors.joining(COMMA));
    }

    private String sortSbinAndMerge(Map<BinKey, BinRelation> binRelationMap) {
        List<String> sbinList = binRelationMap.values().stream()
                .filter(t -> t.getSbinNum() != null)
                .sorted(Comparator.comparingLong(BinRelation::getSbinNum))
                .map(BinRelation::getSbin)
                .collect(Collectors.toList());
        sbinList
                .addAll(binRelationMap.values().stream()
                        .filter(t -> t.getSbinNum() == null)
                        .map(BinRelation::getSbin)
                        .sorted()
                        .collect(Collectors.toList()));
        return sbinList.stream().distinct().collect(Collectors.joining(COMMA));
    }

    static class BinKey {
        Long sbinNum;
        String sbinNam;
        Long hbinNum;
        String hbinNam;

        public BinKey(Long sbinNum, String sbinNam, Long hbinNum, String hbinNam) {
            this.sbinNum = sbinNum;
            this.sbinNam = sbinNam;
            this.hbinNum = hbinNum;
            this.hbinNam = hbinNam;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            BinKey binKey = (BinKey) o;
            return Objects.equals(sbinNum, binKey.sbinNum) && Objects.equals(sbinNam, binKey.sbinNam) && Objects.equals(hbinNum, binKey.hbinNum) && Objects.equals(hbinNam, binKey.hbinNam);
        }

        @Override
        public int hashCode() {
            return Objects.hash(sbinNum, sbinNam, hbinNum, hbinNam);
        }
    }

    private TestProgramTestPlan buildTestProgramTestPlan(List<ImportTestPlanVo> items, ImportTestPlanDimension dimension, Date date, boolean testOrderManualImportFlag, boolean binRelationManualImportFlag) {
        ImportTestPlanVo item = items.get(0);
        String[] split = item.getTestItem().split(":", 2);
        long testNum = 0L;
        String testTxt = item.getTestItem();
        if (split.length > 1) {
            if (NumberUtils.isDigits(split[0])) {
                testNum = Long.parseLong(split[0]);
            }
            testTxt = split[1];
        }
        Long testOrder = null;
        if (testOrderManualImportFlag) {
            testOrder = item.getTestOrder();
        }
        String binRelation = EMPTY_JSON_ARRAY;
        String hbin = EMPTY;
        String sbin = EMPTY;
        if (binRelationManualImportFlag) {
            Map<BinKey, BinRelation> binRelationMap = convertToBinRelationMapForImport(items);
            binRelation = JSON.toJSONString(binRelationMap.values());
            hbin = sortHbinAndMerge(binRelationMap);
            sbin = sortSbinAndMerge(binRelationMap);
        }
        return new TestProgramTestPlan()
                .setCustomer(RequestContext.getCustomer())
                .setSubCustomer(RequestContext.getSubCustomer())
                .setUploadType(dimension.getUploadType())
                .setTestArea(dimension.getTestArea())
                .setFactory(dimension.getFactory())
                .setFactorySite(dimension.getFactorySite())
                .setFab(Constant.EMPTY)
                .setFabSite(Constant.EMPTY)
                .setDeviceId(dimension.getDeviceId())
                .setTestStage(dimension.getTestStage())
                .setLotType(dimension.getLotType())
                .setTestProgram(dimension.getTestProgram())
                .setTestItem(item.getTestItem())
                .setTestOrder(testOrder)
                .setTestitemType("P")
                .setTestNum(testNum)
                .setTestTxt(testTxt)
                .setBinRelation(binRelation)
                .setHbins(hbin)
                .setSbins(sbin)
                .setUnitScale(item.getUnitScale())
                .setCustomUnit(item.getCustomUnit())
                .setTestOrderManualImportFlag(testOrderManualImportFlag ? 1 : 0)
                .setBinRelationManualImportFlag(binRelationManualImportFlag ? 1 : 0)
                .setCreateUser(RequestContext.getUserName())
                .setUpdateUser(RequestContext.getUserName())
                .setCreateTime(date)
                .setUpdateTime(date);
    }

    private Map<BinKey, BinRelation> parseExistsBinRelation(String binRelation) {
        if (StringUtils.isBlank(binRelation)) {
            return new HashMap<>();
        } else {
            return convertToBinRelationMap(JSON.parseArray(binRelation, BinRelation.class));
        }
    }

    private Map<BinKey, BinRelation> convertToBinRelationMapForImport(List<ImportTestPlanVo> importTestPlanVos) {
        return convertToBinRelationMap(
                importTestPlanVos.stream()
                        .map(t -> new BinRelation(
                                        t.getHbinNum(), t.getHbinNam(), t.getHbinPf(), buildBin(HBIN_NAME_PREFIX, t.getHbinNum(), t.getHbinNam()),
                                        t.getSbinNum(), t.getSbinNam(), t.getSbinPf(), buildBin(SBIN_NAME_PREFIX, t.getSbinNum(), t.getSbinNam())
                                )
                        ).collect(Collectors.toList())
        );
    }

    private String buildBin(String prefix, Long binNum, String binNam) {
        if (binNum != null) {
            return prefix + binNum + MIDDLE_LINE + binNam;
        } else {
            return Constant.EMPTY;
        }
    }

    private Map<BinKey, BinRelation> convertToBinRelationMap(List<BinRelation> binRelations) {
        return binRelations.stream().collect(Collectors.toMap(t -> new BinKey(t.getSbinNum(), t.getSbinNam(), t.getHbinNum(), t.getHbinNam()), t -> t, (v1, v2) -> v1));
    }

    private String defaultString(Object obj) {
        if (obj == null) {
            return Constant.EMPTY;
        }
        return obj.toString();
    }

    private static Integer getIntegerValue(String str) {
        try {
            return str.isEmpty() ? null : Double.valueOf(str).intValue();
        } catch (Exception e) {
            throw new RuntimeException(str + " 不是数字格式!");
        }
    }

    private static Long getLongValue(String str) {
        try {
            return str.isEmpty() ? null : Double.valueOf(str).longValue();
        } catch (Exception e) {
            throw new RuntimeException(str + " 不是数字格式!");
        }
    }

    private static BigDecimal getDecimalValue(String str) {
        try {
            return str.isEmpty() ? null : new BigDecimal(str);
        } catch (Exception e) {
            throw new RuntimeException(str + " 不是数字格式!");
        }
    }
}
