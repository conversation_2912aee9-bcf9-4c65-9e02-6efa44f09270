package com.guwave.datahub.bpms.app.vo.bz.dataQualityOverview.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.guwave.datahub.bpms.common.constant.DataQualityOverviewPageDropdownField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(title = "数据质量概览下拉框请求体")
public class DataQualityOverviewDropDownFilterVo {

    @Schema(title = "当前的过滤字段", description = "当前的过滤字段，不能为空，取值为：SUB_CUSTOMER,FACTORY,DEVICE_ID,TEST_AREA,TEST_PROGRAM,TEST_STAGE", example = "FACTORY")
    @NotNull(message = "过滤字段不能为空")
    private DataQualityOverviewPageDropdownField filterField;

    @Schema(title = "subCustomerList", description = "sub_customer列表", example = "[\"AMD\"]")
    private List<String> subCustomerList;

    @Schema(title = "factoryList", description = "factory列表", example = "[\"VTEST\"]")
    private List<String> factoryList;

    @Schema(title = "deviceIdList", description = "device_id列表", example = "[\"SS521BB-DEFAULT\"]")
    private List<String> deviceIdList;

    @Schema(title = "testArea", example = "CP")
    private String testArea;

    @Schema(title = "testProgramList", description = "test_program列表", example = "[\"SS535B_3308_CP2_S0.pln\"]")
    private List<String> testProgramList;

    @Schema(title = "testStageList", description = "test_stage列表", example = "[\"CP2\"]")
    private List<String> testStageList;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(title = "startTime", description = "开始时间", example = "2025-05-01 12:00:00")
    private LocalDateTime startTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(title = "endTime", description = "结束时间", example = "2025-05-01 12:00:00")
    private LocalDateTime endTime;

}