package com.guwave.datahub.bpms.app.vo.bz.dataTimeliness.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.guwave.datahub.bpms.common.constant.DataTimelinessPageDropdownField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Schema(title = "下拉框过滤条件请求体")
@Data
public class DataTimelinessDropDownFilterVo {

    @Schema(title = "当前的过滤字段", description = "当前的过滤字段，不能为空，取值为：SUB_CUSTOMER, FACTORY, DEVICE_ID, TEST_AREA, TEST_PROGRAM, TEST_STAGE, LOT_ID, SBLOT_ID, WAFER_NO", example = "FACTORY")
    @NotNull(message = "过滤字段不能为空")
    private DataTimelinessPageDropdownField filterField;

    @Schema(description = "子客户", example = "[\"AMD\"]")
    private List<String> subCustomerList;

    @Schema(description = "工厂", example = "[\"AMD\"]")
    private List<String> factoryList;

    @Schema(description = "测试区域", example = "CP")
    private String testArea;

    @Schema(description = "deviceId", example = "[\"deviceId-1\"]")
    private List<String> deviceIdList;

    @Schema(description = "testProgram", example = "[\"program-1\"]")
    private List<String> testProgramList;

    @Schema(description = "testStage", example = "[\"CP2\"]")
    private List<String> testStageList;

    @Schema(description = "lotId", example = "[\"lotId-1\"]")
    private List<String> lotIdList;

    @Schema(description = "waferNo", example = "[\"14\"]")
    private List<String> waferNoList;

    @Schema(description = "sblotId", example = "[\"sblotId-1\"]")
    private List<String> sblotIdList;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "startTime", example = "开始时间, 格式 yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "endTime", example = "结束时间，格式 yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

}

