package com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 2025/4/27 18:20
 * RepairInfoSaveVo
 *
 * <AUTHOR>
 */
@Schema(title = "触发/预览数据修复参数")
@Data
@EqualsAndHashCode(callSuper = false)
public class RepairInfoSaveVo {

    @Schema(description = "修复的文件信息")
    private List<RepairFileInfo> repairFileInfos;


    @Schema(description = "修改字段内容")
    private List<RepairFieldInfoVo> repairFieldInfoVos;


    @Data
    public static class RepairFileInfo {
        @Schema(description = "fileId")
        private Long fileId;

        @Schema(description = "文件名称")
        private String fileName;

        @Schema(description = "testArea")
        private String testArea;

        @Schema(description = "修改后的RetestHbinNumber")
        private String modifyRetestHbinNumber;

        @Schema(description = "修改后的FlowId")
        private String modifyFlowId;

        @Schema(description = "原始的RetestHbinNumber")
        private String originalRetestHbinNumber;

        @Schema(description = "原始的FlowId")
        private String originalFlowId;
    }


}
