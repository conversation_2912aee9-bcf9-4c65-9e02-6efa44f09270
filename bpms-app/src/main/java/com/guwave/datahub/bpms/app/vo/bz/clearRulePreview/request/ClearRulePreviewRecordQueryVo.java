package com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.request;

import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

@Schema(title = "清洗规则预览查询条件")
@Data
@EqualsAndHashCode(callSuper = false)
public class ClearRulePreviewRecordQueryVo extends PageableRequestVo {
    @Schema(title = "testArea", example = "CP")
    private String testArea;

    @Schema(title = "deviceId", example = "device1")
    private String deviceId;

    @Schema(title = "lotId", example = "lot1")
    private String lotId;

    @Schema(title = "waferNo", example = "1")
    private String waferNo;

    @Schema(title = "lotType", example = "PRODUCTION")
    private String lotType;

    @Schema(title = "testStage", example = "CP1")
    private String testStage;

    @Schema(title = "factory", example = "TEST")
    private String factory;

    @Schema(title = "factorySite", example = "TEST")
    private String factorySite;

    @Schema(title = "sblotId", example = "sblot1")
    private String sblotId;

    @Schema(title = "fileCategory", example = "STDF")
    private String fileCategory;

    @Schema(title = "processStatus", example = "CREATE")
    private String processStatus;

    @Schema(title = "remoteOriginFilePath", example = "/GUWAVE/XXX")
    @Length(max = 1000)
    private String remoteOriginFilePath;

    @Schema(title = "remoteOriginFileName", example = "XXX")
    @Length(max = 256)
    private String remoteOriginFileName;

    @Schema(title = "fileName", example = "XXX")
    @Length(max = 256)
    private String fileName;

    @Schema(title = "exceptionType", example = "OTHER_EXCEPTION")
    private String exceptionType;

    @Schema(title = "startTime", description = "起始时间", example = "2024-04-03 00:00:00")
    private String startTime;

    @Schema(title = "endTime", description = "结束时间", example = "2024-04-11 23:59:59")
    private String endTime;

}
