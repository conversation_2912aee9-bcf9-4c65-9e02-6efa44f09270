package com.guwave.datahub.bpms.app.vo.bz.dataQualityOverview.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Schema(title = "数据质量概览下拉框结果")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataQualityOverviewDropDownVo {
    @Schema(title = "label")
    private String label;
    @Schema(title = "value")
    private String value;

    public static DataQualityOverviewDropDownVo of(String value) {
        DataQualityOverviewDropDownVo vo = new DataQualityOverviewDropDownVo();
        vo.setLabel(value);
        vo.setValue(value);
        return vo;
    }
}
