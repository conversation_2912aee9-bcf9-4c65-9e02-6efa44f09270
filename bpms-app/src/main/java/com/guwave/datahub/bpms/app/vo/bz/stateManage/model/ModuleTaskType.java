package com.guwave.datahub.bpms.app.vo.bz.stateManage.model;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum ModuleTaskType {

    /**
     * collectx 从ftp拉取文件任务
     */
    COLLECTX_PULL_FILE_FROM_FTP_TASK("COLLECTX_PULL_FILE_FROM_FTP_TASK"),

    /**
     * collectx python脚本转换任务
     */
    COLLECTX_PYTHON_SCRIPT_CONVERT_TASK("COLLECTX_PYTHON_SCRIPT_CONVERT_TASK"),

    /**
     * collectx python脚本合并LOG、MAP任务
     */
    COLLECTX_PYTHON_SCRIPT_MERGE_LOG_MAP_TASK("COLLECTX_PYTHON_SCRIPT_MERGE_LOG_MAP_TASK"),

    /**
     * source-agent 预解析任务
     */
    SOURCE_AGENT_PRE_PARSE_TASK("SOURCE_AGENT_PRE_PARSE_TASK"),

    /**
     * source-agent 正式解析任务
     */
    SOURCE_AGENT_PARSE_TASK("SOURCE_AGENT_PARSE_TASK"),

    /**
     * DIE  DIE数仓计算任务
     */
    DIE_DATAWARE_CALCULATE_TASK_CREATE("DIE_DATAWARE_CALCULATE_TASK_CREATE"),
    DIE_DATAWARE_CALCULATE_TASK_PROCESSING("DIE_DATAWARE_CALCULATE_TASK_PROCESSING"),

    /**
     * MES 数仓计算任务
     */
    MES_DATAWARE_CALCULATE_TASK("MES_DATAWARE_CALCULATE_TASK"),

    /**
     * 手动上传 数仓计算任务
     */
    MANUAL_DATAWARE_CALCULATE_TASK("MANUAL_DATAWARE_CALCULATE_TASK"),

    /**
     * 手动上传 删除文件任务
     */
    MANUAL_DELETE_FILE_TASK("MANUAL_DELETE_FILE_TASK"),

    /**
     * linkx-scheduler 调度任务
     */
    LINKX_SCHEDULER_TASK("LINKX_SCHEDULER_TASK"),

    /**
     * compute-scheduler 调度任务
     */
    COMPUTE_SCHEDULER_PLATFORM_TASK_CREATE("COMPUTE_SCHEDULER_PLATFORM_TASK_CREATE"),
    COMPUTE_SCHEDULER_PLATFORM_TASK_PROCESSING("COMPUTE_SCHEDULER_PLATFORM_TASK_PROCESSING"),

    ;

    private final String type;

    ModuleTaskType(String type) {
        this.type = type;
    }

    private static final Map<String, ModuleTaskType> TYPE_MAP = Stream.of(ModuleTaskType.values()).collect(Collectors.toMap(ModuleTaskType::getType, Function.identity()));

    public static ModuleTaskType of(String type) {
        return TYPE_MAP.get(type);
    }

    public String getType() {
        return type;
    }
}
