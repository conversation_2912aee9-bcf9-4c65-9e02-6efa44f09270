package com.guwave.datahub.bpms.app.callback;

import com.alibaba.fastjson.JSON;
import com.guwave.datahub.bpms.app.service.bz.TaskService;
import com.guwave.datahub.bpms.app.util.ComputeUtil;
import com.guwave.datahub.bpms.dao.repository.dw.ExpireDeleteRecordRepository;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ExpireDeleteRecord;
import com.guwave.onedata.next.compute.api.callback.ComputeCallback;
import com.guwave.onedata.next.compute.common.message.ComputeResultMessage;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class SparkReloadTaskFinishCallback implements ComputeCallback {
    public static final Map<FileCategory, String> supportComputeCodeMap = new HashMap<FileCategory, String>() {{
        put(FileCategory.BIT_MEM, "com.guwave.onedata.dataware.dw.testItem.spark.task.impl.ReloadBitmemTestItemTask");
        put(FileCategory.STDF, "com.guwave.onedata.dataware.dw.testItem.spark.task.impl.ReloadTestItemTask");
        put(FileCategory.RAW_DATA, "com.guwave.onedata.dataware.dw.testItem.spark.task.impl.ReloadTestItemTask");
    }};

    @Autowired
    private ExpireDeleteRecordRepository expireDeleteRecordRepository;
    @Autowired
    private TaskService taskService;

    @Override
    public void doCallback(ComputeResultMessage computeResultMessage) {
        ProcessStatus processStatus = ComputeUtil.convertProcessStatus(computeResultMessage.getProcessStatus());
        if (processStatus == ProcessStatus.PROCESSING) {
            LOGGER.info("computeResultMessage {} 忽略", JSON.toJSONString(computeResultMessage));
            return;
        }
        List<ExpireDeleteRecord> expireDeleteRecords = expireDeleteRecordRepository.findByUniqueIdAndProcessStatus(computeResultMessage.getUniqueId(), ProcessStatus.RELOADING);
        if (CollectionUtils.isEmpty(expireDeleteRecords)) {
            LOGGER.info("computeResultMessage {} 没有对应的记录", JSON.toJSONString(computeResultMessage));
            return;
        }

        taskService.finishReload(expireDeleteRecords, computeResultMessage);
    }

    @Override
    public boolean isSupport(String computeCode) {
        return supportComputeCodeMap.containsValue(computeCode);
    }
}
