package com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.request;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(title = "数据完整性修改")
public class DataIntegrityFileLabelVo {


    @Schema(description = "文件名列表", example = "[\"a.stdf.zip\",\"b.stdf.zip\"]")
    private List<String> fileNames;

    @Schema(description = "备注", example = "123")
    private String remark;

    @Schema(description = "fileLabel", example = "未处理")
    private String fileLabel;
}
