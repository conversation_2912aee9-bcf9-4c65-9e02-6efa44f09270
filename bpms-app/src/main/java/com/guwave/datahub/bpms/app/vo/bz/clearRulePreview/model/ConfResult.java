package com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConfResult {
    private String field;
    private Object value;
    private Integer order;
    private String ruleExpression;
    private String tip;

    public static ConfResult of(ParseField parseField) {
        return new ConfResult(parseField.getField(), null, parseField.getOrder(), null, null);
    }
}
