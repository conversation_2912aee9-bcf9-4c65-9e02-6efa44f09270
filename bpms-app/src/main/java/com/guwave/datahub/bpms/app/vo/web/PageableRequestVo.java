package com.guwave.datahub.bpms.app.vo.web;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Schema(title = "分页请求信息")
@Data
public class PageableRequestVo {

    public static final Integer DEFAULT_PAGE_NUM = 1;
    public static final Integer DEFAULT_PAGE_SIZE = 10;

    @Schema(title = "页码", description = "从1开始", example = "1", defaultValue = "1")
    @Min(1)
    private Integer pageIndex = DEFAULT_PAGE_NUM;


    @Schema(title = "每页条数", example = "10", defaultValue = "10")
    @NotNull
    private Integer pageSize = DEFAULT_PAGE_SIZE;

    public Integer getJpaPageIndex() {
        return pageIndex - 1;
    }

    public Pageable toPageable() {
        return PageRequest.of(this.getJpaPageIndex(), this.getPageSize());
    }
}
