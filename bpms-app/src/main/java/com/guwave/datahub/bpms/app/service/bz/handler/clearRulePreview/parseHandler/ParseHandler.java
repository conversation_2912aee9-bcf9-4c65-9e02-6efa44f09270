package com.guwave.datahub.bpms.app.service.bz.handler.clearRulePreview.parseHandler;

import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import com.google.common.collect.Lists;
import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.service.bz.handler.clearRulePreview.visitor.MultiThreadVisitor;
import com.guwave.datahub.bpms.app.util.FileUtil;
import com.guwave.datahub.bpms.app.util.HdfsUtil;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model.ClearRulePreviewResult;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model.SettingSnapshot;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.dao.domain.clearRulePreview.ClearRulePreviewRecord;
import com.guwave.datahub.bpms.dao.repository.dw.DataClearRuleRepository;
import com.guwave.datahub.bpms.dao.repository.dw.EcidRuleRepository;
import com.guwave.datahub.bpms.dao.repository.dw.LotRelationSyncRepository;
import com.guwave.datahub.bpms.dao.repository.dw.UidRuleRepository;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.DataClearRule;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.EcidRule;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotRelationSync;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.UidRule;
import com.guwave.onedata.dataware.parser.stdf.ecidParse.EcidDieInfo;
import com.guwave.onedata.dataware.parser.stdf.ecidParse.EcidParameterInfo;
import com.guwave.onedata.dataware.parser.stdf.ecidParse.EcidParseUtil;
import com.guwave.onedata.dataware.parser.stdf.ecidParse.EcidRuleSetting;
import com.guwave.onedata.dataware.parser.stdf.model.ClearRule;
import com.guwave.onedata.dataware.parser.stdf.model.FileMainData;
import com.guwave.onedata.dataware.parser.stdf.uidParse.UidDieInfo;
import com.guwave.onedata.dataware.parser.stdf.uidParse.UidParameterInfo;
import com.guwave.onedata.dataware.parser.stdf.uidParse.UidRuleSetting;
import com.guwave.onedata.dataware.parser.stdf.visitor.Visitor;
import com.guwave.onedata.dataware.sdk.model.ecid.CpLotIdInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;
import static java.nio.charset.StandardCharsets.UTF_8;

@Component
@Slf4j
public abstract class ParseHandler {

    @Autowired
    private DataClearRuleRepository dataClearRuleRepository;
    @Autowired
    private LotRelationSyncRepository lotRelationSyncRepository;
    @Autowired
    private EcidRuleRepository ecidRuleRepository;
    @Autowired
    private UidRuleRepository uidRuleRepository;
    @Autowired
    private HdfsUtil hdfsUtil;

    public abstract boolean isSuppport(ClearRulePreviewRecord record);

    public void parseFile(ClearRulePreviewRecord record, File file, ClearRulePreviewResult clearRulePreviewResult) {
        // 获取ecid规则
        List<EcidRule> ecidRules = queryEcidRules(record);
        clearRulePreviewResult.getSettingSnapshot().getDwEcidRules().addAll(ecidRules.stream().map(SettingSnapshot.EcidRuleSnapshot::of).collect(Collectors.toList()));
        Map<String, SettingSnapshot.EcidRuleSnapshot> ecidRuleMap = clearRulePreviewResult.getSettingSnapshot().getDwEcidRules().stream().collect(Collectors.toMap(SettingSnapshot.EcidRuleSnapshot::getTestProgram, t -> t, (v1, v2) -> v1));
        SettingSnapshot.EcidRuleSnapshot ecidRule = ecidRuleMap.getOrDefault(record.getTestProgram(), ecidRuleMap.get(EMPTY));
        EcidRuleSetting ecidRuleSetting = ecidRule != null ? new EcidRuleSetting(ecidRule.getTestProgram(), ecidRule.getScript(), ecidRule.getDecodeEcid(), ecidRule.getDecodeEcidFields(), ecidRule.getDecodeLotId(),
                ecidRule.getParameterWaferNo(), ecidRule.getParameterXAddr(), ecidRule.getParameterYAddr(), ecidRule.getRuleType(), ecidRule.getCheckType(),
                ecidRule.getParameterType(), ecidRule.getFieldPrefix(), ecidRule.getRuleMode()) : null;

        List<LotRelationSync> lotRelationSyncs = queryLotRelationSync(record);
        clearRulePreviewResult.getSettingSnapshot().getDwLotRelationSyncs().addAll(lotRelationSyncs.stream().map(SettingSnapshot.LotRelationSyncSnapshot::of).collect(Collectors.toList()));
        String configCpLotId = CollectionUtils.isNotEmpty(lotRelationSyncs) ? lotRelationSyncs.get(0).getCpLotId() : Constant.EMPTY;
        CpLotIdInfo cpLotInfo = CollectionUtils.isNotEmpty(lotRelationSyncs) ? generateCpLotInfo(lotRelationSyncs.get(0)) : null;

        List<UidRule> uidRules = queryUidRules(record);
        clearRulePreviewResult.getSettingSnapshot().getDwUidRules().addAll(uidRules.stream().map(SettingSnapshot.UidRuleSnapshot::of).collect(Collectors.toList()));
        SettingSnapshot.UidRuleSnapshot uidRule = CollectionUtils.isNotEmpty(clearRulePreviewResult.getSettingSnapshot().getDwUidRules()) ? clearRulePreviewResult.getSettingSnapshot().getDwUidRules().get(0) : null;
        UidRuleSetting uidRuleSetting = uidRule != null ? new UidRuleSetting(uidRule.getParameterUid(), uidRule.getParameterType(), uidRule.getMatchType(), uidRule.getConcatSign(), uidRule.getRuleType()) : null;

        // 解析文件
        List<Visitor> visitors = parseData(record, file, clearRulePreviewResult, ecidRuleSetting, configCpLotId, uidRuleSetting, cpLotInfo);

        // 明细信息写入文件
        writeDataToCsv(record, file, visitors, ecidRule, uidRule);
    }

    private void writeDataToCsv(ClearRulePreviewRecord record, File file, List<Visitor> visitors, SettingSnapshot.EcidRuleSnapshot ecidRule, SettingSnapshot.UidRuleSnapshot uidRule) {
        Long dieDataCnt = 0L;
        Map<String, Long> testItemCntMap = new HashMap<>();
        for (Visitor tmp : visitors) {
            MultiThreadVisitor visitor = (MultiThreadVisitor) tmp;
            visitor.getTestItemCntMap().forEach((item, cnt) -> testItemCntMap.compute(item, (k, v) -> {
                if (v == null) {
                    v = 0L;
                }
                return v + cnt;
            }));
            dieDataCnt += visitor.getDieDataCnt();
        }
        if (dieDataCnt == 0L) {
            throw new FileLoadException(FileLoadExceptionInfo.STDF_NOT_FOUND_DIE_DATA_EXCEPTION, FileLoadExceptionInfo.STDF_NOT_FOUND_DIE_DATA_EXCEPTION.getMessage(), null);
        }

        ArrayList<File> files = new ArrayList<>();
        File testItemDetailCsv = writeTestItemFile(file, testItemCntMap);
        files.add(testItemDetailCsv);

        if (record.getGivenTestArea() == TestArea.FT) {
            File ecidCsv = writeEcidFile(file, visitors, ecidRule);
            files.add(ecidCsv);
        }
        File uidCsv = writeUidFile(file, visitors, uidRule);
        files.add(uidCsv);

        File detailZipCompresFile = new File(file.getAbsolutePath() + "_detail.zip");
        try {
            FileUtil.compressMutilFileToZip(files, detailZipCompresFile);
            File previewDetailFileHdfs = new File(new File(record.getHdfsPath()).getParent(), detailZipCompresFile.getName());
            hdfsUtil.uploadToHDFSByFileSystem(detailZipCompresFile.getAbsolutePath(), previewDetailFileHdfs.getParent(), 1);
            record.setPreviewDetailFileHdfsPath(previewDetailFileHdfs.getAbsolutePath());
        } catch (Exception e) {
            log.info("处理明细文件异常：", e);
            throw new BpmsException(e, ResponseCode.Common.FILE_READ_ERROR, "IO异常");
        }
    }

    private static File writeTestItemFile(File file, Map<String, Long> testItemCntMap) {
        // testItemDetail file
        List<List<Object>> testItemLines = testItemCntMap.entrySet().stream().sorted((l, r) -> StringUtils.compare(l.getKey(), r.getKey()))
                .map(t -> {
                    List<Object> lines = Lists.newArrayList(t.getKey(), t.getValue());
                    return lines;
                }).collect(Collectors.toList());
        File testItemDetailCsv = new File(file.getAbsolutePath() + "_testItemDetail.csv");
        try (CsvWriter testItemDetailwriter = CsvUtil.getWriter(testItemDetailCsv.getAbsolutePath(), UTF_8)) {
            testItemDetailwriter.writeHeaderLine("testItem", "count");
            testItemDetailwriter.write(testItemLines);
        }
        return testItemDetailCsv;
    }

    private static File writeEcidFile(File file, List<Visitor> visitors, SettingSnapshot.EcidRuleSnapshot ecidRule) {
        // ecid file
        File ecidCsv = new File(file.getAbsolutePath() + "_ecid.csv");
        List<String> ecidCsvHead = Lists.newArrayList("cPartId", "ecid", "isStandardEcid", "waferLotId", "waferNo", "xCoord", "yCoord");
        DecodeParameterTypeEnum parameterType = ecidRule == null ? null : DecodeParameterTypeEnum.of(ecidRule.getParameterType());
        List<String> parameterText = ((MultiThreadVisitor) visitors.get(0)).getParameterText();
        ecidCsvHead.addAll(parameterText);
        try (CsvWriter ecidWriter = CsvUtil.getWriter(ecidCsv.getAbsolutePath(), UTF_8)) {
            ecidWriter.writeHeaderLine(ecidCsvHead.toArray(new String[0]));
            for (Visitor tmp : visitors) {
                MultiThreadVisitor visitor = (MultiThreadVisitor) tmp;
                List<List<Object>> lines = visitor.getEcidDieInfoMap().values().stream().sorted(Comparator.comparingInt(EcidDieInfo::getcPartId))
                        .map(ecidDieInfo -> {
                            List<Object> line = Lists.newArrayList(ecidDieInfo.getcPartId(), ecidDieInfo.getEcid(), ecidDieInfo.getIsStandardEcid(), ecidDieInfo.getWaferLotId(), ecidDieInfo.getWaferNo(), ecidDieInfo.getxCoord(), ecidDieInfo.getyCoord());
                            if (parameterType != null) {
                                if (EcidParseUtil.testItemDecodeParameterTypeEnumGetEfuseKvMap.containsKey(parameterType)) {
                                    Map<String, Object> efuseMap = ecidDieInfo.getEfuseInfosMap().values().stream().flatMap(t -> t.values().stream()).collect(Collectors.toMap(EcidParameterInfo::getParameter, ParseHandler::generateEcidParameterRes, (v1, v2) -> v1));
                                    parameterText.forEach(text -> line.add(efuseMap.get(text)));
                                } else {
                                    line.add(ecidDieInfo.getEfuseInfosMap().get(null).get(parameterType.getValue()).getParameter());
                                }
                            }
                            return line;
                        }).collect(Collectors.toList());
                ecidWriter.write(lines);
            }
        }
        return ecidCsv;
    }


    private static File writeUidFile(File file, List<Visitor> visitors, SettingSnapshot.UidRuleSnapshot uidRule) {
        // uid file
        File uidCsv = new File(file.getAbsolutePath() + "_uid.csv");
        List<String> uidCsvHead = Lists.newArrayList("cPartId", "uid");
        List<String> parameterText = new ArrayList<>();
        if (uidRule != null) {
            parameterText.addAll(Arrays.stream(uidRule.getParameterUid().split(COMMA)).flatMap(t -> Arrays.stream(t.split(SPLIT_LOGICAL_OR))).collect(Collectors.toList()));
        }
        uidCsvHead.addAll(parameterText);
        try (CsvWriter uidWriter = CsvUtil.getWriter(uidCsv.getAbsolutePath(), UTF_8)) {
            uidWriter.writeHeaderLine(uidCsvHead.toArray(new String[0]));
            for (Visitor tmp : visitors) {
                MultiThreadVisitor visitor = (MultiThreadVisitor) tmp;
                List<List<Object>> lines = visitor.getUidDieInfoMap().values().stream().sorted(Comparator.comparingInt(UidDieInfo::getcPartId))
                        .map(uidDieInfo -> {
                            List<Object> line = Lists.newArrayList(uidDieInfo.getcPartId(), uidDieInfo.getUid());
                            Map<String, Object> efuseMap = uidDieInfo.getUidParameterInfoMap().values().stream().collect(Collectors.toMap(UidParameterInfo::getParameter, ParseHandler::generateUidParameterRes, (v1, v2) -> v1));
                            parameterText.forEach(text -> line.add(efuseMap.get(text)));
                            return line;
                        }).collect(Collectors.toList());
                uidWriter.write(lines);
            }
        }
        return uidCsv;
    }

    protected abstract List<Visitor> parseData(ClearRulePreviewRecord record, File file, ClearRulePreviewResult clearRulePreviewResult, EcidRuleSetting ecidRuleSetting, String configCpLotId, UidRuleSetting uidRuleSetting, CpLotIdInfo cpLotIdInfo);

    protected List<DataClearRule> queryDataClearRules(ClearRulePreviewRecord record) {
        return dataClearRuleRepository.findAllByCustomerAndFactoryAndTestArea(record.getCustomer(), record.getFactory(), record.getTestArea());
    }

    protected Map<StdfFieldType, List<ClearRule>> filterDataClearRules(List<DataClearRule> clearRules, ClearRulePreviewRecord record) {
        Map<StdfFieldType, List<ClearRule>> resRules = new HashMap<>();
        // 支持一家工厂相同field_type、rule_type规则配置不同的device和factory维度
        clearRules.stream()
                .collect(Collectors.groupingBy(rule -> rule.getFieldType() + UNDER_LINE + rule.getRuleType()))
                .forEach((k, value) -> {
                    Map<String, DataClearRule> deviceIdWithRuleMap = value.stream()
                            .collect(Collectors.groupingBy(DataClearRule::getDeviceId, Collectors.collectingAndThen(Collectors.toList(), list -> list.get(0))));

                    DataClearRule rule = deviceIdWithRuleMap.getOrDefault(record.getDeviceId(), deviceIdWithRuleMap.get(EMPTY));
                    if (rule != null) {
                        StdfFieldType fieldType = rule.getFieldType();
                        List<ClearRule> ruleList = resRules.getOrDefault(fieldType, new ArrayList<>());
                        ruleList.add(new ClearRule(rule.getRuleType(), rule.getRule()));
                        resRules.put(fieldType, ruleList);
                    }
                });
        return resRules;
    }

    protected List<EcidRule> queryEcidRules(ClearRulePreviewRecord record) {
        return ecidRuleRepository.findByCustomerAndDeviceIdAndTestStage(record.getCustomer(), record.getDeviceId(), record.getTestStage());
    }

    protected List<UidRule> queryUidRules(ClearRulePreviewRecord record) {
        return uidRuleRepository.findByCustomerAndSubCustomerAndTestAreaAndFactoryAndFactorySite(record.getCustomer(), record.getSubCustomer(), record.getTestArea().getArea(), record.getFactory(), record.getFactorySite());
    }

    protected List<LotRelationSync> queryLotRelationSync(ClearRulePreviewRecord record) {
        List<LotRelationSync> lotRelationSyncs = lotRelationSyncRepository.findAllByCustomerAndFtDeviceIdAndFtLotId(record.getCustomer(), record.getDeviceId(), record.getLotId());
        if (CollectionUtils.isEmpty(lotRelationSyncs)) {
            return new ArrayList<>();
        }
        Map<String, List<LotRelationSync>> lotRelationMap = lotRelationSyncs.stream().collect(Collectors.groupingBy(t -> t.getFtFactory() != null ? t.getFtFactory() : EMPTY));
        List<LotRelationSync> filterLotRelations = lotRelationMap.getOrDefault(record.getFactory(), lotRelationMap.get(EMPTY));
        if (CollectionUtils.isEmpty(filterLotRelations)) {
            return new ArrayList<>();
        }
        return filterLotRelations;
    }

    protected Consumer<FileMainData> generateFillFileMainDataConsumer(ClearRulePreviewRecord record) {
        return fileMainData -> {
            fileMainData.setTestArea(record.getTestArea());

            fileMainData.setCustomer(record.getCustomer());
            fileMainData.setSubCustomer(record.getSubCustomer());
            fileMainData.setFileCategory(record.getFileCategory());
            fileMainData.setTestStage(record.getTestStage());
            fileMainData.setFactory(record.getFactory());
            fileMainData.setFactorySite(record.getFactorySite());
            fileMainData.setLotType(record.getLotType());
            fileMainData.setFileInfoId(0L);
            fileMainData.setFileName(record.getFileName());
            fileMainData.setDeviceId(record.getDeviceId());
            fileMainData.setLotId(record.getLotId());
            fileMainData.setOriginWaferId(record.getOriginWaferId());
            fileMainData.setWaferNo(record.getWaferNo());
            fileMainData.setSblotId(record.getSblotId());
            fileMainData.setTestProgram(record.getTestProgram());
            fileMainData.setTestTemperature(record.getTestTemperature());
            fileMainData.setTestProgramVersion(record.getTestProgramVersion());
            fileMainData.setTesterName(record.getTesterName());
            fileMainData.setTesterType(record.getTesterType());
            fileMainData.setProbecardLoadboardId(record.getProbecardLoadboardId());
            fileMainData.setStartT(record.getStartT());
            fileMainData.setFinishT(record.getFinishT());
            fileMainData.setTestCod(record.getTestCod());
            fileMainData.setPkgTyp(record.getPkgTyp());
            fileMainData.setFloorId(record.getFloorId());
            fileMainData.setRetestBinNum(record.getRetestBinNum());
            fileMainData.setPosX(record.getPosX());
            fileMainData.setPosY(record.getPosY());
            fileMainData.setNotch(record.getNotch());
            fileMainData.setProcess(record.getProcess());
            fileMainData.setCreateUser(SYSTEM);
            fileMainData.setUploadType(UploadType.AUTO);

            fileMainData.setOfflineRetest(record.getOfflineRetest());
            fileMainData.setDupRetest(record.getDupRetest());
            fileMainData.setBatchNum(record.getBatchNum());
            fileMainData.setInterrupt(record.getInterrupt());

            fileMainData.setConditionSet(record.getConditionSet());
            fileMainData.setRealWaferId(record.getRealWaferId());
        };
    }

    private static String generateEcidParameterRes(EcidParameterInfo ecidParameterInfo) {
        StringBuilder res = new StringBuilder();
        if (ecidParameterInfo.getResult() != null) {
            res.append(ecidParameterInfo.getResult());
        }
        if (StringUtils.isNotEmpty(ecidParameterInfo.getDecodeValue())) {
            res.append("\t->\t").append(ecidParameterInfo.getDecodeValue());
        }
        return res.toString();
    }

    private static String generateUidParameterRes(UidParameterInfo uidParameterInfo) {
        StringBuilder res = new StringBuilder();
        if (uidParameterInfo.getResult() != null) {
            res.append(uidParameterInfo.getResult());
        }
        if (StringUtils.isNotEmpty(uidParameterInfo.getDecodeValue())) {
            res.append("\t->\t").append(uidParameterInfo.getDecodeValue());
        }
        return res.toString();
    }

    private static CpLotIdInfo generateCpLotInfo(LotRelationSync lotRelationSync) {
        return new CpLotIdInfo(lotRelationSync.getCpLotId(), lotRelationSync.getCpWaferId(), lotRelationSync.getCpWaferIdOrg(), lotRelationSync.getCpWaferNo());
    }
}
