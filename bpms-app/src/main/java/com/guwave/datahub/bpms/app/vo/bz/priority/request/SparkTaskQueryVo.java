package com.guwave.datahub.bpms.app.vo.bz.priority.request;

import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = false)
@Data
@Schema(description = "Spark任务查询请求")
public class SparkTaskQueryVo extends PageableRequestVo {
    @Schema(description = "appName")
    private String appName;

    @Schema(description = "queue")
    private String queue;

    @Schema(description = "computeType")
    private String computeType;

    @Schema(description = "processStatus")
    private String processStatus;
}
