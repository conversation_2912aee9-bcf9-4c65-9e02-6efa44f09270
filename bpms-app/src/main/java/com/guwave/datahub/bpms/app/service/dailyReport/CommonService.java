package com.guwave.datahub.bpms.app.service.dailyReport;


import com.guwave.datahub.bpms.app.util.DateUtil;
import com.guwave.datahub.bpms.common.constant.DataCode;
import com.guwave.datahub.bpms.common.constant.ReportModule;
import com.guwave.datahub.bpms.dao.domain.dailyReport.DailyReportResult;
import com.guwave.datahub.bpms.dao.repository.bpms.DailyReportResultRepository;
import com.guwave.datahub.bpms.dao.repository.linkx.BzAppInstanceRepository;
import com.guwave.datahub.bpms.dao.repository.linkx.BzTaskInstanceRepository;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import static com.guwave.onedata.dataware.common.contant.Constant.SYSTEM;

public interface CommonService {

    default void saveDailyReportResult(String data, ReportModule module, DataCode code) {
        getDailyReportResultRepository().save(
                new DailyReportResult()
                        .setData(data)
                        .setReportModule(module)
                        .setDataCode(code)
                        .setDay(DateUtil.getDayBefortDays(1))
                        .setLatestFlag(1)
                        .setCreateUser(SYSTEM)
                        .setCreateTime(new Date())
                        .setUpdateUser(SYSTEM)
                        .setUpdateTime(new Date())
        );
    }


    DailyReportResultRepository  getDailyReportResultRepository();
}
