package com.guwave.datahub.bpms.app.service.bz;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.exception.BpmsParamException;
import com.guwave.datahub.bpms.app.exception.BpmsRpcInvokeException;
import com.guwave.datahub.bpms.app.listener.ExcelConsumer;
import com.guwave.datahub.bpms.app.listener.ExcelLineListener;
import com.guwave.datahub.bpms.app.util.ExcelUtil;
import com.guwave.datahub.bpms.app.vo.bz.quality.model.DataQualityRuleExcel;
import com.guwave.datahub.bpms.app.vo.bz.quality.request.DataQualityRuleSaveVo;
import com.guwave.datahub.bpms.app.vo.bz.quality.response.DataQualityRuleDetailVo;
import com.guwave.datahub.bpms.app.vo.bz.quality.response.DataQualityRuleResultVo;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.common.constant.ChartType;
import com.guwave.datahub.bpms.common.constant.Constant;
import com.guwave.datahub.bpms.dao.repository.dw.QualityDailyResultRepository;
import com.guwave.datahub.bpms.dao.repository.dw.QualityRealtimeResultRepository;
import com.guwave.datahub.bpms.dao.repository.dw.QualityRuleRepository;
import com.guwave.onedata.dataware.bridge.api.iface.IDataQualityRpcService;
import com.guwave.onedata.dataware.bridge.api.vo.request.QualityRuleVo;
import com.guwave.onedata.dataware.bridge.api.vo.response.QualityTryRunRes;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.QualityDailyResult;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.QualityRealtimeResult;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.QualityRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.EmptyFileException;
import org.apache.poi.UnsupportedFileFormatException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Slf4j
@Service
public class DataQualityService {

    private static final Pattern MYSQL_QUERY_SQL_INVALID_PATTERN = Pattern.compile("^\\s*select\\s*.*", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
    private static final Pattern CK_TEMP_TABLE_SQL_PATTERN = Pattern.compile("^\\s*create\\s*temporary\\s*table\\s*(?:if\\s*not\\s*exists\\s*)?(\\w*\\.?\\w*).*", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
    private static final Pattern CK_QUERY_SQL_INVALID_PATTERN = Pattern.compile("^\\s*select\\s*.*", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);


    @Autowired
    private QualityRuleRepository qualityRuleRepository;
    @Autowired
    private QualityRealtimeResultRepository qualityRealtimeResultRepository;
    @Autowired
    private QualityDailyResultRepository qualityDailyResultRepository;
    @DubboReference
    private IDataQualityRpcService dataQualityRpcService;

    // <showName,field>
    private static final Map<String, String> QUALITY_RULE_FIELD_MAPPING = new HashMap<String, String>() {{
        put("sqlType", "sqlType");
        put("executeTimeType", "executeTimeType");
        put("qualityName", "qualityName");
        put("comment", "comment");
        put("mysqlQuerySql", "mysqlQuerySql");
        put("ckTempTableSql", "ckTempTableSql");
        put("ckQuerySql", "ckQuerySql");
        put("status", "status");
    }};
    // <field,consumer>
    private static final Map<String, BiConsumer<String, QualityRuleVo>> QUALITY_RULE_FIELD_CONSUMER_MAP = new HashMap<String, BiConsumer<String, QualityRuleVo>>() {{
        put("sqlType", (str, qualityRule) -> qualityRule.setSqlType(getIntegerValue(str)));
        put("executeTimeType", (str, qualityRule) -> qualityRule.setExecuteTimeType(getIntegerValue(str)));
        put("qualityName", (str, qualityRule) -> qualityRule.setQualityName(str));
        put("comment", (str, qualityRule) -> qualityRule.setComment(str));
        put("mysqlQuerySql", (str, qualityRule) -> qualityRule.setMysqlQuerySql(str));
        put("ckTempTableSql", (str, qualityRule) -> qualityRule.setCkTempTableSql(str));
        put("ckQuerySql", (str, qualityRule) -> qualityRule.setCkQuerySql(str));
        put("status", (str, qualityRule) -> qualityRule.setStatus(getIntegerValue(str)));
    }};


    public PageableDataVo<DataQualityRuleDetailVo> queryRules(PageableRequestVo pageableRequestVo) {
        Page<QualityRule> dataQualityPage = qualityRuleRepository.findAllByDeleteFlagOrderByExecuteTimeTypeDescSqlTypeAscStatusDescQualityNameAsc(0, PageRequest.of(pageableRequestVo.getJpaPageIndex(), pageableRequestVo.getPageSize()));
        List<DataQualityRuleDetailVo> dataQualityRuleDetailVos = dataQualityPage.getContent().stream().map(t -> {
            DataQualityRuleDetailVo dataQualityRuleDetailVo = new DataQualityRuleDetailVo();
            BeanUtils.copyProperties(t, dataQualityRuleDetailVo);
            dataQualityRuleDetailVo.setMysqlQuerySql(null);
            dataQualityRuleDetailVo.setCkTempTableSql(null);
            dataQualityRuleDetailVo.setCkQuerySql(null);
            return dataQualityRuleDetailVo;
        }).collect(Collectors.toList());
        return PageableDataVo.of(new PageImpl<>(dataQualityRuleDetailVos, dataQualityPage.getPageable(), dataQualityPage.getTotalElements()));
    }

    public DataQualityRuleDetailVo queryDetail(Long id) {
        QualityRule qualityRule = qualityRuleRepository.findByIdAndDeleteFlag(id, 0);
        if (qualityRule == null) {
            throw new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT);
        }
        DataQualityRuleDetailVo dataQualityRuleDetailVo = new DataQualityRuleDetailVo();
        BeanUtils.copyProperties(qualityRule, dataQualityRuleDetailVo);
        if (dataQualityRuleDetailVo.getSqlType() == 1) {
            if (dataQualityRuleDetailVo.getExecuteTimeType() == 1) {
                dataQualityRuleDetailVo.setDayKeys(qualityDailyResultRepository.findDayKeys(dataQualityRuleDetailVo.getId(), Pageable.ofSize(30)));
            } else if (dataQualityRuleDetailVo.getExecuteTimeType() == 2) {
                dataQualityRuleDetailVo.setDayKeys(qualityRealtimeResultRepository.findDayKeys(dataQualityRuleDetailVo.getId(), Pageable.ofSize(30)));
            }
            if (CollectionUtils.isNotEmpty(dataQualityRuleDetailVo.getDayKeys())) {
                dataQualityRuleDetailVo.setDataQualityRuleResult(generateDataQualityRuleResult(qualityRule, dataQualityRuleDetailVo.getDayKeys().get(0)));
            }
        }
        return dataQualityRuleDetailVo;
    }

    private DataQualityRuleResultVo generateDataQualityRuleResult(QualityRule qualityRule, String dayKey) {
        if (qualityRule.getExecuteTimeType() == 1) {
            List<QualityDailyResult> qualityDailyResults = qualityDailyResultRepository.findByQualityRuleIdAndDayKey(qualityRule.getId(), dayKey);
            if (CollectionUtils.isEmpty(qualityDailyResults)) {
                qualityDailyResults.add(new QualityDailyResult().setResult(new JSONArray().toJSONString()));
            }
            return generateDataQualityRuleResult(qualityRule.getExecuteTimeType(), qualityDailyResults.get(0), null);
        } else if (qualityRule.getExecuteTimeType() == 2) {
            List<QualityRealtimeResult> qualityRealtimeResults = qualityRealtimeResultRepository.findByQualityRuleIdAndDayKeyOrderByTimeKey(qualityRule.getId(), dayKey);
            return generateDataQualityRuleResult(qualityRule.getExecuteTimeType(), null, qualityRealtimeResults);
        }
        return null;
    }

    private DataQualityRuleResultVo generateDataQualityRuleResult(Integer executeTimeType, QualityDailyResult qualityDailyResult, List<QualityRealtimeResult> qualityRealtimeResults) {
        DataQualityRuleResultVo dataQualityRuleResultVo = new DataQualityRuleResultVo();
        if (executeTimeType == 1) {
            dataQualityRuleResultVo.setChartType(ChartType.TABLE);
            dataQualityRuleResultVo.setTableResult(dailyResultToTable(qualityDailyResult));
        } else if (executeTimeType == 2) {
            dataQualityRuleResultVo.setChartType(ChartType.LINE);
            dataQualityRuleResultVo.setLineResult(realtimeResultToLine(qualityRealtimeResults));
        }
        return dataQualityRuleResultVo;
    }

    public void deleteRule(Long id) {
        log.info("删除quaity rule : {}", id);
        dataQualityRpcService.deleteAll(Lists.newArrayList(id), RequestContext.getUserName());
    }

    public void switchRuleStatus(Long id, Integer status) {
        log.info("切换quaity rule状态 : {}   status : {}", id, status);
        dataQualityRpcService.switchRuleStatus(id, status, RequestContext.getUserName());
    }

    public void saveRule(DataQualityRuleSaveVo dataQualityRuleSaveVo) {
        log.info("保存quality rule : {}", JSON.toJSONString(dataQualityRuleSaveVo));
        List<QualityRule> sameQualityNameRule = qualityRuleRepository.findByQualityNameInAndDeleteFlag(Lists.newArrayList(dataQualityRuleSaveVo.getQualityName()), 0);
        String errorMsg = validateQualityRuleSaveVo(dataQualityRuleSaveVo, sameQualityNameRule);
        if (StringUtils.isNotBlank(errorMsg)) {
            throw new BpmsParamException(errorMsg);
        }
        Date date = new Date();
        QualityRuleVo qualityRuleVo = new QualityRuleVo()
                .setDeleteFlag(0)
                .setCreateTime(date)
                .setCreateUser(RequestContext.getUserName())
                .setUpdateTime(date)
                .setUpdateUser(RequestContext.getUserName());
        if (dataQualityRuleSaveVo.getId() != null) {
            QualityRule existsQualityRule = qualityRuleRepository.findByIdAndDeleteFlag(dataQualityRuleSaveVo.getId(), 0);
            if (existsQualityRule == null) {
                throw new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT);
            }
            qualityRuleVo.setCreateUser(existsQualityRule.getCreateUser()).setCreateTime(existsQualityRule.getCreateTime());
        }
        BeanUtils.copyProperties(dataQualityRuleSaveVo, qualityRuleVo);
        dataQualityRpcService.saveAll(Lists.newArrayList(qualityRuleVo));
    }

    public DataQualityRuleResultVo tryRun(DataQualityRuleSaveVo dataQualityRuleSaveVo) {
        log.info("试运行quality rule : {}", JSON.toJSONString(dataQualityRuleSaveVo));
//        if (!Objects.equals(dataQualityRuleSaveVo.getStatus(), 1)) {
//            throw new BpmsParamException("试运行请将状态置为开启！");
//        }
        List<QualityRule> sameQualityNameRule = qualityRuleRepository.findByQualityNameInAndDeleteFlag(Lists.newArrayList(dataQualityRuleSaveVo.getQualityName()), 0);
        String errorMsg = validateQualityRuleSaveVo(dataQualityRuleSaveVo, sameQualityNameRule);
        if (StringUtils.isNotBlank(errorMsg)) {
            throw new BpmsParamException(errorMsg);
        }
        QualityRuleVo qualityRuleVo = new QualityRuleVo();
        BeanUtils.copyProperties(dataQualityRuleSaveVo, qualityRuleVo);
        QualityTryRunRes qualityTryRunRes = dataQualityRpcService.tryRun(qualityRuleVo);
        if (qualityTryRunRes.isHasErr()) {
            if (qualityTryRunRes.isShowErrMsg()) {
                throw new BpmsException(ResponseCode.Business.DATA_QUALITY_TRY_RUN_ERROR, qualityTryRunRes.getErrMsg());
            } else {
                throw new BpmsRpcInvokeException(new RuntimeException(qualityTryRunRes.getErrMsg()));
            }
        }
        String res = qualityTryRunRes.getResult();
        if (dataQualityRuleSaveVo.getExecuteTimeType() == 1) {
            return generateDataQualityRuleResult(dataQualityRuleSaveVo.getExecuteTimeType(), new QualityDailyResult().setResult(res), null);
        } else if (dataQualityRuleSaveVo.getExecuteTimeType() == 2) {
            return generateDataQualityRuleResult(dataQualityRuleSaveVo.getExecuteTimeType(), null, Lists.newArrayList(new QualityRealtimeResult().setTimeKey(DateFormatUtils.format(new Date(), "HHmmss")).setResult(res)));
        }
        return null;
    }

    public DataQualityRuleResultVo qyeryRuleResult(Long id, String dayKey) {
        QualityRule qualityRule = qualityRuleRepository.findByIdAndDeleteFlag(id, 0);
        if (qualityRule == null) {
            throw new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT);
        }
        return generateDataQualityRuleResult(qualityRule, dayKey);
    }

    public void importRule(MultipartFile file) throws IOException {
        log.info("导入 quality rule");
        if (file.getSize() > 10L * 1024L * 1024L) {
            throw new BpmsException(ResponseCode.Common.FILE_SIZE_EXCEED_LIMIT, "10MB");
        }
        ExcelReader excel = null;
        try {
            excel = EasyExcel.read(file.getInputStream(), new ExcelLineListener(QUALITY_RULE_FIELD_MAPPING, new ExcelConsumer<Map<String, List<Pair<String, String>>>>() {
                        private final Map<String, Integer> qualityNameMap = new HashMap<>();
                        private final List<QualityRuleVo> qualityRules = new ArrayList<>();
                        private final StringBuilder errorMsg = new StringBuilder();
                        private int dataLineNum = 0;

                        @Override
                        public void dealLine(Long num, Map<String, List<Pair<String, String>>> fieldWithContentListMap) {
                            dataLineNum++;
                            Date date = new Date();
                            QualityRuleVo qualityRuleVo = new QualityRuleVo()
                                    .setDeleteFlag(0)
                                    .setCreateTime(date)
                                    .setUpdateTime(date)
                                    .setCreateUser(RequestContext.getUserName())
                                    .setUpdateUser(RequestContext.getUserName());
                            fieldWithContentListMap.forEach((field, contentList) -> {
                                BiConsumer<String, QualityRuleVo> qualityRuleConsumer = QUALITY_RULE_FIELD_CONSUMER_MAP.get(field);
                                if (qualityRuleConsumer != null) {
                                    qualityRuleConsumer.accept(contentList.get(0).getValue(), qualityRuleVo);
                                }
                            });
                            String err = validateQualityRuleSaveVo(qualityRuleVo, new ArrayList<>());
                            if (StringUtils.isNotBlank(err)) {
                                errorMsg.append("第 ").append(dataLineNum + 1).append(" 行数据未导入成功: ").append(err);
                            } else if (qualityNameMap.containsKey(qualityRuleVo.getQualityName())) {
                                errorMsg.append("第 ").append(dataLineNum + 1).append(" 行数据已忽略: ").append("与第 ").append(qualityNameMap.get(qualityRuleVo.getQualityName()) + 1).append(" 行指标名称相同！\n");
                            } else {
                                qualityNameMap.put(qualityRuleVo.getQualityName(), dataLineNum);
                                qualityRules.add(qualityRuleVo);
                                if (qualityRules.size() >= 1000) {
                                    this.saveData();
                                }
                            }
                        }

                        @Override
                        public void after() {
                            saveData();
                            log.info("总共 {} 条数据，成功导入 {} 条！", dataLineNum, qualityNameMap.size());
                            String err = errorMsg.toString();
                            if (StringUtils.isNotBlank(err)) {
                                throw new BpmsParamException(err);
                            }
                        }

                        private void saveData() {
                            if (qualityRules.size() > 0) {
                                Map<String, QualityRule> existsQualityRuleMap = qualityRuleRepository.findByQualityNameInAndDeleteFlag(qualityRules.stream().map(QualityRuleVo::getQualityName).collect(Collectors.toList()), 0)
                                        .stream().collect(Collectors.toMap(QualityRule::getQualityName, t -> t, (v1, v2) -> v1));
                                qualityRules.forEach(qualityRuleVo -> {
                                    QualityRule existsQualityRule = existsQualityRuleMap.get(qualityRuleVo.getQualityName());
                                    if (existsQualityRule != null) {
                                        qualityRuleVo.setId(existsQualityRule.getId()).setCreateUser(existsQualityRule.getCreateUser()).setCreateTime(existsQualityRule.getCreateTime());
                                    }
                                });
                                dataQualityRpcService.saveAll(qualityRules);
                                qualityRules.clear();
                            }
                        }
                    }))
                    .headRowNumber(1)
                    .excelType(ExcelTypeEnum.XLSX)
                    .build();
            excel.read(new ReadSheet(0));
        } catch (Exception e) {
            log.info("导入规则异常：", e);
            if (e instanceof BpmsException) {
                throw e;
            } else if (e instanceof EmptyFileException || e instanceof UnsupportedFileFormatException || e instanceof ExcelAnalysisException) {
                throw new BpmsException(ResponseCode.Common.FILE_NOT_READ_NEED_FIELD);
            }
            throw new BpmsException(ResponseCode.Common.FILE_READ_ERROR, "未知异常");
        } finally {
            if (excel != null) {
                excel.finish();
            }
        }
        log.info("导入 quality rule 完成");
    }

    public void exportRule(HttpServletResponse response) throws IOException {
        log.info("导出 quality rule");
        String fileName = "数据质量规则.xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            List<DataQualityRuleExcel> dataQualityRuleExcels = qualityRuleRepository.findAllByDeleteFlagOrderByExecuteTimeTypeDescSqlTypeAscQualityNameAsc(0).stream().map(t -> {
                DataQualityRuleExcel dataQualityRuleExcel = new DataQualityRuleExcel();
                BeanUtils.copyProperties(t, dataQualityRuleExcel);
                return dataQualityRuleExcel;
            }).collect(Collectors.toList());
            ExcelUtil.exportDataWithDefaultStyle(outputStream, dataQualityRuleExcels, DataQualityRuleExcel.class, new ArrayList<>());
        }
        response.setHeader("Content-Disposition", String.format("attachment;filename=%s", new String(fileName.getBytes("gb2312"))));
        log.info("导出 quality rule 完成");
    }

    private DataQualityRuleResultVo.TableResult dailyResultToTable(QualityDailyResult qualityDailyResult) {
        JSONArray jsonArray = JSON.parseArray(qualityDailyResult.getResult());
        DataQualityRuleResultVo.TableResult tableResult = new DataQualityRuleResultVo.TableResult();
        tableResult.setTitles(new ArrayList<>());
        tableResult.setRows(new ArrayList<>());
        HashSet<String> titleSet = new HashSet<>();
        jsonArray.forEach(item -> {
            JSONObject jsonObject = (JSONObject) item;
            jsonObject.forEach((k, v) -> {
                if (titleSet.add(k)) {
                    tableResult.getTitles().add(new DataQualityRuleResultVo.TableResult.TableCell(k, k));
                    tableResult.getRows().forEach(r -> r.add(new DataQualityRuleResultVo.TableResult.TableCell(k, null)));
                }
            });
            ArrayList<DataQualityRuleResultVo.TableResult.TableCell> row = new ArrayList<>();
            tableResult.getTitles().forEach(title -> {
                row.add(new DataQualityRuleResultVo.TableResult.TableCell(title.getKey(), jsonObject.getString(title.getKey())));
            });
            tableResult.getRows().add(row);
        });
        return tableResult;
    }

    private DataQualityRuleResultVo.LineResult realtimeResultToLine(List<QualityRealtimeResult> qualityRealtimeResults) {
        DataQualityRuleResultVo.LineResult lineResult = new DataQualityRuleResultVo.LineResult();
        lineResult.setXAxis(new DataQualityRuleResultVo.LineResult.Axis("time", qualityRealtimeResults.stream().map(QualityRealtimeResult::getTimeKey).collect(Collectors.toList())));
        List<JSONArray> jsonArrays = qualityRealtimeResults.stream().map(realtimeResult -> JSON.parseArray(realtimeResult.getResult())).collect(Collectors.toList());
        HashMap<String, DataQualityRuleResultVo.LineResult.Axis> yAxisMap = new HashMap<>();
        for (int i = 0; i < jsonArrays.size(); i++) {
            JSONArray jsonArray = jsonArrays.get(i);
            if (!jsonArray.isEmpty()) {
                JSONObject jsonObject = (JSONObject) jsonArray.get(0);
                int finalI = i;
                jsonObject.forEach((k, v) -> yAxisMap.compute(k, (key, oldValue) -> {
                    if (oldValue == null) {
                        oldValue = new DataQualityRuleResultVo.LineResult.Axis(key, new ArrayList<>());
                        for (int j = 0; j < finalI; j++) {
                            oldValue.getValues().add(Constant.EMPTY);
                        }
                    }
                    oldValue.getValues().add(defaultString(v));
                    return oldValue;
                }));
                yAxisMap.forEach((k, v) -> {
                    for (int j = v.getValues().size(); j <= finalI; j++) {
                        v.getValues().add(Constant.EMPTY);
                    }
                });
            }
        }
        lineResult.setYAxisList(new ArrayList<>(yAxisMap.values()));
        return lineResult;
    }

    private String validateQualityRuleSaveVo(DataQualityRuleSaveVo dataQualityRuleSaveVo, List<QualityRule> existsQualityRules) {
        QualityRuleVo qualityRuleVo = new QualityRuleVo();
        BeanUtils.copyProperties(dataQualityRuleSaveVo, qualityRuleVo);
        String err = validateQualityRuleSaveVo(qualityRuleVo, existsQualityRules);
        BeanUtils.copyProperties(qualityRuleVo, dataQualityRuleSaveVo);
        return err;
    }

    private String validateQualityRuleSaveVo(QualityRuleVo qualityRuleVo, List<QualityRule> existsQualityRules) {
        StringBuilder errorMsg = new StringBuilder();
        HashSet<Integer> sqlTypeSet = Sets.newHashSet(0, 1);
        HashSet<Integer> executeTimeTypeSet = Sets.newHashSet(1, 2);
        HashSet<Integer> statusSet = Sets.newHashSet(0, 1);
        if (!sqlTypeSet.contains(qualityRuleVo.getSqlType())) {
            errorMsg.append("sqlType(指标类型) 非法！\n");
        }
        if (!executeTimeTypeSet.contains(qualityRuleVo.getExecuteTimeType())) {
            errorMsg.append("executeTimeType(执行间隔) 非法！\n");
        }
        if (StringUtils.isBlank(qualityRuleVo.getQualityName())) {
            errorMsg.append("qualityName(指标名称) 不能为空！\n");
        }
        if (StringUtils.isBlank(qualityRuleVo.getComment())) {
            errorMsg.append("comment(备注) 不能为空！\n");
        }
        if (!statusSet.contains(qualityRuleVo.getStatus())) {
            errorMsg.append("status(状态) 非法！\n");
        }
        if (Objects.equals(qualityRuleVo.getSqlType(), 0)) {
            qualityRuleVo.setCkQuerySql(Constant.EMPTY);
            if (StringUtils.isBlank(qualityRuleVo.getMysqlQuerySql())) {
                errorMsg.append("mysqlQuerySql(mysql查询sql) 不能为空！\n");
            } else if (!MYSQL_QUERY_SQL_INVALID_PATTERN.matcher(qualityRuleVo.getMysqlQuerySql()).matches()) {
                errorMsg.append("mysqlQuerySql(mysql查询sql) 不合法！\n");
            }
            if (StringUtils.isBlank(qualityRuleVo.getCkTempTableSql())) {
                errorMsg.append("ckTempTableSql(创建ck临时表的sql) 不能为空！\n");
            } else if (!CK_TEMP_TABLE_SQL_PATTERN.matcher(qualityRuleVo.getCkTempTableSql()).matches()) {
                errorMsg.append("ckTempTableSql(创建ck临时表的sql) 不合法！\n");
            }
        } else if (Objects.equals(qualityRuleVo.getSqlType(), 1)) {
            qualityRuleVo.setCkTempTableSql(Constant.EMPTY);
            if (StringUtils.isBlank(qualityRuleVo.getMysqlQuerySql()) && StringUtils.isBlank(qualityRuleVo.getCkQuerySql())) {
                errorMsg.append("mysqlQuerySql(mysql查询sql) 和 ckQuerySql(ck查询sql) 不能同时为空！\n");
            }
            if (StringUtils.isNotBlank(qualityRuleVo.getMysqlQuerySql())) {
                qualityRuleVo.setCkQuerySql(Constant.EMPTY);
                if (!MYSQL_QUERY_SQL_INVALID_PATTERN.matcher(qualityRuleVo.getMysqlQuerySql()).matches()) {
                    errorMsg.append("mysqlQuerySql(mysql查询sql) 不合法！\n");
                }
            } else if (StringUtils.isNotBlank(qualityRuleVo.getCkQuerySql())) {
                qualityRuleVo.setMysqlQuerySql(Constant.EMPTY);
                if (!CK_QUERY_SQL_INVALID_PATTERN.matcher(qualityRuleVo.getCkQuerySql()).matches()) {
                    errorMsg.append("ckQuerySql(ck查询sql) 不合法！\n");
                }
            }
        }
        if (existsQualityRules.stream().anyMatch(t -> !Objects.equals(t.getId(), qualityRuleVo.getId()) && Objects.equals(t.getQualityName(), qualityRuleVo.getQualityName()))) {
            errorMsg.append("qualityName(指标名称) 不能重复！\n");
        }
        return errorMsg.toString();
    }

    private String defaultString(Object obj) {
        if (obj == null) {
            return Constant.EMPTY;
        }
        return obj.toString();
    }

    private static Integer getIntegerValue(String str) {
        return str.isEmpty() ? null : Double.valueOf(str).intValue();
    }
}
