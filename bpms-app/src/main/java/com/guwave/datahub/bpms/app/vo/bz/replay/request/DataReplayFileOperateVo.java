package com.guwave.datahub.bpms.app.vo.bz.replay.request;

import com.guwave.onedata.dataware.common.contant.FileCategory;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;


@Schema(title = "重播批量操作请求体")
@Data
public class DataReplayFileOperateVo {
    @Schema(title = "fileWarehousingRecordIdList", description = "File入库记录Id列表", example = "[\"1,2\"]")
    private List<Long> fileWarehousingRecordIdList;

    @Schema(title = "fileName", description = "文件名", example = "CP1_RT_2022-10-10.stdf")
    private String fileName;

    @Schema(title = "remoteFilePath", description = "远程服务器的原始文件路径", example = "/onedata-saas/ETA/FT/YT_TESTING/raw_data/ETA7085S2F_21442980_A0172211T01022_FT.csv")
    private String remoteFilePath;

    @Schema(title = "testArea", description = "CP/FT/WAT等 表示测试阶段大类型", example = "CP")
    private String testArea;

    @Schema(title = "工厂列表", description = "测试工厂", example = "[\"VC\"]")
    private List<String> factoryList;

    @Schema(title = "fileCategory", description = "只能单选，取值为：STDF,RAW_DATA,WAT", example = "STDF")
    private FileCategory fileCategory;

    @Schema(title = "stepList", example = "[\"5100\"]")
    private List<Integer> stepList;

    @Schema(title = "statusList", description = "文件入库状态", example = "[\"SUCCESS\"]")
    private List<String> processStatusList;

    @Schema(title = "异常信息", example = "RAW_DATA文件转换异常")
    private String exceptionMessage;

    @Schema(title = "createTime", description = "createTime", example = "[\"2023-09-20 08:14:13\", \"2023-09-21 08:14:13\"]")
    private List<String> createTime;

    @Schema(title = "updateTime", description = "updateTime", example = "[\"2023-09-20 08:14:13\", \"2023-09-21 08:14:13\"]")
    private List<String> updateTime;

    @Schema(title = "replayType", description = "重播类型,DELETE_FILE:单条删除，REPROCESS_FILE：单条重跑，DELETE_LOT：批量删除，REPROCESS_LOT：批量重跑", example = "REPROCESS_LOT")
    @NotNull(message = "重播类型")
    private String replayType;

    @Schema(title = "replayStep", description = "重播步骤:2200、3100、4100、5100", example = "3100")
    @NotNull(message = "重播步骤")
    private Integer replayStep;

    @Schema(title = "replayComment", description = "重播备注", example = "关键字段缺失，从预解析重跑")
    @NotNull(message = "重播备注")
    private String replayComments;

    @Schema(title = "replayFileCnt", description = "重播的文件数", example = "10")
    @NotNull(message = "重播的文件数")
    private Integer replayFileCnt;

}
