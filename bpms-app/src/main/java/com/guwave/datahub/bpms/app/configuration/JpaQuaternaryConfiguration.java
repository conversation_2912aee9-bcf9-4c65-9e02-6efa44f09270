package com.guwave.datahub.bpms.app.configuration;

import org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateSettings;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.sql.DataSource;
import java.util.Map;
import java.util.Objects;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * JpaQuaternaryConfiguration
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2024-02-26 10:53:48
 */
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(entityManagerFactoryRef = "entityManagerFactoryQuaternary",
        transactionManagerRef = "transactionManagerQuaternary",
        basePackages = {"com.guwave.datahub.bpms.dao.repository.compute"})
public class JpaQuaternaryConfiguration {

    @Resource
    private JpaProperties jpaProperties;

    @Resource
    private HibernateProperties hibernateProperties;

    @Bean(name = "quaternaryDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.quaternary")
    public DataSource quaternaryDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "entityManagerQuaternary")
    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
        return Objects.requireNonNull(entityManagerFactoryQuaternary(builder).getObject()).createEntityManager();
    }

    @Bean(name = "entityManagerFactoryQuaternary")
    public LocalContainerEntityManagerFactoryBean entityManagerFactoryQuaternary(EntityManagerFactoryBuilder builder) {
        Map<String, Object> properties =
                hibernateProperties.determineHibernateProperties(
                        jpaProperties.getProperties(),
                        new HibernateSettings());

        return builder
                .dataSource(quaternaryDataSource())
                .properties(properties)
                .packages("com.guwave.onedata.next.compute.dao.mysql.domain")
                .persistenceUnit("quaternaryPersistenceUnit")
                .build();
    }

    @Bean(name = "transactionManagerQuaternary")
    PlatformTransactionManager transactionManagerQuaternary(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(Objects.requireNonNull(entityManagerFactoryQuaternary(builder).getObject()));
    }
}
