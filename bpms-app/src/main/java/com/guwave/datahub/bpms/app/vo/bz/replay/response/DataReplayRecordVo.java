package com.guwave.datahub.bpms.app.vo.bz.replay.response;

import cn.hutool.core.date.DateUtil;
import com.guwave.datahub.bpms.dao.domain.replay.DataReplayRecord;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.Arrays;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_FORMAT;

@Schema(title = "数据重播记录")
@Data
@Builder
public class DataReplayRecordVo {
    @Schema(title = "id")
    private Long id;

    @Schema(title = "testArea", description = "CP/FT/WAT等 表示测试阶段大类型", example = "CP,FT")
    private String testArea;

    @Schema(title = "工厂", description = "测试工厂", example = "VC")
    private String factory;

    @Schema(title = "deviceId", example = "SS521BB-DEFAULT")
    private String deviceId;

    @Schema(title = "fileCategory", description = "STDF,RAW_DATA,WAT", example = "STDF")
    private String fileCategory;

    @Schema(title = "lotId", example = "NT41A")
    private String lotId;

    @Schema(title = "waferNo", example = "8")
    private String waferNo;

    @Schema(title = "sblotId", example = "8")
    private String sblotId;

    @Schema(title = "lotType", description = "PRODUCTION,ENGINEERING", example = "PRODUCTION")
    private String lotType;

    @Schema(title = "testStage", example = "CP1")
    private String testStage;

    @Schema(title = "testProgram", example = "PROGRAM1")
    private String testProgram;

    @Schema(title = "fileName", description = "文件名", example = "CP1_RT_2022-10-10.stdf")
    private String fileName;

    @Schema(title = "replayType", description = "重播类型", example = "REPROCESS")
    private String replayType;

    @Schema(title = "step", description = "重播步骤", example = "3100")
    private Integer step;

    @Schema(title = "filterProcessStatus", example = "FAIL")
    private String filterProcessStatus;

    @Schema(title = "filterExceptionMessage", example = "关键字段缺失")
    private String filterExceptionMessage;

    @Schema(title = "filterCreateTime", example = "2023-09-19 08:14:13,2023-09-20 08:14:13")
    private String filterCreateTime;

    @Schema(title = "filterUpdateTime", example = "2023-09-19 08:14:13,2023-09-20 08:14:13")
    private String filterUpdateTime;

    @Schema(title = "fileWarehousingRecordId", example = "1,2")
    private String fileWarehousingRecordId;

    @Schema(title = "failTaskCnt", description = "处理失败任务数", example = "10")
    private Integer failTaskCnt;

    @Schema(title = "successTaskCnt", description = "处理成功任务数", example = "40")
    private Integer successTaskCnt;

    @Schema(title = "totalTaskCnt", description = "重播任务总数", example = "100")
    private Integer totalTaskCnt;

    @Schema(title = "status", description = "重播状态", example = "PROCESSING")
    private String status;

    @Schema(title = "createTime", description = "重播开始时间", example = "2023-09-19 08:14:13")
    private String createTime;

    @Schema(title = "updateTime", description = "重播更新时间", example = "2023-09-20 08:14:13")
    private String updateTime;

    @Schema(title = "replayComments", description = "注释", example = "关键字段缺失，数据重跑")
    private String replayComments;

    public static DataReplayRecordVo of(DataReplayRecord data) {
        return DataReplayRecordVo.builder()
                .id(data.getId())
                .testArea(TestArea.of(data.getTestArea()))
                .factory(data.getFactory() == null ? null : String.join(Constant.COMMA, data.getFactory()))
                .deviceId(data.getDeviceId() == null ? null : String.join(Constant.COMMA, data.getDeviceId()))
                .fileCategory(FileCategory.of(data.getFileCategory()))
                .lotId(data.getLotIdList() == null ? null : String.join(Constant.COMMA, data.getLotIdList()))
                .waferNo(data.getWaferNoList() == null ? null : String.join(Constant.COMMA, data.getWaferNoList()))
                .sblotId(data.getSblotIdList() == null ? null : String.join(Constant.COMMA, data.getSblotIdList()))
                .lotType(LotType.of(data.getLotType()))
                .testStage(data.getTestStageList() == null ? null : String.join(Constant.COMMA, data.getTestStageList()))
                .testProgram(data.getTestProgramList() == null ? null : String.join(Constant.COMMA, data.getTestProgramList()))
                .fileName(data.getFileName())
                .replayType(data.getReplayType().getType())
                .step(data.getStep().getStep())
                .filterProcessStatus(data.getFilterProcessStatusList() == null ? null : String.join(Constant.COMMA, data.getFilterProcessStatusList()))
                .filterExceptionMessage(data.getFilterExceptionMessage())
                .filterCreateTime(Arrays.asList(data.getStartCreateTime(), data.getEndCreateTime()).contains(null) ? null : data.getStartCreateTime() + Constant.COMMA + data.getEndCreateTime())
                .filterUpdateTime(Arrays.asList(data.getStartUpdateTime(), data.getEndUpdateTime()).contains(null) ? null : data.getStartUpdateTime() + Constant.COMMA + data.getEndUpdateTime())
                .fileWarehousingRecordId(data.getFileWarehousingRecordId())
                .failTaskCnt(data.getFailTaskCnt())
                .successTaskCnt(data.getSuccessTaskCnt())
                .totalTaskCnt(data.getTotalTaskCnt())
                .status(data.getProcessStatus().getStatus())
                .createTime(DateUtil.format(data.getCreateTime(), NORM_DATETIME_FORMAT))
                .updateTime(DateUtil.format(data.getUpdateTime(), NORM_DATETIME_FORMAT))
                .replayComments(data.getReplayComments())
                .build();
    }

}
