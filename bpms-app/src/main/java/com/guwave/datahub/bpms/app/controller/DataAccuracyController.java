package com.guwave.datahub.bpms.app.controller;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.DataPermission;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.service.bz.DataAccuracyService;
import com.guwave.datahub.bpms.app.service.bz.DataRepairService;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.DataAccuracyBatchMatchQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.DataAccuracyDetailQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.DataAccuracyDropDownFilterVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.DataAccuracyExportDataQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.DataAccuracyQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.DownloadGoodRetestDieReqVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.DownloadOnlineRetestDieReqVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.RepairEffectBatchQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.RepairFileQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.RepairInfoSaveVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response.DataAccuracyBatchMatchDetailVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response.DataAccuracyBatchYieldDetailVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response.DataAccuracyDetailResVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response.DataAccuracyDropDownVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response.FileRepairRecordVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response.RepairEffectBatchFileVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response.RepairFieldInfoResVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response.RepairPreviewResultVo;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 2025/4/24 18:01
 * DataAccuracyController: 数据准确性
 *
 * <AUTHOR>
 */
@Log
@Auth
//@License
@Validated
@Slf4j
@RestController
@AllArgsConstructor
@Tag(name = "DataAccuracyControllerAPI", description = "数据准确性接口",
        externalDocs = @ExternalDocumentation(description = "需求文档",
                url = "")
)
@RequestMapping("dataAccuracy")
public class DataAccuracyController {

    @Autowired
    private DataAccuracyService dataAccuracyService;
    @Autowired
    private DataRepairService dataRepairService;

    @Operation(summary = "下拉框查询")
    @PostMapping("filter")
    @DataPermission
    public ResponseVo<List<DataAccuracyDropDownVo>> dataAccuracyFilter(@RequestBody @Validated DataAccuracyDropDownFilterVo dataAccuracyDropDownFilterVo) {
        return ResponseVo.ok(dataAccuracyService.dataAccuracyFilter(dataAccuracyDropDownFilterVo));
    }

    @Operation(summary = "准确性指标查询接口")
    @PostMapping("accuracyBase")
    @DataPermission
    public ResponseVo<DataAccuracyBatchMatchDetailVo> accuracyBase(@RequestBody @Validated DataAccuracyBatchMatchQueryVo dataAccuracyBatchMatchQueryVo) {
        return ResponseVo.ok(dataAccuracyService.accuracyBase(dataAccuracyBatchMatchQueryVo));
    }

    @Operation(summary = "数据指标准确性列表接口")
    @PostMapping("accuracy")
    @DataPermission
    public ResponseVo<PageableDataVo<DataAccuracyBatchYieldDetailVo>> accuracy(@RequestBody @Validated DataAccuracyQueryVo dataAccuracyQueryVo) {
        return ResponseVo.ok(dataAccuracyService.accuracy(dataAccuracyQueryVo));
    }

    @Operation(summary = "数据准确性详情接口")
    @PostMapping("detail")
    @DataPermission
    public ResponseVo<DataAccuracyDetailResVo> findAccuracyFileDetail(@RequestBody @Valid DataAccuracyDetailQueryVo accuracyDetailQueryVO) {
        return ResponseVo.ok(dataAccuracyService.findAccuracyFileDetail(accuracyDetailQueryVO));
    }

    @Operation(summary = "数据指标准确性导出接口")
    @PostMapping("export")
    @DataPermission
    public ResponseVo<Void> exportData(@RequestBody @Valid DataAccuracyExportDataQueryVo dataAccuracyExportDataQueryVo, HttpServletResponse response) {
        dataAccuracyService.exportCsv(dataAccuracyExportDataQueryVo, response);
        return ResponseVo.ok();
    }

    @Operation(summary = "好品复测die下载接口")
    @PostMapping("downloadGoodRetest")
    @DataPermission
    public ResponseVo<Void> downloadGoodRetestBins(@RequestBody @Valid DownloadGoodRetestDieReqVo downloadGoodRetestDieReqVo, HttpServletResponse response) {
        dataAccuracyService.downloadGoodRetestBins(downloadGoodRetestDieReqVo, response);
        return ResponseVo.ok();
    }

    @Operation(summary = "onlineRetest die下载接口")
    @PostMapping("downloadOnlineRetest")
    @DataPermission
    public ResponseVo<Void> downloadOnlineRetest(@RequestBody @Valid DownloadOnlineRetestDieReqVo downloadOnlineRetestBinReqVo, HttpServletResponse response) {
        dataAccuracyService.downloadOnlineRetest(downloadOnlineRetestBinReqVo, response);
        return ResponseVo.ok();
    }

    @Operation(summary = "查询数据修复的文件列表")
    @PostMapping("getRepairFileRecord")
    @DataPermission
    public ResponseVo<List<FileRepairRecordVo>> getRepairFileRecord(@RequestBody RepairFileQueryVo fileRepairRecordQueryVo) {
        return ResponseVo.ok(dataRepairService.getRepairFileRecord(fileRepairRecordQueryVo));
    }

    @Operation(summary = "可修复字段和文件中的原始值")
    @PostMapping("getRepairFieldInfo")
    @DataPermission
    public ResponseVo<List<RepairFieldInfoResVo>> getRepairFieldInfo(@RequestBody RepairFileQueryVo fileRepairRecordQueryVo) {
        return ResponseVo.ok(dataRepairService.getRepairFieldInfo(fileRepairRecordQueryVo));
    }

    @Operation(summary = "受影响批次的所有文件列表接口")
    @PostMapping("getRepairEffectBatchFileList")
    @DataPermission
    public ResponseVo<List<RepairEffectBatchFileVo>> getRepairEffectBatchFileList(@RequestBody RepairEffectBatchQueryVo repairEffectBatchQueryVo) {
        return ResponseVo.ok(dataRepairService.getRepairEffectBatchFileList(repairEffectBatchQueryVo));
    }

    @Operation(summary = "数据修复预览")
    @PostMapping("repairPreview")
    @DataPermission
    public ResponseVo<List<RepairPreviewResultVo>> repairPreview(@RequestBody RepairInfoSaveVo repairInfoSaveVo) {
        return ResponseVo.ok(dataRepairService.repairPreview(repairInfoSaveVo));

    }

    @Operation(summary = "触发修复")
    @PostMapping("triggerRepair")
    @DataPermission
    public ResponseVo<Void> triggerRepair(@RequestBody RepairInfoSaveVo repairInfoSaveVo) {
        dataRepairService.triggerRepair(repairInfoSaveVo);
        return ResponseVo.ok();
    }

}
