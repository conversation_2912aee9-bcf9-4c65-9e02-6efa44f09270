package com.guwave.datahub.bpms.app.controller;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.service.bz.LogsService;
import com.guwave.datahub.bpms.app.vo.web.LogsQueryRequestVo;
import com.guwave.datahub.bpms.app.vo.web.LogsQueryResponseVo;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.util.List;

@Log(response = false)
@Auth
// @License
@Validated
@Slf4j
@RestController
@AllArgsConstructor
@Tag(name = "LogsControllerAPI", description = "日志可视化页面接口", externalDocs = @ExternalDocumentation(description = "需求文档", url = ""))
@RequestMapping("logs")
public class LogController {

    private final LogsService logsService;

    @Operation(summary = "日志查询", description = "根据条件查询日志", responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponseVo.class)))
    })
    @GetMapping("queryLogs")
    public ResponseVo<LogsQueryResponseVo> queryLogs(
            @RequestParam("startTime") @Schema(description = "开始时间", example = "2025-08-15 10:00:00") @NotBlank String startTime,
            @RequestParam("endTime") @Schema(description = "结束时间", example = "2025-08-15 11:00:00") @NotBlank String endTime,
            @RequestParam("app") @Schema(description = "应用名称", example = "dataware-collectx") @NotBlank String app,
            @RequestParam(value = "host", required = false) @Schema(description = "主机名称", example = "app01") String host,
            @RequestParam(value = "instance", required = false) @Schema(description = "实例名称", example = "dataware-collectx#01") String instance,
            @RequestParam(value = "logLevel", required = false) @Schema(description = "日志级别", example = "[INFO,ERROR]") List<String> logLevel,
            @RequestParam("queryType") @Schema(description = "查询类型", example = "regexp/keyword") @NotBlank String queryType,
            @RequestParam(value = "queryValue", required = false) @Schema(description = "查询值", example = "error.*") String queryValue) {

        //log.info("进入queryLogs方法");
        //log.debug("请求参数：startTime={}, endTime={}, app={}, host={}, instance={}, logLevel={}, queryType={}, queryValue={}",
        //        startTime, endTime, app, host, instance, logLevel, queryType, queryValue);

        LogsQueryRequestVo requestVo = new LogsQueryRequestVo().build(app, startTime, endTime, host, instance, logLevel, queryType, queryValue);

        return logsService.queryLogs(requestVo);
    }

    @Operation(summary = "日志下载", description = "根据条件下载日志")
    @GetMapping("downloadLogs")
    public void downloadLogs(
            @RequestParam("startTime") @Schema(description = "开始时间", example = "2025-08-15 10:00:00") @NotBlank String startTime,
            @RequestParam("endTime") @Schema(description = "结束时间", example = "2025-08-15 11:00:00") @NotBlank String endTime,
            @RequestParam("app") @Schema(description = "应用名称", example = "dataware-collectx") @NotBlank String app,
            @RequestParam(value = "host", required = false) @Schema(description = "主机名称", example = "app01") String host,
            @RequestParam(value = "instance", required = false) @Schema(description = "实例名称", example = "dataware-collectx#01") String instance,
            @RequestParam(value = "logLevel", required = false) @Schema(description = "日志级别", example = "[INFO,ERROR]") List<String> logLevel,
            @RequestParam("queryType") @Schema(description = "查询类型", example = "regexp/keyword") @NotBlank String queryType,
            @RequestParam(value = "queryValue", required = false) @Schema(description = "查询值", example = "error.*") String queryValue,
            HttpServletResponse response) {

        //log.info("进入downloadLogs方法");
        //log.debug("请求参数：startTime={}, endTime={}, app={}, host={}, instance={}, logLevel={}, queryType={}, queryValue={}, response={}",
        //        startTime, endTime, app, host, instance, logLevel, queryType, queryValue, response);

        LogsQueryRequestVo requestVo = new LogsQueryRequestVo().build(app, startTime, endTime, host, instance, logLevel, queryType, queryValue);
        logsService.downloadLogs(requestVo, response);
    }

    @Operation(summary = "获取应用列表", description = "获取所有应用列表", responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponseVo.class)))
    })
    @GetMapping("getApps")
    public ResponseVo<List<String>> getAppList() {
        return logsService.getAppList();
    }

    @Operation(summary = "获取主机列表", description = "根据应用名获取主机列表", responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponseVo.class)))
    })
    @GetMapping("getHosts")
    public ResponseVo<List<String>> getHostList(
            @RequestParam("app") @Schema(example = "bpms-app#01") @NotBlank String app) {
        return logsService.getHostList(app);
    }

    @Operation(summary = "获取实例列表", description = "根据应用名和主机名获取实例列表", responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponseVo.class)))
    })
    @GetMapping("getInstances")
    public ResponseVo<List<String>> getInstanceList(
            @RequestParam("app") @Schema(example = "bpms-app#01") @NotBlank String app,
            @RequestParam("host") @Schema(example = "host1") @NotBlank String host) {
        return logsService.getInstanceList(app, host);
    }

}