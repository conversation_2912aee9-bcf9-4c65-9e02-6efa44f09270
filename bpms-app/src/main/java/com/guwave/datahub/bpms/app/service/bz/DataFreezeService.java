package com.guwave.datahub.bpms.app.service.bz;

import com.google.common.collect.Lists;
import com.guwave.datahub.bpms.app.ck.CkProvider;
import com.guwave.datahub.bpms.app.util.HdfsUtil;
import com.guwave.datahub.bpms.dao.repository.dw.*;
import com.guwave.datahub.bpms.dao.repository.linkx.BzAppInstanceRecordRepository;
import com.guwave.datahub.bpms.dao.vo.bpms.dataFreeze.DataFreezeRecord;
import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DataFreezeService {

    public static final String DELETE_CK_SQL_TEMPLATE = "alter table {TABLE} on cluster cluster_3shards_1replicas update IS_DELETE = 9 where UPLOAD_TYPE = 'AUTO' and IS_DELETE = 0 and (CUSTOMER, FACTORY, TEST_AREA, DEVICE_ID, LOT_ID) in ({WHERE_IN})";
    public static final String WHERE_IN = "{WHERE_IN}";
    public static final String WHERE_TEMPLATE = "('{CUSTOMER}','{FACTORY}','{TEST_AREA}','{DEVICE_ID}','{LOT_ID}')";
    public static final String MYSQL_QUERY_IDS_SQL_TEMPLATE = "select id from {TABLE} where (customer, factory, test_area, device_id, lot_id) in ({WHERE_IN})";
    private static final List<String> NEED_DELETE_TABLE_SHORT_NAMES = Lists.newArrayList(DwTableEnum.DWD_TEST_ITEM_DETAIL.getLocalTable(), DwTableEnum.DWD_TEST_ITEM_BITMEM_DETAIL.getLocalTable());
    public static final String CLEAN_CK_DISK_SQL_TEMPLATE = "alter table {TABLE} on cluster cluster_3shards_1replicas DELETE IN PARTITION {PARTITION} where IS_DELETE = 9";
    public static final String PARTITION_TEMPLATE = "('{CUSTOMER}', '{UPLOAD_TYPE}', '{TEST_AREA}', '{FACTORY}', '{SUB_CUSTOMER}', '{DEVICE_ID}', {LOT_BUCKET})";

    @Value("${spring.hdfs.dwd.resultDir}")
    private String dwdResultDir;
    @Value("${spring.hdfs.dwd.historyResultDir}")
    private String dwdHistoryResultDir;
    @Value("${spring.hdfs.dim.resultDir}")
    private String dimResultDir;
    @Value("${spring.hdfs.dws.resultDir}")
    private String dwsResultDir;
    @Value("${spring.freezeDataTask.lotValidDay}")
    private Long freezeDataTaskLotValidDay;
    @Value("${spring.freezeDataTask.lotValidDayForDevice}")
    private String freezeDataTaskLotValidDayForDevice;
    @Value("${spring.freezeDataTask.lotValidTimeType}")
    private Integer freezeDataTaskLotValidTimeType;
    @Value("${spring.lotBucketNum}")
    private Integer lotBucketNum;

    @Autowired
    private DwTableRepository dwTableRepository;
    @Autowired
    private HdfsUtil hdfsUtil;
    @Autowired
    private CkProvider ckProvider;
    @PersistenceContext(unitName = "secondaryPersistenceUnit")
    private EntityManager entityManagerSecondary;
    @PersistenceContext(unitName = "tertiaryPersistenceUnit")
    private EntityManager entityManagerTertiary;
    @Autowired
    private WarehousingTaskRecordRepository warehousingTaskRecordRepository;
    @Autowired
    private BzAppInstanceRecordRepository bzAppInstanceRecordRepository;
    @Autowired
    private FileLoadingLogRepository fileLoadingLogRepository;
    @Autowired
    private LotWaferWarehousingRecordRepository lotWaferWarehousingRecordRepository;
    @Autowired
    private FileWarehousingRecordRepository fileWarehousingRecordRepository;
    @Autowired
    private ExpireDeleteRecordRepository expireDeleteRecordRepository;


    private final Map<String, Long> freezeDataTaskLotValidDayForDeviceMap = new HashMap<>();

    @PostConstruct
    public void init() {
        if (StringUtils.isNotBlank(freezeDataTaskLotValidDayForDevice)) {
            String[] deviceConfigs = freezeDataTaskLotValidDayForDevice.split(Constant.SEMICOLON);
            for (String deviceConfig : deviceConfigs) {
                String[] parts = deviceConfig.split(Constant.COLON);
                if (parts.length == 2) {
                    String devicesPart = parts[0];
                    Long validDay = Long.valueOf(parts[1]);
                    String[] devices = devicesPart.split(Constant.COMMA);
                    for (String device : devices) {
                        freezeDataTaskLotValidDayForDeviceMap.put(device, validDay);
                    }
                }
            }
        }
        freezeDataTaskLotValidDayForDeviceMap.put(null, freezeDataTaskLotValidDay);
    }

    /**
     * 冻结历史数据
     */
    @Scheduled(cron = "${spring.scheduler.timer.freezeDataTask}")
    @ScheduleSwitch
    public void runDataFreezeJob() {
        try {
            deleteCk();
        } catch (Exception e) {
            log.info("runDataFreezeJob 异常：", e);
        }
    }

    /**
     * 实际从磁盘上删除数据
     */
    @Scheduled(cron = "${spring.scheduler.timer.deleteCkDataFromDiskTask}")
    @ScheduleSwitch
    public void deleteCkDataFromDiskJob() {
        try {
            deleteCkDataFromDisk();
        } catch (Exception e) {
            log.info("deleteCkDataFromDiskJob 异常：", e);
        }
    }

    public void deleteCkDataFromDisk() {
        List<ExpireDeleteRecord> tasks = expireDeleteRecordRepository.findAllByPlatformAndDeleteFromDiskFlagOrderById(Platform.CK, 0, Pageable.ofSize(1));
        if (CollectionUtils.isEmpty(tasks)) {
            log.info("ck磁盘没有待清理的数据");
            return;
        }
        ExpireDeleteRecord tmp = tasks.get(0);
        Integer lotBucket = getLotBucket(tmp.getLotId());
        List<ExpireDeleteRecord> expireDeleteRecords = expireDeleteRecordRepository.findAllByPlatformAndDeleteFromDiskFlagAndCustomerAndTestAreaAndFactoryAndSubCustomerAndDeviceId(
                tmp.getPlatform(), tmp.getDeleteFromDiskFlag(), tmp.getCustomer(), tmp.getTestArea(), tmp.getFactory(), tmp.getSubCustomer(), tmp.getDeviceId()
        ).stream().filter(t -> Objects.equals(getLotBucket(t.getLotId()), lotBucket)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(expireDeleteRecords)) {
            log.info("lotBucket: {} 没有相关要清理的数据", lotBucket);
            return;
        }
        ExpireDeleteRecord record = expireDeleteRecords.get(0);
        String partition = PARTITION_TEMPLATE
                .replace(Constant.CUSTOMER, record.getCustomer())
                .replace(Constant.UPLOAD_TYPE, UploadType.AUTO.getType())
                .replace(Constant.TEST_AREA, record.getTestArea().getArea())
                .replace(Constant.FACTORY, record.getFactory())
                .replace(Constant.SUB_CUSTOMER, record.getSubCustomer())
                .replace(Constant.DEVICE_ID, record.getDeviceId())
                .replace(Constant.LOT_BUCKET, lotBucket.toString());

        List<DwTable> needDeleteTables = dwTableRepository.findAllByUploadTypeAndDwLayerInAndCalculateFlag(UploadType.AUTO, Lists.newArrayList(DwLayer.DWD), 1);
        List<String> sqls = needDeleteTables.stream()
                .filter(table -> NEED_DELETE_TABLE_SHORT_NAMES.contains(table.getLocalName()))
                .map(table -> CLEAN_CK_DISK_SQL_TEMPLATE
                        .replace(Constant.TABLE, table.getDb() + Constant.POINT + table.getLocalName())
                        .replace(Constant.PARTITION, partition)
                ).collect(Collectors.toList());
        log.info("清理ck磁盘开始 {}", sqls);
        ckProvider.executeSqls(sqls);
        log.info("清理ck磁盘结束");

        expireDeleteRecordRepository.updateDeleteFromDiskFlag(expireDeleteRecords.stream().map(ExpireDeleteRecord::getId).collect(Collectors.toList()));
    }

    public void deleteCk() {
        try {
            List<DataFreezeRecord> expireDatas = new ArrayList<>();
            freezeDataTaskLotValidDayForDeviceMap.forEach((deviceId, validDay) -> {
                List<DataFreezeRecord> expireDataTmps;
                if (Objects.equals(freezeDataTaskLotValidTimeType, 1)) {
                    // use update_time
                    Date updateTime = new Date(System.currentTimeMillis() - validDay * 24L * 60L * 60L * 1000L);
                    log.info("{} use update_time < {}", deviceId, updateTime);
                    expireDataTmps = expireDeleteRecordRepository.findExpireData(updateTime, null, deviceId);
                } else {
                    // use start_time
                    long startT = (System.currentTimeMillis() / 1000) - (validDay * 24L * 60L * 60L);
                    log.info("{} use start_time < {}", deviceId, startT);
                    expireDataTmps = expireDeleteRecordRepository.findExpireData(null, startT, deviceId);
                }
                log.info("查询结果 {} 条", expireDataTmps.size());
                if (deviceId == null) {
                    expireDataTmps = expireDataTmps.stream().filter(t -> !freezeDataTaskLotValidDayForDeviceMap.containsKey(t.getDeviceId())).collect(Collectors.toList());
                    log.info("过滤掉特殊指定的device后 {} 条", expireDataTmps.size());
                }
                expireDatas.addAll(expireDataTmps);
            });


            if (CollectionUtils.isEmpty(expireDatas)) {
                log.info("没有待冻结的记录");
                return;
            }
            log.info("待冻结的记录 {} 条", expireDatas.size());

            String sqlWhereIn = expireDatas.stream().map(t ->
                    WHERE_TEMPLATE
                            .replace(Constant.CUSTOMER, t.getCustomer())
                            .replace(Constant.FACTORY, t.getFactory())
                            .replace(Constant.TEST_AREA, t.getTestArea())
                            .replace(Constant.DEVICE_ID, t.getDeviceId())
                            .replace(Constant.LOT_ID, t.getLotId())
            ).distinct().collect(Collectors.joining(Constant.COMMA));

            // 删除ck testItemDetail
            deleteCkTestItem(sqlWhereIn);

            List<Long> warehousingTaskRecordIds = expireDatas.stream().map(DataFreezeRecord::getWarehousingTaskRecordId).collect(Collectors.toList());

            // dw_lot_wafer_warehousing_record 更新
            updateDwLotWaferWarehousingRecord(warehousingTaskRecordIds);

            // dw_warehousing_task_record 更新
            updateDwWarehousingTaskRecord(warehousingTaskRecordIds);

            // 保存删除记录
            saveExpireDeleteRecord(expireDatas);
        } catch (Exception e) {
            log.info("冻结数据异常：", e);
        }
    }

    private void deleteCkTestItem(String sqlWhereIn) {
        List<DwTable> needDeleteTables = dwTableRepository.findAllByUploadTypeAndDwLayerInAndCalculateFlag(UploadType.AUTO, Lists.newArrayList(DwLayer.DWD), 1);
        List<String> sqls = needDeleteTables.stream()
                .filter(table -> NEED_DELETE_TABLE_SHORT_NAMES.contains(table.getLocalName()))
                .map(table -> DELETE_CK_SQL_TEMPLATE
                        .replace(Constant.TABLE, table.getDb() + Constant.POINT + table.getLocalName())
                        .replace(WHERE_IN, sqlWhereIn)
                ).collect(Collectors.toList());
        log.info("删除ck开始 {}", sqls);
        ckProvider.executeSqls(sqls);
        log.info("删除ck结束");
    }

    private void updateDwLotWaferWarehousingRecord(List<Long> warehousingTaskRecordIds) {
        List<Long> lotWaferWarehousingRecordIds = lotWaferWarehousingRecordRepository.queryWithWarehousingTaskRecordIds(warehousingTaskRecordIds);
        if (CollectionUtils.isNotEmpty(lotWaferWarehousingRecordIds)) {
            log.info("更新dw_lot_wafer_warehousing_record开始 {}", lotWaferWarehousingRecordIds);
            lotWaferWarehousingRecordRepository.updateFreezeFlag(lotWaferWarehousingRecordIds, 1);
            log.info("更新dw_lot_wafer_warehousing_record结束 {}条", lotWaferWarehousingRecordIds.size());
        }
    }

    private void updateDwWarehousingTaskRecord(List<Long> warehousingTaskRecordIds) {
        log.info("更新dw_warehousing_task_record开始 {}", warehousingTaskRecordIds);
        warehousingTaskRecordRepository.freezeByids(warehousingTaskRecordIds);
        log.info("更新dw_warehousing_task_record结束 {}条", warehousingTaskRecordIds.size());
    }

    private void saveExpireDeleteRecord(List<DataFreezeRecord> expireDatas) {
        log.info("保存ExpireDeleteRecord {}条", expireDatas.size());
        Date date = new Date();
        List<ExpireDeleteRecord> expireDeleteRecords = expireDatas.stream().map(t -> {
            ExpireDeleteRecord target = new ExpireDeleteRecord();
            BeanUtils.copyProperties(t, target);
            target
                    .setTestArea(TestArea.of(t.getTestArea()))
                    .setLotType(LotType.of(t.getLotType()))
                    .setFileCategory(FileCategory.of(t.getFileCategory()))
                    .setPlatform(Platform.CK)
                    .setDeleteFromDiskFlag(0)
                    .setProcessStatus(ProcessStatus.FREEZE)
                    .setCreateTime(date)
                    .setUpdateTime(date)
                    .setCreateUser(Constant.SYSTEM)
                    .setUpdateUser(Constant.SYSTEM);
            return target;
        }).collect(Collectors.toList());
        expireDeleteRecordRepository.saveAll(expireDeleteRecords);
    }

    private Integer getLotBucket(String lotId) {
        return Math.abs(lotId.hashCode()) % lotBucketNum;
    }
}
