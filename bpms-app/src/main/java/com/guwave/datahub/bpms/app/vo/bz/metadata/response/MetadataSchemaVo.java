package com.guwave.datahub.bpms.app.vo.bz.metadata.response;

import com.guwave.datahub.bpms.dao.domain.metadata.MetadataSchema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class MetadataSchemaVo {

    @Schema(title = "ID", example = "24")
    private Long id;

    @Schema(title = "数据库", description = "clickhouse/mysql", example = "clickhouse")
    private String datasource;

    @Schema(title = "库名", example = "clickhouse")
    private String databaseName;

    @Schema(title = "表名", example = "ods_dc_device_info_cluster")
    private String tableName;

    @Schema(title = "字段名", example = "ECID_CP_STAGE_ID")
    private String fieldName;

    @Schema(title = "字段类型", example = "Int32")
    private String type;

    @Schema(title = "缺省值", example = "")
    private String defaultValue;

    @Schema(title = "字段位置", example = "12")
    private Integer position;

    @Schema(title = "字段注释", example = "FT、SLT关联的CP的id")
    private String comment;

    @Schema(title = "CP计算逻辑", example = "从onedata.dc_device_info获取")
    private String cpLogic;

    @Schema(title = "FT计算逻辑", example = "从onedata.dc_device_info获取")
    private String ftLogic;

    @Schema(title = "备注", example = "无")
    private String info;

    public static MetadataSchemaVo of(MetadataSchema metadataSchema) {
        MetadataSchemaVo metadataSchemaVo = new MetadataSchemaVo();
        metadataSchemaVo.setId(metadataSchema.getId());
        metadataSchemaVo.setDatasource(metadataSchema.getDatasource());
        metadataSchemaVo.setDatabaseName(metadataSchema.getDatabaseName());
        metadataSchemaVo.setTableName(metadataSchema.getTableName());
        metadataSchemaVo.setFieldName(metadataSchema.getFieldName());
        metadataSchemaVo.setType(metadataSchema.getFieldType());
        metadataSchemaVo.setDefaultValue(metadataSchema.getDefaultValue());
        metadataSchemaVo.setPosition(metadataSchema.getPosition());
        metadataSchemaVo.setComment(metadataSchema.getFieldComment());
        metadataSchemaVo.setCpLogic(metadataSchema.getCpLogic());
        metadataSchemaVo.setFtLogic(metadataSchema.getFtLogic());
        metadataSchemaVo.setInfo(metadataSchema.getInfo());
        return metadataSchemaVo;
    }
}
