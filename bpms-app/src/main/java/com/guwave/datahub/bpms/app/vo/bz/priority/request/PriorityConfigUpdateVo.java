package com.guwave.datahub.bpms.app.vo.bz.priority.request;

import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.Date;

@EqualsAndHashCode(callSuper = false)
@Data
@Schema(description = "优先级配置请求")
public class PriorityConfigUpdateVo extends PageableRequestVo {

    @Min(1)
    @Schema(description = "ID")
    private Long id;

    @NotBlank(message = "优先级不能为空")
    @Schema(description = "优先级")
    private String priority;

    @Schema(description = "最小的startT")
    private Date minStartT;

    @Schema(description = "最大的startT")
    private Date maxStartT;

    @Schema(description = "最小FTP修改时间")
    private Date minFtpMtime;

    @Schema(description = "最大FTP修改时间")
    private Date maxFtpMtime;

    @Schema(description = "FTP路径")
    private String ftpPath;
}
