package com.guwave.datahub.bpms.app.web.aspect;

import com.alibaba.fastjson.JSON;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.util.AnnotationUtil;
import com.guwave.datahub.bpms.app.util.IpUtil;
import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import com.guwave.datahub.bpms.common.constant.Constant;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * LogAspect
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2024-02-26 15:16:40
 */
@Aspect
@Component
public class LogAspect {

    private static final Logger LOGGER = LoggerFactory.getLogger(LogAspect.class);

    @Pointcut("(@annotation(com.guwave.datahub.bpms.app.annotation.Log) || within(@com.guwave.datahub.bpms.app.annotation.Log *)) && !@annotation(com.guwave.datahub.bpms.app.annotation.NoLog)")
    public void logPointCut() {

    }

    @Around("logPointCut()")
    public Object logPointCut(ProceedingJoinPoint joinPoint) throws Throwable {
        StopWatch sw = new StopWatch();
        sw.start();

        Log log = AnnotationUtil.getAnnotation((MethodSignature) joinPoint.getSignature(), Log.class);
        assert log != null;

        Object result;
        try {
            String customer = RequestContext.getCustomer();
            LOGGER.info("当前操作的customer: {}", customer);
            if (log.request()) {
                ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                assert requestAttributes != null;
                HttpServletRequest request = requestAttributes.getRequest();
                String method = request.getMethod();
                String uri = request.getRequestURI();
                String ip = IpUtil.getIpAddress(request);
                // Log request parameters
                LOGGER.info("开始请求, 请求方法: {}, 请求接口: {}, 请求ip: {}, 请求参数: {}", method, uri, ip, this.buildRequestLog(joinPoint));
            }
            result = joinPoint.proceed();
            if (log.response()) {
                // Log response result
                LOGGER.info("结束请求, 返回结果: {}", JSON.toJSONString(result));
            }
        } catch (Exception e) {
            LOGGER.info("请求异常", e);
            throw e;
        } finally {
            sw.stop();
            // Log total request time
            if (log.cost()) {
                LOGGER.info("总请求时间: {} ms", sw.getTotalTimeMillis());
            }
        }
        return result;
    }

    /**
     * 构造请求日志
     *
     * @param proceedingJoinPoint {@link ProceedingJoinPoint}
     * @return {@link String}
     */
    private String buildRequestLog(ProceedingJoinPoint proceedingJoinPoint) {
        MethodSignature signature = (MethodSignature) proceedingJoinPoint.getSignature();
        Object[] args = proceedingJoinPoint.getArgs();
        String[] parameterNames = signature.getParameterNames();
        int argLen = args.length;
        int parameterNameLen = parameterNames.length;
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < argLen; ++i) {
            Object parameterValue = args[i];
            if (needSkipParameter(parameterValue)) {
                continue;
            }
            String parameterName = i < parameterNameLen ? parameterNames[i] : Constant.EMPTY;
            sb.append(parameterName).append(Constant.EQUAL).append(JSON.toJSONString(parameterValue)).append(Constant.COMMA);
        }
        return StringUtils.isBlank(sb.toString()) ? Constant.EMPTY : sb.substring(0, sb.length() - 1);
    }

    /**
     * 判断是否需要跳过参数，对于request/response/multipartFile这种需要跳过，不打印
     *
     * @param parameter 参数
     * @return 是否跳过
     */
    private boolean needSkipParameter(Object parameter) {
        if (null == parameter) {
            return false;
        }
        Class cls = parameter.getClass().isArray() ? parameter.getClass().getComponentType() : parameter.getClass();
        return implOf(cls, HttpServletRequest.class)
                || implOf(cls, HttpServletResponse.class)
                || implOf(cls, MultipartFile.class);
    }

    private boolean implOf(Class cls, Class iInterface) {
        if (cls.equals(iInterface)) {
            return true;
        }
        Class[] interfaces = cls.getInterfaces();
        for (Class i : interfaces) {
            if (i.equals(iInterface)) {
                return true;
            }
        }
        return false;
    }
}
