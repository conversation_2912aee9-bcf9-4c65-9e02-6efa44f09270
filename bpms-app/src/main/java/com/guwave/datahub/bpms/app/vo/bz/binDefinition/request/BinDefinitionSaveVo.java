package com.guwave.datahub.bpms.app.vo.bz.binDefinition.request;

import com.guwave.datahub.bpms.app.vo.bz.binDefinition.model.BinDefinitionDetailVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Bin Definition保存VO
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@Schema(title = "Bin Definition保存请求")
public class BinDefinitionSaveVo {

    @NotNull(message = "测试程序名称不能为空")
    @Schema(description = "测试程序名称", example = "TestProgram1", required = true)
    private String testProgram;

    @Valid
    @NotEmpty(message = "HBIN定义不能为空")
    @Schema(description = "HBIN定义列表", required = true)
    private List<BinDefinitionDetailVo> hbinList;

    @Valid
    @NotEmpty(message = "SBIN定义不能为空")
    @Schema(description = "SBIN定义列表", required = true)
    private List<BinDefinitionDetailVo> sbinList;
}