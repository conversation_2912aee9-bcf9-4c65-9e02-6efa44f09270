package com.guwave.datahub.bpms.app.service.bz.handler.clearRulePreview.preHandler;

import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model.ConfResult;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model.ParseField;
import com.guwave.datahub.bpms.dao.domain.clearRulePreview.ClearRulePreviewRecord;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.common.util.WaferUtil;
import com.guwave.onedata.dataware.parser.stdf.util.DwdCommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Component
public abstract class CpPreHandler extends PreHandler {
    private static final String NEED_GENERATE_WAFER_NO_FLAG = "-99";

    @Value("${spring.handler.cp.keyFields}")
    private String keyFields;

    @Override
    protected String getKeyFields() {
        return keyFields;
    }

    @Override
    public void beforeModifyField(ClearRulePreviewRecord record, Map<ParseField, ConfResult> confResultMap) {
    }

    @Override
    public void afterModifyField(ClearRulePreviewRecord record, Map<ParseField, ConfResult> confResultMap) {
        if (Objects.isNull(record.getTestArea())) {
            record.setTestArea(TestArea.CP);
        }
        if (NEED_GENERATE_WAFER_NO_FLAG.equals(record.getWaferNo())) {
            record.setWaferNo("26");
            confResultMap.get(ParseField.WAFER_NO).setTip("此waferNo解出来值为" + NEED_GENERATE_WAFER_NO_FLAG + "，在正式入库的时候会被伪造一个大于25的数字入库！");
        }

        // 设置standardWaferId
        if (StringUtils.isNotBlank(record.getRealWaferId())) {
            record.setWaferId(record.getRealWaferId());
        } else {
            record.setWaferId(WaferUtil.formatStandardWaferId(record.getLotId(), record.getWaferNo()));
        }
    }
}
