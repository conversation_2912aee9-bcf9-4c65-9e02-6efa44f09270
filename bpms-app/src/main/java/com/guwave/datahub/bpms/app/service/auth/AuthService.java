package com.guwave.datahub.bpms.app.service.auth;

import com.alibaba.fastjson.JSON;
import com.guwave.datahub.bpms.app.exception.BpmsAuthException;
import com.guwave.datahub.bpms.app.vo.auth.UserInfo;
import com.guwave.datahub.bpms.app.vo.auth.UserInfoUcVo;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 鉴权Service
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-27 14:28:44
 */
@Service
@AllArgsConstructor
public class AuthService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AuthService.class);

    private final AuthApi authApi;

    public UserInfo auth(String token) {
        try {
            StopWatch sw = new StopWatch();
            sw.start();
            LOGGER.info("开始请求AuthApi");
            UserInfoUcVo userInfoUcVo = this.authApi.getInfo();
            sw.stop();
            LOGGER.info("结束请求AuthApi, 结果: {}, 耗时: {}ms", JSON.toJSONString(userInfoUcVo), sw.getTotalTimeMillis());
            if (null == userInfoUcVo || HttpStatus.OK.value() != userInfoUcVo.getCode()) {
                LOGGER.info("UC鉴权失败");
                throw new BpmsAuthException(ResponseCode.Common.AUTH_UC_FAIL);
            }
            return UserInfo.buildUserInfo(userInfoUcVo, token);
        } catch (BpmsAuthException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.info("请求UC鉴权接口失败: {}", e.getMessage());
            throw new BpmsAuthException(e, ResponseCode.Common.AUTH_REQUEST_UC_FAIL);
        }
    }
}
