package com.guwave.datahub.bpms.app.vo.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 从UC鉴权获取的User信息
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-27 11:02:25
 */
@Data
public class UserInfoUcVo implements Serializable {


    /**
     * msg : 操作成功
     * code : 200
     * permissions : ["system:user:resetPwd","system:post:list","system:dept:remove","vc:device:add","work:eqp:detail","work:data:add","system:dict:edit","work:eqp:deleteFile","system:user:query","work:task:query","work:task:add","system:user:add","vc:device:export","system:notice:remove","system:user:export","work:task:find","system:role:remove","table:PRO_table:index","datamanage:chartmanagement:list","system:dept:query","system:dict:list","work:task:upload","table:FA_table:index","work:eqp:export","system:notice:list","vc:device:query","datamanage:dataset:list","system:dict:query","work:eqp:uploadImg","system:notice:query","system:notice:edit","work:eqp:downloadFile","work:eqp:list","eqp:log:list","work:task:list","system:post:edit","work:eqp:edit","system:dict:export","work:eqp:remove","system:post:query","system:post:remove","work:eqp:uploadFile","system:user:remove","system:role:list","system:user:import","system:dict:remove","system:user:edit","system:post:export","work:task:remove","system:role:edit","system:dept:list","vc:device:list","system:role:add","work:task:assign","system:dict:add","system:dept:edit","system:post:add","system:user:list","system:notice:add","table:COB_table:index","system:role:export","data:manage:upload","datamanage:analysis:list","table:Type_table:index","system:role:query","work:eqp:add","vc:device:remove","vc:device:edit","system:dept:add"]
     * roles : ["NORMS","admin","vc"]
     * user : {"searchValue":null,"createBy":"admin","createTime":"2021-04-25 19:21:34","updateBy":null,"updateTime":null,"remark":null,"params":{},"userId":2,"deptId":101,"userName":"admin","nickName":"管理员","email":"","phonenumber":"15899999999","sex":"0","avatar":"","salt":null,"status":"0","delFlag":"0","loginIp":"","loginDate":null,"dept":{"searchValue":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{},"deptId":101,"parentId":100,"ancestors":null,"deptCode":null,"deptName":"深圳总公司","orderNum":"1","leaderId":null,"leader":"深圳负责人","phone":null,"email":null,"status":"0","delFlag":null,"parentName":null,"children":[]},"roles":[{"searchValue":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{},"roleId":2,"roleName":"管理员","roleKey":"admin","roleSort":"2","dataScope":"1","menuCheckStrictly":false,"deptCheckStrictly":false,"status":"0","delFlag":null,"flag":false,"menuIds":null,"deptIds":null,"admin":false},{"searchValue":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{},"roleId":103,"roleName":"NORMS","roleKey":"NORMS","roleSort":"0","dataScope":"1","menuCheckStrictly":false,"deptCheckStrictly":false,"status":"0","delFlag":null,"flag":false,"menuIds":null,"deptIds":null,"admin":false},{"searchValue":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{},"roleId":106,"roleName":"VC","roleKey":"vc","roleSort":"0","dataScope":"1","menuCheckStrictly":false,"deptCheckStrictly":false,"status":"0","delFlag":null,"flag":false,"menuIds":null,"deptIds":null,"admin":false}],"roleIds":null,"postIds":null,"tenentId":null,"admin":false}
     */

    private String msg;
    private Integer code;
    private UserBean user;
    private List<String> permissions;
    private List<String> roles;

    @Data
    public static class UserBean implements Serializable {
        /**
         * searchValue : null
         * createBy : admin
         * createTime : 2021-04-25 19:21:34
         * updateBy : null
         * updateTime : null
         * remark : null
         * params : {}
         * userId : 2
         * deptId : 101
         * userName : admin
         * nickName : 管理员
         * email :
         * phonenumber : 15899999999
         * sex : 0
         * avatar :
         * salt : null
         * status : 0
         * delFlag : 0
         * loginIp :
         * loginDate : null
         * dept : {"searchValue":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{},"deptId":101,"parentId":100,"ancestors":null,"deptCode":null,"deptName":"深圳总公司","orderNum":"1","leaderId":null,"leader":"深圳负责人","phone":null,"email":null,"status":"0","delFlag":null,"parentName":null,"children":[]}
         * roles : [{"searchValue":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{},"roleId":2,"roleName":"管理员","roleKey":"admin","roleSort":"2","dataScope":"1","menuCheckStrictly":false,"deptCheckStrictly":false,"status":"0","delFlag":null,"flag":false,"menuIds":null,"deptIds":null,"admin":false},{"searchValue":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{},"roleId":103,"roleName":"NORMS","roleKey":"NORMS","roleSort":"0","dataScope":"1","menuCheckStrictly":false,"deptCheckStrictly":false,"status":"0","delFlag":null,"flag":false,"menuIds":null,"deptIds":null,"admin":false},{"searchValue":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{},"roleId":106,"roleName":"VC","roleKey":"vc","roleSort":"0","dataScope":"1","menuCheckStrictly":false,"deptCheckStrictly":false,"status":"0","delFlag":null,"flag":false,"menuIds":null,"deptIds":null,"admin":false}]
         * roleIds : null
         * postIds : null
         * tenentId : null
         * admin : false
         */

        private boolean admin;
        private String avatar;
        // 当租户使用
        private String companyCode;

        // 子租户
        private String subCustomerCode;

        private String createBy;
        private String createTime;
        private String updateBy;
        private String updateTime;
        private String delFlag;

        private String email;
        private String searchValue;


        private String remark;
        private Object params;
        private Long userId;
        private int deptId;
        private String userName;
        private String nickName;
        @JsonProperty("phonenumber")
        private String phoneNumber;
        private String sex;
        private Object salt;
        private String status;
        private String loginIp;
        private String loginDate;
        private DeptBean dept;
        private List<Integer> roleIds;
        private List<String> postIds;
        private Object tenentId;
        private List<RolesBean> roles;

        @Data
        public static class DeptBean implements Serializable {
            /**
             * searchValue : null
             * createBy : null
             * createTime : null
             * updateBy : null
             * updateTime : null
             * remark : null
             * params : {}
             * deptId : 101
             * parentId : 100
             * ancestors : null
             * deptCode : null
             * deptName : 深圳总公司
             * orderNum : 1
             * leaderId : null
             * leader : 深圳负责人
             * phone : null
             * email : null
             * status : 0
             * delFlag : null
             * parentName : null
             * children : []
             */

            private String searchValue;
            private String createBy;
            private String createTime;
            private String updateBy;
            private String updateTime;
            private String remark;
            private Object params;
            private int deptId;
            private int parentId;
            private String ancestors;
            private String deptCode;
            private String deptName;
            private String orderNum;
            private String leaderId;
            private String leader;
            private String phone;
            private String email;
            private String status;
            private String delFlag;
            private String parentName;
            private List<?> children;

        }

        @Data
        public static class RolesBean implements Serializable {
            /**
             * searchValue : null
             * createBy : null
             * createTime : null
             * updateBy : null
             * updateTime : null
             * remark : null
             * params : {}
             * roleId : 2
             * roleName : 管理员
             * roleKey : admin
             * roleSort : 2
             * dataScope : 1
             * menuCheckStrictly : false
             * deptCheckStrictly : false
             * status : 0
             * delFlag : null
             * flag : false
             * menuIds : null
             * deptIds : null
             * admin : false
             */

            private String searchValue;
            private String createBy;
            private String createTime;
            private String updateBy;
            private String updateTime;
            private String remark;
            private Object params;
            private int roleId;
            private String roleName;
            private String roleKey;
            private String roleSort;
            private String dataScope;
            private boolean menuCheckStrictly;
            private boolean deptCheckStrictly;
            private String status;
            private Integer delFlag;
            private boolean flag;
            private List<String> menuIds;
            private List<String> deptIds;
            private boolean admin;
        }
    }
}
