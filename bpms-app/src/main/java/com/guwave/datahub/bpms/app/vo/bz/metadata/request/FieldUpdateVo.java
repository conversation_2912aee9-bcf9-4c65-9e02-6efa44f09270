package com.guwave.datahub.bpms.app.vo.bz.metadata.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * FieldUpdateVo
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-03-11 15:31:40
 */
@Schema(title = "metadata 修改字段信息")
@Data
@NoArgsConstructor
public class FieldUpdateVo {

    @Schema(title = "字段ID", example = "1")
    @NotNull(message = "ID")
    private Long id;

    @Schema(title = "CP计算逻辑", example = "从TestItemData中取出")
    private String cpLogic;

    @Schema(title = "FT计算逻辑", example = "从TestItemData中取出")
    private String ftLogic;

    @Schema(title = "备注", example = "0，表示初测 1，表示第一次复测 2，表示第二次复测")
    private String info;
}
