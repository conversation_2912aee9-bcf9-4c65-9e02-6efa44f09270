package com.guwave.datahub.bpms.app.vo.bz.priority.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Schema(description = "任务优先级调整请求")
public class TaskPriorityUpdateVo {
    @Schema(description = "任务类型，AUTO/MANUAL/SPARK")
    private String taskType;

    @Schema(description = "任务ID列表")
    @NotEmpty
    private List<Long> taskIds;

    @Schema(description = "优先级")
    private String priority;
}
