package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(title = "子客户目录规则")
public class DwSftpSubCustomerRuleRequestVo {
    @Schema(title = "id")
    private Long id;

    @Schema(description = "子客户", example = "GUWAVE")
    private String subCustomer;

    @Schema(description = "测试工厂名称", example = "TSMC")
    private String factory;

    @Schema(description = "测试子工厂", example = "TSMC")
    private String factorySite;

    @Schema(description = "测试阶段类型: CP/FT/WAT/SLT/EQC 等", example = "CP")
    private String testArea;

    @Schema(description = "ftp服务ip", example = "************")
    private String ftpIp;

    @Schema(description = "ftp目录", example = "/data1/stdf/")
    private String directory;
}
