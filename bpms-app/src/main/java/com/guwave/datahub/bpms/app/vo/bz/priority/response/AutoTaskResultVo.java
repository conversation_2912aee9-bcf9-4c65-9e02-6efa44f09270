package com.guwave.datahub.bpms.app.vo.bz.priority.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "自动入库任务")
public class AutoTaskResultVo extends TaskPriority {
    @Schema(description = "factory")
    private String factory;

    @Schema(description = "deviceId")
    private String deviceId;

    @Schema(description = "lotType")
    private String lotType;

    @Schema(description = "fileCategory")
    private String fileCategory;

    @Schema(description = "lotId")
    private String lotId;

    @Schema(description = "waferNo")
    private String waferNo;

    @Schema(description = "testArea")
    private String testArea;

    @Schema(description = "testStage")
    private String testStage;
}
