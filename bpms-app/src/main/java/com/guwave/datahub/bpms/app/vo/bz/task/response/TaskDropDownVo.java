package com.guwave.datahub.bpms.app.vo.bz.task.response;

import com.guwave.datahub.bpms.app.vo.bz.replay.response.DataReplayDropDownVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/12
 * @description FieldFilterResultVo
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(title = "task YMS级联查询结果")
public class TaskDropDownVo {

    private static final Pattern NUMBER_PATTERN = Pattern.compile("-?\\d+");

    @Schema(title = "下拉框结果值", example = "FT")
    private String name;

    @Schema(title = "页面显示下拉框结果值", example = "FT")
    private String showName;

    @Schema(title = "是否可以被选", example = "true")
    private Boolean selectable;

    public static TaskDropDownVo of(String name) {
        return TaskDropDownVo.of(name, name);
    }

    public static TaskDropDownVo of(String name, String showName) {
        return new TaskDropDownVo(name, showName, true);
    }

    public static TaskDropDownVo of(String name, String showName, Boolean selectable) {
        return new TaskDropDownVo(name, showName, selectable);
    }

    public static List<TaskDropDownVo> ofList(List<String> names) {
        if (CollectionUtils.isEmpty(names)) {
            return Collections.emptyList();
        }
        boolean isNumer = names.stream().allMatch(t -> NUMBER_PATTERN.matcher(t).matches());
        return names.stream()
                .map(TaskDropDownVo::of)
                .sorted(isNumer ? Comparator.comparingInt(t -> Integer.parseInt(t.getShowName())) : Comparator.comparing(TaskDropDownVo::getShowName))
                .collect(Collectors.toList());
    }
}
