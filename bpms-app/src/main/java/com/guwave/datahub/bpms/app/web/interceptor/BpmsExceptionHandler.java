package com.guwave.datahub.bpms.app.web.interceptor;

import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.guwave.datahub.bpms.app.exception.*;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import org.apache.dubbo.rpc.RpcException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.web.servlet.MultipartProperties;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolationException;
import java.util.Objects;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 异常统一处理
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2024-02-28 11:10:22
 */
@RestControllerAdvice
public class BpmsExceptionHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(BpmsExceptionHandler.class);

    @Autowired
    private MultipartProperties multipartProperties;

    @ExceptionHandler(NoHandlerFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseVo<Void> handleNotFound(NoHandlerFoundException e, HttpServletRequest request) {
        LOGGER.info("请求地址{}不存在", request.getRequestURI(), e);
        return ResponseVo.fail(new BpmsException(ResponseCode.Common.COMMON_REQUEST_METHOD_NO_FOUND, request.getRequestURI()));
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseVo<Void> handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
        LOGGER.info("请求地址{},不支持{}请求", request.getRequestURI(), e.getMethod(), e);
        return ResponseVo.fail(new BpmsException(ResponseCode.Common.COMMON_REQUEST_METHOD_INVALID, e.getMethod()));
    }

    @ExceptionHandler(BpmsAuthException.class)
    public ResponseVo<Void> handleAuthException(BpmsAuthException e, HttpServletRequest request) {
        LOGGER.info("请求地址{}发生鉴权异常", request.getRequestURI(), e);
        return ResponseVo.fail(e);
    }

    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseVo<Void> handleSizeLimitExceededException(MaxUploadSizeExceededException e, HttpServletRequest request) {
        LOGGER.info("请求地址{}数据大小超出限制", request.getRequestURI(), e);
        return ResponseVo.fail(new BpmsException(ResponseCode.Common.FILE_SIZE_EXCEED_LIMIT, multipartProperties.getMaxFileSize().toBytes() / 1024L / 1024L / 1024L + "GB"));
    }

    @ExceptionHandler(BpmsLicenseException.class)
    public ResponseVo<Void> handleLicenseException(BpmsLicenseException e, HttpServletRequest request) {
        LOGGER.info("请求地址{}发生License异常", request.getRequestURI(), e);
        return ResponseVo.fail(e);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseVo<Void> handleHttpMessageNotReadableException(HttpMessageNotReadableException e, HttpServletRequest request) {
        if (e.getCause() instanceof InvalidFormatException) {
            String message = ((InvalidFormatException) e.getCause()).getPath().get(0).getFieldName();
            LOGGER.info("请求地址{}发生传参异常, 参数: {}", request.getRequestURI(), message, e);
            return ResponseVo.fail(new BpmsParamException(e, message));
        }
        LOGGER.info("请求地址{}发生传参异常", request.getRequestURI(), e);
        return ResponseVo.fail(new BpmsParamException(e));
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseVo<Void> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        String message = e.getName();
        LOGGER.info("请求地址{}发生传参异常, 参数: {}", request.getRequestURI(), message, e);
        return ResponseVo.fail(new BpmsParamException(e, message));
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseVo<Void> handleMissingServletRequestParameterException(MissingServletRequestParameterException e, HttpServletRequest request) {
        String message = e.getParameterName();
        LOGGER.info("请求地址{}发生传参异常, 参数: {}", request.getRequestURI(), message, e);
        return ResponseVo.fail(new BpmsParamException(e, message));
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseVo<Void> handleConstraintViolationException(ConstraintViolationException e, HttpServletRequest request) {
        String propertyPath = e.getConstraintViolations().iterator().next().getPropertyPath().toString();
        int index = propertyPath.lastIndexOf('.');
        String message = propertyPath.substring(index + 1);
        LOGGER.info("请求地址{}发生传参异常, 参数: {}", request.getRequestURI(), message, e);
        return ResponseVo.fail(new BpmsParamException(e, message));
    }

    @ExceptionHandler(BindException.class)
    public ResponseVo<Void> handleBindException(BindException e, HttpServletRequest request) {
        String message = e.getAllErrors().get(0).getDefaultMessage();
        LOGGER.info("请求地址{}发生传参异常, 参数: {}", request.getRequestURI(), message, e);
        return ResponseVo.fail(new BpmsParamException(e, message));
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseVo<Void> handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletRequest request) {
        String message = Objects.requireNonNull(e.getBindingResult().getFieldError()).getDefaultMessage();
        LOGGER.info("请求地址{}发生传参异常, 参数: {}", request.getRequestURI(), message, e);
        return ResponseVo.fail(new BpmsParamException(e, message));
    }

    @ExceptionHandler(RpcException.class)
    public ResponseVo<Void> handleRpcException(RpcException e, HttpServletRequest request) {
        LOGGER.info("请求地址{}发生RPC请求异常", request.getRequestURI(), e);
        return ResponseVo.fail(new BpmsRpcInvokeException(e));
    }

    @ExceptionHandler(BpmsBusinessException.class)
    public ResponseVo<Void> handleBusinessException(BpmsBusinessException e, HttpServletRequest request) {
        LOGGER.info("请求地址{}发生业务异常", request.getRequestURI(), e);
        return ResponseVo.fail(e);
    }

    @ExceptionHandler(BpmsException.class)
    public ResponseVo<Void> handleBpmsException(BpmsException e, HttpServletRequest request) {
        LOGGER.info("请求地址{}发生异常", request.getRequestURI(), e);
        return ResponseVo.fail(e);
    }

    @ExceptionHandler(RuntimeException.class)
    public ResponseVo<Void> handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        LOGGER.info("请求地址{}发生系统异常", request.getRequestURI(), e);
        return ResponseVo.fail(new BpmsException(ResponseCode.Internal.INTERNAL_ERROR));
    }

    @ExceptionHandler(Exception.class)
    public ResponseVo<Void> handleException(Exception e, HttpServletRequest request) {
        LOGGER.info("请求地址{}发生系统异常", request.getRequestURI(), e);
        return ResponseVo.fail(new BpmsException(ResponseCode.Internal.INTERNAL_ERROR));
    }
}
