package com.guwave.datahub.bpms.app.configuration.clickhouse;

import com.github.housepower.jdbc.BalancedClickhouseDataSource;
import com.github.housepower.settings.SettingKey;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * Copyright (C), 2025, guwave
 * <p>
 * JpaClickhouseConfiguration - 配置ClickHouse JPA连接
 *
 * <AUTHOR>
 * @version 0.0.1
 */
@Slf4j
@Component
class JpaClickhouseConfiguration {

    static String clickhouseAddress;

    static String clickhouseUsername;

    static String clickhousePassword;

    static int maximumPoolSize;

    static int minimumIdle;

    static long connectionTimeout;


    @Value("${spring.datasource.clickhouse.maximum-pool-size}")
    private void setMaximumPoolSize(int value) {
        maximumPoolSize = value;
    }

    @Value("${spring.datasource.clickhouse.minimum-idle}")
    private void setMinimumIdle(int value) {
        minimumIdle = value;
    }

    @Value("${spring.datasource.clickhouse.idle-timeout}")
    private void setConnectionTimeout(long value) {
        connectionTimeout = value;
    }

    @Value("${spring.data.clickhouse.address}")
    public void setClickhouseAddress(String address) {
        clickhouseAddress = address;
    }

    @Value("${spring.data.clickhouse.username}")
    public void setClickhouseUsername(String username) {
        clickhouseUsername = username;
    }

    @Value("${spring.data.clickhouse.password}")
    public void setClickhousePassword(String password) {
        clickhousePassword = password;
    }


    public static DataSource getClickHouseDataSource(String address) {
        Map<SettingKey, Serializable> properties = new HashMap<>();
        properties.put(SettingKey.user, clickhouseUsername);
        properties.put(SettingKey.password, clickhousePassword);
        return new BalancedClickhouseDataSource(address, properties);
    }

    public DataSource getDataSource(String database) {
        HikariConfig config = new HikariConfig();
        config.setDataSource(getClickHouseDataSource(clickhouseAddress + "/" + database));
        config.setMaximumPoolSize(maximumPoolSize);
        config.setMinimumIdle(minimumIdle);
        config.setConnectionTimeout(connectionTimeout);
        config.setPoolName("HikariPool-Clickhouse-" + clickhouseAddress);
        // 连接测试配置
        config.setConnectionTestQuery("SELECT 1");
        config.setValidationTimeout(30000);
        config.setKeepaliveTime(60000);
        HikariDataSource dataSource = new HikariDataSource(config);
        log.info("初始化ck连接 address: {}, maximumPoolSize: {}, minimumIdle: {}, connectionTimeout: {}",
                clickhouseAddress, maximumPoolSize, minimumIdle, connectionTimeout);

        return dataSource;
    }

    public LocalContainerEntityManagerFactoryBean getEntityManagerFactory(String database, String persistenceUnitName, String packagePath) {
        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(getDataSource(database));
        em.setPackagesToScan(packagePath);

        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        vendorAdapter.setDatabasePlatform("com.guwave.datahub.bpms.app.configuration.ClickHouseDialect");
        em.setJpaVendorAdapter(vendorAdapter);

        Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.hbm2ddl.auto", "none"); // 改为none，不进行DDL验证
        properties.put("hibernate.dialect", "com.guwave.datahub.bpms.app.configuration.ClickHouseDialect");
        properties.put("hibernate.show_sql", "false");
        properties.put("hibernate.format_sql", "true");
        em.setJpaPropertyMap(properties);
        em.setPersistenceUnitName(persistenceUnitName);

        return em;
    }
}
