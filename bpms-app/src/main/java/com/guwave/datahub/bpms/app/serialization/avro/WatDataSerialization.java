package com.guwave.datahub.bpms.app.serialization.avro;

import com.guwave.onedata.dataware.common.model.wat.WatData;
import org.apache.avro.Schema;
import org.apache.avro.generic.GenericData;
import org.apache.avro.generic.GenericDatumReader;
import org.apache.avro.generic.GenericDatumWriter;
import org.apache.avro.generic.GenericRecord;
import org.apache.avro.io.BinaryDecoder;
import org.apache.avro.io.BinaryEncoder;
import org.apache.avro.io.DecoderFactory;
import org.apache.avro.io.EncoderFactory;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * WatDataSerialization
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2022-05-19 07:57:22
 */
public class WatDataSerialization {

    private static final String F_CUSTOMER = "f_customer";
    private static final String F_SUB_CUSTOMER = "f_sub_customer";
    private static final String F_FACTORY = "f_factory";
    private static final String F_FACTORY_SITE = "f_factory_site";
    private static final String F_FAB = "f_fab";
    private static final String F_FAB_SITE = "f_fab_site";
    private static final String F_DW_LAYER = "f_dw_layer";
    private static final String F_TS = "f_ts";
    private static final String F_TYPE = "f_type";
    private static final String F_JAVA_TYPE = "f_java_type";
    private static final String F_BODY = "f_body";

    private static final String SCHEMA_STR = "{\"type\":\"record\",\"name\":\"WatData\",\"fields\":[{\"name\":\"f_customer\",\"type\":\"string\"},{\"name\":\"f_sub_customer\",\"type\":\"string\"},{\"name\":\"f_factory\",\"type\":\"string\"},{\"name\":\"f_factory_site\",\"type\":\"string\"},{\"name\":\"f_fab\",\"type\":\"string\"},{\"name\":\"f_fab_site\",\"type\":\"string\"},{\"name\":\"f_dw_layer\",\"type\":\"string\"},{\"name\":\"f_ts\",\"type\":\"long\"},{\"name\":\"f_type\",\"type\":\"string\"},{\"name\":\"f_java_type\",\"type\":\"string\"},{\"name\":\"f_body\",\"type\":\"bytes\"}]}";
    private static final Schema SCHEMA;

    public static byte[] serialize(WatData msg) throws IOException {
        byte[] bytes = null;
        if (null != msg) {
            try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
                BinaryEncoder encoder = EncoderFactory.get().binaryEncoder(out, null);
                GenericDatumWriter<GenericRecord> writer = new GenericDatumWriter<>(SCHEMA);
                GenericRecord payload = new GenericData.Record(SCHEMA);

                payload.put(F_CUSTOMER, msg.getCustomer());
                payload.put(F_SUB_CUSTOMER, msg.getSubCustomer());
                payload.put(F_FACTORY, msg.getFactory());
                payload.put(F_FACTORY_SITE, msg.getFactorySite());
                payload.put(F_FAB, msg.getFab());
                payload.put(F_FAB_SITE, msg.getFabSite());
                payload.put(F_DW_LAYER, msg.getDwLayer());
                payload.put(F_TS, msg.getTs());
                payload.put(F_TYPE, msg.getType());
                payload.put(F_JAVA_TYPE, msg.getJavaType());
                payload.put(F_BODY, msg.getBody());

                writer.write(payload, encoder);
                encoder.flush();
                out.flush();
                bytes = out.toByteArray();
            }
        }
        return bytes;
    }

    static {
        SCHEMA = new Schema.Parser().parse(SCHEMA_STR);
    }
}
