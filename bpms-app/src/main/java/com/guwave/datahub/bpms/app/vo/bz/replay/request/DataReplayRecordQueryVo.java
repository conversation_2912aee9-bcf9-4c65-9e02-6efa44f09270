package com.guwave.datahub.bpms.app.vo.bz.replay.request;

import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import com.guwave.onedata.dataware.common.contant.CleanUpType;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.LotType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Schema(title = "数据重播记录查询请求体")
@Data
@EqualsAndHashCode(callSuper=false)
public class DataReplayRecordQueryVo extends PageableRequestVo {
    @Schema(title = "testArea", description = "CP,FT,WAT", example = "CP")
    private String testArea;

    @Schema(title = "factory列表", example = "[\"VC\"]")
    private List<String> factoryList;

    @Schema(title = "deviceId列表", example = "[\"SS521BB-DEFAULT\"]")
    private List<String> deviceIdList;

    @Schema(title = "fileCategory", description = "只能单选，取值为：STDF,RAW_DATA,WAT", example = "STDF")
    private FileCategory fileCategory;

    @Schema(title = "lotId列表", example = "[\"NT41A\"]")
    private List<String> lotIdList;

    @Schema(title = "waferNo列表", example = "[\"8\"]")
    private List<String> waferNoList;

    @Schema(title = "sblotId列表", example = "[\"8\"]")
    private List<String> sblotIdList;

    @Schema(title = "lotType", description = "PRODUCTION,ENGINEERING", example = "PRODUCTION")
    private String lotType;

    @Schema(title = "testStage列表", example = "[\"CP1\", \"CP2\", \"CP3\"]")
    private List<String> testStageList;

    @Schema(title = "testProgram列表", example = "[\"PROGRAM1\", \"PROGRAM2\"]")
    private List<String> testProgramList;

    @Schema(title = "fileName", description = "文件名", example = "CP1_RT_2022-10-10.stdf")
    private String fileName;

    @Schema(title = "重播类型", example = "[\"REPROCESS_FILE\"]")
    private List<CleanUpType> replayTypeList;

    @Schema(title = "重播步骤", example = "[\"4100\"]")
    private List<Integer> stepList;

    @Schema(title = "重播入库状态列表", example = "[\"FAIL\"]")
    private List<String> filterProcessStatusList;

    @Schema(title = "异常信息列表", example = "[]")
    private List<String> exceptionMessageList;

    @Schema(title = "入库状态列表", example = "[\"FAIL\"]")
    private List<String> processStatusList;

    @Schema(title = "filterCreateTime", description = "filterCreateTime", example = "[\"2023-09-20 08:14:13\", \"2023-09-21 08:14:13\"]")
    private List<String> filterCreateTime;

    @Schema(title = "filterUpdateTime", description = "filterUpdateTime", example = "[\"2023-09-20 08:14:13\", \"2023-09-21 08:14:13\"]")
    private List<String> filterUpdateTime;

    @Schema(title = "createTime", description = "createTime", example = "[\"2023-09-20 08:14:13\", \"2023-09-21 08:14:13\"]")
    private List<String> createTime;

    @Schema(title = "updateTime", description = "updateTime", example = "[\"2023-09-20 08:14:13\", \"2023-09-21 08:14:13\"]")
    private List<String> updateTime;

}
