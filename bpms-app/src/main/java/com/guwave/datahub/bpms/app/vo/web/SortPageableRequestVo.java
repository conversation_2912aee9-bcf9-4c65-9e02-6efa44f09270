package com.guwave.datahub.bpms.app.vo.web;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.domain.Sort;

import javax.validation.constraints.NotNull;

@Schema(title = "排序分页请求信息")
@Data
@EqualsAndHashCode(callSuper=false)
public class SortPageableRequestVo extends PageableRequestVo {

    public static final String DEFAULT_SORT_DIRECTION = "DESC";
    public static final String DEFAULT_SORT_FIELD = "update_time";

    @Schema(title = "排序规则", description = "ASC/DESC", example = "DESC", defaultValue = "DESC")
    private String sortDirection = DEFAULT_SORT_DIRECTION;


    @Schema(title = "排序字段", example = "update_time", defaultValue = "update_time")
    @NotNull
    private String sortField = DEFAULT_SORT_FIELD;

    public Sort toSort() {
        return Sort.by(Sort.Direction.fromString(sortDirection.toUpperCase()), sortField);
    }
}
