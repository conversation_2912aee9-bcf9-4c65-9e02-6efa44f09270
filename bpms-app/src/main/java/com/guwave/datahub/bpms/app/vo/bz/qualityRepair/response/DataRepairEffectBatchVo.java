package com.guwave.datahub.bpms.app.vo.bz.qualityRepair.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.guwave.datahub.bpms.dao.domain.repair.DataRepairEffectBatch;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class DataRepairEffectBatchVo {

    @Schema(title = "id", example = "123")
    private Long id;

    @Schema(title = "customer", example = "guwave")
    private String customer;

    @Schema(title = "subCustomer", example = "guwave")
    private String subCustomer;

    @Schema(title = "处理批次",  example = "123")
    private Long repairBatchId;

    @Schema(title = "deviceId", example = "SS521BB-DEFAULT")
    private String deviceId;

    @Schema(title = "lotId",  example = "NT41A")
    private String lotId;

    @Schema(title = "testStage",  example = "CP1")
    private String testStage;

    @Schema(title = "testProgram")
    private String testProgram;

    @Schema(title = "testArea")
    private String testArea;

    @Schema(title = "sblotId")
    private String sblotId;

    @Schema(title = "waferId")
    private String waferId;

    @Schema(title = "repairNo")
    private Integer repairNo;


    @Schema(title = "修改前文件个数")
    private Long fileCountBefore;


    @Schema(title = "修改后文件个数")
    private Long fileCountAfter;


    @Schema(title = "修改前文件名")
    private String fileListBefore;


    @Schema(title = "修改后文件名")
    private String fileListAfter;


    @Schema(title = "变更文件名")
    private String repairFiles;



    private String repairInfoIds;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;


    private String createUser;


    private String updateUser;


    public static DataRepairEffectBatchVo of(DataRepairEffectBatch dto) {
        if (dto == null) {
            return null;
        }
        DataRepairEffectBatchVo vo = new DataRepairEffectBatchVo();
        vo.setId(dto.getId());
        vo.setCustomer(dto.getCustomer());
        vo.setSubCustomer(dto.getSubCustomer());
        vo.setRepairBatchId(dto.getRepairBatchId());
        vo.setDeviceId(dto.getDeviceId());
        vo.setLotId(dto.getLotId());
        vo.setTestStage(dto.getTestStage());
        vo.setTestProgram(dto.getTestProgram());
        vo.setTestArea(dto.getTestArea());
        vo.setSblotId(dto.getSblotId());
        vo.setWaferId(dto.getWaferId());
        vo.setRepairNo(dto.getRepairNo());
        vo.setFileCountBefore(dto.getFileCountBefore());
        vo.setFileCountAfter(dto.getFileCountAfter());
        vo.setFileListBefore(dto.getFileListBefore());
        vo.setFileListAfter(dto.getFileListAfter());
        vo.setRepairFiles(dto.getRepairFiles());
        vo.setRepairInfoIds(dto.getRepairInfoIds());
        vo.setCreateTime(dto.getCreateTime());
        vo.setUpdateTime(dto.getUpdateTime());
        vo.setCreateUser(dto.getCreateUser());
        vo.setUpdateUser(dto.getUpdateUser());
        return vo;
    }
}