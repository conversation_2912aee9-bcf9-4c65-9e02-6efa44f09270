package com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.request;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(title = "数据完整性备注")
public class DataIntegrityRemarkVo {


    @Schema(description = "文件名列表", example = "[\"a.stdf.zip\",\"b.stdf.zip\"]")
    private List<String> fileNames;

    @Schema(description = "备注", example = "123")
    private String remark;
}
