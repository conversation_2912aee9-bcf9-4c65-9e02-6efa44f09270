package com.guwave.datahub.bpms.app.service.bz.handler.clearRulePreview.preHandler;

import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model.ConfResult;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model.ParseField;
import com.guwave.datahub.bpms.dao.domain.clearRulePreview.ClearRulePreviewRecord;
import com.guwave.onedata.dataware.common.contant.Constant;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public abstract class FtPreHandler extends PreHandler {

    @Value("${spring.handler.ft.keyFields}")
    private String keyFields;

    @Override
    protected String getKeyFields() {
        return keyFields;
    }

    @Override
    public void beforeModifyField(ClearRulePreviewRecord record, Map<ParseField, ConfResult> confResultMap) {

    }

    @Override
    public void afterModifyField(ClearRulePreviewRecord record, Map<ParseField, ConfResult> confResultMap) {
        // wafer相关置空
        record.setWaferNo(Constant.EMPTY);
        record.setWaferId(Constant.EMPTY);
        record.setOriginWaferId(Constant.EMPTY);
        record.setRealWaferId(Constant.EMPTY);
        record.setPosX(Constant.EMPTY);
        record.setPosY(Constant.EMPTY);
        record.setNotch(Constant.EMPTY);
    }
}
