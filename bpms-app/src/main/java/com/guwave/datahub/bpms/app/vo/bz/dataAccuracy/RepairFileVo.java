package com.guwave.datahub.bpms.app.vo.bz.dataAccuracy;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 2025/5/13 16:51
 * RepairFileVo
 *
 * <AUTHOR>
 */
@Data
public class RepairFileVo {

    // 不支持修复的字段
    private String customer;
    private String subCustomer;
    private String factory;
    private String factorySite;
    private String testArea;
    private String fileCategory;

    // 文件
    private Long fileId;
    private String fileName;

    //支持修复的字段

    private String originalDeviceId;
    private String modifyDeviceId;

    private String originalTestStage;
    private String modifyTestStage;

    private String originalLotId;
    private String modifyLotId;

    private String originalWaferNo;
    private String modifyWaferNo;

    private String originalSblotId;
    private String modifySblotId;

    private String originalTestProgram;
    private String modifyTestProgram;

    private String originalLotType;
    private String modifyLotType;

    private String originalWaferId;
    private String modifyWaferId;

    private String originalRetestHbinNumber;
    private String modifyRetestHbinNumber;

    private String originalFlowId;
    private String modifyFlowId;

    private LocalDateTime originalStartTime;
    private LocalDateTime modifyStartTime;

    private LocalDateTime originalEndTime;
    private LocalDateTime modifyEndTime;

    private boolean currentRepairFlag = false;

    public String getCustomer() {
        return customer;
    }

    public RepairFileVo setCustomer(String customer) {
        this.customer = customer;
        return this;
    }

    public String getSubCustomer() {
        return subCustomer;
    }

    public RepairFileVo setSubCustomer(String subCustomer) {
        this.subCustomer = subCustomer;
        return this;
    }

    public String getFactory() {
        return factory;
    }

    public RepairFileVo setFactory(String factory) {
        this.factory = factory;
        return this;
    }

    public String getFactorySite() {
        return factorySite;
    }

    public RepairFileVo setFactorySite(String factorySite) {
        this.factorySite = factorySite;
        return this;
    }

    public String getTestArea() {
        return testArea;
    }

    public RepairFileVo setTestArea(String testArea) {
        this.testArea = testArea;
        return this;
    }

    public String getFileCategory() {
        return fileCategory;
    }

    public RepairFileVo setFileCategory(String fileCategory) {
        this.fileCategory = fileCategory;
        return this;
    }

    public Long getFileId() {
        return fileId;
    }

    public RepairFileVo setFileId(Long fileId) {
        this.fileId = fileId;
        return this;
    }

    public String getFileName() {
        return fileName;
    }

    public RepairFileVo setFileName(String fileName) {
        this.fileName = fileName;
        return this;
    }

    public String getOriginalDeviceId() {
        return originalDeviceId;
    }

    public RepairFileVo setOriginalDeviceId(String originalDeviceId) {
        this.originalDeviceId = originalDeviceId;
        return this;
    }

    public String getModifyDeviceId() {
        return modifyDeviceId;
    }

    public RepairFileVo setModifyDeviceId(String modifyDeviceId) {
        this.modifyDeviceId = modifyDeviceId;
        return this;
    }

    public String getOriginalTestStage() {
        return originalTestStage;
    }

    public RepairFileVo setOriginalTestStage(String originalTestStage) {
        this.originalTestStage = originalTestStage;
        return this;
    }

    public String getModifyTestStage() {
        return modifyTestStage;
    }

    public RepairFileVo setModifyTestStage(String modifyTestStage) {
        this.modifyTestStage = modifyTestStage;
        return this;
    }

    public String getOriginalLotId() {
        return originalLotId;
    }

    public RepairFileVo setOriginalLotId(String originalLotId) {
        this.originalLotId = originalLotId;
        return this;
    }

    public String getModifyLotId() {
        return modifyLotId;
    }

    public RepairFileVo setModifyLotId(String modifyLotId) {
        this.modifyLotId = modifyLotId;
        return this;
    }

    public String getOriginalWaferNo() {
        return originalWaferNo;
    }

    public RepairFileVo setOriginalWaferNo(String originalWaferNo) {
        this.originalWaferNo = originalWaferNo;
        return this;
    }

    public String getModifyWaferNo() {
        return modifyWaferNo;
    }

    public RepairFileVo setModifyWaferNo(String modifyWaferNo) {
        this.modifyWaferNo = modifyWaferNo;
        return this;
    }

    public String getOriginalSblotId() {
        return originalSblotId;
    }

    public RepairFileVo setOriginalSblotId(String originalSblotId) {
        this.originalSblotId = originalSblotId;
        return this;
    }

    public String getModifySblotId() {
        return modifySblotId;
    }

    public RepairFileVo setModifySblotId(String modifySblotId) {
        this.modifySblotId = modifySblotId;
        return this;
    }

    public String getOriginalTestProgram() {
        return originalTestProgram;
    }

    public RepairFileVo setOriginalTestProgram(String originalTestProgram) {
        this.originalTestProgram = originalTestProgram;
        return this;
    }

    public String getModifyTestProgram() {
        return modifyTestProgram;
    }

    public RepairFileVo setModifyTestProgram(String modifyTestProgram) {
        this.modifyTestProgram = modifyTestProgram;
        return this;
    }

    public String getOriginalLotType() {
        return originalLotType;
    }

    public RepairFileVo setOriginalLotType(String originalLotType) {
        this.originalLotType = originalLotType;
        return this;
    }

    public String getModifyLotType() {
        return modifyLotType;
    }

    public RepairFileVo setModifyLotType(String modifyLotType) {
        this.modifyLotType = modifyLotType;
        return this;
    }

    public String getOriginalRetestHbinNumber() {
        return originalRetestHbinNumber;
    }

    public RepairFileVo setOriginalRetestHbinNumber(String originalRetestHbinNumber) {
        this.originalRetestHbinNumber = originalRetestHbinNumber;
        return this;
    }

    public String getModifyRetestHbinNumber() {
        return modifyRetestHbinNumber;
    }

    public RepairFileVo setModifyRetestHbinNumber(String modifyRetestHbinNumber) {
        this.modifyRetestHbinNumber = modifyRetestHbinNumber;
        return this;
    }

    public String getOriginalFlowId() {
        return originalFlowId;
    }

    public RepairFileVo setOriginalFlowId(String originalFlowId) {
        this.originalFlowId = originalFlowId;
        return this;
    }

    public String getModifyFlowId() {
        return modifyFlowId;
    }

    public RepairFileVo setModifyFlowId(String modifyFlowId) {
        this.modifyFlowId = modifyFlowId;
        return this;
    }

    public String getOriginalWaferId() {
        return originalWaferId;
    }

    public RepairFileVo setOriginalWaferId(String originalWaferId) {
        this.originalWaferId = originalWaferId;
        return this;
    }

    public String getModifyWaferId() {
        return modifyWaferId;
    }

    public RepairFileVo setModifyWaferId(String modifyWaferId) {
        this.modifyWaferId = modifyWaferId;
        return this;
    }

    public LocalDateTime getOriginalStartTime() {
        return originalStartTime;
    }

    public RepairFileVo setOriginalStartTime(LocalDateTime originalStartTime) {
        this.originalStartTime = originalStartTime;
        return this;
    }

    public LocalDateTime getModifyStartTime() {
        return modifyStartTime;
    }

    public RepairFileVo setModifyStartTime(LocalDateTime modifyStartTime) {
        this.modifyStartTime = modifyStartTime;
        return this;
    }

    public LocalDateTime getOriginalEndTime() {
        return originalEndTime;
    }

    public RepairFileVo setOriginalEndTime(LocalDateTime originalEndTime) {
        this.originalEndTime = originalEndTime;
        return this;
    }

    public LocalDateTime getModifyEndTime() {
        return modifyEndTime;
    }

    public RepairFileVo setModifyEndTime(LocalDateTime modifyEndTime) {
        this.modifyEndTime = modifyEndTime;
        return this;
    }

    public boolean isCurrentRepairFlag() {
        return currentRepairFlag;
    }

    public RepairFileVo setCurrentRepairFlag(boolean currentRepairFlag) {
        this.currentRepairFlag = currentRepairFlag;
        return this;
    }
}
