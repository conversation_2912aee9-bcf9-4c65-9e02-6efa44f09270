package com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.guwave.datahub.bpms.common.constant.Constant;
import com.guwave.datahub.bpms.dao.clickhouse.domain.dws.DwsFlowidBinIndex;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_FORMAT;
import static com.guwave.onedata.dataware.common.contant.Constant.NORMAL_OFFLINE_PREFIX;


/**
 * 2025/4/27 17:21
 * FileRepairRecordVo
 *
 * <AUTHOR>
 */
@Schema(title = "数据修复文件")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileRepairRecordVo {

    @Schema(title = "ck表中数据的subCustomer，用于大数据消息中")
    private String dwSubCustomer;

    @Schema(title = "Device Id")
    private String deviceId;

    @Schema(title = "factory")
    private String factory;

    @Schema(title = "factorySite")
    private String factorySite;

    @Schema(title = "Test Area")
    private String testArea;

    @Schema(title = "Test Stage")
    private String testStage;

    @Schema(title = "Lot Id")
    private String lotId;

    @Schema(title = "Wafer Id")
    private String waferId;

    @Schema(title = "Wafer No")
    private String waferNo;

    @Schema(title = "Sblot Id")
    private String sblotId;

    @Schema(title = "Test Program")
    private String testProgram;

    @Schema(title = "Lot Type")
    private String lotType;

    @Schema(title = "File Id")
    private Long fileId;

    @Schema(title = "File Name")
    private String fileName;

    @Schema(title = "File Category")
    private String fileCategory;

    @Schema(title = "Flow Id")
    private String flowId;

    @Schema(title = "Retest HBIN Number")
    private String retestHardBin;

    @Schema(title = "Start Time")
    private String startTime;

    @Schema(title = "End Time")
    private String endTime;

    @Schema(title = "Input Count")
    private Long inputCount;

    @Schema(title = "Pass Count")
    private Long passCount;

    @Schema(title = "File Count")
    private Long failCount;

    @Schema(title = "是否修复过")
    private Boolean isRepaired;

    @Schema(title = "修复时间")
    private Integer repairTimes;

    @Schema(title = "是否为修复文件")
    private Boolean repairFile;

    private static FileRepairRecordVo of(DwsFlowidBinIndex dwsFlowidBinIndex, Integer repairTimes, Boolean isRepaired, Boolean repairFile) {
        return FileRepairRecordVo.builder()
                .dwSubCustomer(dwsFlowidBinIndex.getSubCustomer())
                .deviceId(dwsFlowidBinIndex.getDeviceId())
                .factory(dwsFlowidBinIndex.getFactory())
                .factorySite(dwsFlowidBinIndex.getFactorySite())
                .testArea(dwsFlowidBinIndex.getTestArea())
                .testStage(dwsFlowidBinIndex.getTestStage())
                .lotId(dwsFlowidBinIndex.getLotId())
                .waferId(dwsFlowidBinIndex.getWaferId())
                .waferNo(dwsFlowidBinIndex.getWaferNo())
                .sblotId(dwsFlowidBinIndex.getSblotId())
                .testProgram(dwsFlowidBinIndex.getTestProgram())
                .lotType(dwsFlowidBinIndex.getLotType())
                .fileId(dwsFlowidBinIndex.getFileId())
                .fileName(dwsFlowidBinIndex.getFileName())
                .fileCategory(dwsFlowidBinIndex.getFileType())
                .flowId(dwsFlowidBinIndex.getFlowId())
                .retestHardBin(getRetestHardBinNumShowName(dwsFlowidBinIndex.getRetestBinNum(), dwsFlowidBinIndex.getFlowId()))
                .startTime(LocalDateTimeUtil.format(dwsFlowidBinIndex.getStartTime(), NORM_DATETIME_FORMAT.getDateTimeFormatter()))
                .endTime(LocalDateTimeUtil.format(dwsFlowidBinIndex.getEndTime(), NORM_DATETIME_FORMAT.getDateTimeFormatter()))
                .inputCount(dwsFlowidBinIndex.getInputCnt())
                .passCount(dwsFlowidBinIndex.getPassCnt())
                .failCount(dwsFlowidBinIndex.getFailCnt())
                .isRepaired(isRepaired)
                .repairTimes(repairTimes)
                .repairFile(repairFile)
                .build();
    }

    public static FileRepairRecordVo of(DwsFlowidBinIndex dwsFlowidBinIndex, Boolean repairFile) {
        return FileRepairRecordVo.of(dwsFlowidBinIndex, null, null, repairFile);
    }

    public static FileRepairRecordVo of(DwsFlowidBinIndex dwsFlowidBinIndex, Integer repairTimes, Boolean isRepaired) {
        return FileRepairRecordVo.of(dwsFlowidBinIndex, repairTimes, isRepaired, null);
    }

    public static String getRetestHardBinNumShowName(String retestHardBinNum, String flowId) {
        if (StringUtils.isBlank(flowId)) {
            return retestHardBinNum;
        }
        if (flowId.startsWith(NORMAL_OFFLINE_PREFIX)) {
            return Constant.NA;
        }
        return retestHardBinNum;
    }
}
