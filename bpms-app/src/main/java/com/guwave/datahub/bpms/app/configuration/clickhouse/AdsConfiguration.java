//package com.guwave.datahub.bpms.app.configuration.clickhouse;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.ArrayUtils;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.SqlSessionFactoryBean;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//import org.springframework.orm.jpa.JpaTransactionManager;
//import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
//import org.springframework.transaction.PlatformTransactionManager;
//import org.springframework.transaction.annotation.EnableTransactionManagement;
//
//import javax.persistence.EntityManager;
//import javax.sql.DataSource;
//import java.util.Objects;
//
//
//@Slf4j
//@Configuration
//@EnableTransactionManagement
//@EnableJpaRepositories(entityManagerFactoryRef = "entityManagerFactoryAds",
//        transactionManagerRef = "transactionManagerAds",
//        basePackages = {"com.guwave.datahub.bpms.dao.repository.clickhouse.ads"})
//@MapperScan(
//        basePackages = "com.guwave.datahub.bpms.dao.repository.clickhouse.ads.mapper",
//        sqlSessionFactoryRef = "adsSqlSessionFactory"
//)
//public class AdsConfiguration {
//
//    @Autowired
//    JpaClickhouseConfiguration jpaClickhouseConfiguration;
//
//    @Value("${spring.ck.ads.database}")
//    private String database;
//
//    @Bean(name = "entityManagerAds")
//    public EntityManager entityManager() {
//        return Objects.requireNonNull(entityManagerFactoryAds().getObject()).createEntityManager();
//    }
//
//    @Bean(name = "entityManagerFactoryAds")
//    public LocalContainerEntityManagerFactoryBean entityManagerFactoryAds() {
//        return jpaClickhouseConfiguration.getEntityManagerFactory(
//                database,
//                "adsPersistenceUnit",
//                "com.guwave.datahub.bpms.dao.clickhouse.domain.ads");
//    }
//
//    @Bean(name = "transactionManagerAds")
//    public PlatformTransactionManager transactionManagerAds() {
//        return new JpaTransactionManager(Objects.requireNonNull(entityManagerFactoryAds().getObject()));
//    }
//
//
//    @Bean
//    public DataSource adsDataSource() {
//        return entityManagerFactoryAds().getDataSource();
//    }
//
//
//    // 配置MyBatis
//    @Bean(name = "adsSqlSessionFactory")
//    public SqlSessionFactory AdsSqlSessionFactory() throws Exception {
//        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
//        sessionFactory.setDataSource(adsDataSource());
//        sessionFactory.setMapperLocations(
//                ArrayUtils.addAll(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/clickhouse/ads/*.xml"),
//                        new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml"))
//        );
//        return sessionFactory.getObject();
//
//    }
//
//    @Bean(name = "adsMybatisTransactionManager")
//    public PlatformTransactionManager AdsMybatisTransactionManager() {
//        return new DataSourceTransactionManager(adsDataSource());
//    }
//}
