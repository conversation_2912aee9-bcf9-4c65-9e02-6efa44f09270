package com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response;

import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.RepairFileVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.format.DateTimeFormatter;

/**
 * 2025/5/13 18:38
 * RepairEffectBatchFileVo
 *
 * <AUTHOR>
 */
@Schema(title = "数据修复影响的文件列表")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RepairEffectBatchFileVo {

    @Schema(title = "customer")
    private String customer;

    @Schema(title = "subCustomer")
    private String subCustomer;

    @Schema(title = "factory")
    private String factory;

    @Schema(title = "factorySite")
    private String factorySite;

    @Schema(title = "testArea")
    private String testArea;

    @Schema(title = "File Category")
    private String fileCategory;

    @Schema(title = "File Id")
    private Long fileId;

    @Schema(title = "File Name")
    private String fileName;

    @Schema(title = "Device Id")
    private String deviceId;

    @Schema(title = "Test Stage")
    private String testStage;

    @Schema(title = "Lot Id")
    private String lotId;

    @Schema(title = "Sblot Id")
    private String sblotId;

    @Schema(title = "Wafer No")
    private String waferNo;

    @Schema(title = "Wafer Id")
    private String waferId;

    @Schema(title = "Test Program")
    private String testProgram;

    @Schema(title = "Lot Type")
    private String lotType;

    @Schema(title = "Flow Id")
    private String flowId;

    @Schema(title = "Retest HBIN Number")
    private String retestHardBin;

    @Schema(title = "Start Time")
    private String startTime;
    @Schema(title = "End Time")
    private String endTime;

    @Schema(title = "是否为修复文件")
    private Boolean repairFile;

    public static RepairEffectBatchFileVo of(RepairFileVo repairFileVo) {
        return RepairEffectBatchFileVo.builder()
                .customer(repairFileVo.getCustomer())
                .subCustomer(repairFileVo.getSubCustomer())
                .factory(repairFileVo.getFactory())
                .factorySite(repairFileVo.getFactorySite())
                .testArea(repairFileVo.getTestArea())
                .fileCategory(repairFileVo.getFileCategory())
                .fileId(repairFileVo.getFileId())
                .fileName(repairFileVo.getFileName())
                .deviceId(repairFileVo.getModifyDeviceId())
                .testStage(repairFileVo.getModifyTestStage())
                .lotId(repairFileVo.getModifyLotId())
                .sblotId(repairFileVo.getModifySblotId())
                .waferNo(repairFileVo.getModifyWaferNo())
                .waferId(repairFileVo.getModifyWaferId())
                .testProgram(repairFileVo.getModifyTestProgram())
                .lotType(repairFileVo.getModifyLotType())
                .flowId(repairFileVo.getModifyFlowId())
                .retestHardBin(repairFileVo.getModifyRetestHbinNumber())
                .startTime(repairFileVo.getModifyStartTime() == null ? null : repairFileVo.getModifyStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .endTime(repairFileVo.getModifyEndTime() == null ? null : repairFileVo.getModifyEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .repairFile(repairFileVo.isCurrentRepairFlag())
                .build();
    }

}
