package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.request;

import com.guwave.datahub.bpms.common.constant.WarehousingConfigurePageDropdownField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotNull;

@Schema(title = "下拉框过滤条件请求体")
@Data
public class WarehousingRuleDropDownFilterVo {

    @Schema(title = "当前的过滤字段", description = "当前的过滤字段，不能为空，取值为：SUB_CUSTOMER,FACTORY,TEST_AREA, FILE_CATEGORY", example = "SUB_CUSTOMER")
    @NotNull(message = "过滤字段不能为空")
    private WarehousingConfigurePageDropdownField filterField;

}
