package com.guwave.datahub.bpms.app.job;

import com.guwave.datahub.bpms.app.ck.CkProvider;
import com.guwave.datahub.bpms.app.service.bz.DataReplayService;
import com.guwave.datahub.bpms.dao.domain.replay.DataReplayRecord;
import com.guwave.datahub.bpms.dao.repository.bpms.DataReplayRecordRepository;
import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import com.guwave.onedata.dataware.common.contant.CleanUpType;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 2023/11/29 17:19
 * DataReplayJob
 *
 * <AUTHOR>
 */
@Service
public class DataReplayJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(DataReplayJob.class);

    @Autowired
    private DataReplayRecordRepository dataReplayRecordRepository;
    @Autowired
    private DataReplayService dataReplayService;
    @Autowired
    private CkProvider ckProvider;

    @ScheduleSwitch
    @Scheduled(fixedDelayString = "${spring.scheduler.polling.milliseconds}")
    public void run() {
        List<DataReplayRecord> dataReplayRecordList = dataReplayRecordRepository.findAllByProcessStatusInOrderByToppedTimeDescCreateTimeAsc(Arrays.asList(ProcessStatus.CREATE, ProcessStatus.PROCESSING));
        if (CollectionUtils.isEmpty(dataReplayRecordList)) {
            LOGGER.info("没有待处理的重播任务");
            return;
        }

        final AtomicReference<DataReplayRecord> earliestDataReplayRecord = new AtomicReference<>(null);
        Map<String, List<DataReplayRecord>> customerDataReplayRecordList = dataReplayRecordList.stream().collect(Collectors.groupingBy(DataReplayRecord::getCustomer, Collectors.toList()));
        customerDataReplayRecordList.forEach((customer, value) -> {
            if (value.stream().anyMatch(elem -> elem.getProcessStatus() == ProcessStatus.PROCESSING)) {
                LOGGER.info("{} 有未完成的重播任务,暂不处理新的重播任务...", customer);
                dataReplayRecordRepository.findAllByCustomerAndProcessStatusAndUpdateTimeBefore(customer, ProcessStatus.PROCESSING, new Date(System.currentTimeMillis() - 60 * 1000))
                        .forEach(elem -> dataReplayService.syncReplayRecordProgress(elem));
            } else {
                Optional<DataReplayRecord> dataReplayRecordOptional = value.stream()
                        .filter(elem -> elem.getProcessStatus() == ProcessStatus.CREATE)
                        .min(Comparator.comparing(DataReplayRecord::getToppedTime, Comparator.nullsLast(Comparator.reverseOrder()))
                                .thenComparing(DataReplayRecord::getCreateTime));
                if (dataReplayRecordOptional.isPresent()) {
                    if (earliestDataReplayRecord.get() == null) {
                        earliestDataReplayRecord.set(dataReplayRecordOptional.get());
                    } else {
                        earliestDataReplayRecord.set(earliestDataReplayRecord.get().getCreateTime().before(dataReplayRecordOptional.get().getCreateTime()) ? earliestDataReplayRecord.get() : dataReplayRecordOptional.get());
                    }
                }
            }
        });

        // 判断mutations个数
        if (earliestDataReplayRecord.get() != null) {
            DataReplayRecord dataReplayRecord = earliestDataReplayRecord.get();
            if ((dataReplayRecord.getReplayType() == CleanUpType.DELETE_FILE || dataReplayRecord.getReplayType() == CleanUpType.DELETE_LOT) && !ckProvider.checkCkMutations() ) {
                LOGGER.info("当前ck mutations 太多, ");
                return ;
            }
        }


        if (earliestDataReplayRecord.get() != null) {
            DataReplayRecord dataReplayRecord = earliestDataReplayRecord.get();
            LOGGER.info("开始处理重播任务, customer:{}, id:{}", dataReplayRecord.getCustomer(), dataReplayRecord.getId());

            dataReplayRecord.setProcessStatus(ProcessStatus.PROCESSING);
            dataReplayRecord.setUpdateTime(new Date());
            dataReplayRecordRepository.save(dataReplayRecord);

            dataReplayService.createReplayTask(dataReplayRecord);
        }

    }

}
