package com.guwave.datahub.bpms.app.vo.bz.priority.model;

import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotMetaDataDetail;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ComputeDimensionKey {
    private String customer;
    private String subCustomer;
    private String factory;
    private String factorySite;
    private String deviceId;
    private TestArea testArea;
    private String lotId;
    private String waferNo;
    private String testStage;
    private LotType lotType;
    private FileCategory fileCategory;

    public static ComputeDimensionKey from(LotMetaDataDetail detail) {
        return ComputeDimensionKey.builder()
                .customer(detail.getCustomer())
                .subCustomer(detail.getSubCustomer())
                .factory(detail.getFactory())
                .factorySite(detail.getFactorySite())
                .deviceId(detail.getDeviceId())
                .testArea(detail.getTestArea())
                .lotId(detail.getLotId())
                .waferNo(detail.getWaferNo())
                .testStage(detail.getTestStage())
                .lotType(detail.getLotType())
                .fileCategory(detail.getFileCategory())
                .build();
    }
}
