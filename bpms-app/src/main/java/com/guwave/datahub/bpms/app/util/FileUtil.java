package com.guwave.datahub.bpms.app.util;

import com.guwave.datahub.bpms.app.decompress.CompressFileSuffixEnum;
import com.guwave.datahub.bpms.app.decompress.UnCompressHandler;
import com.guwave.datahub.bpms.app.decompress.impl.*;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.FileType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.zip.Zip64Mode;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.commons.io.FileUtils;
import org.apache.tika.Tika;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
public class FileUtil {

    private final static List<String> filterDirList = new ArrayList<String>() {{
        add(".");
        add("..");
    }};
    private final static FilenameFilter dirFilenameFilter = (dir, name) -> !filterDirList.contains(name);

    private final static Map<CompressFileSuffixEnum, UnCompressHandler> canUnCompressMap = new HashMap<CompressFileSuffixEnum, UnCompressHandler>() {{
        put(CompressFileSuffixEnum.ZIP, new ZipUncompressHandler());
        put(CompressFileSuffixEnum.GZ, new GzUncompressHandler());
        put(CompressFileSuffixEnum.Z, new ZUncompressHandler());
        put(CompressFileSuffixEnum.TAR, new TarUncompressHandler());
        put(CompressFileSuffixEnum.SEVENZ, new ServenZUncompressHandler());
        put(CompressFileSuffixEnum.RAR, new RarUncompressHandler());
        put(CompressFileSuffixEnum.RAR4, new RarUncompressHandler());
        put(CompressFileSuffixEnum.TGZ, new TarUncompressHandler());
    }};
    public final static Set<CompressFileSuffixEnum> canUnCompressSuffixSet = canUnCompressMap.keySet();


    public static void deCompressFile(List<File> deCompressFiles, File file, Set<CompressFileSuffixEnum> needDecompressTypesConfig) throws Exception {
        if (file.isDirectory()) {
            // 如果是文件夹
            File[] subFileList = file.listFiles(dirFilenameFilter);
            if (subFileList != null) {
                for (File subFile : subFileList) {
                    deCompressFile(deCompressFiles, subFile, needDecompressTypesConfig);
                }
            }
        } else if (file.isFile()) {
            // 是文件
            String fileName = file.getName();
            int suffixIndex = fileName.lastIndexOf(Constant.POINT);
            CompressFileSuffixEnum fileSuffix = CompressFileSuffixEnum.of(fileName.substring(suffixIndex + 1));

            Tika tika = new Tika();
            // 根据文件识别实际的压缩类型
            CompressFileSuffixEnum actlType = CompressFileSuffixEnum.ofType(tika.detect(file));

            if (fileSuffix != actlType) {
                log.warn("{}的实际压缩类型其实是{}", file.getName(), actlType);
            }
            if (suffixIndex > -1 && actlType != null && canUnCompressSuffixSet.contains(fileSuffix) && needDecompressTypesConfig.contains(fileSuffix)) {
                // 需要解压
                UnCompressHandler unCompressHandler = canUnCompressMap.get(actlType);
                File targetDir = new File(file.getParent(), fileName.substring(0, suffixIndex));
                if (!targetDir.exists()) {
                    targetDir.mkdirs();
                }
                unCompressHandler.unCompress(file, targetDir);
                File[] unCompressFileDir = targetDir.listFiles(dirFilenameFilter);
                if (unCompressFileDir != null) {
                    for (File unCompressFile : unCompressFileDir) {
                        deCompressFile(deCompressFiles, unCompressFile, needDecompressTypesConfig);
                    }
                }
            } else {
                // 不需要解压的文件
                deCompressFiles.add(file);
            }
        }
    }

    public static void compressFileToZip(String originalPath, String fileName, String zipPath) {
        if (!originalPath.endsWith(Constant.SLASH)) {
            originalPath = originalPath + Constant.SLASH;
        }
        if (!zipPath.endsWith(Constant.SLASH)) {
            zipPath = zipPath + Constant.SLASH;
        }

        String zipFileName = fileName + Constant.POINT + FileType.ZIP.getType();
        deleteFile(zipPath, zipFileName);
        String crcFileName = Constant.POINT + fileName + Constant.POINT + FileType.ZIP.getType() + Constant.POINT + Constant.CRC_SUFFIX;
        deleteFile(zipPath, crcFileName);

        File zipFile = new File(zipPath, zipFileName);
        if (!zipFile.getParentFile().exists()) {
            zipFile.getParentFile().mkdirs();
        }

        // 压缩
        compress(originalPath + fileName, zipPath + zipFileName);
        // 删除源文件
        deleteFile(originalPath, fileName);
    }

    public static void deleteFile(String path, String fileName) {
        String fileFullName = getFileFullName(path, fileName);
        File file = new File(fileFullName);
        deleteFile(file);
    }

    public static void deleteFile(File file) {
        try {
            if (file != null && file.exists()) {
                file.delete();
            }
        } catch (Exception e) {
            log.error(e.toString(), e);
        }
    }

    public static void deleteDir(File path) {
        FileUtils.deleteQuietly(path);
    }

    public static String getFileFullName(String filePath, String fileName) {
        String fileFullName = "";
        if (filePath.endsWith(Constant.SLASH)) {
            fileFullName = filePath + fileName;
        } else {
            fileFullName = filePath + Constant.SLASH + fileName;
        }
        return fileFullName;
    }

    /**
     * 压缩文件
     *
     * @param filePath    filePath
     * @param zipFilePath zipFilePath
     */
    public static void compress(String filePath, String zipFilePath) {
        ZipArchiveOutputStream zaos = null;
        File file = new File(filePath);
        try {
            File zipFile = new File(zipFilePath);
            zaos = new ZipArchiveOutputStream(zipFile);
            zaos.setUseZip64(Zip64Mode.AsNeeded);

            ZipArchiveEntry zipArchiveEntry = new ZipArchiveEntry(file, file.getName());
            zaos.putArchiveEntry(zipArchiveEntry);
            try (InputStream is = new BufferedInputStream(new FileInputStream(file))) {
                byte[] buffer = new byte[1024 * 5];
                int len;
                while ((len = is.read(buffer)) != -1) {
                    zaos.write(buffer, 0, len);
                }
                zaos.closeArchiveEntry();
            } catch (Exception e) {
                log.info("压缩失败", e);
            }
            zaos.finish();
        } catch (Exception e) {
            log.info("压缩失败", e);
        } finally {
            try {
                if (zaos != null) {
                    zaos.close();
                }
            } catch (IOException e) {
                log.info("压缩失败", e);
            }
        }
    }

    public static String removeFileSuffix(String fileName, FileType fileType) {
        String suffix = Constant.POINT + fileType.getType();
        if (fileName.endsWith(suffix)) {
            fileName = fileName.substring(0, fileName.length() - suffix.length());
        }
        return fileName;
    }

    public static void compressMutilFileToZip(List<File> files, File targetZipFile) throws Exception {
        try (FileOutputStream fos = new FileOutputStream(targetZipFile);
             ZipOutputStream zipOut = new ZipOutputStream(fos);
        ) {
            for (File file : files) {
                try (FileInputStream fis = new FileInputStream(file)) {
                    // 添加文件到ZIP输出流
                    ZipEntry zipEntry = new ZipEntry(file.getName());
                    zipOut.putNextEntry(zipEntry);

                    // 将文件内容写入ZIP输出流
                    byte[] bytes = new byte[4096];
                    int length;
                    while ((length = fis.read(bytes)) >= 0) {
                        zipOut.write(bytes, 0, length);
                    }
                }
            }
        }
    }

    public static String getFileSuffix(String fileName) {
        if (fileName == null || "".equals(fileName)) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return fileName.substring(lastDotIndex);
    }
}
