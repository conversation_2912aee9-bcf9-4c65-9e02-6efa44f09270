package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DwDataClearRuleRequestSaveVo {

    @Schema(title = "数据清洗规则")
    List<DwDataClearRuleRequestVo> ruleRequestVos;

    @Schema(title = "入库规则流程")
    WarehousingRuleFlowRequestVo warehouseRuleFlowRequestVo;
}
