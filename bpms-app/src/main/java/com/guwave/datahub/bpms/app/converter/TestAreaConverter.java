package com.guwave.datahub.bpms.app.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.guwave.onedata.dataware.common.contant.TestArea;


public class TestAreaConverter implements Converter<TestArea> {
    @Override
    public Class<?> supportJavaTypeKey() {
        return TestArea.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(TestArea value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new WriteCellData<>(value.getArea());
    }

    // 从Excel读取时：String -> TestArea
    @Override
    public TestArea convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String testArea = cellData.getStringValue();
        return TestArea.valueOf(testArea);
    }

}
