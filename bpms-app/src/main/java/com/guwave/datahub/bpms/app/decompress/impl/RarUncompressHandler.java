package com.guwave.datahub.bpms.app.decompress.impl;

import com.guwave.datahub.bpms.app.decompress.UnCompressHandler;
import net.sf.sevenzipjbinding.*;
import net.sf.sevenzipjbinding.impl.RandomAccessFileInStream;
import net.sf.sevenzipjbinding.simple.ISimpleInArchive;
import net.sf.sevenzipjbinding.simple.ISimpleInArchiveItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.RandomAccessFile;

/**
 * rar格式文件解压handler
 *
 * <AUTHOR>
 * @version 0.0.1
 */
@Component
public class RarUncompressHandler implements UnCompressHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(RarUncompressHandler.class);

    private static String tmpDirectory;

    @Value("${spring.rar.tmpDirectory}")
    public void setTmpDirectory(String tmpDirectory) {
        File tmpDirectoryFile = new File(tmpDirectory);
        if (!tmpDirectoryFile.exists()) {
            tmpDirectoryFile.mkdirs();
        }
        RarUncompressHandler.tmpDirectory = tmpDirectory;
    }

    @Override
    public void unCompress(File file, File targetDir) throws Exception {
        try (RandomAccessFile randomAccessFile = new RandomAccessFile(file, "r");
             RandomAccessFileInStream t = new RandomAccessFileInStream(randomAccessFile)
        ) {
            IInArchive inArchive;
            try {
                inArchive = SevenZip.openInArchive(ArchiveFormat.RAR5, t, new File(tmpDirectory));
            } catch (Exception e) {
                LOGGER.info("尝试使用RAR5解压失败，继续尝试使用RAR4：{} --> {}", file.getAbsolutePath(), targetDir);
                inArchive = SevenZip.openInArchive(ArchiveFormat.RAR, t, new File(tmpDirectory));
            }

            ISimpleInArchive simpleInArchive = inArchive.getSimpleInterface();
            for (final ISimpleInArchiveItem item : simpleInArchive.getArchiveItems()) {
                if (!item.isFolder()) {
                    item.extractSlow(data -> {
                        // item有“//”，即压缩的文件中含有文件夹时
                        if (item.getPath().indexOf(File.separator) > 0) {
                            File folderExisting = new File(targetDir, File.separator + item.getPath().substring(0, item.getPath().lastIndexOf(File.separator)));
                            if (!folderExisting.exists()) {
                                folderExisting.mkdirs();
                            }
                        }

                        try (FileOutputStream fos = new FileOutputStream(targetDir.getPath() + File.separator + item.getPath(), true)) {
                            fos.write(data);
                        } catch (Exception e) {
                            LOGGER.info("解压失败：{} --> {}", file.getAbsolutePath(), targetDir, e);
                            throw new RuntimeException(e);
                        }
                        // 返回写入了的数据的大小，以重置偏移量
                        return data.length;
                    });
                }
            }

            inArchive.close();
        } catch (Exception e) {
            LOGGER.info("解压失败：{} --> {}", file.getAbsolutePath(), targetDir, e);
            throw e;
        }
    }

    public static void main(String[] args) throws IOException {
//        new RarUncompressHandler().unCompress(new File("E:\\test.rar"), new File("E:\\test"));
    }
}
