package com.guwave.datahub.bpms.app.util;

import org.aspectj.lang.reflect.MethodSignature;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;

/**
 * Copyright (C), 2024, guwave
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-29 11:00:50
 */
public class AnnotationUtil {

    /**
     * 优先从Method上获取，获取不到从Class上获取
     *
     * @param signature       MethodSignature
     * @param annotationClass annotationClass
     * @return annotation
     */
    public static <T extends Annotation> T getAnnotation(MethodSignature signature, Class<T> annotationClass) {
        Method method = signature.getMethod();
        if (method.isAnnotationPresent(annotationClass)) {
            return method.getAnnotation(annotationClass);
        }
        Class<?> declaringType = signature.getDeclaringType();
        if (declaringType.isAnnotationPresent(annotationClass)) {
            return declaringType.getAnnotation(annotationClass);
        }
        return null;
    }
}
