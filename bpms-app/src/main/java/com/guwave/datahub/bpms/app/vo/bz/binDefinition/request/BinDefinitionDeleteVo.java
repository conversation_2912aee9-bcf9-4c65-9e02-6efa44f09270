package com.guwave.datahub.bpms.app.vo.bz.binDefinition.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * Bin Definition删除VO
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@Schema(title = "Bin Definition删除请求")
public class BinDefinitionDeleteVo {

    @NotNull(message = "测试程序名称不能为空")
    @Schema(description = "测试程序名称", example = "TestProgram1", required = true)
    private String testProgram;
}