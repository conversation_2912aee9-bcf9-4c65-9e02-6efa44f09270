package com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.DataAccuracyCheckHelper;
import com.guwave.datahub.bpms.common.constant.DataSourceEnum;
import com.guwave.datahub.bpms.common.constant.MesMatchTypeEnum;
import com.guwave.datahub.bpms.dao.clickhouse.dto.DataAccuracyBatchYieldDTO;
import com.guwave.onedata.dataware.common.contant.TestArea;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
@Schema(title = "批次良率详情列表")
public class DataAccuracyBatchYieldDetailVo {
    @ExcelProperty("Device Name")
    private String deviceName;
    @ExcelIgnore
    private String testArea;
    @ExcelIgnore
    private String dataSource;
    @ExcelProperty("Test Stage")
    private String testStage;
    @ExcelProperty("Lot ID")
    private String lotId;
    @ExcelProperty("Wafer ID")
    private String waferId;
    @ExcelProperty("Lot Type")
    private String lotType;
    @ExcelProperty("Data Accuracy Check")
    private String accuracyCheck;
    private String accuracyCheckType;
    @ExcelIgnore
    private String firstYield;
    @ExcelIgnore
    private String finalYield;
    @ExcelProperty("First Yield")
    private BigDecimal firstYieldFloat;
    @ExcelProperty("Final Yield")
    private BigDecimal finalYieldFloat;
    @ExcelProperty("Input Count")
    private Long inputCount;
    @ExcelProperty("Pass Count")
    private Long passCount;
    @ExcelProperty("MES MatchFlag")
    private String matchFlag;
    @ExcelProperty("File Count")
    private Long stdFileCount;
    @ColumnWidth(16)
    @ExcelProperty("Start Time")
    private String stdStartTime;

    private String waferNo;

    public static DataAccuracyBatchYieldDetailVo of(DataAccuracyBatchYieldDTO data) {
        return DataAccuracyBatchYieldDetailVo.builder()
                .deviceName(data.getDeviceName())
                .testArea(data.getTestArea())
                .dataSource(DataSourceEnum.ofDwTestArea(TestArea.of(data.getTestArea())).getValue())
                .testStage(data.getTestStage())
                .lotId(data.getLotId())
                .waferId(data.getWaferId())
                .waferNo(data.getWaferNo())
                .lotType(data.getLotType())
                .accuracyCheck(DataAccuracyCheckHelper.getShowName(data.getAccuracyCheck()))
                .accuracyCheckType(data.getAccuracyCheck())
                .firstYield(bigDecimalToPercentStr(data.getFirstYield(), true))
                .finalYield(bigDecimalToPercentStr(data.getFinalYield(), true))
                .firstYieldFloat(data.getFirstYield())
                .finalYieldFloat(data.getFinalYield())
                .inputCount(data.getInputCount())
                .passCount(data.getPassCount())
                .matchFlag(MesMatchTypeEnum.of(data.getMatchFlag()).getShowName())
                .stdFileCount(data.getStdFileCount())
                .stdStartTime(data.getStdStartTime())
                .build();
    }

    private static String bigDecimalToPercentStr(BigDecimal val, boolean multiply) {
        return RepairPreviewResultVo.bigDecimalToPercentStr(val, multiply);
    }
}
