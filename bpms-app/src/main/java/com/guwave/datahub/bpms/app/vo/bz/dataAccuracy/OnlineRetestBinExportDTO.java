package com.guwave.datahub.bpms.app.vo.bz.dataAccuracy;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.guwave.datahub.bpms.dao.clickhouse.dto.OnlineRetestBinDTO;
import lombok.Builder;
import lombok.Data;

/**
 * 片内复测下载实体
 *
 * <AUTHOR>
 * 2023/11/21
 */
@Data
@Builder
public class OnlineRetestBinExportDTO {

    @ExcelProperty(value = "Device Name", order = 1)
    @ColumnWidth(20)
    private String deviceId;
    @ExcelProperty(value = "Test Stage", order = 3)
    @ColumnWidth(20)
    private String testStage;
    @ExcelProperty(value = "Test Program", order = 5)
    @ColumnWidth(40)
    private String testProgram;
    @ExcelProperty(value = "Lot ID", order = 7)
    @ColumnWidth(20)
    private String lotId;
    @ExcelProperty(value = "sbLot ID", order = 9)
    @ColumnWidth(20)
    private String sblotId;
    @ExcelProperty(value = "Lot Type", order = 11)
    @ColumnWidth(20)
    private String lotType;
    @ExcelProperty(value = "File Name", order = 100)
    @ColumnWidth(100)
    private String fileName;

    @ExcelProperty(value = "ECID", order = 20)
    @ColumnWidth(20)
    private String ecid;
    @ExcelProperty(value = "Test Order", order = 21)
    @ColumnWidth(20)
    private Integer testOrder;
    @ExcelProperty(value = "HBin Number", order = 22)
    @ColumnWidth(20)
    private String hbinNum;
    @ExcelProperty(value = "SBin Number", order = 24)
    @ColumnWidth(20)
    private String sbinNum;
    @ExcelProperty(value = "HBin Name", order = 26)
    @ColumnWidth(20)
    private String hbinName;
    @ExcelProperty(value = "SBin Name", order = 28)
    @ColumnWidth(20)
    private String sbinName;
    @ExcelProperty(value = "HBin PF", order = 30)
    @ColumnWidth(20)
    private String hbinPf;

    public static OnlineRetestBinExportDTO of(OnlineRetestBinDTO onlineRetestBinDTO) {
        return OnlineRetestBinExportDTO.builder()
                .deviceId(onlineRetestBinDTO.getDeviceId())
                .testStage(onlineRetestBinDTO.getTestStage())
                .testProgram(onlineRetestBinDTO.getTestProgram())
                .lotId(onlineRetestBinDTO.getLotId())
                .sblotId(onlineRetestBinDTO.getSblotId())
                .lotType(onlineRetestBinDTO.getLotType())
                .fileName(onlineRetestBinDTO.getFileName())
                .ecid(onlineRetestBinDTO.getEcid())
                .testOrder(onlineRetestBinDTO.getTestOrder())
                .hbinNum(onlineRetestBinDTO.getHbinNum())
                .sbinNum(onlineRetestBinDTO.getSbinNum())
                .hbinName(onlineRetestBinDTO.getHbinName())
                .sbinName(onlineRetestBinDTO.getSbinName())
                .hbinPf(onlineRetestBinDTO.getHbinPf())
                .build();
    }
}
