package com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model;

import com.guwave.onedata.dataware.common.model.vo.ProductDeviceInfo;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import java.util.List;

@Data
public class MetaData {
    private String fileName;
    private String partTyp;
    private String lotId;
    private String sblotId;
    private String waferId;
    private String flowId;
    private String rtstCod;
    private String testCod;
    private String setupId;
    private String floorId;
    private String tstTemp;

    // NODE_NAM -> testerName
    private String nodeNam;

    // TSTR_TYP -> testerType
    private String tstrTyp;

    // CARD_ID -> probecardLoadboardId(CP)
    private String cardId;

    // LOAD_ID -> probecardLoadboardId(FT)
    private String loadId;

    // JOB_NAM -> testProgram
    private String jobNam;

    // JOB_REV -> testProgramVersion
    private String jobRev;

    // PKG_TYP
    private String pkgTyp;

    // USER_TXT -> RETEST_BIN_NUM
    private String userTxt;

    private String remoteOriginFilePath;
    private String remoteOriginFileName;

    private String startT;
    private String finishT;
    private List<ProductDeviceInfo> deviceInfos;

    public MetaData copy() {
        MetaData metaData = new MetaData();
        BeanUtils.copyProperties(this, metaData);
        return metaData;
    }
}
