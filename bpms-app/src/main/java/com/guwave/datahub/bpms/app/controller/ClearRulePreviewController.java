package com.guwave.datahub.bpms.app.controller;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.annotation.NoLog;
import com.guwave.datahub.bpms.app.service.bz.ClearRulePreviewService;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.request.ClearRulePreviewDropDownFilterVo;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.request.ClearRulePreviewRecordQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.request.ClearRulePreviewRecordUploadVo;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.request.ClearRulePreviewTestVo;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.response.ClearRulePreviewDropDownVo;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.response.ClearRulePreviewRecordVo;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;


@Log
@Slf4j
@RestController
@AllArgsConstructor
@Auth
//@License
@Tag(name = "ClearRulePreviewControllerAPI", description = "清洗规则预览接口",
        externalDocs = @ExternalDocumentation(description = "需求文档",
                url = "https://ohjj5az3be.feishu.cn/wiki/BXMnwkjguiMrkIkq0Zxcpzd1nBh"))
@RequestMapping("clearRulePreview")
public class ClearRulePreviewController {

    private final ClearRulePreviewService clearRulePreviewService;

    @Operation(summary = "下拉框选项查询")
    @PostMapping("filterDropDown")
    public ResponseVo<List<ClearRulePreviewDropDownVo>> filterDropDown(@Validated @RequestBody ClearRulePreviewDropDownFilterVo clearRulePreviewDropDownFilterVo) {
        return ResponseVo.ok(this.clearRulePreviewService.filterDropDown(clearRulePreviewDropDownFilterVo));
    }

    @Operation(summary = "查询")
    @PostMapping("queryRecord")
    public ResponseVo<PageableDataVo<ClearRulePreviewRecordVo>> queryRecord(@Validated @RequestBody ClearRulePreviewRecordQueryVo clearRulePreviewRecordQueryVo) {
        return ResponseVo.ok(this.clearRulePreviewService.queryRecord(clearRulePreviewRecordQueryVo));
    }

    @Operation(summary = "上传")
    @PostMapping("upload")
    @NoLog
    public ResponseVo<Void> upload(@Validated @ModelAttribute ClearRulePreviewRecordUploadVo clearRulePreviewRecordUploadVo) {
        this.clearRulePreviewService.upload(clearRulePreviewRecordUploadVo);
        return ResponseVo.ok();
    }

    @Operation(summary = "下载明细信息")
    @GetMapping("downloadDetail")
    public void downloadDetail(HttpServletResponse response, @Validated @RequestParam("id") @Schema(example = "1") @Min(1L) Long id) {
        this.clearRulePreviewService.downloadDetail(response, id);
    }

    @Operation(summary = "重试")
    @PostMapping("retry")
    public ResponseVo<Void> retry(@Validated @RequestBody @Schema(example = "[1,2,3]") @NotNull List<Long> ids) {
        this.clearRulePreviewService.retry(ids);
        return ResponseVo.ok();
    }

    @Operation(summary = "调试")
    @PostMapping("test")
    public ResponseVo<Object> test(@Validated @RequestBody ClearRulePreviewTestVo clearRulePreviewTestVo) {
        return ResponseVo.ok(this.clearRulePreviewService.test(clearRulePreviewTestVo));
    }
}
