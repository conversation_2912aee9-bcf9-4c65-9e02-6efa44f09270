package com.guwave.datahub.bpms.app.service.bz.handler.clearRulePreview.convert;

import com.guwave.datahub.bpms.dao.domain.clearRulePreview.ClearRulePreviewRecord;

import java.io.File;
import java.io.IOException;

public abstract class ConvertHandler {
    public abstract boolean isSupport(ClearRulePreviewRecord record);

    public abstract File convertFile(ClearRulePreviewRecord record, File needConvertFile) throws Exception;
}
