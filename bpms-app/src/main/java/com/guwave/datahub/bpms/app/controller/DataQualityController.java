package com.guwave.datahub.bpms.app.controller;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.License;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.service.bz.DataQualityService;
import com.guwave.datahub.bpms.app.vo.bz.quality.request.DataQualityRuleSaveVo;
import com.guwave.datahub.bpms.app.vo.bz.quality.response.DataQualityRuleDetailVo;
import com.guwave.datahub.bpms.app.vo.bz.quality.response.DataQualityRuleResultVo;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.IOException;

@Log
@Auth
//@License
@Validated
@Slf4j
@RestController
@AllArgsConstructor
@Tag(name = "DataQualityControllerAPI", description = "数据质量接口",
        externalDocs = @ExternalDocumentation(description = "需求文档",
                url = "https://ohjj5az3be.feishu.cn/wiki/AQPEw0R4TiiNQ5kkMT1cDnuDnyh?fromTtSwitch=1")
)
@RequestMapping("dataQuality")
public class DataQualityController {

    private DataQualityService dataQualityService;


    @Operation(summary = "数据质量页面 导入")
    @PostMapping("importRule")
    public ResponseVo<Void> importRule(MultipartFile file) throws IOException {
        this.dataQualityService.importRule(file);
        return ResponseVo.ok();
    }

    @Operation(summary = "数据质量页面 导出")
    @PostMapping("exportRule")
    public void exportRule(HttpServletResponse response) throws IOException {
        this.dataQualityService.exportRule(response);
    }

    @Operation(summary = "数据质量页面 表格数据分页查询")
    @PostMapping("queryRules")
    public ResponseVo<PageableDataVo<DataQualityRuleDetailVo>> queryRules(@RequestBody PageableRequestVo pageableRequestVo) {
        return ResponseVo.ok(this.dataQualityService.queryRules(pageableRequestVo));
    }

    @Operation(summary = "数据质量页面 删除一条规则")
    @GetMapping("deleteRule")
    public ResponseVo<Void> deleteRule(@RequestParam("id") @Schema(example = "1") @Min(value = 1) Long id) {
        this.dataQualityService.deleteRule(id);
        return ResponseVo.ok();
    }

    @Operation(summary = "数据质量页面 切换一条规则状态")
    @GetMapping("switchRuleStatus")
    public ResponseVo<Void> switchRuleStatus(@RequestParam("id") @Schema(example = "1") @Min(value = 1) Long id, @RequestParam("status") @Schema(description = "0->关闭，1->开启", example = "1") @NotNull Integer status) {
        this.dataQualityService.switchRuleStatus(id, status);
        return ResponseVo.ok();
    }


    @Operation(summary = "数据质量规则详情编辑页面 查询此条规则的初始化数据")
    @GetMapping("queryDetail")
    public ResponseVo<DataQualityRuleDetailVo> queryDetail(@RequestParam("id") @Schema(example = "1") @Min(value = 1) Long id) {
        return ResponseVo.ok(this.dataQualityService.queryDetail(id));
    }

    @Operation(summary = "数据质量规则编辑页面 试运行")
    @PostMapping("tryRun")
    public ResponseVo<DataQualityRuleResultVo> tryRun(@RequestBody DataQualityRuleSaveVo dataQualityRuleSaveVo) {
        return ResponseVo.ok(this.dataQualityService.tryRun(dataQualityRuleSaveVo));
    }

    @Operation(summary = "数据质量规则编辑页面 保存")
    @PostMapping("saveRule")
    public ResponseVo<Void> saveRule(@RequestBody DataQualityRuleSaveVo dataQualityRuleSaveVo) {
        this.dataQualityService.saveRule(dataQualityRuleSaveVo);
        return ResponseVo.ok();
    }

    @Operation(summary = "数据质量规则详情编辑页面 根据选择的时间查询指标结果")
    @GetMapping("queryRuleResult")
    public ResponseVo<DataQualityRuleResultVo> queryRuleResult(@RequestParam("id") @Schema(example = "1") @Min(value = 1) Long id, @RequestParam("dayKey") @Schema(example = "20240101") @NotBlank String dayKey) {
        return ResponseVo.ok(this.dataQualityService.qyeryRuleResult(id, dayKey));
    }
}
