package com.guwave.datahub.bpms.app.manage;

import com.guwave.datahub.bpms.dao.repository.dw.mapper.BinDefinitionMapper;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.TestProgramBinDefinition;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Component
public class TestProgramBinDefinitionManage {

    @Autowired
    private BinDefinitionMapper binDefinitionMapper;

    @Transactional(rollbackFor = Exception.class, transactionManager = "secondaryMybatisTransactionManager")
    public void deleteAndInsert(String customer, String subCustomer, String testProgram, boolean deleteAllFlag, List<TestProgramBinDefinition> binDefinitionDetails) {
        if (testProgram != null) {
            log.info("删除测试程序bin定义：{} {} {}", customer, subCustomer, testProgram);
            binDefinitionMapper.deleteByTestProgramAndPermissions(testProgram, customer, subCustomer);
        } else if (deleteAllFlag) {
            log.info("删除所有测试程序bin定义 {} {}", customer, subCustomer);
            binDefinitionMapper.deleteAllByPermissions(customer, subCustomer);
        }
        if (CollectionUtils.isNotEmpty(binDefinitionDetails)) {
            log.info("插入测试程序bin定义：{} {} {}条", customer, subCustomer, binDefinitionDetails.size());
            binDefinitionMapper.insertBatch(binDefinitionDetails);
        }
    }
}
