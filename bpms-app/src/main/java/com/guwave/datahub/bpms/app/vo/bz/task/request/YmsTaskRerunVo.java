package com.guwave.datahub.bpms.app.vo.bz.task.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/13
 * @description RerunTaskIdsVo
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(title = "task 需要批量重跑的id")
public class YmsTaskRerunVo {

    @Schema(title = "需要重跑的task id", example = "[1,2,3,4]")
    @NotEmpty(message = "ids")
    private List<Long> ids;
}
