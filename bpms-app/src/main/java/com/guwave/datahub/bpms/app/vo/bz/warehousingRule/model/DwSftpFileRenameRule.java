package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DwSftpFileRenameRule {
    private String subCustomer;
    private String ftpIp;
    private Integer useTimestamp;
    private String ruleExpression;
    private Integer directoryNum;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DwSftpFileRenameRule that = (DwSftpFileRenameRule) o;
        return Objects.equals(subCustomer, that.subCustomer) && Objects.equals(ftpIp, that.ftpIp) && Objects.equals(useTimestamp, that.useTimestamp) && Objects.equals(ruleExpression, that.ruleExpression) && Objects.equals(directoryNum, that.directoryNum);
    }

    @Override
    public int hashCode() {
        return Objects.hash(subCustomer, ftpIp, useTimestamp, ruleExpression, directoryNum);
    }
}
