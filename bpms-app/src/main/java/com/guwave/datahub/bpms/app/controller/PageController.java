package com.guwave.datahub.bpms.app.controller;

import cn.hutool.core.io.FileUtil;
import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.List;

@Log
//@License
@Validated
@Slf4j
@RestController
@AllArgsConstructor
@Tag(name = "PageControllerAPI", description = "页面接口",
        externalDocs = @ExternalDocumentation(description = "需求文档",
                url = "")
)
@RequestMapping("page")
public class PageController {

    @Operation(summary = "openSource")
    @GetMapping("openSource/**")
    public void downloadVue(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String filePath = URLDecoder.decode(request.getRequestURI().substring(request.getContextPath().length()), "UTF-8");
        ClassPathResource classPathResource = new ClassPathResource(filePath);
        try (InputStream in = classPathResource.getInputStream();
             OutputStream out = response.getOutputStream()) {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(classPathResource.getFilename(), "UTF-8"));
            IOUtils.copy(in, out);
        }
    }

    @Auth
    @Operation(summary = "页面")
    @PostMapping("custom/{fileName}")
    public ResponseVo<String> getModuleSwitchPage(@PathVariable("fileName") String fileName) throws IOException {
        ClassPathResource classPathResource = new ClassPathResource("page/" + fileName);
        List<String> lines = FileUtil.readUtf8Lines(classPathResource.getURL());
        return ResponseVo.ok(String.join("\n", lines));
    }
}
