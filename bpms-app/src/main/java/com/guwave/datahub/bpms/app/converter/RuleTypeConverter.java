package com.guwave.datahub.bpms.app.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.guwave.onedata.dataware.common.contant.DataClearRuleType;

public class RuleTypeConverter implements Converter<DataClearRuleType> {
    @Override
    public Class<?> supportJavaTypeKey() {
        return DataClearRuleType.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(DataClearRuleType value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new WriteCellData<>(value.getType());
    }

    // 从Excel读取时：String -> DataClearRuleType
    @Override
    public DataClearRuleType convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String type = cellData.getStringValue();
        return DataClearRuleType.valueOf(type);
    }
}
