//package com.guwave.datahub.bpms.app.configuration.clickhouse;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.ArrayUtils;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.SqlSessionFactoryBean;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//import org.springframework.orm.jpa.JpaTransactionManager;
//import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
//import org.springframework.transaction.PlatformTransactionManager;
//import org.springframework.transaction.annotation.EnableTransactionManagement;
//
//import javax.persistence.EntityManager;
//import javax.sql.DataSource;
//import java.util.Objects;
//
//
//@Slf4j
//@Configuration
//@EnableTransactionManagement
//@EnableJpaRepositories(entityManagerFactoryRef = "entityManagerFactoryDim",
//        transactionManagerRef = "transactionManagerDim",
//        basePackages = {"com.guwave.datahub.bpms.dao.repository.clickhouse.dim"})
//@MapperScan(
//        basePackages = "com.guwave.datahub.bpms.dao.repository.clickhouse.dim.mapper",
//        sqlSessionFactoryRef = "dimSqlSessionFactory"
//)
//public class DimConfiguration {
//
//    @Autowired
//    JpaClickhouseConfiguration jpaClickhouseConfiguration;
//
//    @Value("${spring.ck.dim.database}")
//    private String database;
//
//    @Bean(name = "entityManagerDim")
//    public EntityManager entityManager() {
//        return Objects.requireNonNull(entityManagerFactoryDim().getObject()).createEntityManager();
//    }
//
//    @Bean(name = "entityManagerFactoryDim")
//    public LocalContainerEntityManagerFactoryBean entityManagerFactoryDim() {
//        return jpaClickhouseConfiguration.getEntityManagerFactory(
//                database,
//                "dimPersistenceUnit",
//                "com.guwave.datahub.bpms.dao.clickhouse.domain.dim");
//    }
//
//    @Bean(name = "transactionManagerDim")
//    public PlatformTransactionManager transactionManagerDim() {
//        return new JpaTransactionManager(Objects.requireNonNull(entityManagerFactoryDim().getObject()));
//    }
//
//    @Bean
//    public DataSource dimDataSource() {
//        return entityManagerFactoryDim().getDataSource();
//    }
//
//
//    // 配置MyBatis
//    @Bean(name = "dimSqlSessionFactory")
//    public SqlSessionFactory DimSqlSessionFactory() throws Exception {
//        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
//        sessionFactory.setDataSource(dimDataSource());
//        sessionFactory.setMapperLocations(
//                ArrayUtils.addAll(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/clickhouse/dim/*.xml"),
//                        new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml"))
//        );
//        return sessionFactory.getObject();
//
//    }
//
//    @Bean(name = "dimMybatisTransactionManager")
//    public PlatformTransactionManager DimMybatisTransactionManager() {
//        return new DataSourceTransactionManager(dimDataSource());
//    }
//}
