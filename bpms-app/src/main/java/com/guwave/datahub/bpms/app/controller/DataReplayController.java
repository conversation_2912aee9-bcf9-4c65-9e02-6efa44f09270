package com.guwave.datahub.bpms.app.controller;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.service.bz.DataReplayService;
import com.guwave.datahub.bpms.app.vo.bz.replay.request.DataReplayFileDropDownFilterVo;
import com.guwave.datahub.bpms.app.vo.bz.replay.request.DataReplayFileOperateVo;
import com.guwave.datahub.bpms.app.vo.bz.replay.request.DataReplayRecordDropDownFilterVo;
import com.guwave.datahub.bpms.app.vo.bz.replay.request.DataReplayLotWaferDropDownFilterVo;
import com.guwave.datahub.bpms.app.vo.bz.replay.request.DataReplayLotWaferOperateVo;
import com.guwave.datahub.bpms.app.vo.bz.replay.request.DataReplayRecordQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.replay.request.DataReplayTaskDropDownFilterVo;
import com.guwave.datahub.bpms.app.vo.bz.replay.request.DataReplayTaskQueryPageVo;
import com.guwave.datahub.bpms.app.vo.bz.replay.request.DataReplayTaskQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.replay.request.DataReplayFileQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.replay.request.DataReplayLotWaferQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.replay.response.DataReplayDropDownVo;
import com.guwave.datahub.bpms.app.vo.bz.replay.response.DataReplayRecordVo;
import com.guwave.datahub.bpms.app.vo.bz.replay.response.DataReplayTaskVo;
import com.guwave.datahub.bpms.app.vo.bz.replay.response.DataReplayWarehousingRecordVo;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.Min;
import java.util.List;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * DataReplayController
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2024-02-26 11:37:45
 */
@Log
@Slf4j
@RestController
@AllArgsConstructor
@Auth
//@License
@Tag(name = "DataReplayControllerAPI", description = "数据重播接口",
        externalDocs = @ExternalDocumentation(description = "需求文档",
                url = "https://ohjj5az3be.feishu.cn/wiki/UkFDw7IEBi4vzfkEiitcZOO5nwc"))
@RequestMapping("dataReplay")
public class DataReplayController {

    private final DataReplayService dataReplayService;

    @Operation(summary = "lotWafer页下拉框列表支持的条件")
    @PostMapping("filterWarehousingLotWaferDropDown")
    public ResponseVo<List<DataReplayDropDownVo>> filterWarehousingLotWaferDropDown(@RequestBody @Validated DataReplayLotWaferDropDownFilterVo dataReplayLotWaferDropDownFilterVo) {
        return ResponseVo.ok(this.dataReplayService.filterLotWaferDropDown(dataReplayLotWaferDropDownFilterVo));
    }

    @Operation(summary = "lotWafer页条件查询数据入库记录")
    @PostMapping("queryWarehousingLotWafer")
    public ResponseVo<PageableDataVo<DataReplayWarehousingRecordVo>> queryWarehousingLotWafer(@RequestBody @Validated DataReplayLotWaferQueryVo dataReplayWarehousingRecordQueryVo) {
        return ResponseVo.ok(this.dataReplayService.queryWarehousingLotWafer(dataReplayWarehousingRecordQueryVo));
    }

    @Operation(summary = "lotWafer页根据下拉框条件重播")
    @PostMapping("replayByLotWaferOperate")
    public ResponseVo<Void> replayByLotWaferOperate(@RequestBody @Validated DataReplayLotWaferOperateVo dataReplayLotWaferOperateVo) {
        return ResponseVo.ok(this.dataReplayService.batchReplay(dataReplayLotWaferOperateVo));
    }

    @Operation(summary = "file页下拉框列表支持的条件")
    @PostMapping("filterWarehousingFileDropDown")
    public ResponseVo<List<DataReplayDropDownVo>> filterWarehousingFileDropDown(@RequestBody @Validated DataReplayFileDropDownFilterVo dataReplayFileDropDownFilterVo) {
        return ResponseVo.ok(this.dataReplayService.filterFileDropDown(dataReplayFileDropDownFilterVo));
    }

    @Operation(summary = "file页条件查询数据入库记录")
    @PostMapping("queryWarehousingFile")
    public ResponseVo<PageableDataVo<DataReplayWarehousingRecordVo>> queryWarehousingFile(@RequestBody @Validated DataReplayFileQueryVo dataReplayWarehousingRecordFileQueryVo) {
        return ResponseVo.ok(this.dataReplayService.queryWarehousingRecordByFile(dataReplayWarehousingRecordFileQueryVo));
    }

    @Operation(summary = "file页根据下拉框条件重播")
    @PostMapping("replayByFileOperate")
    public ResponseVo<Void> replayByFileOperate(@RequestBody @Validated DataReplayFileOperateVo dataReplayFileOperateVo) {
        return ResponseVo.ok(this.dataReplayService.batchReplay(dataReplayFileOperateVo));
    }

    @Operation(summary = "数据重播记录查询下拉框列表支持的条件")
    @PostMapping("filterReplayRecordDropDown")
    public ResponseVo<List<DataReplayDropDownVo>> filterReplayRecordDropDown(@RequestBody @Validated DataReplayRecordDropDownFilterVo dataReplayDropDownFilterVo) {
        return ResponseVo.ok(this.dataReplayService.filterReplayRecordDropDown(dataReplayDropDownFilterVo));
    }

    @Operation(summary = "数据重播记录页面：条件查询数据重播记录")
    @PostMapping("queryReplayRecord")
    public ResponseVo<PageableDataVo<DataReplayRecordVo>> queryReplayRecord(@RequestBody @Validated DataReplayRecordQueryVo dataReplayRecordQueryVo) {
        return ResponseVo.ok(this.dataReplayService.queryReplayRecord(dataReplayRecordQueryVo));
    }

    @Operation(summary = "数据重播记录页面：重播记录重试")
    @GetMapping("retryReplay")
    public ResponseVo<Void> retryReplay(@RequestParam("id") @Schema(example = "1") @Min(value = 1) Long id) {
        return ResponseVo.ok(this.dataReplayService.retryReplay(id));
    }

    @Operation(summary = "数据重播记录页面：取消重播")
    @GetMapping("cancelReplay")
    public ResponseVo<Void> cancelReplay(@RequestParam("id") @Schema(example = "1") @Min(value = 1) Long id) {
        return ResponseVo.ok(this.dataReplayService.cancelReplay(id));
    }

    @Operation(summary = "数据重播记录页面：任务置顶")
    @GetMapping("topReplay")
    public ResponseVo<Void> topReplay(@RequestParam("id") @Schema(example = "1") @Min(value = 1) Long id) {
        return ResponseVo.ok(this.dataReplayService.topReplay(id));
    }

    @Operation(summary = "重播任务查询下拉框列表支持的条件")
    @PostMapping("filterReplayTaskDropDown")
    public ResponseVo<List<DataReplayDropDownVo>> filterReplayTaskDropDown(@RequestBody @Validated DataReplayTaskDropDownFilterVo dataReplayTaskDropDownFilterVo) {
        return ResponseVo.ok(this.dataReplayService.filterReplayTaskDropDown(dataReplayTaskDropDownFilterVo));
    }

    @Operation(summary = "任务记录页：条件查询任务记录")
    @PostMapping("queryReplayTask")
    public ResponseVo<List<DataReplayTaskVo>> queryReplayTask(@RequestBody @Validated DataReplayTaskQueryVo dataReplayOperateVo) {
        return ResponseVo.ok(this.dataReplayService.queryReplayTask(dataReplayOperateVo));
    }

    @Operation(summary = "任务记录页：条件分页查询任务记录")
    @PostMapping("queryReplayTaskPage")
    public ResponseVo<PageableDataVo<DataReplayTaskVo>> queryReplayTask(@RequestBody @Validated DataReplayTaskQueryPageVo dataReplayOperateVo) {
        return ResponseVo.ok(this.dataReplayService.queryReplayTaskPage(dataReplayOperateVo));
    }

}
