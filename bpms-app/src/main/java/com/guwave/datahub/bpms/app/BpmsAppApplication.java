package com.guwave.datahub.bpms.app;

import com.guwave.datahub.bpms.app.annotation.EnableFeignClients;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.logging.LoggingApplicationListener;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.Set;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * BpmsAppApplication
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-26 09:55:16
 */
@EnableScheduling
@EnableDubbo
@EnableFeignClients
@EnableAspectJAutoProxy
@MapperScan("com.guwave.datahub.bpms.dao.repository")
@SpringBootApplication(scanBasePackages = {"com.guwave.datahub.bpms.app", "com.guwave.datahub.bpms.common", "com.guwave.datahub.bpms.dao.aspect", "com.guwave.onedata.dataware.dao.mysql", "com.guwave.datahub.bpms.dao.ck", "com.guwave.onedata.next.compute.api", "com.guwave.datahub.bpms.dao.repository.dw"},
        exclude = { ErrorMvcAutoConfiguration.class, MybatisAutoConfiguration.class})

@PropertySource(value = {"classpath:properties/bpms-app.properties", "file:properties/bpms-app.properties"}, ignoreResourceNotFound = true)
public class BpmsAppApplication {

    private static final Logger LOGGER = LoggerFactory.getLogger(BpmsAppApplication.class);

    public static void main(String[] args) {
        SpringApplicationBuilder builder = new SpringApplicationBuilder(BpmsAppApplication.class);
        Set<ApplicationListener<?>> listeners = builder.application().getListeners();
        listeners.removeIf(listener -> listener instanceof LoggingApplicationListener);
        builder.application().setListeners(listeners);
        builder.run(args);
        LOGGER.info("bpms app start successfully");
    }
}
