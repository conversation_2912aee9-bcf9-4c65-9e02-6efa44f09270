package com.guwave.datahub.bpms.app.scheduler;

import com.guwave.datahub.bpms.dao.domain.schedule.ScheduleTaskDefinition;
import com.guwave.datahub.bpms.dao.repository.bpms.ScheduleTaskDefinitionRepository;
import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import org.redisson.executor.CronExpression;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Component
public class GeneralDispatchJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(GeneralDispatchJob.class);
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private final ScheduleTaskDefinitionRepository scheduleTaskDefinitionRepository;
    private final TaskDispatchService taskDispatchService;

    @Value("${spring.scheduler.cron.expression}")
    private String schedulerCronExpression;

    private long scheduleInterval;

    @Autowired
    public GeneralDispatchJob(ScheduleTaskDefinitionRepository scheduleTaskDefinitionRepository,
                              TaskDispatchService taskDispatchService) {
        this.scheduleTaskDefinitionRepository = scheduleTaskDefinitionRepository;
        this.taskDispatchService = taskDispatchService;
    }

    @PostConstruct
    public void init() {
        this.scheduleInterval = calculateScheduleInterval();
    }

    @ScheduleSwitch
    @Scheduled(cron = "${spring.scheduler.cron.expression}")
    public void schedule() {
        try {
            LOGGER.info("开始扫描定时任务");
            runTask();
        } catch (Exception e) {
            LOGGER.error("扫描定时任务异常!", e);
        }
    }

    private long calculateScheduleInterval() {
        try {
            CronExpression cronExpression = new CronExpression(schedulerCronExpression);
            Date minTime = new Date(0L);
            long scheduleInterval = cronExpression.getNextValidTimeAfter(cronExpression.getNextValidTimeAfter(minTime)).getTime() - cronExpression.getNextValidTimeAfter(minTime).getTime();
            LOGGER.info("调度时间间隔为:{}", scheduleInterval);
            return scheduleInterval;
        } catch (Exception e) {
            throw new IllegalStateException("计算调度间隔异常, Cron表达式: " + schedulerCronExpression, e);
        }
    }

    private void runTask() {
        long currentTime = System.currentTimeMillis();
        long lastTime = currentTime - scheduleInterval;
        List<ScheduleTaskDefinition> scheduleTaskDefinitions = scheduleTaskDefinitionRepository.findAllByIsActiveAndDeleteFlag(true, false);

        scheduleTaskDefinitions.forEach(t -> {
            if (t.getCronExpression() != null) {
                try {
                    boolean cronIsValid = CronExpression.isValidExpression(t.getCronExpression());
                    if (cronIsValid) {
                        CronExpression cronExpressionStr = new CronExpression(t.getCronExpression());
                        Date nextValidTimeAfterLastTime = cronExpressionStr.getNextValidTimeAfter(new Date(lastTime));

                        String formattedLastTime = DATE_FORMAT.format(new Date(lastTime));
                        String formattedCurrentTime = DATE_FORMAT.format(new Date(currentTime));

                        LOGGER.info("上次调度时间为:{}", formattedLastTime);
                        LOGGER.info("本次调度时间为:{}", formattedCurrentTime);

                        if (nextValidTimeAfterLastTime != null) {
                            String formattedNextValidTimeAfterLastTime = DATE_FORMAT.format(nextValidTimeAfterLastTime);

                            LOGGER.info("{} 定时任务上次调度后的匹配时间为:{}", t.getTaskName(), formattedNextValidTimeAfterLastTime);

                            if (nextValidTimeAfterLastTime.getTime() <= currentTime && nextValidTimeAfterLastTime.getTime() > lastTime) {
                                LOGGER.info("{} 定时任务开始执行，Cron表达式: {}", t.getTaskName(), t.getCronExpression());
                                taskDispatchService.doTask(t);
                            } else {
                                LOGGER.info("{} 定时任务未到执行时间，Cron表达式: {}", t.getTaskName(), t.getCronExpression());
                            }
                        } else {
                            LOGGER.warn("{} 定时任务的Cron表达式计算的下次执行时间为空", t.getTaskName());
                        }
                    } else {
                        LOGGER.warn("{} 定时任务的Cron表达式无效: {}", t.getTaskName(), t.getCronExpression());
                    }
                } catch (IllegalArgumentException e) {
                    LOGGER.error("{} 定时任务的Cron表达式解析异常，Cron表达式: {}", t.getTaskName(), t.getCronExpression(), e);
                } catch (Exception e) {
                    LOGGER.error("{} 定时任务执行异常，Cron表达式: {}", t.getTaskName(), t.getCronExpression(), e);
                }
            } else {
                LOGGER.warn("{} 定时任务缺少Cron表达式", t.getTaskName());
            }
        });
    }
}
