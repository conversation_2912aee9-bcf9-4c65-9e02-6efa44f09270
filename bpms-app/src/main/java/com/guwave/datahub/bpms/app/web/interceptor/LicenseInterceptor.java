package com.guwave.datahub.bpms.app.web.interceptor;

import com.guwave.datahub.bpms.app.annotation.License;
import com.guwave.datahub.bpms.app.service.license.LicenseService;
import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * License拦截器
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-27 09:00:48
 */
@Component
@AllArgsConstructor
public class LicenseInterceptor implements HandlerInterceptor {

    private static final Logger LOGGER = LoggerFactory.getLogger(LicenseInterceptor.class);

    public static final String MODULE_NAME = "BPMS";

    private final LicenseService licenseService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Method method = handlerMethod.getMethod();
            Class<?> clazz = method.getDeclaringClass();
            if (method.isAnnotationPresent(License.class) || clazz.isAnnotationPresent(License.class)) {
                StopWatch sw = new StopWatch();
                sw.start();
                LOGGER.info("开始验证License");
                String userAgent = request.getHeader("User-Agent");
                if (null != userAgent && userAgent.startsWith("Mozilla")) {
                    // 只有浏览器请求才会触发
                    this.licenseService.check(RequestContext.getCustomer(), MODULE_NAME, RequestContext.getUserName());
                }
                sw.stop();
                LOGGER.info("结束验证License, 耗时: {}ms", sw.getTotalTimeMillis());
            }
        }
        return HandlerInterceptor.super.preHandle(request, response, handler);
    }
}
