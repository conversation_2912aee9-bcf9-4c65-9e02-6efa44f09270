package com.guwave.datahub.bpms.app.decompress.impl;

import com.guwave.datahub.bpms.app.decompress.UnCompressHandler;
import org.apache.commons.compress.archivers.sevenz.SevenZArchiveEntry;
import org.apache.commons.compress.archivers.sevenz.SevenZFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;

public class ServenZUncompressHandler implements UnCompressHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServenZUncompressHandler.class);

    @Override
    public void unCompress(File file, File targetDir) throws Exception {
        try (SevenZFile sevenZFile = new SevenZFile(file)) {
            byte[] buffer = new byte[1024 * 5];
            SevenZArchiveEntry archiveEntry;
            while (null != (archiveEntry = sevenZFile.getNextEntry())) {
                String archiveEntryFileName = archiveEntry.getName();
                File entryFile = new File(targetDir, archiveEntryFileName);
                if (!archiveEntry.isDirectory()) {
                    if (!entryFile.getParentFile().exists()) {
                        entryFile.getParentFile().mkdirs();
                    }
                    try (FileOutputStream fos = new FileOutputStream(entryFile)) {
                        int n;
                        while ((n = sevenZFile.read(buffer)) != -1) {
                            fos.write(buffer, 0, n);
                        }
                        fos.flush();
                    } catch (Exception e) {
                        LOGGER.info("解压失败：{} --> {}", file.getAbsolutePath(), targetDir, e);
                        throw e;
                    }
                } else {
                    if (!entryFile.exists()) {
                        entryFile.mkdirs();
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.info("解压失败：{} --> {}", file.getAbsolutePath(), targetDir, e);
            throw e;
        }
    }
}
