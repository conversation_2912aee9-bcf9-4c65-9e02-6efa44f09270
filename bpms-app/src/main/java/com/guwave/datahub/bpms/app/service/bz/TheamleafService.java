package com.guwave.datahub.bpms.app.service.bz;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Map;

@Component
@Slf4j
public class TheamleafService {

    @Autowired
    private TemplateEngine templateEngine;

    /**
     * 根据模板和数据生成HTML文件
     *
     * @param templateName 模板名称
     * @param data         数据
     * @param outputFile   输出文件
     * @throws IOException IO异常
     */
    public String generateHtmlFile(String templateName, Map<String, Object> data, File outputFile) throws IOException {
        Context context = new Context();
        data.forEach(context::setVariable);

        String htmlContent = templateEngine.process(templateName, context);

        if (outputFile != null) {
            // 确保输出目录存在
            File parentDir = outputFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            try (FileWriter writer = new FileWriter(outputFile)) {
                writer.write(htmlContent);
            }

            log.info("HTML文件生成成功: {}", outputFile.getAbsolutePath());
        }
        return htmlContent;
    }

}
