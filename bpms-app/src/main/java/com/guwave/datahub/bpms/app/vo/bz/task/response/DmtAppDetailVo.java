package com.guwave.datahub.bpms.app.vo.bz.task.response;

import cn.hutool.core.date.DateUtil;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.linkx.dao.mysql.domain.bz.BzAppInstanceRecord;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_FORMAT;

/**
 * 2024/10/25 14:15
 * DmtAppDetailVo
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(title = "单条任务详情，包括spark运行参数")
public class DmtAppDetailVo {
    @Schema(title = "id")
    private Long id;

    @Schema(title = "testArea", description = "CP、FT、SLT等", example = "CP")
    private String testArea;

    @Schema(title = "factory", description = "工厂", example = "TSMC")
    private String factory;

    @Schema(title = "deviceId", description = "deviceId", example = "deviceId1")
    private String deviceId;

    @Schema(title = "testStage", description = "testStage", example = "CP1")
    private String testStage;

    @Schema(title = "lotId", description = "lotId", example = "45rte")
    private String lotId;

    @Schema(title = "waferNo", description = "waferNo", example = "12")
    private String waferNo;

    @Schema(title = "lotType", description = "lotType", example = "PRODUCTION")
    private String lotType;

    @Schema(title = "processStatus", description = "计算状态", example = "FAIL")
    private String processStatus;

    @Schema(title = "optType", description = "任务操作类型：NEW_DATA、MODIFY_WAFERMAP_CONFIG", example = "NEW_DATA")
    private String optType;

    @Schema(title = "executeEngine", description = "执行引擎: CLICKHOUSE/SPARK/PYTHON", example = "SPARK")
    private String executeEngine;

    @Schema(title = "createTime", description = "任务开始时间", example = "2024-11-15 11:21:00")
    private String createTime;

    @Schema(title = "updateTime", description = "任务更新时间", example = "2024-11-18 11:21:00")
    private String updateTime;

    @Schema(title = "errorMessage", description = "错误信息", example = "FAIL")
    private String errorMessage;

    @Schema(title = "dmtTaskDetailVos", description = "具体计算任务列表")
    private List<DmtTaskDetailVo> dmtTaskDetailVos;

    public static DmtAppDetailVo of(BzAppInstanceRecord appInstanceRecord, List<DmtTaskDetailVo> dmtTaskDetailVos) {
        return DmtAppDetailVo.builder()
                .id(appInstanceRecord.getId())
                .testArea(TestArea.of(appInstanceRecord.getTestArea()))
                .factory(appInstanceRecord.getFactory())
                .deviceId(appInstanceRecord.getDeviceId())
                .testStage(appInstanceRecord.getTestStage())
                .lotId(appInstanceRecord.getLotId())
                .waferNo(appInstanceRecord.getWaferNo())
                .lotType(LotType.of(appInstanceRecord.getLotType()))
                .processStatus(appInstanceRecord.getProcessStatus() == null ? null : appInstanceRecord.getProcessStatus().name())
                .optType(appInstanceRecord.getOptType().getType())
                .executeEngine(appInstanceRecord.getExecuteEngine().getEngine())
                .createTime(DateUtil.format(appInstanceRecord.getCreateTime(), NORM_DATETIME_FORMAT))
                .updateTime(DateUtil.format(appInstanceRecord.getUpdateTime(), NORM_DATETIME_FORMAT))
                .errorMessage(appInstanceRecord.getErrorMessage())
                .dmtTaskDetailVos(dmtTaskDetailVos)
                .build();
    }
}
