package com.guwave.datahub.bpms.app.service.notification;

import com.alibaba.fastjson.JSON;
import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.vo.notification.NotificationRequestVo;
import com.guwave.datahub.bpms.app.vo.notification.NotificationResponseVo;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.onedata.dataware.common.contant.Constant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


@Service
public class NotificationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationService.class);
    private static final String SOURCE = "DATAWARE";


    @Value("${spring.notification.email}")
    private String notificationEmail;

    @Autowired
    private NotificationApi notificationApi;
    @Autowired
    private NotificationWithAttachmentApi notificationWithAttachmentApi;

    public void sendEmail(String code, Map<String, Object> parameters, List<File> files) {
        try {
            LOGGER.info("开始请求NotificationApi：{} {}", code, JSON.toJSONString(parameters));
            StopWatch sw = new StopWatch();
            sw.start();
            NotificationRequestVo notificationRequest = new NotificationRequestVo();
            notificationRequest.setSource(SOURCE);
            notificationRequest.setCode(code);
            notificationRequest.setParameters(parameters);
            notificationRequest.setToMails(Arrays.asList(notificationEmail.split(Constant.COMMA)));
            NotificationResponseVo<String> result;
            if (CollectionUtils.isEmpty(files)) {
                result = this.notificationApi.sendNotification(notificationRequest);
            } else {
                LOGGER.info("携带附件：{} 个", files.size());
                String form = JSON.toJSONString(notificationRequest);
                result = this.notificationWithAttachmentApi.sendNotificationWithAttachment(form, files);
            }
            sw.stop();
            LOGGER.info("结束请求NotificationApi, 结果: {}, 耗时: {}ms", JSON.toJSONString(result), sw.getTotalTimeMillis());
            if (!result.success()) {
                LOGGER.info("请求通知中心邮件发送失败: {} ", result.getMsg());
                throw new BpmsException(ResponseCode.Common.COMMON_REQUEST_NOTIFICATION_FAIL, result.getMsg());
            }
        } catch (BpmsException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.info("请求通知中心邮件发送失败: {}", e.getMessage(), e);
            throw new BpmsException(ResponseCode.Common.COMMON_REQUEST_NOTIFICATION_FAIL, e.getMessage());
        }
    }
}
