package com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.request;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(title = "数据完整性修复")
public class DataIntegrityRepairVo {

    private List<IntegrityRepairInfoDTO> repairInfos;

    @Schema(description = "备注", example = "123")
    private String remark;

    @Data
    public static class IntegrityRepairInfoDTO {
        private DataIntegrityDTO integrityInfo;

        private List<IntegrityRepairFieldDTO> repairFields;
    }

    @Data
    public static class DataIntegrityDTO {
        @Schema(description = "文件名", example = "a.stdf.zip")
        private String fileName;
    }

    @Data
    public static class IntegrityRepairFieldDTO {
        @Schema(description = "属性名", example = "LOT_ID")
        private String fieldName;
        @Schema(description = "原始值", example = "A")
        private String originalValue;
        @Schema(description = "修改后的值", example = "B")
        private String modifyValue;
    }
}
