package com.guwave.datahub.bpms.app.service.dailyReport.impl;

import com.guwave.datahub.bpms.app.service.dailyReport.CommonService;
import com.guwave.datahub.bpms.app.util.*;
import com.guwave.datahub.bpms.common.constant.ChartData;
import com.guwave.datahub.bpms.common.constant.ChartType;
import com.guwave.datahub.bpms.dao.repository.bpms.DailyReportResultRepository;
import com.guwave.datahub.bpms.dao.repository.dw.LotWaferCalRecordRepository;
import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.TimelinessResult;
import com.guwave.onedata.dataware.common.contant.TestArea;
import org.jfree.chart.plot.PlotOrientation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.UNDER_LINE;

@Service
public class TimelinessModuleService implements CommonService {

    @Autowired
    private LotWaferCalRecordRepository lotWaferCalRecordRepository;

    @Autowired
    private HdfsUtil hdfsUtil;

    private final List<String> headers = Arrays.asList("createTime", "test_area", "factory", "device_id", "lot_id", "wafer_no", "test_stage", "lot_type", "计算次数", "数据修复次数", "重播次数", "die数", "测项数", "最大的finisT", "ftp文件最小mtime", "ftp文件最大mtime", "等待扫描时间", "ftp最后一个文件下载起始时间", "上传到下载时间", "所有文件脚本解析时间总和", "下载完到预解析gap", "最后一个文件预解析起始时间", "所有文件预解析时间总和", "预解析完到正式解析的gap", "最后一个文件正式解析起始时间", "所有文件正式解析时间总和", "正式解析完到die和rule计算的gap", "die和rule计算起始时间", "die和rule计算时间", "die和rule计算完到test_item的gap", "test_item计算起始时间", "test_item计算时间", "die和rule总耗时(包含等待)", "die和rule总耗时(包含等待,去掉collectx拉文件时间)", "总耗时(包含等待)", "总耗时(包含等待,去掉collectx拉文件时间)");


    public void generateReplayDetail(PdfUtil pdfUtil, String localDir, String hdfsDir) throws IOException {
        List<TimelinessResult> timelinessResults = lotWaferCalRecordRepository.calculateTimeliness(DateUtil.getStringFormattedStartTimeBeforeDays(1), DateUtil.getStringFormattedStartTimeBeforeDays(0))
                .stream().map(this::buildTimelinessResult).collect(Collectors.toList());

        List<ChartData> timelinessResultsChartData = new ArrayList<>();
        List<TimelinessResult> timelinessResultsTop10 = timelinessResults.size() > 10 ? timelinessResults.subList(0, 10) : timelinessResults;
        timelinessResultsChartData.addAll(timelinessResultsTop10.stream().map(t -> new ChartData(TestArea.of(t.getTestArea()).getTestScope().equalsIgnoreCase(TestArea.FT.getTestScope()) ? t.getLotId() : t.getLotId() + UNDER_LINE + t.getWaferNo(),
                t.getScanWaitDuration(),
                "等待扫描时间")).collect(Collectors.toList()));
        timelinessResultsChartData.addAll(timelinessResultsTop10.stream().map(t -> new ChartData(TestArea.of(t.getTestArea()).getTestScope().equalsIgnoreCase(TestArea.FT.getTestScope()) ? t.getLotId() : t.getLotId() + UNDER_LINE + t.getWaferNo(),
                t.getUploadToDownloadDuration(),
                "上传到下载时间")).collect(Collectors.toList()));
        timelinessResultsChartData.addAll(timelinessResultsTop10.stream().map(t -> new ChartData(TestArea.of(t.getTestArea()).getTestScope().equalsIgnoreCase(TestArea.FT.getTestScope()) ? t.getLotId() : t.getLotId() + UNDER_LINE + t.getWaferNo(),
                t.getTotalFileScriptParseDuration(),
                "所有文件脚本解析时间总和")).collect(Collectors.toList()));
        timelinessResultsChartData.addAll(timelinessResultsTop10.stream().map(t -> new ChartData(TestArea.of(t.getTestArea()).getTestScope().equalsIgnoreCase(TestArea.FT.getTestScope()) ? t.getLotId() : t.getLotId() + UNDER_LINE + t.getWaferNo(),
                t.getDownloadToPreparseGap(),
                "下载完到预解析gap")).collect(Collectors.toList()));
        timelinessResultsChartData.addAll(timelinessResultsTop10.stream().map(t -> new ChartData(TestArea.of(t.getTestArea()).getTestScope().equalsIgnoreCase(TestArea.FT.getTestScope()) ? t.getLotId() : t.getLotId() + UNDER_LINE + t.getWaferNo(),
                t.getTotalPreparseDuration(),
                "所有文件预解析时间总和")).collect(Collectors.toList()));
        timelinessResultsChartData.addAll(timelinessResultsTop10.stream().map(t -> new ChartData(TestArea.of(t.getTestArea()).getTestScope().equalsIgnoreCase(TestArea.FT.getTestScope()) ? t.getLotId() : t.getLotId() + UNDER_LINE + t.getWaferNo(),
                t.getPreparseToParseGap(),
                "预解析完到正式解析的gap")).collect(Collectors.toList()));
        timelinessResultsChartData.addAll(timelinessResultsTop10.stream().map(t -> new ChartData(TestArea.of(t.getTestArea()).getTestScope().equalsIgnoreCase(TestArea.FT.getTestScope()) ? t.getLotId() : t.getLotId() + UNDER_LINE + t.getWaferNo(),
                t.getTotalParseDuration(),
                "所有文件正式解析时间总和")).collect(Collectors.toList()));
        timelinessResultsChartData.addAll(timelinessResultsTop10.stream().map(t -> new ChartData(TestArea.of(t.getTestArea()).getTestScope().equalsIgnoreCase(TestArea.FT.getTestScope()) ? t.getLotId() : t.getLotId() + UNDER_LINE + t.getWaferNo(),
                t.getParseToDieGap(),
                "正式解析完到die和rule计算的gap")).collect(Collectors.toList()));
        timelinessResultsChartData.addAll(timelinessResultsTop10.stream().map(t -> new ChartData(TestArea.of(t.getTestArea()).getTestScope().equalsIgnoreCase(TestArea.FT.getTestScope()) ? t.getLotId() : t.getLotId() + UNDER_LINE + t.getWaferNo(),
                t.getDieCalculationDuration(),
                "die和rule计算时间")).collect(Collectors.toList()));
        timelinessResultsChartData.addAll(timelinessResultsTop10.stream().map(t -> new ChartData(TestArea.of(t.getTestArea()).getTestScope().equalsIgnoreCase(TestArea.FT.getTestScope()) ? t.getLotId() : t.getLotId() + UNDER_LINE + t.getWaferNo(),
                t.getDieToTestitemGap(),
                "die和rule计算完到test_item的gap")).collect(Collectors.toList()));
        timelinessResultsChartData.addAll(timelinessResultsTop10.stream().map(t -> new ChartData(TestArea.of(t.getTestArea()).getTestScope().equalsIgnoreCase(TestArea.FT.getTestScope()) ? t.getLotId() : t.getLotId() + UNDER_LINE + t.getWaferNo(),
                t.getTestitemCalculationDuration(),
                "test_item计算时间")).collect(Collectors.toList()));

        pdfUtil.addChart(timelinessResultsChartData, ChartType.STACKED_BAR, "TOP10慢任务", "lot/wafer", "耗时", PlotOrientation.HORIZONTAL);

        writeTimelinessDetail(timelinessResults, localDir, hdfsDir);
    }

    private TimelinessResult buildTimelinessResult(Object[] objects) {
        return new TimelinessResult(
                (new Date(((Timestamp) objects[0]).getTime())),
                ((String) objects[1]),
                ((String) objects[2]),
                ((String) objects[3]),
                ((String) objects[4]),
                ((String) objects[5]),
                ((String) objects[6]),
                ((String) objects[7]),
                ((Number) objects[8]).longValue(),
                ((Number) objects[9]).longValue(),
                ((Number) objects[10]).longValue(),
                ((Number) objects[11]).longValue(),
                ((Number) objects[12]).longValue(),
                (new Date(((Timestamp) objects[13]).getTime())),
                (new Date(((Timestamp) objects[14]).getTime())),
                (new Date(((Timestamp) objects[15]).getTime())),
                ((Number) objects[16]).longValue(),
                ((String) objects[17]),
                ((Number) objects[18]).longValue(),
                ((Number) objects[19]).longValue(),
                ((Number) objects[20]).longValue(),
                ((String) objects[21]),
                ((Number) objects[22]).longValue(),
                ((Number) objects[23]).longValue(),
                ((String) objects[24]),
                ((Number) objects[25]).longValue(),
                ((Number) objects[26]).longValue(),
                ((String) objects[27]),
                ((Number) objects[28]).longValue(),
                ((Number) objects[29]).longValue(),
                ((String) objects[30]),
                ((Number) objects[31]).longValue(),
                ((Number) objects[32]).longValue(),
                ((Number) objects[33]).longValue(),
                ((Number) objects[34]).longValue(),
                ((Number) objects[35]).longValue()
        );
    }

    private void writeTimelinessDetail(List<TimelinessResult> timelinessResults, String localDir, String hdfsDir) throws IOException {
        // 所有数据保存为xlsx并上传至HDFS
        String timelinessFile = FileUtil.getFileFullName(localDir, "自动入库时效性明细" + UNDER_LINE + DateUtil.getDayBefortDays(1) + ".xlsx");
        List<String> fileNames = Arrays.stream(TimelinessResult.class.getDeclaredFields()).map(Field::getName).collect(Collectors.toList());
        ExcelUtil.exportWithHeader(Files.newOutputStream(Paths.get(timelinessFile)), headers, fileNames, timelinessResults, TimelinessResult.class);
        hdfsUtil.uploadToHDFSByFileSystem(timelinessFile, hdfsDir, 1);
    }

    @Override
    public DailyReportResultRepository getDailyReportResultRepository() {
        return null;
    }
}
