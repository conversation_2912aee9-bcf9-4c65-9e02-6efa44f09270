package com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Schema(title = "下拉框结果")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClearRulePreviewDropDownVo {
    private static final Pattern NUMBER_PATTERN = Pattern.compile("-?\\d+");
    @Schema(title = "下拉框结果值", example = "NT41A")
    private String name;
    @Schema(title = "页面显示下拉框结果值", example = "NT41A")
    private String showName;
    @Schema(title = "是否可以被选", example = "true")
    private Boolean selectable;

    public static ClearRulePreviewDropDownVo of(String name) {
        return ClearRulePreviewDropDownVo.of(name, name);
    }

    public static ClearRulePreviewDropDownVo of(String name, String showName) {
        return new ClearRulePreviewDropDownVo(name, showName, true);
    }

    public static ClearRulePreviewDropDownVo of(String name, String showName, Boolean selectable) {
        return new ClearRulePreviewDropDownVo(name, showName, selectable);
    }

    public static List<ClearRulePreviewDropDownVo> ofList(List<String> names) {
        if (CollectionUtils.isEmpty(names)) {
            return Collections.emptyList();
        }
        boolean isNumer = names.stream().allMatch(t -> StringUtils.isNotBlank(t) && NUMBER_PATTERN.matcher(t).matches());
        return names.stream()
                .map(ClearRulePreviewDropDownVo::of)
                .sorted(isNumer ? Comparator.comparingInt(t -> Integer.parseInt(t.getShowName())) : Comparator.comparing(ClearRulePreviewDropDownVo::getShowName))
                .collect(Collectors.toList());
    }
}
