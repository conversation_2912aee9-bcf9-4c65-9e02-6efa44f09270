package com.guwave.datahub.bpms.app.vo.bz.task.request;

import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/11
 * @description BzAppInstanceVo
 */

@Data
@EqualsAndHashCode(callSuper=false)
@NoArgsConstructor
@Schema(title = "task YMS使用task详细信息查询")
public class YmsTaskQueryVo  extends PageableRequestVo {
    @Schema(title = "optTypeList", description = "任务操作类型：NEW_DATA、MODIFY_WAFERMAP_CONFIG")
    private List<String> optTypeList;

    @Schema(title = "executeEngineList", description = "执行引擎: CLICKHOUSE/SPARK/PYTHON")
    private List<String> executeEngineList;

    @Schema(title = "testAreaList", description = "CP、FT、SLT等")
    private List<String> testAreaList;

    @Schema(title = "factoryList", description = "工厂")
    private List<String> factoryList;

    @Schema(title = "deviceIdList", description = "deviceId")
    private List<String> deviceIdList;

    @Schema(title = "testStageList", description = "testStage")
    private List<String> testStageList;

    @Schema(title = "lotIdList", description = "lotId")
    private List<String> lotIdList;

    @Schema(title = "waferNoList", description = "waferNo")
    private List<String> waferNoList;

    @Schema(title = "lotTypeList", description = "lotType")
    private List<String> lotTypeList;

    @Schema(title = "processStatusList", description = "计算状态")
    private List<String> processStatusList;
}
