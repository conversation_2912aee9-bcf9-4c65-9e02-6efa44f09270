package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(title = "关键字段取值规则")
public class DwSourceStandardFieldRuleRequestSaveVo {
    @Schema(description = "关键字段取值规则列表")
    List<DwSourceStandardFieldRuleRequestVo> ruleRequestVos;
    @Schema(description = "入库规则流程")
    WarehousingRuleFlowRequestVo warehouseRuleFlowRequestVo;

}
