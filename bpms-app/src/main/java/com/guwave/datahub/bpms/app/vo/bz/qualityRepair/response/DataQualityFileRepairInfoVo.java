package com.guwave.datahub.bpms.app.vo.bz.qualityRepair.response;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.RepairFieldInfoVo;
import com.guwave.datahub.bpms.common.constant.Constant;
import com.guwave.datahub.bpms.common.constant.RepairCategoryEnum;
import com.guwave.datahub.bpms.dao.domain.qualityOverview.IntegrityRepairInfo;
import com.guwave.datahub.bpms.dao.domain.repair.DataRepairHistory;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.List;

@Data
public class DataQualityFileRepairInfoVo {

    @Schema(title = "id", example = "123")
    private Long id;

    @Schema(title = "customer", example = "guwave")
    private String customer;

    @Schema(title = "subCustomer", example = "guwave")
    private String subCustomer;

    @Schema(title = "处理批次",  example = "123")
    private Long repairBatchId;

    @Schema(title = "fileId", example = "1001")
    private Long fileId;

    @Schema(title = "文件名称")
    private String fileName;

    @Schema(title = "sblotId")
    private String sblotId;

    @Schema(title = "waferId")
    private String waferId;

    @Schema(title = "testArea")
    private String testArea;

    @Schema(title = "修改类别")
    private String repairCategory;

    @Schema(title = "修改类型")
    private String lastRepairType;

    @Schema(title = "修改状态")
    private String repairStatus;

    @Schema(title = "修改异常信息")
    private Integer repairErrorInfo;

    @Schema(title = "修改次数")
    private Integer repairTimes;

    @Schema(title = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @Schema(title = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @Schema(title = "remark")
    private String remark;

    @Schema(title = "删除标志")
    private Boolean deleteFlag;

    @Schema(title = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(title = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Schema(title = "创建用户")
    private String createUser;

    @Schema(title = "修改用户")
    private String updateUser;

    @Schema(title = "修改前数据")
    private String originalInfo;

    @Schema(title = "修改后数据")
    private String modifyInfo;

    @Schema(title = "修改原因")
    private String modifyRemark;

    public static DataQualityFileRepairInfoVo of(DataRepairHistory history) {
        DataQualityFileRepairInfoVo vo = new DataQualityFileRepairInfoVo();
        BeanUtils.copyProperties(history, vo);

        if (history.getRepairCategory().equals(RepairCategoryEnum.DATA_ACCURACY.name())) {
            JSONObject map = JSON.parseObject(history.getRepairJson());
            if (map != null && map.containsKey(Constant.REPAIRINFO_STR)) {
                List<RepairFieldInfoVo> repairInfoList = JSONArray.parseArray(map.get(Constant.REPAIRINFO_STR).toString(), RepairFieldInfoVo.class);
                StringBuilder originalInfo = new StringBuilder();
                StringBuilder modifyInfo = new StringBuilder();
                StringBuilder modifyRemark = new StringBuilder();

                if (repairInfoList != null) {
                    for (RepairFieldInfoVo info : repairInfoList) {
                        if (StringUtils.isNotEmpty(info.getModifyValue())) {
                            originalInfo.append(info.getFieldName()).append(Constant.COLON).append(info.getOriginalValue()).append(Constant.SEMICOLON);
                            modifyInfo.append(info.getFieldName()).append(Constant.COLON).append(info.getModifyValue()).append(Constant.SEMICOLON);
                            modifyRemark.append(info.getModifyRemark()).append(Constant.SEMICOLON);
                        }
                    }
                }

                // 比较flowId
                if (ObjectUtils.notEqual(history.getOriginalFlowId(), history.getModifyFlowId())) {
                    originalInfo.append(Constant.FLOW_ID_STR).append(Constant.COLON).append(history.getOriginalFlowId()).append(Constant.SEMICOLON);
                    modifyInfo.append(Constant.FLOW_ID_STR).append(Constant.COLON).append(history.getModifyFlowId()).append(Constant.SEMICOLON);
                }

                // 比较Retest Hbin Number
                if (ObjectUtils.notEqual(history.getOriginalRetestHbinNumber(), history.getModifyRetestHbinNumber())) {
                    originalInfo.append(Constant.RETEST_HBIN_NUMBER_STR).append(Constant.COLON).append(history.getOriginalRetestHbinNumber()).append(Constant.SEMICOLON);
                    modifyInfo.append(Constant.RETEST_HBIN_NUMBER_STR).append(Constant.COLON).append(history.getModifyRetestHbinNumber()).append(Constant.SEMICOLON);
                }

                vo.setOriginalInfo(trimSemicolon(originalInfo));
                vo.setModifyInfo(trimSemicolon(modifyInfo));
                vo.setModifyRemark(trimSemicolon(modifyRemark));
            }
        } else {
            JSONObject map = JSONObject.parseObject(history.getRepairJson());
            if (map != null && map.containsKey(Constant.REPAIRINFO_STR)) {
                IntegrityRepairInfo Vo = map.getObject(Constant.REPAIRINFO_STR, IntegrityRepairInfo.class);
                String remark = map.getString(Constant.REMARK_STR);

                StringBuilder originalInfo = new StringBuilder();
                StringBuilder modifyInfo = new StringBuilder();
                StringBuilder modifyRemark = new StringBuilder();
                if (StringUtils.isNotBlank(remark)) {
                    modifyRemark.append(remark);
                }
                Vo.getRepairFields().forEach(info -> {
                    originalInfo.append(info.getFieldName()).append(Constant.COLON).append(info.getOriginValue()).append(Constant.SEMICOLON);
                    modifyInfo.append(info.getFieldName()).append(Constant.COLON).append(info.getModifyValue()).append(Constant.SEMICOLON);
                });

                vo.setOriginalInfo(trimSemicolon(originalInfo));
                vo.setModifyInfo(trimSemicolon(modifyInfo));
                vo.setModifyRemark(StringUtils.defaultString(modifyRemark.toString()));
            }
        }

        return vo;
    }

    private static String trimSemicolon(StringBuilder sb) {
        if (sb.length() > 0 && sb.toString().endsWith(Constant.SEMICOLON)) {
            return sb.substring(0, sb.length() - 1);
        }
        return sb.toString();
    }
}