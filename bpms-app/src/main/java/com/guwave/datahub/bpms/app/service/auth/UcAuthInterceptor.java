package com.guwave.datahub.bpms.app.service.auth;

import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import com.guwave.datahub.bpms.common.constant.Constant;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * UcAuthInterceptor
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-27 14:51:51
 */
@Component
public class UcAuthInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        ServletRequestAttributes requestAttributes = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes());
        String token = null;
        if (null != requestAttributes) {
            HttpServletRequest request = requestAttributes.getRequest();
            token = request.getHeader(Constant.AUTH_HEADER_FIELD_NAME);
            if (null == token) {
                token = request.getParameter(Constant.AUTH_HEADER_FIELD_NAME);
            }
        }
        if (null == token) {
            token = RequestContext.getToken();
        }
        requestTemplate.header(Constant.AUTH_HEADER_FIELD_NAME, token);
    }
}
