package com.guwave.datahub.bpms.app.service.dailyReport;

import com.guwave.datahub.bpms.app.service.dailyReport.impl.*;
import com.guwave.datahub.bpms.app.util.DateUtil;
import com.guwave.datahub.bpms.app.util.FileUtil;
import com.guwave.datahub.bpms.app.util.HdfsUtil;
import com.guwave.datahub.bpms.app.util.PdfUtil;
import com.guwave.datahub.bpms.dao.domain.dailyReport.DailyReportRecord;
import com.guwave.datahub.bpms.dao.repository.bpms.DailyReportRecordRepository;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Date;
import java.util.UUID;

import static com.guwave.datahub.bpms.common.constant.Constant.SLASH;

@Service
public class DailyReportService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DailyReportService.class);

    @Autowired
    private DailyReportRecordRepository dailyReportRecordRepository;

    @Value("${spring.handler.file.dailyReportReadPath}")
    private String readPath;
    @Value("${spring.handler.font.path}")
    private String fontPath;

    @Value("${spring.hdfs.dailyReportDir}")
    private String dailyReportDir;

    @Autowired
    private WarehousingModuleService warehousingModuleService;
    @Autowired
    private YmsModifyModuleService ymsModifyModuleService;
    @Autowired
    private SparkTaskModuleService sparkTaskModuleService;
    @Autowired
    private AbnormalDataModuleService abnormalDataModuleService;

    @Autowired
    private CollectxPullingModuleService collectxPullingModuleService;

    @Autowired
    private GdpClusterModuleService gdpClusterModuleService;

    @Autowired
    private ManualModuleService manualModuleService;

    @Autowired
    private RepairModuleService repairModuleService;

    @Autowired
    private ReplayModuleService replayModuleService;

    @Autowired
    private TimelinessModuleService timelinessModuleService;

    @Autowired
    private HdfsUtil hdfsUtil;


    public void generateDailyReport(DailyReportRecord dailyReportRecord) {
        PdfUtil pdfUtil;
        boolean failFlag = false;
        String errorMessage = null;
        String day = DateUtil.getDayBefortDays(1);
        LOGGER.info("生成{}的日报", day);

        String localDir = FileUtil.getFileFullName(readPath, UUID.randomUUID().toString());
        String hdfsDir = FileUtil.getFileFullName(dailyReportDir, day + SLASH);
        String pdfFile = getPdfFilePath(day, localDir);
        try {
            pdfUtil = new PdfUtil(fontPath, localDir);

            pdfUtil.addPrimaryHeading(day + "大数据日报");

            warehousingModuleService.generateWarehousingLotWaferDetail(pdfUtil);
            warehousingModuleService.generateWarehousingSuccessFileDetail(pdfUtil);
            repairModuleService.generateRepairDetail(pdfUtil);
            replayModuleService.generateReplayDetail(pdfUtil);

            warehousingModuleService.generateWarehousingSuccessFileChart(pdfUtil);
            repairModuleService.generateRepairTaskChart(pdfUtil);
            replayModuleService.generateReplayDetailChart(pdfUtil);
            warehousingModuleService.generateWarehousingFailFileDetail(pdfUtil);
            timelinessModuleService.generateReplayDetail(pdfUtil, localDir, hdfsDir);

            ymsModifyModuleService.generateYmsModifyDetail(pdfUtil);
            sparkTaskModuleService.generateSparkFailTaskDetail(pdfUtil);
            collectxPullingModuleService.generateCollectxPullingDetail(pdfUtil, localDir, hdfsDir);

            gdpClusterModuleService.generateGdpClusterDetail(pdfUtil);

            manualModuleService.generateManualTaskDetail(pdfUtil);

            abnormalDataModuleService.generateAbnormalDataDetal(pdfUtil, localDir, hdfsDir);

            pdfUtil.saveDocument(pdfFile);

            hdfsUtil.uploadToHDFSByFileSystem(pdfFile, hdfsDir, 1);
            LOGGER.info("日报生成完成");
        } catch (Exception e) {
            failFlag = true;
            LOGGER.info("日报生成失败");
            errorMessage = e.toString();
            throw new RuntimeException(e);
        } finally {
            FileUtil.deleteDir(new File(localDir));
            // 重置初始化标志
            resetInitFlag();
            udpateDailyReportRecord(dailyReportRecord, failFlag, errorMessage);
        }
    }

    private void udpateDailyReportRecord(DailyReportRecord dailyReportRecord, boolean failFlag, String errorMessage) {
        dailyReportRecord.setEndTime(new Date())
                .setUpdateTime(new Date())
                .setProcessStatus(failFlag ? ProcessStatus.FAIL : ProcessStatus.SUCCESS)
                .setErrorMessage(failFlag ? errorMessage : Constant.EMPTY);
        dailyReportRecordRepository.save(dailyReportRecord);
    }

    private String getPdfFilePath(String day, String localDir) {
        File file = new File(localDir);
        file.mkdirs();
        return FileUtil.getFileFullName(readPath, "bigdata_daily_report_" + day + ".pdf");
    }

    private void resetInitFlag() {
        warehousingModuleService.resetInitFlag();
        replayModuleService.resetInitFlag();
        repairModuleService.resetInitFlag();
    }
}
