package com.guwave.datahub.bpms.app.executor.impl;


import com.guwave.datahub.bpms.app.executor.Executor;
import com.guwave.datahub.bpms.common.constant.ExecuteEngine;
import com.guwave.datahub.bpms.dao.domain.schedule.ScheduleTaskRecord;
import com.guwave.datahub.bpms.dao.repository.bpms.ScheduleTaskRecordRepository;
import com.guwave.onedata.next.compute.api.iface.IComputeRpcService;
import com.guwave.onedata.next.compute.api.vo.response.ComputeResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.guwave.datahub.bpms.common.constant.Constant.HYPHEN;

/**
 * <AUTHOR>
 * @date 2024/4/25
 * @description SparkExecutor
 */
@Component
@Qualifier("batchExecutors")
public class SparkExecutor implements Executor {
    private final Logger LOGGER = LoggerFactory.getLogger(SparkExecutor.class);

    @Autowired
    private ScheduleTaskRecordRepository scheduleTaskRecordRepository;
    @DubboReference
    private IComputeRpcService computeRpcService;

    @Override
    public Boolean support(ExecuteEngine executeEngine) {
        return executeEngine == ExecuteEngine.SPARK;
    }

    @Override
    public ComputeResponse doExecute(ScheduleTaskRecord taskRecord) throws Exception {
        String command = taskRecord.getCommand();
        Map<String, String> params = generateParams(command);
        String appName = generateAppName(command);
        ComputeResponse computeResponse = computeRpcService.submit(
                command,
                appName,
                Long.parseLong(params.get("dieCnt")),
                Long.parseLong(params.get("testItemCnt")),
                params
        );
        return computeResponse;
    }

    private String generateAppName(String command) {
        return command + HYPHEN + System.currentTimeMillis();
    }

    private Map<String, String> generateParams(String command) {
        Map<String, String> params = new HashMap<>();
        params.put("dieCnt", "10000000");
        params.put("testItemCnt", "10000000");
        params.put("command", command);
        return params;
    }
}
