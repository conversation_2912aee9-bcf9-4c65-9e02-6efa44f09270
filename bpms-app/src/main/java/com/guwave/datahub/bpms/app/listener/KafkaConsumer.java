package com.guwave.datahub.bpms.app.listener;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.guwave.datahub.bpms.app.service.bz.DataRepairService;
import com.guwave.datahub.bpms.app.service.bz.DataReplayService;
import com.guwave.onedata.dataware.common.contant.DwLayer;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.Platform;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.model.message.CalculateEndFlag;
import com.guwave.onedata.dataware.common.model.message.DataRepairFinishMessage;
import com.guwave.onedata.dataware.common.model.message.DataRepairRecalculateMessage;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * KafkaConsumer
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2021-12-29 11:10:41
 */
@Component
@AllArgsConstructor
public class KafkaConsumer {

    private static final Logger LOGGER = LoggerFactory.getLogger(KafkaConsumer.class);

    private DataReplayService dataReplayService;
    private DataRepairService dataRepairService;

    static List<FileCategory> SINGLE_FILE_SUPPORT_FILE_CATEGORY = Lists.newArrayList(FileCategory.WAT, FileCategory.SUMMARY);

    @KafkaListener(topics = "${spring.kafka.loadEndFlagTopic}")
    public void loadEndFlagConsumer(ConsumerRecord<byte[], byte[]> record) {
        try {
            String jsonStr = new String(record.value());
            CalculateEndFlag calculateEndFlag = JSON.parseObject(jsonStr, CalculateEndFlag.class);
            LOGGER.info("loadEndFlagTopic,topic:{}, kafka数据:{}", record.topic(), jsonStr);
            if (CollectionUtils.isNotEmpty(calculateEndFlag.getCleanupTaskIds()) && calculateEndFlag.getCleanupTaskIds().stream().anyMatch(Objects::nonNull) && calculateEndFlag.getPlatform() == Platform.CK) {
                if (calculateEndFlag.getProcessStatus() == ProcessStatus.FAIL || SINGLE_FILE_SUPPORT_FILE_CATEGORY.contains(calculateEndFlag.getFileCategory())) {
                    // 中途失败消息或者单文件成功结束消息
                    dataReplayService.updateReplayTask(
                            calculateEndFlag.getCleanupTaskIds().stream().filter(Objects::nonNull).collect(Collectors.toList()),
                            calculateEndFlag.getProcessStatus(),
                            calculateEndFlag.getExceptionType(),
                            calculateEndFlag.getExceptionMessage(),
                            calculateEndFlag.getErrorMessage()
                    );
                }
            }
            LOGGER.info("处理loadEndFlagTopic kafka数据结束...");
        } catch (Exception e) {
            LOGGER.info("处理loadEndFlagTopic kafka数据异常", e);
        }
    }

    @KafkaListener(topics = "${spring.kafka.calculateEndFlagTopic}")
    public void calculateEndFlagConsumer(ConsumerRecord<byte[], byte[]> record) {
        try {
            String jsonStr = new String(record.value());
            CalculateEndFlag calculateEndFlag = JSON.parseObject(jsonStr, CalculateEndFlag.class);
            LOGGER.info("开始处理calculateEndFlagTopic,topic:{}, kafka数据:{}", record.topic(), jsonStr);
            if (CollectionUtils.isNotEmpty(calculateEndFlag.getCleanupTaskIds()) && calculateEndFlag.getCleanupTaskIds().stream().anyMatch(Objects::nonNull) && calculateEndFlag.getPlatform() == Platform.CK) {
                if (calculateEndFlag.getProcessStatus() == ProcessStatus.FAIL || calculateEndFlag.getDwLayer() == DwLayer.ADS) {
                    // testItem数仓中途失败消息或者ADS任务执行结束消息
                    dataReplayService.updateReplayTask(
                            calculateEndFlag.getCleanupTaskIds().stream().filter(Objects::nonNull).collect(Collectors.toList()),
                            calculateEndFlag.getProcessStatus(),
                            calculateEndFlag.getExceptionType(),
                            calculateEndFlag.getExceptionMessage(),
                            calculateEndFlag.getErrorMessage()
                    );
                }
            }
            LOGGER.info("处理calculateEndFlagTopic kafka数据结束...");
        } catch (Exception e) {
            LOGGER.info("处理calculateEndFlagTopic kafka数据异常", e);
        }
    }

    @KafkaListener(topics = "${spring.kafka.repairFinishTopic}")
    public void repairFinishedListener(ConsumerRecord<byte[], byte[]> reocrd) {
        try {
            DataRepairFinishMessage message = JSON.parseObject(reocrd.value(), DataRepairFinishMessage.class);
            LOGGER.info("开始处理repairFinished message: {}", JSON.toJSONString(message));
            dataRepairService.repairFinishedHandler(message);
            LOGGER.info("处理完成repairFinished message: {}", JSON.toJSONString(message));
        } catch (Exception e) {
            LOGGER.error("repairFinished消息处理异常", e);
        }
    }

    @KafkaListener(topics = "${spring.kafka.repairRecalculateTopic}")
    public void repairRecalculateListener(ConsumerRecord<byte[], byte[]> reocrd) {
        try {
            DataRepairRecalculateMessage message = JSON.parseObject(reocrd.value(), DataRepairRecalculateMessage.class);
            LOGGER.info("开始处理repairRecalculate message: {}", JSON.toJSONString(message));
            dataRepairService.repairRecalculateHandler(message);
            LOGGER.info("处理完成repairRecalculate message: {}", JSON.toJSONString(message));
        } catch (Exception e) {
            LOGGER.error("repairRecalculate消息处理异常", e);
        }
    }

}
