package com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model;

import cn.hutool.core.date.DateUtil;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.DataClearRuleType;
import com.guwave.onedata.dataware.common.contant.StdfFieldType;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.*;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_FORMAT;

@Data
public class SettingSnapshot {
    private List<SourceStandardDeviceIdRuleSnapshot> dwSourceStandardDeviceIdRules = new ArrayList<>();
    private List<SourceStandardFieldRuleSnapshot> dwSourceStandardFieldRules = new ArrayList<>();
    private List<DataClearRuleSnapshot> dwDataClearRules = new ArrayList<>();
    private List<EcidRuleSnapshot> dwEcidRules = new ArrayList<>();
    private List<LotRelationSyncSnapshot> dwLotRelationSyncs = new ArrayList<>();
    private List<UidRuleSnapshot> dwUidRules = new ArrayList<>();

    @Data
    @AllArgsConstructor
    public static class SourceStandardDeviceIdRuleSnapshot {
        private String ruleExpression;
        private String createTime;
        private String updateTime;

        public static SourceStandardDeviceIdRuleSnapshot of(SourceStandardDeviceIdRule source) {
            return new SourceStandardDeviceIdRuleSnapshot(source.getRuleExpression(), DateUtil.format(source.getCreateTime(), NORM_DATETIME_FORMAT), DateUtil.format(source.getUpdateTime(), NORM_DATETIME_FORMAT));
        }
    }

    @Data
    @AllArgsConstructor
    public static class SourceStandardFieldRuleSnapshot {
        private String field;
        private String ruleExpression;
        private String createTime;
        private String updateTime;

        public static SourceStandardFieldRuleSnapshot of(SourceStandardFieldRule source) {
            return new SourceStandardFieldRuleSnapshot(source.getField(), source.getRuleExpression(), DateUtil.format(source.getCreateTime(), NORM_DATETIME_FORMAT), DateUtil.format(source.getUpdateTime(), NORM_DATETIME_FORMAT));
        }
    }

    @Data
    @AllArgsConstructor
    public static class DataClearRuleSnapshot {
        private StdfFieldType fieldType;
        private DataClearRuleType ruleType;
        private String rule;
        private String createTime;
        private String updateTime;

        public static DataClearRuleSnapshot of(DataClearRule source) {
            return new DataClearRuleSnapshot(source.getFieldType(), source.getRuleType(), source.getRule(), DateUtil.format(source.getCreateTime(), NORM_DATETIME_FORMAT), DateUtil.format(source.getUpdateTime(), NORM_DATETIME_FORMAT));
        }
    }

    @Data
    @AllArgsConstructor
    public static class EcidRuleSnapshot {
        private String testProgram;
        private String script;
        private String decodeEcid;
        private String decodeEcidFields;
        private String decodeLotId;
        private String parameterWaferNo;
        private String parameterXAddr;
        private String parameterYAddr;
        private String ruleType;
        private String checkType;
        private String parameterType;
        private String fieldPrefix;
        private String ruleMode;
        private String createTime;
        private String updateTime;

        public static EcidRuleSnapshot of(EcidRule source) {
            return new EcidRuleSnapshot(convertNullStr(source.getTestProgram()), convertNullStr(source.getScript()), convertNullStr(source.getDecodeEcid()), convertNullStr(source.getDecodeEcidFields()),
                    convertNullStr(source.getDecodeLotId()), convertNullStr(source.getParameterWaferNo()), convertNullStr(source.getParameterXAddr()), convertNullStr(source.getParameterYAddr()),
                    convertNullStr(source.getRuleType()), convertNullStr(source.getCheckType()), convertNullStr(source.getParameterType()), convertNullStr(source.getFieldPrefix()),
                    convertNullStr(source.getRuleMode()), DateUtil.format(source.getCreateTime(), NORM_DATETIME_FORMAT), DateUtil.format(source.getUpdateTime(), NORM_DATETIME_FORMAT));
        }
    }



    @Data
    @AllArgsConstructor
    public static class LotRelationSyncSnapshot {
        private String cpLotId;
        private String createTime;
        private String updateTime;

        public static LotRelationSyncSnapshot of(LotRelationSync source) {
            return new LotRelationSyncSnapshot(convertNullStr(source.getCpLotId()), DateUtil.format(source.getCreateTime(), NORM_DATETIME_FORMAT), DateUtil.format(source.getUpdateTime(), NORM_DATETIME_FORMAT));
        }
    }


    @Data
    @AllArgsConstructor
    public static class UidRuleSnapshot {
        private String parameterUid;
        private String parameterType;
        private String matchType;
        private String concatSign;
        private String ruleType;
        private String createTime;
        private String updateTime;

        public static UidRuleSnapshot of(UidRule source) {
            return new UidRuleSnapshot(convertNullStr(source.getParameterUid()), convertNullStr(source.getParameterType()), convertNullStr(source.getMatchType()), convertNullStr(source.getConcatSign()),
                    convertNullStr(source.getRuleType()), DateUtil.format(source.getCreateTime(), NORM_DATETIME_FORMAT), DateUtil.format(source.getUpdateTime(), NORM_DATETIME_FORMAT));
        }
    }

    private static String convertNullStr(String str) {
        if (str == null) {
            return Constant.EMPTY;
        }
        return str;
    }
}
