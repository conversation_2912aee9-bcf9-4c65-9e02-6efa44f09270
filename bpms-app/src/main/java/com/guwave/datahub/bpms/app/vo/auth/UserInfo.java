package com.guwave.datahub.bpms.app.vo.auth;

import com.guwave.datahub.bpms.common.constant.Constant;
import lombok.Data;

import java.util.List;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * UserInfo
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-27 11:00:07
 */
@Data
public class UserInfo {

    /**
     * - 是否管理员
     */
    private boolean admin;
    /**
     * - 是否系统操作
     */
    private boolean system;

    /**
     * - 是否系统调用，但是携带了用户信息
     */
    private boolean systemWithUserInfo;

    /**
     * - 如果是系統調用，所屬appId
     */
    private String appId;

    /**
     * - 用户ID，目前不使用id
     */
    private Long userId;

    /**
     * - 用户登录名，是唯一的。目前使用userName唯一标识用户
     */
    private String userName;

    /**
     * - 用户所属租户
     */
    private String customer;

    /**
     * - 用户所属部门
     */
    private String department = Constant.EMPTY;

    /**
     * - 用户所属子租户
     */
    private String subCustomer;

    /**
     * - 用于子线程调用其他服务接口的时候
     */
    private String token;

    private List<String> roles;

    private List<String> permissions;


    public static UserInfo buildUserInfo(UserInfoUcVo userInfoDTO, String token) {
        UserInfo userInfo = new UserInfo();
        userInfo.setSystem(false);
        userInfo.setUserId(userInfoDTO.getUser().getUserId());
        userInfo.setUserName(userInfoDTO.getUser().getUserName());
        userInfo.setCustomer(userInfoDTO.getUser().getCompanyCode());
        userInfo.setSubCustomer(userInfoDTO.getUser().getSubCustomerCode());
        userInfo.setToken(token);
        userInfo.setRoles(userInfoDTO.getRoles());
        userInfo.setPermissions(userInfoDTO.getPermissions());
        userInfo.setAdmin(userInfoDTO.getUser().isAdmin());
        return userInfo;
    }
}
