package com.guwave.datahub.bpms.app.vo.bz.qualityRepair.request;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Schema(title = "批次变更记录下拉框")
public class DataQualityBatchRepairDropDownFilterVo {

    @Schema(description = "需要查询的字段", example = "DEVICE")
    @NotNull(message = "过滤字段不能为空")
    private String filterField;

    @Schema(description = "subCustomer列表", example = "[\"ZY\",\"AMD\"]")
    private List<String> subCustomerList;

    @Schema(description = "deviceId列表", example = "[\"SS521BB-DEFAULT\",\"SS521BB\"]")
    private List<String> deviceIdList;

    @Schema(description = "testStage列表", example = "[\"CP1\",\"FT1\"]")
    private List<String> testStageList;

    @Schema(description = "lotId列表", example = "[\"NT41A\",\"NS9HG-5\"]")
    private List<String> lotIdList;

    public void validCondition() {
        subCustomerList = convertEmpty(subCustomerList);
        deviceIdList = convertEmpty(deviceIdList);
        testStageList = convertEmpty(testStageList);
        lotIdList = convertEmpty(lotIdList);
    }

    private <T> List<T> convertEmpty(List<T> list) {
        return CollectionUtils.isEmpty(list) ? null : list;
    }
}
