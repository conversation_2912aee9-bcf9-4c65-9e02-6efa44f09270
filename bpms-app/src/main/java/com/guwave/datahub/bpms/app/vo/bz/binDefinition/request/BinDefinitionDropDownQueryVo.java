package com.guwave.datahub.bpms.app.vo.bz.binDefinition.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Bin Definition查询条件VO
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(title = "Bin Definition查询条件")
public class BinDefinitionDropDownQueryVo  {

    @Schema(description = "filterField", example = "testProgram")
    private String filterField;
}