package com.guwave.datahub.bpms.app.vo.web;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(title = "日志查询请求VO")
public class LogsQueryRequestVo {

    @Schema(title = "应用名")
    private String app;

    @Schema(title = "主机名")
    private String host;

    @Schema(title = "实例名")
    private String instance;

    @Schema(title = "日志级别")
    private List<String> logLevel;

    @Schema(title = "开始时间")
    private String startTime;

    @Schema(title = "结束时间")
    private String endTime;

    @Schema(title = "查询类型")
    private String queryType;

    @Schema(title = "查询值")
    private String queryValue;

    public LogsQueryRequestVo build(String app, String startTime, String endTime, String host, String instance,
                                   List<String> logLevel, String queryType, String queryValue) {
        this.app = app;
        this.startTime = startTime;
        this.endTime = endTime;
        this.host = host;
        this.instance = instance;
        this.logLevel = logLevel;
        this.queryType = queryType;
        this.queryValue = queryValue;
        return this;
    }

}