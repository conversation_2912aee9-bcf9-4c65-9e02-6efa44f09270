package com.guwave.datahub.bpms.app.vo.dailyReport.model;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
public class TimeOutSparkTask {
    private String uniqueId;
    private String computeCode;
    private Long dieCnt;
    private Long testItemCnt;
    private String computeEngine;
    private String computeType;
    private String queue;
    private Integer numExecutors;
    private Integer executorCores;
    private Integer executorMemory;
    private Integer driverMemory;
    private Integer parallelism;
    private Integer priorityGroup;
    private Long priority;
    private Integer hdfsResultPartition;
    private String extraConf;
    private String jarPath;
    private String mainClass;
    private String extraFiles;
    private String version;
    private Boolean useDynamicResource;
    private String sinkType;
    private Integer failCnt;
    private String processStatus;
    private String submitMode;
    private String appId;
    private Integer stageId;
    private String appName;
    private String exceptionType;
    private String errorMessage;
    private Date startTime;
    private Date endTime;
    private Long executeTime;
    private Integer cancelCnt;
    private Date actlStartTime;
    private Long actlExecuteTime;
    private Long estExecuteTime;
    private Long accEqExecuteTime;
    private Date checkExecuteTime;

    public String getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }

    public String getComputeCode() {
        return computeCode;
    }

    public void setComputeCode(String computeCode) {
        this.computeCode = computeCode;
    }

    public Long getDieCnt() {
        return dieCnt;
    }

    public void setDieCnt(Long dieCnt) {
        this.dieCnt = dieCnt;
    }

    public Long getTestItemCnt() {
        return testItemCnt;
    }

    public void setTestItemCnt(Long testItemCnt) {
        this.testItemCnt = testItemCnt;
    }

    public String getComputeEngine() {
        return computeEngine;
    }

    public void setComputeEngine(String computeEngine) {
        this.computeEngine = computeEngine;
    }

    public String getComputeType() {
        return computeType;
    }

    public void setComputeType(String computeType) {
        this.computeType = computeType;
    }

    public String getQueue() {
        return queue;
    }

    public void setQueue(String queue) {
        this.queue = queue;
    }

    public Integer getNumExecutors() {
        return numExecutors;
    }

    public void setNumExecutors(Integer numExecutors) {
        this.numExecutors = numExecutors;
    }

    public Integer getExecutorCores() {
        return executorCores;
    }

    public void setExecutorCores(Integer executorCores) {
        this.executorCores = executorCores;
    }

    public Integer getExecutorMemory() {
        return executorMemory;
    }

    public void setExecutorMemory(Integer executorMemory) {
        this.executorMemory = executorMemory;
    }

    public Integer getDriverMemory() {
        return driverMemory;
    }

    public void setDriverMemory(Integer driverMemory) {
        this.driverMemory = driverMemory;
    }

    public Integer getParallelism() {
        return parallelism;
    }

    public void setParallelism(Integer parallelism) {
        this.parallelism = parallelism;
    }

    public Integer getPriorityGroup() {
        return priorityGroup;
    }

    public void setPriorityGroup(Integer priorityGroup) {
        this.priorityGroup = priorityGroup;
    }

    public Long getPriority() {
        return priority;
    }

    public void setPriority(Long priority) {
        this.priority = priority;
    }

    public Integer getHdfsResultPartition() {
        return hdfsResultPartition;
    }

    public void setHdfsResultPartition(Integer hdfsResultPartition) {
        this.hdfsResultPartition = hdfsResultPartition;
    }

    public String getExtraConf() {
        return extraConf;
    }

    public void setExtraConf(String extraConf) {
        this.extraConf = extraConf;
    }

    public String getJarPath() {
        return jarPath;
    }

    public void setJarPath(String jarPath) {
        this.jarPath = jarPath;
    }

    public String getMainClass() {
        return mainClass;
    }

    public void setMainClass(String mainClass) {
        this.mainClass = mainClass;
    }

    public String getExtraFiles() {
        return extraFiles;
    }

    public void setExtraFiles(String extraFiles) {
        this.extraFiles = extraFiles;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Boolean getUseDynamicResource() {
        return useDynamicResource;
    }

    public void setUseDynamicResource(Boolean useDynamicResource) {
        this.useDynamicResource = useDynamicResource;
    }

    public String getSinkType() {
        return sinkType;
    }

    public void setSinkType(String sinkType) {
        this.sinkType = sinkType;
    }

    public Integer getFailCnt() {
        return failCnt;
    }

    public void setFailCnt(Integer failCnt) {
        this.failCnt = failCnt;
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
    }

    public String getSubmitMode() {
        return submitMode;
    }

    public void setSubmitMode(String submitMode) {
        this.submitMode = submitMode;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public Integer getStageId() {
        return stageId;
    }

    public void setStageId(Integer stageId) {
        this.stageId = stageId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getExceptionType() {
        return exceptionType;
    }

    public void setExceptionType(String exceptionType) {
        this.exceptionType = exceptionType;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(Long executeTime) {
        this.executeTime = executeTime;
    }

    public Integer getCancelCnt() {
        return cancelCnt;
    }

    public void setCancelCnt(Integer cancelCnt) {
        this.cancelCnt = cancelCnt;
    }

    public Date getActlStartTime() {
        return actlStartTime;
    }

    public void setActlStartTime(Date actlStartTime) {
        this.actlStartTime = actlStartTime;
    }

    public Long getActlExecuteTime() {
        return actlExecuteTime;
    }

    public void setActlExecuteTime(Long actlExecuteTime) {
        this.actlExecuteTime = actlExecuteTime;
    }

    public Long getEstExecuteTime() {
        return estExecuteTime;
    }

    public void setEstExecuteTime(Long estExecuteTime) {
        this.estExecuteTime = estExecuteTime;
    }

    public Long getAccEqExecuteTime() {
        return accEqExecuteTime;
    }

    public void setAccEqExecuteTime(Long accEqExecuteTime) {
        this.accEqExecuteTime = accEqExecuteTime;
    }

    public Date getCheckExecuteTime() {
        return checkExecuteTime;
    }

    public void setCheckExecuteTime(Date checkExecuteTime) {
        this.checkExecuteTime = checkExecuteTime;
    }
}
