package com.guwave.datahub.bpms.app.service.bz;

import com.guwave.datahub.bpms.app.vo.bz.qualityRepair.request.DataQualityBatchRepairDropDownFilterVo;
import com.guwave.datahub.bpms.app.vo.bz.qualityRepair.request.DataQualityBatchRepairVo;
import com.guwave.datahub.bpms.app.vo.bz.qualityRepair.request.DataQualityFileRepairDropDownFilterVo;
import com.guwave.datahub.bpms.app.vo.bz.qualityRepair.request.DataQualityFileRepairVo;
import com.guwave.datahub.bpms.app.vo.bz.qualityRepair.response.*;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import com.guwave.datahub.bpms.common.constant.Constant;
import com.guwave.datahub.bpms.dao.repository.bpms.DataRepairEffectBatchRepository;
import com.guwave.datahub.bpms.dao.repository.bpms.DataRepairHistoryRepository;
import com.guwave.datahub.bpms.dao.repository.bpms.DataRepairInfoRepository;
import com.guwave.datahub.bpms.dao.domain.repair.DataRepairEffectBatch;
import com.guwave.datahub.bpms.dao.domain.repair.DataRepairHistory;
import com.guwave.datahub.bpms.dao.domain.repair.DataRepairInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DataQualityRepairService {
    private final DataRepairEffectBatchRepository dataRepairEffectBatchRepository;
    private final DataRepairHistoryRepository dataRepairHistoryRepository;
    private final DataRepairInfoRepository dataRepairInfoRepository;

    public DataQualityRepairService(DataRepairEffectBatchRepository dataRepairEffectBatchRepository,
                                    DataRepairHistoryRepository dataRepairHistoryRepository,
                                    DataRepairInfoRepository dataRepairInfoRepository) {
        this.dataRepairEffectBatchRepository = dataRepairEffectBatchRepository;
        this.dataRepairHistoryRepository = dataRepairHistoryRepository;
        this.dataRepairInfoRepository = dataRepairInfoRepository;
    }

    public List<DataQualityBatchRepairDropDownVo> getBatchRecordDropdown(DataQualityBatchRepairDropDownFilterVo filterVo) {
        filterVo.validCondition();
        // 到 bz_data_repair_effect_batch 查出下拉框数据
        List<String> dropdownList = dataRepairEffectBatchRepository.getBatchListDropdown(
                RequestContext.getCustomer(),
                filterVo.getFilterField(),
                filterVo.getSubCustomerList(),
                filterVo.getDeviceIdList(),
                filterVo.getTestStageList(),
                filterVo.getLotIdList()
        );

        return CollectionUtils.isEmpty(dropdownList)
                ? Collections.emptyList()
                : dropdownList.stream().map(DataQualityBatchRepairDropDownVo::of).collect(Collectors.toList());
    }

    public PageableDataVo<DataRepairEffectBatchVo> getBatchRecordList(DataQualityBatchRepairVo req) {
        Pageable pageable = PageRequest.of(req.getPageIndex() - 1, req.getPageSize());
        // log.info("customer : {}", RequestContext.getCustomer());
        Page<DataRepairEffectBatch> dataPage = dataRepairEffectBatchRepository.getBatchRecordList(
                RequestContext.getCustomer(),
                req.getSubCustomerList(),
                req.getDeviceIdList(),
                req.getTestStageList(),
                req.getLotIdList(),
                pageable
        );

        Page<DataRepairEffectBatchVo> voPage = dataPage.map(DataRepairEffectBatchVo::of);
        return PageableDataVo.of(voPage);
    }

    public List<DataQualityFileRepairDropDownVo> getFileRecordDropdown(DataQualityFileRepairDropDownFilterVo filterVo) {
        filterVo.validCondition();
        // 到 bz_data_repair_info 查出下拉框数据
        List<String> dropdownList = dataRepairInfoRepository.getFileListDropdown(
                RequestContext.getCustomer(),
                filterVo.getFilterField(),
                filterVo.getSubCustomerList(),
                filterVo.getDeviceIdList(),
                filterVo.getTestStageList(),
                filterVo.getLotIdList(),
                filterVo.getFileNameList(),
                filterVo.getRepairStatusList(),
                filterVo.getRepairCategoryList(),
                filterVo.getLastRepairTypeList(),
                filterVo.getRepairTimesList()
        );

        return CollectionUtils.isEmpty(dropdownList)
                ? Collections.emptyList()
                : dropdownList.stream().map(DataQualityFileRepairDropDownVo::of).collect(Collectors.toList());
    }

    public PageableDataVo<DataQualityBatchRepairInfoVo> getFileRecordList(DataQualityFileRepairVo req) {
        Pageable pageable = PageRequest.of(req.getPageIndex() - 1, req.getPageSize());
        Page<DataRepairInfo> dataPage = dataRepairInfoRepository.findPagedList(
                RequestContext.getCustomer(),
                req.getSubCustomerList(),
                req.getDeviceIdList(),
                req.getTestStageList(),
                req.getLotIdList(),
                req.getFileNameList(),
                req.getRepairStatusList(),
                req.getRepairCategoryList(),
                req.getLastRepairTypeList(),
                req.getRepairTimesList(),
                pageable);

        Page<DataQualityBatchRepairInfoVo> voPage = dataPage.map(DataQualityBatchRepairInfoVo::of);
        return PageableDataVo.of(voPage);
    }

    public List<DataQualityBatchRepairInfoVo> getFileList(Long id) {
        // 到 bz_data_repair_effect_batch 查出 repair_info_ids
        String repairInfoIds = dataRepairEffectBatchRepository.findRepairInfoIdsById(id).getRepairInfoIds();

        if (repairInfoIds == null || repairInfoIds.trim().isEmpty()) {
            return Collections.emptyList();
        }

        List<Long> ids = Arrays.stream(repairInfoIds.split(Constant.COMMA))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Long::valueOf)
                .collect(Collectors.toList());

        List<DataRepairHistory> historyList = dataRepairHistoryRepository.findAllByIdIn(ids);
        if (CollectionUtils.isEmpty(historyList)) {
            return Collections.emptyList();
        }

        return historyList.stream()
                .map(DataQualityBatchRepairInfoVo::of)
                .collect(Collectors.toList());
    }

    public List<DataQualityFileRepairInfoVo> getFileLogList(Long fileId) {
        // 到 dw_data_repair_history 查出单条数据
        List<DataRepairHistory> historyList = dataRepairHistoryRepository.findAllByFileIdOrderByUpdateTimeDesc(fileId);
        // log.info("getFileLogList-historyList : {}", historyList);
        return CollectionUtils.isEmpty(historyList)
                ? Collections.emptyList()
                : historyList.stream()
                .map(DataQualityFileRepairInfoVo::of)
                .collect(Collectors.toList());
    }

}
