package com.guwave.datahub.bpms.app.vo.bz.qualityRepair.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Schema(title = "批次变更记录下拉框结果")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataQualityBatchRepairDropDownVo {
    @Schema(title = "label")
    private String label;
    @Schema(title = "value")
    private String value;

    public static DataQualityBatchRepairDropDownVo of(String value) {
        DataQualityBatchRepairDropDownVo vo = new DataQualityBatchRepairDropDownVo();
        vo.setLabel(value);
        vo.setValue(value);
        return vo;
    }
}
