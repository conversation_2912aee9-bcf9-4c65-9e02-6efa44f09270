package com.guwave.datahub.bpms.app.vo.bz.testItemReload;

import com.guwave.onedata.dataware.bridge.api.vo.WaferVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * 2025/7/7 18:09
 * CalTestItemWaferVo
 *
 * <AUTHOR>
 */
@Data
public class CalTestItemWaferRequestVo {
    @Schema(title = "跑测项的批次信息")
    @NotNull
    private Set<WaferVo> waferVos;
    @Schema(title = "跑dataware测项", example = "true")
    private boolean calculateDwTestItem;
    @Schema(title = "跑yms测项", example = "true")
    private boolean calculateYmsTestItem;
}
