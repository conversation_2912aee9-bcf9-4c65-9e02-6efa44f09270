package com.guwave.datahub.bpms.app.service.bz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.sink.KafkaSink;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.RepairFileVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.RepairEffectBatchQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.RepairFieldInfoVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.RepairFileQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.RepairInfoSaveVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response.FileRepairRecordVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response.RepairEffectBatchFileVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response.RepairFieldInfoResVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response.RepairPreviewResultVo;
import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.common.constant.RepairCategoryEnum;
import com.guwave.datahub.bpms.common.constant.RepairFieldEnum;
import com.guwave.datahub.bpms.common.constant.RepairStatusEnum;
import com.guwave.datahub.bpms.dao.clickhouse.domain.dwd.DwdDieDetail;
import com.guwave.datahub.bpms.dao.clickhouse.domain.dws.DwsFlowidBinIndex;
import com.guwave.datahub.bpms.dao.repository.clickhouse.dwd.mapper.DwdDieDetailMapper;
import com.guwave.datahub.bpms.dao.repository.clickhouse.dws.DwsFlowidBinIndexRepository;
import com.guwave.datahub.bpms.dao.repository.bpms.DataRepairBatchInfoRepository;
import com.guwave.datahub.bpms.dao.repository.bpms.DataRepairEffectBatchRepository;
import com.guwave.datahub.bpms.dao.repository.bpms.DataRepairHistoryRepository;
import com.guwave.datahub.bpms.dao.repository.bpms.DataRepairInfoRepository;
import com.guwave.onedata.dataware.bridge.api.vo.WaferVo;
import com.guwave.onedata.dataware.common.contant.ExceptionType;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.contant.RepairField;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.common.contant.UploadType;
import com.guwave.onedata.dataware.common.model.message.DataRepairFinishMessage;
import com.guwave.onedata.dataware.common.model.message.DataRepairMessage;
import com.guwave.onedata.dataware.common.model.message.DataRepairRecalculateMessage;
import com.guwave.onedata.dataware.common.model.repair.DataRepairDetail;
import com.guwave.onedata.dataware.common.model.repair.DataRepairFail;
import com.guwave.onedata.dataware.common.model.repair.DataRepairField;
import com.guwave.onedata.dataware.common.model.repair.DataRepairRecalculateDetail;
import com.guwave.onedata.dataware.common.util.WaferUtil;
import com.guwave.datahub.bpms.dao.domain.repair.DataRepairBatchInfo;
import com.guwave.datahub.bpms.dao.domain.repair.DataRepairEffectBatch;
import com.guwave.datahub.bpms.dao.domain.repair.DataRepairHistory;
import com.guwave.datahub.bpms.dao.domain.repair.DataRepairInfo;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LotWaferWarehousingRecordRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.RepairLotWaferRepository;
import com.guwave.onedata.dataware.repair.api.MiniDwProvider;
import com.guwave.onedata.dataware.repair.api.vo.SlimDieDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.guwave.onedata.dataware.common.contant.Constant.P;
import static jodd.util.StringPool.COMMA;
import static jodd.util.StringPool.EMPTY;

/**
 * 2025/5/13 14:42
 * DataRepairService
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DataRepairService {

    @Autowired
    private DwsFlowidBinIndexRepository dwsFlowidBinIndexRepository;

    @Autowired
    private DataRepairBatchInfoRepository dataRepairBatchInfoRepository;
    @Autowired
    private DataRepairEffectBatchRepository dataRepairEffectBatchRepository;
    @Autowired
    private DataRepairHistoryRepository dataRepairHistoryRepository;
    @Autowired
    private DataRepairInfoRepository dataRepairInfoRepository;
    @Autowired
    private RepairLotWaferRepository repairLotWaferRepository;
    @Autowired
    private LotWaferWarehousingRecordRepository lotWaferWarehousingRecordRepository;

    @Autowired
    private KafkaSink kafkaSink;

    @Value("${spring.kafka.repairTopic}")
    private String repairTopic;

    private final String repairFailInfo = "请稍后重试";

    private final Map<RepairFieldEnum, BiConsumer<DwsFlowidBinIndex, RepairFieldInfoResVo>> FIELD_CONSUMER_MAP = new HashMap<RepairFieldEnum, BiConsumer<DwsFlowidBinIndex, RepairFieldInfoResVo>>() {{
        put(RepairFieldEnum.DEVICE_ID, (dwsFlowidBinIndex, repairFieldInfoVo) -> repairFieldInfoVo.setOriginalFieldValue(dwsFlowidBinIndex.getDeviceId()));
        put(RepairFieldEnum.TEST_STAGE, (dwsFlowidBinIndex, repairFieldInfoVo) -> repairFieldInfoVo.setOriginalFieldValue(dwsFlowidBinIndex.getTestStage()));
        put(RepairFieldEnum.LOT_ID, (dwsFlowidBinIndex, repairFieldInfoVo) -> repairFieldInfoVo.setOriginalFieldValue(dwsFlowidBinIndex.getLotId()));
        put(RepairFieldEnum.WAFER_NO, (dwsFlowidBinIndex, repairFieldInfoVo) -> repairFieldInfoVo.setOriginalFieldValue(dwsFlowidBinIndex.getWaferNo()));
        put(RepairFieldEnum.SBLOT_ID, (dwsFlowidBinIndex, repairFieldInfoVo) -> repairFieldInfoVo.setOriginalFieldValue(dwsFlowidBinIndex.getSblotId()));
        put(RepairFieldEnum.TEST_PROGRAM, (dwsFlowidBinIndex, repairFieldInfoVo) -> repairFieldInfoVo.setOriginalFieldValue(dwsFlowidBinIndex.getTestProgram()));
        put(RepairFieldEnum.LOT_TYPE, (dwsFlowidBinIndex, repairFieldInfoVo) -> repairFieldInfoVo.setOriginalFieldValue(dwsFlowidBinIndex.getLotType()));
    }};

    private final Map<RepairFieldEnum, BiConsumer<RepairFieldInfoVo, RepairFileVo>> REPAIR_FILE_CONSUMER_MAP = new HashMap<RepairFieldEnum, BiConsumer<RepairFieldInfoVo, RepairFileVo>>() {{
        put(RepairFieldEnum.DEVICE_ID, (repairFieldInfoVo, repairFileVo) -> repairFileVo.setOriginalDeviceId(repairFieldInfoVo.getOriginalValue()).setModifyDeviceId(StringUtils.isBlank(repairFieldInfoVo.getModifyValue()) ? repairFieldInfoVo.getOriginalValue() : repairFieldInfoVo.getModifyValue()).setCurrentRepairFlag(true));
        put(RepairFieldEnum.TEST_STAGE, (repairFieldInfoVo, repairFileVo) -> repairFileVo.setOriginalTestStage(repairFieldInfoVo.getOriginalValue()).setModifyTestStage(StringUtils.isBlank(repairFieldInfoVo.getModifyValue()) ? repairFieldInfoVo.getOriginalValue() : repairFieldInfoVo.getModifyValue()).setCurrentRepairFlag(true));
        put(RepairFieldEnum.LOT_ID, (repairFieldInfoVo, repairFileVo) -> repairFileVo.setOriginalLotId(repairFieldInfoVo.getOriginalValue()).setModifyLotId(StringUtils.isBlank(repairFieldInfoVo.getModifyValue()) ? repairFieldInfoVo.getOriginalValue() : repairFieldInfoVo.getModifyValue()).setCurrentRepairFlag(true));
        put(RepairFieldEnum.WAFER_NO, (repairFieldInfoVo, repairFileVo) -> repairFileVo.setOriginalWaferNo(repairFieldInfoVo.getOriginalValue()).setModifyWaferNo(StringUtils.isBlank(repairFieldInfoVo.getModifyValue()) ? repairFieldInfoVo.getOriginalValue() : repairFieldInfoVo.getModifyValue()).setCurrentRepairFlag(true));
        put(RepairFieldEnum.SBLOT_ID, (repairFieldInfoVo, repairFileVo) -> repairFileVo.setOriginalSblotId(repairFieldInfoVo.getOriginalValue()).setModifySblotId(StringUtils.isBlank(repairFieldInfoVo.getModifyValue()) ? repairFieldInfoVo.getOriginalValue() : repairFieldInfoVo.getModifyValue()).setCurrentRepairFlag(true));
        put(RepairFieldEnum.TEST_PROGRAM, (repairFieldInfoVo, repairFileVo) -> repairFileVo.setOriginalTestProgram(repairFieldInfoVo.getOriginalValue()).setModifyTestProgram(StringUtils.isBlank(repairFieldInfoVo.getModifyValue()) ? repairFieldInfoVo.getOriginalValue() : repairFieldInfoVo.getModifyValue()).setCurrentRepairFlag(true));
        put(RepairFieldEnum.LOT_TYPE, (repairFieldInfoVo, repairFileVo) -> repairFileVo.setOriginalLotType(repairFieldInfoVo.getOriginalValue()).setModifyLotType(StringUtils.isBlank(repairFieldInfoVo.getModifyValue()) ? repairFieldInfoVo.getOriginalValue() : repairFieldInfoVo.getModifyValue()).setCurrentRepairFlag(true));
    }};

    private final Map<RepairField, BiConsumer<RepairFileVo, List<DataRepairField>>> FIELD_MODIFY_MAP = new HashMap<RepairField, BiConsumer<RepairFileVo, List<DataRepairField>>>() {{
        put(RepairField.DEVICE_ID, (repairFileVo, dataRepairFields) -> {
            if (!Objects.equals(repairFileVo.getOriginalDeviceId(), repairFileVo.getModifyDeviceId())) {
                dataRepairFields.add(new DataRepairField().setRepairField(RepairField.DEVICE_ID)
                        .setOriginValue(repairFileVo.getOriginalDeviceId())
                        .setModifyValue(repairFileVo.getModifyDeviceId()));
            }
        });
        put(RepairField.LOT_ID, (repairFileVo, dataRepairFields) -> {
            if (!Objects.equals(repairFileVo.getOriginalLotId(), repairFileVo.getModifyLotId())) {
                dataRepairFields.add(new DataRepairField().setRepairField(RepairField.LOT_ID)
                        .setOriginValue(repairFileVo.getOriginalLotId())
                        .setModifyValue(repairFileVo.getModifyLotId()));
            }
        });
        put(RepairField.WAFER_NO, (repairFileVo, dataRepairFields) -> {
            if (!Objects.equals(repairFileVo.getOriginalWaferNo(), repairFileVo.getModifyWaferNo())) {
                dataRepairFields.add(new DataRepairField().setRepairField(RepairField.WAFER_NO)
                        .setOriginValue(repairFileVo.getOriginalWaferNo())
                        .setModifyValue(repairFileVo.getModifyWaferNo()));
            }
        });
        put(RepairField.TEST_PROGRAM, (repairFileVo, dataRepairFields) -> {
            if (!Objects.equals(repairFileVo.getOriginalTestProgram(), repairFileVo.getModifyTestProgram())) {
                dataRepairFields.add(new DataRepairField().setRepairField(RepairField.TEST_PROGRAM)
                        .setOriginValue(repairFileVo.getOriginalTestProgram())
                        .setModifyValue(repairFileVo.getModifyTestProgram()));
            }
        });
        put(RepairField.TEST_STAGE, (repairFileVo, dataRepairFields) -> {
            if (!Objects.equals(repairFileVo.getOriginalTestStage(), repairFileVo.getModifyTestStage())) {
                dataRepairFields.add(new DataRepairField().setRepairField(RepairField.TEST_STAGE)
                        .setOriginValue(repairFileVo.getOriginalTestStage())
                        .setModifyValue(repairFileVo.getModifyTestStage()));
            }
        });
        put(RepairField.LOT_TYPE, (repairFileVo, dataRepairFields) -> {
            if (!Objects.equals(repairFileVo.getOriginalLotType(), repairFileVo.getModifyLotType())) {
                dataRepairFields.add(new DataRepairField().setRepairField(RepairField.LOT_TYPE)
                        .setOriginValue(repairFileVo.getOriginalLotType())
                        .setModifyValue(repairFileVo.getModifyLotType()));
            }
        });
        put(RepairField.FLOW_ID, (repairFileVo, dataRepairFields) -> {
            if (!Objects.equals(repairFileVo.getOriginalFlowId(), repairFileVo.getModifyFlowId())) {
                dataRepairFields.add(new DataRepairField().setRepairField(RepairField.FLOW_ID)
                        .setOriginValue(repairFileVo.getOriginalFlowId())
                        .setModifyValue(repairFileVo.getModifyFlowId()));
            }
        });
        put(RepairField.SBLOT_ID, (repairFileVo, dataRepairFields) -> {
            if (!Objects.equals(repairFileVo.getOriginalSblotId(), repairFileVo.getModifySblotId())) {
                dataRepairFields.add(new DataRepairField().setRepairField(RepairField.SBLOT_ID)
                        .setOriginValue(repairFileVo.getOriginalSblotId())
                        .setModifyValue(repairFileVo.getModifySblotId()));
            }
        });
        put(RepairField.RETEST_BIN_NUM, (repairFileVo, dataRepairFields) -> {
            if (!Objects.equals(repairFileVo.getOriginalRetestHbinNumber(), repairFileVo.getModifyRetestHbinNumber())) {
                dataRepairFields.add(new DataRepairField().setRepairField(RepairField.RETEST_BIN_NUM)
                        .setOriginValue(repairFileVo.getOriginalRetestHbinNumber())
                        .setModifyValue(repairFileVo.getModifyRetestHbinNumber()));
            }
        });
    }};
    @Autowired
    private DwdDieDetailMapper dwdDieDetailMapper;


    public List<FileRepairRecordVo> getRepairFileRecord(RepairFileQueryVo fileRepairRecordQueryVo) {
        if (CollectionUtils.isEmpty(fileRepairRecordQueryVo.getFileIds())) {
            log.info("文件id为空");
            return Collections.emptyList();
        }
        List<DwsFlowidBinIndex> flowidBinIndexList = dwsFlowidBinIndexRepository.findAllByCustomerAndUploadTypeAndIsDeleteAndFinalFlagAndFileIdIn(
                RequestContext.getCustomer(),
                UploadType.AUTO.getType(),
                (byte) 0,
                (byte) 1,
                fileRepairRecordQueryVo.getFileIds()
        );

        if (CollectionUtils.isEmpty(flowidBinIndexList)) {
            log.info("dws表中没有查到记录");
            return Collections.emptyList();
        }

        if (!dataRepairInfoRepository.existsByFileIdInAndDeleteFlag(fileRepairRecordQueryVo.getFileIds(), false)) {
            log.info("数据修复表中没有查到记录, 是首次修复");
            return flowidBinIndexList.stream()
                    .map(elem -> FileRepairRecordVo.of(elem, 0, false))
                    .collect(Collectors.toList());
        }

        Map<Long, DataRepairInfo> repairInfoMap = dataRepairInfoRepository.findAllByFileIdInAndDeleteFlag(fileRepairRecordQueryVo.getFileIds(), false)
                .stream().collect(Collectors.toMap(DataRepairInfo::getFileId, Function.identity()));

        Set<Long> repairHistoryFileIds = dataRepairHistoryRepository.findAllByFileIdInAndDeleteFlag(fileRepairRecordQueryVo.getFileIds(), false)
                .stream()
                .map(DataRepairHistory::getFileId)
                .collect(Collectors.toSet());

        return flowidBinIndexList.stream()
                .map(elem -> {
                    Integer repairTimes = repairInfoMap.get(elem.getFileId()) != null ? repairInfoMap.get(elem.getFileId()).getRepairTimes() : 0;
                    boolean isRepaired = !CollectionUtils.isEmpty(repairHistoryFileIds) && repairHistoryFileIds.contains(elem.getFileId());
                    return FileRepairRecordVo.of(elem, repairTimes, isRepaired);
                })
                .collect(Collectors.toList());
    }

    public List<RepairFieldInfoResVo> getRepairFieldInfo(RepairFileQueryVo fileRepairRecordQueryVo) {
        if (CollectionUtils.isEmpty(fileRepairRecordQueryVo.getFileIds())) {
            return Collections.emptyList();
        }

        List<DwsFlowidBinIndex> flowidBinIndexList = dwsFlowidBinIndexRepository.findAllByCustomerAndUploadTypeAndIsDeleteAndFinalFlagAndFileIdIn(
                RequestContext.getCustomer(),
                UploadType.AUTO.getType(),
                (byte) 0,
                (byte) 1,
                fileRepairRecordQueryVo.getFileIds()
        );

        if (CollectionUtils.isEmpty(flowidBinIndexList)) {
            log.info("查询dws数据为空");
            return Collections.emptyList();
        }
        DwsFlowidBinIndex head = flowidBinIndexList.get(0);
        boolean isCp = TestArea.getCPList().contains(TestArea.of(head.getTestArea()));
        return getRepairFieldInfoVos(isCp, flowidBinIndexList);
    }

    private List<RepairFieldInfoResVo> getRepairFieldInfoVos(boolean isCp, List<DwsFlowidBinIndex> flowidBinIndexList) {
        List<RepairFieldEnum> repairFieldOrderList = isCp ? RepairFieldEnum.getCpRepairFields() : RepairFieldEnum.getFtRepairFields();

        List<RepairFieldInfoResVo> repairFieldInfoVoList = new ArrayList<>();
        for (DwsFlowidBinIndex dwsFlowidBinIndex : flowidBinIndexList) {
            repairFieldOrderList.forEach(fieldEnum -> {
                BiConsumer<DwsFlowidBinIndex, RepairFieldInfoResVo> consumer = FIELD_CONSUMER_MAP.get(fieldEnum);
                if (consumer != null) {
                    RepairFieldInfoResVo repairFieldInfoVo = RepairFieldInfoResVo.builder()
                            .field(fieldEnum.name())
                            .fieldShowName(fieldEnum.getShowName())
                            .fileId(dwsFlowidBinIndex.getFileId())
                            .fileName(dwsFlowidBinIndex.getFileName())
                            .build();
                    consumer.accept(dwsFlowidBinIndex, repairFieldInfoVo);
                    repairFieldInfoVoList.add(repairFieldInfoVo);
                }
            });
        }
        return repairFieldInfoVoList;
    }

    public List<RepairEffectBatchFileVo> getRepairEffectBatchFileList(RepairEffectBatchQueryVo repairEffectBatchQueryVo) {
        if (CollectionUtils.isEmpty(repairEffectBatchQueryVo.getRepairInfo())) {
            return Collections.emptyList();
        }
        // 修复的文件id
        Map<Long, List<RepairFieldInfoVo>> currentRepairFileMap = repairEffectBatchQueryVo.getRepairInfo().stream()
                .collect(Collectors.groupingBy(RepairFieldInfoVo::getFileId));

        Map<Long, RepairFileVo> allModifyRepairFileVoMap = getAllModifyRepairFileVoMap(currentRepairFileMap);
        return allModifyRepairFileVoMap.values().stream()
                .map(RepairEffectBatchFileVo::of)
                // 本次修复文件排在前
                .sorted(Comparator
                        .comparing(RepairEffectBatchFileVo::getRepairFile, Comparator.nullsLast(Comparator.reverseOrder()))
                        .thenComparing(RepairEffectBatchFileVo::getDeviceId, Comparator.nullsLast(String::compareTo))
                        .thenComparing(RepairEffectBatchFileVo::getTestArea, Comparator.nullsLast(String::compareTo))
                        .thenComparing(RepairEffectBatchFileVo::getTestStage, Comparator.nullsLast(String::compareTo))
                        .thenComparing(RepairEffectBatchFileVo::getTestProgram, Comparator.nullsLast(String::compareTo))
                        .thenComparing(RepairEffectBatchFileVo::getLotId, Comparator.nullsLast(String::compareTo))
                        .thenComparing(RepairEffectBatchFileVo::getWaferId, Comparator.nullsLast(String::compareTo))
                        .thenComparing(RepairEffectBatchFileVo::getSblotId, Comparator.nullsLast(String::compareTo))
                        .thenComparing(RepairEffectBatchFileVo::getStartTime, Comparator.nullsLast(String::compareTo))
                )
                .collect(Collectors.toList());
    }

    public List<RepairPreviewResultVo> repairPreview(RepairInfoSaveVo repairInfoSaveVo) {
        if (CollectionUtils.isEmpty(repairInfoSaveVo.getRepairFileInfos()) || CollectionUtils.isEmpty(repairInfoSaveVo.getRepairFieldInfoVos())) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "请填写修复文件信息");
        }

        // 校验字段变更信息
        boolean fieldModifyFlag = repairInfoSaveVo.getRepairFileInfos().stream().allMatch(elem ->
                !Objects.equals(elem.getModifyFlowId(), elem.getOriginalFlowId())
                        || !Objects.equals(elem.getModifyRetestHbinNumber(), elem.getOriginalRetestHbinNumber()))
                || repairInfoSaveVo.getRepairFieldInfoVos().stream().anyMatch(elem -> StringUtils.isNotBlank(elem.getModifyValue()));
        if (!fieldModifyFlag) {
            throw new BpmsException(ResponseCode.Business.DATA_ACCURACY_FILE_NOT_CHANGE);
        }

        // 本次修复的文件id
        Map<Long, List<RepairFieldInfoVo>> currentRepairFileMap = repairInfoSaveVo.getRepairFieldInfoVos().stream()
                .collect(Collectors.groupingBy(RepairFieldInfoVo::getFileId));
        Map<Long, RepairInfoSaveVo.RepairFileInfo> currentRepairFileInfoMap = repairInfoSaveVo.getRepairFileInfos().stream().collect(Collectors.toMap(RepairInfoSaveVo.RepairFileInfo::getFileId, elem -> elem));

        Map<Long, RepairFileVo> allModifyRepairFileVoMap = this.getAllModifyRepairFileVoMap(currentRepairFileMap);

        allModifyRepairFileVoMap.forEach((fileId, repairFileVo) -> {
            RepairInfoSaveVo.RepairFileInfo repairFileInfo = currentRepairFileInfoMap.get(fileId);
            if (repairFileInfo != null) {
                repairFileVo.setModifyFlowId(repairFileInfo.getModifyFlowId() == null ? repairFileVo.getOriginalFlowId() : repairFileInfo.getModifyFlowId()).setCurrentRepairFlag(true);
                repairFileVo.setModifyRetestHbinNumber(repairFileInfo.getModifyRetestHbinNumber() == null ? repairFileVo.getOriginalRetestHbinNumber() : repairFileInfo.getModifyRetestHbinNumber()).setCurrentRepairFlag(true);
            }
        });

        // 检查修复后的批次有没有初测文件
        Map<WaferVo, List<RepairFileVo>> afterModifyWaferMap = allModifyRepairFileVoMap.values().stream()
                .collect(Collectors.groupingBy(elem -> new WaferVo()
                        .setCustomer(elem.getCustomer())
                        .setSubCustomer(elem.getSubCustomer())
                        .setFactory(elem.getFactory())
                        .setFactorySite(elem.getFactorySite())
                        .setTestArea(TestArea.of(elem.getTestArea()))
                        .setFileCategory(FileCategory.of(elem.getFileCategory()))
                        .setDeviceId(elem.getModifyDeviceId())
                        .setLotType(LotType.of(elem.getModifyLotType()))
                        .setTestStage(elem.getModifyTestStage())
                        .setLotId(elem.getModifyLotId())
                        .setWaferNo(elem.getModifyWaferNo())
                ));
        afterModifyWaferMap.forEach((waferVo, repairFileVos) -> {
            this.checkDataRepair(waferVo);
            if (repairFileVos.stream().noneMatch(elem -> StringUtils.isNotEmpty(elem.getModifyFlowId()) && elem.getModifyFlowId().startsWith(P))) {
                throw new BpmsException(ResponseCode.Business.DATA_ACCURACY_FIRST_TEST_FILE_NOT_EXIST);
            }
        });

        List<WaferVo> allEffectWaferVos = getAllEffectWaferVos(allModifyRepairFileVoMap.values());
        log.info("本次修复受影响的wafer数: {}", allEffectWaferVos.size());

        List<SlimDieDetail> slimDieDetails = allEffectWaferVos.stream()
                .flatMap(waferVo ->
                        // 获取wafer下所有 DieDetail
                        dwdDieDetailMapper.findSlimDieDetailByLotWafer(
                                RequestContext.getCustomer(),
                                waferVo.getSubCustomer(),
                                waferVo.getFactory(),
                                waferVo.getTestArea().getArea(),
                                waferVo.getDeviceId(),
                                waferVo.getTestStage(),
                                waferVo.getLotId(),
                                waferVo.getWaferNo(),
                                waferVo.getLotType().getType()
                        ).stream())
                .map(dwdDieDetail -> {
                    // 替换修复字段值
                    if (currentRepairFileInfoMap.containsKey(dwdDieDetail.getFileId())) {
                        RepairFileVo repairFileVo = allModifyRepairFileVoMap.get(dwdDieDetail.getFileId());
                        dwdDieDetail.setDeviceId(repairFileVo.getModifyDeviceId());
                        dwdDieDetail.setTestStage(repairFileVo.getModifyTestStage());
                        dwdDieDetail.setLotId(repairFileVo.getModifyLotId());
                        dwdDieDetail.setWaferNo(repairFileVo.getModifyWaferNo());
                        dwdDieDetail.setSblotId(repairFileVo.getModifySblotId());
                        dwdDieDetail.setTestProgram(repairFileVo.getModifyTestProgram());
                        dwdDieDetail.setLotType(repairFileVo.getOriginalLotType());
                        dwdDieDetail.setFlowId(repairFileVo.getModifyFlowId());
                        dwdDieDetail.setRetestBinNum(repairFileVo.getModifyRetestHbinNumber());
                        dwdDieDetail.setWaferId(repairFileVo.getModifyWaferId());
                    }
                    return this.buildSlimDieDetail(dwdDieDetail);
                }).collect(Collectors.toList());

        TestArea testArea = TestArea.of(repairInfoSaveVo.getRepairFileInfos().get(0).getTestArea());

        return MiniDwProvider.previewDataAccuracy(slimDieDetails, testArea)
                .stream()
                .map(RepairPreviewResultVo::of)
                .collect(Collectors.toList());
    }

    private Map<Long, RepairFileVo> getAllModifyRepairFileVoMap(Map<Long, List<RepairFieldInfoVo>> currentRepairFileMap) {
        List<DwsFlowidBinIndex> currentDwsFlowidBinIndexList = dwsFlowidBinIndexRepository.findAllByCustomerAndUploadTypeAndIsDeleteAndFinalFlagAndFileIdIn(
                RequestContext.getCustomer(),
                UploadType.AUTO.getType(),
                (byte) 0,
                (byte) 1,
                currentRepairFileMap.keySet()
        );
        if (CollectionUtils.isEmpty(currentDwsFlowidBinIndexList)) {
            log.info("查询修复前的批次信息为空");
            throw new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT);
        }

        // 修复的文件信息
        Map<Long, RepairFileVo> currentRepairFileVoMap = new HashMap<>();
        // 根据修复前后的批次信息查询所有受影响的文件
        Set<String> deviceIds = new HashSet<>();
        Set<String> testStages = new HashSet<>();
        Set<String> lotIds = new HashSet<>();
        Set<String> waferNos = new HashSet<>();
        Set<String> lotTypes = new HashSet<>();

        DwsFlowidBinIndex head = currentDwsFlowidBinIndexList.get(0);
        boolean isCp = TestArea.getCPList().contains(TestArea.of(head.getTestArea()));
        for (DwsFlowidBinIndex dwsFlowidBinIndex : currentDwsFlowidBinIndexList) {
            Long fileId = dwsFlowidBinIndex.getFileId();
            if (currentRepairFileMap.containsKey(fileId)) {
                RepairFileVo repairFileVo = new RepairFileVo();
                currentRepairFileMap.getOrDefault(fileId, new ArrayList<>()).forEach(repairFieldInfoVo -> {
                            REPAIR_FILE_CONSUMER_MAP.get(repairFieldInfoVo.getRepairField()).accept(repairFieldInfoVo, repairFileVo);
                        }
                );
                if (isCp) {
                    repairFileVo
                            .setOriginalWaferId(WaferUtil.formatStandardWaferId(repairFileVo.getOriginalLotId(), repairFileVo.getOriginalWaferNo()))
                            .setModifyWaferId(WaferUtil.formatStandardWaferId(repairFileVo.getModifyLotId(), repairFileVo.getModifyWaferNo()));
                } else {
                    repairFileVo
                            .setOriginalWaferNo(EMPTY)
                            .setModifyWaferNo(EMPTY)
                            .setOriginalWaferId(EMPTY)
                            .setModifyWaferId(EMPTY);
                }

                deviceIds.addAll(Arrays.asList(repairFileVo.getOriginalDeviceId(), repairFileVo.getModifyDeviceId()));
                testStages.addAll(Arrays.asList(repairFileVo.getOriginalTestStage(), repairFileVo.getModifyTestStage()));
                lotIds.addAll(Arrays.asList(repairFileVo.getOriginalLotId(), repairFileVo.getModifyLotId()));
                lotTypes.addAll(Arrays.asList(repairFileVo.getOriginalLotType(), repairFileVo.getModifyLotType()));
                if (isCp) {
                    waferNos.addAll(Arrays.asList(repairFileVo.getOriginalWaferNo(), repairFileVo.getModifyWaferNo()));
                }

                currentRepairFileVoMap.put(fileId, repairFileVo);
            }
        }

        List<DwsFlowidBinIndex> allModifyFlowidBinIndexList = isCp ? dwsFlowidBinIndexRepository.findAllByCustomerAndUploadTypeAndIsDeleteAndSubCustomerAndFactoryAndFactorySiteAndTestAreaAndDeviceIdInAndLotTypeInAndTestStageInAndLotIdInAndWaferNoInAndFinalFlag(
                RequestContext.getCustomer(),
                UploadType.AUTO.getType(),
                (byte) 0,
                head.getSubCustomer(),
                head.getFactory(),
                head.getFactorySite(),
                head.getTestArea(),
                deviceIds,
                lotTypes,
                testStages,
                lotIds,
                waferNos,
                (byte) 1) : dwsFlowidBinIndexRepository.findAllByCustomerAndUploadTypeAndIsDeleteAndSubCustomerAndFactoryAndFactorySiteAndTestAreaAndDeviceIdInAndLotTypeInAndTestStageInAndLotIdInAndFinalFlag(
                RequestContext.getCustomer(),
                UploadType.AUTO.getType(),
                (byte) 0,
                head.getSubCustomer(),
                head.getFactory(),
                head.getFactorySite(),
                head.getTestArea(),
                deviceIds,
                lotTypes,
                testStages,
                lotIds,
                (byte) 1);

        Map<Long, RepairFileVo> allRepairFileVoMap = new HashMap<>();

        allModifyFlowidBinIndexList.forEach(flowIdIndex -> {
            Long fileId = flowIdIndex.getFileId();
            if (currentRepairFileVoMap.containsKey(fileId)) {
                RepairFileVo repairFileVo = currentRepairFileVoMap.get(fileId);
                repairFileVo
                        .setCustomer(flowIdIndex.getCustomer())
                        .setSubCustomer(flowIdIndex.getSubCustomer())
                        .setFactory(flowIdIndex.getFactory())
                        .setFactorySite(flowIdIndex.getFactorySite())
                        .setFileId(fileId)
                        .setFileName(flowIdIndex.getFileName())
                        .setTestArea(flowIdIndex.getTestArea())
                        .setFileCategory(flowIdIndex.getFileType())
                        .setOriginalRetestHbinNumber(flowIdIndex.getRetestBinNum())
                        .setModifyRetestHbinNumber(flowIdIndex.getRetestBinNum())
                        .setOriginalFlowId(flowIdIndex.getFlowId())
                        .setModifyFlowId(flowIdIndex.getFlowId())
                        .setOriginalStartTime(flowIdIndex.getStartTime())
                        .setModifyStartTime(flowIdIndex.getStartTime())
                        .setOriginalEndTime(flowIdIndex.getEndTime())
                        .setModifyEndTime(flowIdIndex.getEndTime());
                allRepairFileVoMap.put(fileId, repairFileVo);
            } else {
                RepairFileVo repairFileVo = new RepairFileVo()
                        .setCustomer(flowIdIndex.getCustomer())
                        .setSubCustomer(flowIdIndex.getSubCustomer())
                        .setFactory(flowIdIndex.getFactory())
                        .setFactorySite(flowIdIndex.getFactorySite())
                        .setTestArea(flowIdIndex.getTestArea())
                        .setFileCategory(flowIdIndex.getFileType())
                        .setFileId(fileId)
                        .setFileName(flowIdIndex.getFileName())
                        .setOriginalDeviceId(flowIdIndex.getDeviceId())
                        .setModifyDeviceId(flowIdIndex.getDeviceId())
                        .setOriginalTestStage(flowIdIndex.getTestStage())
                        .setModifyTestStage(flowIdIndex.getTestStage())
                        .setOriginalLotId(flowIdIndex.getLotId())
                        .setModifyLotId(flowIdIndex.getLotId())
                        .setOriginalWaferNo(isCp ? flowIdIndex.getWaferNo() : EMPTY)
                        .setModifyWaferNo(isCp ? flowIdIndex.getWaferNo() : EMPTY)
                        .setOriginalSblotId(flowIdIndex.getSblotId())
                        .setModifySblotId(flowIdIndex.getSblotId())
                        .setOriginalTestProgram(flowIdIndex.getTestProgram())
                        .setModifyTestProgram(flowIdIndex.getTestProgram())
                        .setOriginalLotType(flowIdIndex.getLotType())
                        .setModifyLotType(flowIdIndex.getLotType())
                        .setOriginalRetestHbinNumber(flowIdIndex.getRetestBinNum())
                        .setModifyRetestHbinNumber(flowIdIndex.getRetestBinNum())
                        .setOriginalFlowId(flowIdIndex.getFlowId())
                        .setModifyFlowId(flowIdIndex.getFlowId())
                        .setOriginalStartTime(flowIdIndex.getStartTime())
                        .setModifyStartTime(flowIdIndex.getStartTime())
                        .setOriginalEndTime(flowIdIndex.getEndTime())
                        .setModifyEndTime(flowIdIndex.getEndTime())
                        .setOriginalWaferId(isCp ? flowIdIndex.getWaferId() : EMPTY)
                        .setModifyWaferId(isCp ? flowIdIndex.getWaferId() : EMPTY);
                allRepairFileVoMap.put(fileId, repairFileVo);
            }
        });

        return allRepairFileVoMap;
    }


    public SlimDieDetail buildSlimDieDetail(DwdDieDetail dwdDieDetail) {
        return new SlimDieDetail()
                .setTestStage(dwdDieDetail.getTestStage())
                .setLotType(dwdDieDetail.getLotType())
                .setFlowId(dwdDieDetail.getFlowId())
                .setFileId(dwdDieDetail.getFileId())
                .setFileName(dwdDieDetail.getFileName())
                .setDeviceId(dwdDieDetail.getDeviceId())
                .setLotId(dwdDieDetail.getLotId())
                .setWaferId(dwdDieDetail.getWaferId())
                .setWaferNo(dwdDieDetail.getWaferNo())
                .setSblotId(dwdDieDetail.getSblotId())
                .setxCoord(dwdDieDetail.getXCoord())
                .setyCoord(dwdDieDetail.getYCoord())
                .setOnlineRetest(dwdDieDetail.getOnlineRetest() == null ? null : Integer.valueOf(dwdDieDetail.getOnlineRetest()))
                .setOfflineRetest(dwdDieDetail.getOfflineRetest() == null ? null : Integer.valueOf(dwdDieDetail.getOfflineRetest()))
                .setInterrupt(dwdDieDetail.getInterrupt() == null ? null : Integer.valueOf(dwdDieDetail.getInterrupt()))
                .setDupRetest(dwdDieDetail.getDupRetest() == null ? null : Integer.valueOf(dwdDieDetail.getDupRetest()))
                .setIsDupFirstTest(dwdDieDetail.getIsDupFirstTest() == null ? null : Integer.valueOf(dwdDieDetail.getIsDupFirstTest()))
                .setIsDupFinalTest(dwdDieDetail.getIsDupFinalTest() == null ? null : Integer.valueOf(dwdDieDetail.getIsDupFinalTest()))
                .setIsFirstTest(dwdDieDetail.getIsFirstTest() == null ? null : Integer.valueOf(dwdDieDetail.getIsFirstTest()))
                .setIsFinalTest(dwdDieDetail.getIsFinalTest() == null ? null : Integer.valueOf(dwdDieDetail.getIsFinalTest()))
                .setBatchNum(dwdDieDetail.getBatchNum() == null ? null : Integer.valueOf(dwdDieDetail.getBatchNum()))
                .setMaxOfflineRetest(dwdDieDetail.getMaxOfflineRetest() == null ? null : Integer.valueOf(dwdDieDetail.getMaxOfflineRetest()))
                .setTestProgram(dwdDieDetail.getTestProgram())
                .setTestProgramVersion(dwdDieDetail.getTestProgramVersion())
                .setHbinNum(dwdDieDetail.getHbinNum())
                .setHbinPf(dwdDieDetail.getHbinPf())
                .setEcid(dwdDieDetail.getEcid())
                .setRetestBinNum(dwdDieDetail.getRetestBinNum())
                ;

    }

    public void triggerRepair(RepairInfoSaveVo repairInfoSaveVo) {
        DataRepairBatchInfo dataRepairBatchInfo = new DataRepairBatchInfo();
        List<DataRepairInfo> dataRepairInfoList = new ArrayList<>();
        List<DataRepairHistory> dataRepairHistoryList = new ArrayList<>();
        List<DataRepairEffectBatch> dataRepairEffectBatchList = new ArrayList<>();

        try {
            Map<Long, RepairInfoSaveVo.RepairFileInfo> currentRepairFileInfoMap = repairInfoSaveVo.getRepairFileInfos().stream().collect(Collectors.toMap(RepairInfoSaveVo.RepairFileInfo::getFileId, elem -> elem));

            // 修复关键字段文件id
            Map<Long, List<RepairFieldInfoVo>> currentRepairFieldMap = repairInfoSaveVo.getRepairFieldInfoVos().stream()
                    .collect(Collectors.groupingBy(RepairFieldInfoVo::getFileId));

            Map<Long, RepairFileVo> allModifyRepairFileVoMap = this.getAllModifyRepairFileVoMap(currentRepairFieldMap);

            allModifyRepairFileVoMap.forEach((fileId, repairFileVo) -> {
                RepairInfoSaveVo.RepairFileInfo repairFileInfo = currentRepairFileInfoMap.get(fileId);
                if (repairFileInfo != null) {
                    repairFileVo.setModifyFlowId(repairFileInfo.getModifyFlowId() == null ? repairFileVo.getOriginalFlowId() : repairFileInfo.getModifyFlowId()).setCurrentRepairFlag(true);
                    repairFileVo.setModifyRetestHbinNumber(repairFileInfo.getModifyRetestHbinNumber() == null ? repairFileVo.getOriginalRetestHbinNumber() : repairFileInfo.getModifyRetestHbinNumber()).setCurrentRepairFlag(true);
                }
            });

            List<WaferVo> allEffectWaferVos = getAllEffectWaferVos(allModifyRepairFileVoMap.values());
            log.info("本次修复受影响的wafer数: {}", allEffectWaferVos.size());
            // 检查有没有正在入库
            allEffectWaferVos.forEach(this::checkDataRepair);

            Map<WaferVo, List<RepairFileVo>> afterModifyWaferMap = allModifyRepairFileVoMap.values().stream()
                    .collect(Collectors.groupingBy(elem -> new WaferVo()
                            .setCustomer(elem.getCustomer())
                            .setSubCustomer(elem.getSubCustomer())
                            .setFactory(elem.getFactory())
                            .setFactorySite(elem.getFactorySite())
                            .setTestArea(TestArea.of(elem.getTestArea()))
                            .setFileCategory(FileCategory.of(elem.getFileCategory()))
                            .setDeviceId(elem.getModifyDeviceId())
                            .setLotType(LotType.of(elem.getModifyLotType()))
                            .setTestStage(elem.getModifyTestStage())
                            .setLotId(elem.getModifyLotId())
                            .setWaferNo(elem.getModifyWaferNo())
                    ));
            // 记录本次修复
            dataRepairBatchInfo = this.buildDataRepairBatchInfo(allModifyRepairFileVoMap.values());
            dataRepairBatchInfoRepository.save(dataRepairBatchInfo);

            DataRepairBatchInfo finalDataRepairBatchInfo = dataRepairBatchInfo;
            afterModifyWaferMap.forEach((waferVo, repairFileVoList) -> {
                // 生成修复文件记录
                for (RepairFileVo repairFileVo : repairFileVoList) {
                    if (repairFileVo.isCurrentRepairFlag()) {
                        Long fileId = repairFileVo.getFileId();
                        RepairInfoSaveVo.RepairFileInfo repairFileInfo = currentRepairFileInfoMap.get(fileId);
                        List<RepairFieldInfoVo> repairFieldInfoVos = currentRepairFieldMap.get(fileId);
                        DataRepairInfo dataRepairInfo = this.buildDataRepairInfo(repairFileVo);
                        dataRepairInfo.setRepairBatchId(finalDataRepairBatchInfo.getId());

                        Map<String, Object> map = new HashMap<>();
                        map.put("fileInfo", repairFileInfo);
                        map.put("repairInfo", repairFieldInfoVos);
                        dataRepairInfo.setRepairJson(JSONObject.toJSONString(map));
                        dataRepairInfoRepository.save(dataRepairInfo);
                        dataRepairInfoList.add(dataRepairInfo);

                        // 生成修复文件历史记录
                        DataRepairHistory dataRepairHistory = this.buildDataRepairHistory(dataRepairInfo);
                        dataRepairHistoryRepository.save(dataRepairHistory);
                        dataRepairHistoryList.add(dataRepairHistory);
                    }
                }

            });

            // 生成数据修复影响批次
            dataRepairEffectBatchList = this.buildDataRepairEffectBatch(allModifyRepairFileVoMap.values(), dataRepairBatchInfo, dataRepairHistoryList);
            dataRepairEffectBatchRepository.saveAll(dataRepairEffectBatchList);

            // 发送修复消息
            List<DataRepairMessage> dataRepairMessages = this.buildDataRepairMessageList(allModifyRepairFileVoMap.values());
            dataRepairMessages.forEach(message -> {
                String messageJson = JSON.toJSONString(message);
                log.info("发送修复消息:{}", messageJson);
                kafkaSink.send(repairTopic, messageJson);
            });
        } catch (Exception e) {
            dataRepairInfoList.forEach(elem -> dataRepairInfoRepository.save(elem.setRepairStatus(RepairStatusEnum.FAIL.name())));
            dataRepairHistoryList.forEach(elem -> dataRepairHistoryRepository.save(elem.setRepairStatus(RepairStatusEnum.FAIL.name())));
            dataRepairEffectBatchList.forEach(elem -> dataRepairEffectBatchRepository.save(elem.setRepairStatus(RepairStatusEnum.FAIL.name())));
            log.error("触发修复失败,", e);
            if (e instanceof BpmsException) {
                throw (BpmsException) e;
            }
            throw new BpmsException(ResponseCode.Business.DATA_ACCURACY_TRIGGER_REPAIR_FAIL);
        }
    }

    public void repairFinishedHandler(DataRepairFinishMessage message) {
        WaferVo waferVo = new WaferVo()
                .setCustomer(message.getCustomer())
                .setSubCustomer(message.getSubCustomer())
                .setFactory(message.getFactory())
                .setFactorySite(message.getFactorySite())
                .setTestArea(message.getTestArea())
                .setFileCategory(FileCategory.of(message.getFileCategory()))
                .setDeviceId(message.getDeviceId())
                .setLotType(message.getLotType())
                .setTestStage(message.getTestStage())
                .setLotId(message.getLotId())
                .setWaferNo(message.getWaferNo());

        List<DataRepairEffectBatch> effectBatches = dataRepairEffectBatchRepository.findAllByCustomerAndTestAreaAndFactoryAndFactorySiteAndDeviceIdAndTestStageAndLotIdAndWaferNo(
                message.getCustomer(),
                TestArea.of(message.getTestArea()),
                message.getFactory(),
                message.getFactorySite(),
                message.getDeviceId(),
                message.getTestStage(),
                message.getLotId(),
                message.getWaferNo());
        if (CollectionUtils.isEmpty(effectBatches)) {
            log.info("没有找到受影响批次，waferVo: {}", waferVo);
            return;
        } else if (effectBatches.size() > 1) {
            log.info("找到多条受影响批次，ids: {}, waferVo: {}", effectBatches.stream().map(DataRepairEffectBatch::getId), waferVo);
            return;
        }

        DataRepairEffectBatch dataRepairEffectBatch = effectBatches.get(0);

        RepairStatusEnum repairStatus = getRepairStatusByMessage(message);
        // 1. error直接更新文件状态
        if (RepairStatusEnum.ERROR.equals(repairStatus)) {
            log.info("repairStatus=ERROR，受影响批次id: {}, waferVo: {}", dataRepairEffectBatch.getId(), waferVo);
            this.updateRepairStatus(dataRepairEffectBatch, repairStatus, message.getDataRepairFails());
            return;
        }

        // 2. 其他情况更新受影响批次
        dataRepairEffectBatch.setRepairStatus(repairStatus.name()).setUpdateTime(new Date());
        dataRepairEffectBatchRepository.save(dataRepairEffectBatch);
        log.info("更新受影响批次状态为{} id: {} 批次信息：{}", repairStatus, dataRepairEffectBatch.getId(), waferVo);

        // 3. 查询同batch其他受影响批次
        List<DataRepairEffectBatch> allEffectBatches = dataRepairEffectBatchRepository.findAllByRepairBatchIdAndDeleteFlag(dataRepairEffectBatch.getRepairBatchId(), false);
        long processingCount = allEffectBatches.stream().filter(val -> RepairStatusEnum.PROCESS.equals(RepairStatusEnum.valueOf(val.getRepairStatus()))).count();
        if (processingCount > 0) {
            log.info("repairBatchId={}存在未处理完的受影响批次，暂不更新文件修复状态", dataRepairEffectBatch.getId());
            return;
        }
        // 3.1 存在error的话，前面流程已经更新完文件状态，并存了失败原因，这里不用其他处理
        long errorCount = allEffectBatches.stream().filter(val -> RepairStatusEnum.ERROR.equals(RepairStatusEnum.valueOf(val.getRepairStatus()))).count();
        if (errorCount > 0) {
            return;
        }
        // 3.2 如果有fail，那么本次修复不成功，但是可以重试
        long failCount = allEffectBatches.stream().filter(val -> RepairStatusEnum.FAIL.equals(RepairStatusEnum.valueOf(val.getRepairStatus()))).count();
        if (failCount > 0) {
            log.info("本次修复失败，但是可以重试");
            this.updateRepairStatus(dataRepairEffectBatch, RepairStatusEnum.FAIL, null);
            return;
        }
        // 3.3 修复成功，更新文件状态
        log.info("本次修复成功");
        this.updateRepairStatus(dataRepairEffectBatch, RepairStatusEnum.SUCCESS, null);
    }

    public void repairRecalculateHandler(DataRepairRecalculateMessage message) {
        // 消息中的文件信息
        Map<Long, DataRepairRecalculateDetail> recalculateDetailMap = message.getRepairRecalculateDetails()
                .stream()
                .collect(Collectors.toMap(DataRepairRecalculateDetail::getFileId, Function.identity()));

        // bz_data_repair_info 表中的文件信息
        Map<Long, DataRepairInfo> dataRepairInfoMap = dataRepairInfoRepository.findAllByFileIdInAndDeleteFlag(recalculateDetailMap.keySet(), false)
                .stream()
                .collect(Collectors.toMap(DataRepairInfo::getFileId, Function.identity()));

        String messageJson = JSON.toJSONString(message);
        recalculateDetailMap.forEach((fileId, recalculateDetail) -> {
            DataRepairInfo dataRepairInfo = dataRepairInfoMap.get(fileId);
            if (null == dataRepairInfo) {
                log.info("收到重计算消息，但bz_data_repair_info表中暂无该文件修复记录，FileId：{}", fileId);
            } else {
                // dc_data_repair_info表中有此文件记录
                dataRepairInfo
                        .setRepairStatus(RepairStatusEnum.SUCCESS.name())
                        .setRepairTimes(dataRepairInfo.getRepairTimes() + 1)
                        .setRepairJson(messageJson)
                        .setUpdateTime(new Date());
                recalculateDetail.getDataRepairFields().forEach(repairField -> fillRepairField(dataRepairInfo, repairField));

                log.info("重计算成功，更新修复记录，fileId：{}, fileName:{}", fileId, dataRepairInfo.getFileName());
                dataRepairInfoRepository.save(dataRepairInfo);

                log.info("重计算成功，保存新的修复历史记录，fileId：{}, fileName:{}", fileId, dataRepairInfo.getFileName());
                DataRepairHistory dataRepairHistory = buildDataRepairHistory(dataRepairInfo);
                dataRepairHistoryRepository.save(dataRepairHistory);
            }
        });
    }


    private void checkDataRepair(WaferVo waferVo) {
        long lotWaferWarehousingNotFinishCnt = lotWaferWarehousingRecordRepository.countAllByCustomerAndTestAreaAndFactoryAndDeviceIdAndLotTypeAndTestStageAndLotIdAndWaferNoAndFileCategoryAndProcessStatusIn(
                waferVo.getCustomer(),
                waferVo.getTestArea(),
                waferVo.getFactory(),
                waferVo.getDeviceId(),
                waferVo.getLotType(),
                waferVo.getTestStage(),
                waferVo.getLotId(),
                waferVo.getWaferNo(),
                waferVo.getFileCategory(),
                Arrays.asList(ProcessStatus.CREATE, ProcessStatus.PROCESSING, ProcessStatus.FAIL));

        if (lotWaferWarehousingNotFinishCnt != 0L) {
            throw new BpmsException(ResponseCode.Business.DATA_ACCURACY_WAREHOUSING_FILE_EXIST);
        }

        long repairNotFinishCnt = repairLotWaferRepository.countAllByCustomerAndTestAreaAndFactoryAndFactorySiteAndDeviceIdAndLotTypeAndTestStageAndLotIdAndWaferNoAndFileCategoryAndProcessStatusIn(
                waferVo.getCustomer(),
                waferVo.getTestArea(),
                waferVo.getFactory(),
                waferVo.getFactorySite(),
                waferVo.getDeviceId(),
                waferVo.getLotType(),
                waferVo.getTestStage(),
                waferVo.getLotId(),
                waferVo.getWaferNo(),
                waferVo.getFileCategory(),
                Arrays.asList(ProcessStatus.CREATE, ProcessStatus.PROCESSING));
        if (repairNotFinishCnt != 0L) {
            throw new BpmsException(ResponseCode.Business.DATA_ACCURACY_WAREHOUSING_FILE_EXIST);
        }

        long repairEffectBatcheNotFinishCnt = dataRepairEffectBatchRepository.countAllByCustomerAndTestAreaAndFactoryAndFactorySiteAndDeviceIdAndTestStageAndLotIdAndWaferNoAndRepairStatusIn(
                waferVo.getCustomer(),
                TestArea.of(waferVo.getTestArea()),
                waferVo.getFactory(),
                waferVo.getFactorySite(),
                waferVo.getDeviceId(),
                waferVo.getTestStage(),
                waferVo.getLotId(),
                waferVo.getWaferNo(),
                Arrays.asList(RepairStatusEnum.ERROR.name(), RepairStatusEnum.PROCESS.name()));
        if (repairEffectBatcheNotFinishCnt != 0L) {
            throw new BpmsException(ResponseCode.Business.Data_ACCURACY_REPAIRING_FILE_EXIST);
        }
    }


    private DataRepairBatchInfo buildDataRepairBatchInfo(Collection<RepairFileVo> repairFileVos) {
        RepairFileVo repairFileVo = repairFileVos.iterator().next();
        return new DataRepairBatchInfo()
                .setCustomer(RequestContext.getCustomer())
                .setSubCustomer(RequestContext.getSubCustomer())
                .setFactory(repairFileVo.getFactory())
                .setFactorySite(repairFileVo.getFactorySite())
                .setDeviceId(repairFileVo.getOriginalDeviceId())
                .setLotId(repairFileVo.getOriginalLotId())
                .setTestStage(repairFileVo.getOriginalTestStage())
                .setTestArea(repairFileVo.getTestArea())
                .setSblotId(repairFileVo.getOriginalSblotId())
                .setWaferId(repairFileVo.getOriginalWaferId())
                .setDeleteFlag(Boolean.FALSE)
                .setCreateTime(new Date())
                .setUpdateTime(new Date())
                .setCreateUser(RequestContext.getUserName())
                .setUpdateUser(RequestContext.getUserName());
    }

    private DataRepairInfo buildDataRepairInfo(RepairFileVo repairFileVo) {
        DataRepairInfo dataRepairInfo = new DataRepairInfo()
                .setCustomer(RequestContext.getCustomer())
                .setSubCustomer(RequestContext.getSubCustomer())
                .setFactory(repairFileVo.getFactory())
                .setFactorySite(repairFileVo.getFactorySite())
                .setFileId(repairFileVo.getFileId())
                .setFileName(repairFileVo.getFileName())
                .setWaferId(repairFileVo.getModifyWaferId())
                .setTestArea(repairFileVo.getTestArea())
                .setOriginalDeviceId(repairFileVo.getOriginalDeviceId())
                .setOriginalTestStage(repairFileVo.getOriginalTestStage())
                .setOriginalLotId(repairFileVo.getOriginalLotId())
                .setOriginalWaferId(repairFileVo.getOriginalWaferId())
                .setOriginalWaferNo(repairFileVo.getOriginalWaferNo())
                .setOriginalSblotId(repairFileVo.getOriginalSblotId())
                .setOriginalTestProgram(repairFileVo.getOriginalTestProgram())
                .setOriginalLotType(repairFileVo.getOriginalLotType())
                .setOriginalRetestHbinNumber(repairFileVo.getOriginalRetestHbinNumber())
                .setOriginalFlowId(repairFileVo.getOriginalFlowId())
                .setModifyDeviceId(repairFileVo.getModifyDeviceId())
                .setModifyTestStage(repairFileVo.getModifyTestStage())
                .setModifyLotId(repairFileVo.getModifyLotId())
                .setModifyWaferNo(repairFileVo.getModifyWaferNo())
                .setModifySblotId(repairFileVo.getModifySblotId())
                .setModifyTestProgram(repairFileVo.getModifyTestProgram())
                .setModifyLotType(repairFileVo.getModifyLotType())
                .setModifyRetestHbinNumber(repairFileVo.getModifyRetestHbinNumber())
                .setModifyFlowId(repairFileVo.getModifyFlowId())
                .setRepairCategory(RepairCategoryEnum.DATA_ACCURACY.getCategory())
                .setLastRepairType(RepairCategoryEnum.DATA_ACCURACY.getType())
                .setRepairStatus(RepairStatusEnum.INIT.name())
                .setRepairErrorInfo(null)
                .setRepairTimes(1)
                .setStartTime(Date.from(repairFileVo.getModifyStartTime().atZone(ZoneId.systemDefault()).toInstant()))
                .setEndTime(Date.from(repairFileVo.getModifyEndTime().atZone(ZoneId.systemDefault()).toInstant()))
                .setRemark(null)
                .setDeleteFlag(Boolean.FALSE)
                .setCreateTime(new Date())
                .setUpdateTime(new Date())
                .setCreateUser(RequestContext.getUserName())
                .setUpdateUser(RequestContext.getUserName());

        dataRepairInfoRepository.findByFileIdAndDeleteFlag(repairFileVo.getFileId(), false)
                .ifPresent(repairInfo ->
                        dataRepairInfo.setId(repairInfo.getId())
                                .setRepairTimes(repairInfo.getRepairTimes() + 1)
                                .setCreateTime(repairInfo.getCreateTime())
                                .setCreateUser(repairInfo.getCreateUser()));

        return dataRepairInfo;
    }


    private List<DataRepairEffectBatch> buildDataRepairEffectBatch(Collection<RepairFileVo> repairFileVos,
                                                                   DataRepairBatchInfo dataRepairBatchInfo,
                                                                   List<DataRepairHistory> dataRepairHistoryList) {
        Map<WaferVo, List<RepairFileVo>> originalWaferMap = repairFileVos.stream().collect(Collectors.groupingBy(elem ->
                new WaferVo()
                        .setCustomer(elem.getCustomer())
                        .setSubCustomer(elem.getSubCustomer())
                        .setFactory(elem.getFactory())
                        .setFactorySite(elem.getFactorySite())
                        .setTestArea(TestArea.of(elem.getTestArea()))
                        .setFileCategory(FileCategory.of(elem.getFileCategory()))
                        .setDeviceId(elem.getOriginalDeviceId())
                        .setLotType(LotType.of(elem.getOriginalLotType()))
                        .setTestStage(elem.getOriginalTestStage())
                        .setLotId(elem.getOriginalLotId())
                        .setWaferNo(elem.getOriginalWaferNo())
        ));

        Map<WaferVo, List<RepairFileVo>> modifyWaferMap = repairFileVos.stream().collect(Collectors.groupingBy(elem ->
                new WaferVo()
                        .setCustomer(elem.getCustomer())
                        .setSubCustomer(elem.getSubCustomer())
                        .setFactory(elem.getFactory())
                        .setFactorySite(elem.getFactorySite())
                        .setTestArea(TestArea.of(elem.getTestArea()))
                        .setFileCategory(FileCategory.of(elem.getFileCategory()))
                        .setDeviceId(elem.getModifyDeviceId())
                        .setLotType(LotType.of(elem.getModifyLotType()))
                        .setTestStage(elem.getModifyTestStage())
                        .setLotId(elem.getModifyLotId())
                        .setWaferNo(elem.getModifyWaferNo())
        ));

        return Stream.concat(originalWaferMap.keySet().stream(), modifyWaferMap.keySet().stream())
                .distinct()
                .map(waferVo -> {
                    List<RepairFileVo> originalRepairFileVoList = originalWaferMap.getOrDefault(waferVo, Collections.emptyList());
                    List<RepairFileVo> modifyRepairFileVoList = modifyWaferMap.getOrDefault(waferVo, Collections.emptyList());

                    List<String> repairFileNames = Stream.concat(originalRepairFileVoList.stream(), modifyRepairFileVoList.stream())
                            .filter(RepairFileVo::isCurrentRepairFlag)
                            .map(RepairFileVo::getFileName)
                            .distinct()
                            .sorted()
                            .collect(Collectors.toList());

                    List<Long> dataRepairHistoryIds = dataRepairHistoryList.stream()
                            .filter(elem -> repairFileNames.contains(elem.getFileName()))
                            .map(DataRepairHistory::getId)
                            .distinct()
                            .collect(Collectors.toList());

                    String testProgramStrs;
                    String sblotIdStrs ;
                    String waferIdStrs;
                    if (CollectionUtils.isNotEmpty(modifyRepairFileVoList)) {
                        testProgramStrs = modifyRepairFileVoList.stream().map(RepairFileVo::getModifyTestProgram).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA));
                        sblotIdStrs = modifyRepairFileVoList.stream().map(RepairFileVo::getModifySblotId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA));
                        waferIdStrs = modifyRepairFileVoList.stream().map(RepairFileVo::getModifyWaferId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA));
                    } else {
                        testProgramStrs = originalRepairFileVoList.stream().map(RepairFileVo::getOriginalTestProgram).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA));
                        sblotIdStrs = originalRepairFileVoList.stream().map(RepairFileVo::getOriginalSblotId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA));
                        waferIdStrs = originalRepairFileVoList.stream().map(RepairFileVo::getOriginalWaferId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA));
                    }

                    DataRepairEffectBatch dataRepairEffectBatch = new DataRepairEffectBatch()
                            .setCustomer(RequestContext.getCustomer())
                            .setSubCustomer(RequestContext.getSubCustomer())
                            .setFactory(waferVo.getFactory())
                            .setFactorySite(waferVo.getFactorySite())
                            .setRepairBatchId(dataRepairBatchInfo.getId())
                            .setRepairStatus(RepairStatusEnum.PROCESS.name())
                            .setDeviceId(waferVo.getDeviceId())
                            .setLotId(waferVo.getLotId())
                            .setTestStage(waferVo.getTestStage())
                            .setTestProgram(testProgramStrs)
                            .setTestArea(waferVo.getTestArea().getArea())
                            .setSblotId(sblotIdStrs)
                            .setWaferId(waferIdStrs)
                            .setWaferNo(waferVo.getWaferNo())
                            .setRepairNo(1)
                            .setFileCountBefore((long) originalRepairFileVoList.size())
                            .setFileListBefore(originalRepairFileVoList.stream().map(RepairFileVo::getFileName).filter(StringUtils::isNotBlank).collect(Collectors.joining(COMMA)))
                            .setFileCountAfter((long) modifyRepairFileVoList.size())
                            .setFileListAfter(modifyRepairFileVoList.stream().map(RepairFileVo::getFileName).filter(StringUtils::isNotBlank).collect(Collectors.joining(COMMA)))
                            .setRepairFiles(String.join(COMMA, repairFileNames))
                            .setRepairInfoIds(dataRepairHistoryIds.stream().map(String::valueOf).collect(Collectors.joining(COMMA)))
                            .setDeleteFlag(Boolean.FALSE)
                            .setCreateTime(new Date())
                            .setUpdateTime(new Date())
                            .setCreateUser(RequestContext.getUserName())
                            .setUpdateUser(RequestContext.getUserName());

                    dataRepairEffectBatchRepository.findFirstByCustomerAndTestAreaAndFactoryAndFactorySiteAndDeviceIdAndTestStageAndLotIdAndWaferNoOrderByRepairNoDesc(
                            waferVo.getCustomer(),
                            TestArea.of(waferVo.getTestArea()),
                            waferVo.getFactory(),
                            waferVo.getFactorySite(),
                            waferVo.getDeviceId(),
                            waferVo.getTestStage(),
                            waferVo.getLotId(),
                            waferVo.getWaferNo()
                    ).ifPresent(existDataRepairEffectBatch -> dataRepairEffectBatch
                            .setId(existDataRepairEffectBatch.getId())
                            .setRepairNo(existDataRepairEffectBatch.getRepairNo() + 1)
                            .setCreateTime(existDataRepairEffectBatch.getCreateTime())
                            .setCreateUser(existDataRepairEffectBatch.getCreateUser()));

                    return dataRepairEffectBatch;
                }).collect(Collectors.toList());
    }

    private List<DataRepairMessage> buildDataRepairMessageList(Collection<RepairFileVo> repairFileVos) {
        List<DataRepairMessage> dataRepairMessageList = new ArrayList<>();

        Map<WaferVo, List<RepairFileVo>> waferMap = repairFileVos.stream()
                .collect(Collectors.groupingBy(elem -> new WaferVo()
                        .setCustomer(elem.getCustomer())
                        .setSubCustomer(elem.getSubCustomer())
                        .setFactory(elem.getFactory())
                        .setFactorySite(elem.getFactorySite())
                        .setTestArea(TestArea.of(elem.getTestArea()))
                        .setFileCategory(FileCategory.of(elem.getFileCategory()))
                        .setDeviceId(elem.getOriginalDeviceId())
                        .setLotType(LotType.of(elem.getOriginalLotType()))
                        .setTestStage(elem.getOriginalTestStage())
                        .setLotId(elem.getOriginalLotId())
                        .setWaferNo(elem.getOriginalWaferNo())
                ));

        waferMap.forEach((waferVo, repairFileVoList) -> {
            List<DataRepairDetail> dataRepairDetails = repairFileVoList.stream().map(repairFileVo -> {
                List<DataRepairField> dataRepairFields = new ArrayList<>();
                FIELD_MODIFY_MAP.values().forEach(consumer -> consumer.accept(repairFileVo, dataRepairFields));

                return new DataRepairDetail()
                        .setFileName(repairFileVo.getFileName())
                        .setFileId(repairFileVo.getFileId())
                        .setFlowId(repairFileVo.getOriginalFlowId())
                        .setTestProgram(repairFileVo.getOriginalTestProgram())
                        .setSblotId(repairFileVo.getOriginalSblotId())
                        .setDataRepairFields(dataRepairFields);
            }).collect(Collectors.toList());

            DataRepairMessage dataRepairMessage = new DataRepairMessage()
                    .setCustomer(waferVo.getCustomer())
                    .setSubCustomer(waferVo.getSubCustomer())
                    .setFactory(waferVo.getFactory())
                    .setFactorySite(waferVo.getFactorySite())
                    .setLotType(waferVo.getLotType())
                    .setTestArea(waferVo.getTestArea())
                    .setDeviceId(waferVo.getDeviceId())
                    .setLotId(waferVo.getLotId())
                    .setWaferNo(waferVo.getWaferNo())
                    .setTestStage(waferVo.getTestStage())
                    .setFileCategory(waferVo.getFileCategory())
                    .setDataRepairDetails(dataRepairDetails);

            dataRepairMessageList.add(dataRepairMessage);
        });
        return dataRepairMessageList;
    }

    private static RepairStatusEnum getRepairStatusByMessage(DataRepairFinishMessage message) {
        if (ProcessStatus.SUCCESS.equals(message.getProcessStatus())) {
            return RepairStatusEnum.SUCCESS;
        }
        if (ProcessStatus.FAIL.equals(message.getProcessStatus())) {
            List<DataRepairFail> dataRepairFails = message.getDataRepairFails();
            if (CollectionUtils.isEmpty(dataRepairFails)) {
                log.error("修复失败，但缺少失败信息");
                return RepairStatusEnum.ERROR;
            }
            ExceptionType exceptionType = dataRepairFails.get(0).getExceptionType();
            // IN_PROGRESS 情况记录为FAIL，其他情况为ERROR
            if (ExceptionType.REPAIR_IN_PROGRESS.equals(exceptionType) || ExceptionType.WAREHOUSING_IN_PROGRESS.equals(exceptionType)) {
                return RepairStatusEnum.FAIL;
            }
            return RepairStatusEnum.ERROR;
        }
        log.error("不支持的修复结束状态");
        return RepairStatusEnum.ERROR;
    }

    /**
     * 更新文件修复状态
     */
    public void updateRepairStatus(DataRepairEffectBatch dataRepairEffectBatch, RepairStatusEnum repairStatus, List<DataRepairFail> dataRepairFails) {
        Long repairBatchId = dataRepairEffectBatch.getRepairBatchId();

        List<DataRepairHistory> dataRepairHistoryList = dataRepairHistoryRepository.findAllByRepairBatchIdAndDeleteFlag(repairBatchId, false);
        List<DataRepairInfo> dataRepairInfoList = dataRepairInfoRepository.findAllByRepairBatchIdAndDeleteFlag(repairBatchId, false);

        Map<Long, String> failInfoMap = CollectionUtils.isNotEmpty(dataRepairFails) ? dataRepairFails.stream().collect(Collectors.toMap(DataRepairFail::getFileId, JSON::toJSONString)) : new HashMap<>();
        dataRepairEffectBatch.setRepairStatus(repairStatus.name()).setUpdateTime(new Date());
        dataRepairHistoryList.forEach(elem -> elem
                .setRepairErrorInfo(RepairStatusEnum.FAIL.equals(repairStatus) ? repairFailInfo : failInfoMap.get(elem.getFileId()))
                .setRepairStatus(repairStatus.name())
                .setUpdateTime(new Date())
        );
        dataRepairInfoList.forEach(elem -> elem
                .setRepairErrorInfo(RepairStatusEnum.FAIL.equals(repairStatus) ? repairFailInfo : failInfoMap.get(elem.getFileId()))
                .setRepairStatus(repairStatus.name())
                .setUpdateTime(new Date())
        );

        dataRepairEffectBatchRepository.save(dataRepairEffectBatch);
        dataRepairHistoryRepository.saveAll(dataRepairHistoryList);
        dataRepairInfoRepository.saveAll(dataRepairInfoList);

        log.info("更新受影响批次 repairStatus={} ids={}", repairStatus, dataRepairEffectBatch);
        log.info("更新文件历史 repairStatus={} ids={}", repairStatus, dataRepairHistoryList.stream().map(DataRepairHistory::getId).collect(Collectors.toList()));
        log.info("更新文件 repairStatus={} ids={}", repairStatus, dataRepairInfoList.stream().map(DataRepairInfo::getId).collect(Collectors.toList()));
    }

    private void fillRepairField(DataRepairInfo repairInfo, DataRepairField dataRepairField) {
        switch (dataRepairField.getRepairField()) {
            case DEVICE_ID:
                repairInfo.setOriginalDeviceId(dataRepairField.getOriginValue());
                repairInfo.setModifyDeviceId(dataRepairField.getModifyValue());
                break;
            case LOT_ID:
                repairInfo.setOriginalLotId(dataRepairField.getOriginValue());
                repairInfo.setModifyLotId(dataRepairField.getModifyValue());
                break;
            case TEST_PROGRAM:
                repairInfo.setOriginalTestProgram(dataRepairField.getOriginValue());
                repairInfo.setModifyTestProgram(dataRepairField.getModifyValue());
                break;
            case TEST_STAGE:
                repairInfo.setOriginalTestStage(dataRepairField.getOriginValue());
                repairInfo.setModifyTestStage(dataRepairField.getModifyValue());
                break;
            case LOT_TYPE:
                repairInfo.setOriginalLotType(dataRepairField.getOriginValue());
                repairInfo.setModifyLotType(dataRepairField.getModifyValue());
                break;
            case FLOW_ID:
                repairInfo.setOriginalFlowId(dataRepairField.getOriginValue());
                repairInfo.setModifyFlowId(dataRepairField.getModifyValue());
                break;
            case SBLOT_ID:
                repairInfo.setOriginalSblotId(dataRepairField.getOriginValue());
                repairInfo.setModifySblotId(dataRepairField.getModifyValue());
                break;
            case RETEST_BIN_NUM:
                repairInfo.setOriginalRetestHbinNumber(dataRepairField.getOriginValue());
                repairInfo.setModifyRetestHbinNumber(dataRepairField.getModifyValue());
                break;
        }

    }


    private DataRepairHistory buildDataRepairHistory(DataRepairInfo dataRepairInfo) {
        return new DataRepairHistory()
                .setCustomer(RequestContext.getCustomer())
                .setSubCustomer(RequestContext.getSubCustomer())
                .setFactory(dataRepairInfo.getFactory())
                .setFactorySite(dataRepairInfo.getFactorySite())
                .setRepairBatchId(dataRepairInfo.getRepairBatchId())
                .setDataRepairId(dataRepairInfo.getId())
                .setFileId(dataRepairInfo.getFileId())
                .setFileName(dataRepairInfo.getFileName())
                .setWaferId(dataRepairInfo.getWaferId())
                .setTestArea(dataRepairInfo.getTestArea())
                .setRepairCategory(dataRepairInfo.getRepairCategory())
                .setLastRepairType(dataRepairInfo.getLastRepairType())
                .setRepairStatus(dataRepairInfo.getRepairStatus())
                .setRepairErrorInfo(dataRepairInfo.getRepairErrorInfo())
                .setOriginalDeviceId(dataRepairInfo.getOriginalDeviceId())
                .setOriginalTestStage(dataRepairInfo.getOriginalTestStage())
                .setOriginalLotId(dataRepairInfo.getOriginalLotId())
                .setOriginalWaferNo(dataRepairInfo.getOriginalWaferNo())
                .setOriginalWaferId(dataRepairInfo.getOriginalWaferId())
                .setOriginalSblotId(dataRepairInfo.getOriginalSblotId())
                .setOriginalTestProgram(dataRepairInfo.getOriginalTestProgram())
                .setOriginalLotType(dataRepairInfo.getOriginalLotType())
                .setOriginalRetestHbinNumber(dataRepairInfo.getOriginalRetestHbinNumber())
                .setOriginalFlowId(dataRepairInfo.getOriginalFlowId())
                .setModifyDeviceId(dataRepairInfo.getModifyDeviceId())
                .setModifyTestStage(dataRepairInfo.getModifyTestStage())
                .setModifyLotId(dataRepairInfo.getModifyLotId())
                .setModifyWaferNo(dataRepairInfo.getModifyWaferNo())
                .setModifySblotId(dataRepairInfo.getModifySblotId())
                .setModifyTestProgram(dataRepairInfo.getModifyTestProgram())
                .setModifyLotType(dataRepairInfo.getModifyLotType())
                .setModifyRetestHbinNumber(dataRepairInfo.getModifyRetestHbinNumber())
                .setModifyFlowId(dataRepairInfo.getModifyFlowId())
                .setRepairJson(dataRepairInfo.getRepairJson())
                .setRepairTimes(dataRepairInfo.getRepairTimes())
                .setDeleteFlag(dataRepairInfo.getDeleteFlag())
                .setCreateTime(new Date())
                .setUpdateTime(new Date())
                .setCreateUser(RequestContext.getUserName())
                .setUpdateUser(RequestContext.getUserName());
    }

    private List<WaferVo> getAllEffectWaferVos(Collection<RepairFileVo> repairFileVos) {
        if (CollectionUtils.isEmpty(repairFileVos)) {
            return Collections.emptyList();
        }
        return repairFileVos.stream()
                .flatMap(elem -> Stream.of(
                        new WaferVo()
                                .setCustomer(elem.getCustomer())
                                .setSubCustomer(elem.getSubCustomer())
                                .setFactory(elem.getFactory())
                                .setFactorySite(elem.getFactorySite())
                                .setTestArea(TestArea.of(elem.getTestArea()))
                                .setFileCategory(FileCategory.of(elem.getFileCategory()))
                                .setDeviceId(elem.getModifyDeviceId())
                                .setLotType(LotType.of(elem.getModifyLotType()))
                                .setTestStage(elem.getModifyTestStage())
                                .setLotId(elem.getModifyLotId())
                                .setWaferNo(elem.getModifyWaferNo()),
                        new WaferVo()
                                .setCustomer(elem.getCustomer())
                                .setSubCustomer(elem.getSubCustomer())
                                .setFactory(elem.getFactory())
                                .setFactorySite(elem.getFactorySite())
                                .setTestArea(TestArea.of(elem.getTestArea()))
                                .setFileCategory(FileCategory.of(elem.getFileCategory()))
                                .setDeviceId(elem.getOriginalDeviceId())
                                .setLotType(LotType.of(elem.getOriginalLotType()))
                                .setTestStage(elem.getOriginalTestStage())
                                .setLotId(elem.getOriginalLotId())
                                .setWaferNo(elem.getOriginalWaferNo())
                ))
                .distinct()
                .collect(Collectors.toList());
    }

}




