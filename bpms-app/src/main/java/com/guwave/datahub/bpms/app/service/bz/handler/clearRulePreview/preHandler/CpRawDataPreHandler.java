package com.guwave.datahub.bpms.app.service.bz.handler.clearRulePreview.preHandler;

import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model.ConfResult;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model.MetaData;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model.ParseField;
import com.guwave.datahub.bpms.dao.domain.clearRulePreview.ClearRulePreviewRecord;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.common.model.raw.TestItemData;
import com.guwave.onedata.dataware.parser.stdf.visitor.impl.RawDataMetaDataVisitor;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Map;

import static com.guwave.onedata.dataware.common.contant.Constant.EMPTY;

@Component
public class CpRawDataPreHandler extends CpPreHandler {
    @Override
    public boolean isSuppport(ClearRulePreviewRecord record) {
        return record.getGivenTestArea() == TestArea.CP && record.getFileCategory() == FileCategory.RAW_DATA;
    }

    @Override
    public void fillMetaData(ClearRulePreviewRecord record, File file, MetaData metaData) throws Exception {
        RawDataMetaDataVisitor rawDataMetaDataVisitor = new RawDataMetaDataVisitor(file.getAbsolutePath());
        TestItemData testItemData = rawDataMetaDataVisitor.getTestItemData();

        record.setDeviceId(convertNullStr(testItemData.getDeviceId()));
        record.setOriginStartT(testItemData.getStartTime() != null ? testItemData.getStartTime() + EMPTY : EMPTY);
        record.setOriginFinishT(testItemData.getEndTime() != null ? testItemData.getEndTime() + EMPTY : EMPTY);
        record.setPosX(convertNullStr(testItemData.getPosX()));
        record.setPosY(convertNullStr(testItemData.getPosY()));
        record.setNotch(convertNullStr(testItemData.getWfFlat()));
        record.setTestCod(convertNullStr(testItemData.getTestCod()));
        record.setFloorId(convertNullStr(testItemData.getFloorId()));
        record.setTestTemperature(convertNullStr(testItemData.getTestTemperature()));
        record.setTesterName(convertNullStr(testItemData.getTesterName()));
        record.setTesterType(convertNullStr(testItemData.getTesterType()));
        record.setProbecardLoadboardId(convertNullStr(testItemData.getProbecardLoadboardId()));
        record.setTestProgram(convertNullStr(testItemData.getTestProgram()));
        record.setTestProgramVersion(convertNullStr(testItemData.getTestProgramVersion()));
        record.setPkgTyp(convertNullStr(testItemData.getPkgTyp()));
        record.setOriginWaferId(convertNullStr(testItemData.getWaferId()));
        record.setSblotId(convertNullStr(testItemData.getSblotId()));
        record.setBatchNum(0);
        record.setProcess(testItemData.getProcess());


        metaData.setStartT(record.getOriginStartT());
        metaData.setFinishT(record.getOriginFinishT());
        metaData.setPartTyp(convertNullStr(testItemData.getDeviceId()));
        metaData.setLotId(convertNullStr(testItemData.getLotId()));
        metaData.setSblotId(convertNullStr(testItemData.getSblotId()));
        metaData.setWaferId(convertNullStr(testItemData.getWaferId()));
        metaData.setFlowId(convertNullStr(testItemData.getFlowId()));
        metaData.setRtstCod(convertNullStr(testItemData.getRtstCod()));
        metaData.setTestCod(convertNullStr(testItemData.getTestCod()));
        metaData.setSetupId(convertNullStr(testItemData.getSetupId()));
        metaData.setFloorId(convertNullStr(testItemData.getFloorId()));
        metaData.setTstTemp(convertNullStr(testItemData.getTestTemperature()));
        metaData.setNodeNam(convertNullStr(testItemData.getTesterName()));
        metaData.setTstrTyp(convertNullStr(testItemData.getTesterType()));
        metaData.setCardId(convertNullStr(testItemData.getProbecardLoadboardId()));
        metaData.setJobNam(convertNullStr(testItemData.getTestProgram()));
        metaData.setJobRev(convertNullStr(testItemData.getTestProgramVersion()));
        metaData.setPkgTyp(convertNullStr(testItemData.getPkgTyp()));
        metaData.setUserTxt(convertNullStr(testItemData.getRetestBinNum()));
    }
}
