package com.guwave.datahub.bpms.app.vo.bz.task.response;

import com.guwave.onedata.dataware.dao.mysql.domain.dw.LayerCalculatePool;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.WarehousingTaskRecord;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/12
 * @description TaskLayerCalculatePoolDetailVo
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(title = "单条任务详情，包括spark运行参数")
public class DwTaskDetailVo {
    @Schema(title = "id")
    private Long id;

    @Schema(title = "testArea", description = "CP、FT、SLT等")
    private String testArea;

    @Schema(title = "factory", description = "工厂")
    private String factory;

    @Schema(title = "deviceId", description = "deviceId")
    private String deviceId;

    @Schema(title = "testStage", description = "testStage")
    private String testStage;

    @Schema(title = "lotId", description = "lotId")
    private String lotId;

    @Schema(title = "waferNo", description = "waferNo")
    private String waferNo;

    @Schema(title = "lotType", description = "lotType")
    private String lotType;

    @Schema(title = "dwLayer", description = "dwLayer")
    private String dwLayer;

    @Schema(title = "processStatus", description = "计算状态")
    private String processStatus;

    @Schema(title = "numExecutors", description = "container个数")
    private String numExecutors;

    @Schema(title = "executorCores", description = "每个container的cpu数")
    private String executorCores;

    @Schema(title = "executorMemory", description = "executor内存(GB)")
    private String executorMemory;

    @Schema(title = "driverMemory", description = "driver内存(GB)(GB)")
    private String driverMemory;

    @Schema(title = "parallelism", description = "最大并发数")
    private String parallelism;

    @Schema(title = "extraConf", description = "额外spark配置")
    private String extraConf;

    @Schema(title = "errorMessage", description = "错误信息")
    private String errorMessage;

    public static DwTaskDetailVo of(WarehousingTaskRecord item) {
        String testArea = item.getTestArea() == null ? null : item.getTestArea().getArea();
        String lotType = item.getLotType() == null ? null : item.getLotType().getType();
        String dwLyaer = item.getDwLayer() == null ? null : item.getDwLayer().getLayer();
        String status = item.getProcessStatus() == null ? null : item.getProcessStatus().getStatus();
        String numExecutors = item.getNumExecutors() == null ? null : item.getNumExecutors().toString();
        String executorCores = item.getExecutorCores() == null ? null : item.getExecutorCores().toString();
        String executorMemory = item.getExecutorMemory() == null ? null : item.getExecutorMemory().toString();
        String driverMemory = item.getDriverMemory() == null ? null : item.getDriverMemory().toString();
        String parallelism = item.getParallelism() == null ? null : item.getParallelism().toString();

        DwTaskDetailVo dwTaskDetailVo = new DwTaskDetailVo();
        dwTaskDetailVo.setTestArea(testArea);
        dwTaskDetailVo.setFactory(item.getFactory());
        dwTaskDetailVo.setDeviceId(item.getDeviceId());
        dwTaskDetailVo.setTestStage(item.getTestStage());
        dwTaskDetailVo.setLotId(item.getLotId());
        dwTaskDetailVo.setWaferNo(item.getWaferNo());
        dwTaskDetailVo.setLotType(lotType);
        dwTaskDetailVo.setDwLayer(dwLyaer);
        dwTaskDetailVo.setProcessStatus(status);
        dwTaskDetailVo.setErrorMessage(item.getErrorMessage());
        dwTaskDetailVo.setNumExecutors(numExecutors);
        dwTaskDetailVo.setExecutorCores(executorCores);
        dwTaskDetailVo.setExecutorMemory(executorMemory);
        dwTaskDetailVo.setDriverMemory(driverMemory);
        dwTaskDetailVo.setParallelism(parallelism);
        dwTaskDetailVo.setExtraConf(item.getExtraConf());
        return dwTaskDetailVo;
    }

}
