package com.guwave.datahub.bpms.app.service.dailyReport.impl;

import com.guwave.datahub.bpms.app.service.dailyReport.CommonService;
import com.guwave.datahub.bpms.app.util.DateUtil;
import com.guwave.datahub.bpms.app.util.PdfUtil;
import com.guwave.datahub.bpms.common.constant.*;
import com.guwave.datahub.bpms.dao.repository.bpms.DailyReportResultRepository;
import com.guwave.datahub.bpms.dao.repository.dw.QualityRealtimeResultRepository;
import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class GdpClusterModuleService implements CommonService {
    @Autowired
    private DailyReportResultRepository dailyReportResultRepository;

    @Autowired
    private QualityRealtimeResultRepository qualityRealtimeResultRepository;

    public void generateGdpClusterDetail(PdfUtil pdfUtil) throws IOException {
        String startDateYYYYMMDD = DateUtil.getDayBefortDays(1);
        String endDateYYYYMMDD = DateUtil.getDayBefortDays(1);

        // 获取各个队列资源使用情况
        List<ChartData> dieQueueDetail = qualityRealtimeResultRepository.findQueueDetailByQueueName(startDateYYYYMMDD, endDateYYYYMMDD, QueueName.DIE.getQueueName())
                .stream().map(this::buildChartData).collect(Collectors.toList());
        List<ChartData> testitemQueueDetail = qualityRealtimeResultRepository.findQueueDetailByQueueName(startDateYYYYMMDD, endDateYYYYMMDD, QueueName.TESTITEM.getQueueName())
                .stream().map(this::buildChartData).collect(Collectors.toList());
        List<ChartData> manualQueueDetail = qualityRealtimeResultRepository.findQueueDetailByQueueName(startDateYYYYMMDD, endDateYYYYMMDD, QueueName.MANUAL.getQueueName())
                .stream().map(this::buildChartData).collect(Collectors.toList());
        List<ChartData> rootQueueDetail = qualityRealtimeResultRepository.findQueueDetailByQueueName(startDateYYYYMMDD, endDateYYYYMMDD, QueueName.ROOT.getQueueName())
                .stream().map(this::buildChartData).collect(Collectors.toList());

        pdfUtil.addSecondaryHeading("GDP集群情况");
        pdfUtil.addChart(dieQueueDetail, ChartType.LINE, "die队列资源使用情况", "时间", "资源使用比例");
        pdfUtil.addChart(testitemQueueDetail, ChartType.LINE, "testitem队列资源使用情况", "时间", "资源使用比例");
        pdfUtil.addChart(manualQueueDetail, ChartType.LINE, "manual队列资源使用情况", "时间", "资源使用比例");
        pdfUtil.addChart(rootQueueDetail, ChartType.LINE, "root队列资源使用情况", "时间", "资源使用比例");
    }

    private ChartData buildChartData(Object[] objects) {
        return new ChartData(
                ((String) objects[0]),
                ((Number) objects[2]).doubleValue(),
                ((String) objects[1])
        );
    }

    @Override
    public DailyReportResultRepository getDailyReportResultRepository() {
        return dailyReportResultRepository;
    }
}
