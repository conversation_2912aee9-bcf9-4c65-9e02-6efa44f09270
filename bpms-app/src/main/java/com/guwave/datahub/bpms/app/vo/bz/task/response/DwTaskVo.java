package com.guwave.datahub.bpms.app.vo.bz.task.response;

import cn.hutool.core.date.DateUtil;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LayerCalculatePool;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.WarehousingTaskRecord;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.apache.commons.lang3.ObjectUtils;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_FORMAT;
import static com.guwave.datahub.bpms.common.constant.Constant.SLASH;
import static com.guwave.datahub.bpms.common.constant.Constant.TASK_SUFFIX;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * LayerCalculatePoolVo
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-26 15:39:49
 */
@Data
@AllArgsConstructor
@ToString
@NoArgsConstructor
@Builder
@Schema(title = "DW任务管理信息")
public class DwTaskVo {
    @Schema(title = "id")
    private Long id;

    @Schema(title = "testArea", description = "CP、FT、SLT等")
    private String testArea;

    @Schema(title = "factory", description = "工厂")
    private String factory;

    @Schema(title = "deviceId", description = "deviceId")
    private String deviceId;

    @Schema(title = "testStage", description = "testStage")
    private String testStage;

    @Schema(title = "lotId", description = "lotId")
    private String lotId;

    @Schema(title = "waferNo", description = "waferNo")
    private String waferNo;

    @Schema(title = "lotType", description = "lotType")
    private String lotType;

    @Schema(title = "fileCategory", description = "文件类型")
    private String fileCategory;

    @Schema(title = "dwLayer", description = "dwLayer")
    private String dwLayer;

    @Schema(title = "processStatus", description = "计算状态")
    private String processStatus;

    @Schema(title = "exceptionType", description = "异常类型")
    private String exceptionType;

    @Schema(title = "errorMessage", description = "错误信息")
    private String errorMessage;

    @Schema(title = "taskUrl", description = "spark任务日志地址")
    private String taskUrl;

    @Schema(title = "createTime", description = "DW任务开始时间")
    private String createTime;

    @Schema(title = "updateTime", description = "DW任务更新时间")
    private String updateTime;


    public static DwTaskVo of(WarehousingTaskRecord item, String resourceManagerUrl) {


        String testArea = item.getTestArea() == null ? null : item.getTestArea().getArea();
        String lotType = item.getLotType() == null ? null : item.getLotType().getType();
        String dwLyaer = item.getDwLayer() == null ? null : item.getDwLayer().getLayer();
        String fileCategory = item.getFileCategory() == null ? null : item.getFileCategory().getCategory();
        String status = item.getProcessStatus() == null ? null : item.getProcessStatus().name();
        String exceptionType = item.getExceptionType() == null ? null : item.getExceptionType().getType();
        String taskUrl;
        if (ObjectUtils.isEmpty(item.getAppId())) {
            taskUrl = null;
        } else if (resourceManagerUrl.endsWith(SLASH)) {
            taskUrl = resourceManagerUrl + TASK_SUFFIX + item.getAppId();
        } else {
            taskUrl = resourceManagerUrl + SLASH + TASK_SUFFIX + item.getAppId();
        }

        return DwTaskVo.builder()
                .id(item.getId())
                .testArea(testArea)
                .factory(item.getFactory())
                .deviceId(item.getDeviceId())
                .testStage(item.getTestStage())
                .lotId(item.getLotId())
                .waferNo(item.getWaferNo())
                .lotType(lotType)
                .dwLayer(dwLyaer)
                .fileCategory(fileCategory)
                .processStatus(status)
                .exceptionType(exceptionType)
                .errorMessage(item.getErrorMessage())
                .taskUrl(taskUrl)
                .createTime(DateUtil.format(item.getCreateTime(), NORM_DATETIME_FORMAT))
                .updateTime(DateUtil.format(item.getUpdateTime(), NORM_DATETIME_FORMAT))
                .build();
    }
}
