package com.guwave.datahub.bpms.app.service.bz;

import com.guwave.datahub.bpms.dao.repository.dw.SftpBatchInfoRepository;
import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class SftpBatchInfoCleanService {

    @Autowired
    private SftpBatchInfoRepository sftpBatchInfoRepository;

    @Scheduled(cron = "${spring.scheduler.timer.cleanSftpBatchInfoTask}")
    @ScheduleSwitch
    public void cleanSftpBatchInfo() {
        try {
            List<Long> collectRuleIds = sftpBatchInfoRepository.findAllCollectRuleId();
            for (Long collectRuleId : collectRuleIds) {
                log.info("清理sftpBatchInfo collectRuleId={}", collectRuleId);
                sftpBatchInfoRepository.cleanForCollectRuleId(collectRuleId);
            }
        } catch (Exception e) {
            log.info("清理sftpBatchInfo 异常：", e);
        }
    }
}
