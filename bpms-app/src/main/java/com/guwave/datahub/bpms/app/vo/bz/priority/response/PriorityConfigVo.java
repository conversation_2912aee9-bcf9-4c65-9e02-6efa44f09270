package com.guwave.datahub.bpms.app.vo.bz.priority.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(description = "优先级配置")
public class PriorityConfigVo {
    @Schema(description = "配置ID")
    private Long id;

    @Schema(description = "customer")
    private String customer;

    @Schema(description = "subCustomer")
    private String subCustomer;

    @Schema(description = "factory")
    private String factory;

    @Schema(description = "factorySite")
    private String factorySite;

    @Schema(description = "deviceId")
    private String deviceId;

    @Schema(description = "testStage")
    private String testStage;

    @Schema(title = "lotType", description = "PRODUCTION,ENGINEERING", example = "PRODUCTION")
    private String lotType;

    @Schema(description = "lotId")
    private String lotId;

    @Schema(description = "waferNo")
    private String waferNo;

    @Schema(description = "testArea")
    private String testArea;

    @Schema(description = "fileCategory")
    private String fileCategory;

    @Schema(description = "优先级")
    private String priority;

    @Schema(description = "最小的startT")
    private Date minStartT;

    @Schema(description = "最大的startT")
    private Date maxStartT;

    @Schema(description = "最小FTP修改时间")
    private Date minFtpMtime;

    @Schema(description = "最大FTP修改时间")
    private Date maxFtpMtime;

    @Schema(description = "FTP路径")
    private String ftpPath;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
}
