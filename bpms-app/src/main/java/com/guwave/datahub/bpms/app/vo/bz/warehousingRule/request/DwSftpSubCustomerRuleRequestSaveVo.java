package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(title = "子客户目录规则")
public class DwSftpSubCustomerRuleRequestSaveVo {
    @Schema(description = "子客户目录规则列表")
    List<DwSftpSubCustomerRuleRequestVo> ruleRequestVos;
    @Schema(description = "入库规则流程")
    WarehousingRuleFlowRequestVo warehouseRuleFlowRequestVo;
}
