package com.guwave.datahub.bpms.app.service.bz.handler.clearRulePreview.visitor;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.guwave.datahub.bpms.dao.domain.clearRulePreview.ClearRulePreviewRecord;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.raw.DieData;
import com.guwave.onedata.dataware.common.model.raw.TestItemData;
import com.guwave.onedata.dataware.parser.stdf.ecidParse.EcidDieInfo;
import com.guwave.onedata.dataware.parser.stdf.ecidParse.EcidRuleHandler;
import com.guwave.onedata.dataware.parser.stdf.ecidParse.EcidRuleSetting;
import com.guwave.onedata.dataware.parser.stdf.model.FileMainData;
import com.guwave.onedata.dataware.parser.stdf.uidParse.UidDieInfo;
import com.guwave.onedata.dataware.parser.stdf.uidParse.UidParseHandler;
import com.guwave.onedata.dataware.parser.stdf.uidParse.UidRuleSetting;
import com.guwave.onedata.dataware.parser.stdf.visitor.Visitor;
import com.guwave.onedata.dataware.sdk.model.ecid.CpLotIdInfo;
import com.guwave.onedata.dataware.sdk.model.ecid.ExtraInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

public class MultiThreadVisitor implements Visitor {
    private static final Logger LOGGER = LoggerFactory.getLogger(MultiThreadVisitor.class);

    public static final Set<TestArea> NOT_CAL_TEST_ITEM_TEST_AREAS = ImmutableSet.<TestArea>builder()
            .addAll(TestArea.getCpMapDataSourceList())
            .addAll(TestArea.getCpInklessMapDataDourceList())
            .build();

    private final int threadIndex;
    private final Consumer<FileMainData> fillFileMainDataConsumer;
    private final ClearRulePreviewRecord clearRulePreviewRecord;
    private EcidRuleHandler ecidRuleHandler;
    private UidParseHandler uidParseHandler;
    private FileMainData fileMainData;
    private Long dieDataCnt = 0L;
    private Long testItemDataCnt = 0L;
    private final Map<String, Long> testItemCntMap = new HashMap<>();
    private final Map<Integer, EcidDieInfo> dieEcidMap = new HashMap<>();
    private final Map<Integer, UidDieInfo> dieUidMap = new HashMap<>();


    public MultiThreadVisitor(int threadIndex, Consumer<FileMainData> fillFileMainDataConsumer, ClearRulePreviewRecord record, EcidRuleSetting ecidRule, String configCpLotId, UidRuleSetting uidRule, CpLotIdInfo cpLotIdInfo) {
        this.threadIndex = threadIndex;
        this.fillFileMainDataConsumer = fillFileMainDataConsumer;
        this.clearRulePreviewRecord = record;
        this.ecidRuleHandler = EcidRuleHandler.getEcidRuleHandler(ecidRule, configCpLotId, new ExtraInfo(record.getFileName(), record.getLotId(), record.getSblotId()), cpLotIdInfo);
        this.uidParseHandler = UidParseHandler.getUidParseHandler(uidRule, this.ecidRuleHandler);
    }

    @Override
    public void beforeFile(FileMainData fileMainData) {
        this.fileMainData = fileMainData;
        fillFileMainDataConsumer.accept(fileMainData);
    }

    @Override
    public void handleTestItemData(TestItemData testItemData) {
        if (fileMainData.getFileCategory() == FileCategory.RAW_DATA) {
            if (NOT_CAL_TEST_ITEM_TEST_AREAS.contains(fileMainData.getTestArea())) {
                return;
            } else {
                if (testItemData.getTestNum() == null) {
                    throw new FileLoadException(FileLoadExceptionInfo.RAW_DATA_KEY_FIELD_NULL_EXCEPTION, FileLoadExceptionInfo.RAW_DATA_KEY_FIELD_NULL_EXCEPTION.getMessage().replace(Constant.KEY_FIELD, "TEST_NUM"), Lists.newArrayList("TEST_NUM"));
                }
            }
        }
        testItemDataCnt++;
        testItemCntMap.compute(testItemData.getTestItem(), (key, oldValue) -> {
            if (oldValue == null) {
                oldValue = 0L;
            }
            return oldValue + 1L;
        });

        if (ecidRuleHandler != null) {
            ecidRuleHandler.handle(null, testItemData);
        }
        if (uidParseHandler != null) {
            uidParseHandler.handleTestItemData(testItemData);
        }
    }

    @Override
    public boolean usefulTextDat(String textDat) {
        if (ecidRuleHandler == null) {
            return false;
        }
        return ecidRuleHandler.usefulTextDat(textDat);
    }

    @Override
    public void handleDieData(DieData dieData) {
        dieDataCnt++;
        if (ecidRuleHandler != null) {
            ecidRuleHandler.handle(dieData, null);
        }
        if (TestArea.FT == clearRulePreviewRecord.getGivenTestArea()) {
            dieEcidMap.compute(dieData.getcPartId(), (cPartId, ecidDieInfo) -> {
                if (ecidDieInfo == null) {
                    ecidDieInfo = new EcidDieInfo()
                            .setcPartId(cPartId)
                            .setxCoord(dieData.getxCoord())
                            .setyCoord(dieData.getyCoord());
                    if (StringUtils.isNotEmpty(dieData.getEcid())) {
                        String[] ecidSplit = dieData.getEcid().split(Constant.UNDER_LINE);
                        if (ecidSplit.length >= 4) {
                            String waferLotId = StringUtils.join(Arrays.asList(ecidSplit).subList(0, ecidSplit.length - 3), Constant.UNDER_LINE);
                            String waferNo = Integer.valueOf(ecidSplit[ecidSplit.length - 3]).toString();
                            ecidDieInfo
                                    .setWaferLotId(waferLotId)
                                    .setWaferNo(waferNo)
                                    .setEcid(dieData.getEcid())
                                    .setIsStandardEcid(1);
                        }
                    }
                }
                return ecidDieInfo;
            });
        }
        dieUidMap.compute(dieData.getcPartId(), (cPartId, uidDieInfo) -> {
            if (uidDieInfo == null) {
                uidDieInfo = new UidDieInfo()
                        .setcPartId(cPartId);
                if (StringUtils.isNotEmpty(dieData.getUid())) {
                    uidDieInfo.setUid(dieData.getUid());
                }
            }
            return uidDieInfo;
        });
    }


    @Override
    public void afterFile() {
        parseEcid();
        parseUid();
        LOGGER.info("解析完成");
    }

    private void parseEcid() {
        if (ecidRuleHandler != null) {
            ecidRuleHandler.parseEcid();
            Map<Integer, EcidDieInfo> ecidDieInfoMapTmp = ecidRuleHandler.getEcidDieInfoMap();
            AtomicInteger parseEcidDieCnt = new AtomicInteger();
            AtomicInteger standardEcidDieCnt = new AtomicInteger();
            ecidDieInfoMapTmp.forEach((cPartId, t) -> {
                if (t.isParseFlag()) {
                    parseEcidDieCnt.getAndIncrement();
                    if (t.getIsStandardEcid() == 1) {
                        standardEcidDieCnt.getAndIncrement();
                    }
                    if ((!dieEcidMap.containsKey(cPartId)) || Objects.equals(dieEcidMap.get(cPartId).getIsStandardEcid(), 0)) {
                        dieEcidMap.put(cPartId, t);
                    }
                }
            });
            LOGGER.info("{} threadIndex:{} dieCnt:{} parseEcidDieCnt:{} standardEcidDieCnt:{}", fileMainData.getFileName(), threadIndex, ecidDieInfoMapTmp.size(), parseEcidDieCnt.get(), standardEcidDieCnt.get());
        }
    }

    private void parseUid() {
        if (uidParseHandler != null) {
            uidParseHandler.parseUid();
            Map<Integer, UidDieInfo> uidDieInfoMap = uidParseHandler.getUidDieInfoMap();
            AtomicInteger parseUidDieCnt = new AtomicInteger();
            AtomicInteger standardUidDieCnt = new AtomicInteger();
            uidDieInfoMap.forEach((cPartId, t) -> {
                if (t.isParseFlag()) {
                    parseUidDieCnt.getAndIncrement();
                    if (StringUtils.isNotEmpty(t.getUid())) {
                        standardUidDieCnt.getAndIncrement();
                        dieUidMap.put(cPartId, t);
                    } else if (StringUtils.isEmpty(dieUidMap.get(cPartId).getUid())) {
                        dieUidMap.put(cPartId, t);
                    }
                }
            });
            LOGGER.info("{} threadIndex:{} dieCnt:{} parseUidDieCnt:{} standardUidDieCnt:{}", fileMainData.getFileName(), threadIndex, uidDieInfoMap.size(), parseUidDieCnt.get(), standardUidDieCnt.get());
        }
    }

    @Override
    public void close() {
    }

    public Long getDieDataCnt() {
        return dieDataCnt;
    }

    public Long getTestItemDataCnt() {
        return testItemDataCnt;
    }

    public Map<String, Long> getTestItemCntMap() {
        return testItemCntMap;
    }


    public Map<Integer, EcidDieInfo> getEcidDieInfoMap() {
        return dieEcidMap;
    }

    public List<String> getParameterText() {
        if (this.ecidRuleHandler != null) {
            return ecidRuleHandler.getParameterText();
        }
        return new ArrayList<>();
    }

    public Map<Integer, UidDieInfo> getUidDieInfoMap() {
        return dieUidMap;
    }

}
