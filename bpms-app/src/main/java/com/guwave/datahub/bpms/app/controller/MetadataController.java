package com.guwave.datahub.bpms.app.controller;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.License;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.service.bz.MetadataService;
import com.guwave.datahub.bpms.app.vo.bz.metadata.request.*;
import com.guwave.datahub.bpms.app.vo.bz.metadata.response.*;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * MetadataController
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-03-11 15:38:41
 */
@Log
@Auth
//@License
@Slf4j
@Validated
@RestController
@AllArgsConstructor
@Tag(name = "MetadataControllerAPI", description = "元数据管理接口",
        externalDocs = @ExternalDocumentation(description = "需求文档",
                url = "https://ohjj5az3be.feishu.cn/wiki/Qwpnwk9zZi4wNikYpZzc9nlGnlf"))
@RequestMapping("metadata")
public class MetadataController {

    private final MetadataService metadataService;

    @Operation(summary = "表文档页面 根据数据源查询库名")
    @GetMapping("getDatabases")
    public ResponseVo<List<String>> getDatabases(@RequestParam("datasource") @NotBlank String source) {
        return ResponseVo.ok(this.metadataService.getDatabases(source));
    }

    @Operation(summary = "表文档页面 根据数据源和库名查询表名")
    @GetMapping("getTableNames")
    public ResponseVo<List<String>> getTableNames(@RequestParam("datasource") @NotBlank String source, @RequestParam("databaseName") @NotBlank String database) {
        return ResponseVo.ok(this.metadataService.getTableNames(source, database));
    }

    @Operation(summary = "表文档页面 根据表查询描述")
    @PostMapping("getDescription")
    public ResponseVo<MetadataTableDescriptionVo> getDescription(@RequestBody @Validated TableVo tableQuery) {
        return ResponseVo.ok(this.metadataService.getDescription(tableQuery));
    }

    @Operation(summary = "表文档页面 修改描述")
    @PostMapping("updateDescription")
    public ResponseVo<Void> updateDescription(@RequestBody @Validated DescriptionUpdateVo descriptionUpdateVo) {
        this.metadataService.updateDescription(descriptionUpdateVo);
        return ResponseVo.ok();
    }

    @Operation(summary = "表文档页面 根据表查询链接")
    @PostMapping("getLinks")
    public ResponseVo<List<MetadataTableLinkVo>> getLinks(@RequestBody @Validated TableVo tableQuery) {
        return ResponseVo.ok(this.metadataService.getLinks(tableQuery));
    }

    @Operation(summary = "表文档页面 添加链接")
    @PostMapping("addLink")
    public ResponseVo<List<MetadataTableLinkVo>> addLink(@RequestBody @Validated LinkAddVo linkAdd) {
        return ResponseVo.ok(this.metadataService.addLink(linkAdd));
    }

    @Operation(summary = "表文档页面 删除链接")
    @DeleteMapping("deleteLink")
    public ResponseVo<List<MetadataTableLinkVo>> deleteLink(@RequestParam("id") @Min(1) Long id) {
        return ResponseVo.ok(this.metadataService.deleteLink(id));
    }

    @Operation(summary = "表文档页面 根据表分页查询字段列表")
    @PostMapping("getSchema")
    public ResponseVo<PageableDataVo<MetadataSchemaVo>> getSchema(@RequestBody @Validated TableByPageVo tableQuery) {
        return ResponseVo.ok(this.metadataService.getSchema(tableQuery));
    }

    @Operation(summary = "表文档页面 修改字段信息")
    @PostMapping("updateField")
    public ResponseVo<Void> updateField(@RequestBody @Validated FieldUpdateVo fieldUpdateVo) {
        this.metadataService.updateField(fieldUpdateVo);
        return ResponseVo.ok();
    }

    @Operation(summary = "表文档页面 删除字段")
    @DeleteMapping("deleteField")
    public ResponseVo<Void> deleteField(@RequestBody @Validated FieldDeleteVo fieldDeleteVo) {
        this.metadataService.deleteField(fieldDeleteVo);
        return ResponseVo.ok();
    }

    @Operation(summary = "表文档页面 从表重新解析字段列表")
    @PostMapping("reloadSchema")
    public ResponseVo<PageableDataVo<MetadataSchemaVo>> reloadSchema(@RequestBody @Validated TableByPageVo tableQuery) {
        return ResponseVo.ok(this.metadataService.reloadSchema(tableQuery));
    }

    @Operation(summary = "表血缘页面 获取表级血缘")
    @PostMapping("getTableLineage")
    public ResponseVo<MetadataTableLineageVo> getTableLineage(@RequestBody @Validated TableVo tableQuery) {
        return ResponseVo.ok(this.metadataService.getTableLineage(tableQuery));
    }

    @Operation(summary = "表血缘页面 获取字段血缘")
    @GetMapping("getFieldLineage")
    public ResponseVo<List<MetadataFieldLineageVo>> getFieldLineage(@RequestParam("id") @Min(1) Long id) {
        return ResponseVo.ok(this.metadataService.getFieldLineage(id));
    }

    @Operation(summary = "表血缘页面 获取字段信息")
    @GetMapping("getFieldInfo")
    public ResponseVo<MetadataSchemaVo> getFieldInfo(@RequestParam("id") @Min(1) Long id) {
        return ResponseVo.ok(this.metadataService.getFieldInfo(id));
    }
}
