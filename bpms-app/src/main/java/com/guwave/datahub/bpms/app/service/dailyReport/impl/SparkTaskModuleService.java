package com.guwave.datahub.bpms.app.service.dailyReport.impl;

import com.alibaba.fastjson2.JSON;
import com.guwave.datahub.bpms.app.service.dailyReport.CommonService;
import com.guwave.datahub.bpms.app.util.DateUtil;
import com.guwave.datahub.bpms.app.util.PdfUtil;
import com.guwave.datahub.bpms.common.constant.ChartData;
import com.guwave.datahub.bpms.common.constant.ChartType;
import com.guwave.datahub.bpms.common.constant.DataCode;
import com.guwave.datahub.bpms.common.constant.ReportModule;
import com.guwave.datahub.bpms.dao.repository.bpms.DailyReportResultRepository;
import com.guwave.datahub.bpms.dao.repository.compute.ComputePoolRepository;
import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.SparkFailTaskResult;
import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.common.constant.SparkExceptionMessage;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputePool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class SparkTaskModuleService implements CommonService {

    @Autowired
    private DailyReportResultRepository dailyReportResultRepository;
    @Autowired
    private ComputePoolRepository computeComputePoolRepository;

    public void generateSparkFailTaskDetail(PdfUtil pdfUtil) throws IOException {
        SparkFailTaskResult result = new SparkFailTaskResult();
        List<ComputePool> sparkFailTask = computeComputePoolRepository.findAllByProcessStatusAndUpdateTimeBetween(ProcessStatus.FAIL, DateUtil.getDateFormattedStartTimeBeforeDays(1), DateUtil.getDateFormattedStartTimeBeforeDays(0));
        Map<String, Long> sparkFailTaskMap = sparkFailTask
                .stream().collect(Collectors.groupingBy(t -> SparkExceptionMessage.getExceptionMessage(t.getErrorMessage()).getValue(), Collectors.counting()));

        result.setFailTaskCnt((long) sparkFailTask.size());
        result.setExceptionTypeMap(sparkFailTaskMap);

        saveDailyReportResult(JSON.toJSONString(result), ReportModule.SPARK_TASK, DataCode.FAIL_SPARK_TASK);

        pdfUtil.addBodyText("Spark任务失败个数：" + result.getFailTaskCnt());
        if (result.getFailTaskCnt() > 0) {
            pdfUtil.addChart(
                    result.getExceptionTypeMap().entrySet().stream().map(t -> new ChartData(t.getKey(), t.getValue())).collect(Collectors.toList()),
                    ChartType.PIE,
                    "Spark任务失败原因分布图"
            );
        }
    }

    @Override
    public DailyReportResultRepository getDailyReportResultRepository() {
        return dailyReportResultRepository;
    }
}
