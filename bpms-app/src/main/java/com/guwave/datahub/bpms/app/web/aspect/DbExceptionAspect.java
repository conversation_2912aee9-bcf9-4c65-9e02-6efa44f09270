package com.guwave.datahub.bpms.app.web.aspect;

import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.exception.BpmsInternalException;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * DbExceptionAspect
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-28 20:14:58
 */
@Aspect
@Component
public class DbExceptionAspect {

    @Pointcut("execution(* com.guwave.datahub.bpms.dao.repository..*.*(..))")
    public void dbException() {
    }

    /**
     * 统一成BpmsInternalException, ResponseCode.Internal.INTERNAL_DB_ERROR
     *
     * @param joinPoint joinPoint
     * @return object
     */
    @Around("dbException()")
    public Object aroundDbException(ProceedingJoinPoint joinPoint) throws Throwable {
        Object proceed;
        try {
            // 将应用层的数据库错误 捕获并进行转换  主要捕获 sql形式的异常
            proceed = joinPoint.proceed();
        } catch (BpmsException bpmsException) {
            throw bpmsException;
        } catch (Exception sqlException) {
            throw new BpmsInternalException(sqlException, ResponseCode.Internal.INTERNAL_DB_ERROR);
        }
        return proceed;
    }
}
