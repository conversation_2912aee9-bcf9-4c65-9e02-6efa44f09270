package com.guwave.datahub.bpms.app.sink;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class KafkaSink {
    @Autowired
    private KafkaTemplate<byte[], byte[]> kafkaTemplate;


    public void send(String topic, String value) {
        byte[] message = value.getBytes(StandardCharsets.UTF_8);
        this.kafkaTemplate.send(topic, message).addCallback(success -> {
            // 消息发送到的topic
            assert success != null;
            // 消息发送到的分区
            int partition = success.getRecordMetadata().partition();
            // 消息在分区内的offset
            long offset = success.getRecordMetadata().offset();
            log.info("发送消息成功, topic: {}, partition: {}, offset: {}", topic, partition, offset);
        }, fail -> {
            log.error("发送消息失败 {}", value, fail);
            throw new RuntimeException("发送消息失败");
        });
    }

    public void send(String topic, String key, String value) {
        byte[] message = value.getBytes(StandardCharsets.UTF_8);
        this.kafkaTemplate.send(topic, key.getBytes(StandardCharsets.UTF_8), message).addCallback(success -> {
            // 消息发送到的topic
            assert success != null;
            // 消息发送到的分区
            int partition = success.getRecordMetadata().partition();
            // 消息在分区内的offset
            long offset = success.getRecordMetadata().offset();
            log.info("发送消息成功, topic: {}, partition: {}, offset: {}", topic, partition, offset);
        }, fail -> {
            log.error("发送消息失败", fail);
            throw new RuntimeException("发送消息失败");
        });
    }

    public void send(String topic, byte[] message) {
        this.kafkaTemplate.send(topic, message).addCallback(success -> {
            // 消息发送到的topic
            assert success != null;
            // 消息发送到的分区
            int partition = success.getRecordMetadata().partition();
            // 消息在分区内的offset
            long offset = success.getRecordMetadata().offset();
            log.info("发送消息成功, topic: {}, partition: {}, offset: {}", topic, partition, offset);
        }, fail -> {
            log.error("发送消息失败", fail);
            throw new RuntimeException("发送消息失败");
        });
    }

    public void send(String topic, byte[] key, byte[] message) {
        this.kafkaTemplate.send(topic, key, message).addCallback(success -> {
            // 消息发送到的topic
            assert success != null;
            // 消息发送到的分区
            int partition = success.getRecordMetadata().partition();
            // 消息在分区内的offset
            long offset = success.getRecordMetadata().offset();
            log.info("发送消息成功, topic: {}, partition: {}, offset: {}", topic, partition, offset);
        }, fail -> {
            log.error("发送消息失败", fail);
            throw new RuntimeException("发送消息失败");
        });
    }
}
