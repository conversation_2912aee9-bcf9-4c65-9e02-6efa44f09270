package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.guwave.datahub.bpms.app.converter.FileCategoryConverter;
import com.guwave.datahub.bpms.app.converter.TestAreaConverter;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.TestArea;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BzWarehousingRuleFlow {
    private String name;
    private String subCustomer;
    private String factory;
    @ExcelProperty(converter = TestAreaConverter.class)
    private TestArea testArea;
    @ExcelProperty(converter = FileCategoryConverter.class)
    private FileCategory fileCategory;
    private String remarks;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BzWarehousingRuleFlow that = (BzWarehousingRuleFlow) o;
        return Objects.equals(name, that.name) && Objects.equals(subCustomer, that.subCustomer) && Objects.equals(factory, that.factory) && testArea == that.testArea && fileCategory == that.fileCategory && Objects.equals(remarks, that.remarks);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, subCustomer, factory, testArea, fileCategory, remarks);
    }
}
