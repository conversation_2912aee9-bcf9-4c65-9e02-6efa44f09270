package com.guwave.datahub.bpms.app.vo.bz.computeManage.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
@Schema(description = "业务计算管理配置")
public class ComputeManageConfig {

    @Schema(description = "ID")
    private Long id;

    @NotBlank(message = "testArea不能为空")
    @Schema(description = "testArea：CP/FT/WAT 等")
    private String testArea;

    @Schema(description = "Lot类型，Engineering/Production等")
    private String lotType;

    @NotBlank(message = "fileCategory不能为空")
    @Schema(description = "fileCategory,STDF、RAW_DATA")
    private String fileCategory;

    @NotBlank(message = "factory不能为空")
    @Schema(description = "factory")
    private String factory;

    @NotBlank(message = "factorySite不能为空")
    @Schema(description = "factorySite")
    private String factorySite;

    @Schema(description = "device id")
    private String deviceId;

    @Schema(description = "test stage")
    private String testStage;

    @Schema(description = "是否计算dw测项相关表")
    private Integer calculateDwTestItem = 1;

    @Schema(description = "是否计算yms测项相关表")
    private Integer calculateYmsTestItem = 1;

    @Schema(description = "状态：0->关闭，1->启动")
    private Integer status = 0;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "创建用户")
    private String createUser;

    @Schema(description = "更新用户")
    private String updateUser;
}
