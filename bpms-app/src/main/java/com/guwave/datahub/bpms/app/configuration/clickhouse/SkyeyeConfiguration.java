package com.guwave.datahub.bpms.app.configuration.clickhouse;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

@Slf4j
@Configuration
@MapperScan(
        basePackages = "com.guwave.datahub.bpms.dao.repository.logs",
        sqlSessionFactoryRef = "skyeyeSqlSessionFactory"
)
public class SkyeyeConfiguration {

    @Autowired
    JpaClickhouseConfiguration jpaClickhouseConfiguration;

    @Value("${spring.ck.skyeye.database}")
    private String database;
    
    // 配置MyBatis
    @Bean(name = "skyeyeSqlSessionFactory")
    public SqlSessionFactory skyeyeSqlSessionFactory() throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(skyeyeDataSource());
        sessionFactory.setMapperLocations(
                ArrayUtils.addAll(
                    new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/clickhouse/skyeye/*.xml"),
                    new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml")
                )
        );
        
        return sessionFactory.getObject();
    }

    @Bean(name = "skyeyeDataSource")
    public DataSource skyeyeDataSource() {
        return jpaClickhouseConfiguration.getDataSource(database);
    }
    
    @Bean(name = "skyeyeMybatisTransactionManager")
    public PlatformTransactionManager skyeyeMybatisTransactionManager() {
        return new DataSourceTransactionManager(skyeyeDataSource());
    }
}