package com.guwave.datahub.bpms.app.service.dailyReport.impl;

import com.guwave.datahub.bpms.app.service.dailyReport.CommonService;
import com.guwave.datahub.bpms.app.util.*;
import com.guwave.datahub.bpms.app.vo.dailyReport.model.AutoAbnormalFile;
import com.guwave.datahub.bpms.app.vo.dailyReport.model.TimeOutSparkTask;
import com.guwave.datahub.bpms.dao.repository.bpms.DailyReportResultRepository;
import com.guwave.datahub.bpms.dao.repository.dw.LotStockingDetailRepository;
import com.guwave.datahub.bpms.dao.repository.dw.ManualFileInfoRepository;
import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.ManualTaskDetail;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotStockingDetail;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualFileInfo;
import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputePool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.EMPTY;
import static com.guwave.onedata.dataware.common.contant.Constant.UNDER_LINE;

@Service
public class AbnormalDataModuleService implements CommonService {

    @Autowired
    private DailyReportResultRepository dailyReportResultRepository;
    @Autowired
    private com.guwave.datahub.bpms.dao.repository.compute.ComputePoolRepository computeComputePoolRepository;
    @Autowired
    private LotStockingDetailRepository lotStockingDetailRepository;

    @Autowired
    private ManualFileInfoRepository manualFileInfoRepository;

    @Autowired
    private HdfsUtil hdfsUtil;

    public void generateAbnormalDataDetal(PdfUtil pdfUtil, String localDir, String hdfsDir) throws IOException {
        String day = DateUtil.getDayBefortDays(1);
        pdfUtil.addSecondaryHeading("异常数据");
        List<TimeOutSparkTask> timeOutSparkTasks = computeComputePoolRepository.findAllByProcessStatusAndUpdateTimeBetween(ProcessStatus.CANCELLED, DateUtil.getDateFormattedStartTimeBeforeDays(1), DateUtil.getDateFormattedStartTimeBeforeDays(0))
                        .stream().map(this::buildTimeOutSparkTask).collect(Collectors.toList());
        pdfUtil.addBodyText("运行时间超过预期的spark任务个数：" + timeOutSparkTasks.size());

        String abnormalSparkTaskFile = FileUtil.getFileFullName(localDir, "Spark任务超时明细" + UNDER_LINE + day + ".xlsx");
        ExcelUtil.exportDataWithDefaultStyle(Files.newOutputStream(Paths.get(abnormalSparkTaskFile)), timeOutSparkTasks, TimeOutSparkTask.class, null);
        hdfsUtil.uploadToHDFSByFileSystem(abnormalSparkTaskFile, hdfsDir, 1);

        List<AutoAbnormalFile> abnormalFile = lotStockingDetailRepository.findAbnormalFileByCreateTime(DateUtil.getStringFormattedStartTimeBeforeDays(1), DateUtil.getStringFormattedStartTimeBeforeDays(0))
                .stream().map(this::buildAutoAbnormalFile).collect(Collectors.toList());
        pdfUtil.addBodyText("单测项过多的文件个数：" + abnormalFile.size());

        String abnormalFilePath = FileUtil.getFileFullName(localDir, "自动入库单测项die超过100W明细" + UNDER_LINE + day + ".xlsx");
        ExcelUtil.exportDataWithDefaultStyle(Files.newOutputStream(Paths.get(abnormalFilePath)), abnormalFile, LotStockingDetail.class, null);
        hdfsUtil.uploadToHDFSByFileSystem(abnormalFilePath, hdfsDir, 1);

        List<AutoAbnormalFile> manualAbnormalFile = manualFileInfoRepository.findAbnormalFileByCreateTime(DateUtil.getStringFormattedStartTimeBeforeDays(1), DateUtil.getStringFormattedStartTimeBeforeDays(0))
                .stream().map(this::buildAutoAbnormalFile).collect(Collectors.toList());
        pdfUtil.addBodyText("手动上传单测项过多的文件个数：" + manualAbnormalFile.size());

        String manualAbnormalFilePath = FileUtil.getFileFullName(localDir, "手动上传单测项die超过100W明细" + UNDER_LINE + day + ".xlsx");
        ExcelUtil.exportDataWithDefaultStyle(Files.newOutputStream(Paths.get(manualAbnormalFilePath)), manualAbnormalFile, ManualTaskDetail.class, null);
        hdfsUtil.uploadToHDFSByFileSystem(manualAbnormalFilePath, hdfsDir, 1);
    }

    private TimeOutSparkTask buildTimeOutSparkTask(ComputePool computePool) {
        return new TimeOutSparkTask(computePool.getUniqueId(),
                computePool.getComputeCode(),
                computePool.getDieCnt(),
                computePool.getTestItemCnt(),
                computePool.getComputeEngine() == null ? EMPTY : computePool.getComputeEngine().getEngine(),
                computePool.getComputeType() == null ? EMPTY : computePool.getComputeType().getType(),
                computePool.getQueue(),
                computePool.getNumExecutors(),
                computePool.getExecutorCores(),
                computePool.getExecutorMemory(),
                computePool.getDriverMemory(),
                computePool.getParallelism(),
                computePool.getPriorityGroup() == null ? 0 : computePool.getPriorityGroup().getPriorityGroup(),
                computePool.getPriority(),
                computePool.getHdfsResultPartition(),
                computePool.getExtraConf(),
                computePool.getJarPath(),
                computePool.getMainClass(),
                computePool.getExtraFiles(),
                computePool.getVersion(),
                computePool.getUseDynamicResource(),
                computePool.getSinkType() == null ? EMPTY : computePool.getSinkType().getType(),
                computePool.getFailCnt(),
                computePool.getProcessStatus() == null ? EMPTY : computePool.getProcessStatus().name(),
                computePool.getSubmitMode() == null ? EMPTY : computePool.getSubmitMode().getMode(),
                computePool.getAppId(),
                computePool.getStageId(),
                computePool.getAppName(),
                computePool.getExceptionType() == null ? EMPTY : computePool.getExceptionType().getType(),
                computePool.getErrorMessage(),
                computePool.getStartTime(),
                computePool.getEndTime(),
                computePool.getExecuteTime(),
                computePool.getCancelCnt(),
                computePool.getActlStartTime(),
                computePool.getActlExecuteTime(),
                computePool.getEstExecuteTime(),
                computePool.getAccEqExecuteTime(),
                computePool.getCheckExecuteTime()
        );
    }

    private AutoAbnormalFile buildAutoAbnormalFile(LotStockingDetail lotStockingDetail) {
        return new AutoAbnormalFile(lotStockingDetail.getCustomer(),
                lotStockingDetail.getSubCustomer(),
                lotStockingDetail.getTestArea() == null ? EMPTY : lotStockingDetail.getTestArea().getArea(),
                lotStockingDetail.getFactory(),
                lotStockingDetail.getFactorySite(),
                lotStockingDetail.getFileType() == null ? EMPTY : lotStockingDetail.getFileType().getType(),
                lotStockingDetail.getFileCategory() == null ? EMPTY : lotStockingDetail.getFileCategory().getCategory(),
                lotStockingDetail.getFileName(),
                lotStockingDetail.getOriginFileName(),
                lotStockingDetail.getDeviceId(),
                lotStockingDetail.getLotId(),
                lotStockingDetail.getWaferId(),
                lotStockingDetail.getOriginWaferId(),
                lotStockingDetail.getWaferNo(),
                lotStockingDetail.getLotType() == null ? EMPTY : lotStockingDetail.getLotType().getType(),
                lotStockingDetail.getSblotId(),
                lotStockingDetail.getTestStage(),
                lotStockingDetail.getStartT(),
                lotStockingDetail.getFinishT(),
                lotStockingDetail.getDieCount(),
                lotStockingDetail.getWarehousingMode() == null ? EMPTY : lotStockingDetail.getWarehousingMode().getMode(),
                lotStockingDetail.getDieDataCount(),
                lotStockingDetail.getTestItemDataCount(),
                lotStockingDetail.getMaxRecordTestItem(),
                lotStockingDetail.getMaxRecordTestItemCnt(),
                lotStockingDetail.getMinRecordTestItem(),
                lotStockingDetail.getMinRecordTestItemCnt());
    }

    private AutoAbnormalFile buildAutoAbnormalFile(ManualFileInfo manualFileInfo) {
        return new AutoAbnormalFile(manualFileInfo.getCustomer(),
                manualFileInfo.getSubCustomer(),
                manualFileInfo.getTestArea() == null ? EMPTY : manualFileInfo.getTestArea().getArea(),
                manualFileInfo.getFactory(),
                manualFileInfo.getFactorySite(),
                EMPTY,
                manualFileInfo.getFileCategory() == null ? EMPTY : manualFileInfo.getFileCategory().getCategory(),
                manualFileInfo.getFileName(),
                manualFileInfo.getFileName(),
                manualFileInfo.getDeviceId(),
                manualFileInfo.getLotId(),
                manualFileInfo.getWaferId(),
                manualFileInfo.getOriginWaferId(),
                manualFileInfo.getWaferNo(),
                manualFileInfo.getLotType() == null ? EMPTY : manualFileInfo.getLotType().getType(),
                manualFileInfo.getSblotId(),
                manualFileInfo.getTestStage(),
                manualFileInfo.getStartT().toString(),
                manualFileInfo.getFinishT().toString(),
                manualFileInfo.getDieDataCount() == null ? 0 : manualFileInfo.getDieDataCount().intValue(),
                EMPTY,
                manualFileInfo.getDieDataCount() == null ? 0 : manualFileInfo.getDieDataCount().intValue(),
                manualFileInfo.getTestItemDataCount(),
                manualFileInfo.getMaxRecordTestItem(),
                manualFileInfo.getMaxRecordTestItemCnt(),
                manualFileInfo.getMinRecordTestItem(),
                manualFileInfo.getMinRecordTestItemCnt());
    }

    @Override
    public DailyReportResultRepository getDailyReportResultRepository() {
        return dailyReportResultRepository;
    }
}
