package com.guwave.datahub.bpms.app.vo.bz.task.response;

import cn.hutool.core.date.DateUtil;
import com.guwave.onedata.linkx.dao.mysql.domain.bz.BzAppInstanceRecord;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_FORMAT;

/**
 * <AUTHOR>
 * @date 2024/3/12
 * @description TaskBzAppInstanceDetailVo
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(title = "YMS任务管理信息")
public class YmsTaskVo {
    @Schema(title = "id")
    private Long id;

    @Schema(title = "name", description = "app的名字(默认definition的定义的名字加-时间戳)")
    private String name;

    @Schema(title = "optType", description = "任务操作类型：NEW_DATA、MODIFY_WAFERMAP_CONFIG")
    private String optType;

    @Schema(title = "executeEngine", description = "执行引擎: CLICKHOUSE/SPARK/PYTHON")
    private String executeEngine;

    @Schema(title = "testArea", description = "CP、FT、SLT等")
    private String testArea;

    @Schema(title = "factory", description = "工厂")
    private String factory;

    @Schema(title = "deviceId", description = "deviceId")
    private String deviceId;

    @Schema(title = "testStage", description = "testStage")
    private String testStage;

    @Schema(title = "lotId", description = "lotId")
    private String lotId;

    @Schema(title = "waferNo", description = "waferNo")
    private String waferNo;

    @Schema(title = "lotType", description = "lotType")
    private String lotType;

    @Schema(title = "startStep", description = "开始执行的步骤")
    private Integer startStep;

    @Schema(title = "executeTime", description = "计算耗时(ms)")
    private Long executeTime;

    @Schema(title = "processStatus", description = "计算状态")
    private String processStatus;

    @Schema(title = "exceptionType", description = "异常类型")
    private String exceptionType;

    @Schema(title = "errorMessage", description = "错误信息")
    private String errorMessage;

    @Schema(title = "createTime", description = "YMS任务开始时间")
    private String createTime;

    @Schema(title = "updateTime", description = "YMS任务更新时间")
    private String updateTime;

    public static YmsTaskVo of(BzAppInstanceRecord bzAppInstanceRecord) {
        String optType = bzAppInstanceRecord.getOptType() == null ? null : bzAppInstanceRecord.getOptType().getType();
        String executeEngine = bzAppInstanceRecord.getExecuteEngine() == null ? null : bzAppInstanceRecord.getExecuteEngine().getEngine();
        String testArea = bzAppInstanceRecord.getTestArea() == null ? null : bzAppInstanceRecord.getTestArea().getArea();
        String lotType = bzAppInstanceRecord.getLotType() == null ? null : bzAppInstanceRecord.getLotType().getType();
        String status = bzAppInstanceRecord.getProcessStatus() == null ? null : bzAppInstanceRecord.getProcessStatus().name();
        String type = bzAppInstanceRecord.getExceptionType() == null ? null : bzAppInstanceRecord.getExceptionType().getType();

        return YmsTaskVo.builder()
                .name(bzAppInstanceRecord.getName())
                .id(bzAppInstanceRecord.getId())
                .optType(optType)
                .executeEngine(executeEngine)
                .testArea(testArea)
                .factory(bzAppInstanceRecord.getFactory())
                .deviceId(bzAppInstanceRecord.getDeviceId())
                .testStage(bzAppInstanceRecord.getTestStage())
                .lotId(bzAppInstanceRecord.getLotId())
                .waferNo(bzAppInstanceRecord.getWaferNo())
                .lotType(lotType)
                .startStep(bzAppInstanceRecord.getStartStep())
                .executeTime(bzAppInstanceRecord.getExecuteTime())
                .processStatus(status)
                .exceptionType(type)
                .errorMessage(bzAppInstanceRecord.getErrorMessage())
                .createTime(DateUtil.format(bzAppInstanceRecord.getCreateTime(), NORM_DATETIME_FORMAT))
                .updateTime(DateUtil.format(bzAppInstanceRecord.getUpdateTime(), NORM_DATETIME_FORMAT))
                .build();
    }
}
