package com.guwave.datahub.bpms.app.controller;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.DataPermission;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.service.bz.DataTimelinessService;
import com.guwave.datahub.bpms.app.vo.bz.dataTimeliness.request.DataTimelinessDropDownFilterVo;
import com.guwave.datahub.bpms.app.vo.bz.dataTimeliness.response.DataTimelinessDropDownVo;
import com.guwave.datahub.bpms.app.vo.bz.dataTimeliness.request.DataTimelinessQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.dataTimeliness.response.DataTimelinessVo;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@Log
@Auth
//@License
@Validated
@Slf4j
@RestController
@AllArgsConstructor
@Tag(name = "DataTimelinessControllerAPI", description = "数据入库实效性接口",
        externalDocs = @ExternalDocumentation(description = "需求文档",
                url = "")
)
@RequestMapping("dataTimeliness")
public class DataTimelinessController {

    private DataTimelinessService dataTimelinessService;

    @Operation(summary = "下拉框查询")
    @DataPermission
    @PostMapping("filter")
    public ResponseVo<List<DataTimelinessDropDownVo>> dataTimelinessFilter(@RequestBody @Validated DataTimelinessDropDownFilterVo dataTimelinessDropDownFilterVo) {
        return ResponseVo.ok(dataTimelinessService.dataTimelinessFiter(dataTimelinessDropDownFilterVo));
    }


    @Operation(summary = "数据入库实效性接口")
    @DataPermission
    @PostMapping("dbtime")
    public ResponseVo<DataTimelinessVo> dataTimeliness(@RequestBody @Valid DataTimelinessQueryVo baseDTO) {
        return ResponseVo.ok(dataTimelinessService.dataTimeliness(baseDTO));
    }
}
