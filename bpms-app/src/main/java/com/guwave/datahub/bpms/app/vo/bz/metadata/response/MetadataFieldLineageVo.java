package com.guwave.datahub.bpms.app.vo.bz.metadata.response;

import com.guwave.datahub.bpms.app.vo.bz.metadata.request.TableVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
public class MetadataFieldLineageVo {

    @Schema(title = "表信息")
    private TableVo table;

    @Schema(title = "字段", description = "这张表需要列出的所有字段", example = "{\"FACTORY_SITE\":22, \"ECID_CP_STAGE_ID\": 24}")
    private Map<String, Long> fields;

    public MetadataFieldLineageVo(TableVo table, Map<String, Long> fields) {
        this.table = table;
        this.fields = fields;
    }
}
