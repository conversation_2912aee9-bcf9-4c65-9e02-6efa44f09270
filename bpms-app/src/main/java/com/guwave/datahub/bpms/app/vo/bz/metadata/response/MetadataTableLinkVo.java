package com.guwave.datahub.bpms.app.vo.bz.metadata.response;

import com.guwave.datahub.bpms.dao.domain.metadata.MetadataTableLink;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class MetadataTableLinkVo {

    @Schema(title = "ID", example = "2")
    private Long id;

    @Schema(title = "数据库", description = "clickhouse/mysql", example = "clickhouse")
    private String datasource;

    @Schema(title = "库名", example = "ods")
    private String databaseName;

    @Schema(title = "表名", example = "ods_dc_device_info_cluster")
    private String tableName;

    @Schema(title = "链接名", example = "元数据管理-MetaData")
    private String linkName;

    @Schema(title = "链接", example = "https://ohjj5az3be.feishu.cn/wiki/Qwpnwk9zZi4wNikYpZzc9nlGnlf")
    private String link;

    public static MetadataTableLinkVo of(MetadataTableLink metadataTableLink) {
        MetadataTableLinkVo metadataTableLinkVo = new MetadataTableLinkVo();
        metadataTableLinkVo.setId(metadataTableLink.getId());
        metadataTableLinkVo.setDatasource(metadataTableLink.getDatasource());
        metadataTableLinkVo.setDatabaseName(metadataTableLink.getDatabaseName());
        metadataTableLinkVo.setTableName(metadataTableLink.getTableName());
        metadataTableLinkVo.setLinkName(metadataTableLink.getLinkName());
        metadataTableLinkVo.setLink(metadataTableLink.getLink());
        return metadataTableLinkVo;
    }
}
