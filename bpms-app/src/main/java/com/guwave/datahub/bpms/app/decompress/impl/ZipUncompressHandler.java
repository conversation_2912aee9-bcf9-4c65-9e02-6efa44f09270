package com.guwave.datahub.bpms.app.decompress.impl;

import com.guwave.datahub.bpms.app.decompress.UnCompressHandler;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;

public class ZipUncompressHandler implements UnCompressHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ZipUncompressHandler.class);

    @Override
    public void unCompress(File file, File targetDir) throws Exception {
        try (InputStream inputStream = new FileInputStream(file);
             ZipArchiveInputStream zipArchiveInputStream = new ZipArchiveInputStream(inputStream)) {
            byte[] buffer = new byte[1024 * 5];
            ArchiveEntry archiveEntry;
            while (null != (archiveEntry = zipArchiveInputStream.getNextEntry())) {
                String archiveEntryFileName = archiveEntry.getName();
                File entryFile = new File(targetDir, archiveEntryFileName);
                if (!archiveEntry.isDirectory()) {
                    if (!entryFile.getParentFile().exists()) {
                        entryFile.getParentFile().mkdirs();
                    }
                    try (OutputStream outputStream = new FileOutputStream(entryFile)) {
                        int length;
                        while ((length = zipArchiveInputStream.read(buffer)) != -1) {
                            outputStream.write(buffer, 0, length);
                        }
                        outputStream.flush();
                    } catch (Exception e) {
                        LOGGER.info("解压失败：{} --> {}", file.getAbsolutePath(), targetDir, e);
                        throw e;
                    }
                } else {
                    if (!entryFile.exists()) {
                        entryFile.mkdirs();
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.info("解压失败：{} --> {}", file.getAbsolutePath(), targetDir, e);
            throw e;
        }
    }
}
