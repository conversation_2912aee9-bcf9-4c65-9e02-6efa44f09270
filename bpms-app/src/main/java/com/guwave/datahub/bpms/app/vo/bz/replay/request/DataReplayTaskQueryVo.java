package com.guwave.datahub.bpms.app.vo.bz.replay.request;

import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(title = "数据重播任务查询请求体")
@Data
@EqualsAndHashCode(callSuper = false)
public class DataReplayTaskQueryVo {
    @Schema(title = "id", example = "1")
    @NotNull
    private Long id;

    @Schema(title = "lotIdList", example = "[\"NT41A\"]")
    private List<String> lotIdList;

    @Schema(title = "waferNoList", example = "[\"8\"]")
    private List<String> waferNoList;

    @Schema(title = "testStageList", example = "[\"CP1\"]")
    private List<String> testStageList;

    @Schema(title = "statusList", description = "重播状态", example = "[\"FAIL\"]")
    private List<ProcessStatus> statusList;

    @Schema(title = "exceptionMessageList", example = "[\"RAW_DATA文件转换异常，RAW_DATA文件对应的脚本不存在\"]")
    private List<String> exceptionMessageList;

}
