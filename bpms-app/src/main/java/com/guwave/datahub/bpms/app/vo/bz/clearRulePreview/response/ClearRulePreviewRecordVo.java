package com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(title = "清洗规则预览记录")
@Data
public class ClearRulePreviewRecordVo {
    @Schema(title = "id")
    private Long id;

    @Schema(title = "fileName", example = "XXX.stdf")
    private String fileName;

    @Schema(title = "remoteOriginFilePath", example = "/GUWAVE/test")
    private String remoteOriginFilePath;

    @Schema(title = "remoteOriginFileName", example = "a.zip")
    private String remoteOriginFileName;

    @Schema(title = "fileCategory", example = "STDF")
    private String fileCategory;

    @Schema(title = "factory", example = "TEST")
    private String factory;

    @Schema(title = "factorySite", example = "TEST")
    private String factorySite;

    @Schema(title = "givenTestArea", example = "CP")
    private String givenTestArea;

    @Schema(title = "testArea", example = "CP")
    private String testArea;

    @Schema(title = "deviceId", example = "SS521BB-DEFAULT")
    private String deviceId;

    @Schema(title = "lotId", example = "NT41A")
    private String lotId;

    @Schema(title = "waferNo", example = "8")
    private String waferNo;

    @Schema(title = "lotType", example = "PRODUCTION")
    private String lotType;

    @Schema(title = "testStage", example = "CP1")
    private String testStage;

    @Schema(title = "sblotId", example = "sblot1")
    private String sblotId;

    @Schema(title = "createTime", example = "2023-09-19 08:14:13")
    private String createTime;

    @Schema(title = "previewResult", example = "{\"metaData\":{},\"confResult\":{},\"settingSnapshot\":{}}")
    private String previewResult;

    @Schema(title = "previewDetailFileHdfsPath", example = "/user/glory/1.zip")
    private String previewDetailFileHdfsPath;

    @Schema(title = "updateTime", example = "2023-09-20 08:14:13")
    private String updateTime;

    @Schema(title = "processStatus", example = "PROCESSING")
    private String processStatus;

    @Schema(title = "exceptionType", example = "STDF_DATA_EXCEPTION")
    private String exceptionType;
    @Schema(title = "exceptionMessage", example = "STDF文件存在不完整的TOUCH_DOWN记录，TOUCH_DOWN缺失PRR")
    private String exceptionMessage;
    @Schema(title = "errorMessage", example = "java.lang.xxxxxx")
    private String errorMessage;
}
