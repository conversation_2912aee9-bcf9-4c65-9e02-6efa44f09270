package com.guwave.datahub.bpms.app.vo.web;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 通用的ResponseVo
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-26 16:13:30
 */
@Data
@AllArgsConstructor
@Schema(title = "统一返回结果", description = "ResponseVo")
public class ResponseVo<T> {

    @Schema(title = "响应码", example = "000000")
    private String code;

    @Schema(title = "响应提示信息", example = "操作成功")
    private String msg;

    @Schema(title = "具体的响应数据")
    @JsonInclude
    private T data;

    public ResponseVo() {

    }

    public static <T> ResponseVo<T> ok() {
        return build(null, ResponseCode.SUCCESS.code(), ResponseCode.SUCCESS.msg());
    }

    public static <T> ResponseVo<T> ok(T data) {
        return build(data, ResponseCode.SUCCESS.code(), ResponseCode.SUCCESS.msg());
    }

    public static <T> ResponseVo<T> fail() {
        return build(null, ResponseCode.FAIL.code(), ResponseCode.FAIL.msg());
    }

    public static <T> ResponseVo<T> fail(T data) {
        return build(data, ResponseCode.FAIL.code(), ResponseCode.FAIL.msg());
    }

    public static <T> ResponseVo<T> fail(BpmsException exception) {
        return build(null, exception.getResponseCode().code(), exception.getMsg());
    }

    public static <T> ResponseVo<T> fail(BpmsException exception, T data) {
        return build(data, exception.getResponseCode().code(), exception.getMsg());
    }

    public static <T> ResponseVo<T> build(T data, String code, String msg) {
        return new ResponseVo<>(code, msg, data);
    }
}
