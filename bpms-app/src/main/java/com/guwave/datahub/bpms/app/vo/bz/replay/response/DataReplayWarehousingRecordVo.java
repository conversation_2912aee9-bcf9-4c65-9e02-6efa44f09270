package com.guwave.datahub.bpms.app.vo.bz.replay.response;

import cn.hutool.core.date.DateUtil;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.FileWarehousingRecord;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_FORMAT;


@Schema(title = "数据入库记录")
@Data
@Builder
public class DataReplayWarehousingRecordVo {
    @Schema(title = "id")
    private Long id;

    @Schema(title = "testArea", description = "CP,FT,WAT", example = "CP")
    private String testArea;

    @Schema(title = "工厂", example = "VC")
    private String factory;

    @Schema(title = "deviceId", example = "SS521BB-DEFAULT")
    private String deviceId;

    @Schema(title = "fileCategory", description = "STDF,RAW_DATA,WAT", example = "STDF")
    private String fileCategory;

    @Schema(title = "ftpPath", description = "远程服务器的原始文件路径", example = "/onedata-saas/ETA/FT/YT_TESTING/raw_data/ETA7085S2F_21442980_A0172211T01022_FT.csv")
    private String ftpPath;

    @Schema(title = "lotId", example = "NT41A")
    private String lotId;

    @Schema(title = "waferNo", example = "8")
    private String waferNo;

    @Schema(title = "sblotId", example = "sblotId")
    private String sblotId;

    @Schema(title = "lotType", description = "PRODUCTION,ENGINEERING", example = "PRODUCTION")
    private String lotType;

    @Schema(title = "testStage", example = "CP1")
    private String testStage;

    @Schema(title = "testProgram", example = "PROGRAM1")
    private String testProgram;

    @Schema(title = "fileName", description = "文件名", example = "CP1_RT_2022-10-10.stdf")
    private String fileName;

    @Schema(title = "step", description = "文件处理步骤", example = "3100")
    private Integer step;

    @Schema(title = "status", description = "入库状态", example = "SUCCESS")
    private String status;

    @Schema(title = "异常信息", example = "RAW_DATA文件转换异常")
    private String exceptionMessage;

    @Schema(title = "createTime", description = "入库开始时间", example = "2023-09-19 08:14:13")
    private String createTime;

    @Schema(title = "updateTime", description = "入库更新时间", example = "2023-09-20 08:14:13")
    private String updateTime;

    @Schema(title = "replayFileMaxCount", description = "重播文件数上限", example = "10000")
    private Integer replayFileMaxCount;

    @Schema(title = "replayFileMaxCount", description = "重播单任务文件数上限", example = "1000")
    private Integer replayFileBatchSize;

    public static DataReplayWarehousingRecordVo of(FileWarehousingRecord data, Integer replayFileMaxCount, Integer replayFileBatchSize) {
        return DataReplayWarehousingRecordVo.builder()
                .id(data.getId())
                .testArea(TestArea.of(data.getTestArea()))
                .factory(data.getFactory())
                .deviceId(data.getDeviceId())
                .fileCategory(FileCategory.of(data.getFileCategory()))
                .ftpPath(data.getFtpPath())
                .lotId(data.getLotId())
                .waferNo(data.getWaferNo())
                .sblotId(data.getSblotId())
                .lotType(LotType.of(data.getLotType()))
                .testStage(data.getTestStage())
                .testProgram(data.getTestProgram())
                .fileName(data.getFileName())
                .step(data.getStep())
                .status(data.getProcessStatus().getStatus())
                .exceptionMessage(data.getExceptionMessage())
                .createTime(DateUtil.format(data.getCreateTime(), NORM_DATETIME_FORMAT))
                .updateTime(DateUtil.format(data.getUpdateTime(), NORM_DATETIME_FORMAT))
                .replayFileMaxCount(replayFileMaxCount)
                .replayFileBatchSize(replayFileBatchSize)
                .build();
    }

}
