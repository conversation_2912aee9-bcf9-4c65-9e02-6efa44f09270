package com.guwave.datahub.bpms.app.configuration;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateSettings;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.sql.DataSource;
import java.util.Map;
import java.util.Objects;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * JpaSecondaryConfiguration
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2024-02-26 10:53:48
 */
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(entityManagerFactoryRef = "entityManagerFactorySecondary",
        transactionManagerRef = "transactionManagerSecondary",
        basePackages = {
                "com.guwave.datahub.bpms.dao.repository.dw",
                "com.guwave.onedata.dataware.dao.mysql.repository"
        }
)
@MapperScan(
        basePackages = "com.guwave.datahub.bpms.dao.repository.dw.mapper",
        sqlSessionFactoryRef = "secondarySqlSessionFactory"
)
public class JpaSecondaryConfiguration {

    @Resource
    private JpaProperties jpaProperties;

    @Resource
    private HibernateProperties hibernateProperties;

    @Bean(name = "secondaryDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.secondary")
    public DataSource secondaryDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "entityManagerSecondary")
    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
        return Objects.requireNonNull(entityManagerFactorySecondary(builder).getObject()).createEntityManager();
    }

    @Bean(name = "entityManagerFactorySecondary")
    public LocalContainerEntityManagerFactoryBean entityManagerFactorySecondary(EntityManagerFactoryBuilder builder) {
        Map<String, Object> properties =
                hibernateProperties.determineHibernateProperties(
                        jpaProperties.getProperties(),
                        new HibernateSettings());

        return builder
                .dataSource(secondaryDataSource())
                .properties(properties)
                .packages("com.guwave.onedata.dataware.dao.mysql.domain")
                .persistenceUnit("secondaryPersistenceUnit")
                .build();
    }

    @Bean(name = "transactionManagerSecondary")
    PlatformTransactionManager transactionManagerSecondary(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(Objects.requireNonNull(entityManagerFactorySecondary(builder).getObject()));
    }

    // 配置MyBatis
    @Bean(name = "secondarySqlSessionFactory")
    public SqlSessionFactory SecondarySqlSessionFactory() throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(secondaryDataSource());
        sessionFactory.setMapperLocations(
                ArrayUtils.addAll(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/mysql/onedata/*.xml"),
                        new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml"))
        );
        return sessionFactory.getObject();

    }

    @Bean(name = "secondaryMybatisTransactionManager")
    public PlatformTransactionManager SecondaryMybatisTransactionManager() {
        return new DataSourceTransactionManager(secondaryDataSource());
    }
}
