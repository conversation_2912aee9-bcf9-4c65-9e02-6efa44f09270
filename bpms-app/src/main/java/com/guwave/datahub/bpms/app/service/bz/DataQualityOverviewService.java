package com.guwave.datahub.bpms.app.service.bz;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.DataAccuracyCheckHelper;
import com.guwave.datahub.bpms.app.vo.bz.dataQualityOverview.request.DataQualityOverviewDropDownFilterVo;
import com.guwave.datahub.bpms.app.vo.bz.dataQualityOverview.request.DataQualityOverviewQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.dataQualityOverview.response.DataQualityOverviewDropDownVo;
import com.guwave.datahub.bpms.app.vo.bz.dataQualityOverview.response.DataQualityOverviewStatisticsVo;
import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.common.constant.*;
import com.guwave.datahub.bpms.dao.domain.qualityOverview.FileLoadInfo;
import com.guwave.datahub.bpms.dao.domain.qualityOverview.StatisticDws;
import com.guwave.datahub.bpms.dao.domain.qualityOverview.StatisticsIndex;
import com.guwave.datahub.bpms.dao.repository.clickhouse.dws.mapper.DwsLotWaferIndexMapper;
import com.guwave.datahub.bpms.dao.repository.clickhouse.dws.mapper.DwsSblotIndexMapper;
import com.guwave.datahub.bpms.dao.repository.dw.mapper.FileLoadingLogMapper;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.contant.TestArea;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_FORMAT;

@Slf4j
@Service
public class DataQualityOverviewService {
    private final FileLoadingLogMapper fileLoadingLogMapper;
    private final DwsLotWaferIndexMapper dwsLotWaferIndexMapper;
    private final DwsSblotIndexMapper dwsSblotIndexMapper;
    private ExecutorService executorService;
    @Value("${spring.clearRulePreview.threadCnt}")
    private Integer clearRulePreviewThreadCnt;
    private static final long HOUR_MS_NUM = 3600 * 1000L;

    public DataQualityOverviewService(FileLoadingLogMapper fileLoadingLogMapper,
                                      DwsLotWaferIndexMapper dwsLotWaferIndexMapper,
                                      DwsSblotIndexMapper dwsSblotIndexMapper) {
        this.fileLoadingLogMapper = fileLoadingLogMapper;
        this.dwsLotWaferIndexMapper = dwsLotWaferIndexMapper;
        this.dwsSblotIndexMapper = dwsSblotIndexMapper;
    }

    private final List<TestArea> SUPPORT_TEST_AREA = Collections.unmodifiableList(Stream.concat(TestArea.getCPList().stream(), TestArea.getFTList().stream()).collect(Collectors.toList()));
    public final Map<StatisticIndexEnum, Function<List<StatisticsIndex>, DataQualityOverviewStatisticsVo>> statisticIndexMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        int corePoolSize = clearRulePreviewThreadCnt;
        int maxPoolSize = corePoolSize * 2;
        long keepAliveTime = 60;
        BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(100);
        this.executorService = new ThreadPoolExecutor(corePoolSize, maxPoolSize, keepAliveTime, TimeUnit.SECONDS, workQueue, new ThreadPoolExecutor.CallerRunsPolicy());

        statisticIndexMap.put(StatisticIndexEnum.PRODUCT_COUNT, this::calculateProductCount);
        statisticIndexMap.put(StatisticIndexEnum.BATCH_COUNT, this::calculateBatchCount);
        statisticIndexMap.put(StatisticIndexEnum.MATCH_RATE, this::calculateMatchRate);
        statisticIndexMap.put(StatisticIndexEnum.CHIP_COUNT, this::calculateChipCount);
        statisticIndexMap.put(StatisticIndexEnum.NORMAL_RATE, this::calculateNormalRate);
        statisticIndexMap.put(StatisticIndexEnum.YIELD_ACCURACY, this::calculateYieldAccuracy);
    }

    // 关闭线程池
    @PreDestroy
    public void destroy() {
        if (executorService != null) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }


    // 下拉框:先查file_loading_log表，拿结果再查dws_lot_wafer_index表
    public List<DataQualityOverviewDropDownVo> dataQualityOverviewFilter(DataQualityOverviewDropDownFilterVo filterVo) {
        DataQualityOverviewPageDropdownField filterField = filterVo.getFilterField();
        log.info("获取{}列表", filterField);

        List<String> dropDownResult;

        if (filterField == null) {
            log.error("不支持的过滤字段：{}", filterField);
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, filterField);
        } else {
            if (DataQualityOverviewPageDropdownField.TEST_AREA.equals(filterField)) {
                // 如果 testArea 为空，则返回所有 SUPPORT_TEST_AREA 并将 CP 排第一
                dropDownResult = Optional.ofNullable(filterVo.getTestArea())
                        .map(Collections::singletonList)
                        .orElseGet(() -> SUPPORT_TEST_AREA.stream().map(TestArea::getArea).collect(Collectors.toList())
                        );
                // log.info("SUPPORT_TEST_AREA:{}", SUPPORT_TEST_AREA);
                // log.info("dropDownResult:{}", dropDownResult);
                return dropDownResult.stream()
                        .map(DataQualityOverviewDropDownVo::of)
                        .collect(Collectors.toList());
            } else {
                boolean isCp = TestArea.getCPList().contains(TestArea.of(filterVo.getTestArea()));
                log.info("质量概览下拉框结果, isCp:{}", isCp);
                if (isCp) {
                    List<String> dropDownMysqlCond = fileLoadingLogMapper.findOverviewFilter(
                            RequestContext.getCustomer(),
                            filterField.getField(),
                            filterVo.getSubCustomerList(),
                            filterVo.getFactoryList(),
                            filterVo.getDeviceIdList(),
                            filterVo.getTestArea(),
                            filterVo.getTestProgramList(),
                            filterVo.getTestStageList(),
                            LocalDateTimeUtil.format(filterVo.getStartTime(), NORM_DATETIME_FORMAT.getDateTimeFormatter()),
                            LocalDateTimeUtil.format(filterVo.getEndTime(), NORM_DATETIME_FORMAT.getDateTimeFormatter())
                    );
                    log.info("下拉框结果 dropDownMysqlCond:{}", dropDownMysqlCond);
                    // 根据mysql查询结果重置下拉框条件
                    switch (filterField) {
                        case SUB_CUSTOMER:
                            filterVo.setSubCustomerList(dropDownMysqlCond);
                            break;
                        case FACTORY:
                            filterVo.setFactoryList(dropDownMysqlCond);
                            break;
                        case DEVICE_ID:
                            filterVo.setDeviceIdList(dropDownMysqlCond);
                            break;
                        case TEST_PROGRAM:
                            filterVo.setTestProgramList(dropDownMysqlCond);
                            break;
                        case TEST_STAGE:
                            filterVo.setTestStageList(dropDownMysqlCond);
                            break;
                    }
                    log.info("重置后的下拉框结果 filterVo:{}", filterVo);
                    dropDownResult = dwsLotWaferIndexMapper.queryOverviewDropdownFieldCp(
                            RequestContext.getCustomer(),
                            filterField.getField(),
                            filterVo.getSubCustomerList(),
                            filterVo.getFactoryList(),
                            filterVo.getDeviceIdList(),
                            filterVo.getTestArea(),
                            filterVo.getTestProgramList(),
                            filterVo.getTestStageList()
                    );
                } else {
                    List<String> dropDownMysqlCond = fileLoadingLogMapper.findOverviewFilter(
                            RequestContext.getCustomer(),
                            filterField.getField(),
                            filterVo.getSubCustomerList(),
                            filterVo.getFactoryList(),
                            filterVo.getDeviceIdList(),
                            filterVo.getTestArea(),
                            filterVo.getTestProgramList(),
                            filterVo.getTestStageList(),
                            LocalDateTimeUtil.format(filterVo.getStartTime(), NORM_DATETIME_FORMAT.getDateTimeFormatter()),
                            LocalDateTimeUtil.format(filterVo.getEndTime(), NORM_DATETIME_FORMAT.getDateTimeFormatter())
                    );
                    // 根据mysql查询结果重置下拉框条件
                    switch (filterField) {
                        case SUB_CUSTOMER:
                            filterVo.setSubCustomerList(dropDownMysqlCond);
                            break;
                        case FACTORY:
                            filterVo.setFactoryList(dropDownMysqlCond);
                            break;
                        case DEVICE_ID:
                            filterVo.setDeviceIdList(dropDownMysqlCond);
                            break;
                        case TEST_PROGRAM:
                            filterVo.setTestProgramList(dropDownMysqlCond);
                            break;
                        case TEST_STAGE:
                            filterVo.setTestStageList(dropDownMysqlCond);
                            break;
                    }
                    dropDownResult = dwsSblotIndexMapper.queryOverviewDropdownFieldFt(
                            RequestContext.getCustomer(),
                            filterField.getField(),
                            filterVo.getSubCustomerList(),
                            filterVo.getFactoryList(),
                            filterVo.getDeviceIdList(),
                            filterVo.getTestArea(),
                            filterVo.getTestProgramList(),
                            filterVo.getTestStageList()
                    );
                }
            }
        }
        log.info("下拉框结果 field:{}, value:{}", filterField, String.join(Constant.COMMA, dropDownResult));
        return dropDownResult.stream().filter(StringUtils::isNotBlank).distinct().map(DataQualityOverviewDropDownVo::of).collect(Collectors.toList());
    }

    // 统计概览主逻辑: 1.查询mysql拿条件，2.查询clickhouse拿数据，3.合并数据，4.返回结果
    public List<DataQualityOverviewStatisticsVo> statisticOverview(DataQualityOverviewQueryVo baseVO) {
        //long startTime = System.currentTimeMillis();
        log.info("统计概览开始, 参数={}", baseVO);
        // 判断CP/FT
        boolean isCp = TestArea.getCPList().contains(TestArea.of(baseVO.getTestArea()));
        //long judgeTime = System.currentTimeMillis();
        //log.info("判断CP/FT耗时: {}ms", judgeTime - startTime);

        //log.info("开始查询MySQL: querySuccessFileOverviewByCond");
        // 查询所有步骤的数据，不仅限于7100
        List<FileLoadInfo> allFileLoadInfoList = fileLoadingLogMapper.querySuccessFileOverviewByCond(
                RequestContext.getCustomer(),
                baseVO.getSubCustomerList(),
                baseVO.getFactoryList(),
                baseVO.getDeviceIdList(),
                baseVO.getTestArea(),
                baseVO.getTestProgramList(),
                baseVO.getTestStageList(),
                LocalDateTimeUtil.format(baseVO.getStartTime(), NORM_DATETIME_FORMAT.getDateTimeFormatter()),
                LocalDateTimeUtil.format(baseVO.getEndTime(), NORM_DATETIME_FORMAT.getDateTimeFormatter()));
        //log.info("完成查询MySQL: querySuccessFileOverviewByCond, 结果大小: {}", allFileLoadInfoList != null ? allFileLoadInfoList.size() : 0);

        // 查dws_lot_wafer_data_check_index_cluster
        List<StatisticsIndex> statisticsIndexVOList = queryStatisticsData(baseVO, isCp, allFileLoadInfoList);
        //long queryDataTime = System.currentTimeMillis();
        //log.info("查询数据耗时: {}ms", queryDataTime - judgeTime);
        //log.info("查询数据指标统计完成,size:{}", statisticsIndexVOList.size());

        // 计算其他指标的线程（PRODUCT_COUNT, BATCH_COUNT, CHIP_COUNT, EXCEPTION_YIELD, MATCH_RATE, YIELD_ACCURACY）
        Future<List<DataQualityOverviewStatisticsVo>> statisticFuture = executorService.submit(() -> 
            StatisticIndexEnum.getStatisticIndexEnumList()
                    .stream()
                    .map(indexEnum -> calculateStaticIndex(indexEnum, statisticsIndexVOList))
                    .collect(Collectors.toList())
        );

        // 计算入库耗时(IN_DB_TIME)
        Future<List<DataQualityOverviewStatisticsVo>> fileFuture = executorService.submit(() -> 
            Collections.singletonList(calculateInDbTime(allFileLoadInfoList))
        );

        // 合并结果
        List<DataQualityOverviewStatisticsVo> dataQualityOverviewStatisticsVoList = new ArrayList<>();
        try {
            //log.info("合并结果开始");
            //long mergeStartTime = System.currentTimeMillis();
            
            //long statisticFutureStartTime = System.currentTimeMillis();
            dataQualityOverviewStatisticsVoList.addAll(statisticFuture.get());
            //long statisticFutureEndTime = System.currentTimeMillis();
            //log.info("statisticFuture.get()耗时: {}ms, 结果数量: {}", 
            //    statisticFutureEndTime - statisticFutureStartTime,
            //    statisticFuture.get().size());
            
            //long fileFutureStartTime = System.currentTimeMillis();
            dataQualityOverviewStatisticsVoList.addAll(fileFuture.get());
            //long fileFutureEndTime = System.currentTimeMillis();
            //log.info("fileFuture.get()耗时: {}ms, 结果数量: {}", 
            //    fileFutureEndTime - fileFutureStartTime,
            //    fileFuture.get().size());
                
            //long mergeEndTime = System.currentTimeMillis();
            //log.info("合并结果总耗时: {}ms, 最终结果数量: {}", 
            //    mergeEndTime - mergeStartTime,
            //    dataQualityOverviewStatisticsVoList.size());
        } catch (Exception e) {
            log.error("统计指标计算失败", e);
        }
        //long mergeTime = System.currentTimeMillis();
        //log.info("合并结果耗时: {}ms", mergeTime - queryDataTime);

        log.info("统计结果size为：{}", dataQualityOverviewStatisticsVoList.size());
        //long endTime = System.currentTimeMillis();
        //log.info("统计概览总耗时: {}ms", endTime - startTime);
        return dataQualityOverviewStatisticsVoList;
    }

    // 计算统计指标
    public List<StatisticsIndex> queryStatisticsData(DataQualityOverviewQueryVo baseVO, boolean isCp, List<FileLoadInfo> fileLoadInfoList) {
        //long startTime = System.currentTimeMillis();
        
        // 改为使用传入的fileLoadInfoList参数，筛选7100步骤数据
        Map<StatisticDws, String> statisticCond = fileLoadInfoList.stream()
                .filter(e -> FileStepEnum.STEP_7100.getStep() == e.getStep())
                .collect(Collectors.toMap(StatisticDws::of, FileLoadInfo::getStepEndTimeWeek, (v1, v2) -> v1));
                
        //long mysqlQueryTime = System.currentTimeMillis();
        //log.info("queryStatisticsData查询MySQL耗时: {}ms", mysqlQueryTime - startTime);
        //log.info("查询数据指标统计完成,size:{}", statisticCond.size());
        if (MapUtil.isEmpty(statisticCond)) {
            log.info("queryStatisticsData map数据为空");
            return Collections.emptyList();
        }
        List<StatisticsIndex> voList;
        // 从dws_lot_wafer_index_cluster和dws_lot_wafer_data_check_index_cluster和 left join查统计信息
        if (isCp) {
            voList = dwsLotWaferIndexMapper.selectStatisticsDataCp(
                    baseVO.getSubCustomerList(),
                    baseVO.getFactoryList(),
                    baseVO.getDeviceIdList(),
                    baseVO.getTestArea(),
                    baseVO.getTestProgramList(),
                    baseVO.getTestStageList()
            );
        } else {
            voList = dwsSblotIndexMapper.selectStatisticsDataFt(
                    baseVO.getSubCustomerList(),
                    baseVO.getFactoryList(),
                    baseVO.getDeviceIdList(),
                    baseVO.getTestArea(),
                    baseVO.getTestProgramList(),
                    baseVO.getTestStageList()
            );
        }
        //long clickhouseQueryTime = System.currentTimeMillis();
        //log.info("queryStatisticsData查询ClickHouse耗时: {}ms", clickhouseQueryTime - mysqlQueryTime);

        if (CollectionUtils.isEmpty(voList)) {
            log.info("queryStatisticsData-2数据为空");
            return Collections.emptyList();
        }

        // 用mysql查出的week替换ck查出的week
        handleDataStatisticBaseMysql(voList, statisticCond);
        //long processTime = System.currentTimeMillis();
        //log.info("queryStatisticsData处理数据耗时: {}ms", processTime - clickhouseQueryTime);

        return voList;
    }

    // 处理week
    private void handleDataStatisticBaseMysql(List<StatisticsIndex> voList, Map<StatisticDws, String> groupMap) {
        //long startTime = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(voList) || MapUtil.isEmpty(groupMap)) {
            log.warn("数据为空，voList size: {}, groupMap size: {}",
                    voList != null ? voList.size() : 0,
                    groupMap != null ? groupMap.size() : 0);
            return;
        }

        Map<StatisticDws, String> copyMap = copyStatisticDwsGroupMap(groupMap);
        //long copyMapTime = System.currentTimeMillis();
        //log.info("handleDataStatisticBaseMysql复制map耗时: {}ms", copyMapTime - startTime);
        //log.info("copyStatisticDwsGroupMap结果为, size:{}", copyMap.size());

        List<StatisticsIndex> filteredList = voList.stream()
                .filter(statisticsIndex -> {
                    StatisticDws dwsVO = StatisticDws.of(statisticsIndex);
                    if (copyMap.containsKey(dwsVO)) {
                        statisticsIndex.setWeek(copyMap.get(dwsVO));
                        return true;
                    } else {
                        // log.error("handleDataStatisticBaseMysql数据处理周异常, 未匹配到数据, VO={}", dwsVO);
                        return false;
                    }
                })
                .collect(Collectors.toList());

        // 清空原列表并添加过滤后的元素
        voList.clear();
        voList.addAll(filteredList);

        // 添加匹配率统计日志分析数据一致性
        // if (VOList.size() > 0) {
        //     double matchRate = (double) filteredList.size() / VOList.size() * 100;
        //     log.info("数据匹配率: {}% ({}/{})", String.format("%.2f", matchRate), filteredList.size(), VOList.size());
        // }
        //long endTime = System.currentTimeMillis();
        //log.info("handleDataStatisticBaseMysql处理数据耗时: {}ms", endTime - copyMapTime);
        //log.info("处理后数据量: {}", VOList.size());
    }

    private Map<StatisticDws, String> copyStatisticDwsGroupMap(Map<StatisticDws, String> groupMap) {
        //long startTime = System.currentTimeMillis();
        if (MapUtil.isEmpty(groupMap)) {
            return groupMap;
        }
        Map<StatisticDws, String> copyMap = new HashMap<>();
        groupMap.forEach((k, v) -> {
            if (Objects.isNull(k.getTestProgram())) {
                copyMap.put(k, v);
            } else {
                StatisticDws vo = new StatisticDws();
                BeanUtils.copyProperties(k, vo);
                vo.setTestProgram(null);
                copyMap.put(vo, v);
            }
        });
        //long endTime = System.currentTimeMillis();
        //log.info("copyStatisticDwsGroupMap耗时: {}ms", endTime - startTime);
        return copyMap;
    }

    // 按照指标调用不同的计算逻辑
    public DataQualityOverviewStatisticsVo calculateStaticIndex(StatisticIndexEnum indexEnum, List<StatisticsIndex> valueList) {
        if (CollectionUtils.isEmpty(valueList)) {
            return null;
        }
        return this.statisticIndexMap.get(indexEnum).apply(valueList);
    }

    /**
     * 计算每组的batch数
     *
     * @param list 数据
     * @return 计算结果
     */
    public DataQualityOverviewStatisticsVo calculateBatchCount(List<StatisticsIndex> list) {
        //log.info("计算batch数");

        Map<String, List<StatisticsIndex>> listMap = statisticDataGroupBy(list, StatisticIndexEnum.BATCH_COUNT);

        List<DataQualityOverviewStatisticsVo.StatisticsDetailVO> batchList = new ArrayList<>();

        Set<Pair<String, String>> batchSet = new HashSet<>();

        for (Map.Entry<String, List<StatisticsIndex>> entry : listMap.entrySet()) {
            Set<Pair<String, String>> weekSet = entry.getValue().stream().collect(Collectors.groupingBy(v -> Pair.of(v.getLotId(), v.getWaferId()))).keySet();
            long count;
            if (CollectionUtils.isEmpty(batchSet)) {
                count = weekSet.size();
            } else {
                count = weekSet.stream().filter(v -> !batchSet.contains(v)).count();
            }

            batchSet.addAll(weekSet);

            batchList.add(DataQualityOverviewStatisticsVo.StatisticsDetailVO.of(entry.getKey(), BigDecimal.valueOf(count)));
        }

        batchList.sort(Comparator.comparing(DataQualityOverviewStatisticsVo.StatisticsDetailVO::getIndexKey));

        //sum chip
        BigDecimal total = batchList.stream().map(DataQualityOverviewStatisticsVo.StatisticsDetailVO::getIndexValue).reduce(BigDecimal.ZERO, BigDecimal::add);
        //log.info("计算batch总数结果：{}", total);
        return DataQualityOverviewStatisticsVo.of(StatisticIndexEnum.BATCH_COUNT, total, batchList);
    }

    /**
     * 计算product数
     *
     * @param list 数据
     * @return 计算结果
     */
    public DataQualityOverviewStatisticsVo calculateProductCount(List<StatisticsIndex> list) {
        //log.info("计算产品数");

        Map<String, List<StatisticsIndex>> deviceWeekMap = statisticDataGroupBy(list, StatisticIndexEnum.PRODUCT_COUNT);

        List<DataQualityOverviewStatisticsVo.StatisticsDetailVO> detailVOList = new ArrayList<>(deviceWeekMap.size());

        //计算每周新增的device
        Set<String> deviceSet = new HashSet<>();
        for (Map.Entry<String, List<StatisticsIndex>> entry : deviceWeekMap.entrySet()) {
            Set<String> weekDeviceSet = entry.getValue().stream().map(StatisticsIndex::getDeviceId).collect(Collectors.toSet());

            long count;
            if (CollectionUtils.isEmpty(deviceSet)) {
                count = weekDeviceSet.size();
            } else {
                count = weekDeviceSet.stream().filter(v -> !deviceSet.contains(v)).count();
            }

            deviceSet.addAll(weekDeviceSet);

            detailVOList.add(DataQualityOverviewStatisticsVo.StatisticsDetailVO.of(entry.getKey(), BigDecimal.valueOf(count)));
        }

        detailVOList.sort(Comparator.comparing(DataQualityOverviewStatisticsVo.StatisticsDetailVO::getIndexKey));

        long total = deviceSet.size();
        //log.info("计算产品数完成， total = {}", total);

        return DataQualityOverviewStatisticsVo.of(StatisticIndexEnum.PRODUCT_COUNT, BigDecimal.valueOf(total), detailVOList);
    }

    /**
     * 计算chip数
     *
     * @param list 数据
     * @return 计算结果
     */
    public DataQualityOverviewStatisticsVo calculateChipCount(List<StatisticsIndex> list) {
        //log.info("开始计算芯片数");
        Map<String, List<StatisticsIndex>> listMap = statisticDataGroupBy(list, StatisticIndexEnum.CHIP_COUNT);
        List<DataQualityOverviewStatisticsVo.StatisticsDetailVO> detailVOList = listMap.entrySet().stream()
                .map(v -> {
                    List<StatisticsIndex> valueList = v.getValue().stream().filter(o -> Objects.nonNull(o.getFinalTotalCount())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(valueList)) {
                        return DataQualityOverviewStatisticsVo.StatisticsDetailVO.of(v.getKey(), BigDecimal.ZERO);
                    }
                    long totalCnt = valueList.stream().mapToLong(StatisticsIndex::getFinalTotalCount).sum();
                    BigDecimal sum = BigDecimal.valueOf(totalCnt);
                    return DataQualityOverviewStatisticsVo.StatisticsDetailVO.of(v.getKey(), sum);
                })
                .sorted(Comparator.comparing(DataQualityOverviewStatisticsVo.StatisticsDetailVO::getIndexKey, KEY_COMPARATOR))
                .collect(Collectors.toList());
        //sum chip
        BigDecimal total = detailVOList.stream().map(DataQualityOverviewStatisticsVo.StatisticsDetailVO::getIndexValue).reduce(BigDecimal.ZERO, BigDecimal::add);
        //log.info("计算芯片数结束, total:{}", total);
        return DataQualityOverviewStatisticsVo.of(StatisticIndexEnum.CHIP_COUNT, total, detailVOList);
    }


    /**
     * 计算inDbTime
     *
     * @param list 数据
     * @return 计算结果
     */
    public DataQualityOverviewStatisticsVo calculateInDbTime(List<FileLoadInfo> list) {
        //long startTime = System.currentTimeMillis();
        //log.info("开始计算入库时间");

        Map<String, List<FileLoadInfo>> fileNameMap = list.stream()
                .collect(Collectors.groupingBy(FileLoadInfo::getFileName));

        //long groupFileNameTime = System.currentTimeMillis();
        //log.info("calculateInDbTime按文件名分组耗时: {}ms", groupFileNameTime - startTime);
        // 1 数据按周分组 计算组内的文件数
        // 1.1 按周分组 7100
        Map<String, List<FileLoadInfo>> weekMap = fileDataGroupBy(list);
        //long groupWeekTime = System.currentTimeMillis();
        //log.info("calculateInDbTime按周分组耗时: {}ms", groupWeekTime - groupFileNameTime);
        // 1.2 按文件名分组 1000
        Map<String, FileLoadInfo> step1000fileMap = list.stream()
                .filter(v -> FileStepEnum.STEP_1000.getStep() == v.getStep() && ProcessStatus.SUCCESS.equals(ProcessStatus.of(v.getProcessStatus())))
                .collect(Collectors.toMap(FileLoadInfo::getFileName, Function.identity()));

        //long groupStep1000Time = System.currentTimeMillis();
        //log.info("calculateInDbTime按step1000分组耗时: {}ms", groupStep1000Time - groupWeekTime);
        // 2 计算每周的文件入库耗时平均值
        List<DataQualityOverviewStatisticsVo> voList = weekMap.entrySet()
                .stream()
                .map(weekEntry -> {
                    try {
                        return getStatisticsVO(step1000fileMap, weekEntry.getKey(), weekEntry.getValue(), fileNameMap);
                    } catch (Exception e) {
                        //log.warn("计算周统计数据时出错，跳过该周: {}，错误: {}", weekEntry.getKey(), e.getMessage());
                        // 返回null，后续过滤掉
                        return null;
                    }
                })
                // 过滤掉出错的记录
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        //long calculateWeekTime = System.currentTimeMillis();
        //log.info("calculateInDbTime计算每周耗时平均值耗时: {}ms", calculateWeekTime - groupStep1000Time);
        // 计算整体平均值
        int total = voList.size();
        BigDecimal avgTime = BigDecimal.ZERO;
        if (total != 0) {
            avgTime = voList.stream()
                    .map(DataQualityOverviewStatisticsVo::getBaseIndexValue)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(BigDecimal.valueOf(total), 2, RoundingMode.HALF_UP);
        }

        //long calculateAvgTime = System.currentTimeMillis();
        //log.info("calculateInDbTime计算整体平均值耗时: {}ms", calculateAvgTime - calculateWeekTime);
        // 重新排序数据
        List<DataQualityOverviewStatisticsVo.StatisticsDetailVO> inDbValues = voList.stream()
                .flatMap(v -> v.getDetail().stream())
                .sorted(Comparator.comparing(DataQualityOverviewStatisticsVo.StatisticsDetailVO::getIndexKey, KEY_COMPARATOR)
                        .thenComparing(DataQualityOverviewStatisticsVo.StatisticsDetailVO::getColorByKey))
                .collect(Collectors.toList());

        //long sortTime = System.currentTimeMillis();
        //log.info("calculateInDbTime排序耗时: {}ms", sortTime - calculateAvgTime);
        // 组装返回数据
        return DataQualityOverviewStatisticsVo.of(StatisticIndexEnum.IN_DB_TIME, avgTime, inDbValues);

        //log.info("计算入库耗时平均值完成:{}", dataQualityOverviewStatisticsVO);
        //long endTime = System.currentTimeMillis();
        //log.info("calculateInDbTime总耗时: {}ms", endTime - startTime);
    }

    /**
     * 计算matchRate
     *
     * @param list 数据
     * @return 计算结果
     */
    public DataQualityOverviewStatisticsVo calculateMatchRate(List<StatisticsIndex> list) {
        //log.info("计算matchRate");
        long sum = list.size();
        Map<MesMatchTypeEnum, List<StatisticsIndex>> mapByMatchType = list.stream().collect(Collectors.groupingBy(val ->
                MesMatchTypeEnum.of(val.getSummaryMatchType())));
        List<MesMatchTypeEnum> keys = new ArrayList<>(mapByMatchType.keySet());
        keys.sort(new MesMatchTypeEnum.MesMatchTypeComparator());
        List<DataQualityOverviewStatisticsVo.StatisticsDetailVO> detailVOList = keys.stream()
                .map(matchType -> {
                    long count = mapByMatchType.get(matchType).size();
                    return DataQualityOverviewStatisticsVo.StatisticsDetailVO.of(
                            matchType.getShowName(),
                            sum == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(count * 1.0 / sum).setScale(4, RoundingMode.HALF_UP));
                }).collect(Collectors.toList());

        long matchCount = list.stream().filter(val ->
                MesMatchTypeEnum.MATCH.getType().equals(val.getSummaryMatchType())).count();
        double rate = sum == 0 ? 0 : matchCount * 1.0 / sum;
        //log.info("匹配率计算完成，matchCount:{}, sum:{}, rate:{}", matchCount, sum, rate);
        return DataQualityOverviewStatisticsVo.of(StatisticIndexEnum.MATCH_RATE, BigDecimal.valueOf(rate).setScale(4, RoundingMode.HALF_UP), detailVOList);
    }

    public DataQualityOverviewStatisticsVo calculateNormalRate(List<StatisticsIndex> list) {
        //log.info("计算normalRate");
        long sum = list.size();
        //此处统计使用showName
        Map<DataAccuracyCheckHelper, List<StatisticsIndex>> mapByAccuracyCheck = list.stream().collect(Collectors.groupingBy(val ->
                DataAccuracyCheckHelper.of(val.getDataAccuracyCheck())));
        List<DataAccuracyCheckHelper> keys = new ArrayList<>(mapByAccuracyCheck.keySet());
        keys.sort(new DataAccuracyCheckHelper.DataAccuracyCheckComparator());
        List<DataQualityOverviewStatisticsVo.StatisticsDetailVO> detailVOList = keys.stream().map(helper ->
                DataQualityOverviewStatisticsVo.StatisticsDetailVO.of(helper.getShowName(),
                        0 == sum ? BigDecimal.ZERO : BigDecimal.valueOf(mapByAccuracyCheck.get(helper).size() * 1.0 / sum))).collect(Collectors.toList());
        long normalCount = list.stream().filter(val ->
                DataAccuracyCheckEnum.isNormal(val.getDataAccuracyCheck())).count();
        double rate = sum == 0 ? 0 : normalCount * 1.0 / sum;
        //log.info("normal计算完成，passCount:{}, sum:{}, rate:{}", normalCount, sum, rate);
        return DataQualityOverviewStatisticsVo.of(StatisticIndexEnum.NORMAL_RATE, BigDecimal.valueOf(rate).setScale(4, RoundingMode.HALF_UP), detailVOList);
    }
    public DataQualityOverviewStatisticsVo calculateYieldAccuracy(List<StatisticsIndex> list) {
        //log.info("计算批次良率准确率");
        long sum = list.size();
        long normalAndMatchCount = list.stream().filter(val ->
                DataAccuracyCheckEnum.isNormal(val.getDataAccuracyCheck()) && MesMatchTypeEnum.isMatch(val.getSummaryMatchType())).count();
        double rate = sum == 0 ? 0 : normalAndMatchCount * 1.0 / sum;
        //log.info("计算批次良率准确率计算完成，normalAndMatchCount:{}, sum:{}, rate:{}", normalAndMatchCount, sum, rate);
        return DataQualityOverviewStatisticsVo.of(StatisticIndexEnum.YIELD_ACCURACY, BigDecimal.valueOf(rate).setScale(4, RoundingMode.HALF_UP), null);
    }
    // 入库统计按周分组,数据准确性按类型分组
    public Map<String, List<StatisticsIndex>> statisticDataGroupBy(List<StatisticsIndex> statisticsIndexVOList, StatisticIndexEnum indexEnum) {
        if (CollectionUtils.isEmpty(statisticsIndexVOList)) {
            return Collections.emptyMap();
        }
        if (indexEnum == StatisticIndexEnum.MATCH_RATE) {
            return statisticsIndexVOList.stream()
                    .collect(Collectors.groupingBy(StatisticsIndex::getSummaryMatchType));
        }
        return groupByWeek(statisticsIndexVOList, StatisticsIndex::getWeek);
    }

    // 入库耗时按周分组
    public Map<String, List<FileLoadInfo>> fileDataGroupBy(List<FileLoadInfo> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyMap();
        }
        return groupByWeek(
                dataList.stream()
                        .filter(v -> FileStepEnum.STEP_7100.getStep() == v.getStep() && ProcessStatus.SUCCESS.equals(ProcessStatus.of(v.getProcessStatus())))
                        .collect(Collectors.toList()),
                FileLoadInfo::getStepEndTimeWeek
        );
    }

    private <T> Map<String, List<T>> groupByWeek(List<T> dataList, Function<T, String> weekExtractor) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyMap();
        }
        return dataList.stream()
                .filter(item -> weekExtractor.apply(item) != null)
                .collect(Collectors.groupingBy(weekExtractor));
    }


    private static final Comparator<String> KEY_COMPARATOR = (v1, v2) -> {
        if (v1.contains(Constant.HYPHEN) && v2.contains(Constant.HYPHEN)) {
            String[] arr1 = v1.split(Constant.HYPHEN);
            String[] arr2 = v2.split(Constant.HYPHEN);
            int cmp = compareNumericParts(arr1, arr2);
            if (cmp != 0) {return cmp;}
        }

        return v1.compareTo(v2);
    };

    private static int compareNumericParts(String[] arr1, String[] arr2) {
        int minLength = Math.min(arr1.length, arr2.length);

        for (int i = 0; i < minLength; i++) {
            if (NumberUtil.isInteger(arr1[i]) && NumberUtil.isInteger(arr2[i])) {
                int cmp = Integer.parseInt(arr1[i]) - Integer.parseInt(arr2[i]);
                if (cmp != 0) {return cmp;}
            }
        }

        return 0;
    }

    /**
     * 得到统计vo
     *
     * @param step1000fileMap step1000file数据map
     * @param key             关键
     * @param value           价值
     * @return {@link DataQualityOverviewStatisticsVo}
     */
    private static DataQualityOverviewStatisticsVo getStatisticsVO(Map<String, FileLoadInfo> step1000fileMap,
                                                                   String key, List<FileLoadInfo> value,
                                                                   Map<String, List<FileLoadInfo>> fileNameMap) {
        Map<String, FileLoadInfo> fileMap = value.stream().collect(Collectors.toMap(FileLoadInfo::getFileName, Function.identity(), (v1, v2) -> v1));

        long fileTransferMs = 0;
        long fileReceiveMs = 0;
        long fileInDbMs = 0;

        for (Map.Entry<String, FileLoadInfo> entry : fileMap.entrySet()) {
            FileLoadInfo step7100Data = entry.getValue();
            FileLoadInfo step1000Data = step1000fileMap.get(entry.getKey());

            if (step1000Data == null) {
                //log.warn("文件名:{} 没有step=1000的数据，跳过该文件", entry.getKey());
                continue; // 跳过而不是抛出异常
            }

            fileTransferMs += DateUtil.betweenMs(step1000Data.getRemoteFileMtime(), step7100Data.getFinishT());
            fileReceiveMs += DateUtil.betweenMs(step1000Data.getStepEndTime(), step1000Data.getRemoteFileMtime());
            fileInDbMs += accumulateStepTime(fileNameMap.get(entry.getKey()));
        }

        long fileCount = fileMap.size();
        double weekTransferHour = fileCount == 0 ? 0 : NumberUtil.div(fileTransferMs, fileCount * HOUR_MS_NUM, 2);
        double weekReceiveHour = fileCount == 0 ? 0 : NumberUtil.div(fileReceiveMs, fileCount * HOUR_MS_NUM, 2);
        double weekInDbHour = fileCount == 0 ? 0 : NumberUtil.div(fileInDbMs, fileCount * HOUR_MS_NUM, 2);

        return assembleWeeklyStatistics(key, fileCount, weekTransferHour, weekReceiveHour, weekInDbHour);
    }

    /**
     * 将step 1000至7100 endTime - startTime 累加
     */
    private static long accumulateStepTime(List<FileLoadInfo> value) {
        if (CollectionUtils.isEmpty(value)) {
            return 0;
        }

        Map<Integer, FileLoadInfo> stepMap = value.stream()
                .collect(Collectors.toMap(FileLoadInfo::getStep, Function.identity()));

        return FileStepEnum.valueList().stream()
                .mapToLong(stepEnum -> calculateStepDuration(stepMap.get(stepEnum.getStep())))
                .sum();
    }

    private static long calculateStepDuration(FileLoadInfo fileLoadInfo) {
        if (fileLoadInfo == null || fileLoadInfo.getStepStartTime() == null || fileLoadInfo.getStepEndTime() == null) {
            return 0;
        }
        return DateUtil.betweenMs(fileLoadInfo.getStepStartTime(), fileLoadInfo.getStepEndTime());
    }

    private static DataQualityOverviewStatisticsVo assembleWeeklyStatistics(String weekKey, long fileCount,
                                                                            double weekTransferHour, double weekReceiveHour, double weekInDbHour) {
        List<DataQualityOverviewStatisticsVo.StatisticsDetailVO> details = new ArrayList<>();

        if (fileCount > 0) {
            details.add(DataQualityOverviewStatisticsVo.StatisticsDetailVO.of(
                    weekKey, BigDecimal.valueOf(weekTransferHour), CostTimeEnum.FILE_TRANSFER.getType()));
            details.add(DataQualityOverviewStatisticsVo.StatisticsDetailVO.of(
                    weekKey, BigDecimal.valueOf(weekReceiveHour), CostTimeEnum.FILE_RECEIVE.getType()));
            details.add(DataQualityOverviewStatisticsVo.StatisticsDetailVO.of(
                    weekKey, BigDecimal.valueOf(weekInDbHour), CostTimeEnum.FILE_PARSE.getType()));
        }

        double totalHour = weekTransferHour + weekReceiveHour + weekInDbHour;
        return DataQualityOverviewStatisticsVo.of(StatisticIndexEnum.IN_DB_TIME, BigDecimal.valueOf(totalHour), details);
    }

    public Map<String, Object> getConstantMap() {
        Map<String, Object> map = new HashMap<>();
        // 填入testArea
        map.put("testArea", Arrays.stream(TestArea.values()).collect(Collectors.toMap(TestArea::getArea, TestArea::getTestScope)));

        return map;
    }
}
