package com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * 2025/4/27 15:23
 * DownloadOnlineRetestBinReqVo
 *
 * <AUTHOR>
 */
@Schema(title = "片内复测Die下载参数")
@Data
@EqualsAndHashCode(callSuper = false)
public class DownloadOnlineRetestDieReqVo {
    @NotEmpty(message = "fileId不能为空")
    private String fileId;
    @NotEmpty(message = "deviceId不能为空")
    private String deviceId;
    @NotEmpty(message = "testStage不能为空")
    private String testStage;
    @NotEmpty(message = "lotId不能为空")
    private String lotId;
    @NotEmpty(message = "sblotId不能为空")
    private String sblotId;
}
