package com.guwave.datahub.bpms.app.controller;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.service.bz.TestItemReloadService;
import com.guwave.datahub.bpms.app.vo.bz.testItemReload.CalTestItemWaferRequestVo;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 2025/7/7 17:01
 * TestItemReloadController
 *
 * <AUTHOR>
 */
@Log
//@Auth
//@License
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("testItemReload")
@Tag(name = "TestItemReload", description = "测项Reload接口"
)
public class TestItemReloadController {

    @Autowired
    private TestItemReloadService testItemReloadService;

    @Operation(summary = "conf配置 导入conf")
    @PostMapping("triggerCalTestItem")
    public ResponseVo<Void> triggerCalTestItem(@RequestBody @Validated CalTestItemWaferRequestVo calTestItemWaferRequestVo) {
        testItemReloadService.triggerCalTestItem(calTestItemWaferRequestVo);
        return ResponseVo.ok();
    }
}
