package com.guwave.datahub.bpms.app.vo.license;

import lombok.Data;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * LicenseResponseVo
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-27 11:53:37
 */
@Data
public class LicenseResponseVo<T> {

    public static final int SUCCESS_CODE = 200;

    private Integer code;

    private String msg;

    private T data;

    public boolean success() {
        return SUCCESS_CODE == code;
    }
}
