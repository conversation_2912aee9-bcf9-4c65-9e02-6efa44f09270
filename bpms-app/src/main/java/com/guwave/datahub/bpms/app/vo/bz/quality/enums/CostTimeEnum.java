package com.guwave.datahub.bpms.app.vo.bz.quality.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Copyright (C), 2021, guwave
 *
 * <AUTHOR>
 * @date 2023/2/8 10:09
 */
@Getter
@AllArgsConstructor
public enum CostTimeEnum {


    FILE_TRANSFER("FILE_TRANSFER", "文件传输耗时\nAvg(Ftp 加载时间 - End Time)"),
    FILE_RECEIVE("FILE_RECEIVE", "数据接收耗时\nAvg(数据接收时间 - Ftp 加载时间)"),
    FILE_PARSE("FILE_PARSE", "数据解析耗时\nAvg(Create Time - 数据接收时间)"),
//    DATA_ALL_TIME("DATA_ALL_TIME", "整体入库耗时\nAvg(Create Time - End Time)"),
    ;

    private final String type;
    private final String desc;
}
