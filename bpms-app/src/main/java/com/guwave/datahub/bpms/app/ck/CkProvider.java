package com.guwave.datahub.bpms.app.ck;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.guwave.datahub.bpms.dao.ck.ClickHouseConnection;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.ENTER;

/**
 * 2023/7/5 16:12
 * CkProvider
 *
 * <AUTHOR>
 */
@Component
public class CkProvider {

    private static final Logger LOGGER = LoggerFactory.getLogger(CkProvider.class);

    @Value("${spring.data.clickhouse.maxMutationsCnt}")
    private Long maxMutationsCnt;

    public <T> List<T> readCk(String sql, Class<T> clazz) {
        LOGGER.info("读取ck开始");
        long start = System.currentTimeMillis();
        // 创建connection
        Connection connection = null;
        PreparedStatement statement = null;
        try {
            connection = ClickHouseConnection.getConnection();
            assert connection != null;
            statement = connection.prepareStatement(sql);
            ResultSet resultSet = statement.executeQuery();
            List<T> data = resultSetToList(resultSet, clazz);
            LOGGER.info("读取ck完成，耗时：{}, dataSize: {}", System.currentTimeMillis() - start, data.size());
            return data;
        } catch (SQLException e) {
            // 处理SQLException
            LOGGER.error("SQL执行出错", e);
            return new ArrayList<>();
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    LOGGER.info("关闭statement失败");
                }
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    LOGGER.info("关闭connection失败");
                }
            }
        }
    }

    private <T> List<T> resultSetToList(ResultSet resultSet, Class<T> clazz) throws SQLException {
        ResultSetMetaData metaData = resultSet.getMetaData();
        int columnCount = metaData.getColumnCount();

        List<T> buffer = new ArrayList<>();
        while (resultSet.next()) {
            JSONObject row = new JSONObject();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnName(i);
                Object value = resultSet.getObject(i);
                row.put(columnName, value);
            }
            buffer.add(JSON.parseObject(row.toJSONString(), clazz));
        }
        return buffer;
    }


    public void executeSqls(List<String> sqls) {
        String allSql = String.join(ENTER, sqls);
        LOGGER.info("执行sql：{}", allSql);
        long start = System.currentTimeMillis();
        executeConnect(connection -> {
            sqls.forEach(sql -> executeStatement(connection, statement -> {
                try {
                    LOGGER.info("执行sql 开始：{}", sql);
                    statement.execute(sql);
                    LOGGER.info("执行sql 结束：{}", sql);
                } catch (Exception e) {
                    LOGGER.info("执行sql 异常：{}", sql, e);
                    throw new RuntimeException(e);
                }
                return null;
            }));
            return null;
        });
        LOGGER.info("执行sql {} 完成，耗时：{}", allSql, System.currentTimeMillis() - start);
    }

    private static <R> R executeConnect(Function<Connection, R> function) {
        R res = null;
        Connection connection = null;
        try {
            connection = ClickHouseConnection.getConnection();
            res = function.apply(connection);
        } catch (Exception e) {
            LOGGER.info("executeConnect 异常：", e);
            throw new RuntimeException(e);
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (Exception e) {
                    LOGGER.info("close connection failed", e);
                }
            }
        }
        return res;
    }

    private static <R> R executeStatement(Connection connection, Function<Statement, R> function) {
        R res = null;
        Statement statement = null;
        try {
            statement = connection.createStatement();
            res = function.apply(statement);
        } catch (Exception e) {
            LOGGER.info("executeStatement 异常：", e);
            throw new RuntimeException(e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (Exception e) {
                    LOGGER.info("close statement failed", e);
                }
            }
        }
        return res;
    }

    private static <R> List<R> executeOnEachNode(Function<Connection, R> function) {
        List<Connection> allConnections = ClickHouseConnection.getAllConnection();
        try {
            return allConnections.stream().map(connection -> {
                R res;
                try {
                    res = function.apply(connection);
                } catch (Exception e) {
                    LOGGER.info("executeConnect 异常：", e);
                    throw new RuntimeException(e);
                }
                return res;
            }).collect(Collectors.toList());
        } finally {
            for (Connection conn : allConnections) {
                if (conn != null) {
                    try {
                        conn.close();
                    } catch (Exception e) {
                        LOGGER.info("close connection failed", e);
                    }
                }
            }
        }
    }

    public boolean checkCkMutations() {
        boolean flag = false;
        LOGGER.info("maxMutationsCnt:{}", maxMutationsCnt);
        try {
            executeOnEachNode(connection -> {
                String ckMutationsCntSql = "select count(1) CNT from system.mutations where is_done = 0";
                executeStatement(connection, statement -> {
                    try {
                        LOGGER.info("执行sql 开始：{}", ckMutationsCntSql);
                        ResultSet resultSet = statement.executeQuery(ckMutationsCntSql);
                        resultSet.next();
                        long cnt = resultSet.getLong("CNT");
                        LOGGER.info("执行sql 结束：{} , 结果：{}", ckMutationsCntSql, cnt);
                        if (cnt > maxMutationsCnt) {
                            throw new RuntimeException("ck mutations 太多，暂停入库!");
                        }
                    } catch (Exception e) {
                        LOGGER.info("执行sql 异常：{}", ckMutationsCntSql, e);
                        throw new RuntimeException(e);
                    }
                    return null;
                });
                return null;
            });
            flag = true;
        } catch (Exception e) {
            flag = false;
        }
        return flag;
    }

}
