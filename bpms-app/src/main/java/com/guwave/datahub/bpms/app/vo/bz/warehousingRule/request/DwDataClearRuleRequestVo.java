package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(title = "特殊逻辑配置规则请求对象")
public class DwDataClearRuleRequestVo {
    @Schema(title = "id")
    private Long id;

    @Schema(description = "子客户", example = "GUWAVE")
    private String subCustomer;

    @Schema(description = "测试阶段类型: CP/FT/WAT/SLT/EQC 等", example = "CP")
    private String testArea;

    @Schema(description = "测试工厂名称", example = "TSMC")
    private String factory;

    @Schema(description = "测试子工厂", example = "TSMC")
    private String factorySite;

    @Schema(description = "DEVICE ID", example = "TMPU29E")
    private String deviceId;

    @Schema(description = "字段大类，比如MIR、PRT、PRR等", example = "FTR")
    private String fieldType;

    @Schema(description = "规则，比如SWAP、REPLACE等", example = "CLEAR_TXT_REGEX")
    private String ruleType;

    @Schema(description = "具体规则", example = "^(.*?)-[0-9]+$")
    private String rule;
}
