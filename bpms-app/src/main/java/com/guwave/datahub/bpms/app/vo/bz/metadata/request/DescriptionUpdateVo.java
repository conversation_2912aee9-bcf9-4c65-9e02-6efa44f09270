package com.guwave.datahub.bpms.app.vo.bz.metadata.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * DescriptionUpdateVo
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-03-11 15:31:40
 */
@Schema(title = "metadata 修改表描述")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
public class DescriptionUpdateVo extends TableVo {

    @Schema(title = "描述", example = "后端的表")
    @NotNull(message = "描述")
    private String description;
}
