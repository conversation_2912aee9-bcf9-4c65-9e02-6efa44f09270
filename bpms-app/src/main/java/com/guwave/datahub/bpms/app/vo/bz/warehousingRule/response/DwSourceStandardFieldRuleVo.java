package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.response;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SourceStandardFieldRule;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_FORMAT;

/**
 * 2025/3/4 16:49
 * DwSourceStandardFieldRuleVo
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(title = "关键字段取值规则")
public class DwSourceStandardFieldRuleVo {

    @Schema(title = "id")
    private Long id;

    @Schema(description = "客户名称", example = "AMD")
    private String customer;

    @Schema(description = "子客户", example = "AMD")
    @ExcelProperty("subCustomer")
    private String subCustomer;

    @Schema(description = "deviceId", example = "DDR5-001")
    @ExcelProperty("deviceId")
    private String deviceId;

    @Schema(description = "测试阶段类型: CP/FT/WAT/SLT/EQC 等", example = "CP")
    @ExcelProperty("testArea")
    private String testArea;

    @Schema(description = "测试工厂名称", example = "TSMC")
    @ExcelProperty("factory")
    private String factory;

    @Schema(description = "测试子工厂", example = "TSMC")
    @ExcelProperty("factorySite")
    private String factorySite;

    @Schema(description = "metaData中的字段名称", example = "waferNo")
    @ExcelProperty("field")
    private String field;

    @Schema(description = "js执行代码", example = "result = 25")
    @ExcelProperty("ruleExpression")
    private String ruleExpression;

    @Schema(title = "createTime", description = "创建时间", example = "2024-11-15 11:21:00")
    private String createTime;

    @Schema(title = "updateTime", description = "更新时间", example = "2024-11-18 11:21:00")
    private String updateTime;

    @Schema(description = "创建用户", example = "System")
    private String createUser;

    @Schema(description = "更新用户", example = "System")
    private String updateUser;

    public static DwSourceStandardFieldRuleVo of(SourceStandardFieldRule data) {
        return DwSourceStandardFieldRuleVo.builder()
                .id(data.getId())
                .customer(data.getCustomer())
                .subCustomer(data.getSubCustomer())
                .deviceId(data.getDeviceId())
                .testArea(TestArea.of(data.getTestArea()))
                .factory(data.getFactory())
                .factorySite(data.getFactorySite())
                .field(data.getField())
                .ruleExpression(data.getRuleExpression())
                .createTime(DateUtil.format(data.getCreateTime(), NORM_DATETIME_FORMAT))
                .updateTime(DateUtil.format(data.getUpdateTime(), NORM_DATETIME_FORMAT))
                .createUser(data.getCreateUser())
                .updateUser(data.getUpdateUser())
                .build();
    }
}
