package com.guwave.datahub.bpms.app.vo.bz.dataTimeliness.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@Schema(title = "数据及时性查询请求体")
@Data
@EqualsAndHashCode(callSuper = false)
public class DataTimelinessQueryVo {

    @Schema(description = "子客户", example = "[\"AMD\"]")
    private List<String> subCustomerList;

    @Schema(description = "工厂", example = "[\"AMD\"]")
    private List<String> factoryList;

    @Schema(description = "测试区域", example = "CP")
    private String testArea;

    @Schema(description = "测试程序", example = "[\"program-1\"]")
    private List<String> testProgramList;

    @Schema(description = "deviceId", example = "[\"deviceId-1\"]")
    private List<String> deviceIdList;

    @Schema(description = "testStage", example = "[\"CP2\"]")
    private List<String> testStageList;

    @Schema(description = "lotId", example = "[\"lotId-1\"]")
    private List<String> lotIdList;

    @Schema(description = "waferNo", example = "[\"1\"]")
    private List<String> waferNoList;

    @Schema(description = "sblotId", example = "[\"sblotId-1\"]")
    private List<String> sblotIdList;

    @Schema(description = "更新时间开始", example = "2023-09-20 08:14:13")
    private String startTime;

    @Schema(description = "更新时间结束", example = "2023-09-21 08:14:13")
    private String endTime;

}
