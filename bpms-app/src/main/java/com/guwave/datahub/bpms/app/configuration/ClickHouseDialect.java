package com.guwave.datahub.bpms.app.configuration;

import org.hibernate.dialect.Dialect;
import org.hibernate.dialect.function.SQLFunctionTemplate;
import org.hibernate.dialect.function.StandardSQLFunction;
import org.hibernate.dialect.function.VarArgsSQLFunction;
import org.hibernate.type.StandardBasicTypes;

import java.sql.Types;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * ClickHouseDialect - Hibernate的ClickHouse方言
 *
 * <AUTHOR> @version 0.0.1
 */
public class ClickHouseDialect extends Dialect {

    public ClickHouseDialect() {
        registerColumnType(Types.BIT, "UInt8");
        registerColumnType(Types.TINYINT, "Int8");
        registerColumnType(Types.SMALLINT, "Int16");
        registerColumnType(Types.INTEGER, "Int32");
        registerColumnType(Types.BIGINT, "Int64");
        registerColumnType(Types.FLOAT, "Float32");
        registerColumnType(Types.DOUBLE, "Float64");
        registerColumnType(Types.NUMERIC, "Decimal(38,18)");
        registerColumnType(Types.DECIMAL, "Decimal(38,18)");
        registerColumnType(Types.CHAR, "String");
        registerColumnType(Types.VARCHAR, "String");
        registerColumnType(Types.LONGVARCHAR, "String");
        registerColumnType(Types.DATE, "Date");
        registerColumnType(Types.TIME, "DateTime");
        registerColumnType(Types.TIMESTAMP, "DateTime");
        registerColumnType(Types.VARBINARY, "String");
        registerColumnType(Types.BINARY, "String");
        registerColumnType(Types.LONGVARBINARY, "String");
        registerColumnType(Types.BLOB, "String");
        registerColumnType(Types.CLOB, "String");
        registerColumnType(Types.BOOLEAN, "UInt8");
        
        registerFunction("abs", new StandardSQLFunction("abs"));
        registerFunction("upper", new StandardSQLFunction("upperUTF8"));
        registerFunction("lower", new StandardSQLFunction("lowerUTF8"));
        registerFunction("concat", new VarArgsSQLFunction("concat(", ",", ")"));
        registerFunction("substring", new StandardSQLFunction("substring"));
        registerFunction("trim", new StandardSQLFunction("trim"));
        registerFunction("length", new StandardSQLFunction("length"));
        registerFunction("count", new StandardSQLFunction("count"));
        registerFunction("avg", new StandardSQLFunction("avg"));
        registerFunction("sum", new StandardSQLFunction("sum"));
        registerFunction("max", new StandardSQLFunction("max"));
        registerFunction("min", new StandardSQLFunction("min"));
        registerFunction("now", new StandardSQLFunction("now"));
        registerFunction("today", new StandardSQLFunction("today"));
        registerFunction("toDateTime", new StandardSQLFunction("toDateTime"));
        registerFunction("toDate", new StandardSQLFunction("toDate"));
    }

    @Override
    public String getAddColumnString() {
        return "ADD COLUMN";
    }

    @Override
    public boolean supportsLimit() {
        return true;
    }

    @Override
    public boolean supportsLimitOffset() {
        return true;
    }

    @Override
    public boolean bindLimitParametersInReverseOrder() {
        return false;
    }

    @Override
    public String getLimitString(String query, boolean hasOffset) {
        return query + (hasOffset ? " LIMIT ? OFFSET ?" : " LIMIT ?");
    }
    
    @Override
    public boolean supportsCurrentTimestampSelection() {
        return true;
    }

    @Override
    public boolean isCurrentTimestampSelectStringCallable() {
        return false;
    }

    @Override
    public String getCurrentTimestampSelectString() {
        return "SELECT now()";
    }
} 
