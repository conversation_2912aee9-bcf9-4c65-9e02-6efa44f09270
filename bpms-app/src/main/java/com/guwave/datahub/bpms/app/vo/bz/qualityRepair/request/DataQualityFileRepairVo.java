package com.guwave.datahub.bpms.app.vo.bz.qualityRepair.request;

import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class DataQualityFileRepairVo extends PageableRequestVo {

    @Schema(title = "subCustomerList", description = "sub_customer列表", example = "[\"AMD\"]")
    private List<String>  subCustomerList;

    @Schema(title = "fileIdList", description = "file_id列表", example = "[123]")
    private List<Long> fileIdList;

    @Schema(title = "fileNameList", description = "file_name列表", example = "[\"CP1_RT_2022-10-10.stdf\"]")
    private List<String> fileNameList;

    @Schema(title = "deviceIdList", description = "device_id列表", example = "[\"SS521BB-DEFAULT\"]")
    private List<String> deviceIdList;

    @Schema(title = "testStageList", description =  "test_stage列表", example = "[\"CP1\"]")
    private List<String> testStageList;

    @Schema(title = "lotIdList", description =  "lot_id列表", example = "[\"NT41A\"]")
    private List<String> lotIdList;

    @Schema(title = "repairTimesList", description =  "修复次数列表", example = "[5]")
    private List<Integer> repairTimesList;

    @Schema(title = "repairCategoryList", description = "修改类别列表", example = "[\"DATA_ACCURACY\"]")
    private List<String> repairCategoryList;

    @Schema(title = "lastRepairTypeList", description = "修改类型列表", example = "[\"关键字段补充\"]")
    private List<String> lastRepairTypeList;

    @Schema(title = "repairStatusList", description = "修改状态列表", example = "[\"SUCCESS\"]")
    private List<String> repairStatusList;
}