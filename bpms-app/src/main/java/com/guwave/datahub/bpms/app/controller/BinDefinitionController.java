package com.guwave.datahub.bpms.app.controller;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.DataPermission;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.service.bz.BinDefinitionService;
import com.guwave.datahub.bpms.app.vo.bz.binDefinition.request.*;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import com.guwave.datahub.bpms.dao.vo.bpms.binDefinition.BinDefinitionDetail;
import com.guwave.datahub.bpms.dao.vo.bpms.binDefinition.BinDefinitionDropDown;
import com.guwave.datahub.bpms.dao.vo.bpms.binDefinition.ConfiguredTestProgramVo;
import com.guwave.datahub.bpms.dao.vo.bpms.binDefinition.UnconfiguredTestProgramVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Test Program Bin Definition Controller
 *
 * <AUTHOR>
 * @version 1.0
 */
@Log
@Auth
@Validated
@Slf4j
@RestController
@AllArgsConstructor
@Tag(name = "BinDefinitionControllerAPI", description = "测试程序Bin定义管理接口", 
     externalDocs = @ExternalDocumentation(description = "需求文档", url = ""))
@RequestMapping("binDefinition")
public class BinDefinitionController {

    private final BinDefinitionService binDefinitionService;


    @Operation(summary = "搜索测试程序")
    @PostMapping("search/testPrograms")
    @DataPermission
    public ResponseVo<List<BinDefinitionDropDown>> searchTestPrograms(@RequestBody @Validated BinDefinitionDropDownQueryVo queryVo) {
        List<BinDefinitionDropDown> testPrograms = binDefinitionService.searchTestPrograms(queryVo);
        return ResponseVo.ok(testPrograms);
    }

    @Operation(summary = "获取已配置的测试程序列表")
    @PostMapping("configured")
    public ResponseVo<PageableDataVo<ConfiguredTestProgramVo>> getConfiguredTestPrograms(
            @RequestBody @Validated BinDefinitionQueryVo queryVo) {
        return ResponseVo.ok(binDefinitionService.getConfiguredTestPrograms(queryVo));
    }

    @Operation(summary = "获取未配置的测试程序列表")
    @PostMapping("unconfigured")
    public ResponseVo<PageableDataVo<UnconfiguredTestProgramVo>> getUnconfiguredTestPrograms(
            @RequestBody @Validated BinDefinitionQueryVo queryVo) {
        return ResponseVo.ok(binDefinitionService.getUnconfiguredTestPrograms(queryVo));
    }

    @Operation(summary = "获取测试程序的bin定义详情")
    @GetMapping("details/{testProgram}")
    public ResponseVo<List<BinDefinitionDetail>> getBinDefinitions(
            @PathVariable @NotNull @Parameter(description = "测试程序名称") String testProgram) {
        return ResponseVo.ok(binDefinitionService.getBinDefinitions(testProgram));
    }

    @Operation(summary = "保存测试程序的bin定义")
    @PostMapping("save")
    public ResponseVo<Void> saveBinDefinitions(@RequestBody @Validated BinDefinitionSaveVo saveVo) {
        binDefinitionService.saveBinDefinitions(saveVo);
        return ResponseVo.ok();
    }

    @Operation(summary = "删除测试程序的bin定义")
    @PostMapping("delete")
    public ResponseVo<Void> deleteTestProgramBinDefinitions(@RequestBody @Validated BinDefinitionDeleteVo deleteVo) {
        binDefinitionService.deleteTestProgramBinDefinitions(deleteVo);
        return ResponseVo.ok();
    }

    @Operation(summary = "导出bin定义到CSV")
    @PostMapping("export")
    public void exportBinDefinitions(@RequestBody BinDefinitionQueryVo queryVo, HttpServletResponse response) {
        binDefinitionService.exportBinDefinitions(response, queryVo);
    }

    @Operation(summary = "从文件导入bin定义")
    @PostMapping("import")
    @DataPermission
    public ResponseVo<com.guwave.datahub.bpms.app.vo.bz.binDefinition.response.BinDefinitionImportResultVo> importBinDefinitions(
            @RequestParam("file") @Parameter(description = "导入文件") MultipartFile file,
            @RequestParam("importMode") @Parameter(description = "导入模式") BinDefinitionImportVo.ImportMode importMode) {
        
        BinDefinitionImportVo importVo = new BinDefinitionImportVo();
        importVo.setImportMode(importMode);
        
        return ResponseVo.ok(binDefinitionService.importBinDefinitions(file, importVo));
    }

    @Operation(summary = "导出模板")
    @GetMapping("export/template")
    public void exportTemplate(HttpServletResponse response) {
        binDefinitionService.exportTemplate(response);
    }
}
