package com.guwave.datahub.bpms.app.vo.bz.dataQualityOverview.response;

import com.guwave.datahub.bpms.common.constant.StatisticIndexEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DataQualityOverviewStatisticsVo {

    private StatisticIndexEnum calculateIndexType;
    private BigDecimal baseIndexValue;
    private List<StatisticsDetailVO> detail;
    private Long totalCnt;
    private Long successCnt;
    private Long failNumCnt;
    private Long processCnt;

    @Data
    @Builder
    public static class StatisticsDetailVO {
        private String indexKey;
        private BigDecimal indexValue;
        private String colorByKey;
        private BigDecimal percent;


        /**
         * 计算结果详情
         *
         * @param indexKey   统计类型
         * @param indexValue 统计值
         * @return 计算结果详情
         */
        public static StatisticsDetailVO of(String indexKey, BigDecimal indexValue) {
            return StatisticsDetailVO.builder()
                    .indexKey(indexKey)
                    .indexValue(indexValue)
                    .build();
        }


        /**
         * 计算结果详情
         *
         * @param indexKey   统计类型
         * @param indexValue 统计值
         * @return 计算结果详情
         */
        public static StatisticsDetailVO of(String indexKey, BigDecimal indexValue, String colorByKey) {
            return StatisticsDetailVO.builder()
                    .indexKey(indexKey)
                    .indexValue(indexValue)
                    .colorByKey(colorByKey)
                    .build();
        }

    }

    /**
     * 数据指标统计数据结果
     *
     * @param calculateIndexType {@link StatisticIndexEnum}
     * @param totalCnt           {@link BigDecimal}
     * @param detail             {@link List}
     * @return StatisticsVO
     */
    public static DataQualityOverviewStatisticsVo of(StatisticIndexEnum calculateIndexType, BigDecimal totalCnt, List<StatisticsDetailVO> detail) {
        return DataQualityOverviewStatisticsVo.builder()
                .calculateIndexType(calculateIndexType)
                .baseIndexValue(totalCnt)
                .detail(detail)
                .build();
    }

}
