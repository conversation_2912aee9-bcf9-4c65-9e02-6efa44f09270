package com.guwave.datahub.bpms.app.vo.bz.replay.response;

import cn.hutool.core.date.DateUtil;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.CleanupTask;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_FORMAT;

@Schema(title = "数据重播记录")
@Data
@Builder
public class DataReplayTaskVo {
    @Schema(title = "id")
    private Long id;

    @Schema(title = "testArea", description = "CP/FT/WAT等 表示测试阶段大类型", example = "CP,FT")
    private String testArea;

    @Schema(title = "工厂", description = "测试工厂", example = "VC")
    private String factory;

    @Schema(title = "deviceId", example = "SS521BB-DEFAULT")
    private String deviceId;

    @Schema(title = "fileCategory", description = "STDF,RAW_DATA,WAT", example = "STDF")
    private String fileCategory;

    @Schema(title = "lotId", example = "NT41A")
    private String lotId;

    @Schema(title = "waferNo", example = "8")
    private String waferNo;

    @Schema(title = "lotType", description = "PRODUCTION,ENGINEERING", example = "PRODUCTION")
    private String lotType;

    @Schema(title = "testStage", example = "CP1")
    private String testStage;

    @Schema(title = "replayType", description = "重播类型", example = "REPROCESS")
    private String replayType;

    @Schema(title = "step", description = "重播步骤", example = "3100")
    private Integer step;

    @Schema(title = "fileList", description = "文件列表", example = "CP1_RT_2022-10-10.stdf,CP1_RT_2022-11-10.stdf")
    private String fileList;

    @Schema(title = "exception", description = "任务异常信息", example = "lotType关键字段为空,testStage关键字段为空,lotId关键字段为空,waferId关键字段为空,waferNo关键字段为空")
    private String exception;

    @Schema(title = "errorMessage", description = "任务错误信息", example = "com.guwave.onedata.dataware.common.exception.FileLoadException: 必要字段为空：{\"batchNum\":0,\"conditionSet\":{},")
    private String errorMessage;

    @Schema(title = "status", description = "重播状态", example = "FAIL")
    private String status;

    @Schema(title = "createTime", description = "重播开始时间", example = "2023-09-19 08:14:13")
    private String createTime;

    @Schema(title = "updateTime", description = "重播更新时间", example = "2023-09-20 08:14:13")
    private String updateTime;


    public static DataReplayTaskVo of(CleanupTask data) {
        return DataReplayTaskVo.builder()
                .id(data.getId())
                .testArea(TestArea.of(data.getTestArea()))
                .factory(data.getFactory())
                .deviceId(data.getDeviceId())
                .fileCategory(FileCategory.of(data.getFileCategory()))
                .lotId(data.getLotId())
                .waferNo(data.getWaferNo())
                .lotType(LotType.of(data.getLotType()))
                .testStage(data.getTestStage())
                .replayType(data.getCleanupType().getType())
                .step(data.getStep().getStep())
                .fileList(String.join(Constant.COMMA, data.getFileList()))
                .exception(data.getExceptionMessage())
                .errorMessage(data.getErrorMessage())
                .status(data.getProcessStatus().getStatus())
                .createTime(DateUtil.format(data.getCreateTime(), NORM_DATETIME_FORMAT))
                .updateTime(DateUtil.format(data.getUpdateTime(), NORM_DATETIME_FORMAT))
                .build();
    }

}
