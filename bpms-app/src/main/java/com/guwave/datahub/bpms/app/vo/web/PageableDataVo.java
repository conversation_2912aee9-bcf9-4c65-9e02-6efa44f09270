package com.guwave.datahub.bpms.app.vo.web;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * PageableDataVo
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-28 16:16:12
 */
@Data
@Schema(title = "分页返回信息")
public class PageableDataVo<T> {

    public static final Integer DEFAULT_PAGE_NUM = 1;
    public static final Integer DEFAULT_PAGE_SIZE = 10;

    @Schema(title = "页码", description = "从1开始", example = "1", defaultValue = "1")
    private int pageIndex;

    @Schema(title = "每页条数", example = "10", defaultValue = "10")
    private int pageSize;

    @Schema(title = "总条数", example = "1024")
    private long total;

    @Schema(title = "总页数", example = "103")
    private int pages;

    @Schema(title = "列表数据")
    private List<T> data;

    public static <T> PageableDataVo<T> of(Page<T> page) {
        Pageable pageable = page.getPageable();
        PageableDataVo<T> pageableDataVo = new PageableDataVo<>();
        pageableDataVo.setPageIndex(pageable.getPageNumber() + 1);
        pageableDataVo.setPageSize(pageable.getPageSize());
        pageableDataVo.setTotal(page.getTotalElements());
        pageableDataVo.setPages(page.getTotalPages());
        pageableDataVo.setData(page.getContent());
        return pageableDataVo;
    }
}
