package com.guwave.datahub.bpms.app.controller;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.service.bz.DataQualityRepairService;
import com.guwave.datahub.bpms.app.vo.bz.qualityRepair.request.DataQualityBatchRepairDropDownFilterVo;
import com.guwave.datahub.bpms.app.vo.bz.qualityRepair.request.DataQualityBatchRepairVo;
import com.guwave.datahub.bpms.app.vo.bz.qualityRepair.request.DataQualityFileRepairDropDownFilterVo;
import com.guwave.datahub.bpms.app.vo.bz.qualityRepair.request.DataQualityFileRepairVo;
import com.guwave.datahub.bpms.app.vo.bz.qualityRepair.response.*;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Log
@Auth
//@License
@Validated
@Slf4j
@RestController
@AllArgsConstructor
@Tag(name = "DataQualityRepairControllerAPI",
     description = "数据修复记录接口",
     externalDocs = @ExternalDocumentation(description = "需求文档",
     url = "https://loop.cloud.microsoft/p/eyJ3Ijp7InUiOiJodHRwczovL2d1d2F2ZS5zaGFyZXBvaW50LmNvbS8%2FbmF2PWN6MGxNa1ltWkQxaUlUQnRhalpRWDBSUGF6Qkhia1J3TjE5U04yMTRaREJOVlROaVozZEljbWhIY0VoWVZFaFVabFUwWkRWQmRWQmpWVmg1YkVGUlNteFZSRUkyZUVwaGJEVW1aajB3TVVoRFRFbERURUpYVWtSVlZ6WkhTbEpIUmtaTVMwVkpURWhQVVVkSVRFdFBKbU05Sm1ac2RXbGtQVEUlM0QiLCJyIjpmYWxzZX0sInAiOnsidSI6Imh0dHBzOi8vZ3V3YXZlLnNoYXJlcG9pbnQuY29tLzpmbDovci9jb250ZW50c3RvcmFnZS9DU1BfM2ZmYTY4ZDItY2VmMC00MTkzLWE3MGUtOWVmZjQ3YjliMTc3L0RvY3VtZW50JTIwTGlicmFyeS9Mb29wQXBwRGF0YS8lRTYlOTclQTAlRTYlQTAlODclRTklQTIlOTglMjAzLmxvb3A%2FZD13MjZlMjY4NDg2M2UxNDU5MmIxZmU3MTY4YTFhNTQ1OGYmY3NmPTEmd2ViPTEmbmF2PWN6MGxNa1pqYjI1MFpXNTBjM1J2Y21GblpTVXlSa05UVUY4elptWmhOamhrTWkxalpXWXdMVFF4T1RNdFlUY3daUzA1WldabU5EZGlPV0l4TnpjbVpEMWlJVEJ0YWpaUVgwUlBhekJIYmtSd04xOVNOMjE0WkRCTlZUTmlaM2RJY21oSGNFaFlWRWhVWmxVMFpEVkJkVkJqVlZoNWJFRlJTbXhWUkVJMmVFcGhiRFVtWmowd01VaERURWxEVEVOSlRrUlNRMDVaVEVSVFNrTXpSRGRVVWs1RFVUSkxVazFRSm1NOUpUSkdKbVpzZFdsa1BURW1ZVDFNYjI5d1FYQndKbkE5SlRRd1pteDFhV1I0SlRKR2JHOXZjQzF3WVdkbExXTnZiblJoYVc1bGNpWjRQU1UzUWlVeU1uY2xNaklsTTBFbE1qSlVNRkpVVlVoNGJtUllaR2hrYlZWMVl6Sm9hR050Vm5kaU1teDFaRU0xYW1JeU1UaFphVVYzWWxkdk1sVkdPVVZVTW5OM1VqSTFSV05FWkdaVmFtUjBaVWRSZDFSV1ZYcFpiV1F6VTBoS2IxSXpRa2xYUmxKSlZrZGFWazVIVVRGUldGWlJXVEZXV1dWWGVFSlZWWEJ6VmxWU1EwNXVhRXRaVjNjeFprUkJlRk5GVGsxVFZVNU5VV3hrVTFKR1ZsaE9hMlJMVld0a1IxSnJlRXhTVld4TlUwVTVVbEl3YUUxVE1EZ2xNMFFsTWpJbE1rTWxNakpwSlRJeUpUTkJKVEl5WlRBMll6RTFaVGN0TTJWallTMDBNekF3TFRoalpqQXRORFkyWVRGaFpUQXpPR013SlRJeUpUZEUiLCJyIjpmYWxzZX0sImkiOnsiaSI6ImUwNmMxNWU3LTNlY2EtNDMwMC04Y2YwLTQ2NmExYWUwMzhjMCJ9fQ%3D%3D")
)
@RequestMapping("dataRepairRecord")
public class DataRepairRecordController {

    @Autowired
    private DataQualityRepairService dataQualityRepairService;

    @PostMapping("/batchRecord/getBatchRecordDropdown")
    @Operation(summary = "批次变更记录页面下拉框")
    public ResponseVo<List<DataQualityBatchRepairDropDownVo>> getBatchRecordDropdown(@RequestBody DataQualityBatchRepairDropDownFilterVo dataQualityBatchRepairDropDownFilterVO) {
        return ResponseVo.ok(dataQualityRepairService.getBatchRecordDropdown(dataQualityBatchRepairDropDownFilterVO));
    }

    @PostMapping("/batchRecord/getBatchRecordList")
    @Operation(summary = "批次变更记录页面接口")
    public ResponseVo<PageableDataVo<DataRepairEffectBatchVo>> getBatchRecordList(@RequestBody DataQualityBatchRepairVo dataQualityBatchRepairVo) {
        return ResponseVo.ok(dataQualityRepairService.getBatchRecordList(dataQualityBatchRepairVo));
    }

    @PostMapping("/fileRecord/getFileRecordDropdown")
    @Operation(summary = "文件修复记录页面下拉框")
    public ResponseVo<List<DataQualityFileRepairDropDownVo>> getFileRecordDropdown(@RequestBody DataQualityFileRepairDropDownFilterVo dataQualityFileRepairDropDownFilterVO) {
        return ResponseVo.ok(dataQualityRepairService.getFileRecordDropdown(dataQualityFileRepairDropDownFilterVO));
    }

    @PostMapping("/fileRecord/getFileRecordList")
    @Operation(summary = "文件修复记录页面接口")
    public ResponseVo<PageableDataVo<DataQualityBatchRepairInfoVo>> getFileRecordList(@RequestBody DataQualityFileRepairVo dataQualityFileRepairVo) {
        return ResponseVo.ok(dataQualityRepairService.getFileRecordList(dataQualityFileRepairVo));
    }

    @GetMapping("/batchRecord/getFileList")
    @Operation(summary = "查看批次变更影响的文件接口")
    @Parameter(name = "id", description = "批次ID")
    public ResponseVo<List<DataQualityBatchRepairInfoVo>> getFileList(@RequestParam(name = "id") Long id) {
        return ResponseVo.ok(dataQualityRepairService.getFileList(id));
    }

    @GetMapping("/fileRecord/getFileLogList")
    @Operation(summary = "查看文件修复记录接口")
    @Parameter(name = "fileId", description = "文件ID")
    public ResponseVo<List<DataQualityFileRepairInfoVo>> getFileLogList(@RequestParam(name = "fileId") Long fileId) {
        return ResponseVo.ok(dataQualityRepairService.getFileLogList(fileId));
    }
}