package com.guwave.datahub.bpms.app.vo.bz.dataTimeliness.response;

import com.guwave.datahub.bpms.common.constant.Constant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Schema(title = "下拉框结果")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataTimelinessDropDownVo {
    private static final Pattern NUMBER_PATTERN = Pattern.compile("-?\\d+");

    @Schema(title = "页面显示下拉框结果值", example = "FACTORY")
    private String label;
    @Schema(title = "页面显示下拉框结果值", example = "AMD")
    private String value;

    public static DataTimelinessDropDownVo of(String name) {
        return DataTimelinessDropDownVo.of(name, name);
    }

    public static DataTimelinessDropDownVo of(String label, String value) {
        return new DataTimelinessDropDownVo(label, value);
    }

    public static List<DataTimelinessDropDownVo> ofList(List<String> names, Boolean needSort) {
        if (CollectionUtils.isEmpty(names)) {
            return Collections.emptyList();
        }

        if (!needSort) {
            return names.stream()
                    .map(DataTimelinessDropDownVo::of)
                    .collect(Collectors.toList());
        }

        List<String> topPriorityList = Arrays.asList(Constant.WRAPPER_NULL, Constant.EMPTY);

        Map<Boolean, List<String>> sortedMap = names.stream()
                .collect(Collectors.groupingBy(topPriorityList::contains, Collectors.toList()));
        sortedMap.computeIfAbsent(true, key -> new ArrayList<>()).sort(Comparator.comparing(String::toString).reversed());

        boolean isNumer = sortedMap.getOrDefault(false, new ArrayList<>()).stream().allMatch(t -> NUMBER_PATTERN.matcher(t).matches());
        sortedMap.computeIfAbsent(false, key -> new ArrayList<>()).sort(isNumer ? Comparator.comparingInt(Integer::parseInt) : Comparator.comparing(String::toString));

        List<String> sortedList = new ArrayList<>();
        sortedList.addAll(sortedMap.get(true));
        sortedList.addAll(sortedMap.get(false));

        return sortedList.stream()
                .map(DataTimelinessDropDownVo::of)
                .collect(Collectors.toList());
    }
}
