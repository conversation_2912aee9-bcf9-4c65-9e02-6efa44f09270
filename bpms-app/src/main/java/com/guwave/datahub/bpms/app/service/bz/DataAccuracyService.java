package com.guwave.datahub.bpms.app.service.bz;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.util.BigDecimalUtil;
import com.guwave.datahub.bpms.app.util.ExcelUtil;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.DataAccuracyCheckHelper;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.GoodRetestBinExportDTO;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.OnlineRetestBinExportDTO;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.DataAccuracyBatchMatchQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.DataAccuracyDetailQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.DataAccuracyDropDownFilterVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.DataAccuracyExportDataQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.DataAccuracyQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.DownloadGoodRetestDieReqVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request.DownloadOnlineRetestDieReqVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response.DataAccuracyBatchMatchDetailVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response.DataAccuracyBatchYieldDetailVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response.DataAccuracyDetailResVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response.DataAccuracyDropDownVo;
import com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response.FileRepairRecordVo;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import com.guwave.datahub.bpms.app.vo.web.SortPageableRequestVo;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.common.constant.DataAccuracyCheckEnum;
import com.guwave.datahub.bpms.common.constant.DataAccuracyPageDropdownField;
import com.guwave.datahub.bpms.common.constant.MesMatchTypeEnum;
import com.guwave.datahub.bpms.dao.clickhouse.domain.dws.DwsFlowidBinIndex;
import com.guwave.datahub.bpms.dao.clickhouse.dto.AccuracyLotWaferDetailDTO;
import com.guwave.datahub.bpms.dao.clickhouse.dto.AccuracyLotWaferFileDetailDTO;
import com.guwave.datahub.bpms.dao.clickhouse.dto.DataAccuracyBatchMatchDTO;
import com.guwave.datahub.bpms.dao.clickhouse.dto.DataAccuracyBatchYieldDTO;
import com.guwave.datahub.bpms.dao.repository.clickhouse.dwd.mapper.DwdDieDetailMapper;
import com.guwave.datahub.bpms.dao.repository.clickhouse.dws.mapper.DwsFlowidBinIndexMapper;
import com.guwave.datahub.bpms.dao.repository.clickhouse.dws.mapper.DwsLotWaferDataCheckIndexMapper;
import com.guwave.datahub.bpms.dao.repository.dw.mapper.FileWarehousingRecordMapper;
import com.guwave.datahub.bpms.dao.vo.dw.WaferVo;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.TestArea;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_FORMAT;

/**
 * 2025/4/27 19:27
 * DataAccuracyService
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DataAccuracyService {

    @Autowired
    private FileWarehousingRecordMapper fileWarehousingRecordMapper;
    @Autowired
    private DwdDieDetailMapper dwdDieDetailMapper;
    @Autowired
    private DwsFlowidBinIndexMapper dwsFlowidBinIndexMapper;
    @Autowired
    private DwsLotWaferDataCheckIndexMapper dwsLotWaferDataCheckIndexMapper;

    private final List<TestArea> SUPPORT_TEST_AREA = Stream.concat(TestArea.getCPList().stream(), TestArea.getFTList().stream())
            .collect(Collectors.toList());
    private final List<FileCategory> TEST_RAW_DATA_SUPPORT_FILE_CATEGORY = Arrays.asList(FileCategory.RAW_DATA, FileCategory.STDF);

    public List<DataAccuracyDropDownVo> dataAccuracyFilter(DataAccuracyDropDownFilterVo dataAccuracyDropDownFilterVo) {
        DataAccuracyPageDropdownField filterField = dataAccuracyDropDownFilterVo.getFilterField();
        log.info("获取{}列表", filterField);

        List<String> dropDownResult;

        if (filterField == null) {
            // 抛出异常处理未支持的过滤字段类型
            log.info("不支持的过滤字段：{}", filterField);
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, filterField);
        } else {
            if (DataAccuracyPageDropdownField.TEST_AREA == filterField) {
                // testArea 常量
                return SUPPORT_TEST_AREA.stream().map(testArea -> DataAccuracyDropDownVo.of(testArea.getArea())).collect(Collectors.toList());
            } else {
                if (dataAccuracyDropDownFilterVo.getTestArea() == null) {
                    throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "testArea不能为空");
                }
                boolean isCp = TestArea.getCPList().contains(TestArea.of(dataAccuracyDropDownFilterVo.getTestArea()));
                log.info("查询准确性下拉框结果, isCp:{}", isCp);
                // 查询入库完成的批次信息
                List<WaferVo> waferVoList = fileWarehousingRecordMapper.find7100SuccessFileWarehousingByFilters(
                        RequestContext.getCustomer(),
                        dataAccuracyDropDownFilterVo.getSubCustomerList(),
                        dataAccuracyDropDownFilterVo.getFactoryList(),
                        Collections.singletonList(dataAccuracyDropDownFilterVo.getTestArea()),
                        dataAccuracyDropDownFilterVo.getDeviceIdList(),
                        dataAccuracyDropDownFilterVo.getTestStageList(),
                        dataAccuracyDropDownFilterVo.getLotIdList(),
                        isCp ? dataAccuracyDropDownFilterVo.getWaferNoList() : null,
                        isCp ? null : dataAccuracyDropDownFilterVo.getSblotIdList(),
                        dataAccuracyDropDownFilterVo.getLotTypeList(),
                        LocalDateTimeUtil.format(dataAccuracyDropDownFilterVo.getStartTime(), NORM_DATETIME_FORMAT.getDateTimeFormatter()),
                        LocalDateTimeUtil.format(dataAccuracyDropDownFilterVo.getEndTime(), NORM_DATETIME_FORMAT.getDateTimeFormatter()),
                        TEST_RAW_DATA_SUPPORT_FILE_CATEGORY.stream().map(FileCategory::getCategory).collect(Collectors.toList())
                );
                log.info("查询入库完成的批次数：{}", waferVoList.size());

                if (CollectionUtils.isEmpty(waferVoList)) {
                    log.info("查询入库完成的批次数为0");
                    return Collections.emptyList();
                }
                // 填充批次信息
                DataAccuracyDropDownFilterVo.fillWafer(dataAccuracyDropDownFilterVo, isCp, waferVoList);

                dropDownResult = dwsLotWaferDataCheckIndexMapper.findDynamicField(
                        RequestContext.getCustomer(),
                        filterField.getField(),
                        dataAccuracyDropDownFilterVo.getSubCustomerList(),
                        dataAccuracyDropDownFilterVo.getFactoryList(),
                        Collections.singletonList(dataAccuracyDropDownFilterVo.getTestArea()),
                        dataAccuracyDropDownFilterVo.getDeviceIdList(),
                        dataAccuracyDropDownFilterVo.getTestStageList(),
                        dataAccuracyDropDownFilterVo.getLotIdList(),
                        dataAccuracyDropDownFilterVo.getWaferNoList(),
                        dataAccuracyDropDownFilterVo.getSblotIdList(),
                        dataAccuracyDropDownFilterVo.getLotTypeList(),
                        dataAccuracyDropDownFilterVo.getMesMatchFlagList(),
                        dataAccuracyDropDownFilterVo.getDataAccuracyCheckList(),
                        isCp);
            }

        }

        dropDownResult = dropDownResult.stream()
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        log.info("下拉框结果 field:{}, value:{}", filterField, String.join(Constant.COMMA, dropDownResult));

        if (DataAccuracyPageDropdownField.DATA_ACCURACY_CHECK.equals(filterField)) {
            return dropDownResult.stream().map(DataAccuracyCheckHelper::of)
                    .sorted(new DataAccuracyCheckHelper.DataAccuracyCheckComparator())
                    .map(val -> DataAccuracyDropDownVo.of(val.getShowName(), val.getFieldValue())).collect(Collectors.toList());
        } else if (DataAccuracyPageDropdownField.MES_MATCH_FLAG.equals(filterField)) {
            return dropDownResult.stream().map(MesMatchTypeEnum::of)
                    .sorted(new MesMatchTypeEnum.MesMatchTypeComparator())
                    .map(val -> DataAccuracyDropDownVo.of(val.getShowName(), val.getType()))
                    .collect(Collectors.toList());
        }

        return DataAccuracyDropDownVo.ofList(dropDownResult);
    }

    public DataAccuracyBatchMatchDetailVo accuracyBase(DataAccuracyBatchMatchQueryVo dataAccuracyBatchMatchQueryVo) {
        boolean isCp = TestArea.getCPList().contains(TestArea.of(dataAccuracyBatchMatchQueryVo.getTestArea()));

        DataAccuracyBatchMatchQueryVo dataAccuracyQueryVoWithLotWafer = fillDataAccuracyBatchMatchQueryVo(dataAccuracyBatchMatchQueryVo);

        List<DataAccuracyBatchMatchDTO> dataAccuracyBatchMatchDTOList = new ArrayList<>();
        if (dataAccuracyQueryVoWithLotWafer == null) {
            log.info("没有查询到入库完成的批次信息");
        } else {
            dataAccuracyBatchMatchDTOList = dwsLotWaferDataCheckIndexMapper.findDataAccuracyBatchMatchDTO(
                    RequestContext.getCustomer(),
                    dataAccuracyBatchMatchQueryVo.getSubCustomerList(),
                    dataAccuracyBatchMatchQueryVo.getFactoryList(),
                    Collections.singletonList(dataAccuracyBatchMatchQueryVo.getTestArea()),
                    dataAccuracyBatchMatchQueryVo.getDeviceIdList(),
                    dataAccuracyBatchMatchQueryVo.getTestStageList(),
                    dataAccuracyBatchMatchQueryVo.getLotIdList(),
                    dataAccuracyBatchMatchQueryVo.getWaferNoList(),
                    dataAccuracyBatchMatchQueryVo.getSblotIdList(),
                    dataAccuracyBatchMatchQueryVo.getLotTypeList(),
                    dataAccuracyBatchMatchQueryVo.getMesMatchFlagList(),
                    dataAccuracyBatchMatchQueryVo.getDataAccuracyCheckList(),
                    isCp);
        }
        return calculateDataAccuracyBatchMatch(dataAccuracyBatchMatchDTOList);
    }


    public PageableDataVo<DataAccuracyBatchYieldDetailVo> accuracy(DataAccuracyQueryVo dataAccuracyQueryVo) {
        Pageable pageable = PageRequest.of(dataAccuracyQueryVo.getJpaPageIndex(), dataAccuracyQueryVo.getPageSize());

        Page<DataAccuracyBatchYieldDTO> dataAccuracyBatchMatchDTOPage = getDataAccuracyBatchMatchDTOPage(pageable, dataAccuracyQueryVo);

        return PageableDataVo.of(dataAccuracyBatchMatchDTOPage.map(DataAccuracyBatchYieldDetailVo::of));
    }

    public DataAccuracyDetailResVo findAccuracyFileDetail(DataAccuracyDetailQueryVo accuracyDetailQueryVO) {
        boolean isCp = TestArea.getCPList().contains(TestArea.of(accuracyDetailQueryVO.getTestArea()));
        if (isCp && StringUtils.isBlank(accuracyDetailQueryVO.getWaferNo())) {
            throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "CP阶段waferNo不能为空");
        }
        List<AccuracyLotWaferDetailDTO> accuracyLotWaferDetailDTOList = dwsLotWaferDataCheckIndexMapper.findLotWaferDetail(
                RequestContext.getCustomer(),
                null,
                null,
                accuracyDetailQueryVO.getTestArea(),
                accuracyDetailQueryVO.getDeviceId(),
                accuracyDetailQueryVO.getTestStage(),
                accuracyDetailQueryVO.getLotId(),
                accuracyDetailQueryVO.getWaferNo(),
                null,
                isCp
        );

        List<AccuracyLotWaferFileDetailDTO> accuracyLotWaferFileDetailDTOList = dwsFlowidBinIndexMapper.findLotWaferFileDetail(
                        RequestContext.getCustomer(),
                        null,
                        null,
                        accuracyDetailQueryVO.getTestArea(),
                        accuracyDetailQueryVO.getDeviceId(),
                        accuracyDetailQueryVO.getTestStage(),
                        accuracyDetailQueryVO.getLotId(),
                        accuracyDetailQueryVO.getWaferNo(),
                        null)
                .stream()
                .peek(val -> val.setRetestHardBin(FileRepairRecordVo.getRetestHardBinNumShowName(val.getRetestHardBin(), val.getFlowId())))
                .collect(Collectors.toList());

        // 聚合文件信息，便于求每个维度文件数量
        Map<String, List<AccuracyLotWaferFileDetailDTO>> fileInfoMap = MapUtils.emptyIfNull(accuracyLotWaferFileDetailDTOList.stream().collect(Collectors.groupingBy(AccuracyLotWaferFileDetailDTO::getQueryKey)));

        accuracyLotWaferDetailDTOList.forEach(val -> {
            List<AccuracyLotWaferFileDetailDTO> fileDetailList = fileInfoMap.getOrDefault(val.getQueryKey(), Collections.emptyList());
            val.setStdFileCount((long) fileInfoMap.getOrDefault(val.getQueryKey(), Collections.emptyList()).size());
            // 产品要求都支持修复
            Boolean supportDataRepair = true;
            // 良率要化成百分数
            val.setFinalYield(BigDecimalUtil.bigDecimalToPercentStr(new BigDecimal(val.getFinalYield()), true));
            val.setFirstYield(BigDecimalUtil.bigDecimalToPercentStr(new BigDecimal(val.getFirstYield()), true));
            if (StringUtils.isNoneBlank(val.getSummaryYield())) {
                val.setSummaryYield(BigDecimalUtil.bigDecimalToPercentStr(new BigDecimal(val.getSummaryYield()), true));
            }
            fileDetailList.forEach(file -> file.setSupportDataRepair(supportDataRepair));
            val.setAccuracyCheckType(val.getAccuracyCheck());
            val.setAccuracyCheck(DataAccuracyCheckHelper.getShowName(val.getAccuracyCheck()));
            val.setMatchFlag(MesMatchTypeEnum.of(val.getMatchFlag()).getShowName());
        });
        return DataAccuracyDetailResVo.builder()
                .detailList(accuracyLotWaferDetailDTOList)
                .fileList(accuracyLotWaferFileDetailDTOList)
                .build();
    }

    public void exportCsv(DataAccuracyExportDataQueryVo dataAccuracyExportDataQueryVo, HttpServletResponse response) {
        response.setCharacterEncoding("gb2312");
        response.setContentType("text/csv;charset=gb2312");

        int pageSize = 10000;

        boolean isCp = TestArea.getCPList().contains(TestArea.of(dataAccuracyExportDataQueryVo.getTestArea()));
        List<String> excludeColumns = isCp ? Arrays.asList("lotId", "waferNo") : Arrays.asList("waferId", "waferNo");
        DataAccuracyQueryVo dataAccuracyQueryVoWithLotWafer = fillDataAccuracyQueryVo(DataAccuracyQueryVo.of(dataAccuracyExportDataQueryVo));

        String fileName = "数据准确性指标.xlsx";
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            ExcelUtil.exportDataWithDefaultStyle(outputStream, getFunction(dataAccuracyQueryVoWithLotWafer, pageSize), dataAccuracyQueryVoWithLotWafer.getJpaPageIndex(), DataAccuracyBatchYieldDetailVo.class, excludeColumns);

            response.setHeader("Content-Disposition", String.format("attachment;filename:%s", new String(fileName.getBytes("gb2312"))));
            log.info("导出csv完成");
        } catch (Exception e) {
            log.error("导出csv异常", e);
        }
    }

    public void downloadGoodRetestBins(DownloadGoodRetestDieReqVo downloadGoodRetestDieReqVo, HttpServletResponse response) {
        log.info("下载好品复测的bin文件开始, 参数: {}", downloadGoodRetestDieReqVo);
        boolean isCp = TestArea.getCPList().contains(TestArea.of(downloadGoodRetestDieReqVo.getTestArea()));
        checkGoodRetestDownloadParam(downloadGoodRetestDieReqVo, isCp);

        List<GoodRetestBinExportDTO> goodRetestBinExportDTOList = dwdDieDetailMapper.selectGoodRetestBins(
                downloadGoodRetestDieReqVo.getDeviceId(),
                downloadGoodRetestDieReqVo.getTestArea(),
                downloadGoodRetestDieReqVo.getTestStage(),
                downloadGoodRetestDieReqVo.getLotId(),
                downloadGoodRetestDieReqVo.getWaferNo(),
                downloadGoodRetestDieReqVo.getSblotId(),
                downloadGoodRetestDieReqVo.getTestProgram(),
                downloadGoodRetestDieReqVo.getLotType()
        ).stream().map(GoodRetestBinExportDTO::of).collect(Collectors.toList());

        List<String> excludeColumns = getGoodRetestBinExcludeColumns(isCp, downloadGoodRetestDieReqVo.getDownloadType());

        String fileName = getExcelFileName(downloadGoodRetestDieReqVo, isCp);
        response.addHeader("Content-Disposition", "attachment;filename=" + fileName);
        response.setContentType("application/octet-stream");

        try (ServletOutputStream outputStream = response.getOutputStream()) {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            ExcelUtil.exportDataWithDefaultStyle(outputStream, goodRetestBinExportDTOList, GoodRetestBinExportDTO.class, excludeColumns);
            log.info("好品复测文件下载完成: {}", fileName);
        } catch (IOException e) {
            log.error("好品复测文件下载失败: ", e);
        }
    }

    public void downloadOnlineRetest(DownloadOnlineRetestDieReqVo downloadOnlineRetestBinReqVo, HttpServletResponse response) {
        log.info("下载片内复测的bin文件开始, 参数: {}", downloadOnlineRetestBinReqVo);

        List<OnlineRetestBinExportDTO> exportData = new ArrayList<>();

        DwsFlowidBinIndex onlineRetestBins = dwsFlowidBinIndexMapper.selectOnlineRetestBins(
                downloadOnlineRetestBinReqVo.getFileId(),
                downloadOnlineRetestBinReqVo.getDeviceId(),
                downloadOnlineRetestBinReqVo.getTestStage(),
                downloadOnlineRetestBinReqVo.getLotId(),
                downloadOnlineRetestBinReqVo.getSblotId()
        );
        if (Objects.isNull(onlineRetestBins)) {
            log.info("片内复测数据为空");
        } else {
            List<String> ecidList = StringUtils.isBlank(onlineRetestBins.getOnlineRetestDies()) ? Collections.emptyList() : Arrays.stream(onlineRetestBins.getOnlineRetestDies().split(StrUtil.COMMA)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ecidList)) {
                exportData = dwdDieDetailMapper.selectOnlineRetestDies(
                                downloadOnlineRetestBinReqVo.getDeviceId(),
                                downloadOnlineRetestBinReqVo.getTestStage(),
                                downloadOnlineRetestBinReqVo.getLotId(),
                                downloadOnlineRetestBinReqVo.getSblotId(),
                                ecidList)
                        .stream()
                        .map(OnlineRetestBinExportDTO::of)
                        .collect(Collectors.toList());
            }
            log.info("片内复测数据条数：{}", exportData.size());
        }

        String fileName = String.format("%s-%s-%s.xlsx", downloadOnlineRetestBinReqVo.getDeviceId(), downloadOnlineRetestBinReqVo.getTestStage(), downloadOnlineRetestBinReqVo.getLotId());
        response.addHeader("Content-Disposition", "attachment;filename=" + fileName);
        response.setContentType("application/octet-stream");

        try (ServletOutputStream outputStream = response.getOutputStream()) {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            ExcelUtil.exportDataWithDefaultStyle(outputStream, exportData, OnlineRetestBinExportDTO.class, Collections.singletonList("onlineRetestDies"));
            log.info("片内复测文件下载完成: {}", fileName);
        } catch (IOException e) {
            log.error("片内复测文件下载失败: ", e);
        }
    }


    private DataAccuracyBatchMatchQueryVo fillDataAccuracyBatchMatchQueryVo(DataAccuracyBatchMatchQueryVo dataAccuracyBatchMatchQueryVo) {
        boolean isCp = TestArea.getCPList().contains(TestArea.of(dataAccuracyBatchMatchQueryVo.getTestArea()));

        // 查询入库完成的批次信息
        List<WaferVo> waferVoList = fileWarehousingRecordMapper.find7100SuccessFileWarehousingByFilters(
                RequestContext.getCustomer(),
                dataAccuracyBatchMatchQueryVo.getSubCustomerList(),
                dataAccuracyBatchMatchQueryVo.getFactoryList(),
                Collections.singletonList(dataAccuracyBatchMatchQueryVo.getTestArea()),
                dataAccuracyBatchMatchQueryVo.getDeviceIdList(),
                dataAccuracyBatchMatchQueryVo.getTestStageList(),
                dataAccuracyBatchMatchQueryVo.getLotIdList(),
                isCp ? dataAccuracyBatchMatchQueryVo.getWaferNoList() : null,
                isCp ? null : dataAccuracyBatchMatchQueryVo.getSblotIdList(),
                dataAccuracyBatchMatchQueryVo.getLotTypeList(),
                LocalDateTimeUtil.format(dataAccuracyBatchMatchQueryVo.getStartTime(), NORM_DATETIME_FORMAT.getDateTimeFormatter()),
                LocalDateTimeUtil.format(dataAccuracyBatchMatchQueryVo.getEndTime(), NORM_DATETIME_FORMAT.getDateTimeFormatter()),
                TEST_RAW_DATA_SUPPORT_FILE_CATEGORY.stream().map(FileCategory::getCategory).collect(Collectors.toList())
        );
        log.info("查询入库完成的批次数：{}", waferVoList.size());

        if (CollectionUtils.isEmpty(waferVoList)) {
            log.info("没有查询到入库完成的批次信息");
            return null;
        } else {
            // 填充批次信息
            DataAccuracyBatchMatchQueryVo.fillWafer(dataAccuracyBatchMatchQueryVo, isCp, waferVoList);

            return dataAccuracyBatchMatchQueryVo;
        }
    }

    private DataAccuracyQueryVo fillDataAccuracyQueryVo(DataAccuracyQueryVo dataAccuracyQueryVo) {
        boolean isCp = TestArea.getCPList().contains(TestArea.of(dataAccuracyQueryVo.getTestArea()));

        // 查询入库完成的批次信息
        List<WaferVo> waferVoList = fileWarehousingRecordMapper.find7100SuccessFileWarehousingByFilters(
                RequestContext.getCustomer(),
                dataAccuracyQueryVo.getSubCustomerList(),
                dataAccuracyQueryVo.getFactoryList(),
                Collections.singletonList(dataAccuracyQueryVo.getTestArea()),
                dataAccuracyQueryVo.getDeviceIdList(),
                dataAccuracyQueryVo.getTestStageList(),
                dataAccuracyQueryVo.getLotIdList(),
                isCp ? dataAccuracyQueryVo.getWaferNoList() : null,
                isCp ? null : dataAccuracyQueryVo.getSblotIdList(),
                dataAccuracyQueryVo.getLotTypeList(),
                LocalDateTimeUtil.format(dataAccuracyQueryVo.getStartTime(), NORM_DATETIME_FORMAT.getDateTimeFormatter()),
                LocalDateTimeUtil.format(dataAccuracyQueryVo.getEndTime(), NORM_DATETIME_FORMAT.getDateTimeFormatter()),
                TEST_RAW_DATA_SUPPORT_FILE_CATEGORY.stream().map(FileCategory::getCategory).collect(Collectors.toList())
        );
        log.info("查询入库完成的批次数：{}", waferVoList.size());

        if (CollectionUtils.isEmpty(waferVoList)) {
            log.info("没有查询到入库完成的批次信息");
            return null;
        } else {
            // 填充批次信息
            DataAccuracyQueryVo.fillWafer(dataAccuracyQueryVo, isCp, waferVoList);

            return dataAccuracyQueryVo;
        }
    }

    private Page<DataAccuracyBatchYieldDTO> getDataAccuracyBatchMatchDTOPage(Pageable pageable, DataAccuracyQueryVo dataAccuracyQueryVo) {
        boolean isCp = TestArea.getCPList().contains(TestArea.of(dataAccuracyQueryVo.getTestArea()));

        DataAccuracyQueryVo dataAccuracyQueryVoWithLotWafer = fillDataAccuracyQueryVo(dataAccuracyQueryVo);

        Page<DataAccuracyBatchYieldDTO> dataAccuracyBatchMatchDTOPage = new PageImpl<>(new ArrayList<>(), pageable, 0);
        if (dataAccuracyQueryVoWithLotWafer == null) {
            log.info("没有查询到入库完成的批次信息");
        } else {
            Long totalCnt = dwsLotWaferDataCheckIndexMapper.countDataAccuracyBatchYieldDTO(
                    RequestContext.getCustomer(),
                    dataAccuracyQueryVo.getSubCustomerList(),
                    dataAccuracyQueryVo.getFactoryList(),
                    Collections.singletonList(dataAccuracyQueryVo.getTestArea()),
                    dataAccuracyQueryVo.getDeviceIdList(),
                    dataAccuracyQueryVo.getTestStageList(),
                    dataAccuracyQueryVo.getLotIdList(),
                    dataAccuracyQueryVo.getWaferNoList(),
                    dataAccuracyQueryVo.getSblotIdList(),
                    dataAccuracyQueryVo.getLotTypeList(),
                    dataAccuracyQueryVo.getMesMatchFlagList(),
                    dataAccuracyQueryVo.getDataAccuracyCheckList(),
                    isCp
            );

            if (Objects.equals(dataAccuracyQueryVo.getSortField(), SortPageableRequestVo.DEFAULT_SORT_FIELD)) {
                dataAccuracyQueryVo.setSortField(null);
                dataAccuracyQueryVo.setSortDirection(null);
            }
            List<DataAccuracyBatchYieldDTO> dataAccuracyBatchYieldDTOList =  dwsLotWaferDataCheckIndexMapper.findDataAccuracyBatchYieldDTOPage(
                    RequestContext.getCustomer(),
                    dataAccuracyQueryVo.getSubCustomerList(),
                    dataAccuracyQueryVo.getFactoryList(),
                    Collections.singletonList(dataAccuracyQueryVo.getTestArea()),
                    dataAccuracyQueryVo.getDeviceIdList(),
                    dataAccuracyQueryVo.getTestStageList(),
                    dataAccuracyQueryVo.getLotIdList(),
                    dataAccuracyQueryVo.getWaferNoList(),
                    dataAccuracyQueryVo.getSblotIdList(),
                    dataAccuracyQueryVo.getLotTypeList(),
                    dataAccuracyQueryVo.getMesMatchFlagList(),
                    dataAccuracyQueryVo.getDataAccuracyCheckList(),
                    isCp,
                    dataAccuracyQueryVo.getSortField(),
                    dataAccuracyQueryVo.getSortDirection(),
                    pageable.getOffset(),
                    pageable.getPageSize()
            );
            dataAccuracyBatchMatchDTOPage = new PageImpl<>(dataAccuracyBatchYieldDTOList, pageable, totalCnt);
        }
        return dataAccuracyBatchMatchDTOPage;
    }

    private Function<Integer, List<?>> getFunction(DataAccuracyQueryVo dataAccuracyQueryVo, Integer pageSize) {
        return (pageIndex) -> {
            Pageable pageable = PageRequest.of(pageIndex, pageSize);
            return dwsLotWaferDataCheckIndexMapper.findDataAccuracyBatchYieldDTOPage(
                            RequestContext.getCustomer(),
                            dataAccuracyQueryVo.getSubCustomerList(),
                            dataAccuracyQueryVo.getFactoryList(),
                            Collections.singletonList(dataAccuracyQueryVo.getTestArea()),
                            dataAccuracyQueryVo.getDeviceIdList(),
                            dataAccuracyQueryVo.getTestStageList(),
                            dataAccuracyQueryVo.getLotIdList(),
                            dataAccuracyQueryVo.getWaferNoList(),
                            dataAccuracyQueryVo.getSblotIdList(),
                            dataAccuracyQueryVo.getLotTypeList(),
                            dataAccuracyQueryVo.getMesMatchFlagList(),
                            dataAccuracyQueryVo.getDataAccuracyCheckList(),
                            TestArea.getCPList().contains(TestArea.of(dataAccuracyQueryVo.getTestArea())),
                            null,
                            null,
                            pageable.getOffset(),
                            pageable.getPageSize())
                    .stream()
                    .map(DataAccuracyBatchYieldDetailVo::of)
                    .collect(Collectors.toList());
        };
    }


    private DataAccuracyBatchMatchDetailVo calculateDataAccuracyBatchMatch(List<DataAccuracyBatchMatchDTO> dataAccuracyDTOList) {
        DataAccuracyBatchMatchDetailVo dataAccuracyBatchMatchDetailVo = new DataAccuracyBatchMatchDetailVo();

        double yieldAccuracy = 0;
        double normalRate = 0;
        double matchRate = 0;
        double mesInputRate = 0;
        List<DataAccuracyBatchMatchDetailVo.PieItem> dataAccuracyCheckPieItems = new ArrayList<>();
        List<DataAccuracyBatchMatchDetailVo.PieItem> mesMatchTypePieItems = new ArrayList<>();

        if (!CollectionUtils.isEmpty(dataAccuracyDTOList)) {
            long totalCnt = dataAccuracyDTOList.size();

            long normalAndMatchCount = dataAccuracyDTOList.stream()
                    .filter(val -> DataAccuracyCheckEnum.isNormal(val.getDataAccuracyCheck()) && MesMatchTypeEnum.isMatch(val.getMesMatchType()))
                    .count();
            yieldAccuracy = normalAndMatchCount * 1.0 / totalCnt;
            log.info("计算批次良率准确率计算完成，normalAndMatchCount:{}, sum:{}, rate:{}", normalAndMatchCount, totalCnt, yieldAccuracy);

            long normalCount = dataAccuracyDTOList.stream()
                    .filter(val -> DataAccuracyCheckEnum.isNormal(val.getDataAccuracyCheck()))
                    .count();
            normalRate = normalCount * 1.0 / totalCnt;
            log.info("正常数据比例计算完成，passCount:{}, sum:{}, rate:{}", normalCount, totalCnt, normalRate);

            long matchCount = dataAccuracyDTOList.stream()
                    .filter(val -> MesMatchTypeEnum.MATCH.getType().equals(val.getMesMatchType()))
                    .count();
            matchRate = matchCount * 1.0;
            log.info("匹配率计算完成，matchCount:{}, sum:{}, rate:{}", matchCount, totalCnt, matchRate);

            long mesLossCount = dataAccuracyDTOList.stream()
                    .filter(val -> MesMatchTypeEnum.MES_DATA_LOSS.getType().equals(val.getMesMatchType()))
                    .count();
            mesInputRate = (totalCnt - mesLossCount) * 1.0 / totalCnt;

            log.info("开始计算批次异常良率分布图");
            Map<DataAccuracyCheckHelper, List<DataAccuracyBatchMatchDTO>> mapByAccuracyCheck = dataAccuracyDTOList.stream()
                    .collect(Collectors.groupingBy(val -> DataAccuracyCheckHelper.of(val.getDataAccuracyCheck())));
            dataAccuracyCheckPieItems = mapByAccuracyCheck.keySet()
                    .stream()
                    .sorted(new DataAccuracyCheckHelper.DataAccuracyCheckComparator())
                    .map(helper -> DataAccuracyBatchMatchDetailVo.PieItem.builder()
                            .category(helper.getShowName())
                            .rate(totalCnt == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(mapByAccuracyCheck.get(helper).size() * 1.0 / totalCnt))
                            .build())
                    .collect(Collectors.toList());

            log.info("测试文件数据流与MES数据流数据一致性");
            Map<MesMatchTypeEnum, List<DataAccuracyBatchMatchDTO>> mapByMatchType = dataAccuracyDTOList.stream().collect(Collectors.groupingBy(val ->
                    MesMatchTypeEnum.of(val.getMesMatchType())));
            mesMatchTypePieItems = mapByMatchType.keySet()
                    .stream()
                    .sorted(new MesMatchTypeEnum.MesMatchTypeComparator())
                    .map(matchType -> DataAccuracyBatchMatchDetailVo.PieItem.builder()
                            .category(matchType.getShowName())
                            .rate(totalCnt == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(mapByMatchType.get(matchType).size() * 1.0 / totalCnt).setScale(4, RoundingMode.HALF_UP)).build())
                    .collect(Collectors.toList());
        }

        dataAccuracyBatchMatchDetailVo.setYieldAccuracy(BigDecimal.valueOf(yieldAccuracy).setScale(4, RoundingMode.HALF_UP));
        dataAccuracyBatchMatchDetailVo.setNormalRate(BigDecimal.valueOf(normalRate).setScale(4, RoundingMode.HALF_UP));
        dataAccuracyBatchMatchDetailVo.setMatchRate(BigDecimal.valueOf(matchRate).setScale(4, RoundingMode.HALF_UP));
        dataAccuracyBatchMatchDetailVo.setMesInputRate(BigDecimal.valueOf(mesInputRate).setScale(4, RoundingMode.HALF_UP));

        dataAccuracyBatchMatchDetailVo.setDataAccuracyCheckPieItems(dataAccuracyCheckPieItems);
        dataAccuracyBatchMatchDetailVo.setMesMatchTypePieItems(mesMatchTypePieItems);

        return dataAccuracyBatchMatchDetailVo;
    }

    private void checkGoodRetestDownloadParam(DownloadGoodRetestDieReqVo reqDTO, boolean cpFlag) {
        if (DownloadGoodRetestDieReqVo.DownloadTypeEnum.LIST.equals(reqDTO.getDownloadType())) {
            if (cpFlag) {
                if (StringUtils.isEmpty(reqDTO.getWaferNo())) {
                    throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "waferNo不能为空");
                }
            }
        } else {
            if (StringUtils.isEmpty(reqDTO.getTestProgram())) {
                throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "testProgram不能为空");
            }
            if (cpFlag) {
                if (StringUtils.isEmpty(reqDTO.getWaferNo())) {
                    throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "waferNo不能为空");
                }
            } else {
                if (StringUtils.isEmpty(reqDTO.getSblotId())) {
                    throw new BpmsException(ResponseCode.Common.COMMON_PARAM_INVALID, "sblotId不能为空");
                }
            }
        }
    }

    private List<String> getGoodRetestBinExcludeColumns(boolean cpFlag, DownloadGoodRetestDieReqVo.DownloadTypeEnum downloadType) {
        if (DownloadGoodRetestDieReqVo.DownloadTypeEnum.LIST.equals(downloadType)) {
            return cpFlag ? Arrays.asList("sblotId", "testProgram") : Arrays.asList("waferId", "sblotId", "testProgram");
        } else if (DownloadGoodRetestDieReqVo.DownloadTypeEnum.DETAIL.equals(downloadType)) {
            return cpFlag ? Collections.singletonList("sblotId") : Collections.singletonList("waferId");
        } else {
            return cpFlag ? Collections.singletonList("sblotId") : Collections.singletonList("waferId");
        }
    }

    private String getExcelFileName(DownloadGoodRetestDieReqVo reqDTO, boolean cpFlag) {
        if (DownloadGoodRetestDieReqVo.DownloadTypeEnum.LIST.equals(reqDTO.getDownloadType())) {
            if (cpFlag) {
                return String.format("%s-%s-%s-%s.xlsx", reqDTO.getDeviceId(), reqDTO.getTestStage(), reqDTO.getLotId(), reqDTO.getWaferNo());
            } else {
                return String.format("%s-%s-%s.xlsx", reqDTO.getDeviceId(), reqDTO.getTestStage(), reqDTO.getLotId());
            }
        } else if (DownloadGoodRetestDieReqVo.DownloadTypeEnum.DETAIL.equals(reqDTO.getDownloadType())) {
            String waferOrSblot = cpFlag ? reqDTO.getWaferNo() : reqDTO.getSblotId();
            return String.format("%s-%s-%s-%s-%s.xlsx", reqDTO.getDeviceId(), reqDTO.getTestStage(), reqDTO.getLotId(), waferOrSblot, reqDTO.getTestProgram());
        } else {
            String lotOrWafer = cpFlag ? reqDTO.getWaferNo() : reqDTO.getLotId();
            return String.format("%s-%s-%s.xlsx", reqDTO.getDeviceId(), reqDTO.getTestStage(), lotOrWafer);
        }
    }

}
