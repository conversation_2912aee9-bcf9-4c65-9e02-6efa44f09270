package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.response;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.guwave.datahub.bpms.app.converter.TestAreaConverter;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpSubCustomerRule;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_FORMAT;

/**
 * 2025/3/4 16:49
 * DwSftpSubCustomerRuleVo
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(title = "子客户目录规则")
public class DwSftpSubCustomerRuleVo {
    @Schema(title = "id")
    private Long id;

    @Schema(description = "客户名称", example = "AMD")
    private String customer;

    @Schema(description = "测试工厂名称", example = "TSMC")
    @ExcelProperty("factory")
    private String factory;

    @Schema(description = "测试子工厂", example = "TSMC")
    @ExcelProperty("factorySite")
    private String factorySite;

    @Schema(description = "测试阶段类型: CP/FT/WAT/SLT/EQC 等", example = "CP")
    @ExcelProperty("testArea")
    private String testArea;

    @Schema(description = "ftp服务ip", example = "************")
    @ExcelProperty("ftpIp")
    private String ftpIp;

    @Schema(description = "ftp目录", example = "/data1/stdf/")
    @ExcelProperty("directory")
    private String directory;

    @Schema(description = "子客户", example = "AMD")
    @ExcelProperty("subCustomer")
    private String subCustomer;

    @Schema(title = "createTime", description = "创建时间", example = "2024-11-15 11:21:00")
    private String createTime;

    @Schema(title = "updateTime", description = "更新时间", example = "2024-11-18 11:21:00")
    private String updateTime;

    @Schema(description = "创建用户", example = "System")
    private String createUser;

    @Schema(description = "更新用户", example = "System")
    private String updateUser;

    public static DwSftpSubCustomerRuleVo of(SftpSubCustomerRule data) {
        return new DwSftpSubCustomerRuleVoBuilder()
                .id(data.getId())
                .customer(data.getCustomer())
                .factory(data.getFactory())
                .factorySite(data.getFactorySite())
                .testArea(TestArea.of(data.getTestArea()))
                .ftpIp(data.getFtpIp())
                .directory(data.getDirectory())
                .subCustomer(data.getSubCustomer())
                .createTime(DateUtil.format(data.getCreateTime(), NORM_DATETIME_FORMAT))
                .updateTime(DateUtil.format(data.getUpdateTime(), NORM_DATETIME_FORMAT))
                .createUser(data.getCreateUser())
                .updateUser(data.getUpdateUser())
                .build();
    }
}
