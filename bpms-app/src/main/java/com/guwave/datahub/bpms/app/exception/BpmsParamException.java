package com.guwave.datahub.bpms.app.exception;

import com.guwave.datahub.bpms.app.web.response.ResponseCode;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * BpmsParamException
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-27 14:00:23
 */
public class BpmsParamException extends BpmsException {

    public BpmsParamException() {
        super(ResponseCode.Common.COMMON_PARAM_NOT_READABLE);
    }

    public BpmsParamException(Throwable e) {
        super(e, ResponseCode.Common.COMMON_PARAM_NOT_READABLE);
    }

    public BpmsParamException(String param) {
        super(ResponseCode.Common.COMMON_PARAM_INVALID, param);
    }

    public BpmsParamException(Throwable e, String param) {
        super(e, ResponseCode.Common.COMMON_PARAM_INVALID, param);
    }
}
