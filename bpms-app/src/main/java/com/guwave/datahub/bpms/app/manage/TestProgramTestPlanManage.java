package com.guwave.datahub.bpms.app.manage;

import com.guwave.datahub.bpms.app.vo.bz.testProgramTestPlan.ImportTestPlanDimension;
import com.guwave.datahub.bpms.dao.repository.dw.TestProgramTestPlanRepository;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.TestProgramTestPlan;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Component
public class TestProgramTestPlanManage {

    @Autowired
    private TestProgramTestPlanRepository testProgramTestPlanRepository;

    @Transactional(rollbackFor = Exception.class)
    public void modifyPlan(ImportTestPlanDimension saveDimension, List<Long> deleteIds, List<TestProgramTestPlan> updateTestProgramTestPlans, List<TestProgramTestPlan> insertTestProgramTestPlans) {
        log.info("维度：{} ，删除数据{}条，更新数据{}条，新增数据{}条 开始", saveDimension, deleteIds.size(), updateTestProgramTestPlans.size(), insertTestProgramTestPlans.size());
        if (CollectionUtils.isNotEmpty(deleteIds)) {
            testProgramTestPlanRepository.deleteAllById(deleteIds);
        }
        if (CollectionUtils.isNotEmpty(updateTestProgramTestPlans)) {
            testProgramTestPlanRepository.saveAll(updateTestProgramTestPlans);
        }
        if (CollectionUtils.isNotEmpty(insertTestProgramTestPlans)) {
            testProgramTestPlanRepository.saveAll(insertTestProgramTestPlans);
        }
        log.info("维度：{} ，删除数据{}条，更新数据{}条，新增数据{}条 结束", saveDimension, deleteIds.size(), updateTestProgramTestPlans.size(), insertTestProgramTestPlans.size());
    }
}
