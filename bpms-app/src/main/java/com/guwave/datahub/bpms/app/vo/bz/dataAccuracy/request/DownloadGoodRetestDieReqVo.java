package com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 2025/4/27 16:18
 * DownloadGoodRetestDieReqVo
 *
 * <AUTHOR>
 */
@Schema(title = "好品复测Die下载参数")
@Data
@EqualsAndHashCode(callSuper = false)
public class DownloadGoodRetestDieReqVo {

    @Schema(description = "deviceId", example = "deviceId-1")
    @NotNull(message = "deviceId不能为空")
    private String deviceId;

    @Schema(description = "testArea", example = "CP")
    @NotNull(message = "testArea不能为空")
    private String testArea;

    @Schema(description = "testStage", example = "CP1")
    @NotNull(message = "testStage不能为空")
    private String testStage;

    @Schema(description = "lotId", example = "lotId-1")
    @NotNull(message = "lotId不能为空")
    private String lotId;

    @Schema(description = "lotType", example = "PRODUCTION")
    @NotNull(message = "lotType不能为空")
    private String lotType;

    @Schema(description = "waferNo", example = "12")
    private String waferNo;

    @Schema(description = "sblotId", example = "sblotId-1")
    private String sblotId;

    @Schema(description = "testProgram", example = "testProgram-1")
    private String testProgram;


    /**
     * LIST, DETAIL
     */
    @Schema(description = "downloadType", required = true, allowableValues = "列表页：LIST, 详情页：DETAIL")
    @NotNull(message = "downloadType不能为空")
    private DownloadTypeEnum downloadType;


    public enum DownloadTypeEnum {
        LIST,
        DETAIL
    }

}
