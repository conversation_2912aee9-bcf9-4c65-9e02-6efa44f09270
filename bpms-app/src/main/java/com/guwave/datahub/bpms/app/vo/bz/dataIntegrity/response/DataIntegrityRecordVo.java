package com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.response;


import com.guwave.datahub.bpms.dao.vo.bpms.dataIntegrity.DataIntegrityRecord;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(title = "数据完整性记录")
public class DataIntegrityRecordVo {

    private Long id;
    private String fileName;
    private String fileLocation;
    private String dataLoaded;
    private String dataLoadingFailedStep;
    private String loadingFailedType;
    private String originalFailedType;
    private String updateTime;
    private String factory;
    private String deviceName;
    private String testStage;
    private String lotId;
    private String sblotId;
    private String testProgram;
    private String waferId;
    private String waferNo;
    private String lotType;
    private String fileCategory;
    private String testArea;
    private String subCustomer;
    private String startTime;
    private String endTime;
    private String fileLabel;
    private String remark;
    private String failedFields;
    private Boolean enableDownload;
    private Boolean deleteFlag;
    private Boolean reRunFlag;
    private Boolean repairFlag;

    public static DataIntegrityRecordVo of(DataIntegrityRecord record) {
        return new DataIntegrityRecordVo(
                record.getId(),
                record.getFileName(),
                record.getFileLocation(),
                record.getDataLoaded(),
                record.getDataLoadingFailedStep(),
                record.getLoadingFailedType(),
                record.getOriginalFailedType(),
                record.getUpdateTime(),
                record.getFactory(),
                record.getDeviceName(),
                record.getTestStage(),
                record.getLotId(),
                record.getSblotId(),
                record.getTestProgram(),
                record.getWaferId(),
                record.getWaferNo(),
                record.getLotType(),
                record.getFileCategory(),
                record.getTestArea(),
                record.getSubCustomer(),
                record.getStartTime(),
                record.getEndTime(),
                record.getFileLabel(),
                record.getRemark(),
                record.getFailedFields(),
                convertIntToBool(record.getEnableDownload()),
                convertIntToBool(record.getDeleteFlag()),
                convertIntToBool(record.getReRunFlag()),
                convertIntToBool(record.getRepairFlag())
        );
    }

    private static Boolean convertIntToBool(Integer i) {
        return i != null && i == 1;
    }
}
