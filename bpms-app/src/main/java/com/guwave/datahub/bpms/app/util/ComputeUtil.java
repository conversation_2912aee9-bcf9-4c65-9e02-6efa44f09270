package com.guwave.datahub.bpms.app.util;

import com.guwave.onedata.dataware.common.contant.CkSinkType;
import com.guwave.onedata.dataware.common.contant.ExceptionType;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;

import java.util.HashMap;
import java.util.Map;

public class ComputeUtil {

    public static final Map<com.guwave.onedata.next.compute.common.constant.ProcessStatus, ProcessStatus> PROCESS_STATUS_MAP = new HashMap<com.guwave.onedata.next.compute.common.constant.ProcessStatus, ProcessStatus>() {{
        put(com.guwave.onedata.next.compute.common.constant.ProcessStatus.SUCCESS, ProcessStatus.SUCCESS);
        put(com.guwave.onedata.next.compute.common.constant.ProcessStatus.FAIL, ProcessStatus.FAIL);
    }};

    public static ProcessStatus convertProcessStatus(com.guwave.onedata.next.compute.common.constant.ProcessStatus status) {
        ProcessStatus processStatus = PROCESS_STATUS_MAP.get(status);
        if (processStatus == null) {
            processStatus = ProcessStatus.PROCESSING;
        }
        return processStatus;
    }

    public static ExceptionType convertExceptionType(com.guwave.onedata.next.compute.common.constant.ExceptionType type) {
        ExceptionType exceptionType = null;
        if (type != null) {
            exceptionType = ExceptionType.DATAWARE_PROCESS_EXCEPTION;
        }
        return exceptionType;
    }

    public static CkSinkType convertCkSinkType(com.guwave.onedata.next.compute.common.constant.SinkType type) {
        CkSinkType sinkType = null;
        if (type != null) {
            sinkType = CkSinkType.of(type.getType());
        }
        return sinkType;
    }
}
