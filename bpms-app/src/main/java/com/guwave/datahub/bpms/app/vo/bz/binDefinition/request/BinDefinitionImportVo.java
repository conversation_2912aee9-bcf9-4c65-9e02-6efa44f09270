package com.guwave.datahub.bpms.app.vo.bz.binDefinition.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * Bin Definition导入VO
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@Schema(title = "Bin Definition导入请求")
public class BinDefinitionImportVo {

    @NotNull(message = "导入模式不能为空")
    @Schema(description = "导入模式：ADD_OR_OVERWRITE-新增或覆盖，CLEAR_AND_IMPORT-清除并全量导入", 
            example = "ADD_OR_OVERWRITE", required = true)
    private ImportMode importMode;

    /**
     * 导入模式枚举
     */
    public enum ImportMode {
        /**
         * 新增或覆盖
         */
        ADD_OR_OVERWRITE,
        
        /**
         * 清除并全量导入
         */
        CLEAR_AND_IMPORT
    }
}