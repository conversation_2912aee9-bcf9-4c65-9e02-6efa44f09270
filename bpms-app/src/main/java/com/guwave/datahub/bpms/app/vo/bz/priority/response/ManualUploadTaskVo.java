package com.guwave.datahub.bpms.app.vo.bz.priority.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "手动上传任务")
public class ManualUploadTaskVo extends TaskPriority {
    @Schema(description = "文件名")
    private String fileName;

    @Schema(description = "用户")
    private String user;

    @Schema(description = "文件大小")
    private Long fileSize;
}
