package com.guwave.datahub.bpms.app.job;

import com.guwave.datahub.bpms.app.service.bz.ResendMessageService;
import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
public class ResendMessageJob {

    @Autowired
    private ResendMessageService resendMessageService;

    @ScheduleSwitch
    @Scheduled(fixedDelayString = "${spring.scheduler.message.retry.millisecond}")
    public void run() {
        resendMessageService.resendMessage();
    }
}
