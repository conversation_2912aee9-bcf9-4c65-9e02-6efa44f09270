package com.guwave.datahub.bpms.app.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.guwave.datahub.bpms.common.constant.WarehousingRuleType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.BeanUtils;

import java.io.OutputStream;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
public class ExcelUtil {

    private static final String DEFAULT_SHEET_NAME_1 = "Sheet1";

    /**
     * 导出多个sheet，每个sheet数据类型可以不同
     * @param outputStream 输出流
     * @param sheetDataList List<SheetData> sheet数据列表
     */
    public static void exportMultipleSheets(OutputStream outputStream, List<SheetData<?>> sheetDataList) {
        ExcelWriter excelWriter = null;
        try {
            excelWriter = EasyExcel.write(outputStream)
                    .autoCloseStream(true)
                    .build();

            for (int i = 0; i < sheetDataList.size(); i++) {
                SheetData<?> sheetData = sheetDataList.get(i);
                WriteSheet writeSheet = EasyExcel.writerSheet(i, sheetData.getSheetName())
                        .head(sheetData.getDataClass())
                        .registerWriteHandler(new HorizontalCellStyleStrategy(
                                getDefaultHeadStyle(),
                                getDefaultContentStyle()))
                        .build();

                excelWriter.write(sheetData.getData(), writeSheet);
            }
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    /**
     * 动态表头导出（字段名与表头不匹配但顺序对应）
     * @param outputStream   输出流
     * @param headers        表头列表（显示名称）
     * @param fieldNames     实体类字段名列表（与headers顺序一致）
     * @param data           数据集合 List<T>
     * @param clazz          实体类Class对象
     */
    public static <T> void exportWithHeader(
            OutputStream outputStream,
            List<String> headers,
            List<String> fieldNames,
            List<T> data,
            Class<T> clazz) {

        // 校验headers与fieldNames长度一致
        if (headers.size() != fieldNames.size()) {
            throw new IllegalArgumentException("headers和fieldNames长度必须一致");
        }

        // 构建动态表头（EasyExcel需要的嵌套格式）
        List<List<String>> multiHeader = headers.stream()
                .map(Collections::singletonList)
                .collect(Collectors.toList());

        // 创建ExcelWriter
        ExcelWriter excelWriter = EasyExcel.write(outputStream)
                .head(multiHeader)
                .autoCloseStream(true)
                .registerWriteHandler(new HorizontalCellStyleStrategy(getDefaultHeadStyle(), getDefaultContentStyle()))
                .build();

        // 构建数据转换器（将T对象按fieldNames顺序提取值）
        List<List<Object>> convertedData = data.stream()
                .map(item -> convertItemToRow(item, fieldNames, clazz))
                .collect(Collectors.toList());

        // 写入数据
        WriteSheet writeSheet = EasyExcel.writerSheet(DEFAULT_SHEET_NAME_1).build();
        excelWriter.write(convertedData, writeSheet);
        excelWriter.finish();
    }

    /**
     * 将实体对象按fieldNames顺序转换为行数据
     */
    private static <T> List<Object> convertItemToRow(T item, List<String> fieldNames, Class<T> clazz) {
        try {
            List<Object> row = new ArrayList<>();
            for (String fieldName : fieldNames) {
                Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                row.add(field.get(item));
            }
            return row;
        } catch (Exception e) {
            throw new RuntimeException("字段映射失败", e);
        }
    }

    /**
     * 通过EasyExcel导出数据列表到xlsx文件,使用默认表头样式
     *
     * @param outputStream   输出流
     * @param collection     数据List，泛型为数据的实体类
     * @param clazz          数据实体类class对象，实体类中可通过@ExcelProperty("show name")定义展示名
     * @param excludeColumns 要排除的字段列表
     */
    public static void exportDataWithDefaultStyle(OutputStream outputStream, List<?> collection, Class<?> clazz,
                                                  List<String> excludeColumns) {
        exportDataToXlsx(outputStream, collection, clazz, excludeColumns,
                EasyExcel.writerSheet(DEFAULT_SHEET_NAME_1).build(), Collections
                        .singletonList(new HorizontalCellStyleStrategy(getDefaultHeadStyle(), getDefaultContentStyle())));
    }

    /**
     * 通过EasyExcel导出数据列表到xlsx文件,使用默认表头样式
     *
     * @param outputStream   输出流
     * @param pageFunction   获取分页数据函数，泛型为数据的实体类
     * @param pageIndex      分页码
     * @param clazz          数据实体类class对象，实体类中可通过@ExcelProperty("show name")定义展示名
     * @param excludeColumns 要排除的字段列表
     */
    public static void exportDataWithDefaultStyle(OutputStream outputStream, Function<Integer, List<?>> pageFunction, Integer pageIndex, Class<?> clazz,
                                                  List<String> excludeColumns) {
        exportDataToXlsx(outputStream, pageFunction, pageIndex, clazz, excludeColumns,
                EasyExcel.writerSheet(DEFAULT_SHEET_NAME_1).build(), Collections
                        .singletonList(new HorizontalCellStyleStrategy(getDefaultHeadStyle(), getDefaultContentStyle())));
    }

    /**
     * 通过EasyExcel导出数据列表到xlsx文件，写入操作结束后自动关闭输出流
     *
     * @param outputStream   输出流
     * @param collection     数据List，泛型为数据的实体类
     * @param clazz          数据实体类class对象，实体类中可通过@ExcelProperty("show name")定义展示名
     * @param excludeColumns 要排除的字段列表
     * @param writeSheet     sheet信息, EasyExcel.writerSheet().sheetName("name").build()
     * @Param writeHandlerList 需要注册的handlerList
     */
    public static void exportDataToXlsx(OutputStream outputStream, List<?> collection, Class<?> clazz,
                                        List<String> excludeColumns, WriteSheet writeSheet, List<WriteHandler> writeHandlerList) {
        ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(outputStream, clazz).excelType(ExcelTypeEnum.XLSX)
                .autoCloseStream(true).excludeColumnFiledNames(excludeColumns);
        writeHandlerList.forEach(excelWriterBuilder::registerWriteHandler);
        ExcelWriter excelWriter = excelWriterBuilder.build();
        excelWriter.write(collection, writeSheet);
        excelWriter.finish();
    }

    /**
     * 通过EasyExcel导出数据列表到xlsx文件，写入操作结束后自动关闭输出流
     *
     * @param outputStream   输出流
     * @param pageFunction   获取分页数据函数，泛型为数据的实体类
     * @param pageIndex      分页码
     * @param clazz          数据实体类class对象，实体类中可通过@ExcelProperty("show name")定义展示名
     * @param excludeColumns 要排除的字段列表
     * @param writeSheet     sheet信息, EasyExcel.writerSheet().sheetName("name").build()
     * @Param writeHandlerList 需要注册的handlerList
     */
    public static void exportDataToXlsx(OutputStream outputStream, Function<Integer, List<?>> pageFunction, Integer pageIndex, Class<?> clazz,
                                        List<String> excludeColumns, WriteSheet writeSheet, List<WriteHandler> writeHandlerList) {
        ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(outputStream, clazz).excelType(ExcelTypeEnum.XLSX)
                .autoCloseStream(true).excludeColumnFiledNames(excludeColumns);
        writeHandlerList.forEach(excelWriterBuilder::registerWriteHandler);
        ExcelWriter excelWriter = excelWriterBuilder.build();

        List<?> data = pageFunction.apply(pageIndex);
        while (data != null && !data.isEmpty()) {
            excelWriter.write(data, writeSheet);
            pageIndex++;
            data = pageFunction.apply(pageIndex);
        }
        excelWriter.finish();
    }

    public static WriteCellStyle getDefaultHeadStyle() {
        WriteCellStyle headStyle = new WriteCellStyle();
        WriteFont headFont = new WriteFont();
        headFont.setFontHeightInPoints((short) 11);
        headStyle.setWriteFont(headFont);
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        return headStyle;
    }

    public static WriteCellStyle getDefaultContentStyle() {
        return new WriteCellStyle();
    }

    /**
     * 添加Sheet数据
     *
     * @param sheets       目标列表
     * @param isEmpty      是否为空数据
     * @param dataSupplier 数据查询函数（需返回实体列表）
     * @param dtoClass     DTO类型
     * @param ruleType     规则类型枚举
     */
    public static <E, D> void addSheetData(
            List<ExcelUtil.SheetData<?>> sheets,
            boolean isEmpty,
            Supplier<List<E>> dataSupplier,
            Class<D> dtoClass,
            WarehousingRuleType ruleType
    ) {
        List<D> dataList = isEmpty ? Collections.emptyList() : dataSupplier.get().stream()
                .map(entity -> convertToDto(entity, dtoClass))
                .collect(Collectors.toList());

        sheets.add(new ExcelUtil.SheetData<>(
                ruleType.getRuleType(),
                dtoClass,
                dataList
        ));
    }

    /**
     * 实体转换DTO工具方法
     */
    private static <E, D> D convertToDto(E entity, Class<D> dtoClass) {
        try {
            D dto = dtoClass.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        } catch (Exception e) {
            throw new RuntimeException("DTO转换失败: " + dtoClass.getSimpleName(), e);
        }
    }

    /**
     * Sheet数据包装类
     * @param <T> 数据类型
     */
    @Data
    @AllArgsConstructor
    public static class SheetData<T> {
        private String sheetName;
        private Class<T> dataClass;
        private List<T> data;

    }

    /**
     *
     * @param <T>
     */
    public static class SheetDataListener<T> extends AnalysisEventListener<T> {
        private final String sheetName;
        private final List<T> dataList = new ArrayList<>();

        public SheetDataListener(String sheetName) {
            this.sheetName = sheetName;
        }

        @Override
        public void invoke(T data, AnalysisContext context) {
            dataList.add(data);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            log.info("Sheet[{}] 读取完成，共{}条数据", sheetName, dataList.size());
        }



        public List<T> getDataList() {
            if (dataList.stream().distinct().count() != dataList.size()) {
                throw new RuntimeException("有重复数据");
            }
            return Collections.unmodifiableList(dataList);
        }
    }
}
