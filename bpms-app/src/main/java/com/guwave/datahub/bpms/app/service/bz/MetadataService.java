package com.guwave.datahub.bpms.app.service.bz;

import com.guwave.datahub.bpms.app.ck.CkProvider;
import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.exception.BpmsInternalException;
import com.guwave.datahub.bpms.app.vo.bz.metadata.request.*;
import com.guwave.datahub.bpms.app.vo.bz.metadata.response.*;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.common.constant.Constant;
import com.guwave.datahub.bpms.dao.domain.metadata.MetadataLineage;
import com.guwave.datahub.bpms.dao.domain.metadata.MetadataSchema;
import com.guwave.datahub.bpms.dao.domain.metadata.MetadataTableDescription;
import com.guwave.datahub.bpms.dao.domain.metadata.MetadataTableLink;
import com.guwave.datahub.bpms.dao.repository.bpms.MetadataLineageRepository;
import com.guwave.datahub.bpms.dao.repository.bpms.MetadataSchemaRepository;
import com.guwave.datahub.bpms.dao.repository.bpms.MetadataTableDescriptionRepository;
import com.guwave.datahub.bpms.dao.repository.bpms.MetadataTableLinkRepository;
import com.guwave.datahub.bpms.dao.vo.bpms.metadata.TableInfo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * MetadataService
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-03-11 15:38:41
 */
@Slf4j
@Service
@AllArgsConstructor
public class MetadataService implements CommandLineRunner {

    private final MetadataSchemaRepository metadataSchemaRepository;

    private final MetadataTableLinkRepository metadataTableLinkRepository;

    private final MetadataTableDescriptionRepository metadataTableDescriptionRepository;

    private final MetadataLineageRepository metadataLineageRepository;

    private final CkProvider ckProvider;

    public static void mayThrowOperateNotPresent(Runnable f) {
        try {
            f.run();
        } catch (BpmsInternalException e) {
            if (e.getCause() instanceof EmptyResultDataAccessException) {
                throw new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT);
            } else {
                throw e;
            }
        }
    }

    private static MetadataTableDescription createMetadataTableDescription(TableVo tableQuery) {
        MetadataTableDescription metadataTableDescription = new MetadataTableDescription();
        Date date = new Date();
        metadataTableDescription.setDatasource(tableQuery.getDatasource());
        metadataTableDescription.setDatabaseName(tableQuery.getDatabaseName());
        metadataTableDescription.setTableName(tableQuery.getTableName());
        metadataTableDescription.setDescription(Constant.EMPTY);
        metadataTableDescription.setCreateTime(date);
        metadataTableDescription.setUpdateTime(date);
        metadataTableDescription.setCreateUser(RequestContext.getUserName());
        metadataTableDescription.setUpdateUser(RequestContext.getUserName());
        return metadataTableDescription;
    }

    public List<String> getDatabases(String source) {
        log.info("getDatabases");
        return metadataSchemaRepository.getDatabases(source);
    }

    public List<String> getTableNames(String source, String database) {
        log.info("getTableNames");
        return metadataSchemaRepository.getTableNames(source, database);
    }

    public MetadataTableDescriptionVo getDescription(TableVo tableQuery) {
        log.info("getDescription");
        return metadataTableDescriptionRepository.findByTable(
                        tableQuery.getDatasource(),
                        tableQuery.getDatabaseName(),
                        tableQuery.getTableName())
                .map(MetadataTableDescriptionVo::of)
                .orElseGet(() -> {
                    // 如果没有查到表描述，需要查看实际上有没有这张表
                    log.info("没有查询到表描述");
                    List<String> tableNames = getTableNames(tableQuery.getDatasource(), tableQuery.getDatabaseName());
                    if (tableNames.contains(tableQuery.getTableName())) {
                        // 实际存在这张表却没有描述，创建一条空的描述
                        log.info("创建空的描述");
                        MetadataTableDescription metadataTableDescription = createMetadataTableDescription(tableQuery);
                        metadataTableDescriptionRepository.save(metadataTableDescription);
                        return MetadataTableDescriptionVo.of(metadataTableDescription);
                    } else {
                        log.info("没有对应的表，无法创建描述: {}", tableQuery);
                        throw new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT);
                    }
                });
    }

    public void updateDescription(DescriptionUpdateVo descriptionUpdateVo) {
        log.info("updateDescription");
        Date date = new Date();
        metadataTableDescriptionRepository.findByTable(
                        descriptionUpdateVo.getDatasource(),
                        descriptionUpdateVo.getDatabaseName(),
                        descriptionUpdateVo.getTableName())
                .map(description -> {
                    description.setDescription(descriptionUpdateVo.getDescription());
                    description.setUpdateTime(date);
                    description.setUpdateUser(RequestContext.getUserName());
                    return metadataTableDescriptionRepository.save(description);
                }).orElseThrow(() -> new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT));
    }

    public List<MetadataTableLinkVo> getLinks(TableVo tableQuery) {
        log.info("getLinks");
        return metadataTableLinkRepository.findByTable(
                        tableQuery.getDatasource(),
                        tableQuery.getDatabaseName(),
                        tableQuery.getTableName())
                .stream().map(MetadataTableLinkVo::of).collect(Collectors.toList());
    }

    public List<MetadataTableLinkVo> addLink(LinkAddVo linkAdd) {
        log.info("addLink");
        Date date = new Date();
        MetadataTableLink metadataTableLink = linkAdd.toMetadataTableLink();
        metadataTableLink.setUpdateTime(date);
        metadataTableLink.setCreateTime(date);
        metadataTableLink.setUpdateUser(RequestContext.getUserName());
        metadataTableLink.setCreateUser(RequestContext.getUserName());
        metadataTableLinkRepository.save(linkAdd.toMetadataTableLink());
        return metadataTableLinkRepository.findByTable(
                        linkAdd.getDatasource(),
                        linkAdd.getDatabaseName(),
                        linkAdd.getTableName())
                .stream().map(MetadataTableLinkVo::of).collect(Collectors.toList());
    }

    public List<MetadataTableLinkVo> deleteLink(Long id) {
        log.info("deleteLink");
        return metadataTableLinkRepository.findById(id)
                .map(metadataTableLink -> {
                    mayThrowOperateNotPresent(() ->
                            metadataTableLinkRepository.deleteById(id)
                    );
                    return metadataTableLinkRepository.findByTable(
                                    metadataTableLink.getDatasource(),
                                    metadataTableLink.getDatabaseName(),
                                    metadataTableLink.getTableName())
                            .stream().map(MetadataTableLinkVo::of).collect(Collectors.toList());
                }).orElseThrow(() -> new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT));
    }

    public PageableDataVo<MetadataSchemaVo> getSchema(TableByPageVo tableQuery) {
        log.info("getSchema");
        return PageableDataVo.of(
                metadataSchemaRepository.findByTable(
                                tableQuery.getDatasource(),
                                tableQuery.getDatabaseName(),
                                tableQuery.getTableName(),
                                PageRequest.of(tableQuery.getJpaPageIndex(), tableQuery.getPageSize()))
                        .map(MetadataSchemaVo::of));
    }

    public void updateField(FieldUpdateVo fieldUpdateVo) {
        log.info("updateField");
        Date date = new Date();
        metadataSchemaRepository.findById(fieldUpdateVo.getId())
                .map(schema -> {
                    schema.setCpLogic(fieldUpdateVo.getCpLogic());
                    schema.setFtLogic(fieldUpdateVo.getFtLogic());
                    schema.setInfo(fieldUpdateVo.getInfo());
                    schema.setUpdateTime(date);
                    schema.setUpdateUser(RequestContext.getUserName());
                    return metadataSchemaRepository.save(schema);
                }).orElseThrow(() -> new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT));
    }

    public void deleteField(FieldDeleteVo fieldDeleteVo) {
        log.info("deleteField: {}", fieldDeleteVo);
        mayThrowOperateNotPresent(() ->
                metadataSchemaRepository.deleteAllById(fieldDeleteVo.getIds())
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public PageableDataVo<MetadataSchemaVo> reloadSchema(TableByPageVo tableQuery) {
        log.info("reloadSchema");
        // 从CK里查询实际的表元数据
        String querySql = String.format(
                "SELECT 'clickhouse' AS datasource, " +
                        "`database` AS databaseName, " +
                        "`table` AS tableName, " +
                        "`name` AS fieldName, " +
                        "`type` AS fieldType, " +
                        "`default_expression` AS defaultValue, " +
                        "`position`, " +
                        "`comment` AS fieldComment FROM system.columns " +
                        "WHERE `database` = '%s' AND `table` = '%s';",
                tableQuery.getDatabaseName(),
                tableQuery.getTableName());
        Map<String, MetadataSchema> columnsInCk = ckProvider.readCk(querySql, MetadataSchema.class)
                .stream().collect(Collectors.toMap(MetadataSchema::getFieldName, Function.identity()));

        Map<String, MetadataSchema> metadataSchemas = metadataSchemaRepository.findByTable(
                        tableQuery.getDatasource(),
                        tableQuery.getDatabaseName(),
                        tableQuery.getTableName())
                .stream().collect(Collectors.toMap(MetadataSchema::getFieldName, Function.identity()));

        // 需要新插入的数据
        ArrayList<MetadataSchema> saveList = new ArrayList<>();
        // 需要删除的数据
        ArrayList<Long> deleteIds = new ArrayList<>();

        Date date = new Date();
        columnsInCk.values().forEach(newOne -> {
            if (metadataSchemas.containsKey(newOne.getFieldName())) {
                // 如果metadata里已存在，使用之前的备注等
                MetadataSchema oldOne = metadataSchemas.get(newOne.getFieldName());
                newOne.setCpLogic(oldOne.getCpLogic());
                newOne.setFtLogic(oldOne.getFtLogic());
                newOne.setInfo(oldOne.getInfo());
                newOne.setUpdateTime(date);
                newOne.setCreateTime(oldOne.getCreateTime());
                newOne.setUpdateUser(RequestContext.getUserName());
                newOne.setCreateUser(oldOne.getCreateUser());
            } else {
                // 如果metadata里不存在，直接新增
                newOne.setUpdateTime(date);
                newOne.setCreateTime(date);
                newOne.setUpdateUser(RequestContext.getUserName());
                newOne.setCreateUser(RequestContext.getUserName());
                log.info("新增字段: {}", newOne);
            }
            saveList.add(newOne);
        });

        metadataSchemas.values().forEach(oldOne -> {
            if (columnsInCk.containsKey(oldOne.getFieldName())) {
                // 如果metadata里存在，ck里也存在，需要清除老数据
                deleteIds.add(oldOne.getId());
            } else {
                // 如果metadata里存在，但是ck里不存在，需要更新老数据，position修改成0
                log.info("该字段在ck里已不存在: {}", oldOne);
                oldOne.setPosition(0);
                oldOne.setUpdateTime(date);
                oldOne.setUpdateUser(RequestContext.getUserName());
                saveList.add(oldOne);
            }
        });

        // 保存新的schema, 重新按position排序
        metadataSchemaRepository.saveAll(saveList.stream().sorted(Comparator.comparing(MetadataSchema::getPosition)).collect(Collectors.toList()));

        // 清除老数据
        mayThrowOperateNotPresent(() ->
                metadataSchemaRepository.deleteAllById(deleteIds)
        );

        // 返回分页查询结果
        return PageableDataVo.of(
                metadataSchemaRepository.findByTable(
                                tableQuery.getDatasource(),
                                tableQuery.getDatabaseName(),
                                tableQuery.getTableName(),
                                PageRequest.of(tableQuery.getJpaPageIndex(), tableQuery.getPageSize()))
                        .map(MetadataSchemaVo::of));
    }


    public MetadataTableLineageVo getTableLineage(@RequestBody @Validated TableVo tableQuery) {
        log.info("getTableLineage");
        // 来源表
        List<TableInfo> parentTables = metadataLineageRepository.findParentTables(
                tableQuery.getDatasource(),
                tableQuery.getDatabaseName(),
                tableQuery.getTableName());
        // 结果表
        List<TableInfo> childTables = metadataLineageRepository.findChildTables(
                tableQuery.getDatasource(),
                tableQuery.getDatabaseName(),
                tableQuery.getTableName());
        LinkedHashMap<String, Long> fields = new LinkedHashMap<>();
        // 查询所有字段
        metadataSchemaRepository.findByTable(
                        tableQuery.getDatasource(),
                        tableQuery.getDatabaseName(),
                        tableQuery.getTableName())
                .forEach(schema -> fields.put(schema.getFieldName(), schema.getId()));
        MetadataTableLineageVo metadataTableLineageVo = new MetadataTableLineageVo();
        metadataTableLineageVo.setFromTables(parentTables.stream().map(TableVo::of).collect(Collectors.toList()));
        metadataTableLineageVo.setToTables(childTables.stream().map(TableVo::of).collect(Collectors.toList()));
        metadataTableLineageVo.setFields(fields);
        return metadataTableLineageVo;
    }

    public List<MetadataFieldLineageVo> getFieldLineage(Long id) {
        log.info("getFieldLineage");
        return metadataSchemaRepository.findById(id).map(schema -> {
            // 来源字段
            List<MetadataLineage> parentFields = metadataLineageRepository.findParentFields(
                    schema.getDatasource(),
                    schema.getDatabaseName(),
                    schema.getTableName(),
                    schema.getFieldName());
            // 结果字段
            List<MetadataLineage> childFields = metadataLineageRepository.findChildFields(
                    schema.getDatasource(),
                    schema.getDatabaseName(),
                    schema.getTableName(),
                    schema.getFieldName());

            ArrayList<MetadataFieldLineageVo> metadataFieldLineageVos = new ArrayList<>();
            // 按表聚合，把属于同一张表的字段放进lineageList
            // 再遍历lineageList取出字段名和id
            parentFields.stream().collect(
                            Collectors.groupingBy(lineage -> new TableVo(lineage.getFromSource(), lineage.getFromDatabase(), lineage.getFromTable())))
                    .forEach((table, lineageList) -> {
                        Map<String, Long> fields = lineageList.stream().collect(Collectors.toMap(MetadataLineage::getFromField, MetadataLineage::getId));
                        metadataFieldLineageVos.add(new MetadataFieldLineageVo(table, fields));
                    });

            childFields.stream().collect(
                            Collectors.groupingBy(lineage -> new TableVo(lineage.getToSource(), lineage.getToDatabase(), lineage.getToTable())))
                    .forEach((table, lineageList) -> {
                        Map<String, Long> fields = lineageList.stream().collect(Collectors.toMap(MetadataLineage::getToField, MetadataLineage::getId));
                        metadataFieldLineageVos.add(new MetadataFieldLineageVo(table, fields));
                    });
            return metadataFieldLineageVos;
        }).orElseThrow(() -> new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT));
    }

    public MetadataSchemaVo getFieldInfo(Long id) {
        log.info("getFieldInfo");
        return metadataSchemaRepository.findById(id).map(MetadataSchemaVo::of)
                .orElseThrow(() -> new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT));
    }

    public void initClickhouseSchema(List<String> databases) {
        log.info("initClickhouseSchema");
        String databaseStr = databases.stream()
                .map(s -> "'" + s + "'")
                .collect(Collectors.joining(","));
        String querySql = String.format("select 'clickhouse' datasource, database databaseName, table tableName from system.tables " +
                "where engine ='Distributed' and database in (%s);", databaseStr);

        List<TableByPageVo> tableByPageVos = ckProvider.readCk(querySql, TableByPageVo.class);
        log.info("需要加载schema的表: \n{}", tableByPageVos.stream().map(Object::toString).collect(Collectors.joining("\n")));

        tableByPageVos.forEach(this::reloadSchema);
    }

    /**
     * 初始化clickhouse表到bz_metadata_schema
     */
    @Override
    public void run(String... args) {
        log.info("check metadata schema.");
        if (metadataSchemaRepository.countByDatasource("clickhouse") == 0) {
            log.info("metadata schema is empty, initialize bz_metadata_schema.");
            initClickhouseSchema(Arrays.asList("ods", "dwd", "dws", "dim", "ads"));
        } else {
            log.info("metadata schema is ok.");
        }
    }
}
