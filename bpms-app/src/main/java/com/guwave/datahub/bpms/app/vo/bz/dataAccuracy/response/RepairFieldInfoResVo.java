package com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(title = "数据修复文件可修复的字段")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RepairFieldInfoResVo {
    @Schema(title = "数据修复字段枚举值:DEVICE_ID,TEST_STAGE,LOT_ID,SBLOT_ID,TEST_PROGRAM,LOT_TYPE")
    private String field;
    @Schema(title = "数据修复字段show name")
    private String fieldShowName;
    @Schema(title = "文件Id")
    private Long fileId;
    @Schema(title = "文件名")
    private String fileName;
    @Schema(title = "字段原始值")
    private String originalFieldValue;

}
