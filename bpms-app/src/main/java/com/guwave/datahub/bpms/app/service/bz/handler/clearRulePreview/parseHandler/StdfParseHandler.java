package com.guwave.datahub.bpms.app.service.bz.handler.clearRulePreview.parseHandler;

import com.guwave.datahub.bpms.app.service.bz.handler.clearRulePreview.visitor.MultiThreadVisitor;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model.ClearRulePreviewResult;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model.SettingSnapshot;
import com.guwave.datahub.bpms.dao.domain.clearRulePreview.ClearRulePreviewRecord;
import com.guwave.onedata.dataware.common.contant.DataClearRuleType;
import com.guwave.onedata.dataware.common.contant.StdfFieldType;
import com.guwave.onedata.dataware.common.contant.TesterType;
import com.guwave.onedata.dataware.common.exception.WrongValidHeadException;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.DataClearRule;
import com.guwave.onedata.dataware.parser.stdf.ecidParse.EcidRuleSetting;
import com.guwave.onedata.dataware.parser.stdf.model.ClearRule;
import com.guwave.onedata.dataware.parser.stdf.uidParse.UidRuleSetting;
import com.guwave.onedata.dataware.parser.stdf.util.StdfMultiThreadParseUtil;
import com.guwave.onedata.dataware.parser.stdf.visitor.Visitor;
import com.guwave.onedata.dataware.sdk.model.ecid.CpLotIdInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public abstract class StdfParseHandler extends ParseHandler {

    @Value("${spring.settings.siteNumFilterFlag}")
    private Boolean siteNumFilterFlag;


    @Override
    protected List<Visitor> parseData(ClearRulePreviewRecord record, File file, ClearRulePreviewResult clearRulePreviewResult, EcidRuleSetting ecidRuleSetting, String configCpLotId, UidRuleSetting uidRuleSetting, CpLotIdInfo cpLotIdInfo) {
        // 获取清洗规则
        List<DataClearRule> dataClearRules = queryDataClearRules(record);
        clearRulePreviewResult.getSettingSnapshot().getDwDataClearRules().addAll(dataClearRules.stream().map(SettingSnapshot.DataClearRuleSnapshot::of).collect(Collectors.toList()));
        Map<StdfFieldType, List<ClearRule>> rules = filterDataClearRules(dataClearRules, record);
        int allThreadCnt = StdfMultiThreadParseUtil.calculateThredCnt(file);
        List<Visitor> visitors;
        try {
            visitors = parseStdfFile(record, file, allThreadCnt, rules, ecidRuleSetting, configCpLotId, uidRuleSetting, cpLotIdInfo);
        } catch (WrongValidHeadException wrongValidHeadException) {
            log.info("{} 分 {} 片解析,head校验不正确,尝试使用1个线程解析", file.getAbsolutePath(), allThreadCnt, wrongValidHeadException);
            allThreadCnt = 1;
            visitors = parseStdfFile(record, file, allThreadCnt, rules, ecidRuleSetting, configCpLotId, uidRuleSetting, cpLotIdInfo);
        }
        return visitors;
    }

    private List<Visitor> parseStdfFile(ClearRulePreviewRecord record, File file, int allThreadCnt, Map<StdfFieldType, List<ClearRule>> rules, EcidRuleSetting ecidRuleSetting, String configCpLotId, UidRuleSetting uidRuleSetting, CpLotIdInfo cpLotIdInfo) {
        List<Visitor> visitors = new ArrayList<>();
        for (int i = 1; i <= allThreadCnt; i++) {
            visitors.add(new MultiThreadVisitor(i, generateFillFileMainDataConsumer(record), record, ecidRuleSetting, configCpLotId, uidRuleSetting, cpLotIdInfo));
        }

        try {
            StdfMultiThreadParseUtil.MetaData multiThreadMetaData = new StdfMultiThreadParseUtil.MetaData()
                    .setFile(file)
                    .setVisitors(visitors)
                    .setTestAreaFunction((mir, prrs) -> record.getTestArea())
                    .setNeedMergeTestTxt(TesterType.J750.getType().equals(record.getTesterType()) && rules.getOrDefault(StdfFieldType.PTR, new ArrayList<>()).stream().anyMatch(t -> t.getRuleType() == DataClearRuleType.INTERSECTION))
                    .setNotNeedLoopTestTxt(false)
                    .setSiteNumFilterFlag(siteNumFilterFlag)
                    .setClearRules(rules);
            StdfMultiThreadParseUtil.parse(multiThreadMetaData);
        } finally {
            visitors.forEach(t -> {
                MultiThreadVisitor visitor = (MultiThreadVisitor) t;
                visitor.close();
            });
        }

        return visitors;
    }
}
