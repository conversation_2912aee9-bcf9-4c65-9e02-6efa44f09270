package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.request;

import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper=false)
@Schema(title = "自动入库规则查询")
public class WarehousingRuleQueryVo extends PageableRequestVo {

    @Schema(title = "subCustomerList", description = "子客户", example = "[\"AMD\"]")
    private List<String> subCustomerList;

    @Schema(title = "factoryList", description = "工厂", example = "[\"TSMC\", \"SMIC\"]")
    private List<String> factoryList;

    @Schema(title = "testAreaList", description = "CP、FT、SLT等", example = "[\"CP\"]")
    private List<String> testAreaList;

    @Schema(title = "fileCategoryList", description = "文件类型", example = "[\"STDF\"]")
    private List<String> fileCategoryList;

}
