package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(title = "文件重命名规则")
public class DwSftpFileRenameRuleRequestVo {
    @Schema(title = "id")
    private Long id;

    @Schema(description = "子客户", example = "GUWAVE")
    private String subCustomer;

    @Schema(description = "ftp服务ip", example = "************")
    private String ftpIp;

    @Schema(description = "使用时间戳，与使用目录互斥", example = "1")
    private Integer useTimestamp;

    @Schema(description = "需要执行的正则", example = "/upload/onedata_saas/NOVOSNS/NOVOSNS/.*/WIP/.*\\.xlsx$")
    private String ruleExpression;

    @Schema(description = "需要拼接的目录个数")
    private Integer directoryNum;
}
