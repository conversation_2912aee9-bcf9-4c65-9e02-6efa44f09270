package com.guwave.datahub.bpms.app.vo.bz.metadata.request;

import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * FieldDeleteVo
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-03-11 15:31:40
 */
@Schema(title = "metadata 删除字段")
@Data
@NoArgsConstructor
public class FieldDeleteVo {

    @Schema(title = "数据库", description = "clickhouse/mysql", example = "clickhouse")
    @NotNull(message = "数据库")
    private String datasource;

    @Schema(title = "库名", example = "ods")
    @NotNull(message = "库名")
    private String databaseName;

    @Schema(title = "表名", example = "ods_dc_device_info_cluster")
    @NotNull(message = "表名")
    private String tableName;

    @Schema(title = "需要删除的字段id列表", example = "[2,4,6]")
    @NotNull(message = "需要删除的字段id列表")
    private List<Long> ids;
}
