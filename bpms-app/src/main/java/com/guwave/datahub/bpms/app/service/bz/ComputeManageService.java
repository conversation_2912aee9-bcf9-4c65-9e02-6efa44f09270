package com.guwave.datahub.bpms.app.service.bz;

import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.exception.BpmsInternalException;
import com.guwave.datahub.bpms.app.vo.bz.computeManage.model.ComputeManageConfig;
import com.guwave.datahub.bpms.app.vo.bz.computeManage.request.ComputeManageUpdateVo;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.dao.repository.dw.DwBzCalculateConfigRepository;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.DwBzCalculateConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.function.Supplier;

import static com.guwave.datahub.bpms.app.web.response.ResponseCode.Business.CONFIG_ALREADY_EXISTS;

@Slf4j
@Service
public class ComputeManageService {

    @Autowired
    private DwBzCalculateConfigRepository dwBzCalculateConfigRepository;

    public PageableDataVo<ComputeManageConfig> list(PageableRequestVo pageableRequestVo) {
        log.info("查询业务计算管理配置列表");

        log.info("RequestContext.getSubCustomer(): {}", RequestContext.getSubCustomer());
        return PageableDataVo.of((dwBzCalculateConfigRepository.findAllByCustomerAndSubCustomerAndDeleteFlagOrderByIdDesc(
                        RequestContext.getCustomer(), RequestContext.getSubCustomer(), 0, pageableRequestVo.toPageable())
                .map(this::convertToVo)));
    }

    @Transactional(rollbackFor = Exception.class)
    public ComputeManageConfig addConfig(ComputeManageConfig config) {
        log.info("新增业务计算管理配置");

        // 检查是否已存在相同配置
        String normalizedTestStage = StringUtils.hasText(config.getTestStage()) ? config.getTestStage().trim() : null;
        config.setTestStage(normalizedTestStage);

        if (dwBzCalculateConfigRepository.existsByCustomerAndFactoryAndFactorySiteAndDeviceIdAndTestStageAndLotTypeAndTestAreaAndFileCategoryAndDeleteFlag(
                RequestContext.getCustomer(),
                config.getFactory(),
                config.getFactorySite(),
                config.getDeviceId(),
                config.getTestStage(),
                LotType.of(config.getLotType()),
                TestArea.of(config.getTestArea()),
                FileCategory.of(config.getFileCategory()),
                0
        )) {
            throw new BpmsException(CONFIG_ALREADY_EXISTS);
        }
        Date now = new Date();
        DwBzCalculateConfig entity = new DwBzCalculateConfig().setCustomer(RequestContext.getCustomer())
                .setSubCustomer(RequestContext.getSubCustomer())
                .setTestArea(TestArea.of(config.getTestArea()))
                .setLotType(LotType.of(config.getLotType()))
                .setFileCategory(FileCategory.of(config.getFileCategory()))
                .setFactory(config.getFactory())
                .setFactorySite(config.getFactorySite())
                .setDeviceId(config.getDeviceId())
                .setTestStage(config.getTestStage())
                .setCalculateDwTestItem(config.getCalculateDwTestItem())
                .setCalculateYmsTestItem(config.getCalculateYmsTestItem())
                .setStatus(config.getStatus())
                .setDeleteFlag(0)
                .setCreateTime(now)
                .setUpdateTime(now)
                .setCreateUser(RequestContext.getUserName())
                .setUpdateUser(RequestContext.getUserName());
        DwBzCalculateConfig saved = mayThrowException(() -> dwBzCalculateConfigRepository.save(entity));
        log.info("新增业务计算管理配置成功, id: {}", saved.getId());
        return convertToVo(saved);
    }

    @Transactional(rollbackFor = Exception.class)
    public ComputeManageConfig updateConfig(ComputeManageUpdateVo updateVo) {
        log.info("更新业务计算管理配置: {}", updateVo);
        DwBzCalculateConfig entity = dwBzCalculateConfigRepository.findByIdAndCustomerAndSubCustomerAndDeleteFlag(
                        updateVo.getId(), RequestContext.getCustomer(), RequestContext.getSubCustomer(), 0)
                .orElseThrow(() -> new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT));

        // 只更新允许修改的字段
        entity.setCalculateDwTestItem(updateVo.getCalculateDwTestItem());
        entity.setCalculateYmsTestItem(updateVo.getCalculateYmsTestItem());
        entity.setStatus(updateVo.getStatus());
        entity.setUpdateTime(new Date());
        entity.setUpdateUser(RequestContext.getUserName());

        DwBzCalculateConfig saved = mayThrowException(() -> dwBzCalculateConfigRepository.save(entity));
        log.info("更新业务计算管理配置成功, id: {}", saved.getId());
        return convertToVo(saved);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteConfig(Long id) {
        log.info("删除业务计算管理配置, id: {}", id);
        DwBzCalculateConfig entity = dwBzCalculateConfigRepository.findByIdAndCustomerAndSubCustomerAndDeleteFlag(
                        id, RequestContext.getCustomer(), RequestContext.getSubCustomer(), 0)
                .orElseThrow(() -> new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT));

        // 逻辑删除
        entity.setDeleteFlag(1);
        entity.setUpdateTime(new Date());
        entity.setUpdateUser(RequestContext.getUserName());
        mayThrowException(() -> dwBzCalculateConfigRepository.save(entity));
        log.info("删除业务计算管理配置成功, id: {}", id);
    }

    /**
     * 将实体对象转换为VO对象
     *
     * @param entity 实体对象
     * @return VO对象
     */
    private ComputeManageConfig convertToVo(DwBzCalculateConfig entity) {
        ComputeManageConfig vo = new ComputeManageConfig();
        vo.setId(entity.getId());
        vo.setTestArea(TestArea.of(entity.getTestArea()));
        vo.setLotType(LotType.of(entity.getLotType()));
        vo.setFileCategory(FileCategory.of(entity.getFileCategory()));
        vo.setFactory(entity.getFactory());
        vo.setFactorySite(entity.getFactorySite());
        vo.setDeviceId(entity.getDeviceId());
        vo.setTestStage(entity.getTestStage());
        vo.setCalculateDwTestItem(entity.getCalculateDwTestItem());
        vo.setCalculateYmsTestItem(entity.getCalculateYmsTestItem());
        vo.setStatus(entity.getStatus());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());
        vo.setCreateUser(entity.getCreateUser());
        vo.setUpdateUser(entity.getUpdateUser());
        return vo;
    }

    public static <T> T mayThrowException(Supplier<T> f) {
        try {
            return f.get();
        } catch (BpmsInternalException e) {
            if (e.getCause() instanceof DataIntegrityViolationException) {
                log.error("{}", e.getMessage(), e.getCause());
                throw new BpmsException(ResponseCode.Common.VIOLATION_INTEGRITY_CONSTRAINTS);
            } else {
                throw e;
            }
        }
    }
}
