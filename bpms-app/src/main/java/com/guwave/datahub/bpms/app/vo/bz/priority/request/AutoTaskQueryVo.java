package com.guwave.datahub.bpms.app.vo.bz.priority.request;

import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = false)
@Data
@Schema(description = "自动入库任务查询请求")
public class AutoTaskQueryVo extends PageableRequestVo {
    @Schema(description = "factory")
    private String factory;

    @Schema(description = "deviceId")
    private String deviceId;

    @Schema(description = "lotType")
    private String lotType;

    @Schema(description = "lotId")
    private String lotId;

    @Schema(description = "waferNo")
    private String waferNo;

    @Schema(description = "fileCategory")
    private String fileCategory;

    @Schema(description = "testArea")
    private String testArea;
}
