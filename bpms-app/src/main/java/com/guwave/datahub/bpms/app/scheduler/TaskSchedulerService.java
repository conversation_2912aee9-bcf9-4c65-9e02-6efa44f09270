package com.guwave.datahub.bpms.app.scheduler;

import com.alibaba.fastjson.JSON;
import com.guwave.datahub.bpms.app.executor.Executor;
import com.guwave.datahub.bpms.app.executor.impl.CkExecutor;
import com.guwave.datahub.bpms.app.executor.impl.PythonExecutor;
import com.guwave.datahub.bpms.app.executor.impl.SparkExecutor;
import com.guwave.datahub.bpms.common.constant.ExceptionType;
import com.guwave.datahub.bpms.common.constant.ExecuteCommandException;
import com.guwave.datahub.bpms.common.constant.ExecuteEngine;
import com.guwave.datahub.bpms.dao.domain.schedule.ScheduleTaskDefinition;
import com.guwave.datahub.bpms.dao.domain.schedule.ScheduleTaskRecord;
import com.guwave.datahub.bpms.dao.repository.bpms.ScheduleTaskDefinitionRepository;
import com.guwave.datahub.bpms.dao.repository.bpms.ScheduleTaskRecordRepository;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.next.compute.api.vo.response.ComputeResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.stream.Collectors;

@Service
public class TaskSchedulerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(TaskSchedulerService.class);

    public static final Set<String> COMPUTE_CODE_SET = new CopyOnWriteArraySet<>();

    private final ScheduleTaskDefinitionRepository scheduleTaskDefinitionRepository;

    @Autowired
    private CkExecutor ckExecutor;
    @Autowired
    private PythonExecutor pythonExecutor;
    @Autowired
    private SparkExecutor sparkExecutor;
    @Autowired
    private ScheduleTaskRecordRepository scheduleTaskRecordRepository;

    public TaskSchedulerService(ScheduleTaskDefinitionRepository scheduleTaskDefinitionRepository) {
        this.scheduleTaskDefinitionRepository = scheduleTaskDefinitionRepository;
        updateComputeCode();
    }

    public void updateComputeCode() {
        COMPUTE_CODE_SET.addAll(
                scheduleTaskDefinitionRepository.findAllByDeleteFlagAndExecuteEngine(false, ExecuteEngine.SPARK)
                        .stream()
                        .map(ScheduleTaskDefinition::getCommand)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toSet())
        );
        // 打印COMPUTE_CODE_SET供分析
        LOGGER.info("COMPUTE_CODE_SET:{}", JSON.toJSONString(COMPUTE_CODE_SET));
    }

    /**
     * 生成ScheduleTaskRecord
     *
     * @param taskDefinition {@link ScheduleTaskDefinition}
     * @return {@link ScheduleTaskRecord}
     */
    public ScheduleTaskRecord generateTaskRecord(ScheduleTaskDefinition taskDefinition) {
        if (taskDefinition == null) {
            LOGGER.info("当前未找到需要执行的Task");
            return null;
        }

        Date now = new Date();
        // 生成 ScheduleTaskRecord
        ScheduleTaskRecord taskRecord = new ScheduleTaskRecord()
                .setTaskDefinitionId(taskDefinition.getId())
                .setTaskName(taskDefinition.getTaskName())
                .setExecuteEngine(taskDefinition.getExecuteEngine())
                .setCronExpression(taskDefinition.getCronExpression())
                .setCommand(taskDefinition.getCommand())
                .setProcessStatus(ProcessStatus.CREATE)
                .setScheduleTime(now)
                .setCreateTime(now)
                .setUpdateTime(now)
                .setCreateUser(Constant.SYSTEM)
                .setUpdateUser(Constant.SYSTEM);

        return taskRecord;
    }

    public void executeAsyncTask(ScheduleTaskRecord taskRecord) {
        if (taskRecord.getExecuteEngine() != ExecuteEngine.SPARK) {
            LOGGER.info("异步任务调度异常,ExecuteEngine不是SPARK!:{}", taskRecord.getExecuteEngine());
        }
        Executor executor = getExecutor(taskRecord.getExecuteEngine());
        try {
            ComputeResponse computeResponse = executor.doExecute(taskRecord);

            if (!ComputeResponse.SUCCESS.equals(computeResponse.getCode())) {
                throw new ExecuteCommandException(ExceptionType.EXECUTE_SPARK_TASK_EXCEPTION, computeResponse.getMsg());
            }
            taskRecord
                    .setUniqueId(computeResponse.getUniqueId())
                    .setStartTime(new Date())
                    .setProcessStatus(ProcessStatus.PROCESSING)
                    .setExceptionType(null)
                    .setErrorMessage(null)
                    .setUpdateTime(new Date())
                    .setUpdateUser(Constant.SYSTEM);
        } catch (Exception e) {
            LOGGER.error("执行异步任务异常: {}", ExceptionUtils.getStackTrace(e));
            taskRecord.setProcessStatus(ProcessStatus.FAIL)
                    .setExceptionType(ExceptionType.EXECUTE_SPARK_TASK_EXCEPTION)
                    .setErrorMessage(ExceptionUtils.getStackTrace(e))
                    .setUpdateTime(new Date())
                    .setUpdateUser(Constant.SYSTEM);
        } finally {
            scheduleTaskRecordRepository.save(taskRecord);
        }
    }

    public void executeSyncTask(ScheduleTaskRecord taskRecord) {
        if (COMPUTE_CODE_SET.contains(taskRecord.getCommand())) {
            LOGGER.info("同步任务调度异常,ExecuteEngine不是PYTHON或CLICKHOUSE!:{}", taskRecord.getExecuteEngine());
        }
        Executor executor = getExecutor(taskRecord.getExecuteEngine());
        try {
            Date startTime = new Date();
            executor.doExecute(taskRecord.getCommand());
            Date endTime = new Date();
            taskRecord.setStartTime(startTime)
                    .setEndTime(endTime)
                    .setExecuteTime(endTime.getTime() - startTime.getTime())
                    .setProcessStatus(ProcessStatus.SUCCESS)
                    .setExceptionType(null)
                    .setErrorMessage(null)
                    .setUpdateUser(Constant.SYSTEM)
                    .setUpdateTime(endTime);
        } catch (Exception e) {
            LOGGER.error("执行同步任务异常: {}", ExceptionUtils.getStackTrace(e));
            taskRecord.setProcessStatus(ProcessStatus.FAIL)
                    .setExceptionType(taskRecord.getExecuteEngine() == ExecuteEngine.PYTHON ? ExceptionType.EXECUTE_PYTHON_SCRIPT_EXCEPTION : ExceptionType.EXECUTE_CLICKHOUSE_SQL_EXCEPTION)
                    .setErrorMessage(ExceptionUtils.getStackTrace(e))
                    .setUpdateUser(Constant.SYSTEM)
                    .setUpdateTime(new Date());
        } finally {
            scheduleTaskRecordRepository.save(taskRecord);
        }
    }

    private Executor getExecutor(ExecuteEngine executeEngine) {
        switch (executeEngine) {
            case CLICKHOUSE:
                return ckExecutor;
            case PYTHON:
                return pythonExecutor;
            case SPARK:
                return sparkExecutor;
            default:
                return null;
        }
    }
}
