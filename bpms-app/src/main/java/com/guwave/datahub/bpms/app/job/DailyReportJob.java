package com.guwave.datahub.bpms.app.job;

import com.guwave.datahub.bpms.app.service.dailyReport.DailyReportService;
import com.guwave.datahub.bpms.app.util.DateUtil;
import com.guwave.datahub.bpms.dao.domain.dailyReport.DailyReportRecord;
import com.guwave.datahub.bpms.dao.repository.bpms.DailyReportRecordRepository;
import com.guwave.datahub.bpms.dao.repository.bpms.DailyReportResultRepository;
import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import static com.guwave.onedata.dataware.common.contant.Constant.SYSTEM;

@Component
public class DailyReportJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(DailyReportJob.class);

    @Autowired
    private DailyReportRecordRepository dailyReportRecordRepository;

    @Autowired
    private DailyReportResultRepository dailyReportResultRepository;

    @Autowired
    private DailyReportService dailyReportService;

    @ScheduleSwitch
    @Scheduled(cron = "${spring.scheduler.timer.dailyReport}")
    public void run() {
        LOGGER.info("开始生成大数据日报");
        DailyReportRecord dailyReportRecord = createDailyReportRecord();
        dailyReportService.generateDailyReport(dailyReportRecord);
    }

    public DailyReportRecord createDailyReportRecord() {
        String day = DateUtil.getDayBefortDays(1);

        // 先将上一次跑的数据的lastestFlag置为0
        dailyReportResultRepository.updateLastestFlag(day, 0);
        DailyReportRecord dailyReportRecord = new DailyReportRecord()
                .setProcessStatus(ProcessStatus.PROCESSING)
                .setDay(day)
                .setStartTime(new Date())
                .setCreateTime(new Date())
                .setCreateUser(SYSTEM)
                .setUpdateTime(new Date())
                .setUpdateUser(SYSTEM);
        return dailyReportRecordRepository.save(dailyReportRecord);
    }
}
