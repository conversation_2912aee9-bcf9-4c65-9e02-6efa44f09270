package com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(title = "数据质量detail列表查询参数")
public class DataAccuracyDetailQueryVo {

    @Schema(description = "deviceId", required = true)
    @NotNull(message = "Device Id不能为空")
    private String deviceId;

    @Schema(description = "testArea", required = true)
    @NotNull(message = "Test Area不能为空")
    private String testArea;

    @Schema(description = "testStage", required = true)
    @NotNull(message = "Test stage不能为空")
    private String testStage;

    @Schema(description = "lotId")
    @NotNull(message = "Lot Id不能为空")
    private String lotId;

    @Schema(description = "waferNo,CP阶段不能为空")
    private String waferNo;

}
