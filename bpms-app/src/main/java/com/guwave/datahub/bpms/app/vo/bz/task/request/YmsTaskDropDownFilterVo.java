package com.guwave.datahub.bpms.app.vo.bz.task.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/3/12
 * @description YmsTaskFieldFilterVo
 */

@Data
@NoArgsConstructor
@Schema(title = "task YMS级联查询")
public class YmsTaskDropDownFilterVo {

    @NotBlank(message = "filterField")
    @Schema(title = "filterField", description = "过滤字段，即当前下拉框需要筛选的字段")
    private String filterField;

    @Schema(title = "testArea列表", example = "[]")
    private List<String> testAreaList;

    @Schema(title = "factory列表", example = "[]")
    private List<String> factoryList;

    @Schema(title = "deviceId列表", example = "[]")
    private List<String> deviceIdList;

    @Schema(title = "testStage列表", example = "[]")
    private List<String> testStageList;

    @Schema(title = "lotId列表", example = "[]")
    private List<String> lotIdList;

    @Schema(title = "waferNo列表", example = "[]")
    private List<String> waferNoList;

    @Schema(title = "lotType列表", example = "[]")
    private List<String> lotTypeList;

    @Schema(title = "processStatus列表", example = "[]")
    private List<String> processStatusList;

    @Schema(title = "optType列表", example = "[]")
    private List<String> optTypeList;

    @Schema(title = "executeEngine列表", example = "[]")
    private List<String> executeEngineList;
}
