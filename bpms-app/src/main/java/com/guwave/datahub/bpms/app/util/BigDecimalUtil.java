package com.guwave.datahub.bpms.app.util;

import cn.hutool.core.util.StrUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

public class BigDecimalUtil {

    public static BigDecimal toBigDecimal(Double val) {
        return null == val || Double.isNaN(val) || Double.isInfinite(val) ? null : new BigDecimal(val.toString());
    }


    public static String bigDecimalToPercentStr(BigDecimal val, boolean multiply) {
        if (Objects.isNull(val)) {
            return StrUtil.EMPTY;
        }

        BigDecimal res = val;
        if (multiply) {
            res = val.multiply(BigDecimal.valueOf(100));
        }
        return res.setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + "%";
    }


    public static String doubleToPercentStr(double val, boolean multiply) {
        return bigDecimalToPercentStr(BigDecimal.valueOf(val), multiply);
    }

}
