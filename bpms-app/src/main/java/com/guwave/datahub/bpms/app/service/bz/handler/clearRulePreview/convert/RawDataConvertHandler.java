package com.guwave.datahub.bpms.app.service.bz.handler.clearRulePreview.convert;

import com.google.common.collect.Lists;
import com.guwave.datahub.bpms.app.util.AdapterUtil;
import com.guwave.datahub.bpms.app.util.FileUtil;
import com.guwave.datahub.bpms.app.util.HdfsUtil;
import com.guwave.datahub.bpms.dao.domain.clearRulePreview.ClearRulePreviewRecord;
import com.guwave.datahub.bpms.dao.repository.dw.SftpFileConvertScriptRepository;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.FileLoadExceptionInfo;
import com.guwave.onedata.dataware.common.contant.FileType;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpFileConvertScript;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.math3.util.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.HashMap;
import java.util.List;

@Component
@Slf4j
public class RawDataConvertHandler extends ConvertHandler {

    @Autowired
    private SftpFileConvertScriptRepository sftpFileConvertScriptRepository;
    @Autowired
    private HdfsUtil hdfsUtil;

    @Override
    public boolean isSupport(ClearRulePreviewRecord record) {
        return record.getFileCategory() == FileCategory.RAW_DATA;
    }

    @Override
    public File convertFile(ClearRulePreviewRecord record, File needConvertFile) throws Exception {
        // 查询文件对应的转换脚本
        List<SftpFileConvertScript> convertScripts = sftpFileConvertScriptRepository.findAllBySftpFileName(record.getCustomer(), record.getSubCustomer(), record.getGivenTestArea().getArea(), record.getFactory(), record.getFactorySite(), record.getFileCategory().getCategory(), record.getOriginFileName());
        if (CollectionUtils.isEmpty(convertScripts)) {
            convertScripts = sftpFileConvertScriptRepository.findAllBySftpFileName(record.getCustomer(), record.getSubCustomer(), record.getGivenTestArea().getArea(), record.getFactory(), record.getFactorySite(), record.getFileCategory().getCategory(), record.getRemoteOriginFilePath());
        }

        if (CollectionUtils.isEmpty(convertScripts)) {
            String errorMessage = String.format("文件%s对应的脚本不存在", record.getFileName());
            throw new FileLoadException(FileLoadExceptionInfo.PYTHON_SCRIPT_NOT_FOUND_EXCEPTION, errorMessage, null);
        } else if (convertScripts.size() > 1) {
            log.info("文件{}对应的脚本有多条", record.getFileName());
            throw new RuntimeException("文件对应的脚本有多条");
        } else {
            SftpFileConvertScript sftpFileConvertScript = convertScripts.get(0);
            File zipCompressScriptFile = new File(needConvertFile.getParent(), sftpFileConvertScript.getFileName());
            hdfsUtil.downloadToLocal(false, sftpFileConvertScript.getHdfsFilePath(), zipCompressScriptFile.getAbsolutePath());
            File scriptFileDir = new File(zipCompressScriptFile.getParent(), FileUtil.removeFileSuffix(zipCompressScriptFile.getName(), FileType.ZIP));
            File executeScriptFile = new File(scriptFileDir, sftpFileConvertScript.getExecuteFileName());
            hdfsUtil.unCompress(scriptFileDir.getAbsolutePath(), zipCompressScriptFile.getAbsolutePath());

            File convertResultFile = new File(new File(needConvertFile.getParent(), "convert_result"), needConvertFile.getName() + ".rawdata");
            convertResultFile.getParentFile().mkdirs();
            Pair<Boolean, HashMap<Object, Object>> resultMap = AdapterUtil.executePython(executeScriptFile.getAbsolutePath(), Lists.newArrayList(needConvertFile.getAbsolutePath(), convertResultFile.getAbsolutePath(), record.getOriginFileName()), null);
            if (!convertResultFile.exists()) {
                throw new RuntimeException("转换后的文件不存在");
            }

            return convertResultFile;
        }
    }
}
