package com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.request;

import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.TestArea;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Schema(title = "清洗规则预览上传条件")
@Data
public class ClearRulePreviewRecordUploadVo {
    @Schema(title = "factory", example = "TEST")
    @NotBlank(message = "factory")
    @Length(max = 50)
    private String factory;

    @Schema(title = "factorySite", example = "TEST")
    @NotBlank(message = "factorySite")
    @Length(max = 50)
    private String factorySite;

    @Schema(title = "givenTestArea", example = "CP")
    @NotNull(message = "givenTestArea")
    private TestArea givenTestArea;

    @Schema(title = "fileCategory", example = "STDF")
    @NotNull(message = "fileCategory")
    private FileCategory fileCategory;

    @Schema(title = "remoteOriginFilePath", example = "/GUWAVE/XXX")
    @NotBlank(message = "remoteOriginFilePath")
    @Length(max = 1000)
    private String remoteOriginFilePath;

    @Schema(title = "file", type = "file")
    @NotNull(message = "文件")
    private MultipartFile file;
}
