package com.guwave.datahub.bpms.app.vo.bz.stateManage.response;


import com.guwave.datahub.bpms.app.vo.bz.stateManage.model.ModuleSwitch;
import com.guwave.datahub.bpms.app.vo.bz.stateManage.model.ModuleTask;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Schema(title = "状态管理回显信息")
@Data
public class StateManageDetailVo {

    @Schema(title = "模块开关列表")
    private List<ModuleSwitch> moduleSwitchDatas;

    @Schema(title = "模块运行中的任务列表")
    private List<ModuleTask> moduleTasks;

}
