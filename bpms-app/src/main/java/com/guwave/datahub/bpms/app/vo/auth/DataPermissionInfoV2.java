package com.guwave.datahub.bpms.app.vo.auth;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * - 数据权限V2对应的数据权限信息
 * Copyright (C), 2021, guwave
 * @Title: DataPermissionInfoV2.java
 * @summary 
 * <AUTHOR> <PERSON>
 * @version 0.0.1
 * @date 2024-9-11
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DataPermissionInfoV2 {
    
    /**
     * - 是否开启数据权限
     * true的时候开启数据权限
     */
    private Boolean accessSwitch;
    
    /**
     * - 是否管理员权限
     * - 管理员拥有所有的数据权限
     */
    private Boolean admin;
    
    /**
     * - 数据权限信息，每个角色一条
     * - 角色之间是or的关系
     */
    private List<RoleDataPermissionInfo> roleDataPermissionInfos;
    
    @Data
    public static class RoleDataPermissionInfo {
        
        private List<String> factorys;
        
        private List<String> products;
        
        /**
         * product权限转换成的deviceId
         */
        private List<String> productDeviceIds;
        
        private List<String> subCustomers;
        
        private List<String> deviceIds;
        
        private List<String> lotTypes;
    }

}
