package com.guwave.datahub.bpms.app.service.dailyReport.impl;

import com.alibaba.fastjson2.JSON;
import com.guwave.datahub.bpms.app.service.dailyReport.CommonService;
import com.guwave.datahub.bpms.app.util.DateUtil;
import com.guwave.datahub.bpms.app.util.PdfUtil;
import com.guwave.datahub.bpms.common.constant.ChartData;
import com.guwave.datahub.bpms.common.constant.ChartType;
import com.guwave.datahub.bpms.common.constant.DataCode;
import com.guwave.datahub.bpms.common.constant.ReportModule;
import com.guwave.datahub.bpms.dao.repository.bpms.DailyReportResultRepository;
import com.guwave.datahub.bpms.dao.repository.dw.ManualCalculateTaskRepository;
import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.ManualTaskDetail;
import com.guwave.datahub.bpms.dao.vo.bpms.dailyReport.ManualTaskResult;
import com.guwave.onedata.dataware.common.contant.ManualType;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ManualModuleService implements CommonService {

    @Autowired
    private DailyReportResultRepository dailyReportResultRepository;

    @Autowired
    private ManualCalculateTaskRepository manualCalculateTaskRepository;

    public void generateManualTaskDetail(PdfUtil pdfUtil) throws IOException {

        List<ManualTaskDetail> manualTaskDetails = manualCalculateTaskRepository.findAllByUpdateTimeBetween(DateUtil.getStringFormattedStartTimeBeforeDays(1), DateUtil.getStringFormattedStartTimeBeforeDays(0))
                .stream().map(ManualTaskDetail::buildManualTaskDetail).collect(Collectors.toList());
        Map<ManualTaskDetail, Long> manualTaskMap = manualTaskDetails.stream().collect(Collectors.groupingBy(t -> t, Collectors.counting()));

        ManualTaskResult result = new ManualTaskResult();
        result.setSuccessUploadTaskCnt(manualTaskMap.getOrDefault(new ManualTaskDetail(ManualType.UPLOAD.getType(), ProcessStatus.SUCCESS.name()), 0L));
        result.setSuccessMergeTaskCnt(manualTaskMap.getOrDefault(new ManualTaskDetail(ManualType.MERGE.getType(), ProcessStatus.SUCCESS.name()), 0L));
        result.setSuccessMoveProductionTaskCnt(manualTaskMap.getOrDefault(new ManualTaskDetail(ManualType.MOVE_PRODUCTION.getType(), ProcessStatus.SUCCESS.name()), 0L));
        result.setFailUploadTaskCnt(manualTaskMap.getOrDefault(new ManualTaskDetail(ManualType.UPLOAD.getType(), ProcessStatus.FAIL.name()), 0L));
        result.setFailMergeTaskCnt(manualTaskMap.getOrDefault(new ManualTaskDetail(ManualType.MERGE.getType(), ProcessStatus.FAIL.name()), 0L));
        result.setFailMoveProductionTaskCnt(manualTaskMap.getOrDefault(new ManualTaskDetail(ManualType.MOVE_PRODUCTION.getType(), ProcessStatus.FAIL.name()), 0L));

        saveDailyReportResult(JSON.toJSONString(result), ReportModule.MANUAL, DataCode.MANUAL_TASK);

        pdfUtil.addSecondaryHeading("手动上传");
        pdfUtil.addBodyText("成功的upload任务数:" + result.getSuccessUploadTaskCnt());
        pdfUtil.addBodyText("成功的merge任务数:" + result.getSuccessMergeTaskCnt());
        pdfUtil.addBodyText("成功的move_production任务数:" + result.getSuccessMoveProductionTaskCnt());
        long failCnt = result.getFailUploadTaskCnt() + result.getFailMergeTaskCnt() + result.getFailMoveProductionTaskCnt();
        pdfUtil.addBodyText("失败的任务数:" + failCnt);

        if (failCnt > 0) {
            List<ChartData> manualFailTaskChartData = new ArrayList<>();
            manualFailTaskChartData.add(new ChartData(ManualType.UPLOAD.getType(), result.getFailUploadTaskCnt()));
            manualFailTaskChartData.add(new ChartData(ManualType.MERGE.getType(), result.getFailMergeTaskCnt()));
            manualFailTaskChartData.add(new ChartData(ManualType.MOVE_PRODUCTION.getType(), result.getFailMoveProductionTaskCnt()));
            pdfUtil.addChart(manualFailTaskChartData, ChartType.PIE, "手动上传失败任务分布");
        }
    }

    @Override
    public DailyReportResultRepository getDailyReportResultRepository() {
        return dailyReportResultRepository;
    }
}
