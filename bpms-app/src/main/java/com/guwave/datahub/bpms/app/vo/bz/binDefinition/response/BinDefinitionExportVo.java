package com.guwave.datahub.bpms.app.vo.bz.binDefinition.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Bin Definition导出VO
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@Schema(title = "Bin Definition导出数据")
public class BinDefinitionExportVo {

    @Schema(description = "测试程序名称", example = "TestProgram1")
    private String testProgram;

    @Schema(description = "Bin类型", example = "HBIN")
    private String binType;

    @Schema(description = "Bin编号", example = "1")
    private Long binNum;

    @Schema(description = "Bin名称", example = "Pass")
    private String binName;

    @Schema(description = "Bin P/F", example = "P")
    private String binPf;
}