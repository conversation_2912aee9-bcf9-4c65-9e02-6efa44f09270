package com.guwave.datahub.bpms.app.exception;

import com.guwave.datahub.bpms.app.web.response.ResponseCode;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * BpmsRpcInvokeException
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-02-27 14:00:23
 */
public class BpmsRpcInvokeException extends BpmsException {

    public BpmsRpcInvokeException() {
        super(ResponseCode.Internal.INTERNAL_RPC_INVOKE_ERROR);
    }

    public BpmsRpcInvokeException(Throwable e) {
        super(e, ResponseCode.Internal.INTERNAL_RPC_INVOKE_ERROR);
    }
}
