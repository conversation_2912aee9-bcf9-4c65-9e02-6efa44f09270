package com.guwave.datahub.bpms.app.vo.bz.binDefinition.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Bin定义导入结果VO
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BinDefinitionImportResultVo {
    
    /**
     * 总记录数
     */
    private Integer totalCount;
    
    /**
     * 成功导入数
     */
    private Integer successCount;
    
    /**
     * 失败数
     */
    private Integer failedCount;
    
    /**
     * 导入模式
     */
    private String importMode;
    
    /**
     * 结果消息
     */
    private String message;
}