package com.guwave.datahub.bpms.app.vo.bz.replay.request;

import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.LotType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Schema(title = "下拉框过滤条件请求体")
@Data
public class DataReplayFileDropDownFilterVo {

    @NotBlank(message = "过滤字段")
    @Schema(title = "当前的过滤字段", description = "当前的过滤字段，不能为空，取值为：TEST_AREA,FACTORY,STEP, EXCEPTION_MESSAGE", example = "STEP")
    private String filterField;

    @Schema(title = "testArea", example = "CP")
    private String testArea;

    @Schema(title = "factory列表", example = "[\"VC\"]")
    private List<String> factoryList;

    @Schema(title = "fileCategory", description = "只能单选，取值为：STDF,RAW_DATA,WAT", example = "STDF")
    private FileCategory fileCategory;

    @Schema(title = "重播步骤", example = "[\"4100\"]")
    private List<Integer> stepList;

    @Schema(title = "入库状态列表", example = "[\"FAIL\"]")
    private List<String> processStatusList;

    @Schema(title = "异常信息", example = "RAW_DATA文件转换异常")
    private String exceptionMessage;


    @Schema(title = "createTime", description = "createTime", example = "[\"2023-09-20 08:14:13\", \"2023-09-21 08:14:13\"]")
    private List<String> createTime;

    @Schema(title = "updateTime", description = "updateTime", example = "[\"2023-09-20 08:14:13\", \"2023-09-21 08:14:13\"]")
    private List<String> updateTime;

}
