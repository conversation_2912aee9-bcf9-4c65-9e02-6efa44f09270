package com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.response;

import com.guwave.datahub.bpms.dao.vo.bpms.dataIntegrity.DataIntegrityDeleteLogDropDown;
import com.guwave.datahub.bpms.dao.vo.bpms.dataIntegrity.DataIntegrityDropDown;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Schema(title = "下拉框结果")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataIntegrityQueryDropDownVo {
    @Schema(title = "下拉框结果值", example = "NT41A")
    private String value;
    @Schema(title = "页面显示下拉框结果值", example = "NT41A")
    private String label;

    public static DataIntegrityQueryDropDownVo of(DataIntegrityDropDown dropDown) {
        return new DataIntegrityQueryDropDownVo(dropDown.getName(), dropDown.getShowName());
    }

    public static DataIntegrityQueryDropDownVo of(DataIntegrityDeleteLogDropDown dropDown) {
        return new DataIntegrityQueryDropDownVo(dropDown.getName(), dropDown.getShowName());
    }

    public static DataIntegrityQueryDropDownVo of(String str) {
        return new DataIntegrityQueryDropDownVo(str, str);
    }
}
