package com.guwave.datahub.bpms.app.vo.bz.dataAccuracy.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.guwave.datahub.bpms.common.constant.DataAccuracyPageDropdownField;
import com.guwave.datahub.bpms.dao.vo.dw.WaferVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Schema(title = "下拉框过滤条件请求体")
@Data
public class DataAccuracyDropDownFilterVo {

    @Schema(title = "当前的过滤字段", description = "当前的过滤字段，不能为空，取值为：FACTORY,DEVICE_ID,TEST_AREA,TEST_STAGE,LOT_ID,WAFER_NO,SBLOT_ID,LOT_TYPE,MES_MATCH_FLAG,DATA_ACCURACY_CHECK", example = "FACTORY")
    @NotNull(message = "过滤字段不能为空")
    private DataAccuracyPageDropdownField filterField;

    @Schema(description = "子客户", example = "[\"AMD\"]")
    private List<String> subCustomerList;

    @Schema(description = "工厂", example = "[\"AMD\"]")
    private List<String> factoryList;

    @Schema(description = "测试区域", example = "CP")
    private String testArea;

    @Schema(description = "deviceId", example = "[\"deviceId-1\"]")
    private List<String> deviceIdList;

    @Schema(description = "testStage", example = "[\"CP2\"]")
    private List<String> testStageList;

    @Schema(description = "lotId", example = "[\"lotId-1\"]")
    private List<String> lotIdList;

    @Schema(description = "waferNo", example = "[\"14\"]")
    private List<String> waferNoList;

    @Schema(description = "sblotId", example = "[\"sblotId-1\"]")
    private List<String> sblotIdList;

    @Schema(description = "lotType", example = "[\"deviceId-1\"]")
    private List<String> lotTypeList;

    @Schema(description = "mesMatchFlag", example = "[\"deviceId-1\"]")
    private List<String> mesMatchFlagList;

    @Schema(description = "dataAccuracyCheck", example = "[\"deviceId-1\"]")
    private List<String> dataAccuracyCheckList;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "startTime", example = "2023-09-20 08:14:13")
    private LocalDateTime startTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "endTime", example = "2023-09-21 08:14:13")
    private LocalDateTime endTime;

    public static void fillWafer(DataAccuracyDropDownFilterVo data, boolean isCp, List<WaferVo> waferVoList) {
        data.setSubCustomerList(waferVoList.stream().map(WaferVo::getSubCustomer).distinct().collect(Collectors.toList()));
        data.setFactoryList(waferVoList.stream().map(WaferVo::getFactory).distinct().collect(Collectors.toList()));
        data.setDeviceIdList(waferVoList.stream().map(WaferVo::getDeviceId).distinct().collect(Collectors.toList()));
        data.setTestStageList(waferVoList.stream().map(WaferVo::getTestStage).distinct().collect(Collectors.toList()));
        data.setLotIdList(waferVoList.stream().map(WaferVo::getLotId).distinct().collect(Collectors.toList()));
        data.setLotTypeList(waferVoList.stream().map(WaferVo::getLotType).distinct().collect(Collectors.toList()));
        if (isCp) {
            data.setWaferNoList(waferVoList.stream().map(WaferVo::getWaferNo).distinct().collect(Collectors.toList()));
            data.setSblotIdList(Collections.emptyList());
        } else {
            data.setWaferNoList(Collections.emptyList());
            data.setSblotIdList(waferVoList.stream().map(WaferVo::getSblotId).distinct().collect(Collectors.toList()));
        }
    }

}

