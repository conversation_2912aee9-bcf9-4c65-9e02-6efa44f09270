package com.guwave.datahub.bpms.app.scheduler;

import com.guwave.datahub.bpms.common.constant.ExecuteEngine;
import com.guwave.datahub.bpms.dao.domain.schedule.ScheduleTaskDefinition;
import com.guwave.datahub.bpms.dao.domain.schedule.ScheduleTaskRecord;
import com.guwave.datahub.bpms.dao.repository.bpms.ScheduleTaskDefinitionRepository;
import com.guwave.datahub.bpms.dao.repository.bpms.ScheduleTaskRecordRepository;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class TaskDispatchService {

    private static final Logger LOGGER = LoggerFactory.getLogger(TaskDispatchService.class);

    private final ScheduleTaskDefinitionRepository scheduleTaskDefinitionRepository;
    private final ScheduleTaskRecordRepository scheduleTaskRecordRepository;

    @Autowired
    private TaskSchedulerService taskService;

    public TaskDispatchService(ScheduleTaskDefinitionRepository scheduleTaskDefinitionRepository,
                               ScheduleTaskRecordRepository scheduleTaskRecordRepository) {
        this.scheduleTaskDefinitionRepository = scheduleTaskDefinitionRepository;
        this.scheduleTaskRecordRepository = scheduleTaskRecordRepository;
    }

    /**
     * 执行具体逻辑
     *
     * @param taskDefinition {@link ScheduleTaskDefinition}
     */
    public void doTask(ScheduleTaskDefinition taskDefinition) {
        if (taskDefinition == null) {
            LOGGER.warn("任务定义为空，无法执行任务");
            return;
        }

        LOGGER.info("开始生成任务: {}", taskDefinition.getTaskName());

        // 生成任务记录
        ScheduleTaskRecord taskRecord = taskService.generateTaskRecord(taskDefinition);
        if (taskRecord == null) {
            LOGGER.warn("生成任务记录失败，无法执行任务: {}", taskDefinition.getTaskName());
            return;
        }

        saveTaskRecord(taskRecord);

        if (taskRecord.getExecuteEngine() == ExecuteEngine.SPARK) {
            taskService.executeAsyncTask(taskRecord);
        }
    }

    /**
     * 保存任务记录
     *
     * @param taskRecord {@link ScheduleTaskRecord}
     */
    private void saveTaskRecord(ScheduleTaskRecord taskRecord) {
        if (taskRecord == null) {
            LOGGER.warn("任务记录为空，无法保存");
            return;
        }

        try {
            scheduleTaskRecordRepository.save(taskRecord);
            LOGGER.info("任务记录保存成功: {}", taskRecord.getTaskName());
        } catch (Exception e) {
            LOGGER.error("保存任务记录异常: {}", ExceptionUtils.getStackTrace(e));
            taskRecord
                    .setProcessStatus(ProcessStatus.FAIL)
                    .setErrorMessage(ExceptionUtils.getStackTrace(e))
                    .setUpdateTime(new Date());
        }
    }
}
