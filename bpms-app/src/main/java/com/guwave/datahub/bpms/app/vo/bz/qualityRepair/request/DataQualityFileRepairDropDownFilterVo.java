package com.guwave.datahub.bpms.app.vo.bz.qualityRepair.request;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Schema(title = "文件修复记录下拉框")
public class DataQualityFileRepairDropDownFilterVo {

    @Schema(description = "需要查询的字段", example = "DEVICE")
    @NotNull(message = "过滤字段不能为空")
    private String filterField;

    @Schema(description = "subCustomer列表", example = "[\"AMD\"]")
    private List<String> subCustomerList;

    @Schema(description = "device列表", example = "[\"SS521BB-DEFAULT\"]")
    private List<String> deviceIdList;

    @Schema(description = "testStage列表", example = "[\"CP1\"]")
    private List<String> testStageList;

    @Schema(description = "lotId列表", example = "[\"NT41A\"]")
    private List<String> lotIdList;

    @Schema(description = "fileName列表", example = "[\"CP1_RT_2022-10-10.stdf\"]")
    private List<String> fileNameList;

    @Schema(title = "repairStatusList", description = "修改状态列表", example = "[\"SUCCESS\"]")
    private List<String> repairStatusList;

    @Schema(title = "repairCategoryList", description = "修改类别列表", example = "[\"DATA_ACCURACY\"]")
    private List<String> repairCategoryList;

    @Schema(title = "lastRepairTypeList", description = "修改类型列表", example = "[\"关键字段补充\"]")
    private List<String> lastRepairTypeList;

    @Schema(title = "repairTimesList", description =  "修复次数列表", example = "[5]")
    private List<Integer> repairTimesList;

    public void validCondition() {
        subCustomerList = convertEmpty(subCustomerList);
        deviceIdList = convertEmpty(deviceIdList);
        testStageList = convertEmpty(testStageList);
        lotIdList = convertEmpty(lotIdList);
        fileNameList = convertEmpty(fileNameList);
        repairStatusList = convertEmpty(repairStatusList);
        repairCategoryList = convertEmpty(repairCategoryList);
        lastRepairTypeList = convertEmpty(lastRepairTypeList);
        repairTimesList = convertEmpty(repairTimesList);
    }

    private <T> List<T> convertEmpty(List<T> list) {
        return CollectionUtils.isEmpty(list) ? null : list;
    }
}
