package com.guwave.datahub.bpms.app.service.bz.handler.clearRulePreview.parseHandler;

import com.guwave.datahub.bpms.dao.domain.clearRulePreview.ClearRulePreviewRecord;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.TestArea;
import org.springframework.stereotype.Component;

@Component
public class FtStdfParseHandler extends StdfParseHandler {
    @Override
    public boolean isSuppport(ClearRulePreviewRecord record) {
        return record.getGivenTestArea() == TestArea.FT && record.getFileCategory() == FileCategory.STDF;
    }
}
