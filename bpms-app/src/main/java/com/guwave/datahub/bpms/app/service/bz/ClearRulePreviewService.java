package com.guwave.datahub.bpms.app.service.bz;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.exception.BpmsParamException;
import com.guwave.datahub.bpms.app.service.bz.handler.clearRulePreview.convert.ConvertHandler;
import com.guwave.datahub.bpms.app.service.bz.handler.clearRulePreview.parseHandler.ParseHandler;
import com.guwave.datahub.bpms.app.service.bz.handler.clearRulePreview.preHandler.PreHandler;
import com.guwave.datahub.bpms.app.util.FileUtil;
import com.guwave.datahub.bpms.app.util.HdfsUtil;
import com.guwave.datahub.bpms.app.util.ThreadPoolUtils;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model.ClearRulePreviewResult;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model.ConfResult;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.model.ParseField;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.request.ClearRulePreviewDropDownFilterVo;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.request.ClearRulePreviewRecordQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.request.ClearRulePreviewRecordUploadVo;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.request.ClearRulePreviewTestVo;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.response.ClearRulePreviewDropDownVo;
import com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.response.ClearRulePreviewRecordVo;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.dao.domain.clearRulePreview.ClearRulePreviewRecord;
import com.guwave.datahub.bpms.dao.repository.bpms.ClearRulePreviewRecordRepository;
import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.curator.shaded.com.google.common.collect.Sets;
import org.apache.hadoop.io.IOUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_FORMAT;

@Slf4j
@Service
public class ClearRulePreviewService {

    private static final String threadPrefix = "clearRulePreview";
    private static final AtomicInteger threadActiveCount = new AtomicInteger(0);
    private static ThreadPoolExecutor threadPoolExecutor = null;
    private static final Field[] clearRulePreviewRecordFields = ClearRulePreviewRecord.class.getDeclaredFields();

    static {
        for (Field field : clearRulePreviewRecordFields) {
            field.setAccessible(true);
        }
    }

    @Value("${spring.upload.baseDir}")
    private String uploadBaseDir;
    @Value("${spring.parse.baseDir}")
    private String parseBaseDir;
    @Value("${gdp.file.hdfsRootPath}")
    private String hdfsRootPath;
    @Value("${spring.clearRulePreview.threadCnt}")
    private Integer clearRulePreviewThreadCnt;

    @Autowired
    private ClearRulePreviewRecordRepository clearRulePreviewRecordRepository;
    @Autowired
    private HdfsUtil hdfsUtil;
    @Autowired
    private List<ConvertHandler> convertHandlers;
    @Autowired
    private List<PreHandler> preHandlers;
    @Autowired
    private List<ParseHandler> parseHandlers;

    @PostConstruct
    public void init() {
        new File(uploadBaseDir).mkdirs();
        new File(parseBaseDir).mkdirs();
        threadPoolExecutor = ThreadPoolUtils.getNewThreadPoolExecutor(threadPrefix, clearRulePreviewThreadCnt, clearRulePreviewThreadCnt, clearRulePreviewThreadCnt);
    }

    public List<ClearRulePreviewDropDownVo> filterDropDown(ClearRulePreviewDropDownFilterVo filterVo) {
        log.info("filterDropDown");
        List<String> results = clearRulePreviewRecordRepository.filterDropDown(RequestContext.getCustomer(), filterVo.getTestArea(), filterVo.getDeviceId(), filterVo.getLotId(), filterVo.getWaferNo(), filterVo.getLotType(), filterVo.getTestStage(), filterVo.getFactory(), filterVo.getFactorySite(), filterVo.getSblotId(), filterVo.getExceptionType(), filterVo.getFilterField());
        return ClearRulePreviewDropDownVo.ofList(results.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
    }

    public PageableDataVo<ClearRulePreviewRecordVo> queryRecord(ClearRulePreviewRecordQueryVo queryVo) {
        log.info("queryRecord");
        Pageable pageable = PageRequest.of(queryVo.getJpaPageIndex(), queryVo.getPageSize());
        Page<ClearRulePreviewRecord> clearRulePreviewRecords = clearRulePreviewRecordRepository.queryRecord(RequestContext.getCustomer(), queryVo.getTestArea(), queryVo.getDeviceId(), queryVo.getLotId(), queryVo.getWaferNo(), queryVo.getLotType(), queryVo.getTestStage(), queryVo.getFactory(), queryVo.getFactorySite(), queryVo.getSblotId(), queryVo.getFileCategory(), queryVo.getProcessStatus(), queryVo.getRemoteOriginFilePath(), queryVo.getRemoteOriginFileName(), queryVo.getFileName(), queryVo.getExceptionType(), queryVo.getStartTime(), queryVo.getEndTime(), pageable);
        Page<ClearRulePreviewRecordVo> clearRulePreviewRecordVos = clearRulePreviewRecords.map(t -> {
            ClearRulePreviewRecordVo target = new ClearRulePreviewRecordVo();
            BeanUtils.copyProperties(t, target);
            target.setGivenTestArea(t.getGivenTestArea() == null ? null : t.getGivenTestArea().getArea());
            target.setFileCategory(t.getFileCategory() == null ? null : t.getFileCategory().getCategory());
            target.setTestArea(t.getTestArea() == null ? null : t.getTestArea().getArea());
            target.setLotType(t.getLotType() == null ? null : t.getLotType().getType());
            target.setProcessStatus(t.getProcessStatus() == null ? null : t.getProcessStatus().name());
            target.setExceptionType(t.getExceptionType() == null ? null : t.getExceptionType().getType());
            target.setCreateTime(DateUtil.format(t.getCreateTime(), NORM_DATETIME_FORMAT));
            target.setUpdateTime(DateUtil.format(t.getUpdateTime(), NORM_DATETIME_FORMAT));
            return target;
        });
        return PageableDataVo.of(clearRulePreviewRecordVos);
    }

    public void upload(ClearRulePreviewRecordUploadVo uploadVo) {
        log.info("upload");
        validateUploadVo(uploadVo);
        log.info("上传文件 {}", uploadVo.getFile().getOriginalFilename());
        File localDir = new File(new File(uploadBaseDir).getAbsolutePath(), UUID.randomUUID().toString());
        localDir.mkdirs();
        try {
            File localUploadDir = new File(localDir, "upload");
            localUploadDir.mkdirs();
            File localCompressDir = new File(localDir, "compress");
            localCompressDir.mkdirs();
            File uploadFile = new File(localUploadDir, uploadVo.getFile().getOriginalFilename());

            // 上传文件下载到本地
            try (InputStream inputStream = uploadVo.getFile().getInputStream()) {
                FileUtils.copyToFile(inputStream, uploadFile);
            } catch (Exception e) {
                log.info("获取上传文件 {} 异常：", uploadFile.getAbsolutePath(), e);
                throw new BpmsException(ResponseCode.Common.FILE_READ_ERROR, "IO异常");
            }

            // 上传文件递归解压
            ArrayList<File> deCompressFiles = new ArrayList<>();
            try {
                FileUtil.deCompressFile(deCompressFiles, uploadFile, FileUtil.canUnCompressSuffixSet);
            } catch (Exception e) {
                log.info("解压文件 {} 异常：", uploadFile.getAbsolutePath(), e);
                throw new BpmsException(ResponseCode.Common.FILE_DECOMPRESS_FAIL);
            }

            // 所有解压后的文件压缩上传到hdfs
            ArrayList<ClearRulePreviewRecord> clearRulePreviewRecords = new ArrayList<>();
            for (File deCompressFile : deCompressFiles) {
                ClearRulePreviewRecord clearRulePreviewRecord = buildClearRulePreviewRecord(uploadVo, uploadFile, deCompressFile);

                File zipCompressFile = new File(localCompressDir, deCompressFile.getName() + Constant.POINT + FileType.ZIP.getType());
                FileUtil.compressFileToZip(deCompressFile.getParent(), deCompressFile.getName(), zipCompressFile.getParent());
                clearRulePreviewRecord.setFileName(zipCompressFile.getName());
                clearRulePreviewRecord.setFileSize(zipCompressFile.length());

                String hdfsPath = String.format(hdfsRootPath, clearRulePreviewRecord.getCustomer(), clearRulePreviewRecord.getGivenTestArea().getArea(), clearRulePreviewRecord.getFactory(), clearRulePreviewRecord.getFileCategory().getCategory(), DateFormatUtils.format(clearRulePreviewRecord.getCreateTime(), "yyyyMMdd"), UUID.randomUUID());
                hdfsUtil.uploadToHDFSByFileSystem(zipCompressFile.getAbsolutePath(), hdfsPath, 1);
                clearRulePreviewRecord.setHdfsPath(new File(hdfsPath, clearRulePreviewRecord.getFileName()).getPath());

                clearRulePreviewRecords.add(clearRulePreviewRecord);
            }

            // 保存数据库
            clearRulePreviewRecordRepository.saveAll(clearRulePreviewRecords);
        } finally {
            FileUtils.deleteQuietly(localDir);
        }

    }

    private void validateUploadVo(ClearRulePreviewRecordUploadVo uploadVo) {
        StringBuilder errorMsg = new StringBuilder();
        if (!Sets.newHashSet(TestArea.CP, TestArea.FT).contains(uploadVo.getGivenTestArea())) {
            errorMsg.append("testArea 非法！\n");
        }
        if (!Sets.newHashSet(FileCategory.STDF, FileCategory.RAW_DATA).contains(uploadVo.getFileCategory())) {
            errorMsg.append("fileCategory 非法！\n");
        }
        if (uploadVo.getFile() == null || uploadVo.getFile().getSize() == 0) {
            errorMsg.append("文件不能为空！\n");
        } else if (uploadVo.getFile().getSize() > 10L * 1024L * 1024L * 1024L) {
            errorMsg.append("文件不能超过10GB！\n");
        }
        String error = errorMsg.toString();
        if (StringUtils.isNotBlank(error)) {
            throw new BpmsParamException(error);
        }
    }

    private ClearRulePreviewRecord buildClearRulePreviewRecord(ClearRulePreviewRecordUploadVo uploadVo, File uploadFile, File deCompressFile) {
        Date date = new Date();
        ClearRulePreviewRecord record = new ClearRulePreviewRecord();
        record.initParseData();

        record.setCustomer(RequestContext.getCustomer());
        record.setSubCustomer(RequestContext.getSubCustomer());
        record.setGivenTestArea(uploadVo.getGivenTestArea());
        record.setFactory(uploadVo.getFactory());
        record.setFactorySite(uploadVo.getFactorySite());
        record.setFab(Constant.EMPTY);
        record.setFabSite(Constant.EMPTY);
        record.setFileCategory(uploadVo.getFileCategory());
        record.setOriginFileName(deCompressFile.getName());
        record.setOriginFileSize(deCompressFile.length());
        record.setRemoteOriginFileName(uploadFile.getName());
        record.setRemoteOriginFilePath(uploadVo.getRemoteOriginFilePath());
        record.setFtpPath(new File(record.getRemoteOriginFilePath(), record.getRemoteOriginFileName()).getPath());
        record.setProcessStatus(ProcessStatus.CREATE);
        record.setDeleteFlag(0);
        record.setCreateTime(date);
        record.setUpdateTime(date);
        record.setCreateUser(RequestContext.getUserName());
        record.setUpdateUser(RequestContext.getUserName());
        return record;
    }

    public void retry(List<Long> ids) {
        log.info("retry");
        ArrayList<ProcessStatus> processStatuses = Lists.newArrayList(ProcessStatus.SUCCESS, ProcessStatus.FAIL);
        List<ClearRulePreviewRecord> clearRulePreviewRecords = clearRulePreviewRecordRepository.findByIdInAndDeleteFlag(ids, 0);
        long expireTime = 24L * 60L * 60L * 1000L;
        List<ClearRulePreviewRecord> updateRecords = clearRulePreviewRecords.stream().filter(t -> processStatuses.contains(t.getProcessStatus()) || (t.getProcessStatus() == ProcessStatus.PROCESSING && (System.currentTimeMillis() - t.getUpdateTime().getTime()) > expireTime)).peek(t -> {
            t.initParseData();

            t.setProcessStatus(ProcessStatus.CREATE);
            t.setUpdateTime(new Date());
            t.setUpdateUser(RequestContext.getUserName());
        }).collect(Collectors.toList());
        clearRulePreviewRecordRepository.saveAll(updateRecords);
    }

    public void downloadDetail(HttpServletResponse response, Long id) {
        log.info("downloadDetail");
        List<ClearRulePreviewRecord> clearRulePreviewRecords = clearRulePreviewRecordRepository.findByIdInAndDeleteFlag(Lists.newArrayList(id), 0);
        if (CollectionUtils.isEmpty(clearRulePreviewRecords)) {
            throw new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT);
        }
        ClearRulePreviewRecord clearRulePreviewRecord = clearRulePreviewRecords.get(0);
        if (clearRulePreviewRecord.getProcessStatus() != ProcessStatus.SUCCESS || StringUtils.isBlank(clearRulePreviewRecord.getPreviewDetailFileHdfsPath())) {
            throw new BpmsException(ResponseCode.Business.CLEAR_RULE_PREVIEW_NOT_FIND_DETAIL_FILE);
        }
        log.info("下载文件 {}", clearRulePreviewRecord.getPreviewDetailFileHdfsPath());
        File file = new File(clearRulePreviewRecord.getPreviewDetailFileHdfsPath());
        response.addHeader("Content-Disposition", "attachment;filename=" + new String(file.getName().getBytes()));
        response.setContentType("application/octet-stream");
        try (InputStream inputStream = hdfsUtil.openFile(clearRulePreviewRecord.getPreviewDetailFileHdfsPath()); ServletOutputStream outputStream = response.getOutputStream()) {
            IOUtils.copyBytes(inputStream, outputStream, 4096, true);
        } catch (Exception e) {
            log.info("下载文件 {} 异常：", clearRulePreviewRecord.getPreviewDetailFileHdfsPath(), e);
            throw new BpmsException(ResponseCode.Common.FILE_READ_ERROR, "IO异常");
        }
    }


    /**
     * 解析文件
     */
    @Scheduled(fixedDelayString = "${spring.scheduler.polling.milliseconds}")
    @ScheduleSwitch
    public void runParseJob() {
        if (threadActiveCount.get() >= threadPoolExecutor.getCorePoolSize()) {
            log.info("线程池已达最大处理数！");
            return;
        }
        List<ClearRulePreviewRecord> clearRulePreviewRecords = clearRulePreviewRecordRepository.findByProcessStatusAndDeleteFlagOrderByUpdateTimeAscIdAsc(ProcessStatus.CREATE, 0, Pageable.ofSize(1));
        if (CollectionUtils.isEmpty(clearRulePreviewRecords)) {
            return;
        }
        ClearRulePreviewRecord record = clearRulePreviewRecords.get(0);
        int updateCnt = clearRulePreviewRecordRepository.updateProcessing(record.getId());
        if (updateCnt == 0) {
            return;
        }
        record.setProcessStatus(ProcessStatus.PROCESSING);
        record.setUpdateTime(new Date());

        threadActiveCount.incrementAndGet();
        threadPoolExecutor.execute(() -> {
            try {
                parseFile(record);
            } finally {
                threadActiveCount.decrementAndGet();
            }
        });
    }

    public void parseFile(ClearRulePreviewRecord record) {
        // 开始解析文件
        log.info("开始解析文件 {} ", record.getFileName());
        File localDir = new File(new File(parseBaseDir).getAbsolutePath(), UUID.randomUUID().toString());
        localDir.mkdirs();

        ClearRulePreviewResult clearRulePreviewResult = new ClearRulePreviewResult();
        clearRulePreviewResult.getMetaData().setFileName(record.getOriginFileName());
        clearRulePreviewResult.getMetaData().setRemoteOriginFilePath(record.getRemoteOriginFilePath());
        clearRulePreviewResult.getMetaData().setRemoteOriginFileName(record.getRemoteOriginFileName());
        clearRulePreviewResult.getConfResults().addAll(Arrays.stream(ParseField.values()).map(ConfResult::of).collect(Collectors.toList()));

        try {
            // 下载文件
            File zipCompressFile = new File(localDir, record.getFileName());
            try {
                hdfsUtil.downloadToLocal(false, record.getHdfsPath(), zipCompressFile.getAbsolutePath());
            } catch (Exception e) {
                throw new BpmsException(e, ResponseCode.Common.FILE_READ_ERROR, "IO异常");
            }

            // 解压文件
            File deCompressFile = new File(new File(zipCompressFile.getParent(), FileUtil.removeFileSuffix(zipCompressFile.getName(), FileType.ZIP)), FileUtil.removeFileSuffix(zipCompressFile.getName(), FileType.ZIP));
            try {
                hdfsUtil.unCompress(deCompressFile.getParent(), zipCompressFile.getAbsolutePath());
            } catch (Exception e) {
                throw new BpmsException(e, ResponseCode.Common.FILE_READ_ERROR, "IO异常");
            }

            // 转换文件
            for (ConvertHandler convertHandler : convertHandlers) {
                if (convertHandler.isSupport(record)) {
                    deCompressFile = convertHandler.convertFile(record, deCompressFile);
                    break;
                }
            }

            // 预解析文件
            for (PreHandler preHandler : preHandlers) {
                if (preHandler.isSuppport(record)) {
                    preHandler.preDealFile(record, deCompressFile, clearRulePreviewResult);
                    break;
                }
            }

            // 正式解析文件
            for (ParseHandler parseHandler : parseHandlers) {
                if (parseHandler.isSuppport(record)) {
                    parseHandler.parseFile(record, deCompressFile, clearRulePreviewResult);
                    break;
                }
            }

            record.setProcessStatus(ProcessStatus.SUCCESS);
        } catch (Exception e) {
            log.info("解析文件 {} 异常：", record.getFileName(), e);
            FileLoadException fileLoadException = e instanceof FileLoadException ? (FileLoadException) e : new FileLoadException(FileLoadExceptionInfo.PARSE_FILE_EXCEPTION, ExceptionUtils.getStackTrace(e), null);
            record.setExceptionType(fileLoadException.getExceptionType());
            record.setExceptionMessage(fileLoadException.getExceptionMessage());
            record.setErrorMessage(fileLoadException.getErrorMessage());
            record.setFailedFields(fileLoadException.getFailedFields());
            record.setProcessStatus(ProcessStatus.FAIL);
        } finally {
            FileUtils.deleteQuietly(localDir);

            fillConfResultWithRecord(clearRulePreviewResult.getConfResults(), record);
            record.setPreviewResult(JSON.toJSONString(clearRulePreviewResult, SerializerFeature.WriteMapNullValue));
            record.setUpdateTime(new Date());
            clearRulePreviewRecordRepository.save(record);
        }
    }

    private void fillConfResultWithRecord(List<ConfResult> confResults, ClearRulePreviewRecord record) {
        Map<String, ConfResult> confResultMap = confResults.stream().collect(Collectors.toMap(ConfResult::getField, t -> t, (v1, v2) -> v1));
        for (Field field : clearRulePreviewRecordFields) {
            ConfResult confResult = confResultMap.get(field.getName());
            if (confResult != null) {
                try {
                    Object value = field.get(record);
                    if (field.getType() == Map.class && value != null) {
                        value = JSON.toJSONString(value);
                    }
                    if (value instanceof TestArea) {
                        value = ((TestArea) value).getArea();
                    }
                    confResult.setValue(value);
                } catch (IllegalAccessException e) {
                    log.info("从 {} 获取 {} 异常：", JSON.toJSONString(record), field.getName(), e);
                }
            }
        }
    }

    public Object test(ClearRulePreviewTestVo clearRulePreviewTestVo) {
        List<ClearRulePreviewRecord> clearRulePreviewRecords = clearRulePreviewRecordRepository.findByIdInAndDeleteFlag(Lists.newArrayList(clearRulePreviewTestVo.getId()), 0);
        if (CollectionUtils.isEmpty(clearRulePreviewRecords)) {
            throw new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT);
        }
        ClearRulePreviewRecord record = clearRulePreviewRecords.get(0);
        for (PreHandler preHandler : preHandlers) {
            if (preHandler.isSuppport(record)) {
                return preHandler.test(clearRulePreviewTestVo.getMetaData(), clearRulePreviewTestVo.getRuleExpression(), clearRulePreviewTestVo.getField());
            }
        }
        throw new BpmsException(ResponseCode.Business.CLEAR_RULE_PREVIEW_NOT_SUPPORT_FILE);
    }
}
