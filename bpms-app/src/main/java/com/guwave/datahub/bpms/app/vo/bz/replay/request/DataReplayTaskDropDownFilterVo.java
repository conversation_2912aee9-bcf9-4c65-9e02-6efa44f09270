package com.guwave.datahub.bpms.app.vo.bz.replay.request;

import com.guwave.onedata.dataware.common.contant.LotType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(title = "重播任务下拉框过滤条件请求体")
@Data
public class DataReplayTaskDropDownFilterVo {

    @NotBlank(message = "过滤字段")
    @Schema(title = "当前的过滤字段", description = "当前的过滤字段，不能为空，取值为：LOT_ID,WAFER_NO,TEST_STAGE,EXCEPTION_MESSAGE", example = "LOT_ID")
    private String filterField;

    @NotBlank(message = "下拉框页面类型")
    @Schema(title = "下拉框页面类型", description = "下拉框页面类型，取值为：REPLAY_TASK_PAGE", example = "REPLAY_TASK_PAGE")
    private String dropDownPageType;

    @Schema(title = "id", example = "1")
    @NotNull(message = "重播任务Id")
    private Long id;

    @Schema(title = "lotId列表", example = "[]")
    private List<String> lotIdList;

    @Schema(title = "waferNo列表", example = "[]")
    private List<String> waferNoList;

    @Schema(title = "testStage列表", example = "[\"CP1\", \"CP2\", \"CP3\"]")
    private List<String> testStageList;

    @Schema(title = "入库状态列表", example = "[\"FAIL\"]")
    private List<String> statusList;

    @Schema(title = "任务异常信息列表", example = "[\"lotType关键字段为空\"]")
    private List<String> exceptionMessageList;

}
