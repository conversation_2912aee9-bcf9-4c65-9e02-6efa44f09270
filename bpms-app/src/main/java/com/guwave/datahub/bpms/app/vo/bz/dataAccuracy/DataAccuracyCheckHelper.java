package com.guwave.datahub.bpms.app.vo.bz.dataAccuracy;

import com.guwave.datahub.bpms.common.constant.DataAccuracyCheckEnum;
import lombok.Data;

import java.util.Comparator;

import static com.guwave.datahub.bpms.common.constant.DataAccuracyCheckEnum.DUP_RETEST_INPUT_LESS;
import static com.guwave.datahub.bpms.common.constant.DataAccuracyCheckEnum.DUP_RETEST_INPUT_MORE;
import static com.guwave.datahub.bpms.common.constant.DataAccuracyCheckEnum.INPUT_LESS;
import static com.guwave.datahub.bpms.common.constant.DataAccuracyCheckEnum.INPUT_MORE;


@Data
public class DataAccuracyCheckHelper {

    private String fieldValue;
    private String showName;
    private DataAccuracyCheckEnum typeEnum;


    public static DataAccuracyCheckHelper of(String fieldValue) {
        DataAccuracyCheckHelper dataAccuracyCheck = new DataAccuracyCheckHelper();
        dataAccuracyCheck.setFieldValue(fieldValue);
        dataAccuracyCheck.setTypeEnum(DataAccuracyCheckEnum.of(fieldValue));
        if (INPUT_MORE.equals(dataAccuracyCheck.getTypeEnum()) || INPUT_LESS.equals(dataAccuracyCheck.getTypeEnum())) {
            dataAccuracyCheck.setShowName(String.format(dataAccuracyCheck.getTypeEnum().getShowName(), fieldValue.split("_")[0]));
        } else if (DUP_RETEST_INPUT_MORE.equals(dataAccuracyCheck.getTypeEnum()) || DUP_RETEST_INPUT_LESS.equals(dataAccuracyCheck.getTypeEnum())) {
            dataAccuracyCheck.setShowName(String.format(dataAccuracyCheck.getTypeEnum().getShowName(), fieldValue.split("_")[2]));
        } else {
            dataAccuracyCheck.setShowName(dataAccuracyCheck.getTypeEnum().getShowName());
        }
        return dataAccuracyCheck;
    }

    public static String getShowName(String fieldValue) {
        return DataAccuracyCheckHelper.of(fieldValue).getShowName();
    }

    public static class DataAccuracyCheckComparator implements Comparator<DataAccuracyCheckHelper> {

        @Override
        public int compare(DataAccuracyCheckHelper o1, DataAccuracyCheckHelper o2) {
            if (o1.getTypeEnum().getOrder().equals(o2.getTypeEnum().getOrder())) {
                return o1.getFieldValue().compareTo(o2.getFieldValue());
            }
            return o1.getTypeEnum().getOrder().compareTo(o2.getTypeEnum().getOrder());
        }
    }
}
