package com.guwave.datahub.bpms.app.controller;

import com.guwave.datahub.bpms.app.annotation.Auth;
import com.guwave.datahub.bpms.app.annotation.Log;
import com.guwave.datahub.bpms.app.service.bz.WarehousingRuleService;
import com.guwave.datahub.bpms.app.vo.bz.warehousingRule.response.DropDownVo;
import com.guwave.datahub.bpms.app.vo.bz.warehousingRule.request.*;
import com.guwave.datahub.bpms.app.vo.bz.warehousingRule.response.*;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.ResponseVo;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Min;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@Log
@Auth
//@License
@Validated
@Slf4j
@RestController
@AllArgsConstructor
@Tag(name = "WarehousingConfigureController", description = "入库规则配置",
        externalDocs = @ExternalDocumentation(description = "需求文档",
                url = "https://loop.cloud.microsoft/p/eyJ1IjoiaHR0cHM6Ly9ndXdhdmUuc2hhcmVwb2ludC5jb20vY29udGVudHN0b3JhZ2UvQ1NQXzVmODkxNGNhLWJiNjAtNDk5NS1iZWI0LTM2NWQ0OTliZGZkNT9uYXY9Y3owbE1rWmpiMjUwWlc1MGMzUnZjbUZuWlNVeVJrTlRVRjgxWmpnNU1UUmpZUzFpWWpZd0xUUTVPVFV0WW1WaU5DMHpOalZrTkRrNVltUm1aRFVtWkQxaUpUSXhlV2hUU2xneVF6ZHNWVzB0ZEVSYVpGTmFkbVl4VlUxVk0ySm5kMGh5YUVkd1NGaFVTRlJtVlRSa05VRjFVR05WV0hsc1FWRktiRlZFUWpaNFNtRnNOU1ptUFRBeE1rTlJORFJKVGtvelJrVlZSVmxMV1RSS1FWcFhRa1ZHUTFaVlUwVkpSRk1tWXowbE1rWW1ZVDFNYjI5d1FYQndKbkE5SlRRd1pteDFhV1I0SlRKR2JHOXZjQzF3WVdkbExXTnZiblJoYVc1bGNpWjRQU1UzUWlVeU1uY2xNaklsTTBFbE1qSlVNRkpVVlVoNGJtUllaR2hrYlZWMVl6Sm9hR050Vm5kaU1teDFaRU0xYW1JeU1UaFphVVkxWVVaT1MxZEVTa1JPTW5oV1lsTXhNRkpHY0d0Vk1YQXlXbXBHVmxSV1ZYcFpiV1F6VTBoS2IxSXpRa2xYUmxKSlZrZGFWazVIVVRGUldGWlJXVEZXV1dWWGVFSlZWWEJ6VmxWU1EwNXVhRXRaVjNjeFprUkJlRTFyVGxKT1JGSktWRlZXV1ZSVlZrcFVWRkpWVWtWa1ExSkZiRVZWUkU1UlVteFZNRTVGU2xwVVJra2xNMFFsTWpJbE1rTWxNakpwSlRJeUpUTkJKVEl5TlRZNE1ESXlNbVV0TkRJeE1pMDBPRGsyTFdKak9HRXRaR1ppT1RRek5EbGlaVEpoSlRJeUpUZEUifQ%3D%3D"))
@RequestMapping("warehousingRuleConfigue")
public class WarehousingRuleConfigureController {

    private WarehousingRuleService warehousingRuleService;

    @Operation(summary = "conf配置 导出所有conf")
    @PostMapping("exportWarehousingRule")
    public void exportWarehousingRule(HttpServletResponse response) throws IOException {
        warehousingRuleService.exportWarehousingRule(response);
    }

    @Operation(summary = "conf配置 导出所有conf时的信息")
    @PostMapping("exportWarehousingRuleMessage")
    public ResponseVo<String> exportWarehousingRuleMessage() {
        return ResponseVo.ok(warehousingRuleService.exportWarehousingRuleMessage());
    }

    @Operation(summary = "conf配置 导入conf")
    @PostMapping("importWarehousingRule")
    public ResponseVo<Void> importWarehousingRule(MultipartFile file) throws IOException {
        warehousingRuleService.importWarehousingRule(file);
        return ResponseVo.ok();
    }


    @Operation(summary = "conf配置 检查是否存在conf")
    @GetMapping("checkConfExists")
    public ResponseVo<Boolean> checkConfExists() {
        return ResponseVo.ok(warehousingRuleService.checkConfExists());
    }

    @Operation(summary = "下拉框查询")
    @PostMapping("filterWarehousingRuleDropDown")
    public ResponseVo<List<DropDownVo>> filterWarehousingRuleDropDown(@RequestBody @Validated WarehousingRuleDropDownFilterVo warehouseRuleDropDownFilterVo) {
        return ResponseVo.ok(warehousingRuleService.filterWarehousingRuleDropDown(warehouseRuleDropDownFilterVo));
    }

    @Operation(summary = "查询子客户目录规则")
    @PostMapping("getDwSftpSubCustomerRule")
    public ResponseVo<PageableDataVo<DwSftpSubCustomerRuleVo>> getDwSftpSubCustomerRule(@RequestBody @Validated WarehousingRuleQueryVo warehousingRuleQueryVo) {
        return ResponseVo.ok(warehousingRuleService.getDwSftpSubCustomerRule(warehousingRuleQueryVo));
    }

    @Operation(summary = "查询文件重命名规则")
    @PostMapping("getDwSftpFileRenameRule")
    public ResponseVo<PageableDataVo<DwSftpFileRenameRuleVo>> getDwSftpFileRenameRule(@RequestBody @Validated WarehousingRuleQueryVo warehousingRuleQueryVo) {
        return ResponseVo.ok(warehousingRuleService.getDwSftpFileRenameRule(warehousingRuleQueryVo));
    }

    @Operation(summary = "查询特殊逻辑配置规则")
    @PostMapping("getDwDataClearRule")
    public ResponseVo<PageableDataVo<DwDataClearRuleVo>> getDwDataClearRule(@RequestBody @Validated WarehousingRuleQueryVo warehousingRuleQueryVo) {
        return ResponseVo.ok(warehousingRuleService.getDwDataClearRule(warehousingRuleQueryVo));
    }

    @Operation(summary = "查询解析脚本配置规则")
    @PostMapping("getDwSftpFileConvertScript")
    public ResponseVo<PageableDataVo<DwSftpFileConvertScriptVo>> getDwSftpFileConvertScript(@RequestBody @Validated WarehousingRuleQueryVo warehousingRuleQueryVo) {
        return ResponseVo.ok(warehousingRuleService.getDwSftpFileConvertScript(warehousingRuleQueryVo));
    }

    @Operation(summary = "查询DEVICE_ID取值规则")
    @PostMapping("getDwSourceStandardDeviceIdRule")
    public ResponseVo<PageableDataVo<DwSourceStandardDeviceIdRuleVo>> getDwSourceStandardDeviceIdRule(@RequestBody @Validated WarehousingRuleQueryVo warehousingRuleQueryVo) {
        return ResponseVo.ok(warehousingRuleService.getDwSourceStandardDeviceIdRule(warehousingRuleQueryVo));
    }

    @Operation(summary = "查询关键字段取值规则")
    @PostMapping("getDwSourceStandardFieldRule")
    public ResponseVo<PageableDataVo<DwSourceStandardFieldRuleVo>> getDwSourceStandardFieldRule(@RequestBody @Validated WarehousingRuleQueryVo warehousingRuleQueryVo) {
        return ResponseVo.ok(warehousingRuleService.getDwSourceStandardFieldRule(warehousingRuleQueryVo));
    }

    @Operation(summary = "保存子客户目录规则")
    @PostMapping("saveDwSftpSubCustomerRule")
    public ResponseVo<Void> saveDwSftpSubCustomerRule(@RequestBody @Validated DwSftpSubCustomerRuleRequestSaveVo dwSftpSubCustomerRuleRequestSaveVo) {
        warehousingRuleService.saveDwSftpSubCustomerRule(dwSftpSubCustomerRuleRequestSaveVo.getRuleRequestVos(), dwSftpSubCustomerRuleRequestSaveVo.getWarehouseRuleFlowRequestVo());
        return ResponseVo.ok();
    }

    @Operation(summary = "保存文件重命名规则")
    @PostMapping("saveDwSftpFileRenameRule")
    public ResponseVo<Void> saveDwSftpFileRenameRule(@RequestBody @Validated DwSftpFileRenameRuleRequestSaveVo dwSftpFileRenameRuleRequestSaveVo) {
        warehousingRuleService.saveDwSftpFileRenameRule(dwSftpFileRenameRuleRequestSaveVo.getRuleRequestVos(), dwSftpFileRenameRuleRequestSaveVo.getWarehouseRuleFlowRequestVo());
        return ResponseVo.ok();
    }

    @Operation(summary = "保存特殊逻辑配置规则")
    @PostMapping("saveDwDataClearRule")
    public ResponseVo<Void> saveDwDataClearRule(@RequestBody @Validated DwDataClearRuleRequestSaveVo dwDataClearRuleRequestSaveVo) {
        warehousingRuleService.saveDwDataClearRule(dwDataClearRuleRequestSaveVo.getRuleRequestVos(), dwDataClearRuleRequestSaveVo.getWarehouseRuleFlowRequestVo());
        return ResponseVo.ok();
    }

    @Operation(summary = "保存解析脚本配置规则")
    @PostMapping(value = "saveDwSftpFileConvertScript", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseVo<String> saveDwSftpFileConvertScript(
            @RequestPart("wrappers") List<DwSftpFileConvertScriptRequestVo> dwSftpFileConvertScriptRequestVos,
            @RequestPart(value = "files", required = false) MultipartFile[] files) {
        if(files == null) {
            files = new MultipartFile[0];
        }
        warehousingRuleService.saveDwSftpFileConvertScript(dwSftpFileConvertScriptRequestVos, files);
        return ResponseVo.ok();
    }

    @Operation(summary = "保存DEVICE_ID取值规则")
    @PostMapping("saveDwSourceStandardDeviceIdRule")
    public ResponseVo<Void> saveDwSourceStandardDeviceIdRule(@RequestBody @Validated DwSourceStandardDeviceIdRuleRequestSaveVo dwSourceStandardDeviceIdRuleRequestSaveVo) {
        warehousingRuleService.checkDwSourceStandardDeviceIdRule(dwSourceStandardDeviceIdRuleRequestSaveVo.getRuleRequestVos(),dwSourceStandardDeviceIdRuleRequestSaveVo.getWarehouseRuleFlowRequestVo());
        warehousingRuleService.saveDwSourceStandardDeviceIdRule(dwSourceStandardDeviceIdRuleRequestSaveVo.getRuleRequestVos());
        return ResponseVo.ok();
    }

    @Operation(summary = "保存关键字段取值规则")
    @PostMapping("saveDwSourceStandardFieldRule")
    public ResponseVo<Void> saveDwSourceStandardFieldRule(@RequestBody @Validated DwSourceStandardFieldRuleRequestSaveVo dwSourceStandardFieldRuleRequestSaveVo) {
        warehousingRuleService.saveDwSourceStandardFieldRule(dwSourceStandardFieldRuleRequestSaveVo.getRuleRequestVos(), dwSourceStandardFieldRuleRequestSaveVo.getWarehouseRuleFlowRequestVo());
        return ResponseVo.ok();
    }

    @Operation(summary = "获取常量Map")
    @PostMapping("getConstantMap")
    public ResponseVo<Map<String, Object>> getConstantMap() {
        return ResponseVo.ok(warehousingRuleService.getConstantMap());
    }

    @Operation(summary = "查询入库规则流程")
    @PostMapping("getWarehousingRuleFlow")
    public ResponseVo<PageableDataVo<WarehousingRuleFlowVo>> getWarehousingRuleFlow(@RequestBody @Validated WarehousingRuleFlowQueryVo warehousingRuleFlowQueryVo) {
        return ResponseVo.ok(warehousingRuleService.getWarehousingRuleFlow(warehousingRuleFlowQueryVo));
    }

    @Operation(summary = "保存入库规则流程")
    @PostMapping("saveWarehousingRuleFlow")
    public ResponseVo<Void> saveWarehousingRuleFlow(@RequestBody @Validated WarehousingRuleFlowRequestVo warehouseRuleFlowRequestVo) {
        warehousingRuleService.saveWarehousingRuleFlow(warehouseRuleFlowRequestVo);
        return ResponseVo.ok();
    }

    @Operation(summary = "校验入库规则流程")
    @PostMapping("validateWarehousingRuleFlow")
    public ResponseVo<Boolean> validateWarehousingRuleFlow(@RequestBody @Validated WarehousingRuleFlowRequestVo warehouseRuleFlowRequestVo) {
        return ResponseVo.ok(warehousingRuleService.validateWarehousingRuleFlow(warehouseRuleFlowRequestVo));
    }

    @Operation(summary = "复制入库规则流程")
    @PostMapping("duplicateWarehousingRuleFlow")
    public ResponseVo<Void> duplicateWarehousingRuleFlow(@RequestParam("id") @Min(1) Long id) {
        warehousingRuleService.duplicateWarehousingRuleFlow(id);
        return ResponseVo.ok();
    }

    @Operation(summary = "删除入库规则流程")
    @DeleteMapping("deleteWarehousingRuleFlow")
    public ResponseVo<Void> deleteWarehousingRuleFlow(@RequestParam("id") @Min(1) Long id) {
        warehousingRuleService.deleteWarehousingRuleFlow(id);
        return ResponseVo.ok();
    }

    @Operation(summary = "下载解析脚本压缩包")
    @GetMapping(value = "downloadScriptFileById", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void downloadScriptFileById(@RequestParam("id") @Schema(example = "1") @Min(value = 1) Long id, HttpServletResponse response) throws IOException {
        // 根据ID获取解析脚本
        log.info("下载解析脚本压缩包,id:{}",  id);
        warehousingRuleService.downloadScriptFileById(id,  response);
    }

    @Operation(summary = "抽取解析脚本")
    @PostMapping("extractScriptList")
    public ResponseVo<List<String>> extractScriptList(
            @RequestParam("file") MultipartFile file) {
        List<String> scriptList = warehousingRuleService.extractConvertScriptFiles(file);
        return ResponseVo.ok(scriptList);
    }
}
