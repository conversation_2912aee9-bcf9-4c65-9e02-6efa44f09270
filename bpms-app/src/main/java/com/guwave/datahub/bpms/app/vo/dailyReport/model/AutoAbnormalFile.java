package com.guwave.datahub.bpms.app.vo.dailyReport.model;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
public class AutoAbnormalFile {
    private String customer;
    private String subCustomer;
    private String testArea;
    private String factory;
    private String factorySite;
    private String fileType;
    private String fileCategory;
    private String fileName;
    private String originFileName;
    private String deviceId;
    private String lotId;
    private String waferId;
    private String originWaferId;
    private String waferNo;
    private String lotType;
    private String sblotId;
    private String testStage;
    private String startT;
    private String finishT;
    private Integer dieCount;
    private String warehousingMode;
    private Integer dieDataCount;
    private Long testItemDataCount;
    private String maxRecordTestItem;
    private Long maxRecordTestItemCnt;
    private String minRecordTestItem;
    private Long minRecordTestItemCnt;

    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public String getSubCustomer() {
        return subCustomer;
    }

    public void setSubCustomer(String subCustomer) {
        this.subCustomer = subCustomer;
    }

    public String getTestArea() {
        return testArea;
    }

    public void setTestArea(String testArea) {
        this.testArea = testArea;
    }

    public String getFactory() {
        return factory;
    }

    public void setFactory(String factory) {
        this.factory = factory;
    }

    public String getFactorySite() {
        return factorySite;
    }

    public void setFactorySite(String factorySite) {
        this.factorySite = factorySite;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFileCategory() {
        return fileCategory;
    }

    public void setFileCategory(String fileCategory) {
        this.fileCategory = fileCategory;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getOriginFileName() {
        return originFileName;
    }

    public void setOriginFileName(String originFileName) {
        this.originFileName = originFileName;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getLotId() {
        return lotId;
    }

    public void setLotId(String lotId) {
        this.lotId = lotId;
    }

    public String getWaferId() {
        return waferId;
    }

    public void setWaferId(String waferId) {
        this.waferId = waferId;
    }

    public String getOriginWaferId() {
        return originWaferId;
    }

    public void setOriginWaferId(String originWaferId) {
        this.originWaferId = originWaferId;
    }

    public String getWaferNo() {
        return waferNo;
    }

    public void setWaferNo(String waferNo) {
        this.waferNo = waferNo;
    }

    public String getLotType() {
        return lotType;
    }

    public void setLotType(String lotType) {
        this.lotType = lotType;
    }

    public String getSblotId() {
        return sblotId;
    }

    public void setSblotId(String sblotId) {
        this.sblotId = sblotId;
    }

    public String getTestStage() {
        return testStage;
    }

    public void setTestStage(String testStage) {
        this.testStage = testStage;
    }

    public String getStartT() {
        return startT;
    }

    public void setStartT(String startT) {
        this.startT = startT;
    }

    public String getFinishT() {
        return finishT;
    }

    public void setFinishT(String finishT) {
        this.finishT = finishT;
    }

    public Integer getDieCount() {
        return dieCount;
    }

    public void setDieCount(Integer dieCount) {
        this.dieCount = dieCount;
    }

    public String getWarehousingMode() {
        return warehousingMode;
    }

    public void setWarehousingMode(String warehousingMode) {
        this.warehousingMode = warehousingMode;
    }

    public Integer getDieDataCount() {
        return dieDataCount;
    }

    public void setDieDataCount(Integer dieDataCount) {
        this.dieDataCount = dieDataCount;
    }

    public Long getTestItemDataCount() {
        return testItemDataCount;
    }

    public void setTestItemDataCount(Long testItemDataCount) {
        this.testItemDataCount = testItemDataCount;
    }

    public String getMaxRecordTestItem() {
        return maxRecordTestItem;
    }

    public void setMaxRecordTestItem(String maxRecordTestItem) {
        this.maxRecordTestItem = maxRecordTestItem;
    }

    public Long getMaxRecordTestItemCnt() {
        return maxRecordTestItemCnt;
    }

    public void setMaxRecordTestItemCnt(Long maxRecordTestItemCnt) {
        this.maxRecordTestItemCnt = maxRecordTestItemCnt;
    }

    public String getMinRecordTestItem() {
        return minRecordTestItem;
    }

    public void setMinRecordTestItem(String minRecordTestItem) {
        this.minRecordTestItem = minRecordTestItem;
    }

    public Long getMinRecordTestItemCnt() {
        return minRecordTestItemCnt;
    }

    public void setMinRecordTestItemCnt(Long minRecordTestItemCnt) {
        this.minRecordTestItemCnt = minRecordTestItemCnt;
    }
}
