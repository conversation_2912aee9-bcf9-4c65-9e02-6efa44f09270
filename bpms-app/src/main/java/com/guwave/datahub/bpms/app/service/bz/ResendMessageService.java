package com.guwave.datahub.bpms.app.service.bz;

import com.alibaba.fastjson2.JSON;
import com.guwave.datahub.bpms.app.serialization.avro.StdfDataSerialization;
import com.guwave.datahub.bpms.app.serialization.avro.WatDataSerialization;
import com.guwave.datahub.bpms.app.sink.KafkaSink;
import com.guwave.datahub.bpms.dao.domain.processMessage.KafkaFailMessageRecord;
import com.guwave.datahub.bpms.dao.repository.bpms.KafkaFailMessageRecordRepository;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.model.stdf.StdfData;
import com.guwave.onedata.dataware.common.model.wat.WatData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.guwave.datahub.bpms.common.constant.Constant.COMMA;

@Slf4j
@Service
public class ResendMessageService {

    @Autowired
    private KafkaFailMessageRecordRepository kafkaFailMessageRecordRepository;

    @Autowired
    private KafkaSink kafkaSink;

    @Value("${spring.retry.message.stdf.data.topic}")
    private String stdfDataTopic;
    @Value("${spring.retry.message.wat.data.topic}")
    private String watDataTopic;

    @Value("${spring.retry.message.stdf.data.useFlag}")
    private Boolean stdfDataUseFlag;


    public void resendMessage() {
        List<KafkaFailMessageRecord> failMessageRecords = kafkaFailMessageRecordRepository.findAllByProcessStatusAndDeleteFlagAndFailCntLessThanOrderByCreateTime(ProcessStatus.FAIL, 0, 5);
        if (CollectionUtils.isEmpty(failMessageRecords)) {
            log.info("没有需要重新发送的消息");
            return;
        }
        KafkaFailMessageRecord failMessageRecord = failMessageRecords.get(0);
        log.info("开始重新发送消息 id:{}", failMessageRecord.getId());
        List<String> stdfDataTopics = Arrays.stream(stdfDataTopic.split(COMMA)).collect(Collectors.toList());
        List<String> watDataTopics = Arrays.stream(watDataTopic.split(COMMA)).collect(Collectors.toList());
        boolean isStdfData = stdfDataTopics.contains(failMessageRecord.getTopic());
        boolean isWatData = watDataTopics.contains(failMessageRecord.getTopic());
        try {
            if (isStdfData || isWatData) {
                if (stdfDataUseFlag) {
                    // source-agent使用kafkaSink才进行重发，否则不处理
                    byte[] message;
                    // 判断是stdfdata或是watdata
                    if (isStdfData) {
                        StdfData stdfData = JSON.parseObject(failMessageRecord.getValue(), StdfData.class);
                        message = StdfDataSerialization.serialize(stdfData);
                    } else {
                        WatData watData = JSON.parseObject(failMessageRecord.getValue(), WatData.class);
                        message = WatDataSerialization.serialize(watData);
                    }

                    if (StringUtils.isNotBlank(failMessageRecord.getKey())) {
                        byte[] key = failMessageRecord.getKey().getBytes(StandardCharsets.UTF_8);
                        kafkaSink.send(failMessageRecord.getTopic(), key, message);
                    } else {
                        kafkaSink.send(failMessageRecord.getTopic(), message);
                    }
                }
            } else {
                if (StringUtils.isNotBlank(failMessageRecord.getKey())) {
                    kafkaSink.send(failMessageRecord.getTopic(), failMessageRecord.getKey(), failMessageRecord.getValue());
                } else {
                    kafkaSink.send(failMessageRecord.getTopic(), failMessageRecord.getValue());
                }
            }
            log.info("重新发送消息成功");
            failMessageRecord
                    .setProcessStatus(ProcessStatus.SUCCESS)
                    .setDeleteFlag(1)
                    .setUpdateTime(new Date());
        } catch (Exception e) {
            failMessageRecord
                    .setFailCnt(failMessageRecord.getFailCnt() + 1)
                    .setUpdateTime(new Date());
        } finally {
            kafkaFailMessageRecordRepository.save(failMessageRecord);
        }
    }
}
