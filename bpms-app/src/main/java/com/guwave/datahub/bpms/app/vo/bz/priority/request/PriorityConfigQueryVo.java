package com.guwave.datahub.bpms.app.vo.bz.priority.request;

import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@EqualsAndHashCode(callSuper = false)
@Data
@Schema(description = "查询优先级配置请求")
public class PriorityConfigQueryVo extends PageableRequestVo {

    @Schema(description = "customer")
    private String customer;

    @Schema(description = "subCustomer")
    private String subCustomer;

    @Schema(description = "factory")
    private String factory;

    @Schema(description = "factorySite")
    private String factorySite;

    @Schema(description = "deviceId")
    private String deviceId;

    @Schema(description = "testStage")
    private String testStage;

    @Schema(description = "lotType")
    private String lotType;

    @Schema(description = "lotId")
    private String lotId;

    @Schema(description = "waferNo")
    private String waferNo;

    @Schema(description = "testArea")
    private String testArea;

    @Schema(description = "fileCategory")
    private String fileCategory;
}
