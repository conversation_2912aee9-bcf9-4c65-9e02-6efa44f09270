package com.guwave.datahub.bpms.app.service.bz;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.guwave.datahub.bpms.app.callback.SparkReloadTaskFinishCallback;
import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.exception.BpmsParamException;
import com.guwave.datahub.bpms.app.util.ComputeUtil;
import com.guwave.datahub.bpms.app.vo.bz.task.request.DmtTaskRerunSingleVo;
import com.guwave.datahub.bpms.app.vo.bz.task.request.DwTaskDropDownFilterVo;
import com.guwave.datahub.bpms.app.vo.bz.task.request.DwTaskQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.task.request.DwTaskRerunVo;
import com.guwave.datahub.bpms.app.vo.bz.task.request.YmsTaskDropDownFilterVo;
import com.guwave.datahub.bpms.app.vo.bz.task.request.YmsTaskQueryVo;
import com.guwave.datahub.bpms.app.vo.bz.task.request.YmsTaskRerunVo;
import com.guwave.datahub.bpms.app.vo.bz.task.response.DmtAppDetailVo;
import com.guwave.datahub.bpms.app.vo.bz.task.response.DmtTaskDetailVo;
import com.guwave.datahub.bpms.app.vo.bz.task.response.DwTaskDetailVo;
import com.guwave.datahub.bpms.app.vo.bz.task.response.DwTaskVo;
import com.guwave.datahub.bpms.app.vo.bz.task.response.TaskDropDownVo;
import com.guwave.datahub.bpms.app.vo.bz.task.response.YmsTaskVo;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.dao.repository.compute.ComputePoolRepository;
import com.guwave.datahub.bpms.dao.repository.dw.*;
import com.guwave.datahub.bpms.dao.repository.linkx.BzAppInstanceRecordRepository;
import com.guwave.datahub.bpms.dao.repository.linkx.BzAppInstanceRepository;
import com.guwave.datahub.bpms.dao.repository.linkx.BzTaskInstanceRepository;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.*;
import com.guwave.onedata.linkx.common.constant.OptType;
import com.guwave.onedata.linkx.dao.mysql.domain.bz.BzAppInstance;
import com.guwave.onedata.linkx.dao.mysql.domain.bz.BzAppInstanceRecord;
import com.guwave.onedata.linkx.dao.mysql.domain.bz.BzTaskInstance;
import com.guwave.onedata.next.compute.api.iface.IComputeRpcService;
import com.guwave.onedata.next.compute.api.vo.response.ComputeResponse;
import com.guwave.onedata.next.compute.common.message.ComputeResultMessage;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputePool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static com.guwave.onedata.dataware.common.contant.Constant.MIDDLE_LINE;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * TaskService
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2024-02-26 13:34:45
 */
@Service
@Slf4j
public class TaskService {

    @Autowired
    private LayerCalculatePoolRepository layerCalculatePoolRepository;
    @Autowired
    private WarehousingTaskRecordRepository warehousingTaskRecordRepository;
    @Autowired
    private LotWaferWarehousingRecordRepository lotWaferWarehousingRecordRepository;
    @Autowired
    private BzAppInstanceRepository bzAppInstanceRepository;
    @Autowired
    private BzAppInstanceRecordRepository bzAppInstanceRecordRepository;
    @Autowired
    private BzTaskInstanceRepository bzTaskInstanceRepository;
    @Autowired
    private ComputePoolRepository computePoolRepository;
    @Autowired
    private ExpireDeleteRecordRepository expireDeleteRecordRepository;
    @Autowired
    private LotStockingDetailRepository lotStockingDetailRepository;
    @DubboReference
    private IComputeRpcService computeRpcService;

    @Value("${spring.task.resourceManagerUrl}")
    private String resourceManagerUrl;

    /**
     * 下拉框筛选
     *
     * @param dwTaskDropDownFilterVo 下拉框的前置条件及需要查询的字段
     * @return 该下拉框的能选取的内容
     */
    public List<TaskDropDownVo> dwTaskFilter(DwTaskDropDownFilterVo dwTaskDropDownFilterVo) {
        log.info("dwTaskFilter");


        List<String> fieldResult = warehousingTaskRecordRepository.findDynamicField(
                dwTaskDropDownFilterVo.getFilterField(), dwTaskDropDownFilterVo.getTestAreaList(), dwTaskDropDownFilterVo.getFactoryList(),
                dwTaskDropDownFilterVo.getDeviceIdList(), dwTaskDropDownFilterVo.getTestStageList(), dwTaskDropDownFilterVo.getLotIdList(),
                dwTaskDropDownFilterVo.getWaferNoList(), dwTaskDropDownFilterVo.getLotTypeList(), dwTaskDropDownFilterVo.getDwLayerList(),
                dwTaskDropDownFilterVo.getProcessStatusList(), RequestContext.getCustomer()
        );
        fieldResult = fieldResult.stream()
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
        return TaskDropDownVo.ofList(fieldResult);
    }

    /**
     * 根据条件查询对应的dw task
     *
     * @param dwTaskQueryVo 查询条件
     * @return 符合条件的内容
     */
    public PageableDataVo<DwTaskVo> queryDwTasks(DwTaskQueryVo dwTaskQueryVo) {
        log.info("queryDwTasks");

        log.info(dwTaskQueryVo.toString());
        Page<WarehousingTaskRecord> dwTasks = warehousingTaskRecordRepository.findDwTasksByFilters(
                dwTaskQueryVo.getTestAreaList(), dwTaskQueryVo.getFactoryList(), dwTaskQueryVo.getDeviceIdList(),
                dwTaskQueryVo.getTestStageList(), dwTaskQueryVo.getLotIdList(), dwTaskQueryVo.getWaferNoList(),
                dwTaskQueryVo.getLotTypeList(), dwTaskQueryVo.getDwLayerList(), dwTaskQueryVo.getProcessStatusList(),
                dwTaskQueryVo.getFileCategoryList(), RequestContext.getCustomer(),
                PageRequest.of(dwTaskQueryVo.getJpaPageIndex(), dwTaskQueryVo.getPageSize())
        );

        return PageableDataVo.of(dwTasks.map(item -> DwTaskVo.of(item, resourceManagerUrl)));
    }

    /**
     * @param id 查询的id
     * @return pool表中的详情
     */
    public DwTaskDetailVo querySingDwTaskDetail(Long id) {
        log.info("querySingDwTaskDetail");

        return warehousingTaskRecordRepository.findById(id)
                .map(DwTaskDetailVo::of)
                .orElseThrow(() -> new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT));
    }

    /**
     * 批量重跑任务
     *
     * @param rerunTaskIdsVo 需要重跑的ids
     */
    public void rerunDwTask(DwTaskRerunVo rerunTaskIdsVo) {

        log.info("rerunDwBatchTasks");

        List<Long> ids = rerunTaskIdsVo.getIds();

        ArrayList<LayerCalculatePool> dwTasks = new ArrayList<>();

        List<WarehousingTaskRecord> warehousingTaskRecords = warehousingTaskRecordRepository.findAllById(ids).stream()
                .peek(record -> {

                    // 找到pool中对应记录
                    LayerCalculatePool task = layerCalculatePoolRepository.findFirstByCustomerAndSubCustomerAndFactoryAndTestAreaAndDeviceIdAndTestStageAndLotIdAndWaferNoAndLotTypeAndFileCategoryAndDwLayerOrderByUpdateTimeDesc(
                            record.getCustomer(),
                            record.getSubCustomer(),
                            record.getFactory(),
                            record.getTestArea(),
                            record.getDeviceId(),
                            record.getTestStage(),
                            record.getLotId(),
                            record.getWaferNo(),
                            record.getLotType(),
                            record.getFileCategory(),
                            DwLayer.DWD
                    );

                    if (ObjectUtils.isEmpty(task)) {
                        log.info("record表没有在calculate_pool表中找到对应DWD任务记录，record表id：{}", record.getId());
                        throw new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT);
                    }

                    // 不允许重跑成功或者正在运行的任务
                    if (record.getProcessStatus() == ProcessStatus.SUCCESS || record.getProcessStatus() == ProcessStatus.PROCESSING ||
                            task.getProcessStatus() == ProcessStatus.SUCCESS || task.getProcessStatus() == ProcessStatus.PROCESSING) {
                        log.info("任务：{} 无法重跑，存在成功或正在运行的任务", DwTaskVo.of(record, resourceManagerUrl));
                        log.info("warehousingid：{}，calculate_pool的id：{}", record.getId(), task.getId());
                        throw new BpmsException(ResponseCode.Business.TASK_NOT_COMPLETED_OR_SUCCESS);
                    }

                    record.setProcessStatus(ProcessStatus.CREATE)
                            .setUpdateTime(new Date())
                            .setUpdateUser(RequestContext.getUserName())
                            .setAppId(null)
                            .setExceptionType(null)
                            .setErrorMessage(null);
                    log.info("需要重跑的任务：{}", JSON.toJSONString(record));
                    updateLayerCalculatePool(task, record);
                    dwTasks.add(task);
                }).collect(Collectors.toList());

        layerCalculatePoolRepository.saveAll(dwTasks);
        warehousingTaskRecordRepository.saveAll(warehousingTaskRecords);
    }

    public List<TaskDropDownVo> ymsTaskDropDownFilter(YmsTaskDropDownFilterVo ymsTaskDropDownFilterVo) {
        log.info("ymsTaskDropDownFilter");

        List<String> fieldResult = bzAppInstanceRecordRepository.findDynamicField(
                ymsTaskDropDownFilterVo.getFilterField(), ymsTaskDropDownFilterVo.getTestAreaList(), ymsTaskDropDownFilterVo.getFactoryList(),
                ymsTaskDropDownFilterVo.getDeviceIdList(), ymsTaskDropDownFilterVo.getTestStageList(), ymsTaskDropDownFilterVo.getLotIdList(),
                ymsTaskDropDownFilterVo.getWaferNoList(), ymsTaskDropDownFilterVo.getLotTypeList(), ymsTaskDropDownFilterVo.getProcessStatusList(),
                ymsTaskDropDownFilterVo.getOptTypeList(), ymsTaskDropDownFilterVo.getExecuteEngineList(), RequestContext.getCustomer()
        );
        fieldResult = fieldResult.stream()
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
        return TaskDropDownVo.ofList(fieldResult);
    }

    public PageableDataVo<YmsTaskVo> queryYmsTasks(YmsTaskQueryVo ymsTaskQueryVo) {
        log.info("queryYmsTasks");

        Page<BzAppInstanceRecord> ymsTasks = bzAppInstanceRecordRepository.findYmsTasksByFilters(
                ymsTaskQueryVo.getTestAreaList(), ymsTaskQueryVo.getFactoryList(), ymsTaskQueryVo.getDeviceIdList(),
                ymsTaskQueryVo.getTestStageList(), ymsTaskQueryVo.getLotIdList(), ymsTaskQueryVo.getWaferNoList(),
                ymsTaskQueryVo.getLotTypeList(), ymsTaskQueryVo.getProcessStatusList(), ymsTaskQueryVo.getOptTypeList(),
                ymsTaskQueryVo.getExecuteEngineList(), RequestContext.getCustomer(), PageRequest.of(ymsTaskQueryVo.getJpaPageIndex(), ymsTaskQueryVo.getPageSize())
        );

        return PageableDataVo.of(ymsTasks.map(YmsTaskVo::of));
    }

    /**
     * 批量重跑yms任务
     *
     * @param ymsTaskRerunVo 需要重跑的全部id
     */
    public void rerunYmsTask(YmsTaskRerunVo ymsTaskRerunVo) {
        log.info("rerunYmsBatchTask");

        List<Long> ids = ymsTaskRerunVo.getIds();
        ArrayList<BzAppInstance> ymsTasks = new ArrayList<>();
        List<BzAppInstanceRecord> bzYmsWarehousingTaskRecords = bzAppInstanceRecordRepository.findAllById(ids).stream()
                .peek(record -> {
                    // 找到AppInstance中对应记录
                    BzAppInstance task = bzAppInstanceRepository.findFirstByCustomerAndSubCustomerAndFactoryAndTestAreaAndDeviceIdAndTestStageAndLotIdAndWaferNoAndLotTypeAndOptTypeAndAppDefinitionIdOrderByDataVersionDescUpdateTimeDesc(
                            record.getCustomer(),
                            record.getSubCustomer(),
                            record.getFactory(),
                            record.getTestArea(),
                            record.getDeviceId(),
                            record.getTestStage(),
                            record.getLotId(),
                            record.getWaferNo(),
                            record.getLotType(),
                            record.getOptType(),
                            record.getAppDefinitionId()
                    );

                    if (ObjectUtils.isEmpty(task)) {
                        log.info("record表没有在app_instance表中找到对应记录，record表id：{}", record.getId());
                        throw new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT);
                    }

                    // 不允许重跑成功或者正在运行的任务
                    if (record.getProcessStatus() == ProcessStatus.SUCCESS || record.getProcessStatus() == ProcessStatus.PROCESSING ||
                            task.getProcessStatus() == ProcessStatus.SUCCESS || task.getProcessStatus() == ProcessStatus.PROCESSING) {
                        log.info("任务：{} 无法重跑", YmsTaskVo.of(record));
                        throw new BpmsException(ResponseCode.Business.TASK_NOT_COMPLETED_OR_SUCCESS);
                    }


                    record.setProcessStatus(ProcessStatus.CREATE)
                            .setExecuteTime(null)
                            .setUpdateTime(new Date())
                            .setUpdateUser(RequestContext.getUserName())
                            .setExceptionType(null)
                            .setErrorMessage(null);
                    // 重跑UPDATE_WAFERMAP_CONFIG时需要重删除阶段开始重跑
                    if (record.getOptType() == OptType.UPDATE_WAFERMAP_CONFIG) {
                        record.setStartStep(0);
                    }
                    log.info("需要重跑的任务：{}", JSON.toJSONString(record));
                    updateBzAppInstance(task, record);
                    ymsTasks.add(task);
                }).collect(Collectors.toList());

        bzAppInstanceRecordRepository.saveAll(bzYmsWarehousingTaskRecords);
        bzAppInstanceRepository.saveAll(ymsTasks);
    }

    /**
     * @param id 查询的id
     * @return pool表中的详情
     */
    public DmtAppDetailVo querySingleDmtAppDetail(Long id) {
        log.info("查询dmt任务详情");

        BzAppInstanceRecord appInstanceRecord = bzAppInstanceRecordRepository.findById(id).orElseThrow(() -> new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT));

        // 找到AppInstance中对应记录
        Optional<BzAppInstance> appInstanceOptional = appInstanceRecord.getExecuteAppInstanceId() != null ? bzAppInstanceRepository.findById(appInstanceRecord.getExecuteAppInstanceId()) :
                bzAppInstanceRepository.findAllByCustomerAndSubCustomerAndFactoryAndTestAreaAndDeviceIdAndTestStageAndLotIdAndWaferNoAndLotTypeAndOptTypeAndAppDefinitionId(
                        appInstanceRecord.getCustomer(),
                        appInstanceRecord.getSubCustomer(),
                        appInstanceRecord.getFactory(),
                        appInstanceRecord.getTestArea(),
                        appInstanceRecord.getDeviceId(),
                        appInstanceRecord.getTestStage(),
                        appInstanceRecord.getLotId(),
                        appInstanceRecord.getWaferNo(),
                        appInstanceRecord.getLotType(),
                        appInstanceRecord.getOptType(),
                        appInstanceRecord.getAppDefinitionId()
                ).stream().min(Comparator.comparingInt(BzAppInstance::getStartStep).thenComparing(BzAppInstance::getId, Comparator.reverseOrder()));
        if (!appInstanceOptional.isPresent()) {
            log.info("record表没有在app_instance表中找到对应记录，record表id：{}", appInstanceRecord.getId());
            throw new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT);
        }
        BzAppInstance appInstance = appInstanceOptional.get();

        List<BzTaskInstance> bzTaskInstances = bzTaskInstanceRepository.findAllByAppInstanceId(appInstance.getId());

        List<DmtTaskDetailVo> dmtTaskDetailVos = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(bzTaskInstances)) {
            Optional<BzTaskInstance> firstTaskOptional = bzTaskInstances.stream().filter(t -> t.getPreTaskInstanceId() == null).max(Comparator.comparing(BzTaskInstance::getId));
            if (firstTaskOptional.isPresent()) {
                // 最近一次跑的taskList
                BzTaskInstance firstTaskInstance = firstTaskOptional.get();

                Long version = firstTaskInstance.getVersion();

                dmtTaskDetailVos.addAll(
                        bzTaskInstances.stream()
                                .filter(t -> Objects.equals(t.getVersion(), version) && t.getPreTaskInstanceId() == null)
                                .map(DmtTaskDetailVo::of)
                                .collect(Collectors.toList())
                );

                Map<Long, List<BzTaskInstance>> taskInstanceMap = bzTaskInstances.stream()
                        .filter(t -> Objects.equals(t.getVersion(), version) && t.getPreTaskInstanceId() != null)
                        .collect(Collectors.groupingBy(BzTaskInstance::getPreTaskInstanceId));

                Long preTaskInstanceId = firstTaskInstance.getId();
                while (taskInstanceMap.containsKey(preTaskInstanceId)) {
                    List<BzTaskInstance> currentTaskInstances = taskInstanceMap.get(preTaskInstanceId).stream().sorted(Comparator.comparing(BzTaskInstance::getId)).collect(Collectors.toList());
                    currentTaskInstances.forEach(task -> dmtTaskDetailVos.add(DmtTaskDetailVo.of(task)));
                    preTaskInstanceId = currentTaskInstances.get(0).getId();
                }
            }
        } else {
            log.warn("没有找到对应的任务，id：{}", id);
        }

        return DmtAppDetailVo.of(appInstanceRecord, dmtTaskDetailVos);
    }

    /**
     * 重跑单个dmt任务
     *
     * @param dmtTaskRerunSingleVo 需要重跑的任务
     */
    public void rerunSingleDmtTask(DmtTaskRerunSingleVo dmtTaskRerunSingleVo) {
        log.info("重跑单个dmt任务");

        BzAppInstanceRecord appInstanceRecord = bzAppInstanceRecordRepository.findById(dmtTaskRerunSingleVo.getId()).orElseThrow(() -> new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT));

        // 找到AppInstance中对应记录
        Optional<BzAppInstance> appInstanceOptional = appInstanceRecord.getExecuteAppInstanceId() != null ? bzAppInstanceRepository.findById(appInstanceRecord.getExecuteAppInstanceId()) :
                bzAppInstanceRepository.findAllByCustomerAndSubCustomerAndFactoryAndTestAreaAndDeviceIdAndTestStageAndLotIdAndWaferNoAndLotTypeAndOptTypeAndAppDefinitionId(
                        appInstanceRecord.getCustomer(),
                        appInstanceRecord.getSubCustomer(),
                        appInstanceRecord.getFactory(),
                        appInstanceRecord.getTestArea(),
                        appInstanceRecord.getDeviceId(),
                        appInstanceRecord.getTestStage(),
                        appInstanceRecord.getLotId(),
                        appInstanceRecord.getWaferNo(),
                        appInstanceRecord.getLotType(),
                        appInstanceRecord.getOptType(),
                        appInstanceRecord.getAppDefinitionId()
                ).stream().min(Comparator.comparingInt(BzAppInstance::getStartStep).thenComparing(BzAppInstance::getId, Comparator.reverseOrder()));

        if (!appInstanceOptional.isPresent()) {
            log.info("record表没有在app_instance表中找到对应记录，record表id：{}", appInstanceRecord.getId());
            throw new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT);
        }
        BzAppInstance appInstance = appInstanceOptional.get();

        // 不允许重跑成功或者正在运行的任务
        if (appInstanceRecord.getProcessStatus() == ProcessStatus.SUCCESS || appInstanceRecord.getProcessStatus() == ProcessStatus.PROCESSING ||
                appInstance.getProcessStatus() == ProcessStatus.SUCCESS || appInstance.getProcessStatus() == ProcessStatus.PROCESSING) {
            log.info("任务：{} 无法重跑, 没有失败的历史任务记录或者有正在运行的任务", YmsTaskVo.of(appInstanceRecord));
            throw new BpmsException(ResponseCode.Business.TASK_NOT_COMPLETED_OR_SUCCESS);
        }

        List<BzTaskInstance> modifyParamTaskInstanceList = new ArrayList<>();
        List<ComputePool> modifyParamComputePoolList = new ArrayList<>();

        List<DmtTaskDetailVo> dmtTaskDetailVos = dmtTaskRerunSingleVo.getDmtTaskDetailVos();
        if (CollectionUtils.isNotEmpty(dmtTaskDetailVos) && dmtTaskDetailVos.stream().anyMatch(DmtTaskDetailVo::isModifyParamFlag)) {
            // 判断有没有修改资源参数
            dmtTaskDetailVos.stream()
                    .filter(DmtTaskDetailVo::isModifyParamFlag)
                    .forEach(t -> {
                        BzTaskInstance taskInstance = bzTaskInstanceRepository.findById(t.getId()).orElseThrow(() -> new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT));
                        log.info("修改前任务，id:{}, numExecutors:{}, executorCores:{}, executorMemory:{}, driverMemory:{}, parallelism:{}, extraConf:{}",
                                taskInstance.getId(), taskInstance.getNumExecutors(), taskInstance.getExecutorCores(), taskInstance.getExecutorMemory(), taskInstance.getDriverMemory(), taskInstance.getParallelism(), taskInstance.getExtraConf());

                        int numExecutors;
                        int executorCores;
                        int executorMemory;
                        int driverMemory;
                        int parallelism;
                        try {
                            numExecutors = Integer.parseInt(t.getNumExecutors());
                            executorCores = Integer.parseInt(t.getExecutorCores());
                            executorMemory = Integer.parseInt(t.getExecutorMemory());
                            driverMemory = Integer.parseInt(t.getDriverMemory());
                            parallelism = Integer.parseInt(t.getParallelism());
                            log.info("修改后任务，id:{}, numExecutors:{}, executorCores:{}, executorMemory:{}, driverMemory:{}, parallelism:{}, extraConf:{}", t.getId(), numExecutors, executorCores, executorMemory, driverMemory, parallelism, t.getExtraConf());
                        } catch (Exception e) {
                            throw new BpmsParamException("numExecutors, executorCores, executorMemory, driverMemory, parallelism");
                        }

                        if (!Objects.equals(taskInstance.getNumExecutors(), numExecutors) ||
                                !Objects.equals(taskInstance.getExecutorCores(), executorCores) ||
                                !Objects.equals(taskInstance.getExecutorMemory(), executorMemory) ||
                                !Objects.equals(taskInstance.getDriverMemory(), driverMemory) ||
                                !Objects.equals(taskInstance.getParallelism(), parallelism) ||
                                !Objects.equals(taskInstance.getExtraConf(), t.getExtraConf())) {
                            log.info("修改了任务资源参数");
                            Optional<ComputePool> computePoolOptional = computePoolRepository.findByUniqueIdAndProcessStatus(taskInstance.getUniqueId(), com.guwave.onedata.next.compute.common.constant.ProcessStatus.FAIL);
                            if (computePoolOptional.isPresent()) {
                                log.info("任务：{} 修改资源参数, 且找到对应失败的next compute任务记录，uniqueId:{}", taskInstance.getId(), taskInstance.getUniqueId());
                                ComputePool computePool = computePoolOptional.get()
                                        .setNumExecutors(numExecutors)
                                        .setExecutorCores(executorCores)
                                        .setExecutorMemory(executorMemory)
                                        .setDriverMemory(driverMemory)
                                        .setParallelism(parallelism)
                                        .setExtraConf(t.getExtraConf());
                                modifyParamComputePoolList.add(computePool);

                                taskInstance
                                        .setNumExecutors(numExecutors)
                                        .setExecutorCores(executorCores)
                                        .setExecutorMemory(executorMemory)
                                        .setDriverMemory(driverMemory)
                                        .setParallelism(parallelism)
                                        .setExtraConf(t.getExtraConf());
                                modifyParamTaskInstanceList.add(taskInstance);
                            }
                        } else {
                            log.info("没有修改任务资源参数");
                        }
                    });
        }

        List<BzTaskInstance> updateTaskInstanceList = new ArrayList<>(modifyParamTaskInstanceList);
        if (CollectionUtils.isNotEmpty(modifyParamTaskInstanceList) && CollectionUtils.isNotEmpty(modifyParamComputePoolList)) {
            // 修改资源
            log.info("修改资源参数，重跑失败的taskInstance");
            appInstanceRecord.setProcessStatus(ProcessStatus.PROCESSING);

            // 从修改参数的任务开始重跑
            BzTaskInstance lastModifyParamTaskInstance = modifyParamTaskInstanceList.stream().max(Comparator.comparing(BzTaskInstance::getId)).get();

            // 获取修改参数之后的任务
            List<Long> afterModifyParamTaskInstanceIds = dmtTaskDetailVos.stream()
                    .map(DmtTaskDetailVo::getId)
                    .filter(t -> t > lastModifyParamTaskInstance.getId())
                    .collect(Collectors.toList());
            updateTaskInstanceList.addAll(
                    StreamSupport.stream(bzTaskInstanceRepository.findAllById(afterModifyParamTaskInstanceIds).spliterator(), false)
                            .map(t -> t.setProcessStatus(ProcessStatus.CREATE)
                                    .setUpdateTime(new Date())
                                    .setUpdateUser(RequestContext.getUserName()))
                            .collect(Collectors.toList())
            );

            modifyParamTaskInstanceList.forEach(t ->
                    t.setProcessStatus(ProcessStatus.PROCESSING)
                            .setStartTime(new Date())
                            .setEndTime(null)
                            .setExecuteTime(null)
                            .setUpdateTime(new Date())
                            .setUpdateUser(RequestContext.getUserName())
                            .setExceptionType(null)
                            .setErrorMessage(null)
            );

            modifyParamComputePoolList.forEach(t ->
                    t.setProcessStatus(com.guwave.onedata.next.compute.common.constant.ProcessStatus.CREATE)
                            .setFailCnt(0)
                            .setVersion(null)
                            .setStartTime(null)
                            .setEndTime(null)
                            .setExecuteTime(null)
                            .setUseDynamicResource(false)
                            .setUpdateTime(new Date())
                            .setUpdateUser(RequestContext.getUserName())
                            .setExceptionType(null)
                            .setErrorMessage(null)
            );
        } else {
            // 没有修改资源
            log.info("不修改资源参数，直接重跑appInstance任务");
            appInstanceRecord.setProcessStatus(ProcessStatus.CREATE);

            // 重跑UPDATE_WAFERMAP_CONFIG时需要重删除阶段开始重跑
            if (appInstanceRecord.getOptType() == OptType.UPDATE_WAFERMAP_CONFIG) {
                appInstanceRecord.setStartStep(0);
            }
        }

        appInstanceRecord.setExecuteTime(null)
                .setUpdateTime(new Date())
                .setUpdateUser(RequestContext.getUserName())
                .setExceptionType(null)
                .setErrorMessage(null);

        log.info("需要重跑的任务：{}", JSON.toJSONString(appInstanceRecord));
        updateBzAppInstance(appInstance, appInstanceRecord);

        bzAppInstanceRecordRepository.save(appInstanceRecord);
        bzAppInstanceRepository.save(appInstance);
        bzTaskInstanceRepository.saveAll(updateTaskInstanceList);
        computePoolRepository.saveAll(modifyParamComputePoolList);
    }

    public void updateLayerCalculatePool(LayerCalculatePool task, WarehousingTaskRecord warehousingTaskRecord) {
        task
                .setVersion(null)
                .setDataVersion(null)
                .setUniqueId(null)
                .setProcessStatus(warehousingTaskRecord.getProcessStatus())
                .setUpdateTime(warehousingTaskRecord.getUpdateTime())
                .setUpdateUser(warehousingTaskRecord.getUpdateUser())
                .setExceptionType(warehousingTaskRecord.getExceptionType())
                .setErrorMessage(warehousingTaskRecord.getErrorMessage());
    }

    public void updateBzAppInstance(BzAppInstance bzAppInstance, BzAppInstanceRecord bzYmsWarehousingTaskRecord) {
        bzAppInstance
                .setProcessStatus(bzYmsWarehousingTaskRecord.getProcessStatus())
                .setStartStep(bzYmsWarehousingTaskRecord.getStartStep())
                .setStartTime(bzYmsWarehousingTaskRecord.getProcessStatus() == ProcessStatus.CREATE ? null : new Date())
                .setEndTime(null)
                .setExecuteTime(bzYmsWarehousingTaskRecord.getExecuteTime())
                .setUpdateTime(bzYmsWarehousingTaskRecord.getUpdateTime())
                .setUpdateUser(bzYmsWarehousingTaskRecord.getUpdateUser())
                .setExceptionType(bzYmsWarehousingTaskRecord.getExceptionType())
                .setErrorMessage(bzYmsWarehousingTaskRecord.getErrorMessage());
    }

    public void reloadDwTask(Long id) {
        Optional<WarehousingTaskRecord> warehousingTaskRecordOpt = warehousingTaskRecordRepository.findById(id);
        if (!warehousingTaskRecordOpt.isPresent()) {
            throw new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT);
        }
        WarehousingTaskRecord task = warehousingTaskRecordOpt.get();
        if (Objects.equals(task.getCalculateDwTestItem(), 0)) {
            throw new BpmsException(ResponseCode.Business.TASK_NOT_NEED_RELOAD);
        }
        int updateCnt = warehousingTaskRecordRepository.updateProcessStatus(Lists.newArrayList(task.getId()), null, Constant.EMPTY, ProcessStatus.RELOADING, ProcessStatus.FREEZE);
        if (updateCnt == 0) {
            throw new BpmsException(ResponseCode.Common.DATA_HAS_BEEN_DEAL);
        }
        List<Long> expireDeleteRecordIds = new ArrayList<>();
        try {
            List<ExpireDeleteRecord> expireDeleteRecords = expireDeleteRecordRepository.findAllByCustomerAndSubCustomerAndFactoryAndFactorySiteAndTestAreaAndLotTypeAndDeviceIdAndLotIdAndWaferNoAndTestStageAndFileCategoryAndPlatformAndProcessStatus(
                    task.getCustomer(), task.getSubCustomer(), task.getFactory(), task.getFactorySite(), task.getTestArea(), task.getLotType(), task.getDeviceId(),
                    task.getLotId(), task.getWaferNo(), task.getTestStage(), task.getFileCategory(), Platform.CK, ProcessStatus.FREEZE
            );
            expireDeleteRecordIds = expireDeleteRecords.stream().map(ExpireDeleteRecord::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(expireDeleteRecords)) {
                throw new BpmsException(ResponseCode.Business.TASK_PLEASE_TRY_REPLAY);
            }
            Map<String, String> params = new HashMap<String, String>() {{
                put("customer", task.getCustomer());
                put("subCustomer", task.getSubCustomer());
                put("factory", task.getFactory());
                put("factorySite", task.getFactorySite());
                put("testArea", task.getTestArea().getArea());
                put("deviceId", task.getDeviceId());
                put("lotId", task.getLotId());
                put("waferNo", task.getWaferNo());
                put("lotType", task.getLotType().getType());
                put("testStage", task.getTestStage());
                put("fileCategory", task.getFileCategory().getCategory());
            }};
            List<LotStockingDetail> lotStockingDetails = lotStockingDetailRepository.findAllByCustomerAndTestAreaAndFactoryAndFactorySiteAndDeviceIdAndLotTypeAndTestStageAndLotIdAndWaferNoAndFileCategory(
                    task.getCustomer(),
                    task.getTestArea(),
                    task.getFactory(),
                    task.getFactorySite(),
                    task.getDeviceId(),
                    task.getLotType(),
                    task.getTestStage(),
                    task.getLotId(),
                    task.getWaferNo(),
                    task.getFileCategory()
            );
            long dieCnt = calDieCnt(lotStockingDetails);
            long testItemCnt = calTestItemCnt(lotStockingDetails);
            String computeCode = SparkReloadTaskFinishCallback.supportComputeCodeMap.get(task.getFileCategory());
            ComputeResponse computeResponse = computeRpcService.submit(computeCode, generateAppName(computeCode, task), dieCnt, testItemCnt, params);
            if (!ComputeResponse.SUCCESS.equals(computeResponse.getCode())) {
                throw new RuntimeException(JSON.toJSONString(computeResponse));
            }
            expireDeleteRecordRepository.updateProcessStatus(expireDeleteRecordIds, computeResponse.getUniqueId(), null, Constant.EMPTY, ProcessStatus.RELOADING, ProcessStatus.FREEZE);
            lotStockingDetailRepository.updateUpdateTime(lotStockingDetails.stream().map(LotStockingDetail::getId).collect(Collectors.toList()));
        } catch (Exception e) {
            log.info("异常：", e);
            warehousingTaskRecordRepository.updateProcessStatus(Lists.newArrayList(task.getId()), null, Constant.EMPTY, ProcessStatus.FREEZE, ProcessStatus.RELOADING);
            if (CollectionUtils.isNotEmpty(expireDeleteRecordIds)) {
                expireDeleteRecordRepository.updateProcessStatus(expireDeleteRecordIds, Constant.EMPTY, null, Constant.EMPTY, ProcessStatus.FREEZE, ProcessStatus.RELOADING);
            }
            throw e;
        }
    }

    public String generateAppName(String prefix, WarehousingTaskRecord task) {
        StringBuilder builder = new StringBuilder(prefix + MIDDLE_LINE)
                .append(task.getFileCategory().getCategory())
                .append("/TEST_AREA=").append(task.getTestArea().getArea())
                .append("/CUSTOMER=").append(task.getCustomer())
                .append("/FACTORY=").append(task.getFactory())
                .append("/DEVICE_ID=").append(task.getDeviceId())
                .append("/LOT_ID=").append(task.getLotId());
        if (StringUtils.isNotBlank(task.getWaferNo())) {
            builder.append("/WAFER_NO=").append(task.getWaferNo());
        }
        builder
                .append("/TEST_STAGE=").append(task.getTestStage())
                .append("/LOT_TYPE=").append(task.getLotType().getType());
        return builder.toString();
    }

    public void finishReload(List<ExpireDeleteRecord> expireDeleteRecords, ComputeResultMessage computeResultMessage) {
        ProcessStatus processStatus = ComputeUtil.convertProcessStatus(computeResultMessage.getProcessStatus());
        ExceptionType exceptionType = processStatus == ProcessStatus.SUCCESS ? null : ComputeUtil.convertExceptionType(computeResultMessage.getExceptionType());
        String errorMsg = processStatus == ProcessStatus.SUCCESS ? Constant.EMPTY : ResponseCode.Business.TASK_PLEASE_TRY_REPLAY.msg() + "\n" + computeResultMessage.getErrorMessage();
        ProcessStatus reloadStatus = processStatus == ProcessStatus.SUCCESS ? ProcessStatus.RELOAD_SUCCESS : ProcessStatus.RELOAD_FAIL;
        ExpireDeleteRecord head = expireDeleteRecords.get(0);

        if (reloadStatus == ProcessStatus.RELOAD_SUCCESS) {
            List<LotWaferWarehousingRecord> lotWaferWarehousingRecords = lotWaferWarehousingRecordRepository.findAllByCustomerAndTestAreaAndFactoryAndDeviceIdAndLotTypeAndTestStageAndLotIdAndWaferNoAndFileCategory(
                    head.getCustomer(), head.getTestArea(), head.getFactory(), head.getDeviceId(), head.getLotType(), head.getTestStage(), head.getLotId(), head.getWaferNo(), head.getFileCategory()
            );
            if (CollectionUtils.isNotEmpty(lotWaferWarehousingRecords)) {
                lotWaferWarehousingRecordRepository.updateFreezeFlag(
                        lotWaferWarehousingRecords.stream().map(LotWaferWarehousingRecord::getId).collect(Collectors.toList()),
                        0
                );
            }
        }

        List<WarehousingTaskRecord> warehousingTaskRecords = warehousingTaskRecordRepository.findAllByCustomerAndSubCustomerAndFactoryAndFactorySiteAndTestAreaAndLotTypeAndDeviceIdAndLotIdAndWaferNoAndTestStageAndFileCategoryAndProcessStatus(
                head.getCustomer(), head.getSubCustomer(), head.getFactory(), head.getFactorySite(), head.getTestArea(), head.getLotType(), head.getDeviceId(),
                head.getLotId(), head.getWaferNo(), head.getTestStage(), head.getFileCategory(), ProcessStatus.RELOADING
        );
        if (CollectionUtils.isNotEmpty(warehousingTaskRecords)) {
            warehousingTaskRecordRepository.updateProcessStatus(
                    warehousingTaskRecords.stream().map(WarehousingTaskRecord::getId).collect(Collectors.toList()),
                    exceptionType,
                    errorMsg,
                    reloadStatus,
                    ProcessStatus.RELOADING
            );
        }
        expireDeleteRecordRepository.updateProcessStatus(
                expireDeleteRecords.stream().map(ExpireDeleteRecord::getId).collect(Collectors.toList()),
                head.getUniqueId(),
                exceptionType,
                errorMsg,
                reloadStatus,
                ProcessStatus.RELOADING
        );
    }

    public static Long defaultDieDataCntValue(Long value) {
        // 历史数据的dieDataCnt可能没有值
        return value == null ? 10000L : value;
    }

    public static Long defaultDieDataCntValue(Integer value) {
        // 历史数据的dieDataCnt可能没有值
        return value == null ? 10000L : value.longValue();
    }

    public static Long defaultTestItemDataCntValue(Long value) {
        // 历史数据的testItemDataCnt可能没有值
        return value == null ? 100000L : value;
    }

    public static Long calDieCnt(List<LotStockingDetail> lotStockingDetails) {
        return lotStockingDetails.stream().mapToLong(t -> defaultDieDataCntValue(t.getDieDataCount())).sum();
    }

    public static Long calTestItemCnt(List<LotStockingDetail> lotStockingDetails) {
        return lotStockingDetails.stream().mapToLong(t -> defaultTestItemDataCntValue(t.getTestItemDataCount())).sum();
    }
}
