package com.guwave.datahub.bpms.app.vo.bz.task.request;

import com.guwave.datahub.bpms.app.vo.bz.task.response.DmtTaskDetailVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/13
 * @description YmsTaskRerunSingleVo
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(title = "task 需要重跑的id")
public class DmtTaskRerunSingleVo {

    @Schema(title = "需要重跑的task id", example = "1")
    @NotNull(message = "id")
    private Long id;

    @Schema(title = "dmtTaskDetailVos", description = "具体计算任务列表")
    private List<DmtTaskDetailVo> dmtTaskDetailVos;
}
