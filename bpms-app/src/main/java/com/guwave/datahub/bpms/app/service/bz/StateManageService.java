package com.guwave.datahub.bpms.app.service.bz;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.guwave.datahub.bpms.app.exception.BpmsParamException;
import com.guwave.datahub.bpms.app.vo.bz.stateManage.model.ModuleSwitch;
import com.guwave.datahub.bpms.app.vo.bz.stateManage.model.ModuleTask;
import com.guwave.datahub.bpms.app.vo.bz.stateManage.model.ModuleTaskType;
import com.guwave.datahub.bpms.app.vo.bz.stateManage.model.TableData;
import com.guwave.datahub.bpms.app.vo.bz.stateManage.response.StateManageDetailVo;
import com.guwave.datahub.bpms.dao.repository.bpms.BpmsModuleScheduledControlSwitchRepository;
import com.guwave.datahub.bpms.dao.repository.dw.OnedataModuleScheduledControlSwitchRepository;
import com.guwave.datahub.bpms.dao.repository.linkx.LinkxModuleScheduledControlSwitchRepository;
import com.guwave.onedata.dataware.bridge.api.iface.*;
import com.guwave.onedata.dataware.common.contant.BatchInfoStatus;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.*;
import com.guwave.onedata.linkx.bridge.api.iface.ILinkxSchedulerOneClickRepairRpcService;
import com.guwave.onedata.linkx.dao.mysql.domain.bz.BzAppInstance;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputePool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.Table;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;


@Slf4j
@Service
public class StateManageService {

    private final Map<ModuleTaskType, Supplier<Pair<String, List<?>>>> queryModuleTaskMap = new LinkedHashMap<ModuleTaskType, Supplier<Pair<String, List<?>>>>() {{
        put(ModuleTaskType.COMPUTE_SCHEDULER_PLATFORM_TASK_CREATE, () ->
                Pair.create(getEntityTableName(ComputePool.class),
                        computeComputePoolRepository.findByProcessStatusInOrderByIdDesc(Lists.newArrayList(com.guwave.onedata.next.compute.common.constant.ProcessStatus.CREATE))));
        put(ModuleTaskType.COMPUTE_SCHEDULER_PLATFORM_TASK_PROCESSING, () ->
                Pair.create(getEntityTableName(ComputePool.class),
                        computeComputePoolRepository.findByProcessStatusInOrderByIdDesc(Lists.newArrayList(com.guwave.onedata.next.compute.common.constant.ProcessStatus.COMMITTED, com.guwave.onedata.next.compute.common.constant.ProcessStatus.PROCESSING))));
        put(ModuleTaskType.COLLECTX_PULL_FILE_FROM_FTP_TASK, () ->
                Pair.create(getEntityTableName(SftpBatchInfo.class),
                        onedataSftpBatchInfoRepository.findAllByBatchStatusOrderByIdDesc(BatchInfoStatus.PROCESSING)));
        put(ModuleTaskType.COLLECTX_PYTHON_SCRIPT_CONVERT_TASK, () ->
                Pair.create(getEntityTableName(SftpFileDetail.class),
                        onedataSftpFileDetailRepository.findByProcessStatusAndConvertFlagOrderByIdDesc(ProcessStatus.PROCESSING, 1)));
        put(ModuleTaskType.COLLECTX_PYTHON_SCRIPT_MERGE_LOG_MAP_TASK, () ->
                Pair.create(getEntityTableName(LogMapDetail.class),
                        onedataLogMapDetailRepository.findByProcessStatusOrderByIdDesc(ProcessStatus.PROCESSING)));
        put(ModuleTaskType.SOURCE_AGENT_PRE_PARSE_TASK, () ->
                Pair.create(getEntityTableName(SftpFileDetail.class),
                        onedataSftpFileDetailRepository.findByProcessStatusAndConvertFlagOrderByIdDesc(ProcessStatus.PROCESSING, 0)));
        put(ModuleTaskType.SOURCE_AGENT_PARSE_TASK, () ->
                Pair.create(getEntityTableName(LotMetaDataDetail.class),
                        onedataLotMetaDataDetailRepository.findByProcessStatusOrderByIdDesc(ProcessStatus.PROCESSING)));
        put(ModuleTaskType.DIE_DATAWARE_CALCULATE_TASK_CREATE, () ->
                Pair.create(getEntityTableName(LayerCalculatePool.class),
                        onedataLayerCalculatePoolRepository.findByProcessStatusInOrderByIdDesc(Lists.newArrayList(ProcessStatus.CREATE))));
        put(ModuleTaskType.DIE_DATAWARE_CALCULATE_TASK_PROCESSING, () ->
                Pair.create(getEntityTableName(LayerCalculatePool.class),
                        onedataLayerCalculatePoolRepository.findByProcessStatusInOrderByIdDesc(Lists.newArrayList(ProcessStatus.PROCESSING))));
        put(ModuleTaskType.MES_DATAWARE_CALCULATE_TASK, () ->
                Pair.create(getEntityTableName(MesParseLog.class),
                        onedataMesParseLogRepository.findByProcessStatusOrderByIdDesc(ProcessStatus.PROCESSING)));
        put(ModuleTaskType.MANUAL_DATAWARE_CALCULATE_TASK, () ->
                Pair.create(getEntityTableName(ManualCalculateTask.class),
                        onedataManualCalculateTaskRepository.findByProcessStatusOrderByIdDesc(ProcessStatus.PROCESSING)));
        put(ModuleTaskType.MANUAL_DELETE_FILE_TASK, () ->
                Pair.create(getEntityTableName(ManualDeleteTask.class),
                        onedataManualDeleteTaskRepository.findByProcessStatusOrderByIdAsc(ProcessStatus.PROCESSING)));
        put(ModuleTaskType.LINKX_SCHEDULER_TASK, () ->
                Pair.create(getEntityTableName(BzAppInstance.class),
                        linkxBzAppInstanceRepository.findByProcessStatusOrderByIdDesc(ProcessStatus.PROCESSING)));
    }};


    @Value("${database.name.bpms}")
    private String bpmsDb;
    @Value("${database.name.dw}")
    private String onedataDb;
    @Value("${database.name.linkx}")
    private String linkxDb;
    @Value("${database.name.compute}")
    private String computeDb;

    @Autowired
    private StateManageService stateManageService;
    @Resource
    private BpmsModuleScheduledControlSwitchRepository bpmsModuleScheduledControlSwitchRepository;
    @Resource
    private OnedataModuleScheduledControlSwitchRepository onedataModuleScheduledControlSwitchRepository;
    @Resource
    private LinkxModuleScheduledControlSwitchRepository linkxModuleScheduledControlSwitchRepository;
    @Resource
    private com.guwave.datahub.bpms.dao.repository.dw.SftpBatchInfoRepository onedataSftpBatchInfoRepository;
    @Resource
    private com.guwave.datahub.bpms.dao.repository.dw.SftpFileDetailRepository onedataSftpFileDetailRepository;
    @Resource
    private com.guwave.datahub.bpms.dao.repository.dw.LogMapDetailRepository onedataLogMapDetailRepository;
    @Resource
    private com.guwave.datahub.bpms.dao.repository.dw.LotMetaDataDetailRepository onedataLotMetaDataDetailRepository;
    @Resource
    private com.guwave.datahub.bpms.dao.repository.dw.LotStockingDetailRepository onedataLotStockingDetailRepository;
    @Resource
    private com.guwave.datahub.bpms.dao.repository.dw.LayerCalculatePoolRepository onedataLayerCalculatePoolRepository;
    @Resource
    private com.guwave.datahub.bpms.dao.repository.dw.MesParseLogRepository onedataMesParseLogRepository;
    @Resource
    private com.guwave.datahub.bpms.dao.repository.dw.ManualCalculateTaskRepository onedataManualCalculateTaskRepository;
    @Resource
    private com.guwave.datahub.bpms.dao.repository.dw.ManualDeleteTaskRepository onedataManualDeleteTaskRepository;
    @Resource
    private com.guwave.datahub.bpms.dao.repository.linkx.BzAppInstanceRepository linkxBzAppInstanceRepository;
    @Resource
    private com.guwave.datahub.bpms.dao.repository.compute.ComputePoolRepository computeComputePoolRepository;
    @Resource
    private com.guwave.datahub.bpms.dao.repository.compute.ResidentTaskRepository computeResidentTaskRepository;
    @Resource
    private com.guwave.datahub.bpms.dao.repository.compute.ResidentProcessStatusRepository computeResidentProcessStatusRepository;
    @DubboReference
    private ICollectxOneClickRepairRpcService collectxOneClickRepairRpcService;
    @DubboReference
    private ISourceAgentCpOneClickRepairRpcService sourceAgentCpOneClickRepairRpcService;
    @DubboReference
    private ISourceAgentFtOneClickRepairRpcService sourceAgentFtOneClickRepairRpcService;
    @DubboReference
    private ISourceAgentWatOneClickRepairRpcService sourceAgentWatOneClickRepairRpcService;
    @DubboReference
    private IDwSchedulerOneClickRepairRpcService dwSchedulerOneClickRepairRpcService;
    @DubboReference
    private ILinkxSchedulerOneClickRepairRpcService linkxSchedulerOneClickRepairRpcService;

    public StateManageDetailVo queryStateManageData() {
        StateManageDetailVo stateManageDetailVo = new StateManageDetailVo();
        stateManageDetailVo.setModuleSwitchDatas(queryModuleSwitch());
        stateManageDetailVo.setModuleTasks(queryModuleTask());
        return stateManageDetailVo;
    }

    private List<ModuleSwitch> queryModuleSwitch() {
        List<ModuleSwitch> moduleSwitches = new ArrayList<>();
        moduleSwitches.addAll(convertToModuleSwitch(bpmsModuleScheduledControlSwitchRepository.findAll(), bpmsDb));
        moduleSwitches.addAll(convertToModuleSwitch(onedataModuleScheduledControlSwitchRepository.findAll(), onedataDb));
        moduleSwitches.addAll(convertToModuleSwitch(linkxModuleScheduledControlSwitchRepository.findAll(), linkxDb));
        return moduleSwitches;
    }

    private List<ModuleTask> queryModuleTask() {
        ArrayList<ModuleTask> moduleTasks = new ArrayList<>();
        queryModuleTaskMap.forEach((key, value) -> {
            Pair<String, List<?>> tableNameWithDatas = value.get();
            List<TableData> tableDatas = tableNameWithDatas.getValue().stream().map(t -> {
                String jsonData = JSON.toJSONString(t);
                TableData tableData = JSON.parseObject(jsonData, TableData.class);
                tableData.setTable(tableNameWithDatas.getFirst());
                tableData.setDetail(t);
                return tableData;
            }).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(tableDatas)) {
                ModuleTask moduleTask = new ModuleTask();
                moduleTask.setModuleTaskType(key);
                moduleTask.setTableDatas(tableDatas);
                moduleTasks.add(moduleTask);
            }
        });
        return moduleTasks;
    }

    private static List<ModuleSwitch> convertToModuleSwitch(Iterable<?> datas, String db) {
        return Lists.newArrayList(datas).stream().map(t -> {
            ModuleSwitch moduleSwitch = JSON.parseObject(JSON.toJSONString(t), ModuleSwitch.class);
            moduleSwitch.setDb(db);
            return moduleSwitch;
        }).collect(Collectors.toList());
    }

    public void onlyRepairCollectx() {
        log.info("只修复Collectx");
        synchronized (StateManageService.class) {
            // 关闭开关 等待10s
            switchAllModule(1);
            try {
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                // nothing
            }
            // 直接修复数据
            repairCollectx(false);
            doCatchException(() -> collectxOneClickRepairRpcService.oneClickRepair());
        }
    }

    public void onlyRepairProcessing() {
        log.info("只修复PROCESSING");
        synchronized (StateManageService.class) {
            // 关闭开关 等待10s
            switchAllModule(1);
            try {
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                // nothing
            }
            // 直接修复数据
            repairAllData(true);
        }
    }

    public void forceOneClickRepair() {
        log.info("强制一键修复");
        synchronized (StateManageService.class) {
            // 关闭开关 等待10s
            switchAllModule(1);
            try {
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                // nothing
            }
            // 直接修复数据
            repairAllData(false);
        }
    }

    public void switchModuleStopFlag(List<ModuleSwitch> modules) {
        Map<String, List<ModuleSwitch>> dbWithModules = modules.stream().collect(Collectors.groupingBy(ModuleSwitch::getDb, Collectors.toList()));
        synchronized (StateManageService.class) {
            dbWithModules.forEach((key, value) -> {
                Integer stopFlag = value.get(0).getStopFlag();
                List<String> names = value.stream().map(ModuleSwitch::getModule).collect(Collectors.toList());
                if (Objects.equals(key, bpmsDb)) {
                    bpmsModuleScheduledControlSwitchRepository.updateStopFlag(names, stopFlag);
                } else if (Objects.equals(key, onedataDb)) {
                    onedataModuleScheduledControlSwitchRepository.updateStopFlag(names, stopFlag);
                } else if (Objects.equals(key, linkxDb)) {
                    linkxModuleScheduledControlSwitchRepository.updateStopFlag(names, stopFlag);
                }
            });
        }
    }

    public void switchAllModule(Integer stopFlag) {
        if (!Lists.newArrayList(0, 1).contains(stopFlag)) {
            throw new BpmsParamException("stopFlag 值不合法！");
        }
        synchronized (StateManageService.class) {
            bpmsModuleScheduledControlSwitchRepository.updateAllStopFlag(stopFlag);
            onedataModuleScheduledControlSwitchRepository.updateAllStopFlag(stopFlag);
            linkxModuleScheduledControlSwitchRepository.updateAllStopFlag(stopFlag);
        }
    }

    public void repairAllData(boolean onlyRepairProcessingFlag) {
        stateManageService.repairCollectx(onlyRepairProcessingFlag);
        doCatchException(() -> collectxOneClickRepairRpcService.oneClickRepair());
        stateManageService.repairSourceAgent(onlyRepairProcessingFlag);
        doCatchException(() -> sourceAgentCpOneClickRepairRpcService.oneClickRepair());
        doCatchException(() -> sourceAgentFtOneClickRepairRpcService.oneClickRepair());
        doCatchException(() -> sourceAgentWatOneClickRepairRpcService.oneClickRepair());
        stateManageService.repairOnedataScheduler(onlyRepairProcessingFlag);
        doCatchException(() -> dwSchedulerOneClickRepairRpcService.oneClickRepair());
        stateManageService.repairLinkxScheduler(onlyRepairProcessingFlag);
        doCatchException(() -> linkxSchedulerOneClickRepairRpcService.oneClickRepair());
        stateManageService.repairComputeScheduler(onlyRepairProcessingFlag);
    }

    private void doCatchException(Runnable runnable) {
        try {
            runnable.run();
        } catch (Throwable e) {
            log.info("执行出错，忽略异常：", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void repairCollectx(boolean onlyRepairProcessingFlag) {
        log.info("把collextx的ftp正在拉取中的记录全部变成失败状态");
        onedataSftpBatchInfoRepository.updateProcessingToFail();

        log.info("把collextx的1000拉取步骤正在上传到hdfs的文件全部删掉");
        onedataSftpFileDetailRepository.deleteTransferProcessing();

        log.info("把collextx的1000拉取步骤拉取到但还不可解析的文件全部变成可解析状态");
        onedataSftpFileDetailRepository.updateBatchProcessingToSuccess();

        log.info("把collextx的2100、2200脚本转换步骤的正在处理中的文件全部变成待处理状态");
        onedataSftpFileDetailRepository.updateConvertProcessingToCreate();

        log.info("把collextx的2200的log、map脚本合并步骤的正在处理中的文件全部变成待处理状态");
        onedataLogMapDetailRepository.updateMergeProcessingToCreate();
    }

    @Transactional(rollbackFor = Exception.class)
    public void repairSourceAgent(boolean onlyRepairProcessingFlag) {
        log.info("把source-agent的3100预解析步骤的正在处理中的文件全部变成待处理状态");
        onedataSftpFileDetailRepository.updateParseProcessingToCreate();

        log.info("把source-agent的4100正式解析步骤的正在处理中的文件全部变成待处理状态");
        onedataLotMetaDataDetailRepository.updateProcessingToCreate();
    }

    @Transactional(rollbackFor = Exception.class)
    public void repairOnedataScheduler(boolean onlyRepairProcessingFlag) {
        if (onlyRepairProcessingFlag) {
            log.info("把dw的5100、6100、7100数仓计算步骤的处理中的文件的layer_calculate_pool全部改成待处理状态");
            onedataLayerCalculatePoolRepository.updateProcessingToCreate();

            log.info("把dw的dws mes patch的正在处理中的记录全部改成待处理状态");
            onedataMesParseLogRepository.updateProcessingToCreate();

            log.info("把手动上传的ODS正在处理中的记录全部改成待处理状态");
            onedataManualCalculateTaskRepository.updateOdsProcessingToCreate();

            log.info("把手动上传的DWD正在处理中的记录全部改成失败状态");
            onedataManualCalculateTaskRepository.updateDwdProcessingToFail();

            log.info("把手动上传的DELETE正在处理中的记录全部改成待处理状态");
            onedataManualDeleteTaskRepository.updateProcessingToCreate();
        } else {
            log.info("把dw的5100、6100、7100数仓计算步骤的未结束的文件的layer_calculate_pool全部改成失败状态");
            onedataLayerCalculatePoolRepository.updateNotCalFinishToFail();

            log.info("把dw的5100、6100、7100数仓计算步骤的未结束的文件全部变成4100正式解析步骤的待处理状态");
            onedataLotMetaDataDetailRepository.updateNotCalFinishToCreate();

            log.info("把dw的5100、6100、7100数仓计算步骤的未结束的文件全部从lot_stocking_detail删掉");
            onedataLotStockingDetailRepository.deleteNotCalFinish();

            log.info("把dw的dws mes patch的正在处理中的记录全部改成待处理状态");
            onedataMesParseLogRepository.updateProcessingToCreate();

            log.info("把手动上传的ODS正在处理中的记录全部改成待处理状态");
            onedataManualCalculateTaskRepository.updateOdsProcessingToCreate();

            log.info("把手动上传的DWD正在处理中的记录全部改成失败状态");
            onedataManualCalculateTaskRepository.updateDwdProcessingToFail();

            log.info("把手动上传的DELETE正在处理中的记录全部改成待处理状态");
            onedataManualDeleteTaskRepository.updateProcessingToCreate();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void repairLinkxScheduler(boolean onlyRepairProcessingFlag) {
        if (onlyRepairProcessingFlag) {
            log.info("把linkx的正在处理中的记录全部改成待处理状态");
            linkxBzAppInstanceRepository.updateProcessingToCreate();
        } else {
            log.info("把linkx的正在处理中的记录全部改成待处理状态");
            linkxBzAppInstanceRepository.updateProcessingToCreate();
            log.info("把linkx的TEST_ITEM的待处理记录全部改成失败状态");
            linkxBzAppInstanceRepository.updateTestItemCreateToFail();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void repairComputeScheduler(boolean onlyRepairProcessingFlag) {
        log.info("把compute_pool的未结束的记录全部改成失败状态");
        computeComputePoolRepository.updateNotFinishToFail();

        log.info("把resident_task的未结束的记录全部改成失败状态");
        computeResidentTaskRepository.updateNotFinishToFail();

        log.info("把resident_process_status的正在运行中的记录全部改成停止状态");
        computeResidentProcessStatusRepository.updateNotFinishToDead();
    }


    private static String getEntityTableName(Class clazz) {
        Table table = (Table) clazz.getDeclaredAnnotation(Table.class);
        if (table == null) {
            throw new RuntimeException(clazz.getName() + "获取表名失败");
        }
        String tableName = table.name();
        if (StringUtils.isEmpty(tableName)) {
            throw new RuntimeException(clazz.getName() + "表名为空");
        }
        return tableName;
    }
}
