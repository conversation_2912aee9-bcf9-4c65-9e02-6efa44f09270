package com.guwave.datahub.bpms.app.vo.bz.clearRulePreview.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;

@Schema(title = "下拉框过滤条件请求体")
@Data
public class ClearRulePreviewDropDownFilterVo {

    @NotBlank(message = "过滤字段")
    @Schema(title = "当前的过滤字段", description = "当前的过滤字段，不能为空，取值为：TEST_AREA,DEVICE_ID,LOT_ID,WAFER_NO,LOT_TYPE,TEST_STAGE,FACTORY,FACTORY_SITE,SBLOT_ID,EXCEPTION_TYPE", example = "LOT_ID")
    private String filterField;

    @Schema(title = "testArea", example = "CP")
    private String testArea;

    @Schema(title = "deviceId", example = "device1")
    private String deviceId;

    @Schema(title = "lotId", example = "lot1")
    private String lotId;

    @Schema(title = "waferNo", example = "1")
    private String waferNo;

    @Schema(title = "lotType", example = "PRODUCTION")
    private String lotType;

    @Schema(title = "testStage", example = "CP1")
    private String testStage;

    @Schema(title = "factory", example = "TEST")
    private String factory;

    @Schema(title = "factorySite", example = "TEST")
    private String factorySite;

    @Schema(title = "sblotId", example = "sblot1")
    private String sblotId;

    @Schema(title = "exceptionType", example = "STDF_DATA_EXCEPTION")
    private String exceptionType;
}
