package com.guwave.datahub.bpms.app.service.bz;

import com.guwave.datahub.bpms.dao.domain.processMessage.KafkaFailMessageRecord;
import com.guwave.datahub.bpms.dao.repository.bpms.KafkaFailMessageRecordRepository;
import com.guwave.datahub.bpms.dao.repository.compute.ComputeFailMessageRecordRepository;
import com.guwave.datahub.bpms.dao.repository.dw.DatawareFailMessageRecordRepository;
import com.guwave.datahub.bpms.dao.repository.linkx.BzLinkxFailMessageRecordRepository;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.DatawareFailMessageRecord;
import com.guwave.onedata.linkx.dao.mysql.domain.bz.BzLinkxFailMessageRecord;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputeFailMessageRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PullFailMessageService {
    @Autowired
    private DatawareFailMessageRecordRepository datawareFailMessageRecordRepository;
    @Autowired
    private BzLinkxFailMessageRecordRepository bzLinkxFailMessageRecordRepository;
    @Autowired
    private ComputeFailMessageRecordRepository computeFailMessageRecordRepository;
    @Autowired
    private KafkaFailMessageRecordRepository kafkaFailMessageRecordRepository;

    public void pullDatawareFailMessage() {
        List<DatawareFailMessageRecord> datawareFailMessageRecords = datawareFailMessageRecordRepository.findAllByProcessStatusAndDeleteFlag(ProcessStatus.FAIL, 0);
        if (CollectionUtils.isNotEmpty(datawareFailMessageRecords)) {
            log.info("dataware 需要重新发送 {} 条消息", datawareFailMessageRecords.size());
            try {
                List<KafkaFailMessageRecord> kafkaFailMessageRecords = datawareFailMessageRecords.stream().map(KafkaFailMessageRecord::of).collect(Collectors.toList());
                kafkaFailMessageRecordRepository.saveAll(kafkaFailMessageRecords);
                List<DatawareFailMessageRecord> updateRecords = datawareFailMessageRecords.stream()
                        .peek(t -> {
                            t.setUpdateTime(new Date());
                            t.setProcessStatus(ProcessStatus.SUCCESS);
                            t.setDeleteFlag(1);
                        })
                        .collect(Collectors.toList());
                datawareFailMessageRecordRepository.saveAll(updateRecords);
                log.info("将 {} 条发送失败消息记入 bz_kafka_fail_message_record ", updateRecords.size());
            } catch (Exception e) {
                log.error("dataware 需要重新发送的消息记入bz_kafka_fail_message_record失败", e);
            }
        } else {
            log.info("dataware 没有需要重新发送的消息");
        }
    }

    public void pullLinkxFailMessage() {
        List<BzLinkxFailMessageRecord> linkxFailMessageRecords = bzLinkxFailMessageRecordRepository.findAllByProcessStatusAndDeleteFlag(ProcessStatus.FAIL, 0);
        if (CollectionUtils.isNotEmpty(linkxFailMessageRecords)) {
            log.info("linkx 需要重新发送 {} 条消息", linkxFailMessageRecords.size());
            try {
                List<KafkaFailMessageRecord> kafkaFailMessageRecords = linkxFailMessageRecords.stream().map(KafkaFailMessageRecord::of).collect(Collectors.toList());
                kafkaFailMessageRecordRepository.saveAll(kafkaFailMessageRecords);
                List<BzLinkxFailMessageRecord> updateRecords = linkxFailMessageRecords.stream()
                        .peek(t -> {
                            t.setUpdateTime(new Date());
                            t.setProcessStatus(ProcessStatus.SUCCESS);
                            t.setDeleteFlag(1);
                        })
                        .collect(Collectors.toList());
                bzLinkxFailMessageRecordRepository.saveAll(updateRecords);
                log.info("将 {} 条发送失败消息记入 bz_kafka_fail_message_record ", updateRecords.size());
            } catch (Exception e) {
                log.error("nextCompute 需要重新发送的消息记入bz_kafka_fail_message_record失败", e);
            }
        } else {
            log.info("linkx 没有需要重新发送的消息");
        }
    }

    public void pullNextComputeFailMessage() {
        List<ComputeFailMessageRecord> nextComputeFailMessageRecords = computeFailMessageRecordRepository.findAllByProcessStatusAndDeleteFlag(com.guwave.onedata.next.compute.common.constant.ProcessStatus.FAIL, 0);
        if (CollectionUtils.isNotEmpty(nextComputeFailMessageRecords)) {
            log.info("nextCompute 需要重新发送 {} 条消息", nextComputeFailMessageRecords.size());
            try {
                List<KafkaFailMessageRecord> kafkaFailMessageRecords = nextComputeFailMessageRecords.stream().map(KafkaFailMessageRecord::of).collect(Collectors.toList());
                kafkaFailMessageRecordRepository.saveAll(kafkaFailMessageRecords);
                List<ComputeFailMessageRecord> updateRecords = nextComputeFailMessageRecords.stream()
                        .peek(t -> {
                            t.setUpdateTime(new Date());
                            t.setProcessStatus(com.guwave.onedata.next.compute.common.constant.ProcessStatus.SUCCESS);
                            t.setDeleteFlag(1);
                        })
                        .collect(Collectors.toList());
                computeFailMessageRecordRepository.saveAll(updateRecords);
                log.info("将 {} 条发送失败消息记入 bz_kafka_fail_message_record ", updateRecords.size());
            } catch (Exception e) {
                log.error("nextCompute 需要重新发送的消息记入bz_kafka_fail_message_record失败", e);
            }
        } else {
            log.info("nextCompute 没有需要重新发送的消息");
        }
    }


}
