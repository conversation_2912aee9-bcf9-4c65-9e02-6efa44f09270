package com.guwave.datahub.bpms.app.vo.bz.replay.request;

import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.LotType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@Schema(title = "数据入库记录查询请求体")
@Data
@EqualsAndHashCode(callSuper=false)
public class DataReplayLotWaferQueryVo extends PageableRequestVo {
    @Schema(title = "testArea", description = "CP/FT/WAT等 表示测试阶段大类型", example = "CP")
    private String testArea;

    @Schema(title = "工厂列表", description = "测试工厂", example = "[\"VC\"]")
    private List<String> factoryList;

    @Schema(title = "deviceId列表", example = "[\"SS521BB-DEFAULT\"]")
    private List<String> deviceIdList;

    @Schema(title = "fileCategory", description = "只能单选，取值为：STDF,RAW_DATA,WAT", example = "STDF")
    private FileCategory fileCategory;

    @Schema(title = "lotId列表", example = "[\"NT41A\"]")
    private List<String> lotIdList;

    @Schema(title = "waferNo列表", example = "[\"8\"]")
    private List<String> waferNoList;

    @Schema(title = "sblotId列表", example = "[\"8\"]")
    private List<String> sblotIdList;

    @Schema(title = "lotType", description = "PRODUCTION,ENGINEERING", example = "PRODUCTION")
    private String lotType;

    @Schema(title = "testStage列表", example = "[\"CP1\", \"CP2\", \"CP3\"]")
    private List<String> testStageList;

    @Schema(title = "testProgram列表", example = "[\"TEST_PROG_1\", \"TEST_PROG_2\"]")
    private List<String> testProgramList;

    @Schema(title = "处理步骤", example = "[\"4100\"]")
    private List<Integer> stepList;

    @Schema(title = "入库状态列表", example = "[\"FAIL\"]")
    private List<String> processStatusList;

    @Schema(title = "异常信息", example = "RAW_DATA文件转换异常")
    private String exceptionMessage;

    @Schema(title = "createTime", description = "createTime", example = "[\"2023-09-20 08:14:13\", \"2023-09-21 08:14:13\"]")
    private List<String> createTime;

    @Schema(title = "updateTime", description = "updateTime", example = "[\"2023-09-20 08:14:13\", \"2023-09-21 08:14:13\"]")
    private List<String> updateTime;

}
