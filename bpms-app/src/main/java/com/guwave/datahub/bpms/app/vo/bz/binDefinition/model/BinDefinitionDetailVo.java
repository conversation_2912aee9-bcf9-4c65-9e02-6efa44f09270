package com.guwave.datahub.bpms.app.vo.bz.binDefinition.model;

import com.guwave.datahub.bpms.common.constant.BinPF;
import com.guwave.datahub.bpms.common.constant.BinType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * Bin Definition详细信息VO
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@Schema(title = "Bin Definition详细信息")
public class BinDefinitionDetailVo {

    @Schema(description = "测试程序名称", example = "TestProgram1")
    private String testProgram;

    @NotNull(message = "Bin类型不能为空")
    @Schema(description = "Bin类型", example = "HBIN", required = true)
    private BinType binType;

    @NotNull(message = "Bin编号不能为空")
    @Positive(message = "Bin编号必须为正数")
    @Schema(description = "Bin编号", example = "1", required = true)
    private Long binNum;

    @NotBlank(message = "Bin名称不能为空")
    @Schema(description = "Bin名称", example = "Pass", required = true)
    private String binName;

    @NotNull(message = "Bin P/F不能为空")
    @Schema(description = "Bin P/F", example = "P", required = true)
    private BinPF binPf;
}