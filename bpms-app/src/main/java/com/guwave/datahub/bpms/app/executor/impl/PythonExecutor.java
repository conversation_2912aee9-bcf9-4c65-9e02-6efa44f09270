package com.guwave.datahub.bpms.app.executor.impl;

import com.guwave.datahub.bpms.app.executor.Executor;
import com.guwave.datahub.bpms.common.constant.Constant;
import com.guwave.datahub.bpms.common.constant.ExceptionType;
import com.guwave.datahub.bpms.common.constant.ExecuteCommandException;
import com.guwave.datahub.bpms.common.constant.ExecuteEngine;
import com.guwave.datahub.bpms.dao.domain.schedule.ScheduleTaskRecord;
import com.guwave.onedata.next.compute.api.vo.response.ComputeResponse;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.FutureTask;
import java.util.function.Consumer;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.guwave.datahub.bpms.common.constant.Constant.*;

@Component
public class PythonExecutor implements Executor {

    private static final Logger LOGGER = LoggerFactory.getLogger(PythonExecutor.class);
    private static final String SYSTEM_CODE = "SYSTEM CODE: 1";

    @Value("${spring.adapter.python.install.path}")
    private String pythonPath;

    @Value("${spring.data.clickhouse.username}")
    private String ckUserName;

    @Value("${spring.data.clickhouse.password}")
    private String ckPassword;

    @Value("${spring.ck.dim.database}")
    private String dimDatabase;

    @Value("${spring.ck.dwd.database}")
    private String dwdDatabase;

    @Value("${spring.data.clickhouse.address}")
    private String ckAddress;

    @Value("${spring.data.clickhouse.replica.address}")
    private String replicaAddress;
    @Value("${spring.data.mysql.username}")
    private String mysqlUserName;

    @Value("${spring.data.mysql.password}")
    private String mysqlPassword;

    @Value("${spring.mysql.onedata.database}")
    private String onedataDatabase;

    @Value("${spring.data.mysql.product.username}")
    private String mysqlProductUserName;

    @Value("${spring.data.mysql.product.password}")
    private String mysqlProductPassword;

    @Value("${spring.mysql.product.database}")
    private String productDatabase;

    @Value("${spring.mysql.lotmanager.database}")
    private String lotmanagerDatabase;

    @Value("${spring.data.mysql.address}")
    private String mysqlAddress;

    @Value("${spring.datasource.primary.username}")
    private String bpmsUserName;

    @Value("${spring.datasource.primary.password}")
    private String bpmsPassword;

    @Value("${spring.datasource.primary.database}")
    private String bpmsDatabase;

    @Value("${spring.ambari.server.url}")
    private String ambariServer;
    @Value("${spring.ambari.server.username}")
    private String ambariUser;
    @Value("${spring.ambari.server.password}")
    private String ambariPass;

    @Override
    public Boolean support(ExecuteEngine executeEngine) {
        return executeEngine == ExecuteEngine.PYTHON;
    }

    @Override
    public void doExecute(String command) throws Exception {
        String[] splitCommand = command.split(Pattern.quote(Constant.Pipe));
        StringBuilder errorStringBuilder = new StringBuilder();
        List<String> args = new ArrayList<String>() {{
            add(pythonPath);
            addAll(Arrays.stream(splitCommand).map(String::trim).collect(Collectors.toList()));
        }};
        final List<String> finalargs = replaceCommandParameters(args);
        // 记录实际执行的Python命令
        LOGGER.info("实际执行的Python命令: {}", String.join(SPACE, finalargs));

        LOGGER.info("开始执行脚本: {}", finalargs);
        StopWatch sw = new StopWatch();
        sw.start();
        Process proc = null;
        try {
            proc = Runtime.getRuntime().exec(finalargs.toArray(new String[0]));

            Process finalProc = proc;
            FutureTask<List<String>> inputTask = new FutureTask<>(() -> readInput(finalargs, finalProc, null));
            FutureTask<List<String>> errorTask = new FutureTask<>(() -> readError(finalargs, finalProc, null));
            new Thread(inputTask).start();
            new Thread(errorTask).start();
            List<String> inputList = inputTask.get();
            List<String> errorList = errorTask.get();

            if (CollectionUtils.isEmpty(inputList) || !inputList.stream().anyMatch(line -> line.contains(SYSTEM_CODE))) {
                errorList.forEach(errorLine -> errorStringBuilder.append(errorLine).append(ENTER));
                String errorMessage = errorStringBuilder.toString();
                LOGGER.info("执行脚本 {} , errorMessage: {}", finalargs, errorMessage);
                throw new RuntimeException(errorMessage);
            }

            proc.waitFor();
        } catch (Exception e) {
            LOGGER.error("执行脚本 {} 异常 ", finalargs, e);
            throw new ExecuteCommandException(ExceptionType.EXECUTE_PYTHON_SCRIPT_EXCEPTION, ExceptionUtils.getStackTrace(e));
        } finally {
            if (proc != null) {
                try {
                    proc.getInputStream().close();
                } catch (Exception ignored) {
                }
                try {
                    proc.getErrorStream().close();
                } catch (Exception ignored) {
                }
                try {
                    proc.getOutputStream().close();
                } catch (Exception ignored) {
                }
            }
            sw.stop();
            LOGGER.info("执行 {} 耗时: {}ms", finalargs, sw.getTotalTimeMillis());
        }
        LOGGER.info("执行脚本 {} 成功", finalargs);
    }

    public ComputeResponse doExecute(ScheduleTaskRecord taskRecord) throws Exception {
        return null;
    }

    private List<String> replaceCommandParameters(List<String> args) {
        Map<String, String> parameterMap = new HashMap<>();
        parameterMap.put("{ck_user}", ckUserName);
        parameterMap.put("{ck_password}", ckPassword);
        parameterMap.put("{dim_database}", dimDatabase);
        parameterMap.put("{dwd_database}", dwdDatabase);
        parameterMap.put("{ck_address}", ckAddress);
        parameterMap.put("{replica_address}", replicaAddress);
        parameterMap.put("{mysqlUserName}", mysqlUserName);
        parameterMap.put("{mysqlPassword}", mysqlPassword);
        parameterMap.put("{onedataDatabase}", onedataDatabase);
        parameterMap.put("{mysqlProductUserName}", mysqlProductUserName);
        parameterMap.put("{mysqlProductPassword}", mysqlProductPassword);
        parameterMap.put("{productDatabase}", productDatabase);
        parameterMap.put("{lotmanagerDatabase}", lotmanagerDatabase);
        parameterMap.put("{mysqlAddress}", mysqlAddress);
        parameterMap.put("{bpmsUserName}", bpmsUserName);
        parameterMap.put("{bpmsPassword}", bpmsPassword);
        parameterMap.put("{bpmsDatabase}", bpmsDatabase);
        parameterMap.put("{ambari_server}", ambariServer);
        parameterMap.put("{ambari_user}", ambariUser);
        parameterMap.put("{ambari_pass}", ambariPass);

        String[] addressParts = ckAddress.split("//");
        String ckHost = EMPTY;
        String ckPort = EMPTY;
        if (addressParts.length > 1) {
            String[] hostPortParts = addressParts[1].split(COLON);
            if (hostPortParts.length > 1) {
                ckHost = hostPortParts[0];
                ckPort = hostPortParts[1];
            }
        }
        parameterMap.put("{ck_host}", ckHost);
        parameterMap.put("{ck_port}", ckPort);

        List<String> replacedArgs = new ArrayList<>();
        for (String arg : args) {
            for (Map.Entry<String, String> entry : parameterMap.entrySet()) {
                arg = arg.replace(entry.getKey(), entry.getValue());
            }
            replacedArgs.add(arg);
        }
        return replacedArgs;
    }

    private static List<String> readInput(List<String> args, Process proc, Consumer<String> logConsumer) {
        try (InputStream inputStream = proc.getInputStream();
             InputStreamReader inputStreamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
             BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {
            return read(args, bufferedReader, logConsumer);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static List<String> readError(List<String> args, Process proc, Consumer<String> logConsumer) {
        try (InputStream errorStream = proc.getErrorStream();
             InputStreamReader inputStreamReader = new InputStreamReader(errorStream, StandardCharsets.UTF_8);
             BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {
            return read(args, bufferedReader, logConsumer);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static List<String> read(List<String> args, BufferedReader reader, Consumer<String> logConsumer) throws Exception {
        List<String> resultList = new ArrayList<>();
        String res;
        while ((res = reader.readLine()) != null) {
            LOGGER.info("执行脚本 {} 返回: {}", args, res);
            resultList.add(res);
            if (logConsumer != null) {
                try {
                    logConsumer.accept(res);
                } catch (Exception e) {
                    LOGGER.info("处理日志 {} 时发生异常", res, e);
                }
            }
        }
        return resultList;
    }
}
