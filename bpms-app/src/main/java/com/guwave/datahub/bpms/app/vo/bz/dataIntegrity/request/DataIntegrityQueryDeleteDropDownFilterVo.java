package com.guwave.datahub.bpms.app.vo.bz.dataIntegrity.request;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
@Schema(title = "数据完整性文件删除记录查询条件下拉框")
public class DataIntegrityQueryDeleteDropDownFilterVo {


    @Schema(description = "需要查询的字段", example = "DEVICE")
    private String filterField;

    @Schema(description = "subCustomer列表", example = "[\"SUBCUS1\",\"SUBCUS2\"]")
    private List<String> subCustomerList;

    @Schema(description = "fileName", example = "a.stdf.zip")
    private String fileName;

    @Schema(description = "删除人列表", example = "[\"user_1\",\"user_2\"]")
    private List<String> deleteByList;

    public void validCondition() {
        subCustomerList = convertEmpty(subCustomerList);
        fileName = convertEmpty(fileName);
        deleteByList = convertEmpty(deleteByList);
    }

    private String convertEmpty(String str) {
        if (StringUtils.isEmpty(str)) {
            return null;
        } else {
            return str;
        }
    }

    private List<String> convertEmpty(List<String> strs) {
        if (CollectionUtils.isEmpty(strs)) {
            return null;
        } else {
            return strs;
        }
    }
}
