package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.response;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.guwave.datahub.bpms.dao.domain.warehousingRule.WarehousingRuleFlow;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.TestArea;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_FORMAT;

/**
 * 2025/3/4 16:49
 * WarehousingRuleFlowVo
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(title = "入库规则流程")
public class WarehousingRuleFlowVo {
    @Schema(title = "id")
    private Long id;

    @Schema(description = "流程名称", example = "AMD入库")
    @ExcelProperty("name")
    private String name;

    @Schema(description = "客户名称", example = "AMD")
    private String customer;

    @Schema(description = "子客户名称", example = "AMD")
    @ExcelProperty("subCustomer")
    private String subCustomer;

    @Schema(description = "测试工厂名称", example = "TSMC")
    @ExcelProperty("factory")
    private String factory;

    @Schema(description = "测试阶段类型: CP/FT/WAT/SLT/EQC 等", example = "CP")
    @ExcelProperty("testArea")
    private String testArea;

    @Schema(description = "文件类型", example = "STDF")
    @ExcelProperty("fileCategory")
    private String fileCategory;

    @Schema(description = "备注", example = "备注1")
    @ExcelProperty("remarks")
    private String remarks;

    @Schema(title = "createTime", description = "创建时间", example = "2024-11-15 11:21:00")
    private String createTime;

    @Schema(title = "updateTime", description = "更新时间", example = "2024-11-18 11:21:00")
    private String updateTime;

    @Schema(description = "创建用户", example = "System")
    private String createUser;

    @Schema(description = "更新用户", example = "System")
    private String updateUser;

    public static WarehousingRuleFlowVo of(WarehousingRuleFlow data) {
        return WarehousingRuleFlowVo.builder()
                .id(data.getId())
                .name(data.getName())
                .customer(data.getCustomer())
                .subCustomer(data.getSubCustomer())
                .factory(data.getFactory())
                .testArea(TestArea.of(data.getTestArea()))
                .fileCategory(FileCategory.of(data.getFileCategory()))
                .remarks(data.getRemarks())
                .createTime(DateUtil.format(data.getCreateTime(), NORM_DATETIME_FORMAT))
                .updateTime(DateUtil.format(data.getUpdateTime(), NORM_DATETIME_FORMAT))
                .createUser(data.getCreateUser())
                .updateUser(data.getUpdateUser())
                .build();
    }
}
