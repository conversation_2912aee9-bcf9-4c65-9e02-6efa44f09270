package com.guwave.datahub.bpms.app.configuration.clickhouse;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManager;
import javax.sql.DataSource;
import java.util.Objects;


@Slf4j
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(entityManagerFactoryRef = "entityManagerFactoryDws",
        transactionManagerRef = "transactionManagerDws",
        basePackages = {"com.guwave.datahub.bpms.dao.repository.clickhouse.dws"})
@MapperScan(
        basePackages = "com.guwave.datahub.bpms.dao.repository.clickhouse.dws.mapper",
        sqlSessionFactoryRef = "dwsSqlSessionFactory"
)
public class DwsConfiguration {

    @Autowired
    JpaClickhouseConfiguration jpaClickhouseConfiguration;

    @Value("${spring.ck.dws.database}")
    private String database;

    @Bean(name = "entityManagerDws")
    public EntityManager entityManager() {
        return Objects.requireNonNull(entityManagerFactoryDws().getObject()).createEntityManager();
    }

    @Bean(name = "entityManagerFactoryDws")
    public LocalContainerEntityManagerFactoryBean entityManagerFactoryDws() {
        return jpaClickhouseConfiguration.getEntityManagerFactory(
                database,
                "dwsPersistenceUnit",
                "com.guwave.datahub.bpms.dao.clickhouse.domain.dws");
    }

    @Bean(name = "transactionManagerDws")
    public PlatformTransactionManager transactionManagerDws() {
        return new JpaTransactionManager(Objects.requireNonNull(entityManagerFactoryDws().getObject()));
    }


    @Bean
    public DataSource dwsDataSource() {
        return entityManagerFactoryDws().getDataSource();
    }


    // 配置MyBatis
    @Bean(name = "dwsSqlSessionFactory")
    public SqlSessionFactory DwsSqlSessionFactory() throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dwsDataSource());
        sessionFactory.setMapperLocations(
                ArrayUtils.addAll(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/clickhouse/dws/*.xml"),
                        new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml"))
        );
        return sessionFactory.getObject();

    }

    @Bean(name = "dwsMybatisTransactionManager")
    public PlatformTransactionManager DwsMybatisTransactionManager() {
        return new DataSourceTransactionManager(dwsDataSource());
    }
}
