package com.guwave.datahub.bpms.app.vo.bz.quality.response;


import com.guwave.datahub.bpms.common.constant.ChartType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(title = "指标结果")
@Data
public class DataQualityRuleResultVo {

    @Schema(title = "图表类型", description = "指标类型为初始化时，值为 null <br/>" +
            "指标类型为指标结果时且执行间隔为每天一次时，值为 TABLE ,从 tableResult 取值<br/>" +
            "指标类型为指标结果时且执行间隔为每半小时一次时，值为 LINE ,从 lineResult 取值<br/>", example = "TABLE")
    private ChartType chartType;
    @Schema(title = "表格内容")
    private TableResult tableResult;
    @Schema(title = "折线图内容")
    private LineResult lineResult;

    @Data
    public static class TableResult {
        @Schema(title = "表头列表")
        private List<TableCell> titles;
        @Schema(title = "表行列表")
        private List<List<TableCell>> rows;

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        public static class TableCell {
            @Schema(title = "关联单元格的key", example = "file_name")
            private String key;
            @Schema(title = "单元格显示的内容", example = "file_name")
            private String value;
        }
    }

    @Data
    public static class LineResult {
        @Schema(title = "x轴信息")
        private Axis xAxis;
        @Schema(title = "y轴信息列表", description = "可能有多个y轴")
        private List<Axis> yAxisList;

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        public static class Axis {
            @Schema(title = "轴标题", example = "时间")
            private String title;
            @Schema(title = "轴内容", example = "[\"202401010130\"]")
            private List<String> values;
        }
    }

}
