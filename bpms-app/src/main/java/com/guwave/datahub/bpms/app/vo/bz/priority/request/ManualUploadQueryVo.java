package com.guwave.datahub.bpms.app.vo.bz.priority.request;

import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = false)
@Data
@Schema(description = "手动上传任务查询请求")
public class ManualUploadQueryVo extends PageableRequestVo {
    @Schema(description = "fileName")
    private String fileName;
}
