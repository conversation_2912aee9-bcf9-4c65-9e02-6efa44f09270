package com.guwave.datahub.bpms.app.serialization.avro;

import com.guwave.onedata.dataware.common.model.stdf.StdfData;
import org.apache.avro.Schema;
import org.apache.avro.generic.GenericData;
import org.apache.avro.generic.GenericDatumWriter;
import org.apache.avro.generic.GenericRecord;
import org.apache.avro.io.BinaryEncoder;
import org.apache.avro.io.EncoderFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * StdfDataSerialization
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-10-19 09:57:22
 */
public class StdfDataSerialization {

    private static final String F_CUSTOMER = "f_customer";
    private static final String F_SUB_CUSTOMER = "f_sub_customer";
    private static final String F_FILE_CATEGORY = "f_file_category";
    private static final String F_FACTORY = "f_factory";
    private static final String F_FACTORY_SITE = "f_factory_site";
    private static final String F_TEST_AREA = "f_test_area";
    private static final String F_TEST_STAGE = "f_test_stage";
    private static final String F_LOT_TYPE = "f_lot_type";
    private static final String F_FILE_ID = "f_file_id";
    private static final String F_FILE_NAME = "f_file_name";
    private static final String F_TS = "f_ts";
    private static final String F_DEVICE_ID = "f_device_id";
    private static final String F_LOT_ID = "f_lot_id";
    private static final String F_WAFER_ID = "f_wafer_id";
    private static final String F_TYPE = "f_type";
    private static final String F_JAVA_TYPE = "f_java_type";
    private static final String F_BODY = "f_body";

    private static final String SCHEMA_STR = "{\"type\":\"record\",\"name\":\"StdfData\",\"fields\":[{\"name\":\"f_customer\",\"type\":\"string\"},{\"name\":\"f_sub_customer\",\"type\":\"string\"},{\"name\":\"f_file_category\",\"type\":\"string\"},{\"name\":\"f_factory\",\"type\":\"string\"},{\"name\":\"f_factory_site\",\"type\":[\"string\",\"null\"]},{\"name\":\"f_test_area\",\"type\":\"string\"},{\"name\":\"f_test_stage\",\"type\":\"string\"},{\"name\":\"f_lot_type\",\"type\":\"string\"},{\"name\":\"f_file_id\",\"type\":\"long\"},{\"name\":\"f_file_name\",\"type\":\"string\"},{\"name\":\"f_ts\",\"type\":\"long\"},{\"name\":\"f_device_id\",\"type\":\"string\"},{\"name\":\"f_lot_id\",\"type\":\"string\"},{\"name\":\"f_wafer_id\",\"type\":[\"string\",\"null\"]},{\"name\":\"f_type\",\"type\":\"string\"},{\"name\":\"f_java_type\",\"type\":\"string\"},{\"name\":\"f_body\",\"type\":\"bytes\"}]}";
    private static final Schema SCHEMA;

    public static byte[] serialize(StdfData msg) throws IOException {
        byte[] bytes = null;
        if (null != msg) {
            try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
                BinaryEncoder encoder = EncoderFactory.get().binaryEncoder(out, null);
                GenericDatumWriter<GenericRecord> writer = new GenericDatumWriter<>(SCHEMA);
                GenericRecord payload = new GenericData.Record(SCHEMA);

                payload.put(F_CUSTOMER, msg.getCustomer());
                payload.put(F_SUB_CUSTOMER, msg.getSubCustomer());
                payload.put(F_FILE_CATEGORY, msg.getFileCategory());
                payload.put(F_FACTORY, msg.getFactory());
                payload.put(F_FACTORY_SITE, msg.getFactorySite());
                payload.put(F_LOT_TYPE, msg.getLotType());
                payload.put(F_TEST_AREA, msg.getTestArea());
                payload.put(F_TEST_STAGE, msg.getTestStage());
                payload.put(F_FILE_ID, msg.getFileId());
                payload.put(F_FILE_NAME, msg.getFileName());
                payload.put(F_TS, msg.getTs());
                payload.put(F_DEVICE_ID, msg.getDeviceId());
                payload.put(F_LOT_ID, msg.getLotId());
                payload.put(F_WAFER_ID, msg.getWaferId());
                payload.put(F_TYPE, msg.getType());
                payload.put(F_JAVA_TYPE, msg.getJavaType());
                payload.put(F_BODY, msg.getBody());

                writer.write(payload, encoder);
                encoder.flush();
                out.flush();
                bytes = out.toByteArray();
            }
        }
        return bytes;
    }

    static {
        SCHEMA = new Schema.Parser().parse(SCHEMA_STR);
    }
}
