package com.guwave.datahub.bpms.app.vo.bz.computeManage.request;

import com.guwave.datahub.bpms.app.vo.web.PageableRequestVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = false)
@Data
@Schema(description = "业务计算管理配置更新请求")
public class ComputeManageUpdateVo extends PageableRequestVo {

    @Min(1)
    @Schema(description = "ID")
    private Long id;

    @Schema(description = "是否计算dw测项相关表")
    @NotNull(message = "是否计算dw测项相关表")
    private Integer calculateDwTestItem;

    @Schema(description = "是否计算yms测项相关表")
    @NotNull(message = "是否计算yms测项相关表")
    private Integer calculateYmsTestItem;

    @Schema(description = "状态：0->关闭，1->启动")
    @NotNull(message = "状态")
    private Integer status;
}
