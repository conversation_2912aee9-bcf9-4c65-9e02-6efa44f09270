package com.guwave.datahub.bpms.app.vo.bz.warehousingRule.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(title = "保存DEVICE_ID取值规则")
public class DwSourceStandardDeviceIdRuleRequestSaveVo {
    @Schema(description = "保存DEVICE_ID取值规则")
    List<DwSourceStandardDeviceIdRuleRequestVo> ruleRequestVos;
    @Schema(description = "入库规则流程")
    WarehousingRuleFlowRequestVo warehouseRuleFlowRequestVo;

}
