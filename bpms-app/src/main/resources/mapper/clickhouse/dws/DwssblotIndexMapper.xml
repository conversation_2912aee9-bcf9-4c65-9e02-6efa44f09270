<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guwave.datahub.bpms.dao.repository.clickhouse.dws.mapper.DwsSblotIndexMapper">

    <sql id="queryCond">
        and m.IS_DELETE = 0
        and m.UPLOAD_TYPE = 'AUTO'
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(subCustomerList)">
            and m.SUB_CUSTOMER in
            <foreach collection="subCustomerList" item="subCustomerList" open="(" close=")" separator=",">
                #{subCustomerList}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(factoryList)">
            and m.FACTORY in
            <foreach collection="factoryList" item="factoryList" open="(" close=")" separator=",">
                #{factoryList}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(deviceIdList)">
            and m.DEVICE_ID in
            <foreach collection="deviceIdList" item="deviceIdList" open="(" close=")" separator=",">
                #{deviceIdList}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testProgramList)">
            and m.TEST_PROGRAM in
            <foreach collection="testProgramList" item="testProgramList" open="(" close=")" separator=",">
                #{testProgramList}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testArea)">
            and m.TEST_AREA = #{testArea}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testStageList)">
            and m.TEST_STAGE in
            <foreach collection="testStageList" item="testStageList" open="(" close=")" separator=",">
                #{testStageList}
            </foreach>
        </if>
    </sql>

    <sql id="queryCondWithoutTestProgram">
        and m.IS_DELETE = 0
        and m.UPLOAD_TYPE = 'AUTO'
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(subCustomerList)">
            and m.SUB_CUSTOMER in
            <foreach collection="subCustomerList" item="subCustomerList" open="(" close=")" separator=",">
                #{subCustomerList}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(factoryList)">
            and m.FACTORY in
            <foreach collection="factoryList" item="factoryList" open="(" close=")" separator=",">
                #{factoryList}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(deviceIdList)">
            and m.DEVICE_ID in
            <foreach collection="deviceIdList" item="deviceIdList" open="(" close=")" separator=",">
                #{deviceIdList}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testArea)">
            and m.TEST_AREA = #{testArea}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testStageList)">
            and m.TEST_STAGE in
            <foreach collection="testStageList" item="testStageList" open="(" close=")" separator=",">
                #{testStageList}
            </foreach>
        </if>
    </sql>

    <select id="queryOverviewDropdownFieldFt" resultType="String">
        SELECT
        <choose>
            <when test="filterField == 'SUB_CUSTOMER'">
                m.SUB_CUSTOMER AS subCustomer
            </when>
            <when test="filterField == 'FACTORY'">
                m.FACTORY AS factory
            </when>
            <when test="filterField == 'DEVICE_ID'">
                m.DEVICE_ID AS deviceId
            </when>
            <when test="filterField == 'TEST_PROGRAM'">
                m.TEST_PROGRAM AS testProgram
            </when>
            <when test="filterField == 'TEST_AREA'">
                m.TEST_AREA AS testArea
            </when>
            <when test="filterField == 'TEST_STAGE'">
                m.TEST_STAGE AS testStage
            </when>
            <otherwise>
                m.SUB_CUSTOMER AS subCustomer
            </otherwise>
        </choose>
        FROM dws_sblot_index_cluster m
        WHERE 1 = 1
        AND
        <choose>
            <when test="filterField == 'SUB_CUSTOMER'">
                m.SUB_CUSTOMER IS NOT NULL
            </when>
            <when test="filterField == 'FACTORY'">
                m.FACTORY IS NOT NULL
            </when>
            <when test="filterField == 'DEVICE_ID'">
                m.DEVICE_ID IS NOT NULL
            </when>
            <when test="filterField == 'TEST_PROGRAM'">
                m.TEST_PROGRAM IS NOT NULL
            </when>
            <when test="filterField == 'TEST_AREA'">
                m.TEST_AREA IS NOT NULL
            </when>
            <when test="filterField == 'TEST_STAGE'">
                m.TEST_STAGE IS NOT NULL
            </when>
            <otherwise>
                m.SUB_CUSTOMER IS NOT NULL
            </otherwise>
        </choose>
        <include refid="queryCond"/>
        <include refid="Common.ckTestAreaFtCond"/>
        <include refid="Common.ckCustomerSql"/>

        GROUP BY
        <choose>
            <when test="filterField == 'SUB_CUSTOMER'">
                m.SUB_CUSTOMER
            </when>
            <when test="filterField == 'FACTORY'">
                m.FACTORY
            </when>
            <when test="filterField == 'DEVICE_ID'">
                m.DEVICE_ID
            </when>
            <when test="filterField == 'TEST_PROGRAM'">
                m.TEST_PROGRAM
            </when>
            <when test="filterField == 'TEST_AREA'">
                m.TEST_AREA
            </when>
            <when test="filterField == 'TEST_STAGE'">
                m.TEST_STAGE
            </when>
            <otherwise>
                m.SUB_CUSTOMER
            </otherwise>
        </choose>

        ORDER BY
        <choose>
            <when test="filterField == 'SUB_CUSTOMER'">
                m.SUB_CUSTOMER
            </when>
            <when test="filterField == 'FACTORY'">
                m.FACTORY
            </when>
            <when test="filterField == 'DEVICE_ID'">
                m.DEVICE_ID
            </when>
            <when test="filterField == 'TEST_PROGRAM'">
                m.TEST_PROGRAM
            </when>
            <when test="filterField == 'TEST_AREA'">
                m.TEST_AREA
            </when>
            <when test="filterField == 'TEST_STAGE'">
                m.TEST_STAGE
            </when>
            <otherwise>
                m.SUB_CUSTOMER
            </otherwise>
        </choose>
    </select>

    <select id="selectStatisticsDataFt" resultType="com.guwave.datahub.bpms.dao.domain.qualityOverview.StatisticsIndex">
        SELECT  m.FACTORY                                               AS factory
               ,m.TESTER_NAME                                           AS tester
               ,m.TEST_AREA                                             AS testArea
               ,m.TEST_STAGE                                            AS testStage
               ,m.DEVICE_ID                                             AS deviceId
               ,m.TEST_PROGRAM                                          AS testProgram
               ,m.TOTAL_CNT                                             AS finalTotalCount
               ,m.LOT_ID                                                AS lotId
               ,m.SBLOT_ID                                              AS sbLotId
               ,CONCAT(m.TEST_AREA,':',CONCAT(m.LOT_ID,':',m.WAFER_ID)) AS lotWaferId
               ,m.END_TIME                                              AS endTime
               ,m.CREATE_TIME                                           AS createTime
               ,m.FIRST_YIELD                                           AS firstYield
               ,m.FINAL_YIELD                                           AS finalYield
               ,n.MES_MATCH_TYPE                                        AS summaryMatchType
               ,n.TOTAL_CNT                                             AS summaryCount
               ,n.PASS_CNT                                              AS summaryPassCount
               ,n.LOSS_CNT                                              AS summaryLossCount
               ,n.DAMAGE_CNT                                            AS summaryDefectiveCount
               ,n.PASS_CNT / n.TOTAL_CNT                                AS summaryYield
               ,m.DATA_ACCURACY_CHECK                                   AS dataAccuracyCheck
        FROM dws_sblot_index_cluster m
        GLOBAL LEFT JOIN
        ( SELECT  MES_MATCH_TYPE
                 ,TOTAL_CNT
                 ,PASS_CNT
                 ,LOSS_CNT
                 ,DAMAGE_CNT
                 ,DEVICE_ID
                 ,TEST_AREA
                 ,TEST_STAGE
                 ,LOT_ID
                 ,SBLOT_ID
                 ,WAFER_ID
           FROM dws_lot_wafer_data_check_index_cluster m
           WHERE 1 = 1
           <include refid="queryCondWithoutTestProgram"/>
           <include refid="Common.ckTestAreaFtCond"/>
           <include refid="Common.ckCustomerSql"/>
        ) n
         ON m.DEVICE_ID = n.DEVICE_ID
        AND m.TEST_AREA = n.TEST_AREA
        AND m.TEST_STAGE = n.TEST_STAGE
        AND m.LOT_ID = n.LOT_ID
        AND m.SBLOT_ID = n.SBLOT_ID
        WHERE 1 = 1
        <include refid="queryCond"/>
        <include refid="Common.ckTestAreaFtCond"/>
        <include refid="Common.ckCustomerSql"/>
    </select>
</mapper>