<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guwave.datahub.bpms.dao.repository.clickhouse.dws.mapper.DwsLotWaferDataCheckIndexMapper">

    <sql id="waferNoWhenCP">
        <if test="isCp == true">
            , m.WAFER_NO
        </if>
    </sql>

    <sql id="QueryByLotWaferList">
        and m.CUSTOMER = #{customer}

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(subCustomerList)">
            and m.SUB_CUSTOMER in
            <foreach collection="subCustomerList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(factoryList)">
            and m.FACTORY in
            <foreach collection="factoryList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testAreaList)">
            and m.TEST_AREA in
            <foreach collection="testAreaList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(deviceIdList)">
            and m.DEVICE_ID in
            <foreach collection="deviceIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(lotTypeList)">
            and m.LOT_TYPE in
            <foreach collection="lotTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testStageList)">
            and m.TEST_STAGE in
            <foreach collection="testStageList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(lotIdList)">
            and m.LOT_ID in
            <foreach collection="lotIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(waferNoList)">
            and m.WAFER_NO in
            <foreach collection="waferNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="findDynamicField" resultType="java.lang.String">
        SELECT
        ${filterField} as filterField
        from
        <choose>
            <when test="isCp == true">
                dws_lot_wafer_index_cluster m
            </when>
            <otherwise>
                dws_sblot_index_cluster m
            </otherwise>
        </choose>
        global left join dws_lot_wafer_data_check_index_cluster n
        on m.CUSTOMER = n.CUSTOMER
        and m.SUB_CUSTOMER = n.SUB_CUSTOMER
        and m.UPLOAD_TYPE = n.UPLOAD_TYPE
        and m.FACTORY = n.FACTORY
        and m.TEST_AREA = n.TEST_AREA
        and m.DEVICE_ID = n.DEVICE_ID
        and m.LOT_TYPE = n.LOT_TYPE
        and m.TEST_STAGE = n.TEST_STAGE
        and m.LOT_ID = n.LOT_ID
        <choose>
            <when test="isCp == true">
                and m.WAFER_NO = n.WAFER_NO
            </when>
            <otherwise>
                and m.SBLOT_ID = n.SBLOT_ID
            </otherwise>
        </choose>
        <where>
            UPLOAD_TYPE = 'AUTO'
            and IS_DELETE = 0
            <if test="!@org.springframework.util.ObjectUtils@isEmpty(sblotIdList)">
                and m.SBLOT_ID in
                <foreach collection="sblotIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <include refid="QueryByLotWaferList"/>
            <include refid="Common.ckCustomerSql"/>
            <if test="!@org.springframework.util.ObjectUtils@isEmpty(mesMatchTypeList)">
                and n.MES_MATCH_TYPE in
                <foreach collection="mesMatchTypeList" item="matchFlag" open="(" close=")" separator=",">
                    #{matchFlag}
                </foreach>
            </if>
            <if test="!@org.springframework.util.ObjectUtils@isEmpty(dataAccuracyCheckList)">
                and m.DATA_ACCURACY_CHECK in
                <foreach collection="dataAccuracyCheckList" item="accuracyCheck" separator="," open="(" close=")">
                    #{accuracyCheck}
                </foreach>
            </if>
        </where>
        and ${filterField} is not null
        group by ${filterField}
        order by ${filterField}
    </select>

    <select id="findDataAccuracyBatchMatchDTO"
            resultType="com.guwave.datahub.bpms.dao.clickhouse.dto.DataAccuracyBatchMatchDTO">
        select
            t1.SUB_CUSTOMER as subCustomer,
            t1.FACTORY as factory,
            t1.TEST_AREA as testArea,
            t1.DEVICE_ID as deviceId,
            t1.LOT_TYPE as lotType,
            t1.TEST_STAGE as testStage,
            t1.LOT_ID as lotId,
            <choose>
                <when test="isCp == true">
                    t1.WAFER_NO as waferNo,
                </when>
                <otherwise>
                    t1.SBLOT_ID as sblotId,
                </otherwise>
            </choose>
            t1.DATA_ACCURACY_CHECK as dataAccuracyCheck,
            t2.MES_MATCH_TYPE as mesMatchType
        from (
        select
            CUSTOMER,
            SUB_CUSTOMER,
            UPLOAD_TYPE,
            FACTORY,
            TEST_AREA,
            DEVICE_ID,
            LOT_TYPE,
            TEST_STAGE,
            LOT_ID,
            <choose>
                <when test="isCp == true">
                    WAFER_NO,
                </when>
                <otherwise>
                    SBLOT_ID,
                </otherwise>
            </choose>
            DATA_ACCURACY_CHECK
        from
        <choose>
            <when test="isCp == true">
                dws_lot_wafer_index_cluster m
            </when>
            <otherwise>
                dws_sblot_index_cluster m
            </otherwise>
        </choose>
        <where>
            UPLOAD_TYPE = 'AUTO'
            and IS_DELETE = 0
            <if test="!@org.springframework.util.ObjectUtils@isEmpty(sblotIdList)">
                and m.SBLOT_ID in
                <foreach collection="sblotIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <include refid="QueryByLotWaferList"/>
            <include refid="Common.ckCustomerSql"/>
            <if test="!@org.springframework.util.ObjectUtils@isEmpty(dataAccuracyCheckList)">
                and m.DATA_ACCURACY_CHECK in
                <foreach collection="dataAccuracyCheckList" item="accuracyCheck" separator="," open="(" close=")">
                    #{accuracyCheck}
                </foreach>
            </if>
        </where>
        ) t1
        global left join (
        select
            CUSTOMER,
            SUB_CUSTOMER,
            UPLOAD_TYPE,
            FACTORY,
            TEST_AREA,
            DEVICE_ID,
            LOT_TYPE,
            TEST_STAGE,
            LOT_ID,
            <choose>
                <when test="isCp == true">
                    WAFER_NO,
                </when>
                <otherwise>
                    SBLOT_ID,
                </otherwise>
            </choose>
            MES_MATCH_TYPE
        from dws.dws_lot_wafer_data_check_index_cluster m
        <where>
            UPLOAD_TYPE = 'AUTO'
            and IS_DELETE = 0
            <if test="!@org.springframework.util.ObjectUtils@isEmpty(sblotIdList)">
                and m.SBLOT_ID in
                <foreach collection="sblotIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <include refid="QueryByLotWaferList"/>
            <include refid="Common.ckCustomerSql"/>
            <if test="!@org.springframework.util.ObjectUtils@isEmpty(mesMatchTypeList)">
                and m.MES_MATCH_TYPE in
                <foreach collection="mesMatchTypeList" item="matchFlag" open="(" close=")" separator=",">
                    #{matchFlag}
                </foreach>
            </if>
        </where>
        ) t2
        on t1.CUSTOMER = t2.CUSTOMER
        and t1.SUB_CUSTOMER = t2.SUB_CUSTOMER
        and t1.UPLOAD_TYPE = t2.UPLOAD_TYPE
        and t1.FACTORY = t2.FACTORY
        and t1.TEST_AREA = t2.TEST_AREA
        and t1.DEVICE_ID = t2.DEVICE_ID
        and t1.LOT_TYPE = t2.LOT_TYPE
        and t1.TEST_STAGE = t2.TEST_STAGE
        and t1.LOT_ID = t2.LOT_ID
        <choose>
            <when test="isCp == true">
                and t1.WAFER_NO = t2.WAFER_NO
            </when>
            <otherwise>
                and t1.SBLOT_ID = t2.SBLOT_ID
            </otherwise>
        </choose>
    </select>


    <sql id="findDataAccuracyBatchYieldDTO">
        select
        t1.DEVICE_ID as deviceName,
        t1.TEST_STAGE as testStage,
        t1.TEST_AREA as testArea,
        <if test="isCp == true">
            t1.WAFER_NO as waferNo,
            t1.WAFER_ID as waferId,
        </if>
        t1.LOT_ID as lotId,
        t1.LOT_TYPE as lotType,
        t1.FIRST_YIELD as firstYield,
        t1.FINAL_YIELD as finalYield,
        t1.TOTAL_CNT as inputCount,
        t1.FINAL_PASS_CNT as passCount,
        formatDateTime(t1.START_TIME, '%F %T') as stdStartTime,
        t2.MES_MATCH_TYPE as matchFlag,
        t1.DATA_ACCURACY_CHECK as accuracyCheck,
        t3.stdFileCount as stdFileCount
        from
        (select
        LOT_TYPE,
        DEVICE_ID,
        TEST_AREA,
        TEST_STAGE,
        LOT_ID,
        <choose>
            <when test="isCp == true">
                WAFER_NO,
                WAFER_ID,
            </when>
            <otherwise>
                SBLOT_ID,
            </otherwise>
        </choose>
        LOT_TYPE,
        FIRST_YIELD,
        FINAL_YIELD,
        TOTAL_CNT,
        FINAL_PASS_CNT,
        START_TIME,
        DATA_ACCURACY_CHECK
        <choose>
            <when test="isCp == true">
                from dws_lot_wafer_index_cluster m
            </when>
            <otherwise>
                from dws_sblot_index_cluster m
            </otherwise>
        </choose>
        <where>
            UPLOAD_TYPE = 'AUTO'
            and IS_DELETE = 0
            <if test="!@org.springframework.util.ObjectUtils@isEmpty(sblotIdList)">
                and m.SBLOT_ID in
                <foreach collection="sblotIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <include refid="QueryByLotWaferList"/>
            <include refid="Common.ckCustomerSql"/>
            <if test="!@org.springframework.util.ObjectUtils@isEmpty(dataAccuracyCheckList)">
                and m.DATA_ACCURACY_CHECK in
                <foreach collection="dataAccuracyCheckList" item="accuracyCheck" separator="," open="(" close=")">
                    #{accuracyCheck}
                </foreach>
            </if>
        </where>
        order by START_TIME desc
        limit 1 by LOT_TYPE, DEVICE_ID,TEST_AREA,TEST_STAGE,LOT_ID <include refid="waferNoWhenCP"/>) t1
        global left join
        (select
        LOT_TYPE,
        DEVICE_ID,
        TEST_AREA,
        TEST_STAGE,
        LOT_ID,
        <choose>
            <when test="isCp == true">
                WAFER_NO,
            </when>
            <otherwise>
                SBLOT_ID,
            </otherwise>
        </choose>
        MES_MATCH_TYPE
        from dws_lot_wafer_data_check_index_cluster m
        <where>
            UPLOAD_TYPE = 'AUTO'
            and IS_DELETE = 0
            <if test="!@org.springframework.util.ObjectUtils@isEmpty(sblotIdList)">
                and m.SBLOT_ID in
                <foreach collection="sblotIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <include refid="QueryByLotWaferList"/>
            <include refid="Common.ckCustomerSql"/>
            <if test="!@org.springframework.util.ObjectUtils@isEmpty(mesMatchTypeList)">
                and m.MES_MATCH_TYPE in
                <foreach collection="mesMatchTypeList" item="matchFlag" open="(" close=")" separator=",">
                    #{matchFlag}
                </foreach>
            </if>
        </where>
        order by START_TIME desc
        limit 1 by LOT_TYPE, DEVICE_ID, TEST_AREA, TEST_STAGE,LOT_ID <include refid="waferNoWhenCP"/>) t2
        on t1.LOT_TYPE = t2.LOT_TYPE
        and t1.DEVICE_ID = t2.DEVICE_ID
        and t1.TEST_AREA = t2.TEST_AREA
        and t1.TEST_STAGE = t2.TEST_STAGE
        and t1.LOT_ID = t2.LOT_ID
        <choose>
            <when test="isCp == true">
                and t1.WAFER_NO = t2.WAFER_NO
            </when>
            <otherwise>
                and t1.SBLOT_ID = t2.SBLOT_ID
            </otherwise>
        </choose>
        global left join
        (select
        count(distinct FILE_NAME) stdFileCount, LOT_TYPE, DEVICE_ID, TEST_AREA,TEST_STAGE,LOT_ID <include refid="waferNoWhenCP"/>
        from dws_flowid_bin_index_cluster m
        <where>
            FINAL_FLAG = 1
            and UPLOAD_TYPE = 'AUTO'
            and IS_DELETE = 0
            <if test="!@org.springframework.util.ObjectUtils@isEmpty(sblotIdList)">
                and m.SBLOT_ID in
                <foreach collection="sblotIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <include refid="QueryByLotWaferList"/>
            <include refid="Common.ckCustomerSql"/>
        </where>
        group by
        LOT_TYPE, DEVICE_ID, TEST_AREA, TEST_STAGE,LOT_ID <include refid="waferNoWhenCP"/>) t3
        on t1.LOT_TYPE = t3.LOT_TYPE
        and t1.DEVICE_ID = t3.DEVICE_ID
        and t1.TEST_AREA = t3.TEST_AREA
        and t1.TEST_STAGE = t3.TEST_STAGE
        and t1.LOT_ID = t3.LOT_ID
        <if test="isCp == true">
            and t1.WAFER_NO = t3.WAFER_NO
        </if>
    </sql>


    <select id="findDataAccuracyBatchYieldDTOPage" resultType="com.guwave.datahub.bpms.dao.clickhouse.dto.DataAccuracyBatchYieldDTO">
        <include refid="findDataAccuracyBatchYieldDTO"/>
        order by
        <choose>
            <when test="!@org.springframework.util.ObjectUtils@isEmpty(sortField)">
                ${sortField}
                <if test="!@org.springframework.util.ObjectUtils@isEmpty(sortDirection)">
                    ${sortDirection}
                </if>
            </when>
            <otherwise>
                stdStartTime desc, finalYield desc
            </otherwise>
        </choose>
        limit #{offset}, #{pageSize}
    </select>

    <select id="countDataAccuracyBatchYieldDTO" resultType="java.lang.Long">
        select count() from
        (
            <include refid="findDataAccuracyBatchYieldDTO"/>
        )
    </select>

    <select id="findLotWaferDetail"
            resultType="com.guwave.datahub.bpms.dao.clickhouse.dto.AccuracyLotWaferDetailDTO">
        select
        m.DEVICE_ID as deviceName,
        m.TEST_AREA as testArea,
        m.TEST_STAGE as testStage,
        m.TEST_PROGRAM as testProgram,
        m.LOT_ID as lotId,
        <choose>
            <when test="isCp == true">
                m.WAFER_ID as waferId,
                m.WAFER_NO as waferNo,
            </when>
            <otherwise>
                m.SBLOT_ID as sbLotId,
            </otherwise>
        </choose>
        m.LOT_TYPE as lotType,
        m.DATA_ACCURACY_CHECK as accuracyCheck,
        m.FIRST_YIELD as firstYield,
        m.FINAL_YIELD as finalYield,
        m.TOTAL_CNT as inputCount,
        m.FINAL_PASS_CNT as passCount,
        n.PASS_CNT / n.TOTAL_CNT  as summaryYield,
        n.TOTAL_CNT as summaryCount,
        n.PASS_CNT as summaryPassCount,
        n.MES_MATCH_TYPE as matchFlag,
        n.LOSS_CNT as lossCount,
        n.DAMAGE_CNT as defectCount,
        formatDateTime(m.START_TIME, '%F %T') as stdStartTime
        <choose>
            <when test="isCp == true">
                from dws_lot_wafer_index_cluster m
            </when>
            <otherwise>
                from dws_sblot_index_cluster m
            </otherwise>
        </choose>
        global left join
        (
        select
        MES_MATCH_TYPE, TOTAL_CNT, PASS_CNT, LOSS_CNT, DAMAGE_CNT, DEVICE_ID, TEST_AREA, TEST_STAGE, LOT_ID, SBLOT_ID, WAFER_ID, LOT_TYPE
        from dws_lot_wafer_data_check_index_cluster m
        <where>
            <include refid="Common.ckCustomerSql"/>
            and IS_DELETE = 0
            and UPLOAD_TYPE = 'AUTO'
        </where>
        ) n
        on m.DEVICE_ID = n.DEVICE_ID
        and m.TEST_AREA = n.TEST_AREA
        and m.TEST_STAGE = n.TEST_STAGE
        and m.LOT_ID = n.LOT_ID
        and m.LOT_TYPE = n.LOT_TYPE
        <choose>
            <when test="isCp == true">
                and m.WAFER_ID = n.WAFER_ID
            </when>
            <otherwise>
                and m.SBLOT_ID = n.SBLOT_ID
            </otherwise>
        </choose>
        <where>
            m.UPLOAD_TYPE = 'AUTO'
            and m.IS_DELETE = 0
            <include refid="QueryByLotWafer"/>
            <include refid="Common.ckCustomerSql"/>
        </where>
        order by stdStartTime desc, finalYield desc
    </select>

    <sql id="QueryByLotWafer">
        and m.CUSTOMER = #{customer}

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(subCustomer)">
            and m.SUB_CUSTOMER = #{subCustomer}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(factory)">
            and m.FACTORY = #{factory}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testArea)">
            and m.TEST_AREA = #{testArea}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(deviceId)">
            and m.DEVICE_ID = #{deviceId}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(lotType)">
            and m.LOT_TYPE = #{lotType}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testStage)">
            and m.TEST_STAGE = #{testStage}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(lotId)">
            and m.LOT_ID = #{lotId}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(waferNo)">
            and m.WAFER_NO = #{waferNo}
        </if>
    </sql>


</mapper>