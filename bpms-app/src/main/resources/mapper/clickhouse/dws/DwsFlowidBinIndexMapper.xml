<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guwave.datahub.bpms.dao.repository.clickhouse.dws.mapper.DwsFlowidBinIndexMapper">

    <select id="selectOnlineRetestBins"
            resultType="com.guwave.datahub.bpms.dao.clickhouse.domain.dws.DwsFlowidBinIndex">
        select
        DEVICE_ID as deviceId,
        TEST_AREA as testArea,
        TEST_STAGE as testStage,
        TEST_PROGRAM as testProgram,
        LOT_ID as lotId,
        WAFER_ID as waferId,
        SBLOT_ID as sblotId,
        LOT_TYPE as lotType,
        FILE_NAME as fileName,
        FILE_ID as fileId,
        ONLINE_RETEST_DIES as onlineRetestDies
        FROM dws_flowid_bin_index_cluster
        WHERE CUSTOMER = '${@com.guwave.datahub.bpms.app.vo.web.RequestContext@getCustomer()}'
          and UPLOAD_TYPE = 'AUTO'
          and IS_DELETE = 0
          and IS_ONLINE_RETEST = 1
          and FILE_ID = #{fileId}
          and DEVICE_ID = #{deviceId}
          and TEST_STAGE = #{testStage}
          and LOT_ID = #{lotId}
          and SBLOT_ID = #{sblotId}
    </select>

    <select id="findLotWaferFileDetail"
            resultType="com.guwave.datahub.bpms.dao.clickhouse.dto.AccuracyLotWaferFileDetailDTO">
        select
        DEVICE_ID as deviceName,
        TEST_AREA as testArea,
        TEST_STAGE as testStage,
        TEST_PROGRAM as testProgram,
        LOT_ID as lotId,
        WAFER_ID as waferId,
        SBLOT_ID as sbLotId,
        FLOW_ID as flowId,
        if(IS_ONLINE_RETEST = 1, '片内复测', '正常') as fileFlag,
        IS_ONLINE_RETEST as onlineRetest,
        RETEST_BIN_NUM as retestHardBin,
        FILE_NAME as fileName,
        FILE_ID as fileId,
        formatDateTime(START_TIME, '%F %T') as stdStartTime,
        formatDateTime(END_TIME, '%F %T') as endTime,
        INPUT_CNT as inputCount,
        PASS_CNT as passCount,
        FAIL_CNT as failCount
        FROM dws_flowid_bin_index_cluster m
        <where>
            <include refid="QueryByLotWafer"/>
            <include refid="Common.ckCustomerSql"/>
            and m.UPLOAD_TYPE = 'AUTO'
            and m.IS_DELETE = 0
            and FINAL_FLAG = 1
        </where>
        order by DEVICE_ID,TEST_AREA,TEST_STAGE,TEST_PROGRAM,LOT_ID,WAFER_ID,SBLOT_ID,START_TIME
    </select>

    <sql id="QueryByLotWafer">
        and m.CUSTOMER = #{customer}

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(subCustomer)">
            and m.SUB_CUSTOMER = #{subCustomer}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(factory)">
            and m.FACTORY = #{factory}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testArea)">
            and m.TEST_AREA = #{testArea}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(deviceId)">
            and m.DEVICE_ID = #{deviceId}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(lotType)">
            and m.LOT_TYPE = #{lotType}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testStage)">
            and m.TEST_STAGE = #{testStage}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(lotId)">
            and m.LOT_ID = #{lotId}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(waferNo)">
            and m.WAFER_NO = #{waferNo}
        </if>
    </sql>




</mapper>