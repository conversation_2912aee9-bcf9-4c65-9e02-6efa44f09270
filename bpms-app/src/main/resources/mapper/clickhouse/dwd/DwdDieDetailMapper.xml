<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guwave.datahub.bpms.dao.repository.clickhouse.dwd.mapper.DwdDieDetailMapper">
    <!-- 自定义添加通过customer查询数据 SQL -->
    <select id="findByCustomer" resultType="com.guwave.datahub.bpms.dao.clickhouse.domain.dwd.ClickHouseExample">
        SELECT ID, CUSTOMER, ECID, TEST_VALUE as testValue, IS_DELETE as isDelete  FROM dwd_test_item_detail_cluster WHERE CUSTOMER = #{customer} LIMIT 5
    </select>
    <select id="selectGoodRetestBins"
            resultType="com.guwave.datahub.bpms.dao.clickhouse.dto.GoodRetestBinDTO">
        select
        ECID ecid,
        HBIN_PF hbinPf,
        HBIN_NUM hbinNum,
        SBIN_NUM sbinNum,
        DEVICE_ID deviceId,
        TEST_STAGE testStage,
        TEST_PROGRAM testProgram,
        LOT_ID lotId,
        SBLOT_ID sblotId,
        WAFER_ID waferId,
        LOT_TYPE lotType,
        '好品复测' accuracyCheck,
        HBIN_NUM hbinNum,
        SBIN_NUM sbinNum,
        HBIN_NAM hbinName,
        SBIN_NAM sbinName,
        FILE_NAME fileName,
        row_number() OVER (partition by ECID ORDER BY START_TIME, C_PART_ID) AS retestNo
        FROM dwd_die_detail_cluster
        <where>
            ECID global in (
            select
            ecid
            from (
            SELECT
            ECID ecid,
            HBIN_PF hbinPf,
            groupArray(HBIN_PF) over (partition by ECID order by (OFFLINE_RETEST, INTERRUPT, ONLINE_RETEST) ROWS BETWEEN 1 FOLLOWING AND unbounded FOLLOWING) as nextPF
            FROM dwd_die_detail_cluster
            <where>
                <include refid="GoodRetestBinsCond" />
                <include refid="ValidPointCond" />
            </where>
            )
            <where>
                hbinPf = 'P'
                and LENGTH(nextPF) > 0
            </where>
            )
            <include refid="GoodRetestBinsCond" />
        </where>
        order by ECID, START_TIME, C_PART_ID
    </select>

    <select id="selectOnlineRetestDies"
            resultType="com.guwave.datahub.bpms.dao.clickhouse.dto.OnlineRetestBinDTO">
        select
        DEVICE_ID deviceId,
        TEST_STAGE testStage,
        TEST_PROGRAM testProgram,
        LOT_ID lotId,
        SBLOT_ID sblotId,
        LOT_TYPE lotType,
        FILE_NAME fileName,
        ECID ecid,
        HBIN_NUM hbinNum,
        SBIN_NUM sbinNum,
        HBIN_NAM hbinName,
        SBIN_NAM sbinName,
        HBIN_PF hbinPf,
        row_number() OVER (partition by ECID ORDER BY START_TIME, C_PART_ID) AS testOrder
        from dwd_die_detail_cluster
        <where>
            and CUSTOMER = '${@com.guwave.datahub.bpms.app.vo.web.RequestContext@getCustomer()}'
            and UPLOAD_TYPE = 'AUTO'
            and IS_DELETE = 0
            and DEVICE_ID = #{deviceId}
            and TEST_STAGE = #{testStage}
            and LOT_ID = #{lotId}
            and SBLOT_ID = #{sblotId}
            and IS_STANDARD_ECID = 1
            and ECID in
            <foreach collection="ecidList" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </where>
        order by ECID, START_TIME, C_PART_ID
    </select>

    <select id="findSlimDieDetailByLotWafer" resultType="com.guwave.datahub.bpms.dao.clickhouse.domain.dwd.DwdDieDetail">
        select
        TEST_STAGE as testStage,
        LOT_TYPE as lotType,
        FLOW_ID as flowId,
        FILE_ID as fileId,
        FILE_NAME as fileName,
        DEVICE_ID as deviceId,
        LOT_ID as lotId,
        WAFER_ID as waferId,
        WAFER_NO as waferNo,
        SBLOT_ID as sblotId,
        ONLINE_RETEST as onlineRetest,
        TEST_PROGRAM as testProgram,
        TEST_PROGRAM_VERSION as testProgramVersion,
        HBIN_NUM as hbinNum,
        HBIN_PF as hbinPf,
        ECID as ecid,
        RETEST_BIN_NUM as retestBinNum
        from dwd_die_detail_cluster m
        <where>
            UPLOAD_TYPE = 'AUTO'
            and IS_DELETE = 0
            <include refid="QueryByLotWafer"/>
            <include refid="Common.ckCustomerSql"/>
        </where>
    </select>


    <sql id="GoodRetestBinsCond">
        and CUSTOMER = '${@com.guwave.datahub.bpms.app.vo.web.RequestContext@getCustomer()}'
        and UPLOAD_TYPE = 'AUTO'
        and DEVICE_ID = #{deviceId}
        and TEST_AREA = #{testArea}
        and TEST_STAGE = #{testStage}
        and LOT_ID = #{lotId}
        and IS_DELETE = 0
        and IS_STANDARD_ECID = 1
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(waferNo)">
            and WAFER_NO = #{waferNo}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(sblotId)">
            and SBLOT_ID = #{sblotId}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testProgram)">
            and TEST_PROGRAM = #{testProgram}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(lotType)">
            and LOT_TYPE = #{lotType}
        </if>
    </sql>

    <sql id="ValidPointCond">
        <if test="testArea == 'CP'">
            <!-- 剔除非法坐标 -->
            and X_COORD between -1000 and 1000
            and Y_COORD between -1000 and 1000
        </if>
    </sql>

    <sql id="QueryByLotWafer">
        and m.CUSTOMER = #{customer}

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(subCustomer)">
            and m.SUB_CUSTOMER = #{subCustomer}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(factory)">
            and m.FACTORY = #{factory}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testArea)">
            and m.TEST_AREA = #{testArea}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(deviceId)">
            and m.DEVICE_ID = #{deviceId}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(lotType)">
            and m.LOT_TYPE = #{lotType}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testStage)">
            and m.TEST_STAGE = #{testStage}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(lotId)">
            and m.LOT_ID = #{lotId}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(waferNo)">
            and m.WAFER_NO = #{waferNo}
        </if>
    </sql>

</mapper>