<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guwave.datahub.bpms.dao.repository.clickhouse.dwd.mapper.ClickHouseExampleMapper">
    <!-- 自定义添加通过customer查询数据 SQL -->
    <select id="findByCustomer" resultType="com.guwave.datahub.bpms.dao.clickhouse.domain.dwd.ClickHouseExample">
        SELECT ID, CUSTOMER, ECID, TEST_VALUE as testValue, IS_DELETE as isDelete  FROM dwd_test_item_detail_cluster WHERE CUSTOMER = #{customer} LIMIT 5
    </select>

</mapper>