<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guwave.datahub.bpms.dao.repository.logs.OdsAppLogMapper">

    <select id="selectLogsByKeywords" resultType="java.lang.String">
        SELECT RPAD(concat('[',HOST,' ',INSTANCE,']'), #{host_instance_max_length}, ' ') || ' ' || concat(toString(TIME), ' [', THREAD, '] ', LEVEL, ' ', CLAZZ, '[', toString(LINE), ']: ', MESSAGE) AS log
        FROM ods_app_log_cluster
        WHERE APP = #{app}
        <if test="host != null and host != ''">
            AND HOST = #{host}
        </if>
        <if test="instance != null and instance != ''">
            AND INSTANCE = #{instance}
        </if>
        <if test="logLevel != null and logLevel.size() > 0">
            AND LEVEL in
            <foreach collection="logLevel" item="level" open="(" separator="," close=")">
                #{level}
            </foreach>
        </if>
        AND TIME >= toDateTime(#{startTime}) AND TIME &lt;= toDateTime(#{endTime})
        <if test="keywords != null and keywords.size() > 0">
            AND (
            <foreach collection="keywords" item="keyword" separator=" AND ">
                MESSAGE LIKE concat('%', #{keyword}, '%')
            </foreach>
            )
        </if>
        ORDER BY NANO_TIME
        LIMIT #{limit}
    </select>

    <select id="selectLogsByRegexp" resultType="java.lang.String">
        SELECT RPAD(concat('[',HOST,' ',INSTANCE,']'), #{host_instance_max_length}, ' ') || ' ' || concat(toString(TIME), ' [', THREAD, '] ', LEVEL, ' ', CLAZZ, '[', toString(LINE), ']: ', MESSAGE) AS log
        FROM ods_app_log_cluster
        WHERE APP = #{app}
        <if test="host != null and host != ''">
            AND HOST = #{host}
        </if>
        <if test="instance != null and instance != ''">
            AND INSTANCE = #{instance}
        </if>
        <if test="logLevel != null and logLevel.size() > 0">
            AND LEVEL in
            <foreach collection="logLevel" item="level" open="(" separator="," close=")">
                #{level}
            </foreach>
        </if>
        AND TIME >= toDateTime(#{startTime}) AND TIME &lt;= toDateTime(#{endTime})
        <if test="regexp != null and regexp != ''">
            AND match(MESSAGE, #{regexp})
        </if>
        ORDER BY NANO_TIME
        LIMIT #{limit}
    </select>

    <select id="countLogsByKeywords" resultType="map">
        SELECT count(*) AS count,max(length(concat('[',HOST,' ',INSTANCE,']'))) as host_instance_max_length
        FROM ods_app_log_cluster
        WHERE APP = #{app}
        <if test="host != null and host != ''">
            AND HOST = #{host}
        </if>
        <if test="instance != null and instance != ''">
            AND INSTANCE = #{instance}
        </if>
        <if test="logLevel != null and logLevel.size() > 0">
            AND LEVEL in
            <foreach collection="logLevel" item="level" open="(" separator="," close=")">
                #{level}
            </foreach>
        </if>
        AND TIME >= toDateTime(#{startTime}) AND TIME &lt;= toDateTime(#{endTime})
        <if test="keywords != null and keywords.size() > 0">
            AND (
            <foreach collection="keywords" item="keyword" separator=" AND ">
                MESSAGE LIKE concat('%', #{keyword}, '%')
            </foreach>
            )
        </if>
    </select>

    <select id="countLogsByRegexp" resultType="map">
        SELECT count(*) AS count,max(length(concat('[',HOST,' ',INSTANCE,']'))) as host_instance_max_length
        FROM ods_app_log_cluster
        WHERE APP = #{app}
        <if test="host != null and host != ''">
            AND HOST = #{host}
        </if>
        <if test="instance != null and instance != ''">
            AND INSTANCE = #{instance}
        </if>
        <if test="logLevel != null and logLevel.size() > 0">
            AND LEVEL in
            <foreach collection="logLevel" item="level" open="(" separator="," close=")">
                #{level}
            </foreach>
        </if>
        AND TIME >= toDateTime(#{startTime}) AND TIME &lt;= toDateTime(#{endTime})
        <if test="regexp != null and regexp != ''">
            AND match(MESSAGE, #{regexp})
        </if>
    </select>

    <select id="selectApps" resultType="string">
        SELECT DISTINCT APP FROM dim_app_host_cluster ORDER BY APP
    </select>

    <select id="selectHosts" resultType="string">
        SELECT DISTINCT HOST FROM dim_app_host_cluster WHERE APP = #{app} ORDER BY HOST
    </select>

    <select id="selectInstances" resultType="string">
        SELECT DISTINCT INSTANCE FROM dim_app_host_cluster WHERE APP = #{app} AND HOST = #{host} ORDER BY INSTANCE
    </select>

</mapper>