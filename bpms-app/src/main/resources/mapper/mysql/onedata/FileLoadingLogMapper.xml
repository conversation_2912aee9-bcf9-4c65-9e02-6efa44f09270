<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guwave.datahub.bpms.dao.repository.dw.mapper.FileLoadingLogMapper">
    <select id="querySuccessFileLogByCond"
            resultType="com.guwave.datahub.bpms.dao.clickhouse.dto.DataTimelinessDTO">
        select
        file_name fileName,
        factory,
        tester_name testerName,
        step,
        process_status processStatus,
        remote_file_mtime remoteFileMtime,
        step_start_time stepStartTime,
        step_end_time stepEndTime,
        finish_t finishT,
        origin_file_size originFileSize,
        hdfs_path hdfsPath,
        update_time updateTime
        from dw_file_loading_log n
        <where>
            and file_name in
            (
            select file_name
            from dw_file_loading_log m
            <where>
                process_status = 'SUCCESS'
                <include refid="QueryCond"/>
                <include refid="Common.mysqlCustomerSql"/>
            </where>
            )
        </where>

    </select>


    <sql id="QueryCond">
        and m.convert_flag = 0
        and m.step = 7100
        and m.file_category in ('STDF', 'RAW_DATA')
        and m.test_area = #{testArea}
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(allSupportTestArea)">
            and m.test_area in
            <foreach collection="allSupportTestArea" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(factoryList)">
            and m.factory in
            <foreach collection="factoryList" item="factory" open="(" close=")" separator=",">
                #{factory}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(deviceIdList)">
            and m.device_id in
            <foreach collection="deviceIdList" item="device" open="(" close=")" separator=",">
                #{device}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testStageList)">
            and m.test_stage in
            <foreach collection="testStageList" item="testStage" open="(" close=")" separator=",">
                #{testStage}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(waferNoList)">
            and m.wafer_no in
            <foreach collection="waferNoList" item="waferNo" open="(" close=")" separator=",">
                #{waferNo}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(lotIdList)">
            and m.lot_id in
            <foreach collection="lotIdList" item="lotId" open="(" close=")" separator=",">
                #{lotId}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(sblotIdList)">
            and m.sblot_Id in
            <foreach collection="sblotIdList" item="sblotId" open="(" close=")" separator=",">
                #{sblotId}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testProgramList)">
            and m.test_program in
            <foreach collection="testProgramList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(startTime)">
            and m.step_end_time &gt;= #{startTime}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(endTime)">
            and m.step_end_time &lt;= #{endTime}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(subCustomerList)">
            and m.sub_customer in
            <foreach collection="subCustomerList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="findCpDynamicField"
            resultType="String">
        select case
        when upper(#{filterField}) = upper('SUB_CUSTOMER') then sub_customer
        when upper(#{filterField}) = upper('FACTORY') then factory
        when upper(#{filterField}) = upper('DEVICE_ID') then device_id
        when upper(#{filterField}) = upper('TEST_PROGRAM') then test_program
        when upper(#{filterField}) = upper('TEST_STAGE') then test_stage
        when upper(#{filterField}) = upper('LOT_ID') then lot_id
        when upper(#{filterField}) = upper('WAFER_NO') then wafer_no
        end as result
        from (
        select distinct customer
        , sub_customer
        , factory
        , test_area
        , device_id
        , test_program
        , test_stage
        , lot_id
        , wafer_no
        from dw_file_loading_log m
        <where>
            process_status = 'SUCCESS'
            <include refid="FilterQueryCond"/>
            <include refid="QueryByWaferNoList"/>
            <include refid="QueryByUpdateTime"/>
            <include refid="Common.mysqlCustomerSql"/>
        </where>
        ) t
    </select>

    <select id="findFtDynamicField"
            resultType="String">
        select case
        when upper(#{filterField}) = upper('SUB_CUSTOMER') then sub_customer
        when upper(#{filterField}) = upper('FACTORY') then factory
        when upper(#{filterField}) = upper('DEVICE_ID') then device_id
        when upper(#{filterField}) = upper('TEST_PROGRAM') then test_program
        when upper(#{filterField}) = upper('TEST_STAGE') then test_stage
        when upper(#{filterField}) = upper('LOT_ID') then lot_id
        when upper(#{filterField}) = upper('SBLOT_ID') then sblot_id
        end as result
        from (
        select distinct customer
        , sub_customer
        , factory
        , test_area
        , device_id
        , test_program
        , test_stage
        , lot_id
        , sblot_id
        from dw_file_loading_log m
        <where>
            process_status = 'SUCCESS'
            <include refid="FilterQueryCond"/>
            <include refid="QueryBySblotIdList"/>
            <include refid="QueryByUpdateTime"/>
            <include refid="Common.mysqlCustomerSql"/>
        </where>
        ) t
    </select>

    <sql id="FilterQueryCond">
        and m.convert_flag = 0
        and m.step = 7100
        and m.file_category in ('STDF', 'RAW_DATA')
        and m.test_area = #{testArea}
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(factoryList)">
            and m.factory in
            <foreach collection="factoryList" item="factory" open="(" close=")" separator=",">
                #{factory}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(deviceIdList)">
            and m.device_id in
            <foreach collection="deviceIdList" item="device" open="(" close=")" separator=",">
                #{device}
            </foreach>
        </if>

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testStageList)">
            and m.test_stage in
            <foreach collection="testStageList" item="testStage" open="(" close=")" separator=",">
                #{testStage}
            </foreach>
        </if>

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(lotIdList)">
            and m.lot_id in
            <foreach collection="lotIdList" item="lotId" open="(" close=")" separator=",">
                #{lotId}
            </foreach>
        </if>

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testProgramList)">
            and m.test_program in
            <foreach collection="testProgramList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(subCustomerList)">
            and m.sub_customer in
            <foreach collection="subCustomerList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="QueryByUpdateTime">
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(startTime)">
            and m.update_time &gt;= #{startTime}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(endTime)">
            and m.update_time &lt;= #{endTime}
        </if>
    </sql>

    <sql id="QueryByWaferNoList">
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(waferNoList)">
            and m.wafer_no in
            <foreach collection="waferNoList" item="waferNo" open="(" close=")" separator=",">
                #{waferNo}
            </foreach>
        </if>
    </sql>

    <sql id="QueryBySblotIdList">
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(sblotIdList)">
            and m.sblot_Id in
            <foreach collection="sblotIdList" item="sblotId" open="(" close=")" separator=",">
                #{sblotId}
            </foreach>
        </if>
    </sql>

    <sql id="OverviewFilterQueryCond">
        <include refid="Common.mysqlCustomerSql"/>
        and m.test_area = #{testArea}
        and m.step = 7100
        and m.process_status = 'SUCCESS'
        and m.file_category in ('STDF', 'RAW_DATA')
        and m.convert_flag = 0
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(subCustomerList)">
            and m.sub_customer in
            <foreach collection="subCustomerList" item="subCustomerList" open="(" close=")" separator=",">
                #{subCustomerList}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(factoryList)">
            and m.factory in
            <foreach collection="factoryList" item="factoryList" open="(" close=")" separator=",">
                #{factoryList}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(deviceIdList)">
            and m.device_id in
            <foreach collection="deviceIdList" item="deviceIdList" open="(" close=")" separator=",">
                #{deviceIdList}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testProgramList)">
            and m.test_program in
            <foreach collection="testProgramList" item="testProgramList" open="(" close=")" separator=",">
                #{testProgramList}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testStageList)">
            and m.test_stage in
            <foreach collection="testStageList" item="testStageList" open="(" close=")" separator=",">
                #{testStageList}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(startTime)">
            and m.step_start_time &gt;= #{startTime}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(endTime)">
            and m.step_end_time &lt;= #{endTime}
        </if>
    </sql>

    <select id="findOverviewFilter" resultType="String">
        SELECT DISTINCT CASE WHEN UPPER(#{filterField}) = UPPER('SUB_CUSTOMER')  THEN sub_customer
                        WHEN UPPER(#{filterField}) = UPPER('FACTORY')       THEN factory
                        WHEN UPPER(#{filterField}) = UPPER('DEVICE_ID')     THEN device_id
                        WHEN UPPER(#{filterField}) = UPPER('TEST_PROGRAM')  THEN test_program
                        WHEN UPPER(#{filterField}) = UPPER('TEST_STAGE')    THEN test_stage END AS result
        FROM
        ( SELECT  DISTINCT sub_customer
                          ,factory
                          ,test_area
                          ,device_id
                          ,test_program
                          ,test_stage
        FROM dw_file_loading_log m
        WHERE 1 = 1
        <include refid="OverviewFilterQueryCond"/>
        <include refid="Common.mysqlCustomerSql"/>
        ) t
    </select>

    <select id="querySuccessFileOverviewByCond" resultType="com.guwave.datahub.bpms.dao.domain.qualityOverview.FileLoadInfo">
        SELECT  DATE_FORMAT(step_end_time,'%X_%v') AS stepEndTimeWeek
               ,test_area                          AS testArea
               ,factory                            AS factory
               ,file_name                          AS fileName
               ,remote_file_mtime                  AS remoteFileMtime
               ,step                               AS step
               ,step_start_time                    AS stepStartTime
               ,step_end_time                      AS stepEndTime
               ,device_id                          AS deviceId
               ,lot_id                             AS lotId
               ,sblot_id                           AS sbLotId
               ,wafer_id                           AS waferId
               ,test_stage                         AS testStage
               ,test_program                       AS testProgram
               ,finish_t                           AS finishT
               ,process_status                     AS processStatus
        FROM dw_file_loading_log
        WHERE file_name IN
            ( SELECT  file_name
              FROM dw_file_loading_log m
              WHERE 1 = 1
              <include refid="OverviewFilterQueryCond"/>
              <include refid="Common.mysqlCustomerSql"/>
            )
        AND step IN (1000,7100)
    </select>

</mapper>
