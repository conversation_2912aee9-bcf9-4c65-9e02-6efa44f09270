<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guwave.datahub.bpms.dao.repository.dw.mapper.BinDefinitionMapper">

    <!-- 通用权限条件 -->
    <sql id="permissionCondition">
        AND customer = #{customer}
        AND sub_customer = #{subCustomer}
    </sql>

    <sql id="queryTestPrograms">
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testPrograms)">
            and test_program in
            <foreach collection="testPrograms" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and test_program is not null
    </sql>

    <!-- 通用排序条件 -->
    <sql id="orderByCondition">
        ORDER BY
        <choose>
            <when test="orderByField == 'testProgram'">
                testProgram
            </when>
            <when test="orderByField == 'updateTime'">
                updateTime
            </when>
            <when test="orderByField == 'createTime'">
                createTime
            </when>
            <otherwise>
                testProgram
            </otherwise>
        </choose>
        <choose>
            <when test="orderByType != null and orderByType.toLowerCase() == 'desc'">
                DESC
            </when>
            <otherwise>
                ASC
            </otherwise>
        </choose>
    </sql>


    <!-- 搜索测试程序 -->
    <select id="searchTestPrograms" resultType="java.lang.String">
        select * from (
        SELECT DISTINCT test_program
        FROM dw_test_program_bin_definition
        <where>
            1=1
            <include refid="permissionCondition"/>
        </where>
        union
        SELECT DISTINCT test_program
        FROM dw_lot_meta_data_detail
        <where>
            1=1
            <include refid="permissionCondition"/>
        </where>
        ) a where test_program is not null order by test_program
    </select>

    <!-- 查询已配置的测试程序统计信息 -->
    <select id="findConfiguredTestPrograms" resultType="com.guwave.datahub.bpms.dao.vo.bpms.binDefinition.ConfiguredTestProgramVo">
        SELECT 
            test_program as testProgram,
            date_format(MAX(update_time), '%Y-%m-%d %H:%i:%s') as updateTime,
            SUM(CASE WHEN bin_type = 'HBIN' THEN 1 ELSE 0 END) as hbinCount,
            SUM(CASE WHEN bin_type = 'SBIN' THEN 1 ELSE 0 END) as sbinCount
        FROM dw_test_program_bin_definition
        <where>
            1=1
            <include refid="permissionCondition"/>
            <include refid="queryTestPrograms"/>
        </where>
        GROUP BY test_program
        <include refid="orderByCondition"/>
        LIMIT #{offset}, #{size}
    </select>

    <!-- 统计已配置的测试程序总数 -->
    <select id="countConfiguredTestPrograms" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT test_program)
        FROM dw_test_program_bin_definition
        <where>
            1=1
            <include refid="permissionCondition"/>
            <include refid="queryTestPrograms"/>
        </where>
    </select>

    <!-- 查询未配置的测试程序 -->
    <select id="findUnconfiguredTestPrograms" resultType="com.guwave.datahub.bpms.dao.vo.bpms.binDefinition.UnconfiguredTestProgramVo">
        select a.test_program as testProgram, date_format(min_create_time, '%Y-%m-%d %H:%i:%s') as createTime
        from (select test_program, min(create_time) min_create_time
        from dw_lot_meta_data_detail
        <where>
            1=1
            <include refid="permissionCondition"/>
            <include refid="queryTestPrograms"/>
        </where>
        group by test_program
        ) a
        left join (select distinct test_program
        from dw_test_program_bin_definition
        <where>
            1=1
            <include refid="permissionCondition"/>
            <include refid="queryTestPrograms"/>
        </where>
        ) b on a.test_program = b.test_program
        where b.test_program is null
        <include refid="orderByCondition"/>
        LIMIT #{offset}, #{size}
    </select>

    <!-- 统计未配置的测试程序总数 -->
    <select id="countUnconfiguredTestPrograms" resultType="java.lang.Long">
        select count(1)
        from (select test_program
        from dw_lot_meta_data_detail
        <where>
            1=1
            <include refid="permissionCondition"/>
            <include refid="queryTestPrograms"/>
        </where>
        group by test_program
        ) a
        left join (select distinct test_program
        from dw_test_program_bin_definition
        <where>
            1=1
            <include refid="permissionCondition"/>
            <include refid="queryTestPrograms"/>
        </where>
        ) b on a.test_program = b.test_program
        where b.test_program is null
    </select>

    <!-- 根据测试程序和权限查询Bin定义 -->
    <select id="findByTestProgramAndPermissions" resultType="com.guwave.datahub.bpms.dao.vo.bpms.binDefinition.BinDefinitionDetail">
        select a.* from (SELECT
            test_program as testProgram,
            bin_type as binType,
            bin_num as binNum,
            bin_name as binName,
            bin_pf as binPf
        FROM dw_test_program_bin_definition
        <where>
            <include refid="queryTestPrograms"/>
            <include refid="permissionCondition"/>
        </where>) a join (SELECT
            test_program ,
            max(update_time) as updateTime
        FROM dw_test_program_bin_definition
        <where>
            <include refid="queryTestPrograms"/>
            <include refid="permissionCondition"/>
        </where>
        group by test_program
        ) b on a.testProgram = b.test_program
        <include refid="orderByCondition"/>
        , binType, binNum
    </select>

    <!-- 检查Bin编号唯一性 -->
    <select id="countByTestProgramAndBinTypeAndBinNumAndIdNot" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM dw_test_program_bin_definition
        WHERE test_program = #{testProgram}
        <include refid="queryTestPrograms"/>
        <include refid="permissionCondition"/>
        AND bin_type = #{binType}
        AND bin_num = #{binNum}
        <if test="id != null">
            AND id != #{id}
        </if>
    </select>


    <select id="findExportUnconfigured" resultType="com.guwave.datahub.bpms.dao.vo.bpms.binDefinition.BinDefinitionDetail">
        SELECT
         test_program as testProgram,min(create_time) as createTime
        FROM dw_lot_meta_data_detail
        <where>
            <include refid="queryTestPrograms"/>
            <include refid="permissionCondition"/>
        </where>
        group by test_program
        <include refid="orderByCondition"/>
    </select>

    <!-- 插入Bin定义 -->
    <insert id="insert" parameterType="com.guwave.onedata.dataware.dao.mysql.domain.dw.TestProgramBinDefinition">
        INSERT INTO dw_test_program_bin_definition (
            customer,
            sub_customer,
            test_program,
            bin_type,
            bin_num,
            bin_name,
            bin_pf,
            create_time,
            update_time,
            create_user,
            update_user
        ) VALUES (
            #{customer},
            #{subCustomer},
            #{testProgram},
            #{binType},
            #{binNum},
            #{binName},
            #{binPf},
            NOW(),
            NOW(),
            #{createUser},
            #{updateUser}
        )
    </insert>

    <!-- 批量插入Bin定义 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO dw_test_program_bin_definition (
            customer,
            sub_customer,
            test_program,
            bin_type,
            bin_num,
            bin_name,
            bin_pf,
            create_time,
            update_time,
            create_user,
            update_user
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.customer},
                #{item.subCustomer},
                #{item.testProgram},
                #{item.binType},
                #{item.binNum},
                #{item.binName},
                #{item.binPf},
                NOW(),
                NOW(),
                #{item.createUser},
                #{item.updateUser}
            )
        </foreach>
    </insert>

    <!-- 根据测试程序删除Bin定义 -->
    <delete id="deleteByTestProgramAndPermissions">
        DELETE FROM dw_test_program_bin_definition
        <where>
            test_program = #{testProgram}
            <include refid="permissionCondition"/>
        </where>
    </delete>

    <!-- 检查测试程序是否已存在 -->
    <select id="countByTestProgramAndPermissions" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM dw_test_program_bin_definition
        <where>
            test_program = #{testProgram}
            <include refid="permissionCondition"/>
        </where>
    </select>

    <!-- 删除所有指定客户的Bin定义 -->
    <delete id="deleteAllByPermissions">
        DELETE FROM dw_test_program_bin_definition
        <where>
            1=1
            <include refid="permissionCondition"/>
        </where>
    </delete>

</mapper>