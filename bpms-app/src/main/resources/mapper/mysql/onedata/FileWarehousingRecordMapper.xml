<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guwave.datahub.bpms.dao.repository.dw.mapper.FileWarehousingRecordMapper">
    <sql id="QueryByLotWafer">
        and m.customer = #{customer}

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(subCustomerList)">
            and m.sub_customer in
            <foreach collection="subCustomerList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(factoryList)">
            and m.factory in
            <foreach collection="factoryList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testAreaList)">
            and m.test_area in
            <foreach collection="testAreaList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(deviceIdList)">
            and m.device_id in
            <foreach collection="deviceIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(lotTypeList)">
            and m.lot_type in
            <foreach collection="lotTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testStageList)">
            and m.test_stage in
            <foreach collection="testStageList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(lotIdList)">
            and m.lot_id in
            <foreach collection="lotIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="!@org.springframework.util.ObjectUtils@isEmpty(waferNoList)">
            and m.wafer_no in
            <foreach collection="waferNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="QueryByUpdateTime">
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(startTime)">
            and m.update_time &gt;= #{startTime}
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(endTime)">
            and m.update_time &lt;= #{endTime}
        </if>
    </sql>

    <select id="find7100SuccessFileWarehousingByFilters"
            resultType="com.guwave.datahub.bpms.dao.vo.dw.WaferVo">
        select
        distinct customer,
        sub_customer as subCustomer,
        factory  as factory,
        test_area as testArea,
        device_id as deviceId,
        test_stage as testStage,
        lot_id as lotId,
        wafer_no as waferNo,
        sblot_id as sblotId,
        lot_type as lotType
        from dw_file_warehousing_record m
        <where>
            process_status = 'SUCCESS'
            and step = '7100'
            and convert_flag = 0
            <if test="!@org.springframework.util.ObjectUtils@isEmpty(allSupportFileCategory)">
                and m.file_category in
                <foreach collection="allSupportFileCategory" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="!@org.springframework.util.ObjectUtils@isEmpty(sblotIdList)">
                and m.sblot_Id in
                <foreach collection="sblotIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <include refid="QueryByLotWafer"/>
            <include refid="QueryByUpdateTime"/>
            <include refid="Common.mysqlCustomerSql"/>
        </where>

    </select>

    <sql id="dataIntegrityDataLoaded">
        (case
            when step = 7100 and process_status = 'SUCCESS' then 'Y'
            when process_status = 'FAIL' then 'N'
            else 'Processing' end)
    </sql>

    <sql id="dataIntegrityFailedType">
        case
        when ${failedTypeItem} in ('KEY_FIELD_NULL_EXCEPTION', 'RAW_DATA_KEY_FIELD_NULL_EXCEPTION')
        then
            <choose>
                <when test="${completeExcFlag} == true">
                    concat('字段为空: ', failed_fields)
                </when>
                <otherwise>
                    '字段为空'
                </otherwise>
            </choose>
        when ${failedTypeItem} = 'KEY_FIELD_JS_SCRIPT_EXECUTE_FAIL_EXCEPTION'
        then
            <choose>
                <when test="${completeExcFlag} == true">
                    concat('配置异常: ', failed_fields)
                </when>
                <otherwise>
                    '配置异常'
                </otherwise>
            </choose>
        when ${failedTypeItem} = 'TOUCH_DOWN_NOT_FOUND_PRR_EXCEPTION'
        then 'STDF缺失PRR'
        when ${failedTypeItem} = 'STDF_NOT_FOUND_TEST_DATA_EXCEPTION'
        then 'STDF缺失PTR/FTR/MPR'
        when ${failedTypeItem} = 'DUPLICATE_FILE_PARSED_EXCEPTION'
        then '数据重复'
        when ${failedTypeItem} = 'DWD_PROCESS_FAIL_EXCEPTION'
        then '明细表计算失败'
        when ${failedTypeItem} = 'DWS_PROCESS_FAIL_EXCEPTION'
        then '聚合表计算失败'
        when ${failedTypeItem} = 'PULL_FILE_FAIL'
        then '拉取失败'
        when ${failedTypeItem} = 'DECOMPRESSION_FILE_FAIL'
        then '拉取成功，解压失败'
        when ${failedTypeItem} = 'BACKUP_FAIL'
        then '拉取成功，备份失败'
        when ${failedTypeItem} = 'STDF_READ_FAIL_EXCEPTION'
        then 'STDF读取失败'
        when ${failedTypeItem} = 'HEADER_NOT_MATCH_DATA_ROW_FIELD_EXCEPTION'
        then '列名异常'
        when ${failedTypeItem} = 'NOT_A_NUMBER_EXCEPTION'
        then '存在异常数值'
        when ${failedTypeItem} = 'PARSE_FILE_EXCEPTION'
        then '文件解析异常'
        else 'Other' end
    </sql>

    <sql id="dataIntegrityStepOrder">
        case
            when step = 7100 and process_status = 'SUCCESS' then 100
            when step >= 4100 and (process_status != 'FAIL' or (process_status = 'FAIL' and failed_type not in ('KEY_FIELD_NULL_EXCEPTION'))) then 4
            when step >= 3100 and process_status = 'FAIL' and failed_type in ('KEY_FIELD_NULL_EXCEPTION') then 3
            when step = 1000 and process_status = 'FAIL' then 1
            else 2 end
    </sql>

    <sql id="dataIntegrityCondition">
        convert_flag = 0
        <include refid="Common.mysqlCustomerSql"/>
        and customer = #{customer}
        and test_area in
        <foreach collection="testAreas" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and file_category in
        <foreach collection="fileCategorys" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(remoteFileMtimeStart)">
            and remote_file_mtime between CAST(#{remoteFileMtimeStart} AS DATETIME) and CAST(#{remoteFileMtimeEnd} AS DATETIME)
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(subCustomers)">
            and sub_customer in
            <foreach collection="subCustomers" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(factorys)">
            and factory in
            <foreach collection="factorys" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(deviceIds)">
            and device_id in
            <foreach collection="deviceIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testStages)">
            and test_stage in
            <foreach collection="testStages" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(lotTypes)">
            and lot_type in
            <foreach collection="lotTypes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(lotIds)">
            and lot_id in
            <foreach collection="lotIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(sblotIds)">
            and sblot_id in
            <foreach collection="sblotIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(waferIds)">
            and wafer_id in
            <foreach collection="waferIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(dataLoadeds)">
            and
             <include refid="dataIntegrityDataLoaded" />
             in
            <foreach collection="dataLoadeds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(failedTypes)">
            and failed_type in
            <foreach collection="failedTypes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(testPrograms)">
            and test_program in
            <foreach collection="testPrograms" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(ftpPath)">
            and ftp_path like concat('%', #{ftpPath}, '%')
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(fileName)">
            and file_name like concat('%', #{fileName}, '%')
        </if>
        <if test="!@org.springframework.util.ObjectUtils@isEmpty(fileNames)">
            and file_name in
            <foreach collection="fileNames" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="dataIntegrityOrderBy">
        order by
        <choose>
            <when test="orderByField == 'fileName'">
                file_name
            </when>
            <when test="orderByField == 'dataLoaded'">
                dataLoaded
            </when>
            <when test="orderByField == 'dataLoadingFailedStep'">
                stepOrder
            </when>
            <when test="orderByField == 'updateTime'">
                update_time
            </when>
            <otherwise>
                id
            </otherwise>
        </choose>
        ${orderByType}
        ,id desc
    </sql>

    <select id="queryDataIntegrityDropDown"
            resultType="com.guwave.datahub.bpms.dao.vo.bpms.dataIntegrity.DataIntegrityDropDown">
        select case #{filterField}
        when 'DATA_LOADING_FAILED_TYPE' then
            <include refid="dataIntegrityFailedType">
                <property name="completeExcFlag" value="false"/>
                <property name="failedTypeItem" value="res"/>
            </include>
        else res end showName,
        group_concat(res) name
        from (select case #{filterField}
                when 'FACTORY' then factory
                when 'DEVICE_ID' then device_id
                when 'TEST_STAGE' then test_stage
                when 'LOT_TYPE' then lot_type
                when 'LOT_ID' then lot_id
                when 'SBLOT_ID' then sblot_id
                when 'WAFER_ID' then wafer_id
                when 'DATA_LOADED' then
                <include refid="dataIntegrityDataLoaded" />
                when 'DATA_LOADING_FAILED_TYPE' then failed_type
                when 'TEST_PROGRAM' then test_program
                else ''
                end res
                from dw_file_warehousing_record m
                where
                <include refid="dataIntegrityCondition" />
                group by res) x
        where res is not null
        and res != ''
        group by showName
        order by showName
    </select>


    <select id="queryDataIntegrityBase"
            resultType="com.guwave.datahub.bpms.dao.vo.bpms.dataIntegrity.DataIntegrityBase">
        select count(1) totalCnt,
        ifnull(sum(if(process_status = 'FAIL', 1, 0)),0) failCnt,
        ifnull(sum(if(stepOrder = 100, 1, 0)),0) successCnt,
        ifnull(sum(if(stepOrder = 4, 1, 0)),0) keyFieldSuccessCnt,
        ifnull(sum(if(stepOrder = 3, 1, 0)),0) keyFieldFailCnt,
        ifnull(sum(if(stepOrder = 1, 1, 0)),0) transferToPlatformFailCnt
        from (
            select
            <include refid="dataIntegrityStepOrder" />
            stepOrder,
            process_status
            from dw_file_warehousing_record m
            where
            <include refid="dataIntegrityCondition"/>
        ) x
    </select>


    <select id="queryDataIntegrityRecord"
            resultType="com.guwave.datahub.bpms.dao.vo.bpms.dataIntegrity.DataIntegrityRecord">
        select x.*,
        case
        when stepOrder = 100 then 'Not Applicable'
        when stepOrder = 4 then 'Step4:解析入库'
        when stepOrder = 3 then 'Step3:关键字段验证'
        when stepOrder = 1 then 'Step1:传输至大数据存储计算平台'
        else 'Step2:触发解析' end dataLoadingFailedStep
       from (
        select id, file_name fileName,
        ftp_path fileLocation,
        <include refid="dataIntegrityDataLoaded"/>
        dataLoaded,
        <include refid="dataIntegrityStepOrder" />
        stepOrder,
        case
        when step = 7100 and process_status = 'SUCCESS' then 'Not Applicable'
        when process_status = 'FAIL' then
            <include refid="dataIntegrityFailedType">
                <property name="completeExcFlag" value="true"/>
                <property name="failedTypeItem" value="failed_type"/>
            </include>
        else '' end loadingFailedType,
        ifnull(m.failed_type, m.exception_type) originalFailedType,
        date_format(update_time, '%Y-%m-%d %H:%i:%s') updateTime,
        factory,
        device_id deviceName,
        test_stage testStage,
        lot_id lotId,
        sblot_id sblotId,
        test_program testProgram,
        wafer_id waferId,
        wafer_no waferNo,
        lot_type lotType,
        file_category fileCategory,
        test_area testArea,
        sub_customer subCustomer,
        date_format(start_t, '%Y-%m-%d %H:%i:%s') startTime,
        date_format(finish_t, '%Y-%m-%d %H:%i:%s') endTime,
        data_integrity_file_label fileLabel,
        data_integrity_file_comment remark,
        failed_fields failedFields,
        if
        (step = 1000 and process_status = 'FAIL', 0, 1)
        enableDownload,
        if(process_status = 'FAIL', 1, 0) deleteFlag,
        if(failed_type in ('PARSE_FILE_EXCEPTION', 'TOUCH_DOWN_NOT_FOUND_PRR_EXCEPTION'),
        1, 0) reRunFlag,
        if(failed_type in ('KEY_FIELD_NULL_EXCEPTION'),
        1, 0) repairFlag
        from dw_file_warehousing_record m
        where
        <include refid="dataIntegrityCondition" />
        <include refid="dataIntegrityOrderBy" />
        limit #{offset},#{size}
        ) x
    </select>


    <select id="countDataIntegrityRecord"
            resultType="java.lang.Long">
        select count(1) cnt
        from dw_file_warehousing_record m
        where
        <include refid="dataIntegrityCondition" />
    </select>

</mapper>