<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guwave.datahub.bpms.dao.repository.dw.mapper.MysqlExampleMapper">
    <!-- 自定义添加通过customer查询数据 SQL -->
    <select id="findByCustomer" resultType="com.guwave.onedata.dataware.dao.mysql.domain.dw.FileWarehousingRecord">
        SELECT id,customer,file_name as fileName,process_status as processStatus  FROM dw_file_warehousing_record WHERE CUSTOMER = #{customer} LIMIT 5
    </select>

</mapper>