<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="Common">
    <sql id="ckCustomerSql">
        and CUSTOMER = '${@com.guwave.datahub.bpms.app.vo.web.RequestContext@getCustomer()}'
        <!-- added 20240911 by wt for 数据权限V2 -->
        <if test="@com.guwave.datahub.bpms.app.vo.web.RequestContext@needProcDataPermission()">
            <choose>
                <when test="@com.guwave.datahub.bpms.app.vo.web.RequestContext@noDataPermission()">
                    <!-- 如果完全没有数据权限，直接 1 = 0 -->
                    and 1 = 0
                </when>
                <otherwise>
                    <!-- 有数据权限时，用户的多个角色之间是or的关系 -->
                    <foreach collection="@com.guwave.datahub.bpms.app.vo.web.RequestContext@getRoleDataPermissions()" item="roleDataPermission" open="and (" close=")" separator=" or ">
                        <trim prefix="(" suffix=")" prefixOverrides="and">
                            <if test="!@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.factorys)"><foreach collection="roleDataPermission.factorys" item="item" open="and m.FACTORY in (" close=")" separator=",">#{item}</foreach></if>
                            <if test="!@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.subCustomers)"><foreach collection="roleDataPermission.subCustomers" item="item" open="and m.SUB_CUSTOMER in (" close=")" separator=",">#{item}</foreach></if>
                            <if test="!@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.deviceIds)"><foreach collection="roleDataPermission.deviceIds" item="item" open="and m.DEVICE_ID in (" close=")" separator=",">#{item}</foreach></if>
                            <if test="!@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.lotTypes)"><foreach collection="roleDataPermission.lotTypes" item="item" open="and m.LOT_TYPE in (" close=")" separator=",">#{item}</foreach></if>
                            <if test="!@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.productDeviceIds)"><foreach collection="roleDataPermission.productDeviceIds" item="item" open="and m.DEVICE_ID in (" close=")" separator=",">#{item}</foreach></if>
                        </trim>
                    </foreach>
                </otherwise>
            </choose>
        </if>
    </sql>

    <sql id="mysqlCustomerSql">
        and m.customer = '${@com.guwave.datahub.bpms.app.vo.web.RequestContext@getCustomer()}'
        <!-- added 20240911 by wt for 数据权限V2 -->
        <if test="@com.guwave.datahub.bpms.app.vo.web.RequestContext@needProcDataPermission()">
            <choose>
                <when test="@com.guwave.datahub.bpms.app.vo.web.RequestContext@noDataPermission()">
                    <!-- 如果完全没有数据权限，直接 1 = 0 -->
                    and 1 = 0
                </when>
                <otherwise>
                    <if test="@com.guwave.datahub.bpms.app.vo.web.RequestContext@hasFactoryDataPermission() or @com.guwave.datahub.bpms.app.vo.web.RequestContext@hasSubCustomerDataPermission() or @com.guwave.datahub.bpms.app.vo.web.RequestContext@hasDeviceDataPermission()">
                        <!-- 有数据权限时，用户的多个角色之间是or的关系 -->
                        <foreach collection="@com.guwave.datahub.bpms.app.vo.web.RequestContext@getRoleDataPermissions()" item="roleDataPermission" open="and (" close=")" separator=" or ">
                            <choose>
                                <when test="!@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.factorys) or !@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.subCustomers) or !@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.deviceIds) or !@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.productDeviceIds)">
                                    <trim prefix="(" suffix=")" prefixOverrides="and">
                                        <if test="!@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.factorys)"><foreach collection="roleDataPermission.factorys" item="item" open="and m.factory in (" close=")" separator=",">#{item}</foreach></if>
                                        <if test="!@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.subCustomers)"><foreach collection="roleDataPermission.subCustomers" item="item" open="and m.sub_customer in (" close=")" separator=",">#{item}</foreach></if>
                                        <if test="!@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.deviceIds)"><foreach collection="roleDataPermission.deviceIds" item="item" open="and m.device_id in (" close=")" separator=",">#{item}</foreach></if>
                                        <!-- <if test="!@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.lotTypes)"><foreach collection="roleDataPermission.lotTypes" item="item" open="and m.lot_type in (" close=")" separator=",">#{item}</foreach></if> -->
                                        <if test="!@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.productDeviceIds)"><foreach collection="roleDataPermission.productDeviceIds" item="item" open="and m.device_id in (" close=")" separator=",">#{item}</foreach></if>
                                    </trim>
                                </when>
                                <otherwise>
                                    1 = 0
                                </otherwise>
                            </choose>
                        </foreach>
                    </if>
                </otherwise>
            </choose>
        </if>
    </sql>

    <sql id="mysqlCustomerSqlOnlySubCustomerDevice">
        and m.customer = '${@com.guwave.datahub.bpms.app.vo.web.RequestContext@getCustomer()}'
        <!-- added 20240911 by wt for 数据权限V2 -->
        <if test="@com.guwave.datahub.bpms.app.vo.web.RequestContext@needProcDataPermission()">
            <choose>
                <when test="@com.guwave.datahub.bpms.app.vo.web.RequestContext@noDataPermission()">
                    <!-- 如果完全没有数据权限，直接 1 = 0 -->
                    and 1 = 0
                </when>
                <otherwise>
                    <if test="@com.guwave.datahub.bpms.app.vo.web.RequestContext@hasSubCustomerDataPermission() or @com.guwave.datahub.bpms.app.vo.web.RequestContext@hasDeviceDataPermission()">
                        <foreach collection="@com.guwave.datahub.bpms.app.vo.web.RequestContext@getRoleDataPermissions()" item="roleDataPermission" open="and (" close=")" separator=" or ">
                            <choose>
                                <when test="!@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.subCustomers) or !@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.deviceIds) or !@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.productDeviceIds)">
                                    <trim prefix="(" suffix=")" prefixOverrides="and">
                                        <if test="!@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.subCustomers)"><foreach collection="roleDataPermission.subCustomers" item="item" open="and m.sub_customer in (" close=")" separator=",">#{item}</foreach></if>
                                        <if test="!@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.deviceIds)"><foreach collection="roleDataPermission.deviceIds" item="item" open="and m.device_id in (" close=")" separator=",">#{item}</foreach></if>
                                        <if test="!@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.productDeviceIds)"><foreach collection="roleDataPermission.productDeviceIds" item="item" open="and m.device_id in (" close=")" separator=",">#{item}</foreach></if>
                                    </trim>
                                </when>
                                <otherwise>
                                    1 = 0
                                </otherwise>
                            </choose>
                        </foreach>
                    </if>
                </otherwise>
            </choose>
        </if>
    </sql>

    <sql id="mysqlCustomerSqlOnlySubCustomerFactory">
        and m.customer = '${@com.guwave.datahub.bpms.app.vo.web.RequestContext@getCustomer()}'
        <!-- added 20240911 by wt for 数据权限V2 -->
        <if test="@com.guwave.datahub.bpms.app.vo.web.RequestContext@needProcDataPermission()">
            <choose>
                <when test="@com.guwave.datahub.bpms.app.vo.web.RequestContext@noDataPermission()">
                    <!-- 如果完全没有数据权限，直接 1 = 0 -->
                    and 1 = 0
                </when>
                <otherwise>
                    <if test="@com.guwave.datahub.bpms.app.vo.web.RequestContext@hasSubCustomerDataPermission() or @com.guwave.datahub.bpms.app.vo.web.RequestContext@hasFactoryDataPermission()">
                        <foreach collection="@com.guwave.datahub.bpms.app.vo.web.RequestContext@getRoleDataPermissions()" item="roleDataPermission" open="and (" close=")" separator=" or ">
                            <choose>
                                <when test="!@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.product)">
                                    <trim prefix="(" suffix=")" prefixOverrides="and">
                                        <if test="!@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.factorys)"><foreach collection="roleDataPermission.factorys" item="item" open="and m.factory in (" close=")" separator=",">#{item}</foreach></if>
                                        <if test="!@org.springframework.util.ObjectUtils@isEmpty(roleDataPermission.subCustomers)"><foreach collection="roleDataPermission.subCustomers" item="item" open="and m.sub_customer in (" close=")" separator=",">#{item}</foreach></if>
                                    </trim>
                                </when>
                                <otherwise>
                                    1 = 0
                                </otherwise>
                            </choose>
                        </foreach>
                    </if>
                </otherwise>
            </choose>
        </if>
    </sql>

    <sql id="ckTestAreaCpCond">
        and TEST_AREA in ('CP','CP(Map)','CP(InklessMap)','BUMP','BUMP(Map)','BURNIN','BURNIN(Map)','WLT','WLT(Map)','REL(Wafer)','REL(Map)','ASSY(Wafer)','ASSY(Map)')
    </sql>

    <sql id="ckTestAreaFtCond">
        and TEST_AREA in ('ASSY','REL','FT','SLT','MT','NA','CAL')
    </sql>
</mapper>
