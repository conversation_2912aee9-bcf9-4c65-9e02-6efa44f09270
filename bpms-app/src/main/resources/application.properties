spring.module.name=${module.name}
spring.main.allow-circular-references=true
# server
server.address=${serverAddress}
server.port=${serverPort}

# 开启响应压缩
server.compression.enabled=true
# 设置需要压缩的内容类型，例如文本类、JSON 等
server.compression.mime-types=text/html,text/xml,text/plain,application/json
# 设置触发压缩的响应体最小大小（字节），低于此大小不进行压缩
server.compression.min-response-size=1024

spring.mvc.throw-exception-if-no-handler-found=true
spring.web.resources.add-mappings=false
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
spring.servlet.multipart.max-file-size=10GB
spring.servlet.multipart.max-request-size=10GB

# spring doc config
springdoc.api-docs.enabled=${api-docs.enabled}

# jpa config
spring.datasource.primary.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.primary.jdbc-url=jdbc:mysql://${database.address}/${database.name.bpms}?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&failOverReadOnly=false&useSSL=false&allowPublicKeyRetrieval=true&useTimezone=true&serverTimezone=Asia/Shanghai&allowMultiQueries=true&rewriteBatchedStatements=true
spring.datasource.primary.username=${database.username.bpms}
spring.datasource.primary.password=${database.password.bpms}
spring.datasource.primary.database=${database.name.bpms}
spring.datasource.primary.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.primary.hikari.read-only=false
spring.datasource.primary.hikari.auto-commit=true
spring.datasource.primary.hikari.minimum-idle=5
spring.datasource.primary.hikari.maximum-pool-size=50
spring.datasource.primary.hikari.idle-timeout=30000
spring.datasource.primary.hikari.pool-name=HikariCP
spring.datasource.primary.hikari.max-lifetime=60000
spring.datasource.primary.hikari.connection-timeout=5000
spring.datasource.primary.hikari.connection-test-query=SELECT 1

spring.datasource.secondary.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.secondary.jdbc-url=jdbc:mysql://${database.address}/${database.name.dw}?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&failOverReadOnly=false&useSSL=false&allowPublicKeyRetrieval=true&useTimezone=true&serverTimezone=Asia/Shanghai&allowMultiQueries=true&rewriteBatchedStatements=true
spring.datasource.secondary.username=${database.username.dw}
spring.datasource.secondary.password=${database.password.dw}
spring.datasource.secondary.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.secondary.hikari.read-only=false
spring.datasource.secondary.hikari.auto-commit=true
spring.datasource.secondary.hikari.minimum-idle=5
spring.datasource.secondary.hikari.maximum-pool-size=50
spring.datasource.secondary.hikari.idle-timeout=30000
spring.datasource.secondary.hikari.pool-name=HikariCP
spring.datasource.secondary.hikari.max-lifetime=60000
spring.datasource.secondary.hikari.connection-timeout=5000
spring.datasource.secondary.hikari.connection-test-query=SELECT 1

spring.datasource.tertiary.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.tertiary.jdbc-url=jdbc:mysql://${database.address}/${database.name.linkx}?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&failOverReadOnly=false&useSSL=false&allowPublicKeyRetrieval=true&useTimezone=true&serverTimezone=Asia/Shanghai&allowMultiQueries=true&rewriteBatchedStatements=true
spring.datasource.tertiary.username=${database.username.linkx}
spring.datasource.tertiary.password=${database.password.linkx}
spring.datasource.tertiary.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.tertiary.hikari.read-only=false
spring.datasource.tertiary.hikari.auto-commit=true
spring.datasource.tertiary.hikari.minimum-idle=5
spring.datasource.tertiary.hikari.maximum-pool-size=50
spring.datasource.tertiary.hikari.idle-timeout=30000
spring.datasource.tertiary.hikari.pool-name=HikariCP
spring.datasource.tertiary.hikari.max-lifetime=60000
spring.datasource.tertiary.hikari.connection-timeout=5000
spring.datasource.tertiary.hikari.connection-test-query=SELECT 1

spring.datasource.quaternary.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.quaternary.jdbc-url=jdbc:mysql://${database.address}/${database.name.compute}?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&failOverReadOnly=false&useSSL=false&allowPublicKeyRetrieval=true&useTimezone=true&serverTimezone=Asia/Shanghai&allowMultiQueries=true&rewriteBatchedStatements=true
spring.datasource.quaternary.username=${database.username.dw}
spring.datasource.quaternary.password=${database.password.dw}
spring.datasource.quaternary.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.quaternary.hikari.read-only=false
spring.datasource.quaternary.hikari.auto-commit=true
spring.datasource.quaternary.hikari.minimum-idle=5
spring.datasource.quaternary.hikari.maximum-pool-size=50
spring.datasource.quaternary.hikari.idle-timeout=30000
spring.datasource.quaternary.hikari.pool-name=HikariCP
spring.datasource.quaternary.hikari.max-lifetime=60000
spring.datasource.quaternary.hikari.connection-timeout=5000
spring.datasource.quaternary.hikari.connection-test-query=SELECT 1

spring.data.jpa.repositories.enabled=true
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.generate-ddl=false
spring.jpa.database=MYSQL
spring.jpa.show-sql=false
spring.jpa.open-in-view=false
spring.jpa.properties.hibernate.jdbc.batch_size=10000
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect

# Auth config
auth.baseUrl=${authBaseUrl}

# License config
license.baseUrl=${licenseBaseUrl}

# dubbo config
dubbo.application.name=bpms-app
dubbo.application.serialize-check-status=WARN
dubbo.application.qos-enable=false
dubbo.registry.address=zookeeper://${zookeeper.address}
dubbo.consumer.check=false
dubbo.consumer.timeout=${rpc.timeout}
dubbo.consumer.retries=0
dubbo.consumer.group=${environment.group}

# ck config
spring.data.clickhouse.address=${data.clickhouse.address}
spring.data.clickhouse.main.address=${data.clickhouse.main.address}
spring.data.clickhouse.replica.address=${data.clickhouse.replica.address}
spring.data.clickhouse.username=${data.clickhouse.username}
spring.data.clickhouse.password=${data.clickhouse.password}
spring.data.clickhouse.cluster=${data.clickhouse.cluster}
spring.data.clickhouse.dwdDbName=${data.clickhouse.dwdDbName}
spring.data.clickhouse.maxMutationsCnt=${data.clickhouse.maxMutationsCnt}
spring.ck.ads.database = ${ck.ads.database}
spring.ck.dws.database = ${ck.dws.database}
spring.ck.dim.database = ${ck.dim.database}
spring.ck.dwd.database = ${ck.dwd.database}
spring.ck.ods.database = ${ck.ods.database}
spring.ck.skyeye.database = ${ck.skyeye.database}

# ClickHouse连接池配置
spring.datasource.clickhouse.maximum-pool-size=10
spring.datasource.clickhouse.minimum-idle=2
spring.datasource.clickhouse.idle-timeout=30000

# mysql config
spring.data.mysql.address = ${data.mysql.address}
spring.mysql.onedata.database = ${mysql.onedata.database}
spring.data.mysql.username = ${data.mysql.username}
spring.data.mysql.password = ${data.mysql.password}
spring.data.mysql.product.username = ${mysql.product.username}
spring.data.mysql.product.password = ${mysql.product.password}
spring.mysql.product.database = ${mysql.product.database}
spring.mysql.lotmanager.database = ${mysql.lotmanager.database}

# kafka
spring.kafka.bootstrap-servers=${kafka.bootstrapServers}
spring.kafka.consumer.properties.group.id=${kafka.consumer.consumeGroup}
spring.kafka.consumer.enable-auto-commit=true
spring.kafka.consumer.auto-commit-interval=${kafka.consumer.autoCommitInterval}
spring.kafka.consumer.auto-offset-reset=${kafka.consumer.autoOffsetReset}
spring.kafka.consumer.properties.session.timeout.ms=120000
spring.kafka.consumer.properties.request.timeout.ms=180000
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.ByteArrayDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.ByteArrayDeserializer
spring.kafka.consumer.max-poll-records=${kafka.consumer.maxPollRecords}
spring.kafka.listener.missing-topics-fatal=false
spring.kafka.listener.concurrency=${kafka.listener.concurrency}
spring.kafka.listener.ack-mode=RECORD
spring.kafka.listener.type=SINGLE
spring.kafka.listener.poll-timeout=5000
spring.kafka.producer.retries=0
spring.kafka.producer.acks=1
spring.kafka.producer.batch-size=${kafka.producer.batchSize}
spring.kafka.producer.properties.linger.ms=${kafka.producer.lingerMs}
spring.kafka.producer.buffer-memory=${kafka.producer.bufferMemory}
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.ByteArraySerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.ByteArraySerializer
spring.kafka.properties.max.request.size=104857600
spring.kafka.loadEndFlagTopic=${kafka.loadEndFlagTopic}
spring.kafka.calculateEndFlagTopic=${kafka.calculateEndFlagTopic}
spring.kafka.computeResultTopic=${kafka.computeResultTopic}
spring.kafka.repairTopic=${kafka.repairTopic}
spring.kafka.repairFinishTopic=${kafka.repairFinishTopic}
spring.kafka.repairRecalculateTopic=${kafka.repairRecalculateTopic}

spring.data.replay.file.maxCount=${data.replay.file.maxCount}
spring.data.replay.file.batchSize=${data.replay.file.batchSize}
spring.data.replay.task.maxCount=${data.replay.task.maxCount}

spring.task.resourceManagerUrl=${resourceManagerUrl}

#hdfs
spring.gdp.file.hdfsMode=${gdp.file.hdfsMode}
spring.gdp.file.hdfsUrl=${gdp.file.hdfsUrl}
spring.gdp.file.hdfsUser=${gdp.file.hdfsUser}
# /user/glory/clear_rule_preview/customer/testArea/factory/fileCategory/mTime/uuid/
gdp.file.hdfsRootPath=/user/${gdp.file.hdfsUser}/clear_rule_preview/%s/%s/%s/%s/%s/%s/

spring.rar.tmpDirectory=rar_tmp
spring.upload.baseDir=upload
spring.parse.baseDir=parse

# scheduling
spring.task.scheduling.pool.size=${task.scheduling.pool.size}
spring.task.scheduling.shutdown.await-termination=true
spring.task.scheduling.shutdown.await-termination-period=60
spring.task.scheduling.thread-name-prefix=listener-file-

spring.scheduler.polling.milliseconds=${scheduler.polling.milliseconds}
spring.scheduler.message.retry.millisecond=${scheduler.message.retry.millisecond}
spring.scheduler.timer.dailyReport=${scheduler.timer.dailyReport}

spring.adapter.python.install.path=${adapter.python.install.path}

# siteNum校验,filter(siteNum != 255)
spring.settings.siteNumFilterFlag=${settings.siteNumFilterFlag}

# keyFields
spring.handler.cp.keyFields=${handler.cp.keyFields}
spring.handler.ft.keyFields=${handler.ft.keyFields}

# clearRulePreview parse file threadCnt
spring.clearRulePreview.threadCnt=${clearRulePreview.threadCnt}

spring.scheduler.timer.freezeDataTask=${scheduler.timer.freezeDataTask}
spring.scheduler.timer.deleteCkDataFromDiskTask=${scheduler.timer.deleteCkDataFromDiskTask}
spring.freezeDataTask.lotValidDay=${freezeDataTask.lotValidDay}
spring.freezeDataTask.lotValidDayForDevice=${freezeDataTask.lotValidDayForDevice}
spring.freezeDataTask.lotValidTimeType=${freezeDataTask.lotValidTimeType}
spring.hdfs.dwd.resultDir=${hdfs.dwd.resultDir}
spring.hdfs.dwd.historyResultDir=${hdfs.dwd.historyResultDir}
spring.hdfs.dim.resultDir=${hdfs.dim.resultDir}
spring.hdfs.dws.resultDir=${hdfs.dws.resultDir}
spring.scheduler.timer.cleanSftpBatchInfoTask=${scheduler.timer.cleanSftpBatchInfoTask}


# redis
spring.redis.host=${redis.host}
spring.redis.port=${redis.port}
spring.redis.database=0
spring.redis.password=${redis.password}
spring.redis.timeout=3000
spring.redis.jedis.pool.min-idle=1
spring.redis.jedis.pool.max-idle=10
spring.redis.jedis.pool.max-active=20
spring.redis.jedis.pool.max-wait=-1
spring.data.redis.repositories.enabled=false

spring.retry.message.stdf.data.topic=${retry.message.stdf.data.topic}
spring.retry.message.wat.data.topic=${retry.message.wat.data.topic}
spring.retry.message.stdf.data.useFlag=${retry.message.stdf.data.useFlag}

spring.lotBucketNum=${lotBucketNum}

spring.scheduler.timer.fastRetryFailRecordTask=${scheduler.timer.fastRetryFailRecordTask}
spring.scheduler.timer.slowRetryFailRecordTask=${scheduler.timer.slowRetryFailRecordTask}

spring.handler.file.dailyReportReadPath=${handler.file.readPath}dailyReport/
spring.handler.font.path=${handler.font.path}
spring.hdfs.dailyReportDir=${hdfs.dailyReportDir}
spring.handler.file.confReadPath=${handler.file.readPath}config/
spring.hdfs.adapterDir=${hdfs.adapterDir}

spring.scheduler.timer.generateDailySummaryTask=${scheduler.timer.generateDailySummaryTask}
spring.scheduler.timer.sendDailySummaryTask=${scheduler.timer.sendDailySummaryTask}
spring.notification.dailySummaryCode=${notification.dailySummaryCode}
spring.notification.email=${notification.email}

spring.scheduler.cron.expression = ${scheduler.cron.expression}

mybatis.mapper-locations=classpath*:/mapper/**/*.xml
mybatis.configuration.map-underscore-to-camel-case=true

spring.ambari.server.url= ${ambari.server.url}
spring.ambari.server.username= ${ambari.server.username}
spring.ambari.server.password= ${ambari.server.password}

spring.handler.file.dailySummaryPath=${handler.file.readPath}dailySummary/