module.name=bpms-app-q
# server
serverAddress=0.0.0.0
serverPort=16666

# spring doc config
api-docs.enabled=true

# database config
database.address=mpp01.dev.guwave.com:3306
database.name.bpms=bpms
database.username.bpms=bpms
database.password.bpms=bpms@guwave
database.name.dw=onedata
database.name.compute=compute
database.username.dw=bi
database.password.dw=bi@guwave
database.name.linkx=linkx
database.username.linkx=scheduler
database.password.linkx=scheduler@guwave

# Auth config
authBaseUrl=http://dev01.guwave.com:9094

# License config
licenseBaseUrl=http://dev01.guwave.com:9094

# dubbo config
zookeeper.address=gdp01.dev.guwave.com:2181
# qa deploy fill with   qa    ,otherwise fill with    prod
environment.group=prod
# rpc timeout (ms)
rpc.timeout=60000

# ck config
data.clickhouse.address=*******************************************
data.clickhouse.main.address=*******************************************
data.clickhouse.replica.address=*******************************************
data.clickhouse.username=admin
data.clickhouse.password=admin@ck@Guwave
data.clickhouse.maxMutationsCnt=500
ck.ads.database=ads
ck.dws.database=dws
ck.dim.database=dim
ck.dwd.database=dwd
ck.ods.database=ods
ck.skyeye.database=skyeye

# mysql config
data.mysql.address=**************************************
mysql.onedata.database=onedata
data.mysql.username=bi
data.mysql.password=bi@guwave
mysql.product.username=product
mysql.product.password=product@guwave
mysql.product.database=product
mysql.lotmanager.database=lotmanager

# redis config
redis.host=redis01.dev.guwave.com
redis.port=6379
redis.password=devops@guwave

# kafka config
kafka.bootstrapServers=gdp01.dev.guwave.com:6667
kafka.consumer.consumeGroup=OnedataBpms_q
kafka.consumer.autoOffsetReset=latest
kafka.consumer.autoCommitInterval=1000
kafka.consumer.maxPollRecords=10
kafka.listener.concurrency=1
kafka.producer.batchSize=104857600
kafka.producer.lingerMs=0
kafka.producer.bufferMemory=104857600
kafka.loadEndFlagTopic=t_dw_load_end_flag
kafka.calculateEndFlagTopic=t_dw_calculate_end_flag
kafka.computeResultTopic=t_compute_result
kafka.repairTopic=t_dw_repair
kafka.repairFinishTopic=t_dw_repair_finish
kafka.repairRecalculateTopic=t_dw_repair_recalculate

data.replay.file.maxCount=10000
data.replay.file.batchSize=2
data.replay.task.maxCount=10

# yarn
resourceManagerUrl=http://gdp01.dev.guwave.com:8088/

# hdfs
gdp.file.hdfsMode=STANDALONE
gdp.file.hdfsUrl=hdfs://gdp01.dev.guwave.com:8020
gdp.file.hdfsUser=glory

# scheduling
task.scheduling.pool.size=2
scheduler.polling.milliseconds=5000
scheduler.message.retry.millisecond=60000
scheduler.timer.dailyReport=0 0 1 * * ?

adapter.python.install.path=/usr/bin/python3

# siteNum校验,filter(siteNum != 255)
settings.siteNumFilterFlag=false

# keyFields
handler.cp.keyFields=deviceId,testArea,lotType,testStage,lotId,waferId,waferNo,startT,finishT
handler.ft.keyFields=deviceId,testArea,lotType,testStage,lotId,sblotId,startT,finishT

# clearRulePreview parse file threadCnt
clearRulePreview.threadCnt=5

scheduler.timer.freezeDataTask=0 0 0 ? * 7
scheduler.timer.deleteCkDataFromDiskTask=0 0/30 * * * ?
# when lot update_time + lotValidDay < now ,data will be freezed
freezeDataTask.lotValidDay=1460
# deviceA,deviceB:1000;deviceC:2000
freezeDataTask.lotValidDayForDevice=GUBO_PART_TYP_001,TMNR89H:365;SS497BD-DEFAULT:1000
freezeDataTask.lotValidTimeType=1
hdfs.dwd.resultDir=/user/glory/data/onedata/dataware/dwd/result/*/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}
hdfs.dwd.historyResultDir=/user/glory/data/onedata/dataware/dwd_history/result/*/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/LOT_ID={LOT_ID}
hdfs.dim.resultDir=/user/glory/data/onedata/dataware/dim/result/*/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}
hdfs.dws.resultDir=/user/glory/data/onedata/dataware/dws/result/*/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}
scheduler.timer.cleanSftpBatchInfoTask=0 0 0 * * ?

retry.message.stdf.data.topic=t_dw_ft,t_dw_kgu,t_dw_eqc,t_dw_lat,t_dw_slt,t_dw_cal,t_dw_cp,t_dw_cp_map,t_dw_cp_inkless_map
retry.message.wat.data.topic=t_dw_wat
retry.message.stdf.data.useFlag=false

lotBucketNum=6

# 失败数据重跑 快速重试和慢速重试的定时任务
scheduler.timer.fastRetryFailRecordTask=0 0/30 * * * ?
scheduler.timer.slowRetryFailRecordTask=0 0 0/6 * * ?

handler.file.readPath=/home/<USER>/deploy/onedata/bpms/bpms-app/data/read/
handler.font.path=/usr/share/fonts/
hdfs.dailyReportDir=/user/glory/data/onedata/report/daily/
hdfs.adapterDir=/user/glory/adapter/

# 数据平台日报
scheduler.timer.generateDailySummaryTask=0 10 0 * * ?
scheduler.timer.sendDailySummaryTask=0 30 8 * * ?
notification.dailySummaryCode=DATAWARE_DAILY_SUMMARY
notification.email=<EMAIL>

# 定时任务扫描间隔(毫秒)
scheduler.cron.expression=0 0/1 * * * ?

ambari.server.url=http://gdp01:8080
ambari.server.username=admin
ambari.server.password=admin
