<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>大数据平台运行日报</title>
</head>
<body style="padding: 20px; margin: 0; background-color: #ffffff;">
<div style="width: 100%; margin: 0 auto; padding: 0 15px;">

  <!-- 平台数据概览 -->
  <div style="margin-bottom: 10px;">
    <h3 style="margin-bottom: 5px; color: #333; font-size: 18px;">平台数据概览</h3>
    <div style="overflow-x: auto;">
      <table style="width: 100%; border-collapse: collapse; border-spacing: 0; font-size: 14px; border: 1px solid #dee2e6;">
        <thead>
        <tr>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">日期</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">新增CP Wafer数</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">新增FT Lot数</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">新增Die数</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">新增测项数</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">自动重跑次数</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">手动重跑次数</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">磁盘使用量(G)<br/>(GDP|MPP)</th>
        </tr>
        </thead>
        <tbody>
        <tr th:each="overview : ${autoPlatformOverview}" style="background-color: #ffffff;">
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.date}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.datas[0].cpWaferCnt}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.datas[0].ftLotCnt}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.datas[0].dieCnt}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.datas[0].testItemCnt}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.datas[0].dieTaskCntAutoRerun}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.datas[0].dieTaskCntManualRerun}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${(overview.datas[0].gdpDiskUsed == null ? '-' :  overview.datas[0].gdpDiskUsed) + ' | ' + (overview.datas[0].mppDiskUsed == null ? '-' :  overview.datas[0].mppDiskUsed)}"></td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>


  <!-- 自动运行任务概览 -->
  <div style="margin-bottom: 10px;">
    <h3 style="margin-bottom: 5px; color: #333; font-size: 18px;">自动运行任务概览</h3>
    <div style="overflow-x: auto;">
      <table style="width: 100%; border-collapse: collapse; border-spacing: 0; font-size: 14px; border: 1px solid #dee2e6;">
        <thead>
        <tr>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">日期</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">新增文件数</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">新增文件量(G)<br/>(压缩|原始)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">扫描耗时(秒)<br/>(平均|最小|最大)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">转换文件<br/>(失败|成功|待处理|处理中)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">转换耗时(秒)<br/>(平均|最小|最大)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">解析文件<br/>(失败|成功|待处理|处理中)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">解析耗时(秒)<br/>(平均|最小|最大)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">Die任务<br/>(失败|成功|待处理|处理中)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">Die耗时(秒)<br/>(平均|最小|最大)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">Die等待(秒)<br/>(平均|最小|最大)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">测项任务<br/>(失败|成功|待处理|处理中)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">测项耗时(秒)<br/>(平均|最小|最大)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">测项等待(秒)<br/>(平均|最小|最大)</th>
        </tr>
        </thead>
        <tbody>
        <tr th:each="overview : ${autoTaskOverview}" style="background-color: #ffffff;">
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.date}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.datas[0].fileCnt}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.datas[0].fileSizeCompress + ' | ' + overview.datas[0].fileSizeOrigin}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;"
              th:text="${overview.datas[0].scanFileConsumeCnt == 0} ? '-'
          : ((${overview.datas[0].scanFileConsumeAvg} == 0 ? '<1' : ${overview.datas[0].scanFileConsumeAvg}) + ' | '
          + (${overview.datas[0].scanFileConsumeMin} == 0 ? '<1' : ${overview.datas[0].scanFileConsumeMin}) + ' | '
          + (${overview.datas[0].scanFileConsumeMax} == 0 ? '<1' : ${overview.datas[0].scanFileConsumeMax}))">
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;">
            <span th:style="${overview.datas[0].convertFileCntFail} > 0 ? 'color: red; font-weight: bold;' : ''">
              [[${overview.datas[0].convertFileCntFail}]]
            </span>
            |
            [[${overview.datas[0].convertFileCntSuccess}]]
            |
            [[${overview.datas[0].convertFileCntPending}]]
            |
            [[${overview.datas[0].convertFileCntProcessing}]]
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;"
              th:text="${overview.datas[0].convertfileConsumeCnt == 0} ? '-'
            : ((${overview.datas[0].convertfileConsumeAvg} == 0 ? '<1' : ${overview.datas[0].convertfileConsumeAvg}) + ' | '
            + (${overview.datas[0].convertfileConsumeMin} == 0 ? '<1' : ${overview.datas[0].convertfileConsumeMin}) + ' | '
            + (${overview.datas[0].convertfileConsumeMax} == 0 ? '<1' : ${overview.datas[0].convertfileConsumeMax}))">
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;">
            <span th:style="${overview.datas[0].parsefileCntFail} > 0 ? 'color: red; font-weight: bold;' : ''">
              [[${overview.datas[0].parsefileCntFail}]]
            </span>
            |
            [[${overview.datas[0].parsefileCntSuccess}]]
            |
            [[${overview.datas[0].parsefileCntPending}]]
            |
            [[${overview.datas[0].parsefileCntProcessing}]]
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;"
              th:text="${overview.datas[0].parsefileConsumeCnt == 0} ? '-'
            : ((${overview.datas[0].parsefileConsumeAvg} == 0 ? '<1' : ${overview.datas[0].parsefileConsumeAvg}) + ' | '
            + (${overview.datas[0].parsefileConsumeMin} == 0 ? '<1' : ${overview.datas[0].parsefileConsumeMin}) + ' | '
            + (${overview.datas[0].parsefileConsumeMax} == 0 ? '<1' : ${overview.datas[0].parsefileConsumeMax}))">
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;">
            <span th:style="${overview.datas[0].dieTaskCntFail} > 0 ? 'color: red; font-weight: bold;' : ''">
              [[${overview.datas[0].dieTaskCntFail}]]
            </span>
            |
            [[${overview.datas[0].dieTaskCntSuccess}]]
            |
            [[${overview.datas[0].dieTaskCntPending}]]
            |
            [[${overview.datas[0].dieTaskCntProcessing}]]
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;"
              th:text="${overview.datas[0].dieTaskConsumeCnt == 0} ? '-'
            : ((${overview.datas[0].dieTaskConsumeAvg} == 0 ? '<1' : ${overview.datas[0].dieTaskConsumeAvg}) + ' | '
            + (${overview.datas[0].dieTaskConsumeMin} == 0 ? '<1' : ${overview.datas[0].dieTaskConsumeMin}) + ' | '
            + (${overview.datas[0].dieTaskConsumeMax} == 0 ? '<1' : ${overview.datas[0].dieTaskConsumeMax}))">
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;"
              th:text="${overview.datas[0].dieWaitTimeCnt == 0} ? '-'
            : ((${overview.datas[0].dieWaitTimeAvg} == 0 ? '<1' : ${overview.datas[0].dieWaitTimeAvg}) + ' | '
            + (${overview.datas[0].dieWaitTimeMin} == 0 ? '<1' : ${overview.datas[0].dieWaitTimeMin}) + ' | '
            + (${overview.datas[0].dieWaitTimeMax} == 0 ? '<1' : ${overview.datas[0].dieWaitTimeMax}))">
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;">
            <span th:style="${overview.datas[0].testItemTaskCntFail} > 0 ? 'color: red; font-weight: bold;' : ''">
              [[${overview.datas[0].testItemTaskCntFail}]]
            </span>
            |
            [[${overview.datas[0].testItemTaskCntSuccess}]]
            |
            [[${overview.datas[0].testItemTaskCntPending}]]
            |
            [[${overview.datas[0].testItemTaskCntProcessing}]]
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;"
              th:text="${overview.datas[0].testItemTaskConsumeCnt == 0} ? '-'
            : ((${overview.datas[0].testItemTaskConsumeAvg} == 0 ? '<1' : ${overview.datas[0].testItemTaskConsumeAvg}) + ' | '
            + (${overview.datas[0].testItemTaskConsumeMin} == 0 ? '<1' : ${overview.datas[0].testItemTaskConsumeMin}) + ' | '
            + (${overview.datas[0].testItemTaskConsumeMax} == 0 ? '<1' : ${overview.datas[0].testItemTaskConsumeMax}))">
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;"
              th:text="${overview.datas[0].testItemWaitTimeCnt == 0} ? '-'
            : ((${overview.datas[0].testItemWaitTimeAvg} == 0 ? '<1' : ${overview.datas[0].testItemWaitTimeAvg}) + ' | '
            + (${overview.datas[0].testItemWaitTimeMin} == 0 ? '<1' : ${overview.datas[0].testItemWaitTimeMin}) + ' | '
            + (${overview.datas[0].testItemWaitTimeMax} == 0 ? '<1' : ${overview.datas[0].testItemWaitTimeMax}))">
          </td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>



  <!-- 手动运行任务概览 -->
  <div style="margin-bottom: 10px;">
    <h3 style="margin-bottom: 5px; color: #333; font-size: 18px;">手动运行任务概览</h3>
    <div style="overflow-x: auto;">
      <table style="width: 100%; border-collapse: collapse; border-spacing: 0; font-size: 14px; border: 1px solid #dee2e6;">
        <thead>
        <tr>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">日期</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">新增文件数</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">新增文件量(G)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">解析文件<br/>(失败|成功|待处理|处理中)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">解析耗时(秒)<br/>(平均|最小|最大)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">数仓任务<br/>(失败|成功|待处理|处理中)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">数仓耗时(秒)<br/>(平均|最小|最大)</th>
        </tr>
        </thead>
        <tbody>
        <tr th:each="overview : ${manualTaskOverview}" style="background-color: #ffffff;">
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.date}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.datas[0].fileCnt}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${overview.datas[0].fileSize}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;">
            <span th:style="${overview.datas[0].parsefileCntFail} > 0 ? 'color: red; font-weight: bold;' : ''">
              [[${overview.datas[0].parsefileCntFail}]]
            </span>
            |
            [[${overview.datas[0].parsefileCntSuccess}]]
            |
            [[${overview.datas[0].parsefileCntPending}]]
            |
            [[${overview.datas[0].parsefileCntProcessing}]]
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;"
              th:text="${overview.datas[0].parsefileConsumeCnt == 0} ? '-'
            : ((${overview.datas[0].parsefileConsumeAvg} == 0 ? '<1' : ${overview.datas[0].parsefileConsumeAvg}) + ' | '
            + (${overview.datas[0].parsefileConsumeMin} == 0 ? '<1' : ${overview.datas[0].parsefileConsumeMin}) + ' | '
            + (${overview.datas[0].parsefileConsumeMax} == 0 ? '<1' : ${overview.datas[0].parsefileConsumeMax}))">
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;">
            <span th:style="${overview.datas[0].wareHouseTaskCntFail} > 0 ? 'color: red; font-weight: bold;' : ''">
              [[${overview.datas[0].wareHouseTaskCntFail}]]
            </span>
            |
            [[${overview.datas[0].wareHouseTaskCntSuccess}]]
            |
            [[${overview.datas[0].wareHouseTaskCntPending}]]
            |
            [[${overview.datas[0].wareHouseTaskCntProcessing}]]
          </td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;"
              th:text="${overview.datas[0].wareHouseTaskConsumeCnt == 0} ? '-'
            : ((${overview.datas[0].wareHouseTaskConsumeAvg} == 0 ? '<1' : ${overview.datas[0].wareHouseTaskConsumeAvg}) + ' | '
            + (${overview.datas[0].wareHouseTaskConsumeMin} == 0 ? '<1' : ${overview.datas[0].wareHouseTaskConsumeMin}) + ' | '
            + (${overview.datas[0].wareHouseTaskConsumeMax} == 0 ? '<1' : ${overview.datas[0].wareHouseTaskConsumeMax}))">
          </td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>




  <!-- 自动任务耗时Top 10 -->
  <div style="margin-bottom: 10px;">
    <h3 style="margin-bottom: 5px; color: #333; font-size: 18px;">自动任务耗时Top 10</h3>
    <div style="overflow-x: auto;">
      <table style="width: 100%; border-collapse: collapse; border-spacing: 0; font-size: 14px; border: 1px solid #dee2e6;">
        <thead>
        <tr>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">任务名</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">开始时间</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">结束时间</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">耗时(秒)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">任务描述</th>
        </tr>
        </thead>
        <tbody>
        <tr th:if="${#lists.isEmpty(autoTaskSlow[0].datas)}">
          <td colspan="5" style="padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">暂无数据</td>
        </tr>
        <tr th:each="task : ${autoTaskSlow[0].datas}" style="background-color: #ffffff;">
          <td style="vertical-align: middle; width: 400px; padding: 3px; border: 1px solid #dee2e6; text-align: left; word-break: break-all;" th:text="${task.taskName}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.startTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.endTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.executeTime == 0} ? '<1' : ${task.executeTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.detail}"></td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>






  <!-- 手动任务耗时Top 10 -->
  <div style="margin-bottom: 10px;">
    <h3 style="margin-bottom: 5px; color: #333; font-size: 18px;">手动任务耗时Top 10</h3>
    <div style="overflow-x: auto;">
      <table style="width: 100%; border-collapse: collapse; border-spacing: 0; font-size: 14px; border: 1px solid #dee2e6;">
        <thead>
        <tr>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">任务名</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">开始时间</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">结束时间</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">耗时(秒)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">任务描述</th>
        </tr>
        </thead>
        <tbody>
        <tr th:if="${#lists.isEmpty(manualTaskSlow[0].datas)}">
          <td colspan="5" style="padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">暂无数据</td>
        </tr>
        <tr th:each="task : ${manualTaskSlow[0].datas}" style="background-color: #ffffff;">
          <td style="vertical-align: middle; width: 400px; padding: 3px; border: 1px solid #dee2e6; text-align: left; word-break: break-all;" th:text="${task.taskName}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.startTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.endTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.executeTime == 0} ? '<1' : ${task.executeTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.detail}"></td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>

</div>
</body>
</html>
