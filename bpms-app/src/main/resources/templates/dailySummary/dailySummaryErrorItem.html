<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>大数据平台运行日报</title>
</head>
<body style="padding: 20px; margin: 0; background-color: #ffffff;">
<div style="width: 100%; margin: 0 auto; padding: 0 15px;">

  <!-- 自动任务失败明细 -->
  <div style="margin-bottom: 10px;">
    <h3 style="margin-bottom: 5px; color: #333; font-size: 18px;">自动任务失败明细</h3>
    <div style="overflow-x: auto;">
      <table style="width: 100%; border-collapse: collapse; border-spacing: 0; font-size: 14px; border: 1px solid #dee2e6;">
        <thead>
        <tr>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">任务名</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">开始时间</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">结束时间</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">耗时(秒)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">任务描述</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">异常信息</th>
        </tr>
        </thead>
        <tbody>
        <tr th:if="${#lists.isEmpty(autoTaskFail[0].datas)}">
          <td colspan="6" style="padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">暂无数据</td>
        </tr>
        <tr th:each="task : ${autoTaskFail[0].datas}" style="background-color: #ffffff;">
          <td style="vertical-align: middle; min-width: 200px; padding: 3px; border: 1px solid #dee2e6; text-align: left; word-break: break-all;" th:text="${task.taskName}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.startTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.endTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.executeTime == 0} ? '<1' : ${task.executeTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.detail}"></td>
          <td style="vertical-align: middle; min-width: 500px; padding: 3px; border: 1px solid #dee2e6; text-align: left; word-break: break-all;" th:text="${task.exceptionMessage}"></td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>







  <!-- 手动任务失败明细 -->
  <div style="margin-bottom: 10px;">
    <h3 style="margin-bottom: 5px; color: #333; font-size: 18px;">手动任务失败明细</h3>
    <div style="overflow-x: auto;">
      <table style="width: 100%; border-collapse: collapse; border-spacing: 0; font-size: 14px; border: 1px solid #dee2e6;">
        <thead>
        <tr>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">任务名</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">开始时间</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">结束时间</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">耗时(秒)</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">任务描述</th>
          <th style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">异常信息</th>
        </tr>
        </thead>
        <tbody>
        <tr th:if="${#lists.isEmpty(manualTaskFail[0].datas)}">
          <td colspan="6" style="padding: 3px; border: 1px solid #dee2e6; text-align: center; background-color: #f8f9fa; font-weight: bold; white-space: nowrap;">暂无数据</td>
        </tr>
        <tr th:each="task : ${manualTaskFail[0].datas}" style="background-color: #ffffff;">
          <td style="vertical-align: middle; min-width: 200px; padding: 3px; border: 1px solid #dee2e6; text-align: left; word-break: break-all;" th:text="${task.taskName}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.startTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.endTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.executeTime == 0} ? '<1' : ${task.executeTime}"></td>
          <td style="vertical-align: middle; min-width: 100px; padding: 3px; border: 1px solid #dee2e6; text-align: center; white-space: nowrap;" th:text="${task.detail}"></td>
          <td style="vertical-align: middle; min-width: 500px; padding: 3px; border: 1px solid #dee2e6; text-align: left; word-break: break-all;" th:text="${task.exceptionMessage}"></td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>

</div>
</body>
</html>
