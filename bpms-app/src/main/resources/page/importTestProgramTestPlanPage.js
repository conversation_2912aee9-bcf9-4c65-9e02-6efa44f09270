const createModal = () => {
    function generateRandomString(length) {
        let result = ''
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'

        for (let i = 0; i < length; i++) {
            result += characters.charAt(Math.floor(Math.random() * characters.length))
        }

        return result
    }

    const uploadModal = document.createElement('div')
    uploadModal.style.position = 'fixed'
    uploadModal.style.top = '50%'
    uploadModal.style.left = '50%'
    uploadModal.style.transform = 'translate(-50%, -50%)'
    uploadModal.style.padding = '20px'
    uploadModal.style.backgroundColor = '#ffffff'
    uploadModal.style.borderRadius = '10px'
    uploadModal.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.2)'
    uploadModal.style.zIndex = '100000000'
    uploadModal.style.width = '1000px'
    uploadModal.style.textAlign = 'center'
    uploadModal.style.fontFamily = 'Arial, sans-serif'

    const modalTitle = document.createElement('div')
    modalTitle.innerHTML = '<span>TEST PROGRAM TEST PLAN 导入</span><br/><br/>'
    modalTitle.style.fontWeight = 'bold'
    uploadModal.appendChild(modalTitle)

    const fileInput = document.createElement('input')
    fileInput.type = 'file'
    fileInput.style.width = '100%'
    fileInput.style.padding = '10px'
    fileInput.style.border = '1px solid #ccc'
    fileInput.style.borderRadius = '5px'
    fileInput.style.marginBottom = '15px'
    fileInput.style.boxSizing = 'border-box'
    uploadModal.appendChild(fileInput)

    const overriteDiv = document.createElement('div')
    overriteDiv.style.marginBottom = '10px'
    overriteDiv.style.textAlign = 'left'
    uploadModal.appendChild(overriteDiv)
    const overriteLabel1 = document.createElement('label')
    overriteLabel1.innerText = '全量覆盖:'
    overriteLabel1.style.marginRight = '10px'
    overriteLabel1.style.verticalAlign = 'middle'
    overriteDiv.appendChild(overriteLabel1)
    const radioName = generateRandomString(8)
    const overriteFullRadio = document.createElement('input')
    overriteFullRadio.style.marginRight = '30px'
    overriteFullRadio.style.verticalAlign = 'middle'
    overriteFullRadio.type = 'radio'
    overriteFullRadio.name = radioName
    overriteFullRadio.value = 'true'
    overriteFullRadio.checked = true
    overriteDiv.appendChild(overriteFullRadio)
    const overriteLabel2 = document.createElement('label')
    overriteLabel2.innerText = '增量覆盖:'
    overriteLabel2.style.marginRight = '10px'
    overriteLabel2.style.verticalAlign = 'middle'
    overriteDiv.appendChild(overriteLabel2)
    const overriteNotFullRadio = document.createElement('input')
    overriteNotFullRadio.style.verticalAlign = 'middle'
    overriteNotFullRadio.type = 'radio'
    overriteNotFullRadio.name = radioName
    overriteNotFullRadio.value = 'false'
    overriteDiv.appendChild(overriteNotFullRadio)

    const uploadButton = document.createElement('button')
    uploadButton.innerText = '上传文件'
    uploadButton.style.backgroundColor = '#007bff'
    uploadButton.style.color = 'white'
    uploadButton.style.border = 'none'
    uploadButton.style.padding = '10px 20px'
    uploadButton.style.borderRadius = '5px'
    uploadButton.style.cursor = 'pointer'
    uploadButton.style.fontSize = '16px'
    uploadButton.style.transition = 'background-color 0.3s ease'
    uploadButton.style.width = '100%'

    uploadModal.appendChild(uploadButton)

    const closeButton = document.createElement('button')
    closeButton.innerText = '关闭'
    closeButton.style.backgroundColor = '#dc3545'
    closeButton.style.color = 'white'
    closeButton.style.border = 'none'
    closeButton.style.padding = '10px 20px'
    closeButton.style.borderRadius = '5px'
    closeButton.style.cursor = 'pointer'
    closeButton.style.fontSize = '16px'
    closeButton.style.marginTop = '10px'
    closeButton.style.transition = 'background-color 0.3s ease'
    closeButton.style.width = '100%'

    uploadModal.appendChild(closeButton)

    const resultMessage = document.createElement('div')
    resultMessage.style.maxHeight = '500px'
    resultMessage.style.overflow = 'auto'
    resultMessage.style.marginTop = '15px'
    resultMessage.style.fontWeight = 'bold'
    uploadModal.appendChild(resultMessage)

    document.body.appendChild(uploadModal)

    function getAdminToken() {
        const match = document.cookie.match(/Admin-Token=([^;]+)/)
        return match ? match[1] : null
    }

    uploadButton.addEventListener('click', function () {
        resultMessage.innerText = ''
        const file = fileInput.files[0]
        if (!file) {
            resultMessage.style.color = 'red'
            resultMessage.innerText = '请先选择一个文件'
            return
        }

        const formData = new FormData()
        formData.append('file', file)
        formData.append('overriteFullFlag', overriteFullRadio.checked)

        const url = window.location.origin + '/bpms-api/testProgramTestPlan/importPlan'

        this.setAttribute('disabled', 'true')
        this.style.backgroundColor = 'gray'
        this.innerText = '上传中...'
        fetch(url, {
            method: 'POST',
            body: formData,
            headers: {
                'Authorization': getAdminToken()
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络响应不正常')
                }
                return response.json()
            })
            .then(data => {
                this.removeAttribute('disabled')
                this.style.backgroundColor = '#007bff'
                this.innerText = '上传文件'
                if (data.code === '00000001') {
                    resultMessage.style.color = 'green'
                    resultMessage.innerText = '上传成功！'
                } else {
                    resultMessage.style.color = 'red'
                    resultMessage.innerText = data.msg || '未知错误'
                }
            })
            .catch(error => {
                this.removeAttribute('disabled')
                this.style.backgroundColor = '#007bff'
                this.innerText = '上传文件'
                resultMessage.style.color = 'red'
                resultMessage.innerText = '上传失败: ' + error.message
            })
    })

    closeButton.addEventListener('click', () => {
        document.body.removeChild(uploadModal)
    })
}

createModal()