const createModal = () => {
    let urlPrefix = '/bpms-api'

    function getAdminToken() {
        const match = document.cookie.match(/Admin-Token=([^;]+)/)
        return match ? match[1] : null
    }

    function loadVueScript() {
        var vueScript = document.createElement('script');
        vueScript.src = window.location.origin + urlPrefix + '/page/openSource/vue.js';
        document.head.appendChild(vueScript);
    }

    function loadElementUiScript() {
        var elementUiScript = document.createElement('script');
        elementUiScript.src = window.location.origin + urlPrefix + '/page/openSource/element-ui.js';
        document.head.appendChild(elementUiScript);
    }

    function loadElementUiCss() {
        var elementUiCss = document.createElement('link');
        elementUiCss.rel = 'stylesheet'
        elementUiCss.href = window.location.origin + urlPrefix + '/page/openSource/element-ui.css';
        document.head.appendChild(elementUiCss);
    }

    function loadPage() {
        loadPageCss()
        loadPageVue(loadPageTemplate())
        console.log('加载完成！')
    }

    loadVueScript()
    setTimeout(() => {
        loadElementUiScript()
        setTimeout(() => {
            loadElementUiCss()
            setTimeout(() => {
                loadPage()
            }, 1000);
        }, 1000);
    }, 1000);

    function loadPageCss() {
        var stylee = document.createElement('style');
        stylee.type = 'text/css';
        document.head.appendChild(stylee);
        stylee.innerHTML = `
        
.clearfix:before,
.clearfix:after {
    display: table;
    content: "";
}
.clearfix:after {
    clear: both
}

.box-card {
    width: 100%;
  }

`
    }

    function loadPageTemplate() {
        return `
        <div id='app' style="overflow: auto">

        <div>
            <el-card class="box-card">
                <div slot="header" class="clearfix">
                    <div style="float: right; padding: 3px 0">
                        <el-button type="success" :loading="loadingFlag" round @click="queryStateManageData">刷新</el-button>
                    </div>
                </div>
            </el-card>
            <br />
        </div>
        <div>
            <el-card class="box-card"
                v-show="stateManageData.moduleSwitchDatas && stateManageData.moduleSwitchDatas.length > 0">
                <div slot="header" class="clearfix">
                    <span>模块开关</span>
                    <div style="float: right; padding: 3px 0">
                        <el-button type="success" :loading="loadingFlag" round @click="switchAllModule(0)">全部开启</el-button>
                        <el-button type="danger" :loading="loadingFlag" round @click="switchAllModule(1)">全部关闭</el-button>
                    </div>
                </div>
                <el-table :data="stateManageData.moduleSwitchDatas" max-height="400" border style="width: 100%">
                    <el-table-column prop="module" label="模块" width="360">
                    </el-table-column>
                    <el-table-column label="开启状态" width="360">
                        <template slot-scope="scope">
                            <el-switch v-model="scope.row.stopFlag" @change="switchModuleStopFlag(scope.$index)"
                                :active-value="0" :inactive-value="1" active-color="#13ce66" inactive-color="#ff4949">
                            </el-switch>
                        </template>
                    </el-table-column>
                    <el-table-column prop="updateTime" label="更新时间">
                    </el-table-column>
                </el-table>
            </el-card>
            <br />
        </div>
        <div v-for="moduleTask of stateManageData.moduleTasks" :key="moduleTask.id">
            <el-card class="box-card">
                <div slot="header" class="clearfix">
                    <span>{{moduleTask.moduleTaskType}}</span>
                    <el-badge :value="moduleTask.tableDatas.length">
                    </el-badge>
                </div>
                <el-table :data="moduleTask.tableDatas" max-height="400" border style="width: 100%">
                    <el-table-column prop="id" label="id" width="100">
                    </el-table-column>
                    <el-table-column prop="createTime" label="创建时间">
                    </el-table-column>
                    <el-table-column prop="updateTime" label="更新时间">
                    </el-table-column>
                    <el-table-column>
                        <template slot-scope="scope">
                            <el-button type="text" @click="openTableDetail(scope.row)">详情</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
            <br />
        </div>
        <div>
            <el-card class="box-card" >
                <div slot="header" class="clearfix">
                    <div style="float: right; padding: 3px 0">
                        <el-button  type="danger" round :loading="loadingFlag" 
                            @click="onlyRepairCollectx">只修复Collectx</el-button>
                        <el-button  type="danger" round :loading="loadingFlag"
                            @click="onlyRepairProcessing">只修复PROCESSING</el-button>
                        <el-button  type="danger" round :loading="loadingFlag"
                            @click="forceOneClickRepair">强制一键修复所有</el-button>
                    </div>
                </div>
            </el-card>
            <br />
        </div>
    
        <el-dialog :title="currentTableDetail.table" :visible.sync="centerDialogVisible" width="30%" center>
            <span style="white-space: pre-line">{{JSON.stringify(currentTableDetail.detail, null, 2)}}</span>
        </el-dialog>
    </div>
`
    }

    function createAppDiv() {
        let appDiv = document.querySelector('#app')
        if (!appDiv) {
            appDiv = document.createElement('div')
            appDiv.id = 'app'
            document.body.appendChild(appDiv)
        }
    }

    function loadPageVue(template) {
        createAppDiv()
        const app = new Vue({
            data: {
                stateManageData: {},
                loadingFlag: false,
                centerDialogVisible: false,
                currentTableDetail: {},
            },
            methods: {
                async queryStateManageData() {
                    this.loadingFlag = true
                    let res = await this.fetchUrl('/stateManage/queryStateManageData', 'POST', null)
                    if (res != null) {
                        this.stateManageData = res.data
                    } else {
                        this.stateManageData = {}
                    }
                    this.loadingFlag = false
                },
                async switchAllModule(stopFlag) {
                    this.loadingFlag = true
                    let res = await this.fetchUrl('/stateManage/switchAllModule', 'GET', {stopFlag})
                    if (res != null) {
                        this.$notify({
                            title: '成功',
                            message: '执行成功',
                            type: 'success'
                        });
                    }
                    await this.queryStateManageData()
                    this.loadingFlag = false
                },
                async switchModuleStopFlag(index) {
                    this.loadingFlag = true
                    let res = await this.fetchUrl('/stateManage/switchModuleStopFlag', 'POST', [this.stateManageData.moduleSwitchDatas[index]])
                    if (res != null) {
                        this.$notify({
                            title: '成功',
                            message: '执行成功',
                            type: 'success'
                        });
                    }
                    await this.queryStateManageData()
                    this.loadingFlag = false
                },
                openTableDetail(row) {
                    this.currentTableDetail = row
                    this.centerDialogVisible = true
                },
                async repair(url, tip) {
                    this.loadingFlag = true
                    try {
                        await this.$confirm(tip, '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            dangerouslyUseHTMLString: true,
                            center: true,
                            type: 'warning'
                        });
                        let res = await this.fetchUrl(url, 'POST', null)
                        if (res != null) {
                            this.$notify({
                                title: '成功',
                                message: '执行成功',
                                type: 'success'
                            });
                        }
                        await this.queryStateManageData()
                        scrollTo(0, 0)
                        this.$notify({
                            title: '警告',
                            message: '开关已关闭',
                            type: 'warning'
                        });
                    } catch (e) {
                        // 用户取消操作，无需处理
                    } finally {
                        this.loadingFlag = false
                    }
                },
                async onlyRepairCollectx() {
                    await this.repair('/stateManage/onlyRepairCollectx', '请确认<b>dataware-collectx</b>进程已经重启')
                },
                async onlyRepairProcessing() {
                    await this.repair('/stateManage/onlyRepairProcessing', '请确认<b>COMPUTE_SCHEDULER_PLATFORM_TASK<b>没有<b>CREATE<b>或<b>PROCESSING<b>的任务<br/><br/>或者<b>next-compute-scheduler<b>进程已经停止')
                },
                async forceOneClickRepair() {
                    await this.repair('/stateManage/forceOneClickRepair', '<b>请确认以上真的没有正在处理中的任务！</b>')
                },
                async fetchUrl(url, method, data) {
                    if (method === 'GET' && data != null) {
                        const params = new URLSearchParams();
                        Object.getOwnPropertyNames(data).forEach(key => {
                            params.append(key, data[key])
                        });
                        url += `?${params.toString()}`
                    }
                    try {
                        const response = await fetch(window.location.origin + urlPrefix + url, {
                            method,
                            headers: {
                                'Authorization': getAdminToken(),
                                "content-type": method === 'POST' ? "application/json" : "",
                            },
                            body: method === 'POST' && data != null ? JSON.stringify(data) : null
                        });
                        let result = await response.json();
                        if (result.code === '00000001') {
                            return result
                        } else {
                            this.$notify.error({
                                title: '错误',
                                message: result.msg
                            });
                            return null
                        }
                    } catch (error) {
                        console.error("Error fetching data:", error);
                        this.$notify.error({
                            title: '错误',
                            message: '系统异常!'
                        });
                        return null
                    }
                },
            },
            mounted() {
                this.queryStateManageData();
            },
            template
        }).$mount('#app');
    }


}

createModal()